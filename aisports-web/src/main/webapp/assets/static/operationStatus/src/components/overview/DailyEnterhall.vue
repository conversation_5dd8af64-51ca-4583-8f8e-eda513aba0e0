<template>
    <div class="colx-6">
        <chart
            ref="chart"
            height="562"
            title="当日入馆人次时间分布">
        </chart>
    </div>
</template>

<script>
import fetchData from '../common/fetchData'
import _ from 'underscore'
import line from '../charts/line'

export default {
    mixins: [fetchData],
    data() {
        return {
            list: [],
        }
    },
    methods: {
        paintChart(res) {
            const axisMin = +_.min(res.masterCenterRankHour, mc => +mc.key).key
            const axisMax = +_.max(res.masterCenterRankHour, mc => +mc.key).key
            const axis = _.range(axisMin, axisMax + 1)

            const list =
					_.chain(res.masterCenterRankHour)
					    .groupBy('centerName')
					    .pairs()
					    .map(series => ({
					        name: series[0],
					        type: 'line',
					        smooth: true,
					        data: axis.map((axi) => {
					            const item = _.find(series[1], se => se.key == axi)
					            return {
					                key: axi,
					                value: item ? item.value : 0,
					            }
					        })
					    }))
					    .value()

            return line(this.$refs.chart.$refs.cbd, list.filter(n => n.data.some(v => v.value)), axis)
        },
    },
}
</script>
