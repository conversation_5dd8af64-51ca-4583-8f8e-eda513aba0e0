package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class PerformStock {
    private Long id;

    private String name;

    private String performId;

    private Integer totalAmount;

    private Integer remainAmount;

    private String location;

    private Integer version;

    private String state;

    private String assignState;

    private Date createTime;

    private Long createStaffId;

    private Date updateTime;

    private Long updateStaffId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getPerformId() {
        return performId;
    }

    public void setPerformId(String performId) {
        this.performId = performId == null ? null : performId.trim();
    }

    public Integer getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(Integer remainAmount) {
        this.remainAmount = remainAmount;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location == null ? null : location.trim();
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getAssignState() {
        return assignState;
    }

    public void setAssignState(String assignState) {
        this.assignState = assignState == null ? null : assignState.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", performId=").append(performId);
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", remainAmount=").append(remainAmount);
        sb.append(", location=").append(location);
        sb.append(", version=").append(version);
        sb.append(", state=").append(state);
        sb.append(", assignState=").append(assignState);
        sb.append(", createTime=").append(createTime);
        sb.append(", createStaffId=").append(createStaffId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append("]");
        return sb.toString();
    }
}