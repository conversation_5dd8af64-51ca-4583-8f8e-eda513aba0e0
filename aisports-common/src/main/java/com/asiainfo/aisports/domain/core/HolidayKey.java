package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class HolidayKey {
    private Long holidayType;

    private Date startDate;

    public Long getHolidayType() {
        return holidayType;
    }

    public void setHolidayType(Long holidayType) {
        this.holidayType = holidayType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", holidayType=").append(holidayType);
        sb.append(", startDate=").append(startDate);
        sb.append("]");
        return sb.toString();
    }
}