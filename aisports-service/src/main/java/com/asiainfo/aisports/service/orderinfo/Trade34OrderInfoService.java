package com.asiainfo.aisports.service.orderinfo;

import com.asiainfo.aisports.domain.core.ServiceAcceptInfo;
import com.asiainfo.aisports.persistence.core.MemberSearchMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by wangdd on 16/4/5.
 * 升卡
 */
@Service
public class Trade34OrderInfoService implements HandleOrderInfo{

    @Autowired
    MemberSearchMapper memberSearchMapper;

    /**
     * 升卡
     * @param info
     * @return
     */
    public ServiceAcceptInfo handleOrderInfo(ServiceAcceptInfo info){
        //升卡要显示从什么卡升级到什么卡
        String productNameA = memberSearchMapper.getProductNameAByTradeId(info.getTradeId());
        String productNameB = memberSearchMapper.getProductNameBByTradeId(info.getTradeId());
        if (productNameA != null && !"".equals(productNameA)) {
            info.setShowInfo1("原专项卡：" + productNameA);
        }
        if (productNameB != null && !"".equals(productNameB)) {
            info.setShowInfo2("新专项卡：" + productNameB);
        }
        return info;
    }
}
