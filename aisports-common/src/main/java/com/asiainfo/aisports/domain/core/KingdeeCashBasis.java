package com.asiainfo.aisports.domain.core;

/**
 * 金蝶对接收费实现制
 * <AUTHOR>
 */
public class KingdeeCashBasis {

    private Long id;

    /**
     * 日期
     */
    private String statDate;

    /**
     * 业务类型
     */
    private String tradeType;

    /**
     * 场馆名称，对应金蝶项目
     */
    private String venueName;

    /**
     * 类型
     */
    private String typeName;

    /**
     * 种类
     */
    private String projectName;

    /**
     * 支付方式
     */
    private String payModeName;

    /**
     * 金额
     */
    private Double money;

    /**
     * 统计标签，1为详单，2为业务类型-场馆级别小计，3为业务类型-场馆级别合计，4为业务类型小计，5为业务类型合计，6为支付方式小计，7为合计
     */
    private Integer statisticsTag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatDate() {
        return statDate;
    }

    public void setStatDate(String statDate) {
        this.statDate = statDate;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getVenueName() {
        return venueName;
    }

    public void setVenueName(String venueName) {
        this.venueName = venueName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getPayModeName() {
        return payModeName;
    }

    public void setPayModeName(String payModeName) {
        this.payModeName = payModeName;
    }

    public Double getMoney() {
        return money;
    }

    public void setMoney(Double money) {
        this.money = money;
    }

    public Integer getStatisticsTag() {
        return statisticsTag;
    }

    public void setStatisticsTag(Integer statisticsTag) {
        this.statisticsTag = statisticsTag;
    }

    @Override
    public String toString() {
        return "KingdeeCashBasis{" +
                "id=" + id +
                ", statDate='" + statDate + '\'' +
                ", tradeType='" + tradeType + '\'' +
                ", venueName='" + venueName + '\'' +
                ", typeName='" + typeName + '\'' +
                ", projectName='" + projectName + '\'' +
                ", payModeName='" + payModeName + '\'' +
                ", money=" + money +
                ", statisticsTag=" + statisticsTag +
                '}';
    }
}
