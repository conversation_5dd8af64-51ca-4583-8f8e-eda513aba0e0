package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.AppBillFile;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface AppBillFileMapper {
    @Delete({
        "delete from app_bill_file",
        "where bill_file_id = #{billFileId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long billFileId);

    @Insert({
        "insert into app_bill_file (bill_file_id, center_id, ",
        "app_id, bill_type, ",
        "bill_cycle, cycle_date, ",
        "file_id, state, remark, ",
        "create_time, create_staff_id, ",
        "update_time, update_staff_id)",
        "values (#{billFileId,jdbcType=BIGINT}, #{centerId,jdbcType=BIGINT}, ",
        "#{appId,jdbcType=BIGINT}, #{billType,jdbcType=VARCHAR}, ",
        "#{billCycle,jdbcType=CHAR}, #{cycleDate,jdbcType=VARCHAR}, ",
        "#{fileId,jdbcType=BIGINT}, #{state,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(AppBillFile record);

    @Select({
        "select",
        "bill_file_id, center_id, app_id, bill_type, bill_cycle, cycle_date, file_id, ",
        "state, remark, create_time, create_staff_id, update_time, update_staff_id",
        "from app_bill_file",
        "where bill_file_id = #{billFileId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="bill_file_id", property="billFileId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="app_id", property="appId", jdbcType=JdbcType.BIGINT),
        @Result(column="bill_type", property="billType", jdbcType=JdbcType.VARCHAR),
        @Result(column="bill_cycle", property="billCycle", jdbcType=JdbcType.CHAR),
        @Result(column="cycle_date", property="cycleDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_id", property="fileId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    AppBillFile selectByPrimaryKey(Long billFileId);

    @Update({
        "update app_bill_file",
        "set center_id = #{centerId,jdbcType=BIGINT},",
          "app_id = #{appId,jdbcType=BIGINT},",
          "bill_type = #{billType,jdbcType=VARCHAR},",
          "bill_cycle = #{billCycle,jdbcType=CHAR},",
          "cycle_date = #{cycleDate,jdbcType=VARCHAR},",
          "file_id = #{fileId,jdbcType=BIGINT},",
          "state = #{state,jdbcType=CHAR},",
          "remark = #{remark,jdbcType=VARCHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where bill_file_id = #{billFileId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(AppBillFile record);

    @Select({
        "<script>",
        "select",
        "bill_file_id, center_id, app_id, bill_type, bill_cycle, cycle_date, file_id, state, remark, create_time, create_staff_id, update_time, update_staff_id",
        "from app_bill_file",
        "<where>",
        "<if test=\"billFileId != null\">and bill_file_id = #{billFileId,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"appId != null\">and app_id = #{appId,jdbcType=BIGINT}</if>",
        "<if test=\"billType != null\">and bill_type = #{billType,jdbcType=VARCHAR}</if>",
        "<if test=\"billCycle != null\">and bill_cycle = #{billCycle,jdbcType=CHAR}</if>",
        "<if test=\"cycleDate != null\">and cycle_date = #{cycleDate,jdbcType=VARCHAR}</if>",
        "<if test=\"fileId != null\">and file_id = #{fileId,jdbcType=BIGINT}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="bill_file_id", property="billFileId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="app_id", property="appId", jdbcType=JdbcType.BIGINT),
        @Result(column="bill_type", property="billType", jdbcType=JdbcType.VARCHAR),
        @Result(column="bill_cycle", property="billCycle", jdbcType=JdbcType.CHAR),
        @Result(column="cycle_date", property="cycleDate", jdbcType=JdbcType.VARCHAR),
        @Result(column="file_id", property="fileId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<AppBillFile> selectByFields(AppBillFile param);

    @Update({
        "<script>",
        "update app_bill_file",
        "<set>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"appId != null\">app_id = #{appId,jdbcType=BIGINT},</if>",
        "<if test=\"billType != null\">bill_type = #{billType,jdbcType=VARCHAR},</if>",
        "<if test=\"billCycle != null\">bill_cycle = #{billCycle,jdbcType=CHAR},</if>",
        "<if test=\"cycleDate != null\">cycle_date = #{cycleDate,jdbcType=VARCHAR},</if>",
        "<if test=\"fileId != null\">file_id = #{fileId,jdbcType=BIGINT},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"remark != null\">remark = #{remark,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where bill_file_id = #{billFileId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(AppBillFile record);

    /**
     * 查询app相关文件的下载地址
     *
     * @param appId
     * @param date
     * @param billType
     * @return
     */
    @Select({
          " select a.center_id,a.bill_file_id,a.file_id,b.file_path,b.file_name",
          " from app_bill_file a ,general_file b",
          " where a.app_id = #{appId,jdbcType=BIGINT} and a.bill_type = #{billType,jdbcType=VARCHAR} and a.cycle_date =#{date,jdbcType=VARCHAR} " ,
          " and a.state = '1' and b.state = '1'",
          " and a.file_id = b.file_id",
          " order by a.create_time desc ",
          " limit 1"
    })
    @Results({
            @Result(column="bill_file_id", property="billFileId", jdbcType=JdbcType.BIGINT),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="file_id", property="fileId", jdbcType=JdbcType.BIGINT),
            @Result(column="file_path", property="filePath", jdbcType=JdbcType.VARCHAR),
            @Result(column="file_name", property="fileName", jdbcType=JdbcType.VARCHAR)

    })
    DataMap selectAppBileUrl(@Param("appId") Long appId, @Param("date") String date, @Param("billType") String billType);
}
