// bar.js
define(function(require,exports,module){
	var dimple = require('dimple');
	var margin = {top: 16, right: 20, bottom: 25, left: 52};

	function paintBar(elem, data, color, tickFormat){
		var color = color || '#ff7500',
			tickFormat = tickFormat || function(str){
				// console.log(str)
			return str.substr(6, 2);
		};

		var container = d3.select(elem).select('.chart-body');
		container.html('');
		var chartbody = container.node();
		var clientRect = chartbody.getBoundingClientRect();

		var svg = dimple.newSvg(container.node(), clientRect.width, clientRect.height);
		data.forEach(function(item, index){
			item.index = index;
		});
      	var barChart = new dimple.chart(svg, data);

		barChart.setMargins(margin.left, margin.top, margin.right, margin.bottom);

      	var y = barChart.addMeasureAxis('y', 'value');
      	y.ticks = 5;

      	var x = barChart.addCategoryAxis('x', 'key');
		x.tickFormat = tickFormat;
		x.addOrderRule('index');
		//x轴隔行展示
		var cleanAxis = function (axis, oneInEvery) {
			// This should have been called after draw, otherwise do nothing
			if (axis.shapes.length > 0) {
				// Leave the first label
				var del = 0;
				// If there is an interval set
				if (oneInEvery > 1) {
					// Operate on all the axis text
					// console.log(del % oneInEvery)
					axis.shapes.selectAll("text").each(function (d) {
						d3.select(this).attr("opacity", 1);
						// Remove all but the nth label
						if (del % oneInEvery !== 0) {
							d3.select(this).attr("opacity", 0);
						}
						del += 1;
					});
				}
			}
		};
      	var barSeries = barChart.addSeries('key', dimple.plot.bar);
		barSeries.barGap = 0.5;
      	barSeries.afterDraw = function () {
			cleanAxis(x, 2);
      		container
				.selectAll('.dimple-bar')
				.style('fill', color)
				.style('stroke', color);
			
      	};
      	barChart.draw();
	}

    module.exports = paintBar;
});
