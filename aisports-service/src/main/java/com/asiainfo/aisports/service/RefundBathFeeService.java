package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.ChargeFee;
import com.asiainfo.aisports.domain.core.EntryInfo;
import com.asiainfo.aisports.domain.core.Trade;
import com.asiainfo.aisports.domain.core.TradePayLog;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.ChargeFeeMapper;
import com.asiainfo.aisports.persistence.core.EntryInfoMapper;
import com.asiainfo.aisports.persistence.core.TradeMapper;
import com.asiainfo.aisports.service.api.CommonService;
import com.asiainfo.aisports.tools.GsonUtils;
import com.asiainfo.aisports.tools.StringUtils;
import com.asiainfo.aisports.tools.TradeConstants;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> niithub
 * @description : 浴资退款
 * @date : 2020/11/5
 */
@Service
public class RefundBathFeeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundBathFeeService.class);

    @Autowired
    private ExitHangWallService exitHangWallService;
    @Autowired
    private ChargeFeeMapper chargeFeeMapper;
    @Autowired
    private EntryInfoMapper entryInfoMapper;
    @Autowired
    private TradePayLogService tradePayLogService;
    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private ChargeFeeService chargeFeeService;
    @Autowired
    private SequenceWrapper sequenceWrapper;
    @Autowired
    private TradeMapper tradeMapper;
    @Autowired
    private TradeService tradeService;
    @Autowired
    private RefundTradeService refundTradeService;
    @Autowired
    private CommonService commonService;

    /**
     * 查询手环对应的最近一次入馆记录绑定的水量充值记录
     *
     * @param keyId
     * @param staff
     * @return
     */
    @Transactional(readOnly = true)
    public ServiceResult queryRechargeList(String keyId, LoginStaff staff) {

        ServiceResult serviceResult = new ServiceResult();
        // 获取最近的入馆记录
        EntryInfo entryInfo = entryInfoMapper.selectByKeyId(keyId, staff.getVenueId());
        if (entryInfo == null) {
            serviceResult.setError(1);
            serviceResult.setMessage("该手环下无入馆记录");
            return serviceResult;
        }
        // 根据ticketId查询用户信息和一卡通余额
        serviceResult = exitHangWallService.getEntryInfoByTicketId(entryInfo.getTicketId());

        if (serviceResult.getError() != 0) {
            return serviceResult;
        }

        // 查询水控充值记录
        ChargeFee param = new ChargeFee();
        param.setCenterId(staff.getCenterId());
        param.setVenueId(staff.getVenueId());
        param.setFeeType(Constants.ChargeFeeType.SHOWER_FEE);
        param.setState(Constants.Tag.YES);
        param.setRelatedId(String.valueOf(entryInfo.getTicketId()));
        List<ChargeFee> chargeFeeList = chargeFeeMapper.selectByFields(param);

        List<DataMap> resultList = Lists.newArrayList();
        for (ChargeFee chargeFee : chargeFeeList) {
            DataMap dataMap = new DataMap();
            dataMap.set("payTradeId", chargeFee.getPayTradeId());
            dataMap.set("id", chargeFee.getId());
            dataMap.set("updateTime", chargeFee.getUpdateTime());
            dataMap.set("feeAmount", chargeFee.getFeeAmount());
            dataMap.set("payState", chargeFee.getPayState());
            if (chargeFee.getPayTradeId() == null) {
                resultList.add(dataMap);
                continue;
            }
            List<TradePayLog> tradePayLogList = tradePayLogService.getTradePayLog(chargeFee.getPayTradeId());
            if (tradePayLogList.isEmpty()) {
                resultList.add(dataMap);
                continue;
            }
            Integer paySum = 0;
            String payModeCode = "";
            for (TradePayLog tradePayLog : tradePayLogList) {
                paySum += tradePayLog.getRealPay();
                payModeCode = payModeCode + tradePayLog.getPayModeCode() + ",";
            }
            dataMap.set("payModeCode", payModeCode.substring(0, payModeCode.length() - 1));
            dataMap.set("paySum", paySum);
            resultList.add(dataMap);
        }

        String value = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.RECHARGE_WATER_RULE);
        if (!Strings.isNullOrEmpty(value)) {
            DataMap dataMap = GsonUtils.getGson().fromJson(value, DataMap.class);
            serviceResult.set("rechargeMinute", dataMap.getInteger("minute"));
        }
        serviceResult.put("chargeFeeList", resultList);

        return serviceResult;
    }

    /**
     * 根据充值记录的id取消充值
     *
     * @param keyId
     * @param staff
     * @param ids
     * @return
     */
    @Transactional
    public ServiceResult cancelChargeFee(String keyId, LoginStaff staff, List<String> ids) {

        ServiceResult serviceResult = new ServiceResult();

        // 获取最近的入馆记录包含的水量充值记录
        EntryInfo entryInfo = entryInfoMapper.selectByKeyId(keyId, staff.getVenueId());
        if (entryInfo == null) {
            serviceResult.setError(1);
            serviceResult.setMessage("非法操作");
            return serviceResult;
        }
        ChargeFee chargeFeeParam = new ChargeFee();
        chargeFeeParam.setCenterId(staff.getCenterId());
        chargeFeeParam.setVenueId(staff.getVenueId());
        chargeFeeParam.setFeeType(Constants.ChargeFeeType.SHOWER_FEE);
        chargeFeeParam.setState(Constants.Tag.YES);
        chargeFeeParam.setRelatedId(String.valueOf(entryInfo.getTicketId()));
        List<ChargeFee> chargeFeeList = chargeFeeService.queryChargeFeeList(chargeFeeParam);

        // 根据要取消的水量充值记录id过滤充值记录
        List<ChargeFee> chargeFeeCancels = chargeFeeList.stream().filter(fee -> ids.contains(String.valueOf(fee.getId())))
                .collect(Collectors.toList());
        if (chargeFeeCancels.isEmpty()) {
            return serviceResult;
        }

        int count = chargeFeeService.cancelChargeFee(chargeFeeCancels, staff.getStaffId());
        if (count == 0) {
            serviceResult.setError(1);
            serviceResult.setMessage("充值记录取消失败");
        }
        return serviceResult;
    }

    /**
     * 根据充值记录退款
     *
     * @param staff
     * @param jsonArray [{"tradeId":2020110600201244,"ids":"1000","payMode":"11,12"}]
     * @return
     */
    public ServiceResult refundMoney(LoginStaff staff, JSONArray jsonArray) {

        ServiceResult serviceResult = new ServiceResult();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            String ids = jsonObject.getString("ids");
            String payMode = jsonObject.getString("payMode");
            Long tradeId = jsonObject.getLong("tradeId");
            if (StringUtils.isEmpty(ids) || StringUtils.isEmpty(payMode)) {
                continue;
            }
            // 查询支付日志，判断支付类型对不对，不对就抛异常
            List<TradePayLog> tradePayLogList = tradePayLogService.getTradePayLog(tradeId);
            if (tradePayLogList.isEmpty()) {
                serviceResult.setError(1);
                serviceResult.setMessage("订单号" + jsonObject.getString("ids") + "未发现支付记录");
                return serviceResult;
            }
            // 退款总额
            Integer sum = 0;
            for (TradePayLog tradePayLog : tradePayLogList) {
                if (!payMode.contains(tradePayLog.getPayModeCode())) {
                    serviceResult.setError(1);
                    serviceResult.setMessage("浴资充值订单支付方式核验失败");
                    return serviceResult;
                }
                // 如果是使用优惠卷等付款，则不退款
                if (Constants.PayMode.PLATFROM_COUPON.equals(tradePayLog.getPayModeCode())
                        || Constants.PayMode.DISCOUNT_COUPON.equals(tradePayLog.getPayModeCode())
                        || Constants.PayMode.MANUAL_REDUCTION.equals(tradePayLog.getPayModeCode())
                        || Constants.PayMode.COUPON.equals(tradePayLog.getPayModeCode())) {
                    sum -= tradePayLog.getRealPay();
                }
            }
            List<String> idList = Arrays.asList(ids.split(","));
            for (String feeId : idList) {
                ChargeFee chargeFeeParam = chargeFeeMapper.selectByPrimaryKey(Long.parseLong(feeId));
                if (chargeFeeParam == null) {
                    continue;
                }
                if (Constants.PayState.UNPAID.equals(chargeFeeParam.getPayState())) {
                    continue;
                }
                if (Constants.Tag.NO.equals(chargeFeeParam.getState())) {
                    continue;
                }
                // 计算退款额
                sum += chargeFeeParam.getFeeAmount();
            }
            if (sum <= 0) {
                // 更新充值记录状态
                updateChargeFeeState(staff.getCenterId(), staff.getVenueId(), tradeId, idList, staff.getStaffId());
                return serviceResult;
            }

            // 修改订单状态并退款
            ServiceResult tradeResult = updateTrade(tradeId, sum, staff);

            if (tradeResult.getError() != 0) {
                serviceResult.putAll(tradeResult);
                return serviceResult;
            }
            // 更新充值记录状态
            updateChargeFeeState(staff.getCenterId(), staff.getVenueId(), tradeId, idList, staff.getStaffId());
        }

        return serviceResult;
    }

    /**
     * 修改订单状态并退款
     *
     * @param tradeId
     * @param sum     退款总金额
     * @param staff
     * @return
     */
    private ServiceResult updateTrade(Long tradeId, Integer sum, LoginStaff staff) {

        ServiceResult serviceResult = new ServiceResult();
        Trade trade = tradeMapper.selectByPrimaryKey(tradeId);
        Long refundTradeId = sequenceWrapper.tradeSequence();
        // 保存主业务流水
        Trade refundTrade = new Trade();
        refundTrade.setTradeId(refundTradeId);
        refundTrade.setTradeTypeCode(TradeConstants.TradeTypeCode.REFUND_TRADE.getLongValue());
        refundTrade.setPayState(Constants.PayState.UNPAID);
        refundTrade.setCustId(trade.getCustId());
        refundTrade.setUserId(trade.getUserId());
        refundTrade.setCustName(trade.getCustName());
        refundTrade.setAcctId(trade.getAcctId());
        refundTrade.setEcardNo(trade.getEcardNo());
        refundTrade.setPayTfee(-sum.longValue());
        refundTrade.setTradeStaffId(staff.getStaffId());
        refundTrade.setVenueId(trade.getVenueId());
        refundTrade.setCenterId(trade.getCenterId());
        refundTrade.setServiceId(trade.getServiceId());
        refundTrade.setTradeIdB(tradeId);
        refundTrade.setTradeDesc("单笔续时记录退款");
        refundTrade.setChannelAppId(staff.getAppId());
        refundTrade.setSubscribeState(Constants.SubscribeState.UNCOMPLETED);
        tradeService.saveTrade(refundTrade, staff);

        Map<String, Object> refundResult = refundTradeService.refundMoney(staff, sum, refundTradeId, trade, Constants.ReturnType.BACK, 0L);
        if (((ServiceResult) refundResult).getError() != 0) {
            serviceResult.putAll(refundResult);
            return serviceResult;
        }

        Date now = new Date();
        Long ecardTradeId = (Long) refundResult.get("ecardTradeId");
        refundTrade.setSubscribeState(Constants.SubscribeState.COMPLETED);
        refundTrade.setPayState(Constants.PayState.PAID);
        refundTrade.setFinishDate(now);
        refundTrade.setPayTime(now);
        refundTrade.setEcardTradeId(ecardTradeId);
        tradeService.updateTrade(refundTrade);
        return serviceResult;
    }

    /**
     * 更新充值记录的状态，将充值记录置为失效
     *
     * @param centerId
     * @param venueId
     * @param tradeId
     * @param idList
     * @param staffId
     */
    private void updateChargeFeeState(Long centerId, Long venueId, Long tradeId, List<String> idList, Long staffId) {
        List<ChargeFee> chargeFeeCancels = chargeFeeService.checkChargeFee(centerId, venueId, null, tradeId, idList);
        if (chargeFeeCancels.isEmpty()) {
            return;
        }
        chargeFeeService.cancelChargeFee(chargeFeeCancels, staffId);
    }

    /**
     * 根据订单号做退款操作
     *
     * @param centerId
     * @param tradeId
     * @param staffId
     * @param channelId
     * @return
     */
    public ServiceResult refundMoney(Long centerId, Long tradeId, Long staffId, Long channelId, String keyId) {
        ServiceResult serviceResult = new ServiceResult();
        Trade trade = tradeService.getTradeByTradeId(tradeId);
        if (trade == null) {
            LOGGER.info("水控机-根据订单号退款失败，查询订单结果为空，centerId：{}，tradeId：{}", centerId, tradeId);
            serviceResult.setError(1);
            serviceResult.setMessage("订单查询失败");
            return serviceResult;
        }
        if (!keyId.equals(trade.getKeyId())) {
            LOGGER.info("水控机-根据订单号退款失败，手环号不匹配，centerId：{}，tradeId：{}，keyId：{}", centerId, tradeId, keyId);
            serviceResult.setError(1);
            serviceResult.setMessage("订单查询失败");
            return serviceResult;
        }
        if (Constants.Channel.WALL_HANGING != channelId) {
            LOGGER.info("水控机-根据订单号退款失败，渠道有误，centerId：{}，tradeId：{}，传入的渠道id：{}", centerId, tradeId, channelId);
            serviceResult.setError(1);
            serviceResult.setMessage("渠道有误");
            return serviceResult;
        }
        if (TradeConstants.TradeTypeCode.RECHARGE_BATHE.getLongValue() != trade.getTradeTypeCode()) {
            LOGGER.info("水控机-根据订单号退款失败，业务类型有误，centerId：{}，tradeId：{}，订单的业务类型：{}", centerId, tradeId, trade.getTradeTypeCode());
            serviceResult.setError(1);
            serviceResult.setMessage("业务类型有误");
            return serviceResult;
        }
        if (!Constants.PayState.PAID.equals(trade.getPayState())) {
            LOGGER.info("水控机-根据订单号退款失败，订单支付结果不是已支付，centerId：{}，tradeId：{}，payState：{}", centerId, tradeId, trade.getPayState());
            serviceResult.setError(1);
            serviceResult.setMessage("订单支付状态有误");
            return serviceResult;
        }
        List<TradePayLog> tradePayLogList = tradePayLogService.getTradePayLog(tradeId);
        if (tradePayLogList.isEmpty()) {
            LOGGER.info("水控机-根据订单号退款失败，未发现支付记录，centerId：{}，tradeId：{}", centerId, tradeId);
            serviceResult.setError(1);
            serviceResult.setMessage("订单号" + tradeId + "未发现支付记录");
            return serviceResult;
        }
        // 如果传staffId，就根据staffId查询staff；不传staffId，就根据channelId确定staff
        LoginStaff staff = commonService.getLoginStaff(staffId, channelId, centerId, null);
        for (TradePayLog tradePayLog : tradePayLogList) {
            // 修改订单状态并退款
            ServiceResult tradeResult = updateTrade(tradeId, tradePayLog.getRealPay(), staff);
            if (tradeResult.getError() != 0) {
                LOGGER.error("水控机-根据订单号退款失败，失败原因：{}，centerId：{}，tradeId：{}", tradeResult.getMessage(), centerId, tradeId);
                throw new ServiceException("浴资充值退款失败");
            }
        }
        return serviceResult;
    }
}
