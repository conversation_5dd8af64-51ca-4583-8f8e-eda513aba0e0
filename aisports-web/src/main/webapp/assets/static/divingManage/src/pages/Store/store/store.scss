@import "../addStore/addStore.scss";

.store-wrap {
    background: #fff;
    padding: 0 20px;
}
.store {
    &-header {
        display: flex;
        justify-content: space-between;
        padding: 20px 0;
        .btn {
            margin-left: 10px;
        }
        &-left {
            display: flex;
        }
        &-select {
            margin: 0 10px;
        }
        &-add {
            width: 100px;
            height: 36px;
            line-height: 36px;
            border: 1px solid #E0E0E0;
            border-radius: 4px;
            padding: 0 10px;
            color: #7b7b7b;
            i {
                font-size: 14px;
                font-weight: 100;
            }
            span {
                vertical-align: top;
                margin-left: -4px;
            }
        }
    }
    &-body {
        padding: 20px 0;
        border-top: 1px solid #E0E0E0;
    }
    &-name {
        max-width: 210px;
        margin: auto;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}
input::-webkit-input-placeholder{
    color: #999;
}
input:-ms-input-placeholder{  /* Internet Explorer 10-11 */
    color: #999;
}

.el-table--enable-row-hover .el-table__body tr:hover > td {
    background: #157283;
    color: #fff; 
}