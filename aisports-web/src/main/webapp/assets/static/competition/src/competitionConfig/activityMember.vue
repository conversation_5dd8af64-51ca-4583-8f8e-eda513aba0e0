<template>
    <div>
        <div ref="allItems">
            <div ref="primaryItems">
                <dl class="f-line">
                    <dt class="f-label">姓名</dt>
                    <dd class="f-field">
                        <input
                            v-model="memberInfo.name"
                            type="text"
                            required
                            class="normal">
                    </dd>
                </dl>
                
                <dl class="f-line">
                    <dt class="f-label">手机号</dt>
                    <dd class="f-field">
                        <input
                            v-model="memberInfo.contactPhone"
                            type="text"
                            required
                            vtype="mobile"
                            rtext="请填写手机号"
                            vtext="请填写正确的手机号码格式"
                            class="normal">
                    </dd>
                </dl>
                
                
            </div>
            <configured-items
                ref="configuredItems"
                :value="memberInfo.enrollExtraInfoList"
                :items="configuredItems"></configured-items>
        </div>
    </div>
</template>

<script>
import _ from 'underscore'
import { initSelectPicker } from 'utils/initPlugins'
import ConfiguredItems from 'vue-components/ConfiguredItems.vue'
import { validateIdentity } from 'xValidate'
import 'slimScroll'

const defaultMember = {
    name: '',
    gender: '1',
    contactPhone: '',
    psptType: '1',
    psptId: '',
    enrollExtraInfoList: '',
}

export default {
    components: {
        ConfiguredItems,
    },
    props: ['configuredItems', 'value'],
    data() {
        return {
            memberInfo: _.extend({}, defaultMember),
        }
    },
    watch: {
        value() {
            this.fillInDefault()
        },
        'memberInfo.psptType': function (val) {
            if (val === '1') {
                $(this.$refs.pspt).attr('vtype', 'identity')
            } else {
                $(this.$refs.pspt).attr('vtype', '')
            }
        },
    },
    mounted() {
        this.fillInDefault()
        initSelectPicker(this.$el)
        this.initScroll()
    },
    // beforeDestroy() {
    // 	$(this.$refs.allItems).slimscroll('destroy')
    // },
    methods: {
        getValue() {
            this.memberInfo.enrollExtraInfoList =
						this.$refs.configuredItems.getValue()
            return this.memberInfo
        },
        initScroll() {
            if ($(this.$refs.allItems).height() > 260) {
                $(this.$refs.allItems).slimscroll({
			            height: '260px',
			            size: '0px',
			        })
            }
        },
        fillInDefault() {
            if (typeof this.value === 'object') {
                this.memberInfo = _.extend({}, defaultMember, this.value)
            } else {
                this.memberInfo = _.extend({}, defaultMember)
            }

            this.$nextTick(() => {
                initSelectPicker(this.$el)
            })
        },
        validate(cb) {
            if($(this.$refs.primaryItems).validate()) {
                this.$refs.configuredItems.validate(cb)
            }		
        },
    },
}
</script>
