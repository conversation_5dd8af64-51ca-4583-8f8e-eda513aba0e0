<template>
  <div class="team-ticket-container">
    <section class="header-area">
      <div class="panel-heading">
        <span>座位号管理</span>
        <span class="btn-return" title="后退" @click="goBack">
          <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
        </span>
      </div>
    </section>

    <section class="search-area">
      <input v-model="form.row" class="normal" required placeholder="排数" />
      <input v-model="form.seat" class="normal" required placeholder="座位号" />

      <el-button icon="el-icon-search" type="primary" @click="getSeatList"
        >查询</el-button
      >
    </section>

    <section class="action-area">
      <!-- <el-button type="primary" @click="createdTeamTicket">下载</el-button> -->
      <el-button type="primary" @click="clearSeat">清空</el-button>
      <el-button type="primary" @click="importFile">导入座位</el-button>
    </section>

    <section class="table-area">
      <el-table
        :data="siteList"
        header-cell-class-name="camp-table-header"
        style="width: 100%"
      >
        <el-table-column
          align="center"
          prop="stockName"
          label="票区"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="row"
          label="排数"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="startSeat"
          label="起始座位号"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="endSeat"
          label="结束座位号"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="num"
          label="座位数"
        ></el-table-column>
        <el-table-column
          align="center"
          prop="invalidSeat"
          label="盲区座位号码"
        ></el-table-column>

        <el-table-column
          align="center"
          prop="state"
          label="座位号明细"
          width="800"
        >
          <template slot-scope="scope">
            <el-tag :type="item.state==1?'warning':''"
              v-for="item in scope.row.seatList"
              style="margin-right: 4px; margin-bottom: 4px"
            >
              {{ item.stockName }}-第{{ item.row }}排-{{
                item.seat
              }}号
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="page_footer">
        <el-pagination
          layout="prev, pager, next"
          background
          :total="teamPageInfo.total"
          @current-change="handleSeatPageChange"
        >
        </el-pagination>
      </div>
    </section>
    <import-file
      @successFile="successFile"
      @changeImportFileState="changeImportFileState"
      v-if="importFileState"
    >
    </import-file>
  </div>
</template>

<script>
import * as service from "./service";
import { showSuccess, showWrong } from "mAlert";
import moment from "moment";
import ImportFile from "./importFile.vue";

export default {
  name: "TeamTicket",
  data() {
    return {
      importFileState: false,
      // 数据属性
      projectId: "",
      performId: "",
      stockId: "",
      stockList: [],
      keyword: "",
      salesDateRange: [],
      performList: [],
      startDate: "",
      endDate: "",
      teamPageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      siteList: [],
      teamTicketList: [],
      teamTicketSelected: {
        remainAmount: 100,
        price: 0,
      },
      drawer: false,
      form: {
        row: "",
        seat: "",
      },
    };
  },
  components: {
    ImportFile,
  },
  methods: {
    m2front,
    showSuccess,
    showWrong,
    // 方法定义
    goBack() {
      this.$router.go(-1);
    },
    changeImportFileState() {
      this.importFileState = false;
    },
    handleview(index, row) {
      console.log(index, row, "查看");
      this.$router.push({
        path: "/teamTicketDetail",
        query: {
          projectId: this.projectId,
          recordId: row.id,
        },
      });
    },
    changeMatchId() {
      this.getTeamPerformTicketPageList();
    },
    // 清空
    clearSeat() {
      this.$confirm("您确定要清空吗？", "提示").then(() => {
        let params = {
          stockId: this.stockId,
        };
        service.clearSeat(params, (res) => {
          showSuccess("清空成功");
          this.getSeatList();
        });
      });
    },
    successFile(data) {
      if (data.error != 0) {
        showWrong(data.message);
      } else {
        this.importFileState = false;
        showSuccess("导入成功");
        this.getSeatList();
      }
    },
    //  导入座位
    importFile() {
      this.importFileState = true;
    },
    handleSeatPageChange(pageNum) {
      this.teamPageInfo.pageNum = pageNum;
      this.getSeatList();
    },
    getSeatList() {
      let params = {
        stockId: this.stockId,
        row: this.form.row,
        seat: this.form.seat,
        pageNum: this.teamPageInfo.pageNum,
        pageSize: 10,
      };
      service.getSeatList(params, (res) => {
        this.siteList = res.pageInfo.list;
        this.teamPageInfo.total = res.pageInfo.total;
      });
    },
  },
  mounted() {
    this.stockId = this.$route.query.stockId;
    this.getSeatList();
  },
};
</script>

<style lang="scss" scoped>
.team-ticket-container {
  .drawer-content {
    padding: 20px;
    width: 500px;
  }

  .box-card {
    margin-bottom: 20px;
  }

  .text.item p {
    margin: 5px 0;
  }

  .warning-message {
    color: #f56c6c;
    margin-top: 20px;
  }

  .panel-heading {
    background-color: #fafafa;
    height: 60px;
    font-size: 20px;
  }
  .page_footer {
    display: flex;
    justify-content: flex-end;
  }

  .search-area {
    padding: 20px;
    display: flex;
    gap: 20px;
  }

  .action-area {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
  }

  .table-area {
    padding: 20px;
  }

  .ticket-area-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); // 两列等宽
    gap: 10px;
    width: 472px;
    height: 165px;
    background-color: #f8f8f8;
    padding: 10px;
    font-size: 14px;
    box-sizing: border-box;
    margin-bottom: 20px;
  }

  .ticket-item {
    display: flex;
    padding: 5px;
  }

  .ticket-label {
    color: #666;
  }

  .ticket-value {
    color: #333;
  }

  .text-red {
    color: #fc5f44;
  }

  .text-blue {
    color: #1fa2f5;
  }

  .demo-drawer__footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 40px;
  }

  .el-button {
    border-radius: 8px;
    width: auto;
    height: 36px;
    padding: 0 15px;
  }
}
</style>
