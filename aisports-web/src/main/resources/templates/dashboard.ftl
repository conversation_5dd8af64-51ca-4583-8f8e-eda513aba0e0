<#include "public/macros.ftl">
<!DOCTYPE html>
<html>
    <head>
        <@head_declares />
        <title>工作台</title>
        <link rel="stylesheet" href="/assets/release/static/workspace/workspace.css"/>
        <link rel="stylesheet" href="/assets/release/static/workspace/rentalConsultant.css" />
        <@head_includes />
    </head>
<body>
    <div class="wrapper">
        <!--header-->
        <#include "public/header.ftl">
        <!--left menu-->
        <#include "public/aside.ftl">
    <#--<#if center??>-->
            <#--<input id="centerName" value="${center.centerName!""}" />-->
    <#--</#if>-->
        <!--main content-->
        <div class="main" ${bodyStyle!}>
            <div class="workspace">
                <div class="top-info clearfix">
                    <div class="pend-approval right">
                        <#if authFlag?? && authFlag == '1'>
                            <div class="approval">
                                <a href="${webRoot}/wishare/oauth/openApp?clientId=${fitnessClientId}">
                                    <span class="title">科学健身</span>
                                    <span class="img-box">
                                    <img src="/assets/release/static/images/scientific-fitness.png"/>
                                </span>
                                </a>
                            </div>
                            <div class="approval">
                                <a href="#" class="pedding">
                                    <span class="title">赛事活动</span>
                                    <span class="img-box">
                                    <img src="/assets/release/static/images/zj-event.png"/>
                                </span>
                                </a>
                            </div>

                            <div class="approval">
                                <a href="#" class="pedding">
                                    <span class="title">体育培训</span>
                                    <span class="img-box">
                                    <img src="/assets/release/static/images/sports-training.png"/>
                                </span>
                                </a>
                            </div>
                        </#if>
                        <#if changshuflag?? && changshuflag == '1'>
                            <div class="approval">
                                <a href="${url}">
                                    <span class="title">能耗</span>
                                    <span class="img-box">
                                    <img src="/assets/release/static/images/scientific-fitness.png"/>
                                </span>
                                </a>
                            </div>
                            <div class="approval">
                                <div class="csbigdispaly" href="${viewUrl}">
                                    <span class="title" >大屏</span>
                                    <span class="img-box">
                                    <img src="/assets/release/static/images/scientific-fitness.png"/>
                                </span>
                                </div>
                            </div>
                        </#if>
                          <div class="cspopup">  
                                <div class="popup-content">
                                <span>请输入用气量：</span>
                                <input class='csglobal_name normal ime-inactive'  name="favcolor">
                                </div>  
                                <div class="popup-bottom">
                                    <div class="btn btn-default" id='sccancel'>取消</div>  
                                    <div class="btn btn-primary" id='scconfrim'>确定</div>
                                </div>  
                                  
                            </div>  
                        <div class="approval">
                            <a href="${webRoot}/notification/messages">
                                <span class="title">消息通知</span><span class="num"></span>
                                <span class="icon">
                                    <span class="red-dot"></span>
                                    <i class="iconfont icon-xiaoxitongzhitongzhi"></i>
                                </span>
                            </a>
                        </div>
                        <div class="approval">
                            <a href="${webRoot}/audit/main">
                                <span class="title">待审批</span><span class="num">#{tasks}</span>
                                <span class="icon">
                                    <span class="red-dot"></span>
                                    <i class="iconfont icon-shenpi"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>

                <#if session_dashboard_layout?exists>
                    <#assign col = 0>
                    <#list session_dashboard_layout as element>
                        <#if col=0> <#-- 如果原列数为0，则开始一行 -->
                            <div class="row">
                            <#assign col = element.elementCol> <#-- 列数＝组件列数 -->
                        <#else>
                            <#if col + element.elementCol &gt; 12> <#-- 如果原列数＋组件列数大于12，则换一行 -->
                            </div>
                            <div class="row">
                                <#assign col = element.elementCol> <#-- 列数改为0 -->
                            <#else>
                                <#assign col = col + element.elementCol>
                            </#if>
                        </#if>

                        <div class="chart-col col-xs-#{element.elementCol}" id="${element.elementId}" data-url="${element.dataUrl!""}">
                            <span class="cssload-loader"><span class="cssload-loader-inner"></span></span>
                            <#include element.elementPath>
                            <#--<#include "elements/cashFlow.ftl">-->
                        </div>
                    </#list>
                    </div>
                </#if>
            </div>
        </div>
    </div>
    <#include "elements/coverboxes.ftl">

    <script type="text/template" id="dailyTradeTmpl">
        <tr>
            <td class="mark-info">
                <span class="mark-color"><%= tradeTypeName %></span>
            </td>
            <td class="service-number">
                <span class="progress-bar" style="width: <%= barWidth %>"></span>
                <span class="num-bi"><%= tradeNumber %></span></td>
            <td class="money-info">
                <span><%= income %></span>
            </td>
        </tr>
    </script>
    <@foot_includes path="workspace" filename="main" />

    <#--  租柜到期模块  -->
    <script type="text/javascript" src="/assets/release/jquery/1.12.4/jquery-min.js"></script>
    <script type="text/javascript" src="/assets/release/static/workspace/common.js"></script>
    <script type="text/javascript" src="/assets/release/static/workspace/rentalConsultant.js"></script>

    <#if session_dashboard_js??>
        <#list session_dashboard_js as jsObject>
            <@foot_includes path="workspace" filename="${jsObject}" />
        </#list>
    </#if>
</body>
</html>
