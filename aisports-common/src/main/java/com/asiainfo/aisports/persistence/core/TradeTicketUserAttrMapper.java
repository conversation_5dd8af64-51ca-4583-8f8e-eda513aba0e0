package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.TradeTicketUserAttr;
import com.asiainfo.aisports.domain.core.TradeTicketUserAttrKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface TradeTicketUserAttrMapper {
    @Delete({
        "delete from trade_ticket_user_attr",
        "where ticket_user_id = #{ticketUserId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    int deleteByPrimaryKey(TradeTicketUserAttrKey key);

    @Insert({
        "insert into trade_ticket_user_attr (ticket_user_id, attr_code, ",
        "attr_value, update_time, ",
        "update_staff_id)",
        "values (#{ticketUserId,jdbcType=BIGINT}, #{attrCode,jdbcType=VARCHAR}, ",
        "#{attrValue,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(TradeTicketUserAttr record);

    @Select({
        "select",
        "ticket_user_id, attr_code, attr_value, update_time, update_staff_id",
        "from trade_ticket_user_attr",
        "where ticket_user_id = #{ticketUserId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="ticket_user_id", property="ticketUserId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    TradeTicketUserAttr selectByPrimaryKey(TradeTicketUserAttrKey key);

    @Update({
        "update trade_ticket_user_attr",
        "set attr_value = #{attrValue,jdbcType=VARCHAR},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where ticket_user_id = #{ticketUserId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    int updateByPrimaryKey(TradeTicketUserAttr record);

    @Select({
        "<script>",
        "select",
        "ticket_user_id, attr_code, attr_value, update_time, update_staff_id",
        "from trade_ticket_user_attr",
        "<where>",
        "<if test=\"ticketUserId != null\">and ticket_user_id = #{ticketUserId,jdbcType=BIGINT}</if>",
        "<if test=\"attrCode != null\">and attr_code = #{attrCode,jdbcType=VARCHAR}</if>",
        "<if test=\"attrValue != null\">and attr_value = #{attrValue,jdbcType=VARCHAR}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="ticket_user_id", property="ticketUserId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<TradeTicketUserAttr> selectByFields(TradeTicketUserAttr param);

    @Update({
        "<script>",
        "update trade_ticket_user_attr",
        "<set>",
        "<if test=\"attrValue != null\">attr_value = #{attrValue,jdbcType=VARCHAR},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where ticket_user_id = #{ticketUserId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}",
        "</script>"
    })
    int updateByPrimaryKeySelective(TradeTicketUserAttr record);


    @Insert({
            "replace into trade_ticket_user_attr (ticket_user_id, attr_code, ",
            "attr_value, update_time, ",
            "update_staff_id)",
            "values (#{ticketUserId,jdbcType=BIGINT}, #{attrCode,jdbcType=VARCHAR}, ",
            "#{attrValue,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, ",
            "#{updateStaffId,jdbcType=BIGINT})"
    })
    int replace(TradeTicketUserAttr record);
}
