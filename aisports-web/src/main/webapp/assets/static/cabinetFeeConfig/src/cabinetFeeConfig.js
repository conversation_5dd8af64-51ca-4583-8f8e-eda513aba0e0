import Vue from 'vue'
import $ from 'jquery'
import 'frame'
import VueRouter from 'vue-router'
import List from './components/List.vue'
import Detail from './components/Detail.vue'

Vue.use(VueRouter)

const routes = [{
    path: '/',
    component: List,
}, {
    path: '/detail',
    component: Detail,
}]

const router = new VueRouter({
    routes,
})

new Vue({
    el: '#cabinetFeeConfig',
    template: '<router-view></router-view>',
    router,
})
