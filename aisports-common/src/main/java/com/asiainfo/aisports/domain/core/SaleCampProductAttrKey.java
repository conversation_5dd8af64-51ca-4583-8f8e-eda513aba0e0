package com.asiainfo.aisports.domain.core;

public class SaleCampProductAttrKey {
    private Long campId;

    private Long saleProductId;

    private String attrCode;

    public Long getCampId() {
        return campId;
    }

    public void setCampId(Long campId) {
        this.campId = campId;
    }

    public Long getSaleProductId() {
        return saleProductId;
    }

    public void setSaleProductId(Long saleProductId) {
        this.saleProductId = saleProductId;
    }

    public String getAttrCode() {
        return attrCode;
    }

    public void setAttrCode(String attrCode) {
        this.attrCode = attrCode == null ? null : attrCode.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", campId=").append(campId);
        sb.append(", saleProductId=").append(saleProductId);
        sb.append(", attrCode=").append(attrCode);
        sb.append("]");
        return sb.toString();
    }
}