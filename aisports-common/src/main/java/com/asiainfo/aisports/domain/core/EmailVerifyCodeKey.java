package com.asiainfo.aisports.domain.core;

public class EmailVerifyCodeKey {
    private String email;

    private String verifyCodeType;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    public String getVerifyCodeType() {
        return verifyCodeType;
    }

    public void setVerifyCodeType(String verifyCodeType) {
        this.verifyCodeType = verifyCodeType == null ? null : verifyCodeType.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", email=").append(email);
        sb.append(", verifyCodeType=").append(verifyCodeType);
        sb.append("]");
        return sb.toString();
    }
}