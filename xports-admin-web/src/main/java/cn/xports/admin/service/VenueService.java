package cn.xports.admin.service;

import cn.xports.admin.common.ServiceResult;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.helper.Errors;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.pay.client.AccountData;
import com.asiainfo.aisports.pay.client.PayClient;
import com.asiainfo.aisports.pay.client.Result;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.SequenceWrapper;
import com.asiainfo.aisports.service.job.JobEnum;
import com.asiainfo.aisports.service.mq.JobMessageService;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.StringUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class VenueService {
    @Autowired
    private VenueMapper venueMapper;
    @Autowired
    private CenterService centerService;
    @Autowired
    private SequenceWrapper sequenceWrapper;
    @Autowired
    private PayClient payClient;
    @Autowired
    private StaffResourceMapper staffResourceMapper;
    @Autowired
    private DepartmentResourceMapper departmentResourceMapper;
    @Autowired
    private DepartmentMapper departmentMapper;
    @Autowired
    private StaffService staffService;
    @Autowired
    TrainingInstMapper trainingInstMapper;
    @Autowired
    private JobMessageService jobMessageService;

//    @Cacheable(CacheKeyConst.VENUE_LIST)
    @Transactional(readOnly = true)
    public List<Venue> findAll() {
        Venue param = new Venue();
        return venueMapper.selectValidVenues(param);
    }

    @Transactional(readOnly = true)
    public List<Map<String, Object>> findCenterAndVenues() {
        List<Center> centerList = centerService.findAll();
        List<Venue> venueList = findAll();
        List<Map<String, Object>> resultList = Lists.newArrayList();
        for (Center center : centerList) {
            Map<String, Object> centerMap = Maps.newHashMap();
            centerMap.put("venueId", center.getCenterId());
            centerMap.put("venueName", center.getCenterName());
            centerMap.put("children", findVenuesByCenterId(center.getCenterId(), venueList));
            resultList.add(centerMap);
        }
        return resultList;
    }

    private List<Map<String, Object>> findVenuesByCenterId(Long centerId, List<Venue> venueList) {
        return venueList.stream().filter(p -> p.getCenterId().equals(centerId)).map(p -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("venueId", p.getVenueId());
            map.put("venueName", p.getVenueName());
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * 保存场馆信息
     *
     * @param venue
     * @return
     */
    @Transactional
    public ServiceResult save(Venue venue, Long updateStaffId, String staffs, Long tenantId) {
        // 判断场馆名称是否重复
        if (!Strings.isNullOrEmpty(venue.getVenueName())) {
            Venue params = new Venue();
            params.setVenueName(venue.getVenueName());
            params.setCenterId(venue.getCenterId());
            List<Venue> venueList = venueMapper.selectValidVenues(params);
            if (!venueList.isEmpty() && venueList.stream().noneMatch(p -> p.getVenueId().equals(venue.getVenueId()))) {
                return new ServiceResult(Errors.DATA_ERROR.getError(), "场馆名称重复");
            }
        }
        Date now = new Date();
        venue.setUpdateTime(now);
        venue.setUpdateStaffId(updateStaffId);
        if (!Strings.isNullOrEmpty(venue.getVenueName())) {
            venue.setPinyin(StringUtils.getPinYinHeadString(venue.getVenueName()));
        }
        Map<String, Object> message = Maps.newHashMap();
        if (venue.getVenueId() == null) { // 新增
            // 查询 center
            Center center = centerService.findById(venue.getCenterId());
            if (center == null) {
                return new ServiceResult(Errors.DATA_ERROR.getError(), "中心未找到");
            }
            // 新增场馆账户
            AccountData accountData = new AccountData();
            accountData.setCustName(venue.getVenueName());
            accountData.setCustType("A");
            accountData.setChannelTradeId(sequenceWrapper.tradeSequence());
            accountData.setCenterId(venue.getCenterId());
            Result result = payClient.createAccount(accountData);

            venue.setVenueId(generateVenueId(venue.getCenterId()));
            venue.setEcardCustId(result.getCustId());
            venue.setCreateTime(now);
            venue.setStartDate(now);
            venue.setEndDate(DateCalUtil.defaultEndDate());
            venue.setCreateStaffId(updateStaffId);
            venue.setCompanyId(center.getCompanyId());
            venueMapper.insert(venue);

            Set<Long> departmentIdSet = Sets.newHashSet();
            // 保存 staff_resource
            if (!Strings.isNullOrEmpty(staffs)) {
                Set<Long> staffArray = Arrays.stream(staffs.split(",")).map(Long::parseLong).collect(Collectors.toSet());
                List<StaffResource> staffResourceList = Lists.newArrayList();
                for (Long staffId : staffArray) {
                    // 查找员工
                    Staff staff = staffService.findById(staffId);
                    if (staff == null) {
                        continue;
                    }
                    departmentIdSet.add(staff.getDeptId());

                    StaffResource staffResource = new StaffResource();
                    staffResource.setStaffId(staffId);
                    staffResource.setResourceRange(Constants.ResourceRange.PERSONAL_LEVEL);
                    staffResource.setResourceType(Constants.StaffResourceType.VENUE);
                    staffResource.setResourceId(venue.getVenueId());
                    staffResource.setStartDate(now);
                    staffResource.setUpdateStaffId(updateStaffId);
                    staffResource.setUpdateTime(now);
                    staffResource.setState(Constants.Status.VALID);

                    staffResourceList.add(staffResource);
                }
                if (!staffResourceList.isEmpty()) {
                    staffResourceMapper.batchInsert(staffResourceList);
                }
            }

            // 保存 department_resource
            List<Department> departments = departmentMapper.selectTopDepartmentList(tenantId);
            for (Department department : departments) {
                departmentIdSet.add(department.getDeptId());
            }

            departmentIdSet.forEach(deptId -> {
                DepartmentResource departmentResource = new DepartmentResource();
                departmentResource.setDeptId(deptId);
                departmentResource.setResourceRange(Constants.ResourceRange.PERSONAL_LEVEL);
                departmentResource.setResourceType(Constants.StaffResourceType.VENUE);
                departmentResource.setResourceId(venue.getVenueId());
                departmentResource.setStartDate(now);
                departmentResource.setUpdateStaffId(updateStaffId);
                departmentResource.setUpdateTime(now);
                departmentResource.setState(Constants.Status.VALID);

                departmentResourceMapper.insert(departmentResource);
            });

            // 设置同步数据
            message.put("venue", venue);
            message.put("actionFlag", Constants.ActionFlag.ADD);
        } else {
            venueMapper.updateVenue(venue);

            // 设置同步数据
            message.put("venue", venueMapper.selectByPrimaryKey(venue.getVenueId()));
            message.put("actionFlag", Constants.ActionFlag.UPDATE);
        }
        // 同步数据到分库
        jobMessageService.sendMessage(tenantId, JobEnum.SYNC_VENUE_JOB, message);

        return new ServiceResult().set("venueId", venue.getVenueId());
    }

    /**
     * 生成venueId，venueId的格式是centerId + 两位数字
     *
     * @param centerId
     * @return
     */
    private Long generateVenueId(Long centerId) {
        Venue param = new Venue();
        param.setCenterId(centerId);
        List<Venue> venueList = venueMapper.selectByFields(param);
        Set<Long> set = venueList.stream().map(Venue::getVenueId).collect(Collectors.toSet());
        int sequence = venueList.size() + 1;
        Long venueId;
        do {
            venueId = Long.parseLong(centerId + Strings.padStart(Integer.toString(sequence ++), 2, '0'));
        } while (set.contains(venueId));
        return venueId;
    }

    @Transactional(readOnly = true)
    public List<Map<String, Object>> find(Map<String, Object> params, int pageNum, int pageSize) {
        return venueMapper.selectPageByFields(params, new RowBounds(pageNum, pageSize));
    }

    /**
     * 更新场馆状态，把场馆设置为有效或失效
     *
     * @param venueId
     * @param staff
     * @return
     */
    @Transactional
    public ServiceResult updateState(Long venueId, String state, LoginStaff staff) {
        Venue venue = venueMapper.selectByPrimaryKey(venueId);
        if (venue == null) {
            return new ServiceResult(Errors.DATA_NOT_FOUND.getError(), "场馆未找到");
        }
        Date now = new Date();
        if (Constants.Status.INVALID.equals(state)) {
            venue.setEndDate(now);
        } else {
            venue.setEndDate(DateCalUtil.defaultEndDate());
        }
        venue.setUpdateStaffId(staff.getStaffId());
        venue.setUpdateTime(now);
        venueMapper.updateByPrimaryKeySelective(venue);

        // 同步数据到分库
        Center center = centerService.findById(venue.getCenterId());
        Map<String, Object> message = Maps.newHashMap();
        message.put("venue", venue);
        message.put("state", state);
        message.put("actionFlag", Constants.ActionFlag.UPDATE);
        jobMessageService.sendMessage(center.getTenantId(), JobEnum.SYNC_VENUE_JOB, message);

        return new ServiceResult();
    }

    @Transactional(readOnly = true)
    public Venue findById(Long venueId) {
        return venueMapper.selectByPrimaryKey(venueId);
    }
}
