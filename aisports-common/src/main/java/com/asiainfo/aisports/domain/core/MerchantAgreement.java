package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class MerchantAgreement {
    private Long agreementId;

    private Long merchantId;

    private Date startDate;

    private Date endDate;

    private Integer feeRate;

    private Short feeCycle;

    private Date createTime;

    private Date updateTime;

    private String remark;

    private String lastFeeCycle;

    public Long getAgreementId() {
        return agreementId;
    }

    public void setAgreementId(Long agreementId) {
        this.agreementId = agreementId;
    }

    public Long getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(Long merchantId) {
        this.merchantId = merchantId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(Integer feeRate) {
        this.feeRate = feeRate;
    }

    public Short getFeeCycle() {
        return feeCycle;
    }

    public void setFeeCycle(Short feeCycle) {
        this.feeCycle = feeCycle;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public String getLastFeeCycle() {
        return lastFeeCycle;
    }

    public void setLastFeeCycle(String lastFeeCycle) {
        this.lastFeeCycle = lastFeeCycle == null ? null : lastFeeCycle.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", agreementId=").append(agreementId);
        sb.append(", merchantId=").append(merchantId);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", feeRate=").append(feeRate);
        sb.append(", feeCycle=").append(feeCycle);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", remark=").append(remark);
        sb.append(", lastFeeCycle=").append(lastFeeCycle);
        sb.append("]");
        return sb.toString();
    }
}