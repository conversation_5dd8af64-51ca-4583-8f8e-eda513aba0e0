package com.asiainfo.aisports.model.project;

/**
 * <AUTHOR>
 * @date 2025/7/3 14:58
 */
public class SeatImpDto {

    protected String stockName;

    protected String row;

    protected String startSeat;

    protected String endSeat;

    protected String invalidSeat;

    protected String num;

    protected String distType;

    public String getRow() {
        return row;
    }

    public void setRow(String row) {
        this.row = row;
    }

    public String getStartSeat() {
        return startSeat;
    }

    public void setStartSeat(String startSeat) {
        this.startSeat = startSeat;
    }

    public String getEndSeat() {
        return endSeat;
    }

    public void setEndSeat(String endSeat) {
        this.endSeat = endSeat;
    }

    public String getInvalidSeat() {
        return invalidSeat;
    }

    public void setInvalidSeat(String invalidSeat) {
        this.invalidSeat = invalidSeat;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getDistType() {
        return distType;
    }

    public void setDistType(String distType) {
        this.distType = distType;
    }

    public String getStockName() {
        return stockName;
    }

    public void setStockName(String stockName) {
        this.stockName = stockName;
    }

    @Override
    public String toString() {
        return "SeatImpDto{" +
                "stockName='" + stockName + '\'' +
                ", row='" + row + '\'' +
                ", startSeat='" + startSeat + '\'' +
                ", endSeat='" + endSeat + '\'' +
                ", invalidSeat='" + invalidSeat + '\'' +
                ", num='" + num + '\'' +
                ", distType='" + distType + '\'' +
                '}';
    }
}
