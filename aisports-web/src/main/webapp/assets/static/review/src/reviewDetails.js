import Vue from 'vue'
import ContractContent from './components/ContractContent.vue'
import intercept from 'utils/intercept'
import ProductDetails from './components/productDetails.vue'
import SpecialCardDetails from './components/specialCardDetails.vue'
import DropClassDetails from './components/dropClassDetails.vue';
import CancellationDetails from './components/cancellation.vue';
import {Tag,Table,TableColumn,Pagination,Icon,Button} from 'element-ui'

Vue.use(Tag);
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(Pagination);
Vue.use(Icon);
Vue.use(Button);


define(function (require) {
    var $ = require("jquery");
    require("./ui");
    var settings = require("aisportsConfig");
    var mAlertInstance = require("mAlert");
    var tipCover = require('handleTipCover');
    var tipIns;
    var photoSwipe = require('libs/photoSwipe/photoSwipeForSea');
    new Vue({
        el: '#product-details',
        components: {
            ProductDetails,
            SpecialCardDetails,
            DropClassDetails,
            CancellationDetails,
        },
        data() {
            return {
                info: {},
                pageInfo: {
                    total: 1,
                    pages: 1,
                },
                tableData: [],
                detailData:{},
                ossUrl: $('#ossUrl').val(),
                srcList:[],
                showPreview: false,
                scale: 1,
                rotate:0,
                imageUrl:'',
                currentIndex: 0,
                scaleStep:0.1,
            }
        },
        mounted(){
            this.queryDetails();
            
        },
        computed: {
            previewStyle() {
              return {
                transform: `scale(${this.scale}) rotate(${this.rotate}deg)`,
                transition: 'transform 1s',
              };
            },
            totalImages(){
                return this.srcList.length
            }
          },
        methods: {
            togglePreview(currentIndex) {
                this.showPreview = !this.showPreview;
                this.currentIndex = currentIndex
                this.imageUrl = this.srcList[this.currentIndex]
                this.scale = 1
                this.rotate = 0
            },
            imgout(){
                this.scale >=3 ? this.scale = 3 :this.scale +=0.1
            },
            imgin(){
                this.scale <=0.2 ? this.scale = 0.2 :this.scale -=0.1
            },
            imgrotate(direction){
                if(direction == 'right'){
                    this.rotate += 90
                }else{
                    this.rotate -= 90
                }
            },
            closePreview(event) {
                if (event.target.classList.contains('preview-modal')) {
                this.showPreview = false;
                }
            },
            closePreview2() {
                this.showPreview = false;
            },
            handleWheel(event) {
                event.preventDefault();
                const delta = Math.sign(event.deltaY);
                this.scale += delta * 0.1; // 调整缩放比例可以根据需要更改
            },
            handleScroll(event) {
                // 检查滚轮方向
                event.preventDefault();
                const delta = Math.sign(event.deltaY);

                // 根据滚轮方向调整缩放比例
                if (delta > 0 && this.scale >0.2) {
                  this.scale -= this.scaleStep; // 缩小图片
                } else if (delta < 0 && this.scale <3) {
                  this.scale += this.scaleStep; // 放大图片
                }

                // 防止缩放比例小于等于0
                if (this.scale <= 0.1) {
                    this.scale = 0.1;
                }else if(this.scale >= 3){
                    this.scale = 3;
                }
            },
            previousImage() {
                this.currentIndex = (this.currentIndex - 1 + this.totalImages) % this.totalImages;
                this.imageUrl = this.srcList[this.currentIndex]
                this.scale = 1
            },
            nextImage() {
                this.currentIndex = (this.currentIndex + 1) % this.totalImages;
                this.imageUrl = this.srcList[this.currentIndex]
                this.scale = 1
            },
            queryDetails() {
                $.ajax({
                    method: 'get',
                    url: '/audit/auditInfo',
                    data: {
                        taskId: getQueryParam('taskId')
                    },
                    success: (res) => {
                        this.info = res.data;
                        console.log(1234567,'01')
                        this.detailData.tradeId = res.data.pageData.tradeId
                        if(res.data.pageData.tradeTypeCode == '246') this.getFreezeCabinetInfoList()
                        if(this.info.pageData.imgUrl){
                            this.srcList = this.info.pageData.imgUrl.split(',').map(item=>{
                                return this.ossUrl + item
                            })
                        }
                        console.log(this.srcList)
                        // this.auditTypeList = auditTypeList.map((m, index) => ({
                        //     ...m,
                        //     index,
                        // }))
                    },
                });
            },
            currentPageChange(pages) {
                let data = { pageNum: pages }
                console.log(pages)
                this.getFreezeCabinetInfoList(data)
            },
            getFreezeCabinetInfoList(item) {
                let data = {
                    ...item,
                    tradeId:this.detailData.tradeId,
                }
                console.log(data)
                $.ajax({
                    method: 'get',
                    url: '/freezeCabinet/getFreezeCabinetInfoList',
                    data:data,
                    success: (res) => {
                        if (res.error == 0 &&res.tradeFreezeCabinet  && res.freezeCabinetInfoList) {
                            this.tableData = res.freezeCabinetInfoList.list;
                            this.pageInfo.total = res.freezeCabinetInfoList.total
                            this.detailData = { ...res.tradeFreezeCabinet }
                        }
                    },
                });
            },
        }
    })
    if($('#contract-card')){
        console.log($('#contract-card'))
        new Vue({
            el: '#contract-card',
            components: {
                ContractContent,
            }
        })
    }
    console.log('执行了-=-=-==-=--=--=-=-==')
    function loginByToken(data) {
        return $.get('/oauth/token', data).then(intercept)
    }

    function getQueryParam(name) {
        const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
        const r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    function redirectUrl() {
        let code = getQueryParam('code'),
            centerId = getQueryParam('state'),
            url = getQueryParam('url');
        // url = 'http://localhost:8082/aisports-web/audit/auditDtl?taskId=2021042800007246&tag=1'
        if (!url) return;
        const param = {
            centerId,
            code
        };

        loginByToken(param).then(res => {
            let username = {
                u: res.u,
                d: res.d,
                s: res.s.toString(),
                uid: res.uid,
                token: res.token,
                url,
            };
            $("#username").val(JSON.stringify(username))
            $("#submitBtn").click();
        });
    }

    const tipOptions = {
        pass: {
            tipTitle: '审核通过',
            tipContent: '请确认审核通过',
            buttonClickHandler: auditConfirm,
        },
        refuse: {
            tipTitle: '审核不通过',
            tipContent: '请确认审核不通过',
            buttonClickHandler: auditConfirm,
        },
    };

    function getImgs() {
        const $list = $('[data-role="photo-swipe"]');
        if ($list.length <= 0) {
            return;
        }
        $list.on('click', function () {
            const $this = $(this);
            const index = $this.index('[data-role="photo-swipe"]');
            const items = $list.map((i, item) => ({
                src: $(item).find('img').attr('src'),
            })).toArray();
            photoSwipe.initSwiper(items, index)
        })
    }

    const handleSwitchChange = function () {
        $(document).on('switchChange.bootstrapSwitch', ".switch-confirm input[type='checkbox']", function () {
            const _checked = this.checked;
            if (_checked === true) {
                $('#dateContainer').css('display', 'inline-block');
                $('.bootstrap-switch-label').addClass('change-color');
            } else {
                $('#dateContainer').css('display', 'none');
                $('.bootstrap-switch-label').removeClass('change-color');
            }
        });
    }

    /**
     * 创建 tipCover
     */
    function createTipCover() {
        tipIns = tipCover.createTipCover(tipOptions.pass);
    }

// 查询变更申请单
    $('#protocolQryBtn').bind('click', function () {
        if (!$(this).attr('protocolUrl') || $(this).attr('protocolUrl') == '') {
            mAlertInstance.showWarning('未查询到相关的变更申请单');
        } else {
            alert($(this).attr('ossUrl') + $(this).attr('protocolUrl'));
        }
    });

    $(document).on('click', '.trans-sub .member-link', function () {
        const $this = $(this);
        if ($this.html() != '') {
            window.open(`${settings.urlPrefix}/memberSearch/index?ecardNo=${$('#eCardNo').text()}`);
        }
    });

// 业务提交
    const handleBindSubmit = function () {
        $('#auditConfirm,#auditCancel').bind('click', function () {
            let params = {};
            if ($(this).attr('id') == 'auditConfirm') {
                params.auditTag = 1;
            } else {
                params.auditTag = 0;
            }
            const actionUrl = $(this).attr('actionUrl');
            params = $.extend(params, {
                taskId: $(this).attr('taskId') || '',
                tradeId: $(this).attr('tradeId') || '',
            });
            params = $.extend(params, {pageData: $(this).attr('pageData')});
            params = $.extend(params, {auditComment: $('#auditComment').val().trim() || ''});
            if (params.auditTag === 0 && params.auditComment === '') {
                mAlertInstance.showWarning('审核不通过,必须填写审核意见')
                return;
            }
            if (!$('#custName').html()) {
                const a = JSON.parse(params.pageData);
                a.custName = '无';
                params.pageData = JSON.stringify(a);
            }
            tipIns.args = {
                params,
                actionUrl,
            }
            tipIns.update(params.auditTag === 1 ? tipOptions.pass : tipOptions.refuse);
            tipIns.showTipCover();
        });
    }

// 审核确定
    function auditConfirm(data) {
        const $el = $(data.el);
        const role = $el.attr('data-role');
        data.$container.hide();
        if (role === 'cancel') {
            return;
        }
        $.ajax({
            type: 'POST',
            url: data.context.args.actionUrl,
            data: data.context.args.params,
            success(data) {
                if (data.hasOwnProperty('error')) {
                    if (data.error == 0) {
                        settings.spinner.showSuccessResult(data.message);
                        settings.spinner.addHideEventHandle(e => {
                            if (e = 'success') {
                                window.location.href = settings.urlPrefix + data.redirect;
                            }
                        });
                    } else {
                        settings.spinner.showErrorResult(data.message || '');
                    }
                } else {
                    settings.spinner.showErrorResult(data);
                }
            },
        });
    }

    function handleRemarks() {
        $(document).on('click', '[data-click="show-remark"]', () => {
            $('#remark-wrap').show();
        });
        $(document).on('click', '[data-click="close-remark"]', () => {
            $('#remark-wrap').hide();
        });
    }

// 展示证明材料
    function showProve() {
        $(document).on('click', '.checkinfo', function () {
            const proveImg = $(this).children('a').attr('data-value');
            window.open(`${settings.urlPrefix}/audit/showProveForAudit?proveImg=${proveImg}`);
        });
    }

//
    function getDetailPage() {
        const $el = $('#ext-detail');
        const url = $el.val();
        if (url === '') {
            getImgs();
            return;
        }
        $.ajax({
            url,
            success(data) {
                $('#ext-con').html(data);
                getImgs();
            },
        })
    }

    // 附件图片放大缩小
    function initZoomPic() {
        var rate = 5000;
        const calc = (value) => 1 + value / (rate || 5000);
        $('.imgContainer').bind('mousewheel', function (e, delta) {
            // $(this).css('z-index',10)
            // $(this).siblings().css('z-index',0)
            e.target.style.width = e.target.offsetWidth * calc(e.originalEvent.wheelDelta) + 'px'
            e.target.style.height = e.target.offsetHeight * calc(e.originalEvent.wheelDelta) + 'px'
            e.preventDefault()
        });
    }

// 打印
    function print() {
        $(document).on('click', '.print-btn', () => {
            window.print();
        })
    }

    $(function () {
        redirectUrl();
        settings.util.PrintHF()
        handleSwitchChange();
        handleBindSubmit();
        handleRemarks();
        showProve();
        getDetailPage();
        createTipCover();
        print();
        initZoomPic()
    });
});
