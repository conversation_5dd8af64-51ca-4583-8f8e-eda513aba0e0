package com.asiainfo.aisports.domain.core;

public class DepositDetail {
    private Long depositDetailId;

    private Long tradeId;

    private Long depositId;

    private Long acctId;

    private String payModeCode;

    private Long rechargeMoney;

    private Long balance;

    private Long realPay;

    public Long getDepositDetailId() {
        return depositDetailId;
    }

    public void setDepositDetailId(Long depositDetailId) {
        this.depositDetailId = depositDetailId;
    }

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    public Long getDepositId() {
        return depositId;
    }

    public void setDepositId(Long depositId) {
        this.depositId = depositId;
    }

    public Long getAcctId() {
        return acctId;
    }

    public void setAcctId(Long acctId) {
        this.acctId = acctId;
    }

    public String getPayModeCode() {
        return payModeCode;
    }

    public void setPayModeCode(String payModeCode) {
        this.payModeCode = payModeCode == null ? null : payModeCode.trim();
    }

    public Long getRechargeMoney() {
        return rechargeMoney;
    }

    public void setRechargeMoney(Long rechargeMoney) {
        this.rechargeMoney = rechargeMoney;
    }

    public Long getBalance() {
        return balance;
    }

    public void setBalance(Long balance) {
        this.balance = balance;
    }

    public Long getRealPay() {
        return realPay;
    }

    public void setRealPay(Long realPay) {
        this.realPay = realPay;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", depositDetailId=").append(depositDetailId);
        sb.append(", tradeId=").append(tradeId);
        sb.append(", depositId=").append(depositId);
        sb.append(", acctId=").append(acctId);
        sb.append(", payModeCode=").append(payModeCode);
        sb.append(", rechargeMoney=").append(rechargeMoney);
        sb.append(", balance=").append(balance);
        sb.append(", realPay=").append(realPay);
        sb.append("]");
        return sb.toString();
    }
}