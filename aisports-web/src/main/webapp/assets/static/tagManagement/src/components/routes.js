import list from './list.vue'
import detail from './detail.vue'

const NoMatch = { template: '<div>页面未找到！</div>' }

export const routes = [
    {
        path: '/',
        component: list,
        name:'list'
    },
    {   name: 'detail',
        path: '/detail/:id',
        component: detail,
    },
    // {
    //     path: '/class-detail' ,
    //     component : ClassTimeEdit,
    //     props: route => ({ timeId: route.query.timeId })
    // },
    {
        path: '*',
        component: NoMatch,
    },
]

export default routes
