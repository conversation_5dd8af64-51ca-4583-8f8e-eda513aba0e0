package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.EntCustAgreement;
import com.asiainfo.aisports.domain.core.Enterprise;
import com.asiainfo.aisports.domain.core.Venue;
import com.asiainfo.aisports.model.CustInfo;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.TradeMore;
import com.asiainfo.aisports.service.*;
import com.asiainfo.aisports.storage.OssFilePath;
import com.asiainfo.aisports.tools.TradeConstants;
import com.asiainfo.aisports.web.WebResult;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Created by fuzf on 2015/12/23.
 * <p/>
 * 企业协议挂账
 */

@Controller
@RequestMapping(value = "/entAgreements")
public class EntCustAgreementController {
    private static final ThreadLocal<DateFormat> datetimeFormat = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));
    @Autowired
    private VenueService venueService;
    @Autowired
    private EnterpriseService enterpriseService;
    @Autowired
    private CustQueryService custQueryService;
    @Autowired
    private SequenceWrapper sequenceWrapper;
    @Autowired
    private EntCustAgreementService entCustAgreementService;
    @Autowired
    private OssFilePath ossFilePath;
    @Autowired
    private UploadFileService uploadFileService;

    /**
     * 进入企业协议挂账首页
     *
     * @param staff
     * @return
     */
    @RequestMapping(value = "/init", method = RequestMethod.GET)
    public String init(LoginStaff staff, ModelMap modelMap) {
        List<Venue> venueList = venueService.findAll(staff.getCenterId());
        modelMap.put("venueName", staff.getVenueName());
        modelMap.put("venueList", venueList);
        return "entCustAgreement/entCustAgreement";
    }

    /**
     * 获取企业客户相关信息
     *
     * @param staff
     * @param ecardNo
     * @return
     */
    @RequestMapping(value = "/getEntInfo", method = RequestMethod.POST)
    @ResponseBody
    public WebResult getEntInfo(LoginStaff staff,
                                @RequestParam(value = "ecardNo", required = true) String ecardNo) {
        if (Strings.isNullOrEmpty(ecardNo)) {
            return new WebResult(1, "卡号不能为空！");
        }

        CustInfo custInfo = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        if (custInfo == null) {
            return new WebResult(1, "请输入正确卡号！");
        } else if (!custInfo.getCustType().equals(Constants.CustomerType.ENTERPRISE)) {
            return new WebResult(1, "该卡号不是企业客户！");
        }
        Enterprise enterprise = null;
        if (custInfo.getEnterpriseId() != null) {
            enterprise = enterpriseService.selectByPrimaryKey(custInfo.getEnterpriseId());
        }
        if (enterprise == null) {
            return new WebResult(1, "没有公司资料！");
        }

        return new WebResult().set("enterprise", enterprise).set("custInfo", custInfo);
    }

    /**
     * 提交
     *
     * @param staff
     * @param ecardNo
     * @param startDate
     * @param endDate
     * @param venueId
     * @param enterpriseName
     * @param remark
     * @return
     */
    @RequestMapping(value = "/submitEntAgreement", method = RequestMethod.POST)
    @ResponseBody
    public WebResult submitEntAgreementsConsumption(@RequestParam("upload") MultipartFile file, LoginStaff staff,
                                                    @RequestParam(value = "ecardNo", required = false) String ecardNo,
                                                    @RequestParam(value = "startDate", required = false) String startDate,
                                                    @RequestParam(value = "endDate", required = false) String endDate,
                                                    @RequestParam(value = "venueId", required = false) Long venueId,
                                                    @RequestParam(value = "enterpriseName", required = false) String enterpriseName,
                                                    @RequestParam(value = "remark", required = false) String remark,
                                                    String agreementNo) throws ParseException {
        CustInfo custInfo = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        Long entAgreementId = sequenceWrapper.entAgreementSequence();
        Long tradeId = sequenceWrapper.tradeSequence();
        ServiceResult result = uploadFileService.uploadFile(file, ossFilePath.getAgreementFilePath(staff.getCenterId()), false, staff);
        if (result.getError() != 0) {
            return new WebResult(result.getError(), result.getMessage());
        }

        EntCustAgreement entCustAgreement = new EntCustAgreement();
        entCustAgreement.setAgreementId(entAgreementId);
        entCustAgreement.setStartDate(datetimeFormat.get().parse(startDate));
        entCustAgreement.setEndDate(datetimeFormat.get().parse(endDate));
        entCustAgreement.setEntCustId(custInfo.getCustId());
        entCustAgreement.setAgreementType(Constants.EntCustAgreementType.ON_CREDIT);
        entCustAgreement.setAgreementNo(agreementNo);
        entCustAgreement.setUpdateStaffId(staff.getStaffId());
        entCustAgreement.setFilePath((String) result.get("fileName"));
        entCustAgreement.setCreateTime(new Date());
        entCustAgreement.setCreateTradeId(tradeId);
        entCustAgreement.setVenueId(staff.getVenueId());
        entCustAgreement.setCenterId(staff.getCenterId());

        TradeMore trade = new TradeMore();
        trade.setTradeId(tradeId);
        trade.setVenueId(venueId);
        trade.setCenterId(staff.getCenterId());
        trade.setEnterpriseName(enterpriseName);
        trade.setAgreementId(entAgreementId);
        trade.setAcceptDate(new Date());
        trade.setCustId(custInfo.getCustId());
        trade.setCustName(custInfo.getCustName());
        trade.setEcardNo(ecardNo);
        trade.setLeadingPhone(custInfo.getLeadingPhone());
        trade.setBusinessType(Constants.BusinessType.ENT_CUST_AGREEMENT);
        trade.setTradeTypeCode(TradeConstants.TradeTypeCode.ON_CREDIT_AGREEMENT.getLongValue());
        trade.setTradeDesc("企业客户挂账协议");
        trade.setRemark(remark);

        entCustAgreementService.saveEntAgreementsConsumption(trade, entCustAgreement, staff);

        return new WebResult();
    }
}
