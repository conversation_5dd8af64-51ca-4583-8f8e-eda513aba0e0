export default {
    computed: {
        serviceList() {
            return window.__serviceList__
        },
        courseTypeList() {
            return window.__courseTypeList__
        },
        levelList() {
            let curService
            const _this = this
            curService = _.find(window.__serviceList__, service => service.service.serviceId === _this.serviceId)
            return curService ? curService.coachLevelList : []
        },
    },
}
