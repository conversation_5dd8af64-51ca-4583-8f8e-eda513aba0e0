;define(function(require , exports , module){

    var $ = require('jquery'),
        getUserMedia = require('getUserMedia'),
        settings = require('aisportsConfig'),
        Webcam2 =  require('libs/webcam2').default,
        isCamera2 = false,
        App = {
            init: function () {

                // The shim requires options to be supplied for it's configuration,
                // which can be found lower down in this file. Most of the below are
                // demo specific and should be used for reference within this context
                // only
                if ( !!this.options ) {
                    var _this = this
                    this.pos = 0;
                    this.cam = null;
                    this.filter_on = false;
                    this.filter_id = 0;
                    this.canvas = document.getElementById("canvas");
                    this.ctx = this.canvas.getContext("2d");
                    this.img = new Image();
                    this.ctx.clearRect(0, 0, this.options.width, this.options.height);
                    this.image = this.ctx.getImageData(0, 0, this.options.width, this.options.height);
                    this.snapshotBtn = document.getElementById('takeSnapshot');
                    // this.detectBtn = document.getElementById('detectFaces');

                    // Trigger a snapshot
                    this.addEvent('click', this.snapshotBtn, this.getSnapshot);

                    // Trigger face detection (using the glasses option)
                    // this.addEvent('click', this.detectBtn, function () {
                    // 	App.drawToCanvas('glasses');
                    // });

                    // 先使用第三方摄像头控件
                    var camera2 = new Webcam2('#webcam')
                    setTimeout(function() {
                        if (camera2.camWorked) {
                            isCamera2 = true
                        } else {
                            isCamera2 = false
                            // Initialize getUserMedia with options
                            getUserMedia(_this.options, _this.success, _this.deviceError);
                            // Initialize webcam options for fallback
                            window.webcam = _this.options;
                        }
                    }, 500)
                } else {
                    console.log('No options were supplied to the shim!');
                }
            },
            clear: function(){
            	if(!App.stream){
            		return
				}
                try{
                    App.stream.getTracks()[0].stop();
                    App.stream.stop && App.stream.stop();
				}
				catch(e){
                	console.log(e)
				}
            },
            setup: function(){
                var self = this;
                $(document).on('click','#camera', function(){
                    $('#camera-pop').show();
                    self.init();
                });

                $(document).on('click','#snap-apply', function(){
                    window.stream && window.stream.stop && window.stream.stop()
                    var snap = new Image(),
                        canvas = document.getElementById('canvas'),
                        ctx = canvas.getContext('2d');

                    snap.setAttribute('id', 'photoImg');
                    snap.src = canvas.toDataURL();
                    $('#camera').html('').append(snap);
                    $('#camera').find("img").css({
                        width:"auto",
                        heigth:"auto"
                    });
                    $('#camera').trigger("srcChange");
                    //$('#camera-pop').hide();
                    if (isCamera2) {
                        document.all.cap1 && document.all.cap1.stop()
                    } else if (App.options.context === 'webrtc') {
                        self.clear();
                    } else if(App.options.context === 'flash'){
                        //$('#XwebcamXobjectX').remove();
                        self.clear();
                        self.init();
                    }
                    else{
                        console.log('No context was supplied to getSnapshot()');
                    }
                });
            },
            takeSnapShot : function () {
                var canvas = document.getElementById('canvas');
                return canvas.toDataURL();
                if (App.options.context === 'webrtc') {
                    self.clear();
                } else if(App.options.context === 'flash'){
                    //$('#XwebcamXobjectX').remove();
                    self.clear();
                    self.init();
                }
                else{
                    console.log('No context was supplied to getSnapshot()');
                }
            },
            addEvent: function (type, obj, fn) {
                if (obj.attachEvent) {
                    obj['e' + type + fn] = fn;
                    obj[type + fn] = function () {
                        obj['e' + type + fn](window.event);
                    }
                    obj.attachEvent('on' + type, obj[type + fn]);
                } else {
                    obj.addEventListener(type, fn, false);
                }
            },

            // options contains the configuration information for the shim
            // it allows us to specify the width and height of the video
            // output we're working with, the location of the fallback swf,
            // events that are triggered onCapture and onSave (for the fallback)
            // and so on.
            options: {
                "audio": false, //OTHERWISE FF nightlxy throws an NOT IMPLEMENTED error
                "video": true,
                el: "webcam",

                extern: null,
                append: true,

                // noFallback:true, use if you don't require a fallback

                width: 320,
                height: 240,

                mode: "callback",
                // callback | save | stream
                swffile: settings.flashFallBack,
                // "../dist/fallback/jscam_canvas_only.swf",
                quality: 85,
                context: "",

                debug: function () {},
                onCapture: function () {
                    window.webcam.save();
                },
                onTick: function () {},
                onSave: function (data) {

                    var col = data.split(";"),
                        img = App.image,
                        tmp = null,
                        w = this.width,
                        h = this.height;

                    for (var i = 0; i < w; i++) {
                        tmp = parseInt(col[i], 10);
                        img.data[App.pos + 0] = (tmp >> 16) & 0xff;
                        img.data[App.pos + 1] = (tmp >> 8) & 0xff;
                        img.data[App.pos + 2] = tmp & 0xff;
                        img.data[App.pos + 3] = 0xff;
                        App.pos += 4;
                    }

                    if (App.pos >= 4 * w * h) {
                        App.ctx.putImageData(img, 0, 0, 0, 0, w, h);
                        App.pos = 0;
                    }

                },
                onLoad: function () {}
            },

            success: function (stream) {
                App.stream = stream;
                if (App.options.context === 'webrtc') {
                    window.stream = stream
                    var video = App.options.videoEl;

                    var videoTracks = stream.getVideoTracks();

                    App.options.videoEl = attachMediaStream(video, stream); 

                    // var video = App.options.videoEl;

                    // if ((typeof MediaStream !== "undefined" && MediaStream !== null) && stream instanceof MediaStream) {

                    //     if (video.mozSrcObject !== undefined) { // FF18a
                    //         video.mozSrcObject = stream;
                    //     } else if (video.srcObject !== undefined) {
                    //         video.srcObject = stream;
                    //     } else { // FF16a, 17a
                    //         video.srcObject = stream;
                    //     }

                    //     return video.play();

                    // } else {
                    //     var vendorURL = window.URL || window.webkitURL;
                    //     video.src = vendorURL ? vendorURL.createObjectURL(stream) : stream;
                    // }

                    video.onerror = function () {
                        stream.stop();
                        streamError();
                    };

                } else{
                    // flash context
                }

            },

            deviceError: function (error) {
                console.log('No camera available.');
                console.error('An error occurred: [CODE ' + error.code + ']');
            },

            changeFilter: function () {
                if (this.filter_on) {
                    this.filter_id = (this.filter_id + 1) & 7;
                }
            },

            getSnapshot: function () {
                // If the current context is WebRTC/getUserMedia (something
                // passed back from the shim to avoid doing further feature
                // detection), we handle getting video/images for our canvas
                // from our HTML5 <video> element.
                if (isCamera2) {
                    document.all.cap1.cap()
                    // 接上原来的逻辑，canvas，drawImage
                    var img = new Image()
                    img.src = 'data:image/jpg;base64,' + document.all.cap1.jpegBase64Data
                    img.onload = function() {
                        document.all.cap1.start()
                        var canvas = document.getElementById('canvas')
                        var ctx = canvas.getContext('2d')
                        
                        var width = img.width
                        var height = img.height
                        var offsetX, offsetY, sWidth, sHeight
                        if (width > height) {
                            offsetX = (width - (height * (300 / 240))) / 2
                            offsetY = 0
                            sWidth = width - offsetX * 2
                            sHeight = height
                        } else {
                            offsetX = 0
                            offsetY = (height - (width * (240 / 300))) / 2
                            sWidth = width
                            sHeight = height - offsetY * 2
                        }
                        ctx.clearRect(0, 0, 300, 240)
                        // 绘图出不来
                        console.dir(img)
                        console.log(offsetX, offsetY, sWidth, sHeight)
                        ctx.drawImage(img, offsetX, offsetY, sWidth, sHeight, 0, 0, 300, 240)
                    }
                } else if (App.options.context === 'webrtc') {
                    var video = App.options.videoEl;
                    if (webrtcDetectedType === 'plugin') {
                        var base64 = video.getFrame();
                        var image = new Image();
                        image.onload = function () {
                            App.canvas.getContext('2d').drawImage(image, 0, 0, App.canvas.width, App.canvas.height);
                        };
                        image.setAttribute('src', 'data:image/png;base64,' + base64);
                    } else {
                        // const video = document.getElementsByTagName('video')[0];
                        App.canvas.width = video.clientWidth;
                        App.canvas.height = video.clientHeight;
                        const ctx = App.canvas.getContext('2d');
                        ctx.clearRect(0, 0, App.width, App.height);
                        ctx.drawImage(
                            video, 0, 0, video.videoWidth, video.videoHeight,
                            0, 0, video.clientWidth, video.clientHeight,
                        );
                    }
                } else if(App.options.context === 'flash'){

                    window.webcam.capture();
                    App.changeFilter();
                }
                else{
                    console.log('No context was supplied to getSnapshot()');
                }
            }
        };
    module.exports = App;
});