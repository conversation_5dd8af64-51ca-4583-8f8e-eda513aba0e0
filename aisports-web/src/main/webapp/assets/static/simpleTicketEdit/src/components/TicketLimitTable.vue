<template>
    <div>
        <table class="simple-splitter">
            <thead>
                <tr>
                    <th>用户角色</th>
                    <th>购票时段</th>
                    <th>可提前购票天数</th>
                    <th>删除</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(item, key) in rulesList" :key="key">
                    <td>
                        <el-select v-model="item.role">
                            <el-option v-for="(i,index) in roleList" :key="index" :value="i.paramKey"
                                :label="i.paramValue" :disabled="getDisabled(i.paramKey)"></el-option>
                        </el-select>
                    </td>
                    <td>
                        <el-time-picker placeholder="起始时间" size="medium" value-format="HH:mm:ss"
                            v-model="item.startSegment">
                        </el-time-picker>
                        <span>至</span>
                        <el-time-picker placeholder="结束时间" size="medium" value-format="HH:mm:ss"
                            v-model="item.endSegment">
                        </el-time-picker>
                    </td>
                    <td>
                        <el-input-number v-model="item.advanceDays" :min="1"></el-input-number>
                    </td>
                    <td>
                        <el-button type="text" @click="deleteRules(key)">删除</el-button>
                    </td>
                </tr>
                <tr>
                    <td>
                        <el-button type="text" @click="addRules">新增限制规则</el-button>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="bottom-btns">
            <button class="btn btn-lg btn-hw-1fa2f5 cancel-edit" onClick="window.history.back()">取消</button>
            <button class="btn btn-lg btn-1fa2f5 save" @click="saveTicketLimit">保存</button>
        </div>
    </div>
</template>
<script>
import { Popover, Select, Option, TimePicker, InputNumber, Button } from 'element-ui'
import $ from 'jquery'
import intercept from 'utils/intercept'
// api
const queryRoleList = () => $.get('/simpleTicketManage/getRoleList').then(intercept)
const getTicketRestriction = data => $.get('/simpleTicketManage/getTicketRestriction', data).then(intercept)
const saveTicketRestriction = data => $.post('/simpleTicketManage/saveTicketRestrictionList', data).then(intercept)


export default {
    components: {
        'el-popover': Popover,
        'el-select': Select,
        'el-option': Option,
        'el-time-picker': TimePicker,
        'el-input-number': InputNumber,
        'el-button': Button
    },
    data() {
        return {
            rulesList: [
            ], // 限制规则的数组
            roleList: [], // 角色列表
        }
    },
    computed: {

    },
    mounted() {
        this.ticketTypeId = $('#ticketTypeId').val()
        this.getRoleListHandle()
        this.getLimitTicjet()
    },
    methods: {
        // 获取次票的购票限制
        getLimitTicjet() {
            getTicketRestriction({ ticketTypeId: this.ticketTypeId }).then(res => {
                if (res.restrictionList.length) {
                    this.rulesList = res.restrictionList
                } else {
                    this.rulesList.push({
                        role: "",
                        startSegment: "",
                        endSegment: "",
                        advanceDays: 1,
                    })
                }

            })
        },
        // 获取角色列表
        getRoleListHandle() {
            queryRoleList().then(res => {
                this.roleList = res.roleList
            })
        },
        // 新增限制规则
        addRules() {
            this.rulesList.push({
                role: "",
                startSegment: "",
                endSegment: "",
                advanceDays: 1,
            })
        },
        // 删除限制规则
        deleteRules(index) {
            console.log(index);
            this.rulesList.splice(index, 1)
        },
        getDisabled(value) {
            return this.rulesList.some(i => i.role == value)
        },
        // 保存购票限制
        saveTicketLimit() {
            const ticketRestrictionInfo = {
                ticketTypeId: this.ticketTypeId,
                ruleList: this.rulesList
            }
            saveTicketRestriction({ ticketRestrictionInfo: JSON.stringify(ticketRestrictionInfo) }).then(res => {
                this.getLimitTicjet()
            })
        }
    },
}
</script>
