package com.asiainfo.aisports.web.resolver;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.model.LoginSite;
import com.asiainfo.aisports.model.LoginStaff;
import org.springframework.beans.BeanUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * project:aisports
 * package:com.asiainfo.aisports.resolver
 * Created by Stomic on 14/12/15.
 */
public class StaffArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType().equals(LoginStaff.class)
                || parameter.getParameterType().equals(LoginSite.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest,
                                  WebDataBinderFactory binderFactory) {
        if (parameter.getParameterType().equals(LoginStaff.class)) {
            LoginStaff loginStaff = (LoginStaff) webRequest.getAttribute(Constants.SessionKey.STAFF, RequestAttributes.SCOPE_SESSION);
            if (loginStaff != null) {
                LoginStaff staff = new LoginStaff();
                BeanUtils.copyProperties(loginStaff, staff);
                return staff;
            }
        } else if (parameter.getParameterType().equals(LoginSite.class)) {
            return webRequest.getAttribute(Constants.SessionKey.LOGIN_SITE, RequestAttributes.SCOPE_SESSION);
        }
        return null;
    }

}
