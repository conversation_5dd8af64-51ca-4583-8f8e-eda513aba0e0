package com.asiainfo.aisports.domain.core;

public class WechatAcctModuleKey {
    private Long wechatAccountId;

    private Long moduleId;

    public Long getWechatAccountId() {
        return wechatAccountId;
    }

    public void setWechatAccountId(Long wechatAccountId) {
        this.wechatAccountId = wechatAccountId;
    }

    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", wechatAccountId=").append(wechatAccountId);
        sb.append(", moduleId=").append(moduleId);
        sb.append("]");
        return sb.toString();
    }
}