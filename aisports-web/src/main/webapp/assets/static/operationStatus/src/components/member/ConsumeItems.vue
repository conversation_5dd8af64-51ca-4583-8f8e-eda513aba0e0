<template>
    <div class="colx-4">
        <chart
            ref="chart"
            height="562"
            title="会员项目消费喜好"
            unit="（单位：次）">

        </chart>
    </div>
</template>

<script>
import bar from '../charts/bar'
import fetchData from '../common/fetchData'

export default {
    mixins: [fetchData],
    methods: {
        paintChart(res) {
            const list = res.masterCenterMemberLoveRank.map(mc => ({
                key: mc.key,
                value: mc.value,
            }))
            return bar(this.$refs.chart.$refs.cbd, list, function(option) {
                option.xAxis.axisLabel = {
                    show: true,
                    interval: 0,
                }
            })
        },
    },
}
</script>
