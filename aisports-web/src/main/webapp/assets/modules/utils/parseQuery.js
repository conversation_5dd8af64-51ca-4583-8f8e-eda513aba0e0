/**
 * 将 query string 转换为 object
 * @param {string} queryString
 * @return {object}
 */
define(function (require , exports , module) {
    function parseQuery(queryString) {
        var params = queryString.split('&');
        var object = {};

        params.forEach(function(param){
            var key = param.split('=')[0];
            var val = param.split('=')[1];

            object[key] = val;
        })

        return object;
    }
    module.exports = parseQuery;
})

