package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.PoAccountChange;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PoAccountChangeMapper {
    @Delete({
        "delete from po_account_change",
        "where points_change_id = #{pointsChangeId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long pointsChangeId);

    @Insert({
        "insert into po_account_change (points_change_id, points_account_id, ",
        "trade_id, net_user_id, ",
        "cust_id, change_type, ",
        "change_points, balance, ",
        "get_log_id, center_id, ",
        "channel_id, change_desc, ",
        "remark, create_time, ",
        "create_staff_id, update_time, ",
        "update_venue_id, update_staff_id)",
        "values (#{pointsChangeId,jdbcType=BIGINT}, #{pointsAccountId,jdbcType=BIGINT}, ",
        "#{tradeId,jdbcType=BIGINT}, #{netUserId,jdbcType=BIGINT}, ",
        "#{custId,jdbcType=BIGINT}, #{changeType,jdbcType=VARCHAR}, ",
        "#{changePoints,jdbcType=INTEGER}, #{balance,jdbcType=INTEGER}, ",
        "#{getLogId,jdbcType=BIGINT}, #{centerId,jdbcType=BIGINT}, ",
        "#{channelId,jdbcType=BIGINT}, #{changeDesc,jdbcType=VARCHAR}, ",
        "#{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateVenueId,jdbcType=BIGINT}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(PoAccountChange record);

    @Select({
        "select",
        "points_change_id, points_account_id, trade_id, net_user_id, cust_id, change_type, ",
        "change_points, balance, get_log_id, center_id, channel_id, change_desc, remark, ",
        "create_time, create_staff_id, update_time, update_venue_id, update_staff_id",
        "from po_account_change",
        "where points_change_id = #{pointsChangeId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
        @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
        @Result(column="balance", property="balance", jdbcType=JdbcType.INTEGER),
        @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_venue_id", property="updateVenueId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    PoAccountChange selectByPrimaryKey(Long pointsChangeId);

    @Update({
        "update po_account_change",
        "set points_account_id = #{pointsAccountId,jdbcType=BIGINT},",
        "trade_id = #{tradeId,jdbcType=BIGINT},",
        "net_user_id = #{netUserId,jdbcType=BIGINT},",
        "cust_id = #{custId,jdbcType=BIGINT},",
        "change_type = #{changeType,jdbcType=VARCHAR},",
        "change_points = #{changePoints,jdbcType=INTEGER},",
        "balance = #{balance,jdbcType=INTEGER},",
        "get_log_id = #{getLogId,jdbcType=BIGINT},",
        "center_id = #{centerId,jdbcType=BIGINT},",
        "channel_id = #{channelId,jdbcType=BIGINT},",
        "change_desc = #{changeDesc,jdbcType=VARCHAR},",
        "remark = #{remark,jdbcType=VARCHAR},",
        "create_time = #{createTime,jdbcType=TIMESTAMP},",
        "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
        "update_time = #{updateTime,jdbcType=TIMESTAMP},",
        "update_venue_id = #{updateVenueId,jdbcType=BIGINT},",
        "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where points_change_id = #{pointsChangeId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(PoAccountChange record);

    @Select({
        "<script>",
        "select",
        "points_change_id, points_account_id, trade_id, net_user_id, cust_id, change_type, change_points, balance, get_log_id, center_id, channel_id, change_desc, remark, create_time, create_staff_id, update_time, update_venue_id, update_staff_id",
        "from po_account_change",
        "<where>",
        "<if test=\"pointsChangeId != null\">and points_change_id = #{pointsChangeId,jdbcType=BIGINT}</if>",
        "<if test=\"pointsAccountId != null\">and points_account_id = #{pointsAccountId,jdbcType=BIGINT}</if>",
        "<if test=\"tradeId != null\">and trade_id = #{tradeId,jdbcType=BIGINT}</if>",
        "<if test=\"netUserId != null\">and net_user_id = #{netUserId,jdbcType=BIGINT}</if>",
        "<if test=\"custId != null\">and cust_id = #{custId,jdbcType=BIGINT}</if>",
        "<if test=\"changeType != null\">and change_type = #{changeType,jdbcType=VARCHAR}</if>",
        "<if test=\"changePoints != null\">and change_points = #{changePoints,jdbcType=INTEGER}</if>",
        "<if test=\"balance != null\">and balance = #{balance,jdbcType=INTEGER}</if>",
        "<if test=\"getLogId != null\">and get_log_id = #{getLogId,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"channelId != null\">and channel_id = #{channelId,jdbcType=BIGINT}</if>",
        "<if test=\"changeDesc != null\">and change_desc = #{changeDesc,jdbcType=VARCHAR}</if>",
        "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateVenueId != null\">and update_venue_id = #{updateVenueId,jdbcType=BIGINT}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
        @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
        @Result(column="balance", property="balance", jdbcType=JdbcType.INTEGER),
        @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_venue_id", property="updateVenueId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PoAccountChange> selectByFields(PoAccountChange param);

    @Update({
        "<script>",
        "update po_account_change",
        "<set>",
        "<if test=\"pointsAccountId != null\">points_account_id = #{pointsAccountId,jdbcType=BIGINT},</if>",
        "<if test=\"tradeId != null\">trade_id = #{tradeId,jdbcType=BIGINT},</if>",
        "<if test=\"netUserId != null\">net_user_id = #{netUserId,jdbcType=BIGINT},</if>",
        "<if test=\"custId != null\">cust_id = #{custId,jdbcType=BIGINT},</if>",
        "<if test=\"changeType != null\">change_type = #{changeType,jdbcType=VARCHAR},</if>",
        "<if test=\"changePoints != null\">change_points = #{changePoints,jdbcType=INTEGER},</if>",
        "<if test=\"balance != null\">balance = #{balance,jdbcType=INTEGER},</if>",
        "<if test=\"getLogId != null\">get_log_id = #{getLogId,jdbcType=BIGINT},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"channelId != null\">channel_id = #{channelId,jdbcType=BIGINT},</if>",
        "<if test=\"changeDesc != null\">change_desc = #{changeDesc,jdbcType=VARCHAR},</if>",
        "<if test=\"remark != null\">remark = #{remark,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateVenueId != null\">update_venue_id = #{updateVenueId,jdbcType=BIGINT},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where points_change_id = #{pointsChangeId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(PoAccountChange record);

    @Select({
            "<script>",
            "select",
            "a.points_change_id, a.points_account_id, a.trade_id, a.net_user_id, a.cust_id, a.change_type, ",
            "a.change_points, a.get_log_id, a.center_id, a.channel_id, a.change_desc, a.remark, a.create_time, ",
            "a.create_staff_id, a.update_time, a.update_venue_id, a.update_staff_id",
            "from po_account_change a, po_account b ",
            "where b.state = '1' ",
            "<if test=\"netUserId != null\"> and a.net_user_id = #{netUserId,jdbcType=BIGINT} ",
            "and a.net_user_id = b.net_user_id </if>",
            "<if test=\"custId != null\"> and b.cust_id = #{custId,jdbcType=BIGINT} ",
            "and b.cust_id = a.cust_id </if>",
            "<if test=\"pointsType != null\">",
            "and b.points_type = #{pointsType,jdbcType=VARCHAR} ",
            "</if>",
            "and a.points_account_id = b.points_account_id ",
            "order by a.create_time desc ",
            "</script>"
    })
    @Results({
            @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
            @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
            @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
            @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
            @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
            @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
            @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
            @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_venue_id", property="updateVenueId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PoAccountChange> selectInAndOut(@Param("netUserId") Long netUserId,
                                         @Param("custId") Long custId,
                                         @Param("pointsType") String pointsType,
                                         RowBounds rowBounds);

    @Select({
            "select",
            "a.points_change_id, a.points_account_id, a.trade_id, a.net_user_id, a.cust_id, a.change_type, ",
            "a.change_points, a.get_log_id, a.center_id, a.channel_id, a.change_desc, a.remark, a.create_time, ",
            "a.create_staff_id, a.update_time, a.update_venue_id, a.update_staff_id",
            "from po_account_change a, trade b",
            "where a.points_account_id = #{pointsAccountId,jdbcType=BIGINT} ",
            "and b.accept_date between curdate() and date_add(curdate(), interval 1 day) ",
            "and a.net_user_id = #{netUserId,jdbcType=BIGINT}",
            "and a.trade_id = b.trade_id",
            "and b.trade_type_code = 139",
            "and b.business_type = 37",
            "and a.change_type = '1'"
    })
    @Results({
            @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
            @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
            @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
            @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
            @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
            @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
            @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
            @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_venue_id", property="updateVenueId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PoAccountChange> selectDayChangeByAccount(@Param("pointsAccountId") Long pointsAccountId,
                                                   @Param("netUserId") Long netUserId);

    @Select({
            "select a.net_user_id, sum(a.change_points) change_points, b.mobile_num,d.nick_name,d.avatar ",
            "from po_account_change a, net_user b left join wechat_mini_app_member d on b.net_user_id = d.net_user_id and d.state = '1', po_account c",
            "where a.change_type = '1' ",
            "and a.create_time between curdate() and date_add(curdate(), interval 1 day) ",
            "and a.net_user_id = b.net_user_id ",
            "and a.points_account_id = c.points_account_id ",
            "and c.points_type = '1'",
            "group by net_user_id ",
            "order by sum(a.change_points) desc ",
            "limit 8"
    })
    @Results({
            @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
            @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
            @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
            @Result(column="nick_name", property="nickName", jdbcType=JdbcType.VARCHAR),
            @Result(column="avatar", property="avatar", jdbcType=JdbcType.VARCHAR),
    })
    List<DataMap> selectLeaderBoard();

    @Select({
            "select count(*) ranking",
            "from ",
            "(select sum(b.change_points) change_points ",
            "from po_account_change b, po_account c ",
            "where b.change_type = '1' ",
            "and b.create_time between curdate() and date_add(curdate(), interval 1 day) ",
            "and b.points_account_id = c.points_account_id ",
            "and c.points_type = '1'",
            "group by b.net_user_id ",
            "order by sum(b.change_points) desc) a ",
            "where a.change_points >= (select sum(d.change_points) ",
            "from po_account_change d, po_account c ",
            "where d.net_user_id = #{netUserId,jdbcType=BIGINT} ",
            "and d.points_account_id = c.points_account_id ",
            "and c.points_type = '1'",
            "and d.create_time between curdate() and date_add(curdate(), interval 1 day) ",
            "and d.change_type = '1') ",
    })
    @Results({
            @Result(column="ranking", property="ranking", jdbcType=JdbcType.INTEGER),
    })
    Integer selectRanking(Long netUserId);

    @Select({
        "<script>",
        "select",
        "a.trade_id, b.ecard_no, c.mobile_num, d.coupon_name, count(c.coupon_id) num, a.create_time, a.change_points",
        "from po_account_change a, trade b, coupon_entity c, coupon d",
        "<where>",
        "and a.trade_id = b.trade_id",
        "and b.trade_id = c.create_trade_id",
        "and c.coupon_id = d.coupon_id",
        "and b.center_id = #{centerId,jdbcType=BIGINT}",
        "and b.cancel_tag != '1'",
        "<if test=\"venueId != null\">and b.venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"startDate != null\">and a.create_time &gt;= #{startDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"endDate != null\">and a.create_time &lt;= #{endDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"input != null\">and (b.ecard_no = #{input,jdbcType=VARCHAR} or c.mobile_num = #{input,jdbcType=VARCHAR})</if>",
        "</where>",
        "group by a.trade_id",
        "order by a.create_time desc",
        "</script>"
    })
    @Results({
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="ecard_no", property="ecardNo", jdbcType=JdbcType.VARCHAR),
        @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
        @Result(column="coupon_name", property="couponName", jdbcType=JdbcType.VARCHAR),
        @Result(column="num", property="num", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER)
    })
    List<DataMap> selectExchangeCouponList(Map<String, Object> param, RowBounds rowBounds);

    @Select({
        "select",
        "points_change_id, points_account_id, trade_id, net_user_id, cust_id, change_type, ",
        "change_points, balance, get_log_id, center_id, channel_id, change_desc, remark, create_time, ",
        "create_staff_id, update_time, update_venue_id, update_staff_id",
        "from po_account_change",
        "where trade_id = #{tradeId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
        @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
        @Result(column="balance", property="balance", jdbcType=JdbcType.INTEGER),
        @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_venue_id", property="updateVenueId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    PoAccountChange selectByTradeId(Long tradeId);

    @Select({
        "<script>",
        "select",
        "points_change_id, points_account_id, trade_id, net_user_id, cust_id, change_type, change_points, get_log_id, center_id, channel_id, change_desc, remark, create_time, create_staff_id, update_time, update_venue_id, update_staff_id",
        "from po_account_change",
        "<where>",
        "and points_account_id = #{pointsAccountId,jdbcType=BIGINT}",
        "and create_time &gt;= #{startDate,jdbcType=TIMESTAMP}",
        "and create_time &lt;= #{endDate,jdbcType=TIMESTAMP}",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
        @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
        @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_venue_id", property="updateVenueId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PoAccountChange> selectMonthChangeList(@Param("pointsAccountId") Long pointsAccountId,
                                                @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    @Select({
        "<script>",
        "select",
        "points_change_id, points_account_id, trade_id, net_user_id, cust_id, change_type, change_points, get_log_id, center_id, channel_id, change_desc, remark, create_time, create_staff_id",
        "from po_account_change",
        "<where>",
        "and points_account_id = #{pointsAccountId,jdbcType=BIGINT}",
        "<if test=\"startDate != null\">and create_time &gt;= #{startDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"endDate != null\">and create_time &lt;= #{endDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"changeType != null\">and change_type = #{changeType,jdbcType=VARCHAR}</if>",
        "</where>",
        "order by create_time desc",
        "</script>"
    })
    @Results({
        @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
        @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
        @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> selectAccountDetailList(Map<String, Object> param, RowBounds rowBounds);

    @Insert({
        "<script>",
        "insert into po_account_change (points_change_id, points_account_id, ",
        "trade_id, net_user_id, ",
        "cust_id, change_type, ",
        "change_points, balance, get_log_id, ",
        "center_id, channel_id, ",
        "change_desc, remark, ",
        "create_time, create_staff_id, ",
        "update_time, update_venue_id, ",
        "update_staff_id)",
        "values",
        "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
        "(#{item.pointsChangeId,jdbcType=BIGINT}, #{item.pointsAccountId,jdbcType=BIGINT}, ",
        "#{item.tradeId,jdbcType=BIGINT}, #{item.netUserId,jdbcType=BIGINT}, ",
        "#{item.custId,jdbcType=BIGINT}, #{item.changeType,jdbcType=VARCHAR}, ",
        "#{item.changePoints,jdbcType=INTEGER}, #{item.balance,jdbcType=INTEGER}, #{item.getLogId,jdbcType=BIGINT}, ",
        "#{item.centerId,jdbcType=BIGINT}, #{item.channelId,jdbcType=BIGINT}, ",
        "#{item.changeDesc,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, ",
        "#{item.createTime,jdbcType=TIMESTAMP}, #{item.createStaffId,jdbcType=BIGINT}, ",
        "#{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateVenueId,jdbcType=BIGINT}, ",
        "#{item.updateStaffId,jdbcType=BIGINT})",
        "</foreach>",
        "</script>"
    })
    int batchInsert(List<PoAccountChange> poAccountChangeList);

    @Select({
        "<script>",
        "select",
        "a.points_change_id, a.cust_id, a.change_points, a.balance, a.change_desc, a.create_time, b.contact_phone",
        "from po_account_change a, customer b",
        "<where>",
        "and a.cust_id = b.cust_id",
        "and a.update_venue_id = #{venueId,jdbcType=BIGINT}",
        "and curdate() = date_format(a.create_time, '%Y-%m-%d')",
        "and not exists(select 1 from cust_reminder cr where cr.related_trade_id = a.points_change_id and cr.reminder_type = " + Constants.ReminderType.POINTS_CHANGE + ")",
        "and a.create_time &lt;= #{startDate,jdbcType=TIMESTAMP}",
        "</where>",
        "order by a.create_time",
        "</script>"
    })
    @Results({
        @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT),
        @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
        @Result(column="balance", property="balance", jdbcType=JdbcType.INTEGER),
        @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="contact_phone", property="contactPhone", jdbcType=JdbcType.VARCHAR)
    })
    List<DataMap> selectRemindList(ParamMap paramMap);


    @Select({
            "<script>",
            "select",
            "a.points_change_id, a.points_account_id, a.trade_id, a.net_user_id, a.cust_id, a.change_type, ",
            "a.change_points, a.get_log_id, a.center_id, a.channel_id, a.change_desc",
            "from po_account_change a, po_account b ",
            "where b.state = '1' and a.points_account_id = b.points_account_id and b.cust_id = a.cust_id",
            "and b.cust_id = #{custId,jdbcType=BIGINT} ",
            "and date_format(a.create_time, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')",
            "<if test=\"pointsType != null\"> and b.points_type = #{pointsType,jdbcType=VARCHAR} </if>",
            "</script>"
    })
    @Results({
            @Result(column="points_change_id", property="pointsChangeId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="points_account_id", property="pointsAccountId", jdbcType=JdbcType.BIGINT),
            @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
            @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
            @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
            @Result(column="change_type", property="changeType", jdbcType=JdbcType.VARCHAR),
            @Result(column="change_points", property="changePoints", jdbcType=JdbcType.INTEGER),
            @Result(column="get_log_id", property="getLogId", jdbcType=JdbcType.BIGINT),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
            @Result(column="change_desc", property="changeDesc", jdbcType=JdbcType.VARCHAR)
    })
    List<PoAccountChange> getCurrentMonthPoint(@Param("custId") Long custId, @Param("pointsType") String pointsType);
}
