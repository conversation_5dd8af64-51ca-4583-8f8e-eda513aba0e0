package com.asiainfo.aisports.api.controller.pos;

import com.asiainfo.aisports.api.web.WebResult;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.*;
import com.asiainfo.aisports.param.StaticParamConfig;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.service.*;
import com.asiainfo.aisports.service.api.CommonService;
import com.asiainfo.aisports.service.contract.ContractService;
import com.asiainfo.aisports.service.covisitor.CompanyMemberFollowService;
import com.asiainfo.aisports.service.gxt.GxtMemberService;
import com.asiainfo.aisports.tools.StringUtils;
import com.asiainfo.aisports.tools.TradeConstants;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhuyu on 2018/5/28.
 * 手持终端专项卡售卖接口
 */
@RestController
@RequestMapping("/api/pos/applyCard")
public class PosCardController {
    private Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd").create();

    private static final String TRADE_TYPE = TradeConstants.TradeTypeCode.OPEN_CARD.toString();


    @Autowired
    VisitorService visitorService;
    @Autowired
    CustQueryService custQueryService;
    @Autowired
    ProductService productService;
    @Autowired
    StaffService staffService;
    @Autowired
    ConsultantService consultantService;
    @Autowired
    PromotionService promotionService;
    @Autowired
    ECardService eCardService;
    @Autowired
    SpecialCardService specialCardService;
    @Autowired
    CommonService commonService;
    @Autowired
    VenueStaticParamService venueStaticParamService;
    @Autowired
    private AgreementTypeService agreementTypeService;
    @Autowired
    private ContractService contractService;
    @Autowired
    private GxtMemberService gxtMemberService;
    @Autowired
    private TradeService tradeService;
    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private CoachScheduleService coachScheduleService;
    @Autowired
    private StaticParamConfig staticParamConfig;
    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private MemberService memberService;

    @Autowired
    private CompanyMemberFollowService companyMemberFollowService;

    /**
     * 查询cust和访客
     *
     * @param phoneNum
     * @param centerId
     * @return
     */
    @RequestMapping("/queryCustInfoByPhone")
    public WebResult queryCustInfoByPhone(@RequestParam("phoneNum") String phoneNum,
                                          @RequestParam("centerId") Long centerId) {
        //查询潜在客户
        Visitor visitor = new Visitor();
        visitor.setPhoneNumber(phoneNum);
        visitor.setCenterId(centerId);

        CustInfo custInfo = new CustInfo();
        custInfo.setCenterId(centerId);
        custInfo.setContactPhone(phoneNum);
        custInfo.setCustState(Constants.CustomerState.NORMAL);
        return new WebResult()
                .set("visitorList", visitorService.selectByFields(visitor).stream().filter(a -> !"3".equals(a.getSaleStatus())).toArray())
                .set("custList", custQueryService.queryCustInfoByField(custInfo));
    }


    /**
     * 查询cust和访客
     *
     * @param psptId
     * @param centerId
     * @return
     */
    @RequestMapping("/queryCustInfoByPsptId")
    public WebResult queryCustInfoByPsptId(@RequestParam("psptId") String psptId,
                                           @RequestParam("centerId") Long centerId) {
        //查询潜在客户
        Visitor visitor = new Visitor();
        visitor.setPsptId(psptId);
        visitor.setCenterId(centerId);

        CustInfo custInfo = new CustInfo();
        custInfo.setCenterId(centerId);
        custInfo.setPsptId(psptId);
        custInfo.setCustState(Constants.CustomerState.NORMAL);
        return new WebResult()
                .set("visitorList", visitorService.selectByFields(visitor).stream().filter(a -> !"3".equals(a.getSaleStatus())).toArray())
                .set("custList", custQueryService.findByPsptIdAndName(custInfo));
    }

    /**
     * 查询cust和访客
     *
     * @param name
     * @param centerId
     * @return
     */
    @RequestMapping("/queryCustInfoByCustName")
    public WebResult queryCustInfoByCustName(@RequestParam("name") String name,
                                             @RequestParam("centerId") Long centerId) {
        //查询潜在客户
        Visitor visitor = new Visitor();
        visitor.setVisitorName(name);
        visitor.setCenterId(centerId);

        CustInfo custInfo = new CustInfo();
        custInfo.setCenterId(centerId);
        custInfo.setCustName(name);
        custInfo.setCustState(Constants.CustomerState.NORMAL);
        return new WebResult()
                .set("visitorList", visitorService.selectByFields(visitor).stream().filter(a -> !"3".equals(a.getSaleStatus())).toArray())
                .set("custList", custQueryService.findByPsptIdAndName(custInfo));
    }

    /**
     * 查询产促销活动和办卡送礼
     *
     * @param productId
     * @return
     */
    @RequestMapping("/getValidPromotionsByMainProduct")
    public WebResult getValidPromotionsByMainProduct(@RequestParam(value = "productId") Long productId,
                                                     @RequestParam(value = "centerId") Long centerId) {
        WebResult webResult = new WebResult();
        List<PromInfo> promotionList = promotionService.getValidGiftsByProduct(productId, null, null);
        List<PromInfo> limitedDiscountPriceList = promotionService.getLimitedTimeDiscountByProduct(productId, null, null);
        Product product = productService.selectByPrimaryKey(productId);
        if (product != null) {
            webResult.set("limitTag", product.getLimitTag());
        }
        return webResult.set("specialCardPromotionList", promotionList).set("limitedDiscountPriceList", limitedDiscountPriceList);
    }


    /**
     * 报名提交
     *
     * @param staff
     * @param custInfoJson    客户信息 {"ecardNo":"卡号","psptTypeId":"0-身份证 1-军官证","psptId":"证件号","custName":"姓名","psptAddress":"证件地址",
     *                        "contactPhone":"联系电话","telephone":"固定电话","enterpriseName":"单位名称","birthday":"生日","psptEndDate":"证件有效期","gender":"性别0-男 1-女","picContent":"图片的base64文件"}
     * @param ecardNo         一卡通卡号
     * @param newCardTag      是否是新用户办理 0-否 1-是
     * @param custAttr        客户属性表
     * @param productId       产品id
     * @param shouldPay       应付金额
     * @param saleNum         办卡数量 默认1张
     * @param proms           优惠信息 proms: [{"promId":"10000213","promType":"1","gifts":[{"productId":"10000726"}],
     *                        "coupons":[{"couponId":"2018031500000388","couponNo":"1"}],"buyGifts":{}},{"promId":"10000215","promType":"2"}]
     * @param customDate      产品的开始时间
     * @param developerId
     * @param sourceId
     * @param serviceId
     * @param remark
     * @param recommendCustId
     * @param agreementBase64
     * @param agreementId
     * @param contractNo
     * @param agreementInfo   协议信息 agreementInfo: {"info":[{"name":"卡号","val":"800000000502"},{"name":"业务类型","val":"个人办卡"},{"name":"卡类","val":"游泳 - 游泳全年卡"},{"name":"有效期","val":"1年"},{"name":"用户","val":"王毅敏"},{"name":"证件号","val":"361001201503214482"},{"name":"手机号码","val":"15150500804"},{"name":"卡内余额","val":"18640.13"},{"name":"限时优惠","val":"限时-1","oneLine":true},{"name":"活动赠送","val":"关联-1(赠送13次卡 , 测试专用体验券x1)","oneLine":true},{"name":"实际价格","val":"2380"},{"name":"产品规则","val":""},{"name":"备注","val":"这里是备注","oneLine":true}],"agreementId":"0"}
     * @return
     */
    @RequestMapping("/submit")
    @ResponseBody
    public WebResult submit(LoginStaff staff,
                            @RequestParam("custInfo") String custInfoJson,
                            @RequestParam("ecardNo") String ecardNo,
                            @RequestParam(value = "newCardTag", defaultValue = "0") String newCardTag,
                            @RequestParam(value = "productId") Long productId,
                            @RequestParam(value = "shouldPay") Long shouldPay,
                            @RequestParam(value = "saleNum", defaultValue = "1") Long saleNum,
                            @RequestParam(value = "custAttr", required = false) String custAttr,
                            @RequestParam(value = "proms", required = false) String proms,
                            @RequestParam(value = "customDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date customDate,
                            @RequestParam(value = "developerId", required = false) String developerId,
                            @RequestParam(value = "consultantId", required = false) Long consultantId,
                            @RequestParam(value = "sourceId", required = false) String sourceId,
                            Long serviceId, String remark, Long recommendCustId,
                            String agreementBase64, Long agreementId, String contractNo, String agreementInfo) {
        staff.setChannelId(Constants.Channel.HANDHELD);
        CustomerPlus customer = gson.fromJson(custInfoJson, CustomerPlus.class);
        CustInfo custInfo;
        String tradeDesc = "老用户办专项卡";
        String bussnessType = Constants.BusinessType.INDIVIDUAL_OLD_CARD;
        //新办卡用户校验卡号是否可用
        if ("1".equals(newCardTag)) {
            if (!eCardService.validateECard(ecardNo, staff.getCenterId(), staff.getVenueId(), staff.getStaffId())) {
                return new WebResult(1, "卡资源已被占用");
            }
            //保存三户资料
            customer.setCustAttrMap(StringUtils.jsonToMap(custAttr));
            specialCardService.getBaseInfo(customer, staff);
            customer.setVenueId(staff.getVenueId());
            tradeDesc = "新用户办专项卡";
            bussnessType = Constants.BusinessType.INDIVIDUAL_CARD;
            custInfo = customer;
        } else { // 老客户根据卡号查询信息
            custInfo = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        }

        //创建办卡的trade
        TradeMore trade = specialCardService.createCardTradeInfo(bussnessType, custInfo, shouldPay, remark,
                tradeDesc, agreementBase64, agreementId, contractNo);
        trade.setServiceId(serviceId);
        if (!Strings.isNullOrEmpty(developerId)) {
            trade.setDeveloperId(Long.valueOf(developerId));
        }
        trade.setVenueId(staff.getVenueId());

        Map<String, Object> tradeExtraMap = Maps.newHashMap();
        tradeExtraMap.put(Constants.Extra.SALE_NUM, saleNum);
        tradeExtraMap.put(Constants.Extra.AGREEMENT_INFO, agreementInfo);
        tradeExtraMap.put(Constants.Extra.NEW_CARD_TAG, newCardTag);
        if ("1".equals(newCardTag)) {
            tradeExtraMap.put(Constants.Extra.CONSULTANT_ID, custInfo.getConsultantId());
            tradeExtraMap.put(Constants.Extra.COACH_ID, custInfo.getCoachId());
        } else { // 老客户，更新照片
            JSONObject updateCust = new JSONObject();
            updateCust.put("picUrl", customer.getPicUrl());
            updateCust.put("custAttr", custAttr);
            tradeExtraMap.put(Constants.Extra.UPDATE_CUST, updateCust.toString());
            tradeExtraMap.put(Constants.Extra.CONSULTANT_ID, consultantId);

        }

        if (recommendCustId != null) {
            tradeExtraMap.put(Constants.Extra.RECOMMEND_CUST_ID, recommendCustId);
        }
        if (!Strings.isNullOrEmpty(sourceId)) {
            tradeExtraMap.put(Constants.Extra.SOURCE_ID, sourceId);
        }

        specialCardService.saveSpecialTrade(trade, "1".equals(newCardTag) ? customer : null, tradeExtraMap, staff, customDate, productId, proms);

        return new WebResult()
                .set("tradeId", trade.getTradeId())
                .set("ecardNo", ecardNo);
    }

    @RequestMapping("/getProducts")
    public WebResult getProducts(LoginStaff staff, Long siteId) {
        JSONArray productJsonArray = productService.getProductByVenueIdAndCompanyTag(staff.getVenueId(), Constants.CompanyTag.INDIVIDUAL, siteId);
        List<Map<String, Object>> productList = Lists.newArrayList();
        for (int i = 0; i < productJsonArray.size(); i++) {
            JSONObject product = productJsonArray.getJSONObject(i);
            JSONArray secondProducts = product.getJSONArray("secondProduct");
            String serviceId = product.getString("serviceId");
            for (int j = 0; j < secondProducts.size(); j++) {
                JSONObject thirdProduct = secondProducts.getJSONObject(j);
                JSONArray thirdProducts = thirdProduct.getJSONArray("productList");
                for (int k = 0; k < thirdProducts.size(); k++) {
                    JSONObject json = thirdProducts.getJSONObject(k);
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("productId", json.get("productId"));
                    map.put("productName", json.getString("productName"));
                    map.put("price", json.getString("price"));
                    map.put("serviceId", serviceId);
                    map.put("limitTag", json.getString("limitTag"));

                    String offsetUnit = json.getString("offsetUnit");
                    String offsetValue = json.containsKey("offsetValue") ? json.getString("offsetValue") : "";
                    String offsetStartDate = json.containsKey("offsetStartDate") ? json.getString("offsetStartDate") : "";
                    String offsetEndDate = json.containsKey("offsetEndDate") ? json.getString("offsetEndDate") : "";
                    String validPeriod = "";
                    // 计算有效期
                    switch (offsetUnit) {
                        case Constants.OffsetUnit.DAY:
                            validPeriod = offsetValue + "天";
                            break;
                        case Constants.OffsetUnit.MONTH:
                            validPeriod = offsetValue + "个月";
                            break;
                        case Constants.OffsetUnit.QUARTER:
                            validPeriod = Long.parseLong(offsetValue) * 3 + "个月";
                            break;
                        case Constants.OffsetUnit.YEAR:
                            validPeriod = offsetValue + "年";
                            break;
                        case Constants.OffsetUnit.FIXED_DATE:
                            validPeriod = offsetStartDate.substring(0, 2) + "-" + offsetStartDate.substring(2)
                                    + " 至 " + offsetEndDate.substring(0, 2) + "-" + offsetEndDate.substring(2);
                            break;
                        case Constants.OffsetUnit.FIXED_END_DATE:
                            if (offsetEndDate.length() == 8) {
                                validPeriod = "截止至 " + offsetEndDate.substring(0, 4) + "-" + offsetEndDate.substring(4, 6) + "-" + offsetEndDate.substring(6);
                            } else {
                                validPeriod = "有效期配置错误";
                            }
                            break;
                        default:
                            validPeriod = "未知";
                    }
                    map.put("validPeriod", validPeriod);
                    productList.add(map);
                }
            }
        }

        return new WebResult().set("productList", productList);
    }

    @RequestMapping("/getProductsNew")
    public WebResult getProductsNew(LoginStaff staff, Long siteId) {
        JSONArray productJsonArray = productService.getProductByVenueIdAndCompanyTag(staff.getVenueId(), Constants.CompanyTag.INDIVIDUAL, siteId);
        for (int i = 0; i < productJsonArray.size(); i++) {
            JSONObject product = productJsonArray.getJSONObject(i);
            JSONArray secondProducts = product.getJSONArray("secondProduct");
            String serviceId = product.getString("serviceId");
            for (int j = 0; j < secondProducts.size(); j++) {
                JSONObject thirdProduct = secondProducts.getJSONObject(j);
                JSONArray thirdProducts = thirdProduct.getJSONArray("productList");
                for (int k = 0; k < thirdProducts.size(); k++) {
                    JSONObject json = thirdProducts.getJSONObject(k);
                    json.put("serviceId", serviceId);

                    String offsetUnit = json.getString("offsetUnit");
                    String offsetValue = json.containsKey("offsetValue") ? json.getString("offsetValue") : "";
                    String offsetStartDate = json.containsKey("offsetStartDate") ? json.getString("offsetStartDate") : "";
                    String offsetEndDate = json.containsKey("offsetEndDate") ? json.getString("offsetEndDate") : "";
                    String validPeriod = "";
                    // 计算有效期
                    switch (offsetUnit) {
                        case Constants.OffsetUnit.DAY:
                            validPeriod = offsetValue + "天";
                            break;
                        case Constants.OffsetUnit.MONTH:
                            validPeriod = offsetValue + "个月";
                            break;
                        case Constants.OffsetUnit.QUARTER:
                            validPeriod = Long.parseLong(offsetValue) * 3 + "个月";
                            break;
                        case Constants.OffsetUnit.YEAR:
                            validPeriod = offsetValue + "年";
                            break;
                        case Constants.OffsetUnit.FIXED_DATE:
                            validPeriod = offsetStartDate.substring(0, 2) + "-" + offsetStartDate.substring(2)
                                    + " 至 " + offsetEndDate.substring(0, 2) + "-" + offsetEndDate.substring(2);
                            break;
                        case Constants.OffsetUnit.FIXED_END_DATE:
                            if (offsetEndDate.length() == 8) {
                                validPeriod = "截止至 " + offsetEndDate.substring(0, 4) + "-" + offsetEndDate.substring(4, 6) + "-" + offsetEndDate.substring(6);
                            } else {
                                validPeriod = "有效期配置错误";
                            }
                            break;
                        default:
                            validPeriod = "未知";
                    }
                    json.put("validPeriod", validPeriod);
                }
            }
        }

        return new WebResult().set("productJsonArray", productJsonArray);
    }

    /**
     * 查询参数列表，包括发展人、会籍顾问、客户来源
     *
     * @param staff
     * @return
     */
    @RequestMapping("/getParams")
    public WebResult getParams(LoginStaff staff) {
        Long venueId = staff.getVenueId();
        List<Map<String, Object>> developerList = staffService.findValidDevelopers(staff.getCenterId(), venueId).stream()
                .map(p -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("developerId", p.getStaffId());
                    map.put("developerName", p.getStaffName());
                    return map;
                }).collect(Collectors.toList());
        List<Consultant> consultantList = consultantService.queryCurrentVenueConsultantList(venueId);
        List<Map<String, String>> sourceList = commonService.getSourceList(staff.getCenterId())
                .stream().map(p -> {
                    Map<String, String> map = Maps.newHashMap();
                    map.put("sourceId", p.get("paramKey"));
                    map.put("sourceName", p.get("paramValue"));
                    return map;
                }).collect(Collectors.toList());

        return new WebResult()
                .set("developerList", developerList)
                .set("consultantList", consultantList)
                .set("sourceList", sourceList);
    }

    /**
     * 根据证件号码查询该中心的客户
     *
     * @param centerId
     * @param psptType
     * @param psptId
     * @return
     */
    @RequestMapping("/getCustByPsptId")
    public WebResult getCustByPsptId(@RequestParam Long centerId, @RequestParam String psptType,
                                     @RequestParam String psptId) {

        List<Map<String, Object>> custInfo = custQueryService.getCustInfosByPsptId(psptId, psptType, centerId);
        return new WebResult().set("data", custInfo);
    }

    /**
     * 根据证件号或者卡号校验产品
     *
     * @param psptType
     * @param psptId
     * @param centerId
     * @param productId
     * @return
     */
    @RequestMapping("/checkProduct")
    public WebResult checkProduct(String psptType, String psptId, Long centerId, Long productId) {
        ServiceResult serviceResult = specialCardService.checkProduct(psptType, psptId, centerId, productId);

        WebResult webResult = new WebResult();
        webResult.putAll(serviceResult);
        return webResult;
    }

    /**
     * 查询证件类型
     *
     * @param loginStaff
     * @return
     */
    @RequestMapping("/getPsptTypes")
    public WebResult getPsptTypes(LoginStaff loginStaff) {
        Long venueId = loginStaff.getVenueId();
        if (venueId == null) {
            venueId = 0L;
        }
        // 按场馆查询
        List<Map<String, String>> psptTypes = venueStaticParamService.getParamKeyAndValueList(loginStaff.getCenterId(),
                venueId, Constants.VenueStaticParam.POS_APPLY_CARD_PSPT_TYPE);
        if (psptTypes.isEmpty()) {
            if (venueId != 0) {
                // 按中心查询
                psptTypes = venueStaticParamService.getParamKeyAndValueList(loginStaff.getCenterId(),
                        0L, Constants.VenueStaticParam.POS_APPLY_CARD_PSPT_TYPE);
            }
            if (psptTypes.isEmpty()) {
                // 查询默认配置
                psptTypes = venueStaticParamService.getParamKeyAndValueList(0L,
                        0L, Constants.VenueStaticParam.POS_APPLY_CARD_PSPT_TYPE);
            }
        }
        return new WebResult().set("data", psptTypes.stream().map(p ->
                ImmutableMap.of("id", p.get("paramKey"), "name", p.get("paramValue")))
                .collect(Collectors.toList()));
    }

    /**
     * 报名提交（适配“大家签”合同上传）
     *
     * @param staff
     * @param custInfoJson    客户信息 {"ecardNo":"卡号","psptTypeId":"0-身份证 1-军官证","psptId":"证件号","custName":"姓名","psptAddress":"证件地址",
     *                        "contactPhone":"联系电话","telephone":"固定电话","enterpriseName":"单位名称","birthday":"生日","psptEndDate":"证件有效期","gender":"性别0-男 1-女","picContent":"图片的base64文件"}
     * @param ecardNo         一卡通卡号
     * @param newCardTag      是否是新用户办理 0-否 1-是
     * @param custAttr        客户属性表
     * @param productId       产品id
     * @param shouldPay       应付金额
     * @param saleNum         办卡数量 默认1张
     * @param proms           优惠信息 proms: [{"promId":"10000213","promType":"1","gifts":[{"productId":"10000726"}],
     *                        "coupons":[{"couponId":"2018031500000388","couponNo":"1"}],"buyGifts":{}},{"promId":"10000215","promType":"2"}]
     * @param customDate      产品的开始时间
     * @param developerId
     * @param sourceId
     * @param serviceId
     * @param remark
     * @param recommendCustId
     * @param agreementBase64
     * @param agreementId
     * @param contractNo
     * @param agreementInfo   协议信息 agreementInfo: {"info":[{"name":"卡号","val":"800000000502"},{"name":"业务类型","val":"个人办卡"},{"name":"卡类","val":"游泳 - 游泳全年卡"},{"name":"有效期","val":"1年"},{"name":"用户","val":"王毅敏"},{"name":"证件号","val":"361001201503214482"},{"name":"手机号码","val":"15150500804"},{"name":"卡内余额","val":"18640.13"},{"name":"限时优惠","val":"限时-1","oneLine":true},{"name":"活动赠送","val":"关联-1(赠送13次卡 , 测试专用体验券x1)","oneLine":true},{"name":"实际价格","val":"2380"},{"name":"产品规则","val":""},{"name":"备注","val":"这里是备注","oneLine":true}],"agreementId":"0"}
     * @param htmlContract    html格式的合同内容
     * @param userLocator     新增的用户签字定位符
     * @param companyLocator  新增的公司签章定位符
     * @return
     */
    @RequestMapping("/submit/new")
    @ResponseBody
    public WebResult submitExtend(LoginStaff staff,
                                  @RequestParam("custInfo") String custInfoJson,
                                  @RequestParam("ecardNo") String ecardNo,
                                  @RequestParam(value = "newCardTag", defaultValue = "0") String newCardTag,
                                  @RequestParam(value = "productId") Long productId,
                                  @RequestParam(value = "shouldPay") Long shouldPay,
                                  @RequestParam(value = "saleNum", defaultValue = "1") Long saleNum,
                                  @RequestParam(value = "custAttr", required = false) String custAttr,
                                  @RequestParam(value = "proms", required = false) String proms,
                                  @RequestParam(value = "customDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date customDate,
                                  @RequestParam(value = "developerId", required = false) String developerId,
                                  @RequestParam(value = "consultantId", required = false) Long consultantId,
                                  @RequestParam(value = "sourceId", required = false) String sourceId,
                                  Long serviceId,
                                  String remark,
                                  Long recommendCustId,
                                  String agreementBase64,
                                  Long agreementId,
                                  String contractNo,
                                  String agreementInfo,
                                  String htmlContract,
                                  String userLocator,
                                  String companyLocator) {
        AgreementType agreementType = agreementTypeService.findById(agreementId);
        if (agreementType == null) {
            return new WebResult(1, "协议内容不存在");
        }
        WebResult webResult = submit(staff, custInfoJson, ecardNo, newCardTag, productId, shouldPay, saleNum, custAttr, proms, customDate, developerId, consultantId, sourceId, serviceId, remark, recommendCustId, agreementBase64, agreementId, contractNo, agreementInfo);
        if (webResult.getError() != 0) {
            return webResult;
        }
        ServiceResult serviceResult = contractService.saveContract(htmlContract, agreementBase64, agreementType.getCenterId(), userLocator, companyLocator, (Long) webResult.get("tradeId"), agreementType.getSealPicPath(), staff.getVenueId());
        if (serviceResult.getError() != 0) {
            throw new ServiceException(serviceResult.getMessage());
        }
        // 国信通对接，会员验证
        gxtMemberService.memberCheck(staff.getCenterId(), MapUtils.getLong(webResult, "tradeId"));
        return webResult;
    }



    @RequestMapping("/apply")
    @ResponseBody
    public WebResult getProductByVenueId(LoginStaff staff) {
        Long venueId = staff.getVenueId();
        WebResult result = new WebResult();
        // 发展人、会籍顾问、来源
        result.putAll(getParams(staff));

        //中心拥有电子卡的权限标志：0否 1是
        String venueElectronCardTag = venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.VENUE_ELECTRON_CARD);
        result.put("venueElectronCardTag", venueElectronCardTag);

        //获取该场馆是否需要选择教练
        boolean isSelectCoach = venueParamConfig.getBoolean(venueId, Constants.VenueParam.APPLY_CARD_SELECT_COACH);
        if (isSelectCoach) {
            result.put("coachList", coachScheduleService.getCoachListForVenue(venueId));
        }

        //获取紧急联系人关系列表
        Map<String, String> emergencyMap = staticParamConfig.getParamMap(Constants.StaticParam.EMERGENCY_CONTACT_RELATION);
        if (!emergencyMap.isEmpty()) {
            result.put("emergencyMap", emergencyMap);
        }

        //获取证件类型
        result.put("psptTypeMap", staticParamConfig.getParamMap(Constants.StaticParam.PSPT_TYPE));

        String flag = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.GUO_XIN_CA_FLAG);
        //如果查不到,查中心的
        if (Strings.isNullOrEmpty(flag)) {
            flag = venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.GUO_XIN_CA_FLAG);
        }
        if (Constants.Tag.YES.equals(flag)) {
            //国信增加自定义附件
            result.put("addGxcaExtraAttachment", venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.GUO_XIN_CA_ADD_EXTRA_ATTACHMENT_NUM));
        } else {
            //增加老年证等附件
            result.put("addExtraAttachment", venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.ADD_EXTRA_ATTACHMENT));
        }

        //运动指导是否存在
        result.put("exerciseGuidanceTag", venueParamConfig.getBoolean(venueId, Constants.VenueParam.EXERCISE_GUIDE_TAG)); // 运动指导

        //获取企业列表
        result.put("enterpriseList", enterpriseService.selectEnterpriseListByCenterId(staff.getCenterId()));


        //合同编号是否需要填写
        result.put("personalCardNeedContractTag", venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.PERSONAL_CARD_NEED_CONTRACT_TAG));

        return result;
    }


    /**
     * 新用户办一卡通
     *
     * @return
     */
    @RequestMapping("/eCardCreateForNew")
    @ResponseBody
    public WebResult eCardCreateForNew(LoginStaff staff, CustomerPlus customer, Long promId, String custAttr,
                                       @RequestParam(value = "shouldPay") Long shouldPay,
                                       @RequestParam(value = "developerId", required = false) String developerId,
                                       @RequestParam(value = "sourceId", required = false) String sourceId,
                                       @RequestParam(value = "exerGroupId", required = false) Long exerGroupId,
                                       @RequestParam(value = "exerStaffId", required = false) Long exerStaffId,
                                       String agreementBase64, Long agreementId, Long recommendCustId,
                                       String remark, String agreementInfo) {
        staff.setChannelId(Constants.Channel.HANDHELD);
        Long venueId = staff.getVenueId();
        boolean ecardEffectTag = eCardService.validateECard(customer.getEcardNo(), staff.getCenterId(), venueId, staff.getStaffId());
        if (!ecardEffectTag) {
            return new WebResult(1, "卡资源已被占用");
        }

        //获取三户资料对象
        specialCardService.getBaseInfo(customer, staff);
        customer.setCustAttrMap(StringUtils.jsonToMap(custAttr));
        customer.setVenueId(venueId);

        //获取cardTrade对象
        String tradeDesc = "新用户办一卡通";
        TradeMore trade = specialCardService.createCardTradeInfo(Constants.BusinessType.INDIVIDUAL_ECARD, customer,
                shouldPay, remark, tradeDesc, agreementBase64, agreementId, customer.getContractNo());
        if (!Strings.isNullOrEmpty(developerId)) {
            trade.setDeveloperId(Long.valueOf(developerId));
        }
        trade.setVenueId(venueId);

        Map<String, Object> map = Maps.newHashMap();
        map.put(Constants.Extra.CONSULTANT_ID, customer.getConsultantId());
        map.put(Constants.Extra.COACH_ID, customer.getCoachId());
        map.put(Constants.Extra.AGREEMENT_INFO, agreementInfo);
        map.put(Constants.Extra.EXER_GROUP_ID, exerGroupId);
        map.put(Constants.Extra.EXER_STAFF_ID, exerStaffId);
        if (!Strings.isNullOrEmpty(sourceId)) {
            map.put(Constants.Extra.SOURCE_ID, sourceId);
        }
        if (recommendCustId != null) {
            map.put(Constants.Extra.RECOMMEND_CUST_ID, recommendCustId);
        }

        //数据插到三户表里面
        ServiceResult serviceResult = eCardService.saveEcardTrade(trade, customer, map, staff, promId);
        return new WebResult(0, "ok")
                .set("redirect", serviceResult.containsKey("makeCardTag") ? "/unpaidOrderQuery" : "/pay")
                .set("tradeId", trade.getTradeId())
                .set("ecardNo", customer.getEcardNo());
    }

    /**
     * 新用户办一卡通
     *
     * @return
     */
    @RequestMapping("/eCardCreateForNew/new")
    @ResponseBody
    public WebResult eCardCreateForNewExtend(LoginStaff staff, CustomerPlus customer, Long promId, String custAttr,
                                             @RequestParam(value = "shouldPay") Long shouldPay,
                                             @RequestParam(value = "developerId", required = false) String developerId,
                                             @RequestParam(value = "sourceId", required = false) String sourceId,
                                             @RequestParam(value = "exerGroupId", required = false) Long exerGroupId,
                                             @RequestParam(value = "exerStaffId", required = false) Long exerStaffId,
                                             String agreementBase64, Long agreementId, Long recommendCustId,
                                             String remark, String agreementInfo,
                                             String htmlContract,
                                             String userLocator,
                                             String companyLocator) {
        AgreementType agreementType = agreementTypeService.findById(agreementId);
        if (agreementType == null) {
            return new WebResult(1, "协议内容不存在");
        }
        WebResult webResult = eCardCreateForNew(staff, customer, promId, custAttr, shouldPay, developerId, sourceId,
                exerGroupId, exerStaffId, agreementBase64, agreementId, recommendCustId, remark, agreementInfo);
        if (webResult.getError() == 0) {
            ServiceResult serviceResult = contractService.saveContract(htmlContract, agreementBase64, agreementType.getCenterId(), userLocator, companyLocator, (Long) webResult.get("tradeId"), agreementType.getSealPicPath(), staff.getVenueId());
            if (serviceResult.getError() != 0) {
                throw new ServiceException(serviceResult.getMessage());
            }
        }
        return webResult;
    }


    /**
     * 获取一卡通充值预估金额
     * @param staff
     * @param promId
     * @param tradeMoney
     * @return
     */
    @RequestMapping("/getExpectedChargeMoneyForNew")
    @ResponseBody
    public WebResult getExpectedChargeMoneyForNew(LoginStaff staff, Long promId, Integer tradeMoney) {

        WebResult result = new WebResult();
        if (tradeMoney == null || tradeMoney < 0) {
            return new WebResult(1, "充值金额不合法");
        }

        ServiceResult serviceResult = eCardService.getExpectedChargeMoneyForNew(promId, tradeMoney);
        result.putAll(serviceResult);
        return result;
    }

    /**
     * 获取一卡通充值优惠
     *
     * @param centerId
     * @param staff
     * @return
     */
    @RequestMapping(value = "/getRechargePromotions", method = RequestMethod.GET)
    @ResponseBody
    public WebResult findRechargePromotions(Long centerId, LoginStaff staff) {
        if (centerId == null) {
            centerId = staff.getCenterId();
        }
        //获取一卡通充值优惠(已经根据centerId进行筛选了)
        List<DataMap> promotionList = promotionService.findRechargePromotions(centerId);
        return new WebResult().set("result", promotionList);
    }

    /**
     * 个人办卡 展示会员或潜在客户的更多详情信息
     *
     * @param ecardNo
     * @param phone
     * @param staff
     * @return
     */
    @RequestMapping("/getCustMoreInfo")
    @ResponseBody
    public WebResult getCustMoreInfo(@RequestParam(value = "ecardNo") String ecardNo,
                                     @RequestParam(value = "phone", required = false) String phone,
                                     LoginStaff staff) {
        Long venueId = staff.getVenueId();

        WebResult webResult = new WebResult();
        CustInfo custInfo = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        // 查询国信公司级会籍信息
        ServiceResult newMemberResult = companyMemberFollowService.companyMemberConsultant(custInfo == null ? null : custInfo.getCustId(), phone, staff);
        webResult.set("newMemberResult", newMemberResult);

        if (custInfo != null) {
            Member member = memberService.selectByCustId(custInfo.getCustId(), venueId);
            if (member != null) {
                //会员的信息
                webResult.putAll(memberService.findMemberMoreInfo(member, venueId));
                extractedConsultantInfo(webResult, newMemberResult);
                return webResult;
            }
        }
        //如果会员信息没有，则查询潜在客户的信息
        if (!Strings.isNullOrEmpty(phone)) {
            webResult.putAll(visitorService.findVisitorMoreInfo(phone, venueId, staff.getCenterId()));
        }
        extractedConsultantInfo(webResult, newMemberResult);
        return webResult;
    }

    private void extractedConsultantInfo(WebResult webResult, ServiceResult newMemberResult) {
        // 处理下省的前端处理了
        if (newMemberResult.containsKey("newMemberConsultantId")) {
            webResult.put("memberConsultantId", newMemberResult.get("newMemberConsultantId"));
            webResult.put("memberConsultantName", newMemberResult.get("consultantName"));
        } else if (webResult.containsKey("member") && webResult.get("member") instanceof MemberInfo) {
            webResult.put("memberConsultantId", ((MemberInfo) webResult.get("member")).getConsultantId());
            webResult.put("memberConsultantName", ((MemberInfo) webResult.get("member")).getConsultantName());
        } else if (webResult.containsKey("visitor") && webResult.get("visitor") instanceof VisitorPlus) {
            webResult.put("memberConsultantId", ((VisitorPlus) webResult.get("visitor")).getConsultantId());
            webResult.put("memberConsultantName", ((VisitorPlus) webResult.get("visitor")).getConsultantName());

        }
    }


}
