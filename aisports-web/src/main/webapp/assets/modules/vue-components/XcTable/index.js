export default {
    install(Vue) {
        Vue.component('xc-table', {
            render(create) {
                return create('table', {
                    class: {
                        'simple-splitter': true,
                    },
                }, this.$slots.default)
            },
        })
        Vue.component('xc-thead', {
            render(create) {
                return create('thead', this.$slots.default)
            },
        })
        Vue.component('xc-tbody', {
            render(create) {
                return create('tbody', this.$slots.default)
            },
        })
        Vue.component('xc-head', {
            render(create) {
                return create('thead', this.$slots.default)
            },
        })
        Vue.component('xc-body', {
            render(create) {
                return create('thead', this.$slots.default)
            },
        })
        Vue.component('xc-th', {
            render(create) {
                return create('th', this.$slots.default)
            },
        })
        Vue.component('xc-td', {
            render(create) {
                return create('td', this.$slots.default)
            },
        })
        Vue.component('xc-tr', {
            render(create) {
                return create('tr', this.$slots.default)
            },
        })
    },
}

