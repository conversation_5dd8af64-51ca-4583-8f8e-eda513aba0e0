@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../common/scss/bigTabs";
// bs 下拉选择框
@import "../../../sea_modules/bootstrap-select/bootstrap-select";

.rents-main {
  position: static !important;
  padding: 20px 20px 70px 20px;
  margin-bottom: 60px;
  .title-box {
    .sub-box {
      .left {
        > .f-line:first-child {
          margin-top: 0;
        }
      }
    }
    .sub-box-spe {
      padding-top: 0;
      padding-left: 0;
    }
  }
  .f-line {
    .search {
      margin-left: 20px;
    }
  }
}

.rents {
  @include rightFixCol();
  .left {
    min-height: 528px;
    padding-left: 0 !important;
    padding-top: 0 !important;
  }
  @include lrLayout();
}

.c-search {
  float: left;
  position: relative;
  .icon-sousuo {
    cursor: pointer;
    position: absolute;
    right: 8px;
    top: 6px;
    font-size: 22px;
    color: #c2c0c0;
    line-height: 1;
  }
}

.btn-q {
  margin-left: 10px;
}

.items {
  margin-right: 20px;
  .item {
    position: relative;
    cursor: pointer;
    .icon-gou{
      width: 26px;
      height: 26px;
      color: #fff;
      right: 20px;
      top: 52px;
      font-size: 16px;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #56b854;
      border-radius: 50%;
      opacity: 0;
    }
    &.on {
      border: 1px solid #9ad498;
      .remark-container {
        display: block;
      }
      .icon-gou{
        opacity: 1;
      }
    }
  }

  .item + .item {
    margin-top: 20px;
  }
}


.item-info-ct {
  position: relative;
  padding: 20px;
  @include clearfix();
  .remark-container {
    display: none;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e0e0e0;
    textarea {
      width: 100%;
      height: 34px;
      border: 1px solid #e0e0e0;
      padding: 10px;
      line-height: 14px;
    }
  }
  .name {
    line-height: 18px;
    font-size: 18px;
  }
  @include imgBox(90 , 90);
  .img-box {
    float: left;
  }
  .item-info-detail {
    margin-left: 110px;
    color: #333;
    position: relative;
    min-height: 88px;
    .count {
      color: #ef1616;
      margin-left: 5px;
    }
    .item-price {
      color: #e41919;
    }
    .rent-info {
      position: absolute;
      bottom: 0px;
      color: #333;
      line-height: 14px;
      .iconfont {
        color: #999;
        margin-right: 6px;
      }
      span + span {
        margin-left: 40px;
      }
    }
    &.return-style {
      .rent-info {
        position: static;
        margin: 20px 0;
      }
      .status-op {
        display: block;
      }
    }
    .item-title{
      font-size: 18px;
      line-height: 18px;
    }
    .item-num{
      margin-top: 10px;
      line-height: 14px;
    }
    .infos {
      @include flex;
      margin-top: 30px;
      .info {
        .mn {
          color: #ef1616;
        }
        .pay-mode{
          color: #b2b2b2;
        }
      }
      .info + .info {
        margin-left: 40px;
      }
    }
  }
}

.dsearch-box {
  margin-bottom: 20px;
  .dq {
    width: 530px;
    height: 35px;
    color: #7b7b7b;
    text-indent: 20px;
  }
  .btn {
    padding-left: 40px;
    padding-right: 40px;
    margin-left: 15px;
  }
}

.submit {
  @include bSubmit();
}

.tabs-content {
  padding-left: 20px;
  margin-top: 15px;
}

.rent-item-deals {
  .big-tab {
    //margin-top: -15px;
    //margin-left: -21px;
    > .tab {
      > li {
        background-color: #fafafa;
        cursor: pointer;
      }
      > li.cur {
        background-color: #fff;
      }
    }
  }
}

.item-placeholder {
  height: 152px;
  background: url(../images/item-select-bg.png) no-repeat center center;
}

.submit-lg {
  padding-left: 55px;
  padding-right: 55px;
}

.status-op {
  @include clearfix;
  margin-top: 8px;
  display: none;
  height: 40px;
  .compensation-type {
    width: 112px !important;
  }
  .status-op-item {
    // float: left;
    position: relative;
    line-height: 40px;
    margin-right: 20px;
    font-size: 0px;
    padding-left: 20px;
    display: inline-block;
    input, span {
      font-size: 14px;
    }
    .qselect {
      vertical-align: top;
    }
    .bootstrap-select > .btn {
      height: 40px;
      padding-left: 25px;
    }
    .input-with-unit {
      input {
        height: 40px;
        border-color: #eee;
      }
      .unit {
        height: 40px;
        line-height: 40px;
      }
    }
    .compensation-icon {
      position: absolute;
      left: 0px;
      @include circle(40px);
      color: #fff;
      font-size: 33px;
      background-color: #56b854;
      text-align: center;
      z-index: 101;
    }
    .com-with-num, .com-without-num {
      margin-left: -1px;
      vertical-align: top;
    }
    .com-with-num {
      input {
        width: 110px;
      }
    }
    .com-without-num {
      display: none;
    }
    .quantity-form {
      // margin-bottom: 150px;
      margin-left: -1px;
      display: inline-block;
      .decrement, .increment, .quantity-text {
        float: left;
        width: 40px;
        text-align: center;
        border: 1px solid #e0e0e0;
        height: 40px;
        .iconfont {
          color: #c8c8c8;
        }
      }
      .quantity-text {
        margin: 0 -1px;
      }
    }
  }
}

.deatail-submit {
  margin-top: 20px;
  .btn {
    padding-left: 27px;
    padding-right: 27px;
  }
  .btn:first-child {
    margin-right: 11px;
  }
  .btn:last-child {
    margin-left: 11px;
  }
}

.col-xs-width {
  width: 20%;
  display: inline-block;
}

.remark-line {
  td {
    padding: 13px 0;
  }
}

.bottom-half-table {
  width: 100%;
}

.search-container {
  padding: 20px 20px 5px 20px;
}
.item-info-ft{
  margin-top: 20px;
  border-top: 1px dashed #f1f2f7;
}
