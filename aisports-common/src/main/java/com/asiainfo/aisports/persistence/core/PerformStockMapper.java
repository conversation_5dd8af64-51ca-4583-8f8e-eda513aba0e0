package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.PerformStock;
import java.util.List;

import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

public interface PerformStockMapper {
    @Delete({
            "delete from perform_stock",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
            "insert into perform_stock (id, name, ",
            "perform_id, total_amount, ",
            "remain_amount, location, ",
            "version, state, ",
            "assign_state, create_time, ",
            "create_staff_id, update_time, ",
            "update_staff_id)",
            "values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, ",
            "#{performId,jdbcType=VARCHAR}, #{totalAmount,jdbcType=INTEGER}, ",
            "#{remainAmount,jdbcType=INTEGER}, #{location,jdbcType=VARCHAR}, ",
            "#{version,jdbcType=INTEGER}, #{state,jdbcType=VARCHAR}, ",
            "#{assignState,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
            "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
            "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(PerformStock record);

    @Select({
            "select",
            "id, name, perform_id, total_amount, remain_amount, location, version, state, ",
            "assign_state, create_time, create_staff_id, update_time, update_staff_id",
            "from perform_stock",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
            @Result(column="total_amount", property="totalAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="remain_amount", property="remainAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="location", property="location", jdbcType=JdbcType.VARCHAR),
            @Result(column="version", property="version", jdbcType=JdbcType.INTEGER),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="assign_state", property="assignState", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    PerformStock selectByPrimaryKey(Long id);

    @Update({
            "update perform_stock",
            "set name = #{name,jdbcType=VARCHAR},",
            "perform_id = #{performId,jdbcType=VARCHAR},",
            "total_amount = #{totalAmount,jdbcType=INTEGER},",
            "remain_amount = #{remainAmount,jdbcType=INTEGER},",
            "location = #{location,jdbcType=VARCHAR},",
            "version = #{version,jdbcType=INTEGER},",
            "state = #{state,jdbcType=VARCHAR},",
            "assign_state = #{assignState,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(PerformStock record);

    @Select({
            "<script>",
            "select",
            "id, name, perform_id, total_amount, remain_amount, location, version, state, assign_state, create_time, create_staff_id, update_time, update_staff_id",
            "from perform_stock",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"name != null\">and name = #{name,jdbcType=VARCHAR}</if>",
            "<if test=\"performId != null\">and perform_id = #{performId,jdbcType=VARCHAR}</if>",
            "<if test=\"totalAmount != null\">and total_amount = #{totalAmount,jdbcType=INTEGER}</if>",
            "<if test=\"remainAmount != null\">and remain_amount = #{remainAmount,jdbcType=INTEGER}</if>",
            "<if test=\"location != null\">and location = #{location,jdbcType=VARCHAR}</if>",
            "<if test=\"version != null\">and version = #{version,jdbcType=INTEGER}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"assignState != null\">and assign_state = #{assignState,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
            @Result(column="total_amount", property="totalAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="remain_amount", property="remainAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="location", property="location", jdbcType=JdbcType.VARCHAR),
            @Result(column="version", property="version", jdbcType=JdbcType.INTEGER),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="assign_state", property="assignState", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PerformStock> selectByFields(PerformStock param);

    @Update({
            "<script>",
            "update perform_stock",
            "<set>",
            "<if test=\"name != null\">name = #{name,jdbcType=VARCHAR},</if>",
            "<if test=\"performId != null\">perform_id = #{performId,jdbcType=VARCHAR},</if>",
            "<if test=\"totalAmount != null\">total_amount = #{totalAmount,jdbcType=INTEGER},</if>",
            "<if test=\"remainAmount != null\">remain_amount = #{remainAmount,jdbcType=INTEGER},</if>",
            "<if test=\"location != null\">location = #{location,jdbcType=VARCHAR},</if>",
            "<if test=\"version != null\">version = #{version,jdbcType=INTEGER},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"assignState != null\">assign_state = #{assignState,jdbcType=VARCHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where id = #{id,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(PerformStock record);


    @Update({
            "<script>",
            "update perform_stock",
            "<set>",
            "<if test=\"name != null\">name = #{name,jdbcType=VARCHAR},</if>",
            "<if test=\"performId != null\">perform_id = #{performId,jdbcType=VARCHAR},</if>",
            "<if test=\"totalAmount != null\">total_amount = #{totalAmount,jdbcType=INTEGER},</if>",
            "<if test=\"remainAmount != null\">remain_amount = #{remainAmount,jdbcType=INTEGER},</if>",
            "<if test=\"location != null\">location = #{location,jdbcType=VARCHAR},</if>",
            "version = version+1,",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where id = #{id,jdbcType=BIGINT}",
            "and version = #{version,jdbcType=INTEGER}",
            "</script>"
    })
    int updateByCheck(PerformStock performStock);


    @Update({
            "update perform_stock" ,
            "set remain_amount = remain_amount + #{changeNum}," ,
            "    update_time   = now()" ,
            "where id = #{stockId}"
    })
    int updateRemain(@Param("stockId") Long stockId, @Param("changeNum") Integer changeNum);

    @Select({
            "<script>",
            "select ifnull(sum(a.total_amount), 0) total_num from perform_stock a",
            "left join perform b on a.perform_id = b.id",
            "where a.state = 1",
            "<if test=\"projectId != null\">and b.project_id = #{projectId,jdbcType=BIGINT}</if>",
            "<if test=\"stockId != null\">and a.id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"performId != null and performId != ''\">and a.perform_id = #{performId,jdbcType=VARCHAR}</if>",
            "</script>"
    })
    @Results({
            @Result(column="total_num", property="totalNum", jdbcType=JdbcType.BIGINT)
    })
    long selectTotalNum(@Param("projectId") String projectId,
                        @Param("performId") String performId,
                        @Param("stockId") Long stockId);
}
