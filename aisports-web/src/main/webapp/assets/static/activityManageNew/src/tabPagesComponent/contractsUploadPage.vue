<template>
<div class="add-detail">
    <div class="sub-box">
        <dl class="f-line">
            <dt class="f-label">
                <span class="star" style="color: #fc8068; font-size: 18px;" >*</span>
                合同编号：
            </dt>
            <dd class="f-field contract-logs">
                <input type="text" :disabled="!editable" required rtext="请输入合同编号" class="normal" placeholder="请输入合同编码" v-model="contractCode">
                <el-button plain size="medium" @click="showLog">合同日志</el-button>
            </dd>
        </dl>
        <dl class="f-line" v-if="editable">
            <dt class="f-label">
                <span class="star" style="color: #fc8068; font-size: 18px;">*</span>
                合同上传：
            </dt>
            <dd class="f-field">
                    <button class="btn btn-md btn-primary query-btn" @click="selectFile">
                        <i class="iconfont icon-shangchuan1"></i>
                        点击上传
                        <input type="file" class="file-upload" ref="upload" multiple @change="handleUpload($event)">
                    </button>
                    <span style="color: #999999">支持图片、Word、Excel、PDF格式</span>
            </dd>
        </dl>
        <dl class="f-line" v-if="fileList.length">
            <dt class="f-label">
                
            </dt>
            <dd class="f-field">
                <!-- 编辑模式 -->
                <div class="uploader-result" v-if="editable">
                    <div class="uploader-item" v-for="item in fileList" :key="item.filePath">
                        <!-- word/excel/pdf -->
                        <div class="file-box" v-if="'xlsx,pdf,docx'.indexOf(item.fileType) > -1">
                            <img :src="'/assets/release/static/images/'+fileImg[item.fileType]" alt="">
                            <div class="mask" v-if="item.fileType !== 'pdf'" @click="viewFile(item)">
                                <i  class="iconfont icon-yanjing"></i>
                            </div>
                        </div>
                        <!-- 图片 -->
                        <div class="img-box" v-else>
                            <img :src="item.url" alt="">
                            <div class="mask" @click="previewImg(item)">
                                <i  class="iconfont icon-yanjing"></i>
                            </div>
                        </div>
                        <span>{{item.fileName}}</span>
                        <div class="delete-btn" @click="deleteFile(item)">
                            <i class="iconfont icon-cuowu2" style="color: #999999"></i>
                        </div>
                    </div>
                </div>
                <!-- 查看模式，不能编辑 -->
                <div class="file-list" v-else>
                    <div class="file-item" v-for="item in fileList" :key="item.filePath">
                        <!-- xlsx,pdf,docx -->
                        <div class="file-box" v-if="'xlsx,pdf,docx'.indexOf(item.fileType) > -1">
                            <img :src="'/assets/release/static/images/'+fileImg[item.fileType]" alt="">
                        </div>
                        <!-- 图片 -->
                        <div class="img-box" v-else>
                            <img :src="item.url" alt="">
                        </div>
                        <div class="right">
                            <div class="file-name">{{item.fileName}}</div>
                            <div class="operation-box">
                                <operation-text v-if="item.fileType != 'pdf'" @click="onPreview(item)">预览</operation-text>
                                <operation-text @click="download(item)">下载</operation-text>
                            </div>
                        </div>
                    </div>
                </div>
            </dd>
        </dl>

        <!-- 保存按钮 -->
        <dl class="f-line" v-if="editable">
            <dt class="f-label">
            </dt>
            <dd class="f-field">
                <el-button @click="submit" type="primary">提交审核</el-button>
                <!-- <el-button @click="backtopre">取消</el-button> -->
            </dd>
        </dl>
    </div>
    <div class="img-preview" v-if="preview">
        <i class="iconfont icon-cuowu2" @click="cancelPreview"></i>
        <div>
            <img :src="previewUrl" alt="">
        </div>
    </div>
    <el-dialog
        :visible.sync="showLogHistory"
        title="合同日志"
    >
        <el-table :data="logHistory">
            <el-table-column
                prop="logId"
                label="序号"
                align="center"
                show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
                prop="field"
                label="变更字段"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="oldValue"
                label="变更前"
                align="center"
                show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
                prop="value"
                label="变更后"
                align="center"
                show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
                prop="staffName"
                label="变更人"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="createTime"
                label="变更时间"
                align="center"
                show-overflow-tooltip
            >
            </el-table-column>
        </el-table>
        <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="pageNum"
        :page-size="10"
        layout="total, prev, pager, next"
        :total="total">
        </el-pagination>
    </el-dialog>
</div>
</template>

<script>
import * as apis from '../apis.js'
import mAlertInstance from 'mAlert'
import { saveAs } from 'file-saver'

var settings = require("aisportsConfig")

export default {
    components: {
    },
    data() {
        return {
            ossUrl: $('#ossUrl').val(),
            contractCode: '',
            fileList: [],
            preview: false,
            previewUrl: '',
            fileImg: {
                xlsx: 'file-excel-icon.png',
                xls: 'file-excel-icon.png',
                pdf: 'file-pdf-icon.png',
                docx: 'file-word-icon.png',
                doc: 'file-word-icon.png',
            },
            showLogHistory: false,
            logHistory: [],
            campState: '', // '0'-待审核 '1'-已审核 '2'-未通过 '3'-待上传合同
            pageNum: 1,
            total: 0
        }
    },
    mounted() {
        // 点击合同上传
        $(document).on('click','[data-role="contractsUpload"]', () => {
            this.campState = $('#campState').val()
                // 获取已上传合同
                this.getContractInfo()
        })
    },
    computed: {
        editable() {
            return this.campState === '2' || this.campState === '3'
        }
    },
    watch: {
    },
    methods: {
        getContractInfo() {
            apis.getContractInfo().then((res) => {
                this.contractCode = res.contractCode && res.contractCode.attrValue || ''
                this.fileList = (res.contracts || []).map((item) => {
                    const {fileName, filePath} = item
                    return {
                        fileType: fileName.substring(fileName.lastIndexOf(".") + 1),
                        filePath,
                        fileName,
                        url: this.ossUrl + filePath
                    }
                })
            })
        },
        previewImg(item) {
            this.preview = true
            this.previewUrl = item.url
        },
        onPreview(item) {
            if ('xlsx,pdf,docx'.indexOf(item.fileType) > -1) {
                this.viewFile(item)
            } else {
                this.previewImg(item)
            }

        },
        deleteFile(item) {
            apis.removeContract({
                filePath: item.filePath,
                fileName: item.fileName
            }).then(() => {
                this.getContractInfo()
            })
        },
        viewFile(item) {
            window.open('http://view.officeapps.live.com/op/view.aspx?src=' + item.url)
        },
        cancelPreview() {
            this.preview = false
            this.previewUrl = ''
        },
        submit() {
            var validate = $('#validateParent').validate({autofocus: false});
            if (!validate) return
            if (!this.fileList.length) {
                mAlertInstance.showWarning('请上传合同!');
                return
            }
            apis.addVenueContract({
                value: this.contractCode
            }).then(() => {
                window.location.href = settings.urlPrefix + '/venueCamp/init';
            })
        },
        backtopre() {

        },
        selectFile() {
            this.$refs.upload.click()
        },
        handleUpload(e) {
            const files = e.target.files
            
            for (let k in files) {
                if (Object.prototype.hasOwnProperty.call(files, k)) {
                    const fileData = new FormData()
                    fileData.append('file', files[k])
                    this.uploadFile(fileData)
                }
            }
        },
        uploadFile(fileData) {
            apis.importContract(fileData).then((res) => {
                const {fileName, url, filePath} = res
                this.fileList.push({
                    fileType: fileName.substring(fileName.lastIndexOf(".") + 1),
                    filePath,
                    fileName,
                    url
                })
            })
        },
        download(item) {
            if(item.url !== undefined){
                saveAs(item.url, item.fileName)
            }
        },
        showLog() {
            this.showLogHistory = true
            this.pageNum = 1
            this.getLogs()
        },
        getLogs() {
            apis.getContractOperationLog({
                pageNum: this.pageNum,
                pageSize: 10
            }).then(({operationLogList}) => {
                this.logHistory = operationLogList.list.map((item) => {
                    return {
                        ...item,
                        field: item.propKey === 'venue_campaign_contract' ? '合同附件' : '合同编码',
                        value: item.value || '--',
                        oldValue: item.oldValue || '--',
                    }
                })
                this.total = operationLogList.total
            })
        },
        handleCurrentChange(v) {
            this.pageNum = v
            this.getLogs()
        }
    }
}
</script>
