function intercept(res) {
    if (res.error !== 0) {
        throw new Error(res.message)
    }
    return res
}

// 获取证件类型下拉选
export const psptTypeList =
    $.get('/studentInfoManage/findPsptTypeList').then(intercept).then(({ psptTypeList }) => psptTypeList.map(p => ({
        value: p.paramKey,
        label: p.paramValue,
    })))

// 获取身份等级下拉选
export const identityLevelList =
    $.get('/studentInfoManage/findIdentityLevel').then(intercept).then(({ identityLevelList }) => identityLevelList.map(p => ({
        value: p.paramKey,
        label: p.paramValue,
    })))

// 获取国籍下拉选
export const nationalityList =
    $.get('/studentInfoManage/findNationalityList').then(intercept).then(({ nationalityList }) => nationalityList.map(p => ({
        value: p.paramKey,
        label: p.paramValue,
    })))

// 获取证件类型下拉选
export const venueList =
    $.get('/studentInfoManage/findVenueList').then(intercept).then(({ venueList }) => venueList.map(p => ({
        value: p.venueId,
        label: p.venueName,
    })))

// 获取学校下拉选
export function getSchoolLists(stuId) {
    return $.get('/studentInfoManage/findSchoolList', { stuId }).then(intercept)
}

// 获取学员信息
export function getStudentInfos(stuId) {
    return $.get('/studentInfoManage/findStudentInfo', { stuId }).then(intercept)
}

// 保存学员信息
export function saveStudentInfos(data) {
    return $.post('/studentInfoManage/saveStudentInfo', data).then(intercept)
}

// 获取项目等级信息
export function getItemLevels(stuId) {
    return $.get('/studentInfoManage/findServiceInfo', { stuId }).then(intercept)
}

// 获取项目等级下拉选
export function findTcLevelLists(serviceId) {
    return $.get('/studentInfoManage/findTcLevelList', { serviceId }).then(intercept)
}

// 保存项目等级
export function saveStudentLevels(data) {
    return $.get('/studentInfoManage/saveStudentLevel', data).then(intercept)
}

// 获取培训/私教信息
export function findCourseEnrollLists(stuId) {
    return $.get('/studentInfoManage/findCourseEnrollLists', { stuId }).then(intercept)
}
