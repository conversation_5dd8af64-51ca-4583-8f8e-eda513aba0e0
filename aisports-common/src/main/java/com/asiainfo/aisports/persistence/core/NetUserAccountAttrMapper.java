package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.NetUserAccount;
import com.asiainfo.aisports.domain.core.NetUserAccountAttr;
import com.asiainfo.aisports.domain.core.NetUserAccountAttrKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface NetUserAccountAttrMapper {
    @Delete({
        "delete from net_user_account_attr",
        "where net_user_id = #{netUserId,jdbcType=BIGINT}",
          "and username = #{username,jdbcType=VARCHAR}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    int deleteByPrimaryKey(NetUserAccountAttrKey key);

    @Insert({
        "insert into net_user_account_attr (net_user_id, username, ",
        "attr_code, attr_value)",
        "values (#{netUserId,jdbcType=BIGINT}, #{username,jdbcType=VARCHAR}, ",
        "#{attrCode,jdbcType=VARCHAR}, #{attrValue,jdbcType=VARCHAR})"
    })
    int insert(NetUserAccountAttr record);

    @Select({
        "select",
        "net_user_id, username, attr_code, attr_value",
        "from net_user_account_attr",
        "where net_user_id = #{netUserId,jdbcType=BIGINT}",
          "and username = #{username,jdbcType=VARCHAR}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="username", property="username", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR)
    })
    NetUserAccountAttr selectByPrimaryKey(NetUserAccountAttrKey key);

    @Update({
        "update net_user_account_attr",
        "set attr_value = #{attrValue,jdbcType=VARCHAR}",
        "where net_user_id = #{netUserId,jdbcType=BIGINT}",
          "and username = #{username,jdbcType=VARCHAR}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    int updateByPrimaryKey(NetUserAccountAttr record);

    @Select({
        "<script>",
        "select",
        "net_user_id, username, attr_code, attr_value",
        "from net_user_account_attr",
        "<where>",
        "<if test=\"netUserId != null\">and net_user_id = #{netUserId,jdbcType=BIGINT}</if>",
        "<if test=\"username != null\">and username = #{username,jdbcType=VARCHAR}</if>",
        "<if test=\"attrCode != null\">and attr_code = #{attrCode,jdbcType=VARCHAR}</if>",
        "<if test=\"attrValue != null\">and attr_value = #{attrValue,jdbcType=VARCHAR}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="username", property="username", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR)
    })
    List<NetUserAccountAttr> selectByFields(NetUserAccountAttr param);

    @Update({
        "<script>",
        "update net_user_account_attr",
        "<set>",
            "<if test=\"attrValue != null\">attr_value = #{attrValue,jdbcType=VARCHAR},</if>",
        "</set>",
        "where net_user_id = #{netUserId,jdbcType=BIGINT}",
          "and username = #{username,jdbcType=VARCHAR}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}",
        "</script>"
    })
    int updateByPrimaryKeySelective(NetUserAccountAttr record);

    @Select({
            "<script>",
            "select",
            "a.net_user_id, a.username, a.role",
            "from net_user_account a,net_user_account_attr b",
            "<where>",
            "a.net_user_id = b.net_user_id and b.attr_code = 'employee_id' and b.attr_value = #{attrCode,jdbcType=VARCHAR} limit 1",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
            @Result(column="username", property="username", jdbcType=JdbcType.VARCHAR),
            @Result(column="role", property="role", jdbcType=JdbcType.VARCHAR)
    })
    NetUserAccount selectByAttrCode(@Param("attrCode")String attrCode);
}