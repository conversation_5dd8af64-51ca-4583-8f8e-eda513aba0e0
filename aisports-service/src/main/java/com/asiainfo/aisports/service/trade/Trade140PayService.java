package com.asiainfo.aisports.service.trade;

import com.asiainfo.aisports.domain.core.Trade;
import com.asiainfo.aisports.service.game.GameService;
import com.yunyu.park.payment.enums.ConsumeTypeEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by xzeng on 18/12/21.
 * 约赛
 */
@Service
public class Trade140PayService extends BaseTradePayService {
    @Autowired
    GameService gameService;
    /**
     * 支付完成后调用的完工流程
     *
     * @param trade
     * @return
     */
    @Override
    public TradePayResult finishTrade(Trade trade) {
        TradePayResult tradePayResult = gameService.finishOrder(trade);
        if (tradePayResult.getError() == 0) {
            super.countPoints(trade);
            // 平阳场馆同步订单
            super.pingYangSyncFinishedOrder(trade, ConsumeTypeEnums.NormalConsume.getValue());
        }
        return tradePayResult;
    }

    /**
     * 业务对应的url
     *
     * @param trade
     * @return
     */
    @Override
    public String getUrl(Trade trade) {
        return null;
    }
}
