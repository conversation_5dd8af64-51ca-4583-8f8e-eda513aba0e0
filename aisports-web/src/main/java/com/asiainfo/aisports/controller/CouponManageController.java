package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.service.*;
import com.asiainfo.aisports.web.WebResult;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

/**
 * Created by xzeng on 16/2/22.
 * 优惠券配置
 */
@Controller
@RequestMapping("/couponManage")
public class CouponManageController {
    //定义页面tab页和业务类型数组
    private static final ImmutableMap<String, List<Long>> tradeTypeCodes = ImmutableMap.of("04", Arrays.asList(41L),
            "03", Arrays.asList(10L),
            "01", Arrays.asList(16L),
            "02", Arrays.asList(11L, 15L),
            "05", Arrays.asList(39L));

    @Autowired
    CouponService couponService;
    @Autowired
    TicketTypeService ticketTypeService;
    @Autowired
    ProductService productService;
    @Autowired
    VenueServService venueServService;
    @Autowired
    CouponBatchService couponBatchService;
    @Autowired
    GoodsService goodsService;

    /**
     * 优惠券配置,初始页面
     *
     * @param loginStaff 登陆员工
     * @param model      返回值
     * @return
     */
    @RequestMapping("/initial")
    public String couponManage(LoginStaff loginStaff,
                               ModelMap model) {
        List couponList = couponService.getCoupons(null, tradeTypeCodes.get("04"), 1, loginStaff.getCenterId(), loginStaff.getVenueId());
        PageInfo pageInfo = new PageInfo<Map<String, Object>>(couponList);
        model.put("couponList", couponList);
        model.put("pageInfo", pageInfo);
        return "/couponManage/couponInfo";
    }

    /**
     * 获得优惠券信息列表
     *
     * @param pageNo     页码
     * @param couponName 优惠券名
     * @param type       类型
     * @param loginStaff 登陆员工
     * @return
     */
    @RequestMapping("/getCoupons")
    @ResponseBody
    public WebResult getCoupons(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "couponName", required = false) String couponName,
                                @RequestParam(value = "type", required = true) String type,
                                LoginStaff loginStaff) {
        //初始化页码
        if (pageNo == null) {
            pageNo = 1;
        }
        List tradeTypes = tradeTypeCodes.get(type);
        List couponList = couponService.getCoupons(couponName, tradeTypes, pageNo, loginStaff.getCenterId(), loginStaff.getVenueId());
        PageInfo pageInfo = new PageInfo<Map<String, Object>>(couponList);
        return new WebResult().set("couponList", couponList).set("pageInfo", pageInfo);
    }

    /**
     * 跳转到优惠券配置页面
     *
     * @param type      类型
     * @param couponId  优惠券Id
     * @param valueType 券类型
     * @param staff     登陆员工
     * @param model     返回值
     * @return
     */
    @RequestMapping("/configCoupon")
    public String configCoupon(@RequestParam(value = "type", required = true) String type,
                               @RequestParam(value = "couponId", required = false) Long couponId,
                               @RequestParam(value = "valueType", required = true) String valueType,
                               LoginStaff staff,
                               ModelMap model) {
        //如果是编辑,获取原来保存的内容
        if (couponId != null) {
            Map couponConfig = couponService.getCouponConfig(couponId, tradeTypeCodes.get(type), staff.getCenterId(), staff.getVenueId());
            if (!Constants.CouponValueType.MONEY.equals(couponConfig.get("valueType").toString())) {
                Long ticketTypeId = Long.parseLong(couponConfig.get("value").toString());
                TicketType ticketType = ticketTypeService.selectByPrimaryKey(ticketTypeId);
                model.put("ticketType", ticketType);
            }
            model.put("couponConfig", couponConfig);
        }
        //获取体验券和兑换券类型的所有运动类型
        List<Map> ticketsAndServices = ticketTypeService.getTicketsAndServices(staff.getCenterId(), staff.getVenueId());
        model.put("ticketsAndServices", ticketsAndServices);

        //获取场馆下所有可以使用的产品
        List<Product> products = productService.queryProductListByVenueId(staff.getVenueId());
        model.put("products", products);

        //获取场馆下所有服务
        List<DataMap> venueServList = venueServService.findByCenterIdAndVenueId(staff.getCenterId(), staff.getVenueId());
        model.put("venueServList", venueServList);

        //获取中心下的所有商品
        List<Goods> goodsList = goodsService.getGoodsByCenterId(staff.getCenterId());
        model.put("goodsList", goodsList);

        model.put("type", type);
        model.put("valueType", valueType);
        return "/couponManage/configCoupon";
    }

    //保存修改的优惠券

    /**
     * 保存修改的优惠券
     *
     * @param couponId   优惠券id
     * @param type       类型
     * @param services   适用服务
     * @param products   适用产品
     * @param couponName 优惠券名
     * @param startDate  开始时间
     * @param endDate    结束时间
     * @param valueType  券类型
     * @param value      金额或票Id
     * @param workDays   使用的工作日
     * @param validDays  有效天数
     * @param holidayTag 节假日可用标志
     * @param staff      登陆员工
     * @return
     */
    @RequestMapping("/saveCoupon")
    @ResponseBody
    public WebResult saveCoupon(@RequestParam(value = "couponId", required = false) Long couponId,
                                @RequestParam(value = "type", required = true) String type,
                                @RequestParam(value = "services", required = false) String services,
                                @RequestParam(value = "products", required = false) String products,
                                @RequestParam(value = "couponName", required = true) String couponName,
                                @RequestParam(value = "startDate", required = true) String startDate,
                                @RequestParam(value = "endDate", required = true) String endDate,
                                @RequestParam(value = "valueType", required = true) String valueType,
                                @RequestParam(value = "value", required = true) Long value,
                                @RequestParam(value = "workDays", required = false) String workDays,
                                @RequestParam(value = "validDays", required = false) Integer validDays,
                                @RequestParam(value = "holidayTag", required = false) String holidayTag,
                                LoginStaff staff) {
        //保存券
        JSONObject result = couponService.saveCouponConfig(couponId, staff.getCenterId(), staff.getVenueId(),
                couponName, valueType, value, startDate, endDate,
                workDays,validDays, holidayTag, staff.getStaffId(),
                tradeTypeCodes.get(type), services, products, type);
        if (result.getInt("error") == 0) {
            return new WebResult();
        } else {
            return new WebResult(result.getInt("error"), result.getString("message"));
        }
    }

    /**
     * 删除优惠券
     *
     * @param couponId 优惠券Id
     * @param staff    登陆员工
     * @return
     */
    @RequestMapping("/deleteCoupon")
    @ResponseBody
    public WebResult deleteCoupon(@RequestParam(value = "couponId", required = true) Long couponId,
                                  LoginStaff staff) {
        JSONObject result = couponService.deleteCoupon(couponId, staff.getStaffId());
        if (result.getInt("error") == 0) {
            return new WebResult();
        } else {
            return new WebResult(result.getInt("error"), result.getString("message"));
        }
    }

    /**
     * 查看优惠券库存
     *
     * @param couponId 优惠券Id
     * @param staff    登陆员工
     * @param model    返回值
     * @return
     */
    @RequestMapping("/couponDetails")
    public String couponDetails(@RequestParam(value = "couponId") Long couponId,
                                LoginStaff staff,
                                ModelMap model) {
        Map couponInfo = couponService.getCouponDetails(couponId, staff.getCenterId());
        model.putAll(couponInfo);
        model.put("couponId", couponId);
        return "/couponManage/couponDetails";
    }

    /**
     * 添加电子券
     *
     * @param couponId 优惠券Id
     * @param amount   数量
     * @param staff    登陆员工
     * @return
     */
    @RequestMapping("/addElecCoupon")
    @ResponseBody
    public WebResult addElecCoupon(@RequestParam(value = "couponId", required = true) Long couponId,
                                   @RequestParam(value = "amount", required = true) Integer amount,
                                   LoginStaff staff) {
        ServiceResult result = couponBatchService.saveCouponBatch(couponId, staff.getStaffId(), Constants.CouponType.ELECTRIC, amount);
        WebResult webResult = new WebResult();
        webResult.putAll(result);
        return webResult;
    }

    /**
     * 添加实体券
     *
     * @param couponId 优惠券Id
     * @param amount   数量
     * @param staff    登陆员工
     * @return
     */
    @RequestMapping("addEntityCouon")
    @ResponseBody
    public WebResult addEntityCouon(@RequestParam(value = "couponId", required = true) Long couponId,
                                    @RequestParam(value = "amount", required = true) Integer amount,
                                    LoginStaff staff) {
        ServiceResult result = couponBatchService.saveCouponBatch(couponId, staff.getStaffId(), Constants.CouponType.ENTITY, amount);
        WebResult webResult = new WebResult();
        webResult.putAll(result);
        return webResult;
    }
}
