<#if cabinetJSONArray?? && (cabinetJSONArray?size>0)>
    <#list cabinetJSONArray as cabinetJSONObject>
<div class="f-line">
    <div class="remark-line">
        <span class="level-num">${cabinetJSONObject.CabinetLevel.levelNo}层</span>
        <span class="standard">（规格：${cabinetJSONObject.CabinetLevel.cabinetSpec})</span>
    </div>
    <ul class="select-line clearfix">
        <#if cabinetJSONObject.cabinetList?? && (cabinetJSONObject.cabinetList?size>0)>
            <#list cabinetJSONObject.cabinetList as cabinet>
                <#if cabinet.state=="0">
                <li id="#{cabinet.cabinetId}" data-deposit="#{cabinet.depositFee/100}" class="select-wrapper available">
                <#else>
                <li id="#{cabinet.cabinetId}" data-deposit="#{cabinet.depositFee/100}" class="select-wrapper occupy">
                </#if>
            <span class="cab-number">${cabinet.cabinetNo}</span>
            <span class="cab-price">#{cabinet.rentFee/100}</span>
        </li>
            </#list>
        </#if>
    </ul>
</div>
</#list>
</#if>