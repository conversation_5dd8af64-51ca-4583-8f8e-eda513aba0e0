package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.model.*;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface TradeTicketMapper {
    @Delete({
            "delete from trade_ticket",
            "where ticket_id = #{ticketId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long ticketId);

    @Insert({
            "insert into trade_ticket (ticket_id, trade_id, ",
            "ticket_no, service_id, ",
            "venue_id, field_id, ",
            "field_name, start_segment, ",
            "end_segment, start_time, ",
            "end_time, pay_money, ",
            "discount, state, ",
            "create_time, fetch_ticket_time, ",
            "check_ticket_time, deposit_id, ",
            "ticket_source_type, ticket_drawer, ",
            "ticket_collector, ticket_cancel_person, ",
            "check_mode, effect_date, ",
            "expire_date, ecard_no, ",
            "cust_id, cust_name, ",
            "product_id, ticket_type, ",
            "price_item, round, ",
            "remark, coupon_amount, ",
            "cancel_trade_id, group_ticekt_id, ",
            "group_tag, prom_id, pspt_id, ",
            "full_tag, regular_price, ",
            "player_num, coupon_no, ",
            "coach_id, chnl_cust_id, ",
            "refund_money, allowance, ",
            "ticket_time_id, old_state, ",
            "entry_ticket_id, rent_tag)",
            "values (#{ticketId,jdbcType=BIGINT}, #{tradeId,jdbcType=BIGINT}, ",
            "#{ticketNo,jdbcType=VARCHAR}, #{serviceId,jdbcType=BIGINT}, ",
            "#{venueId,jdbcType=BIGINT}, #{fieldId,jdbcType=BIGINT}, ",
            "#{fieldName,jdbcType=VARCHAR}, #{startSegment,jdbcType=DECIMAL}, ",
            "#{endSegment,jdbcType=DECIMAL}, #{startTime,jdbcType=VARCHAR}, ",
            "#{endTime,jdbcType=VARCHAR}, #{payMoney,jdbcType=BIGINT}, ",
            "#{discount,jdbcType=BIGINT}, #{state,jdbcType=VARCHAR}, ",
            "#{createTime,jdbcType=TIMESTAMP}, #{fetchTicketTime,jdbcType=TIMESTAMP}, ",
            "#{checkTicketTime,jdbcType=TIMESTAMP}, #{depositId,jdbcType=BIGINT}, ",
            "#{ticketSourceType,jdbcType=VARCHAR}, #{ticketDrawer,jdbcType=BIGINT}, ",
            "#{ticketCollector,jdbcType=BIGINT}, #{ticketCancelPerson,jdbcType=BIGINT}, ",
            "#{checkMode,jdbcType=VARCHAR}, #{effectDate,jdbcType=DATE}, ",
            "#{expireDate,jdbcType=DATE}, #{ecardNo,jdbcType=VARCHAR}, ",
            "#{custId,jdbcType=BIGINT}, #{custName,jdbcType=VARCHAR}, ",
            "#{productId,jdbcType=BIGINT}, #{ticketType,jdbcType=BIGINT}, ",
            "#{priceItem,jdbcType=BIGINT}, #{round,jdbcType=VARCHAR}, ",
            "#{remark,jdbcType=VARCHAR}, #{couponAmount,jdbcType=INTEGER}, ",
            "#{cancelTradeId,jdbcType=BIGINT}, #{groupTicektId,jdbcType=BIGINT}, ",
            "#{groupTag,jdbcType=CHAR}, #{promId,jdbcType=BIGINT}, #{psptId,jdbcType=VARCHAR}, ",
            "#{fullTag,jdbcType=CHAR}, #{regularPrice,jdbcType=INTEGER}, ",
            "#{playerNum,jdbcType=INTEGER}, #{couponNo,jdbcType=VARCHAR}, ",
            "#{coachId,jdbcType=BIGINT}, #{chnlCustId,jdbcType=VARCHAR}, ",
            "#{refundMoney,jdbcType=BIGINT}, #{allowance,jdbcType=INTEGER}, ",
            "#{ticketTimeId,jdbcType=BIGINT}, #{oldState,jdbcType=VARCHAR}, ",
            "#{entryTicketId,jdbcType=BIGINT}, #{rentTag,jdbcType=CHAR})"
    })
    int insert(TradeTicket record);

    @Select({
            "select",
            "ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, ",
            "start_segment, end_segment, start_time, end_time, pay_money, discount, state, ",
            "create_time, fetch_ticket_time, check_ticket_time, deposit_id, ticket_source_type, ",
            "ticket_drawer, ticket_collector, ticket_cancel_person, check_mode, effect_date, ",
            "expire_date, ecard_no, cust_id, cust_name, product_id, ticket_type, price_item, ",
            "round, remark, coupon_amount, cancel_trade_id, group_ticekt_id, group_tag, prom_id, ",
            "pspt_id, full_tag, regular_price, player_num, coupon_no, coach_id, chnl_cust_id, ",
            "refund_money, allowance, ticket_time_id, old_state, entry_ticket_id, rent_tag",
            "from trade_ticket",
            "where ticket_id = #{ticketId,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "rent_tag", property = "rentTag", jdbcType = JdbcType.CHAR)
    })
    TradeTicket selectByPrimaryKey(Long ticketId);

    @Update({
            "update trade_ticket",
            "set trade_id = #{tradeId,jdbcType=BIGINT},",
            "ticket_no = #{ticketNo,jdbcType=VARCHAR},",
            "service_id = #{serviceId,jdbcType=BIGINT},",
            "venue_id = #{venueId,jdbcType=BIGINT},",
            "field_id = #{fieldId,jdbcType=BIGINT},",
            "field_name = #{fieldName,jdbcType=VARCHAR},",
            "start_segment = #{startSegment,jdbcType=DECIMAL},",
            "end_segment = #{endSegment,jdbcType=DECIMAL},",
            "start_time = #{startTime,jdbcType=VARCHAR},",
            "end_time = #{endTime,jdbcType=VARCHAR},",
            "pay_money = #{payMoney,jdbcType=BIGINT},",
            "discount = #{discount,jdbcType=BIGINT},",
            "state = #{state,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "fetch_ticket_time = #{fetchTicketTime,jdbcType=TIMESTAMP},",
            "check_ticket_time = #{checkTicketTime,jdbcType=TIMESTAMP},",
            "deposit_id = #{depositId,jdbcType=BIGINT},",
            "ticket_source_type = #{ticketSourceType,jdbcType=VARCHAR},",
            "ticket_drawer = #{ticketDrawer,jdbcType=BIGINT},",
            "ticket_collector = #{ticketCollector,jdbcType=BIGINT},",
            "ticket_cancel_person = #{ticketCancelPerson,jdbcType=BIGINT},",
            "check_mode = #{checkMode,jdbcType=VARCHAR},",
            "effect_date = #{effectDate,jdbcType=DATE},",
            "expire_date = #{expireDate,jdbcType=DATE},",
            "ecard_no = #{ecardNo,jdbcType=VARCHAR},",
            "cust_id = #{custId,jdbcType=BIGINT},",
            "cust_name = #{custName,jdbcType=VARCHAR},",
            "product_id = #{productId,jdbcType=BIGINT},",
            "ticket_type = #{ticketType,jdbcType=BIGINT},",
            "price_item = #{priceItem,jdbcType=BIGINT},",
            "round = #{round,jdbcType=VARCHAR},",
            "remark = #{remark,jdbcType=VARCHAR},",
            "coupon_amount = #{couponAmount,jdbcType=INTEGER},",
            "cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT},",
            "group_ticekt_id = #{groupTicektId,jdbcType=BIGINT},",
            "group_tag = #{groupTag,jdbcType=CHAR},",
            "prom_id = #{promId,jdbcType=BIGINT},",
            "pspt_id = #{psptId,jdbcType=VARCHAR},",
            "full_tag = #{fullTag,jdbcType=CHAR},",
            "regular_price = #{regularPrice,jdbcType=INTEGER},",
            "player_num = #{playerNum,jdbcType=INTEGER},",
            "coupon_no = #{couponNo,jdbcType=VARCHAR},",
            "coach_id = #{coachId,jdbcType=BIGINT},",
            "chnl_cust_id = #{chnlCustId,jdbcType=VARCHAR},",
            "refund_money = #{refundMoney,jdbcType=BIGINT},",
            "allowance = #{allowance,jdbcType=INTEGER},",
            "ticket_time_id = #{ticketTimeId,jdbcType=BIGINT},",
            "old_state = #{oldState,jdbcType=VARCHAR},",
            "entry_ticket_id = #{entryTicketId,jdbcType=BIGINT},",
            "rent_tag = #{rentTag,jdbcType=CHAR}",
            "where ticket_id = #{ticketId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(TradeTicket record);

    @Select({
            "<script>",
            "select",
            "ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, start_segment, end_segment, start_time, end_time, pay_money, discount, state, create_time, fetch_ticket_time, check_ticket_time, deposit_id, ticket_source_type, ticket_drawer, ticket_collector, ticket_cancel_person, check_mode, effect_date, expire_date, ecard_no, cust_id, cust_name, product_id, ticket_type, price_item, round, remark, coupon_amount, cancel_trade_id, group_ticekt_id, group_tag, prom_id, pspt_id, full_tag, regular_price, player_num, coupon_no, coach_id, chnl_cust_id, refund_money, allowance, ticket_time_id, old_state, entry_ticket_id, rent_tag",
            "from trade_ticket",
            "<where>",
            "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "<if test=\"tradeId != null\">and trade_id = #{tradeId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketNo != null\">and ticket_no = #{ticketNo,jdbcType=VARCHAR}</if>",
            "<if test=\"serviceId != null\">and service_id = #{serviceId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldId != null\">and field_id = #{fieldId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldName != null\">and field_name = #{fieldName,jdbcType=VARCHAR}</if>",
            "<if test=\"startSegment != null\">and start_segment = #{startSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"endSegment != null\">and end_segment = #{endSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"startTime != null\">and start_time = #{startTime,jdbcType=VARCHAR}</if>",
            "<if test=\"endTime != null\">and end_time = #{endTime,jdbcType=VARCHAR}</if>",
            "<if test=\"payMoney != null\">and pay_money = #{payMoney,jdbcType=BIGINT}</if>",
            "<if test=\"discount != null\">and discount = #{discount,jdbcType=BIGINT}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"fetchTicketTime != null\">and fetch_ticket_time = #{fetchTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"checkTicketTime != null\">and check_ticket_time = #{checkTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"depositId != null\">and deposit_id = #{depositId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketSourceType != null\">and ticket_source_type = #{ticketSourceType,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketDrawer != null\">and ticket_drawer = #{ticketDrawer,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCollector != null\">and ticket_collector = #{ticketCollector,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCancelPerson != null\">and ticket_cancel_person = #{ticketCancelPerson,jdbcType=BIGINT}</if>",
            "<if test=\"checkMode != null\">and check_mode = #{checkMode,jdbcType=VARCHAR}</if>",
            "<if test=\"effectDate != null\">and effect_date = #{effectDate,jdbcType=DATE}</if>",
            "<if test=\"expireDate != null\">and expire_date = #{expireDate,jdbcType=DATE}</if>",
            "<if test=\"ecardNo != null\">and ecard_no = #{ecardNo,jdbcType=VARCHAR}</if>",
            "<if test=\"custId != null\">and cust_id = #{custId,jdbcType=BIGINT}</if>",
            "<if test=\"custName != null\">and cust_name = #{custName,jdbcType=VARCHAR}</if>",
            "<if test=\"productId != null\">and product_id = #{productId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketType != null\">and ticket_type = #{ticketType,jdbcType=BIGINT}</if>",
            "<if test=\"priceItem != null\">and price_item = #{priceItem,jdbcType=BIGINT}</if>",
            "<if test=\"round != null\">and round = #{round,jdbcType=VARCHAR}</if>",
            "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
            "<if test=\"couponAmount != null\">and coupon_amount = #{couponAmount,jdbcType=INTEGER}</if>",
            "<if test=\"cancelTradeId != null\">and cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTicektId != null\">and group_ticekt_id = #{groupTicektId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTag != null\">and group_tag = #{groupTag,jdbcType=CHAR}</if>",
            "<if test=\"promId != null\">and prom_id = #{promId,jdbcType=BIGINT}</if>",
            "<if test=\"psptId != null\">and pspt_id = #{psptId,jdbcType=VARCHAR}</if>",
            "<if test=\"fullTag != null\">and full_tag = #{fullTag,jdbcType=CHAR}</if>",
            "<if test=\"regularPrice != null\">and regular_price = #{regularPrice,jdbcType=INTEGER}</if>",
            "<if test=\"playerNum != null\">and player_num = #{playerNum,jdbcType=INTEGER}</if>",
            "<if test=\"couponNo != null\">and coupon_no = #{couponNo,jdbcType=VARCHAR}</if>",
            "<if test=\"coachId != null\">and coach_id = #{coachId,jdbcType=BIGINT}</if>",
            "<if test=\"chnlCustId != null\">and chnl_cust_id = #{chnlCustId,jdbcType=VARCHAR}</if>",
            "<if test=\"refundMoney != null\">and refund_money = #{refundMoney,jdbcType=BIGINT}</if>",
            "<if test=\"allowance != null\">and allowance = #{allowance,jdbcType=INTEGER}</if>",
            "<if test=\"ticketTimeId != null\">and ticket_time_id = #{ticketTimeId,jdbcType=BIGINT}</if>",
            "<if test=\"oldState != null\">and old_state = #{oldState,jdbcType=VARCHAR}</if>",
            "<if test=\"entryTicketId != null\">and entry_ticket_id = #{entryTicketId,jdbcType=BIGINT}</if>",
            "<if test=\"rentTag != null\">and rent_tag = #{rentTag,jdbcType=CHAR}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "rent_tag", property = "rentTag", jdbcType = JdbcType.CHAR)
    })
    List<TradeTicket> selectByFields(TradeTicket param);

    @Update({
            "<script>",
            "update trade_ticket",
            "<set>",
            "<if test=\"tradeId != null\">trade_id = #{tradeId,jdbcType=BIGINT},</if>",
            "<if test=\"ticketNo != null\">ticket_no = #{ticketNo,jdbcType=VARCHAR},</if>",
            "<if test=\"serviceId != null\">service_id = #{serviceId,jdbcType=BIGINT},</if>",
            "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
            "<if test=\"fieldId != null\">field_id = #{fieldId,jdbcType=BIGINT},</if>",
            "<if test=\"fieldName != null\">field_name = #{fieldName,jdbcType=VARCHAR},</if>",
            "<if test=\"startSegment != null\">start_segment = #{startSegment,jdbcType=DECIMAL},</if>",
            "<if test=\"endSegment != null\">end_segment = #{endSegment,jdbcType=DECIMAL},</if>",
            "<if test=\"startTime != null\">start_time = #{startTime,jdbcType=VARCHAR},</if>",
            "<if test=\"endTime != null\">end_time = #{endTime,jdbcType=VARCHAR},</if>",
            "<if test=\"payMoney != null\">pay_money = #{payMoney,jdbcType=BIGINT},</if>",
            "<if test=\"discount != null\">discount = #{discount,jdbcType=BIGINT},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"fetchTicketTime != null\">fetch_ticket_time = #{fetchTicketTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"checkTicketTime != null\">check_ticket_time = #{checkTicketTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"depositId != null\">deposit_id = #{depositId,jdbcType=BIGINT},</if>",
            "<if test=\"ticketSourceType != null\">ticket_source_type = #{ticketSourceType,jdbcType=VARCHAR},</if>",
            "<if test=\"ticketDrawer != null\">ticket_drawer = #{ticketDrawer,jdbcType=BIGINT},</if>",
            "<if test=\"ticketCollector != null\">ticket_collector = #{ticketCollector,jdbcType=BIGINT},</if>",
            "<if test=\"ticketCancelPerson != null\">ticket_cancel_person = #{ticketCancelPerson,jdbcType=BIGINT},</if>",
            "<if test=\"checkMode != null\">check_mode = #{checkMode,jdbcType=VARCHAR},</if>",
            "<if test=\"effectDate != null\">effect_date = #{effectDate,jdbcType=DATE},</if>",
            "<if test=\"expireDate != null\">expire_date = #{expireDate,jdbcType=DATE},</if>",
            "<if test=\"ecardNo != null\">ecard_no = #{ecardNo,jdbcType=VARCHAR},</if>",
            "<if test=\"custId != null\">cust_id = #{custId,jdbcType=BIGINT},</if>",
            "<if test=\"custName != null\">cust_name = #{custName,jdbcType=VARCHAR},</if>",
            "<if test=\"productId != null\">product_id = #{productId,jdbcType=BIGINT},</if>",
            "<if test=\"ticketType != null\">ticket_type = #{ticketType,jdbcType=BIGINT},</if>",
            "<if test=\"priceItem != null\">price_item = #{priceItem,jdbcType=BIGINT},</if>",
            "<if test=\"round != null\">round = #{round,jdbcType=VARCHAR},</if>",
            "<if test=\"remark != null\">remark = #{remark,jdbcType=VARCHAR},</if>",
            "<if test=\"couponAmount != null\">coupon_amount = #{couponAmount,jdbcType=INTEGER},</if>",
            "<if test=\"cancelTradeId != null\">cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT},</if>",
            "<if test=\"groupTicektId != null\">group_ticekt_id = #{groupTicektId,jdbcType=BIGINT},</if>",
            "<if test=\"groupTag != null\">group_tag = #{groupTag,jdbcType=CHAR},</if>",
            "<if test=\"promId != null\">prom_id = #{promId,jdbcType=BIGINT},</if>",
            "<if test=\"psptId != null\">pspt_id = #{psptId,jdbcType=VARCHAR},</if>",
            "<if test=\"fullTag != null\">full_tag = #{fullTag,jdbcType=CHAR},</if>",
            "<if test=\"regularPrice != null\">regular_price = #{regularPrice,jdbcType=INTEGER},</if>",
            "<if test=\"playerNum != null\">player_num = #{playerNum,jdbcType=INTEGER},</if>",
            "<if test=\"couponNo != null\">coupon_no = #{couponNo,jdbcType=VARCHAR},</if>",
            "<if test=\"coachId != null\">coach_id = #{coachId,jdbcType=BIGINT},</if>",
            "<if test=\"chnlCustId != null\">chnl_cust_id = #{chnlCustId,jdbcType=VARCHAR},</if>",
            "<if test=\"refundMoney != null\">refund_money = #{refundMoney,jdbcType=BIGINT},</if>",
            "<if test=\"allowance != null\">allowance = #{allowance,jdbcType=INTEGER},</if>",
            "<if test=\"ticketTimeId != null\">ticket_time_id = #{ticketTimeId,jdbcType=BIGINT},</if>",
            "<if test=\"oldState != null\">old_state = #{oldState,jdbcType=VARCHAR},</if>",
            "<if test=\"entryTicketId != null\">entry_ticket_id = #{entryTicketId,jdbcType=BIGINT},</if>",
            "<if test=\"rentTag != null\">rent_tag = #{rentTag,jdbcType=CHAR},</if>",
            "</set>",
            "where ticket_id = #{ticketId,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(TradeTicket record);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.ticket_no, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, a.end_segment+1 end_segment, ",
            "a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type, ",
            "a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, ",
            "a.product_id, a.ticket_type, a.remark, a.coupon_amount, a.group_ticekt_id, a.group_tag, a.coach_id, a.ticket_time_id",
            "from trade_ticket a where trade_id=#{tradeId,jdbcType=BIGINT} ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeTicket> selectByTradeId(Long tradeId);

    @Select({
            "<script>",
            "select",
            "ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, ",
            "start_segment, end_segment, start_time, end_time, pay_money, discount, state, ",
            "create_time, fetch_ticket_time, check_ticket_time, deposit_id, ticket_source_type, ",
            "ticket_drawer, ticket_collector, ticket_cancel_person, check_mode, effect_date, ",
            "expire_date, ecard_no, cust_id, cust_name, product_id, ticket_type, price_item, ",
            "round, remark, coupon_amount, cancel_trade_id, group_ticekt_id, group_tag, prom_id, ",
            "pspt_id, full_tag, regular_price, player_num, coupon_no, coach_id, chnl_cust_id, ",
            "refund_money, allowance, ticket_time_id, old_state, entry_ticket_id, rent_tag",
            "from trade_ticket where trade_id = #{tradeId,jdbcType=BIGINT} ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "rent_tag", property = "rentTag", jdbcType = JdbcType.CHAR)
    })
    List<DataMap> selectTradeTicketByTradeId(Long tradeId);

    @Update({
            "<script>",
            "update trade_ticket",
            "<set>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"ecardNo != null\">ecard_no = #{ecardNo,jdbcType=VARCHAR},</if>",
            "<if test=\"custId != null\">cust_id = #{custId,jdbcType=BIGINT},</if>",
            "<if test=\"custName != null\">cust_name = #{custName, jdbcType=VARCHAR},</if>",
            "<if test=\"psptId != null\">pspt_id = #{psptId, jdbcType=VARCHAR},</if>",
            "<if test=\"depositId != null\">deposit_id = #{depositId,jdbcType=BIGINT},</if>",
            "<if test=\"productId != null\">product_id = #{productId,jdbcType=BIGINT},</if>",
            "</set>",
            "where trade_id = #{tradeId,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByTradeIdSelective(TradeTicket record);

    // 取票时更新状态和取票时间
    @Update({
            "update trade_ticket",
            "set state ='" + Constants.TicketState.FETCHED + "'",
            ",fetch_ticket_time = sysdate()",
            "where ticket_id = #{ticketId,jdbcType=BIGINT}"
    })
    int collectTicketByTicketId(Long ticketId);

    // 根据tradeId 更改票的状态为已经取票
    @Update({
            "update trade_ticket",
            "set state ='" + Constants.TicketState.FETCHED + "'",
            ",fetch_ticket_time = now()",
            "where trade_id = #{tradeId,jdbcType=BIGINT}"
    })
    int collectTicketsByTradeId(Long tradeId);

    /**
     * 根据trade_id更新票状态
     *
     * @param tradeId
     * @return
     */
    @Update({
            "update trade_ticket",
            "set state = #{state,jdbcType=VARCHAR}",
            "where trade_id = #{tradeId,jdbcType=BIGINT}"
    })
    int updateTicketStateByTradeId(@Param("tradeId") long tradeId, @Param("state") String state);

    /**
     * 取消票,并填写退票金额
     */
    @Update({
            "<script>",
            "<foreach collection=\"list\" item=\"ticket\" index=\"index\" separator=\";\">",
            "update trade_ticket",
            "set state ='1',",
            "cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT},",
            "refund_money = #{ticket.returnMoney,jdbcType=BIGINT}",
            "where ticket_id = #{ticket.ticketId,jdbcType=BIGINT}",
            "</foreach>",
            "</script>"
    })
    int refundTickets(@Param("list") List list, @Param("cancelTradeId") Long cancelTradeId);

    @Update({
            "<script>",
            "update trade_ticket",
            "set old_state = state, ",
            "state ='1', ",
            "refund_money = #{refundMoney,jdbcType=BIGINT}, ",
            "cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT}",
            "where ticket_id = #{ticketId,jdbcType=BIGINT}",
            "and state != '1'",
            "</script>"
    })
    int cancelTicket(@Param("ticketId") Long ticketId, @Param("refundMoney") long refundMoney,
                     @Param("cancelTradeId") Long cancelTradeId);

    @Update({
            "<script>",
            "update trade_ticket",
            "<set>",
            "state = old_state, ",
            "old_state = null, ",
            "refund_money = null, ",
            "cancel_trade_id = null",
            "</set>",
            "where ticket_id in (",
            "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
            "#{item.ticketId,jdbcType=BIGINT}",
            "</foreach>",
            ")",
            "</script>"
    })
    int restoreTicket(List<TradeTicket> refundTicketList);

    @Insert({
            "<script>",
            "insert into trade_ticket (ticket_id, trade_id, ",
            "ticket_no, service_id, ",
            "venue_id, field_id, ",
            "field_name, start_segment, ",
            "end_segment, start_time, end_time, pay_money, ",
            "discount, state, ",
            "create_time, fetch_ticket_time, ",
            "check_ticket_time, deposit_id, ",
            "ticket_source_type, ticket_drawer, ",
            "ticket_collector, ticket_cancel_person, ",
            "check_mode, effect_date, ",
            "expire_date, ecard_no, ",
            "cust_id, cust_name, ",
            "product_id, ticket_type, ",
            "price_item, round, ",
            "remark, coupon_amount, ",
            "cancel_trade_id, group_ticekt_id, ",
            "group_tag, prom_id, pspt_id, ",
            "full_tag, regular_price, ",
            "player_num, coupon_no, ",
            "coach_id, chnl_cust_id, ",
            "refund_money, allowance, ticket_time_id, entry_ticket_id, rent_tag)",
            "values ",
            "<foreach collection=\"tradeTickets\" item=\"item\" index=\"index\" separator=\",\">",
            "(#{item.ticketId,jdbcType=BIGINT}, #{item.tradeId,jdbcType=BIGINT}, ",
            "#{item.ticketNo,jdbcType=VARCHAR}, #{item.serviceId,jdbcType=BIGINT}, ",
            "#{item.venueId,jdbcType=BIGINT}, #{item.fieldId,jdbcType=BIGINT}, ",
            "#{item.fieldName,jdbcType=VARCHAR}, #{item.startSegment,jdbcType=DECIMAL}, ",
            "#{item.endSegment,jdbcType=DECIMAL}, #{item.startTime,jdbcType=VARCHAR}, #{item.endTime,jdbcType=VARCHAR}, #{item.payMoney,jdbcType=BIGINT}, ",
            "#{item.discount,jdbcType=BIGINT}, #{item.state,jdbcType=VARCHAR}, ",
            "#{item.createTime,jdbcType=TIMESTAMP}, #{item.fetchTicketTime,jdbcType=TIMESTAMP}, ",
            "#{item.checkTicketTime,jdbcType=TIMESTAMP}, #{item.depositId,jdbcType=BIGINT}, ",
            "#{item.ticketSourceType,jdbcType=VARCHAR}, #{item.ticketDrawer,jdbcType=BIGINT}, ",
            "#{item.ticketCollector,jdbcType=BIGINT}, #{item.ticketCancelPerson,jdbcType=BIGINT}, ",
            "#{item.checkMode,jdbcType=VARCHAR}, #{item.effectDate,jdbcType=DATE}, ",
            "#{item.expireDate,jdbcType=DATE}, #{item.ecardNo,jdbcType=VARCHAR}, ",
            "#{item.custId,jdbcType=BIGINT}, #{item.custName,jdbcType=VARCHAR}, ",
            "#{item.productId,jdbcType=BIGINT}, #{item.ticketType,jdbcType=BIGINT}, ",
            "#{item.priceItem,jdbcType=BIGINT}, #{item.round,jdbcType=VARCHAR}, ",
            "#{item.remark,jdbcType=VARCHAR}, #{item.couponAmount,jdbcType=INTEGER}, ",
            "#{item.cancelTradeId,jdbcType=BIGINT}, #{item.groupTicektId,jdbcType=BIGINT}, ",
            "#{item.groupTag,jdbcType=CHAR}, #{item.promId,jdbcType=BIGINT}, #{item.psptId,jdbcType=VARCHAR}, ",
            "#{item.fullTag,jdbcType=CHAR}, #{item.regularPrice,jdbcType=INTEGER}, ",
            "#{item.playerNum,jdbcType=INTEGER}, #{item.couponNo,jdbcType=VARCHAR}, ",
            "#{item.coachId,jdbcType=BIGINT}, #{item.chnlCustId,jdbcType=VARCHAR}, ",
            "#{item.refundMoney,jdbcType=BIGINT},#{item.allowance,jdbcType=INTEGER},#{item.ticketTimeId,jdbcType=BIGINT},",
            "#{item.entryTicketId,jdbcType=BIGINT}, #{item.rentTag,jdbcType=CHAR})",
            "</foreach>",
            "</script>"
    })
    int insertBatch(@Param("tradeTickets") List<TradeTicket> tradeTickets);

    @Update({
            "<script>",
            "update trade_ticket",
            "<set>",
            "state = #{state,jdbcType=VARCHAR}, ",
            "check_ticket_time = #{sysdate,jdbcType=TIMESTAMP}, ",
            "<if test=\"staffId != null\">ticket_collector = #{staffId,jdbcType=BIGINT}</if>",
            "</set>",
            "where ticket_id = #{ticketId,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByTicketId(@Param("ticketId") Long ticketId, @Param("state") String state,
                         @Param("sysdate") Date sysdate, @Param("staffId") Long staffId);

    @Select({
            "<script>",
            "select t.trade_id, v.ticket_id, v.ticket_no, v.ecard_no, v.ticket_type, v.pay_money price, v.create_time, ",
            "DATE_FORMAT(v.effect_date,\"%Y-%m-%d\") effect_date, v.start_segment, v.end_segment, v.start_time, v.end_time, ",
            "(select s.staff_name from staff s where s.staff_id = t.trade_staff_id) staff_name, v.state, v.service_id, v.field_id, ",
            "IF((select count(*) from print_log pl where pl.ticket_id = v.ticket_id and pl.state = '1') > 0, '1', '0') print_success_tag, ",
            "(select ticket_type_name a from ticket_type n where v.ticket_type = n.ticket_type_id) ticket_type_name, te.value group_buying_codes",
            "from trade t left join customer cu on t.cust_id = cu.cust_id left join trade_extra te on t.trade_id = te.trade_id and te.extra_key = 'group_buying_codes', trade_ticket v",
            "<where>",
            "and t.trade_id = v.trade_id",
            "and t.venue_id = v.venue_id",
            "and if(cu.cust_id is not null, cu.cust_state != '1', 1=1)",
            "and t.center_id = #{centerId,jdbcType=BIGINT}",
            "<if test=\"stateList.size &gt; 0\">and v.state in",
            "<foreach item=\"item\" collection=\"stateList\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            " #{item, jdbcType=VARCHAR}",
            "</foreach>",
            "</if>",
            "<if test=\"custId != null\">and t.cust_id = #{custId,jdbcType=BIGINT}</if>",
            "<if test=\"tradeId != null\">and t.trade_id = #{tradeId,jdbcType=BIGINT}</if>",
            "<if test=\"staffId != null\">and t.trade_staff_id = #{staffId,jdbcType=BIGINT}</if>",
            "<if test=\"createTime != null\">and t.accept_date &gt;= #{createTime,jdbcType=DATE} and t.accept_date &lt; date_add(#{createTime,jdbcType=DATE}, interval 1 day)</if>",
            "<if test=\"effectDate != null\">and v.effect_date = #{effectDate,jdbcType=DATE}</if>",
            "<if test=\"venueId != null and venueId != 0\">and t.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"printSuccessTag != null and printSuccessTag == 0\">and v.state = '2' and not exists (select * from print_log pl where pl.ticket_id = v.ticket_id and pl.state = '1')</if>",
            "<if test=\"printSuccessTag != null and printSuccessTag == 1\">and v.state = '2' and exists (select * from print_log pl where pl.ticket_id = v.ticket_id and pl.state = '1')</if>",
            "<if test=\"groupBuyingCode != null\">and find_in_set(#{groupBuyingCode,jdbcType=VARCHAR}, te.value)</if>",
            "order by v.create_time desc, v.ticket_id desc",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price", property = "price", jdbcType = JdbcType.BIGINT),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "staff_name", property = "staffName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "print_success_tag", property = "printSuccessTag", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_buying_codes", property = "groupBuyingCodes", jdbcType = JdbcType.VARCHAR)
    })
    List<TicketCompensateInfo> selectTicketCompensateInfo(Map<String, Object> param, RowBounds rowBounds);

    @Select({
            "select",
            "a.ticket_id, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.start_time, a.end_time, a.pay_money-IFNULL(discount,0) pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, ",
            "a.price_item, a.round, a.group_ticekt_id, a.group_tag,c.channel_name,a.player_num",
            "from (trade_ticket a,trade b) left join channel c on b.channel_id = c.channel_id",
            "where a.cust_id = #{custId,jdbcType=BIGINT} and a.venue_id = #{venueId,jdbcType=BIGINT} ",
            "and a.trade_id = b.trade_id ",//票来源不为协议占场 a.ticket_source_type != '3'
            "and (a.state in (0,2) ",//状态为未取票或者已取票
            "or (a.state = 3 and curdate() = date(b.accept_date) and b.trade_type_code = 14 ",
            "and EXISTS(SELECT p.product_id FROM product p where p.product_id = a.product_id and p.limit_tag = 1)))",
            "order by a.service_id, a.trade_id"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "channel_name", property = "channelName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<TicketInfo> selectCancelTicketByCustId(@Param("custId") Long custId, @Param("venueId") Long venueId);

    //取活动时间所有的有效的订单和未支付未失效的订单票信息
    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, ",
            "a.price_item, a.round, a.player_num",
            "from trade_ticket a,trade b",
            "<where> ",
            "<if test=\"custId != null\"> and b.cust_id = #{custId,jdbcType=BIGINT} </if>",
            "<if test=\"netUserId != null\"> and b.trade_id in ( select nt.trade_id from net_trade nt  where nt.net_user_id =  #{netUserId,jdbcType=BIGINT}) </if>",
            "<if test=\"phoneNum != null\"> and b.trade_id in (select fb.trade_id from field_book fb where fb.phone = #{phoneNum,jdbcType=VARCHAR} and fb.venue_id = a.venue_id and fb.service_id = a.service_id ) </if>",
            "and a.trade_id = b.trade_id",
            "<if test=\"tradeId != null\"> and a.trade_id != #{tradeId,jdbcType=BIGINT} </if>",
            "<if test=\"depositId != null\"> and a.deposit_id = #{depositId,jdbcType=BIGINT} </if>",
            //"and b.pay_state = '1'",
            "and ((b.pay_state = '0' AND TIMESTAMPDIFF(SECOND, b.expire_time, NOW()) &lt;0) or b.pay_state = '1')",
            "and a.effect_date = #{effectDate,jdbcType=DATE}",
            "and a.state in (0,2,3,9)",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.service_id = #{serviceId,jdbcType=BIGINT}",
            "and a.field_id is not null and a.field_id&lt;&gt;''",
            "</where>",
            "order by a.field_id,a.start_segment",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<TradeTicket> selectTicketsByEffectDate(@Param("custId") Long custId,
                                                @Param("netUserId") Long netUserId,
                                                @Param("effectDate") Date effectDate,
                                                @Param("serviceId") Long serviceId,
                                                @Param("venueId") Long venueId,
                                                @Param("phoneNum") String phoneNum,
                                                @Param("tradeId") Long tradeId,
                                                @Param("depositId") Long depositId);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, ",
            "a.price_item, a.round, a.player_num",
            "from trade_ticket a,trade b",
            "where",
            "b.cust_id = #{custId,jdbcType=BIGINT}",
            "and a.trade_id = b.trade_id",
            "<if test=\"tradeId != null\"> and a.trade_id != #{tradeId,jdbcType=BIGINT} </if>",
            "<if test=\"depositId != null\"> and a.deposit_id = #{depositId,jdbcType=BIGINT} </if>",
            //"and b.pay_state = '1'",
            "and ((b.pay_state = '0' AND TIMESTAMPDIFF(SECOND, b.expire_time, NOW()) &lt;0) or b.pay_state = '1')",
            "and a.effect_date = #{effectDate,jdbcType=DATE}",
            "and a.state in (0,2,3,9)",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.service_id = #{serviceId,jdbcType=BIGINT}",
            "and a.field_id is not null and a.field_id&lt;&gt;''",
            "union",
            "select",
            "a.ticket_id, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, ",
            "a.price_item, a.round, a.player_num",
            "from trade_ticket a,trade b",
            "where",
            "b.trade_id in ( select nt.trade_id from net_trade nt  where nt.net_user_id =  #{netUserId,jdbcType=BIGINT})",
            "and a.trade_id = b.trade_id",
            "<if test=\"tradeId != null\"> and a.trade_id != #{tradeId,jdbcType=BIGINT} </if>",
            "<if test=\"depositId != null\"> and a.deposit_id = #{depositId,jdbcType=BIGINT} </if>",
            //"and b.pay_state = '1'",
            "and ((b.pay_state = '0' AND TIMESTAMPDIFF(SECOND, b.expire_time, NOW()) &lt;0) or b.pay_state = '1')",
            "and a.effect_date = #{effectDate,jdbcType=DATE}",
            "and a.state in (0,2,3,9)",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.service_id = #{serviceId,jdbcType=BIGINT}",
            "and a.field_id is not null and a.field_id&lt;&gt;''",
            "order by field_id,start_segment",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<TradeTicket> selectAllTicketsByEffectDate(@Param("custId") Long custId,
                                                   @Param("netUserId") Long netUserId,
                                                   @Param("effectDate") Date effectDate,
                                                   @Param("serviceId") Long serviceId,
                                                   @Param("venueId") Long venueId,
                                                   @Param("tradeId") Long tradeId,
                                                   @Param("depositId") Long depositId);

    @Select({
            "select count(1) submit_times",
            "from trade_ticket a, trade b ",
            "where a.trade_id = b.trade_id ",
            "and (exists (select nt.trade_id from net_trade nt where nt.net_user_id =#{netUserId,jdbcType=BIGINT} and nt.trade_id = b.trade_id) ",
            "or exists (select nuc.venue_cust_id from net_user_cards nuc where nuc.net_user_id = #{netUserId,jdbcType=BIGINT} and b.cust_id = cast(nuc.venue_cust_id as unsigned))) ",
            "and (b.pay_state = '0' AND TIMESTAMPDIFF(SECOND, b.expire_time, NOW()) <0) ",
            "and a.effect_date = #{effectDate,jdbcType=DATE} ",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.service_id = #{serviceId,jdbcType=BIGINT}",
            "and a.field_id is not null and a.field_id<>''",
    })
    @Results({
            @Result(column = "submit_times", property = "submitTimes", jdbcType = JdbcType.INTEGER)
    })
    Integer selectSubmitTimes(@Param("netUserId") Long netUserId,
                              @Param("effectDate") Date effectDate,
                              @Param("serviceId") Long serviceId,
                              @Param("venueId") Long venueId);

    //查询次票票表信息，同时返回场馆名称和票表名称
    @Select({
            "select ifnull(dl.center_name, d.center_name) center_name, ifnull(bl.venue_name, b.venue_name) venue_name, ",
            "IFNULL(ifnull(cl.ticket_type_name ,c.ticket_type_name), e.product_name) ticket_type_name ,c.ticket_type_id, a.ticket_id, a.ticket_no, c.charge_mode,",
            "a.field_id,a.field_name,a.start_segment,a.end_segment,a.pay_money,a.discount,a.ecard_no,a.effect_date, a.group_tag, a.group_ticekt_id ",
            "from trade_ticket a left join ticket_type c on a.ticket_type=c.ticket_type_id",
            "LEFT JOIN trade t ON a.trade_id = t.trade_id",
            "left join venue b on t.venue_id=b.venue_id ",
            "left join ticket_type_lang cl on c.ticket_type_id = cl.ticket_type_id and cl.language=#{lang,jdbcType=VARCHAR}",
            "left join venue_lang bl on b.venue_id = bl.venue_id and bl.language=#{lang,jdbcType=VARCHAR} ",
            "LEFT JOIN center d ON t.center_id = d.center_id ",
            "left join center_lang dl on d.center_id = dl.center_id and dl.language=#{lang,jdbcType=VARCHAR}",
            "LEFT JOIN product e ON e.product_id = a.product_id",
            "where a.trade_id=#{tradeId,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column = "center_name", property = "centerName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "charge_mode", property = "chargeMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "group_ticekt_id", property = "groupTicketId", jdbcType = JdbcType.CHAR)
    })
    List<Map> selectTicketInfoByTradeId(@Param("tradeId") Long tradeId,
                                        @Param("lang") String lang);

    //查询场地票票表信息，同时返回场馆名称和票表名称
    @Select({
            "select d.center_name,b.venue_name ,c.field_type_name ,",
            "c.field_type ,a.ticket_id,a.ticket_no,a.field_id,a.field_name,a.start_segment,a.end_segment,a.pay_money,a.discount,a.ecard_no,a.effect_date",
            "from trade_ticket a ,venue b,field_type c,center d,field f",
            "where a.trade_id=#{tradeId,jdbcType=BIGINT} and a.venue_id = b.venue_id and a.field_id= f.field_id and f.field_type = c.field_type and b.center_id = d.center_id"
    })
    @Results({
            @Result(column = "center_name", property = "centerName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR)
    })
    List<Map> selectFieldTicketInfoByTradeId(@Param("tradeId") Long tradeId);

    //根据tradeId获取没有取消的票的数量
    @Select({
            "select count(1) as num",
            "from trade_ticket  where trade_id=#{tradeId,jdbcType=BIGINT} and state !='" + Constants.TicketState.CANCELED + "'"
    })
    int selectNotCancelTicketNum(@Param("tradeId") Long tradeId);

    //批量更新票表状态
    @Update({
            "<script>",
            "update trade_ticket",
            "set state =#{state,jdbcType=VARCHAR}",
            "where ticket_id in",
            "<foreach collection=\"lists\" item=\"list\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{list.ticketId}",
            "</foreach>",
            "</script>"
    })
    int batchUpdateState(@Param("lists") List lists, @Param("state") String state);

    //批量更新票表状态
    @Update({
            "<script>",
            "update trade_ticket",
            "set state =#{state,jdbcType=VARCHAR}",
            "where ticket_id in",
            "<foreach collection=\"lists\" item=\"ticketId\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{ticketId}",
            "</foreach>",
            "</script>"
    })
    int batchUpdateStateByTicketIds(@Param("lists") List lists, @Param("state") String state);

    /**
     * 查询trade_ticket，返回TicketInfo
     *
     * @param param
     * @return
     */
    @Select({
            "<script>",
            "select",
            "ticket_id, trade_id, service_id, venue_id, field_id, field_name, start_segment, end_segment, pay_money, discount, state, create_time, fetch_ticket_time, check_ticket_time, deposit_id, ticket_source_type, ticket_drawer, ticket_collector, ticket_cancel_person, check_mode, effect_date, ecard_no, cust_id, cust_name, product_id, ticket_type, price_item, round, remark, coupon_amount, cancel_trade_id, group_ticekt_id, group_tag, player_num",
            "from trade_ticket",
            "<where>",
            "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "<if test=\"tradeId != null\">and trade_id = #{tradeId,jdbcType=BIGINT}</if>",
            "<if test=\"serviceId != null\">and service_id = #{serviceId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldId != null\">and field_id = #{fieldId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldName != null\">and field_name = #{fieldName,jdbcType=VARCHAR}</if>",
            "<if test=\"startSegment != null\">and start_segment = #{startSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"endSegment != null\">and end_segment = #{endSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"payMoney != null\">and pay_money = #{payMoney,jdbcType=BIGINT}</if>",
            "<if test=\"discount != null\">and discount = #{discount,jdbcType=BIGINT}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"fetchTicketTime != null\">and fetch_ticket_time = #{fetchTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"checkTicketTime != null\">and check_ticket_time = #{checkTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"depositId != null\">and deposit_id = #{depositId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketSourceType != null\">and ticket_source_type = #{ticketSourceType,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketDrawer != null\">and ticket_drawer = #{ticketDrawer,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCollector != null\">and ticket_collector = #{ticketCollector,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCancelPerson != null\">and ticket_cancel_person = #{ticketCancelPerson,jdbcType=BIGINT}</if>",
            "<if test=\"checkMode != null\">and check_mode = #{checkMode,jdbcType=VARCHAR}</if>",
            "<if test=\"effectDate != null\">and effect_date = #{effectDate,jdbcType=DATE}</if>",
            "<if test=\"ecardNo != null\">and ecard_no = #{ecardNo,jdbcType=VARCHAR}</if>",
            "<if test=\"custId != null\">and cust_id = #{custId,jdbcType=BIGINT}</if>",
            "<if test=\"custName != null\">and cust_name = #{custName,jdbcType=VARCHAR}</if>",
            "<if test=\"productId != null\">and product_id = #{productId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketType != null\">and ticket_type = #{ticketType,jdbcType=BIGINT}</if>",
            "<if test=\"priceItem != null\">and price_item = #{priceItem,jdbcType=BIGINT}</if>",
            "<if test=\"round != null\">and round = #{round,jdbcType=VARCHAR}</if>",
            "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
            "<if test=\"couponAmount != null\">and coupon_amount = #{couponAmount,jdbcType=INTEGER}</if>",
            "<if test=\"cancelTradeId != null\">and cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTicektId != null\">and group_ticekt_id = #{groupTicektId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTag != null\">and group_tag = #{groupTag,jdbcType=CHAR}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<TicketInfo> selectTicketInfoByFields(TradeTicket param);

    /**
     * 根据 tradeId 查询 TicketPrintInfo
     *
     * @param tradeId
     * @return
     */
    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, a.end_segment, a.start_time, a.end_time, a.round,",
            "a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, a.deposit_id,",
            "a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, a.check_mode, ",
            "a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type,b.ticket_kind,a.remark,",
            "a.coupon_amount,a.group_ticekt_id,a.group_tag,a.player_num, b.ticket_type_name,",
            "(select ss.service_name from service ss where ss.service_id = a.service_id limit 1) service_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type=b.ticket_type_id ",
            "where trade_id=#{tradeId,jdbcType=BIGINT} ",
            "<if test=\"ticketId != null\">and a.ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_kind", property = "ticketKind", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_type_name", property = "ticketName", jdbcType = JdbcType.INTEGER),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR)
    })
    List<TicketPrintInfo> selectTicketPrintInfoByTradeId(@Param("tradeId") Long tradeId, @Param("ticketId") Long ticketId);

    //根据tradeId获取没有取消的票的数量
    @Select({
            "<script>",
            "select count(1) as num",
            "from trade_ticket ",
            "<where>",
            "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "<if test=\"tradeId != null\">and trade_id = #{tradeId,jdbcType=BIGINT}</if>",
            "<if test=\"serviceId != null\">and service_id = #{serviceId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldId != null\">and field_id = #{fieldId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldName != null\">and field_name = #{fieldName,jdbcType=VARCHAR}</if>",
            "<if test=\"startSegment != null\">and start_segment = #{startSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"endSegment != null\">and end_segment = #{endSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"payMoney != null\">and pay_money = #{payMoney,jdbcType=BIGINT}</if>",
            "<if test=\"discount != null\">and discount = #{discount,jdbcType=BIGINT}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"fetchTicketTime != null\">and fetch_ticket_time = #{fetchTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"checkTicketTime != null\">and check_ticket_time = #{checkTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"depositId != null\">and deposit_id = #{depositId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketSourceType != null\">and ticket_source_type = #{ticketSourceType,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketDrawer != null\">and ticket_drawer = #{ticketDrawer,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCollector != null\">and ticket_collector = #{ticketCollector,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCancelPerson != null\">and ticket_cancel_person = #{ticketCancelPerson,jdbcType=BIGINT}</if>",
            "<if test=\"checkMode != null\">and check_mode = #{checkMode,jdbcType=VARCHAR}</if>",
            "<if test=\"effectDate != null\">and effect_date = #{effectDate,jdbcType=DATE}</if>",
            "<if test=\"ecardNo != null\">and ecard_no = #{ecardNo,jdbcType=VARCHAR}</if>",
            "<if test=\"custId != null\">and cust_id = #{custId,jdbcType=BIGINT}</if>",
            "<if test=\"custName != null\">and cust_name = #{custName,jdbcType=VARCHAR}</if>",
            "<if test=\"productId != null\">and product_id = #{productId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketType != null\">and ticket_type = #{ticketType,jdbcType=BIGINT}</if>",
            "<if test=\"priceItem != null\">and price_item = #{priceItem,jdbcType=BIGINT}</if>",
            "<if test=\"round != null\">and round = #{round,jdbcType=VARCHAR}</if>",
            "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
            "<if test=\"couponAmount != null\">and coupon_amount = #{couponAmount,jdbcType=INTEGER}</if>",
            "<if test=\"cancelTradeId != null\">and cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTicektId != null\">and group_ticekt_id = #{groupTicektId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTag != null\">and group_tag = #{groupTag,jdbcType=CHAR}</if>",
            "<if test=\"promId != null\">and prom_id = #{promId,jdbcType=BIGINT}</if>",
            "<if test=\"psptId != null\">and pspt_id = #{psptId,jdbcType=VARCHAR}</if>",
            "</where>",
            "</script>"
    })
    int selectTicketNum(TradeTicket param);

    /**
     * 根据psptId，promId,ticketTypeId查看此人是否买过此票
     *
     * @param psptId
     * @param promId
     * @return
     */
    @Select({
            "select",
            "ticket_type, count(1) as num",
            "from trade_ticket",
            "where pspt_id= #{psptId,jdbcType=VARCHAR} and prom_id=#{promId,jdbcType=BIGINT}",
            "and create_time >= CURDATE() AND create_time < date_add(curdate(), interval 1 day)",
            "and state in('0','2','3')",
            "group by ticket_type"
    })
    @Results({
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "num", property = "num", jdbcType = JdbcType.INTEGER)
    })
    List<Map<String, Object>> countTicketsByPsptIdPromId(@Param("psptId") String psptId, @Param("promId") Long promId);

    /**
     * 根据tradeId查看此人是否买过此票
     *
     * @param tradeId
     * @return
     */
    @Select({
            "select",
            "prom_id, ticket_type, count(1) as num",
            "from trade_ticket",
            "where trade_id = #{tradeId,jdbcType=BIGINT} and prom_id is not NULL and field_id is NULL",
            "group by ticket_type, prom_id"
    })
    @Results({
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "num", property = "num", jdbcType = JdbcType.INTEGER)
    })
    List<Map<String, Object>> countTicketsByTradeId(Long tradeId);

    /**
     * 查询订单暂停已经生成的有效票的记录和退费金额
     * 此处主要是提供根据trade表的tradeIdb字段来查询相应的数据
     *
     * @param param
     * @return
     */
    @Select({
            "select",
            "a.ticket_id, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, ",
            "a.price_item, a.round, a.remark, a.coupon_amount, a.cancel_trade_id, a.group_ticekt_id, ",
            "a.group_tag, a.player_num",
            "from trade_ticket a, field_book b",
            "where a.state != '1'",
            "and if(b.cycle_trade_id is not null, b.cycle_trade_id = #{tradeId,jdbcType=BIGINT}, b.trade_id = #{tradeId,jdbcType=BIGINT})",
            "and a.trade_id = b.trade_id and a.field_id = #{fieldId,jdbcType=BIGINT} and a.start_segment=#{startSegment,jdbcType=BIGINT}",
            "and a.effect_date between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<TradeTicket> tradeTicketByTradeIdB(Map param);

    /**
     * Created by xuzeng on 2015/12/04.
     * 根据netUserId获取票情况(根据tradeId分组)
     *
     * @param netUserId
     * @param date
     * @return
     */
    @Select({
            "select ttf.money cashPledge,a.state,a.pay_money,a.ticket_id, a.ticket_no, a.discount, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, d.venue_name, a.field_id, d.icon_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join trade_ticket_foregift ttf on a.ticket_id = ttf.ticket_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id, venue d, net_trade f ",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.group_tag = '0'",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} ",
            "union",
            "select ttf.money cashPledge,a.state,a.pay_money,a.ticket_id, a.ticket_no, a.discount, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, d.venue_name, a.field_id, d.icon_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join trade_ticket_foregift ttf on a.ticket_id = ttf.ticket_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id, venue d,  net_user_cards e",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.group_tag = '0'",
            "and a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId, jdbcType=BIGINT} and e.status = '1'",
            "union",
            "select ttf.money cashPledge,a.state,a.pay_money,a.ticket_id, a.ticket_no, a.discount, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, d.venue_name, a.field_id, d.icon_name",
            "from trade_ticket_user f,trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join trade_ticket_foregift ttf on a.ticket_id = ttf.ticket_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id, venue d,  net_trade e ",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.trade_id = e.trade_id",
            "and f.net_user_id = #{netUserId, jdbcType=BIGINT}",
            "and f.ticket_id =a.ticket_id ",
            "and a.group_tag = '0'",
            "and f.state<> 0",
            "and f.net_user_id <> e.net_user_id ",
            "order by effect_date, start_segment",
    })
    @Results({
            @Result(column = "cashPledge", property = "cashPledge", jdbcType = JdbcType.INTEGER),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR)
    })
    List<Map> selectTicketsByNetUserId(@Param("netUserId") Long netUserId,
                                       @Param("date") Date date);

    /**
     * 汇泉湾专用
     * 过滤不可入馆的票（已进出馆三次）已出馆、已过期、之前的票（当天入馆）
     * 根据netUserId获取票情况(根据tradeId分组)
     *
     * @param netUserId
     * @param date
     * @return
     */
    @Select({
            "<script>",
            "select * from (",
            "select ttf.money cashPledge,a.state,a.pay_money,a.ticket_id, a.ticket_no, a.discount, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, d.venue_name, a.field_id, d.icon_name, a.create_time",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join trade_ticket_foregift ttf on a.ticket_id = ttf.ticket_id",
            "left join entry_info ei on a.ticket_id = ei.ticket_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id, venue d, net_trade f ",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} ",
            "and ((a.check_ticket_time &gt;= #{date, jdbcType=DATE} and a.check_ticket_time &lt; DATE_ADD(#{date, jdbcType=DATE},INTERVAL 1 DAY)) or a.check_ticket_time is null) ",
            "and ((ifnull(a.field_id,(ei.enter_times &lt; 3 or (ei.enter_times = 3 and ei.enter_state = '1'))) and ei.enter_in_time &gt;= #{date, jdbcType=DATE} and ei.enter_in_time &lt; DATE_ADD(#{date, jdbcType=DATE},INTERVAL 1 DAY)) or ei.ticket_id is null)",//#1008325
            "union",
            "select ttf.money cashPledge,a.state,a.pay_money,a.ticket_id, a.ticket_no, a.discount, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, d.venue_name, a.field_id, d.icon_name, a.create_time",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join trade_ticket_foregift ttf on a.ticket_id = ttf.ticket_id",
            "left join entry_info ei on a.ticket_id = ei.ticket_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id, venue d,  net_user_cards e",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId, jdbcType=BIGINT} and e.status = '1'",
            "and ((a.check_ticket_time &gt;= #{date, jdbcType=DATE} and a.check_ticket_time &lt; DATE_ADD(#{date, jdbcType=DATE},INTERVAL 1 DAY)) or a.check_ticket_time is null) ",
            "and ((ifnull(a.field_id,(ei.enter_times &lt; 3 or (ei.enter_times = 3 and ei.enter_state = '1'))) and ei.enter_in_time &gt;= #{date, jdbcType=DATE} and ei.enter_in_time &lt; DATE_ADD(#{date, jdbcType=DATE},INTERVAL 1 DAY)) or ei.ticket_id is null)",
            "union",
            "select ttf.money cashPledge,a.state,a.pay_money,a.ticket_id, a.ticket_no, a.discount, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, d.venue_name, a.field_id, d.icon_name, a.create_time",
            "from trade_ticket_user f,trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join trade_ticket_foregift ttf on a.ticket_id = ttf.ticket_id",
            "left join entry_info ei on a.ticket_id = ei.ticket_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id, venue d,  net_trade e ",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.trade_id = e.trade_id",
            "and f.net_user_id = #{netUserId, jdbcType=BIGINT}",
            "and f.ticket_id =a.ticket_id ",
            "and f.state != 0",
            "and f.net_user_id != e.net_user_id ",
            "and ((a.check_ticket_time &gt;= #{date, jdbcType=DATE} and a.check_ticket_time &lt; DATE_ADD(#{date, jdbcType=DATE},INTERVAL 1 DAY)) or a.check_ticket_time is null) ",
            "and ((ifnull(a.field_id,(ei.enter_times &lt; 3 or (ei.enter_times = 3 and ei.enter_state = '1'))) and ei.enter_in_time &gt;= #{date, jdbcType=DATE} and ei.enter_in_time &lt; DATE_ADD(#{date, jdbcType=DATE},INTERVAL 1 DAY)) or ei.ticket_id is null)",//#1008325
            ") un ",
            "<if test=\"hqwWechatTicketOrderTag != null and hqwWechatTicketOrderTag == 1\"> order by un.create_time desc</if>",
            "<if test=\"hqwWechatTicketOrderTag != null and hqwWechatTicketOrderTag == 0\"> order by un.effect_date, un.start_segment</if>",
            "</script>"
    })
    @Results({
            @Result(column = "cashPledge", property = "cashPledge", jdbcType = JdbcType.INTEGER),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.DATE),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR)
    })
    List<Map> selectAccessibleTicketsByNetUserId(@Param("netUserId") Long netUserId, @Param("date") Date date,@Param("hqwWechatTicketOrderTag") String hqwWechatTicketOrderTag);

    @Select({
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,",
            "if(b.ticket_type_name is not null,b.ticket_type_name, a.field_name) as ticket_type_name, ",
            "a.service_id, d.venue_id, d.venue_name, a.field_id, d.icon_name, a.state, ",
            "(select ss.service_name from service ss where ss.service_id = a.service_id) service_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            ", venue d, net_trade f ",
            "where a.effect_date <= #{date, jdbcType=DATE} ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} ",
            "union",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,",
            "if(b.ticket_type_name is not null,b.ticket_type_name, a.field_name) as ticket_type_name, ",
            "a.service_id, d.venue_id, d.venue_name, a.field_id, d.icon_name, a.state, ",
            "(select ss.service_name from service ss where ss.service_id = a.service_id) service_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            ", venue d,  net_user_cards e",
            "where a.effect_date <= #{date, jdbcType=DATE} ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end >= #{date, jdbcType=DATE}",
            "and a.state in ('0','3')",
            "and d.venue_id = a.venue_id",
            "and a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId, jdbcType=BIGINT} and e.status = '1'",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR)
    })
    List<Map> selectEnterHallTickets(@Param("netUserId") Long netUserId,
                                     @Param("date") Date date);

    /**
     * 根据netUserId获取票情况(不根据tradeId分组)
     *
     * @param netUserId
     * @param extendTicketTypeList
     * @return
     */
    @Select({
            "<script>",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ss.service_name service_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d, net_trade f,service ss, trade t ",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate()",
            "and (a.state = '0' or a.state = '2' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and d.venue_id = a.venue_id and a.group_tag = '0'",
            "and ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} and a.trade_id = t.trade_id",
            "<if test=\"channelId != null\">and t.channel_id = #{channelId, jdbcType=BIGINT}</if>",
            "<if test=\"excludeTicketTypes != null\">",
            "and a.ticket_type not in ",
            "<foreach collection=\"excludeTicketTypes\" item=\"item\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            "</if>",
            "union",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ss.service_name service_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d,  net_user_cards e, service ss, trade t",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate()",
            "and (a.state = '0' or a.state = '2' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and d.venue_id = a.venue_id and a.group_tag = '0'",
            "and ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')",
            "and a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId, jdbcType=BIGINT} and e.status = '1' and a.trade_id = t.trade_id",
            "<if test=\"channelId != null\">and t.channel_id = #{channelId, jdbcType=BIGINT}</if>",
            "<if test=\"excludeTicketTypes != null\">",
            "and IF(a.ticket_type is null, 1=1 ,a.ticket_type not in ",
            "<foreach collection=\"excludeTicketTypes\" item=\"item\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            ")",
            "</if>",
            "union",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ",
            "(select ss.service_name from service ss where ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')) service_name",
            "from trade_ticket_user f,trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d,",
            //" net_trade e,", // 先去掉吧 前台买的分享票和后台发起的约球都查不到
            " service ss, trade t ",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate()",
            "and (a.state = '0' or a.state = '2' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate()))",
            "and d.venue_id = a.venue_id",
            //"and a.trade_id = e.trade_id",
            "and f.net_user_id = #{netUserId, jdbcType=BIGINT}",
            "and f.ticket_id =a.ticket_id ",
            "and a.group_tag = '0'",
            "and ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')",
            "and f.state &lt;&gt; 0",
            //"and f.net_user_id &lt;&gt; e.net_user_id ",
            "and a.trade_id = t.trade_id",
            "<if test=\"channelId != null\">and t.channel_id = #{channelId, jdbcType=BIGINT}</if>",
            "<if test=\"excludeTicketTypes != null\">",
            "and a.ticket_type not in ",
            "<foreach collection=\"excludeTicketTypes\" item=\"item\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            "</if>",
            "order by effect_date, if(isnull(ticket_type_id),0,1), start_segment",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pic_url", property = "picUrl", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_times", property = "enterTimes", jdbcType = JdbcType.INTEGER),
    })
    List<DataMap> selectTicketListByNetUserId(@Param("netUserId") Long netUserId, @Param("channelId") Long channelId, @Param("excludeTicketTypes") List<Long> excludeTicketTypes);

    /**
     * 根据netUserId获取票情况(不根据tradeId分组)
     *
     * @param netUserId
     * @return
     */
    @Select({
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ff.field_no from field ff where ff.field_id = a.field_id) field_no,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ",
            "(select ss.service_name from service ss where ss.service_id = a.service_id) service_name,",
            "(select k.key_no from key_info k where ei.key_id = k.key_id) key_no",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d, net_trade f , key_info k",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= curdate()",
            "and (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and d.venue_id = a.venue_id",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} and a.venue_id = #{venueId, jdbcType=BIGINT}",
            "union",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ff.field_no from field ff where ff.field_id = a.field_id) field_no,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ",
            "(select ss.service_name from service ss where ss.service_id = a.service_id) service_name,",
            "(select k.key_no from key_info k where ei.key_id = k.key_id) key_no",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d,  net_user_cards e, key_info k",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= curdate()",
            "and (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and d.venue_id = a.venue_id",
            "and a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId, jdbcType=BIGINT} and e.status = '1' and a.venue_id = #{venueId, jdbcType=BIGINT}",
            "union",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ff.field_no from field ff where ff.field_id = a.field_id) field_no,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ",
            "(select ss.service_name from service ss where ss.service_id = a.service_id) service_name,",
            "(select k.key_no from key_info k where ei.key_id = k.key_id) key_no",
            "from trade_ticket_user f,trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d, net_trade e , key_info k",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= curdate()",
            "and (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate()))",
            "and d.venue_id = a.venue_id",
            "and a.trade_id = e.trade_id",
            "and f.net_user_id = #{netUserId, jdbcType=BIGINT}",
            "and a.venue_id = #{venueId, jdbcType=BIGINT}",
            "and f.ticket_id =a.ticket_id ",
            "and f.state<> 0",
            "and f.net_user_id <> e.net_user_id ",
            "order by effect_date, if(isnull(ticket_type_id),0,1), start_segment",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_no", property = "fieldNo", jdbcType = JdbcType.INTEGER),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pic_url", property = "picUrl", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "key_no", property = "keyNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_times", property = "enterTimes", jdbcType = JdbcType.INTEGER),
    })
    List<DataMap> selectTicketListByNetUserIdForVenue(@Param("netUserId") Long netUserId, @Param("venueId") Long venueId);

    /**
     * 根据netUserId获取票情况(不根据tradeId分组)
     *
     * @param netUserId
     * @return
     */
    @Select({
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ff.field_no from field ff where ff.field_id = a.field_id) field_no,",
            "(select ft.field_type from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ss.service_name service_name,",
            "(select cc.chamber_id from chamber cc where a.ticket_id = cc.ticket_id and cc.status = '1' limit 1) chamber_id",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d, net_trade f,service ss ",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= curdate()",
            "and (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and d.venue_id = a.venue_id and a.group_tag = '0'",
            "and ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} ",
            "and exists(select 1 from venue_param st where st.param_code = 'field_ticket_share_tag' and st.venue_id = a.venue_id and st.param_value = '1')" ,
            "and exists(select 1 from field f,field_type_attr pt where f.field_type = pt.field_type_id and f.field_id = a.field_id and pt.attr_code = 'playAA_tag' and pt.attr_value='1' )",
            "union",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            "b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            "(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            "(select ff.field_no from field ff where ff.field_id = a.field_id) field_no,",
            "(select ft.field_type from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type,",
            "(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            "(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ss.service_name service_name,",
            "(select cc.chamber_id from chamber cc where a.ticket_id = cc.ticket_id and cc.status = '1' limit 1) chamber_id",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d,  net_user_cards e, service ss",
            "where case when a.expire_date is not null then a.expire_date else a.effect_date end >= curdate()",
            "and (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and d.venue_id = a.venue_id and a.group_tag = '0'",
            "and ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')",
            "and a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId, jdbcType=BIGINT} and e.status = '1'",
            "and exists(select 1 from venue_param st where st.param_code = 'field_ticket_share_tag' and st.venue_id = a.venue_id and st.param_value = '1')" ,
            "and exists(select 1 from field f,field_type_attr pt where f.field_type = pt.field_type_id and f.field_id = a.field_id and pt.attr_code = 'playAA_tag' and pt.attr_value='1' )",
            //"union",
            //"select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.start_time,a.end_time,a.trade_id,",
            //"if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.venue_id, a.state, ",
            //"b.ticket_type_id, d.venue_name, a.field_id, d.icon_name, a.player_num, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times,",
            //"(select ff.field_name from field ff where ff.field_id = a.field_id) field_name,",
            //"(select ff.field_no from field ff where ff.field_id = a.field_id) field_no,",
            //"(select ft.field_type from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type,",
            //"(select ft.field_type_name from field ff, field_type ft where ff.field_id = a.field_id and ff.field_type = ft.field_type) field_type_name,",
            //"(select ss.pic_url from service ss where ss.service_id = a.service_id) pic_url, ",
            //"(select ss.service_name from service ss where ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')) service_name,",
            //"(select ifnull(group_concat(cc.chamber_id), '') from chamber cc where a.ticket_id = cc.ticket_id) chamber_id",
            //"from trade_ticket_user f,trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            //"left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            //"left join entry_info ei on a.ticket_id = ei.ticket_id, venue d, net_trade e,service ss ",
            //"where case when a.expire_date is not null then a.expire_date else a.effect_date end >= curdate()",
            //"and (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate()))",
            //"and d.venue_id = a.venue_id",
            //"and a.trade_id = e.trade_id",
            //"and f.net_user_id = #{netUserId, jdbcType=BIGINT}",
            //"and f.ticket_id =a.ticket_id ",
            //"and a.group_tag = '0'",
            //"and ss.service_id = a.service_id and ss.service_name not like concat('%', '更衣室', '%')",
            //"and f.state<> 0",
            //"and f.net_user_id <> e.net_user_id ",
            //"order by effect_date, if(isnull(ticket_type_id),0,1), start_segment",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_no", property = "fieldNo", jdbcType = JdbcType.INTEGER),
            @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pic_url", property = "picUrl", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_times", property = "enterTimes", jdbcType = JdbcType.INTEGER),
            @Result(column = "chamber_id", property = "chamberId", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectTicketListByNetUserIdWithFriend(@Param("netUserId") Long netUserId);

    /**
     * 根据netUserId获取票情况(不根据tradeId分组)
     *
     * @param netUserId
     * @return
     */
    @Select({
            "<script>",
            "select a.trade_id, a.ticket_id, a.ticket_no, a.venue_id, a.effect_date, a.expire_date ,a.start_segment,a.end_segment, ",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.group_tag, a.group_ticekt_id, b.ticket_type_id, b.ticket_kind, d.venue_name, d.icon_name, f.net_user_id, ",
            "g.id trade_ticket_user_id, g.net_user_id sign_net_user_id, g.use_cust_id, g.signature_name, g.child_name, g.child_age, g.phone, g.pspt_no, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times ",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id ",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join trade_ticket_user g on a.ticket_id = g.ticket_id and g.state ='1' ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d, net_trade f ",
            "<where>",
            "a.effect_date &lt;= curdate() ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate() ",
            "and (ei.enter_out_time is null or ei.enter_out_time between curdate() and date_add(curdate(), interval 1 day))",
            "and a.state in ('0','2','3') ",
            "and d.venue_id = a.venue_id ",
            "<if test=\"playProjectTag != null\">and ifnull(b.ticket_kind, 0) = '8' </if>",
            "<if test=\"playProjectTag == null\">and ifnull(b.ticket_kind, 0) != '8' </if>",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} ",
            "and a.field_id is null ",
            "and a.entry_ticket_id is null ",
            "and (ei.ticket_id is null or ei.enter_in_time between curdate() and date_add(curdate(), interval 1 day))",
            "and not exists (select 1 from trade_ticket tt where tt.group_tag = '0' and a.ticket_id = tt.group_ticekt_id and tt.entry_ticket_id is not null)",
            "</where>",
            "union ",
            "select a.trade_id, a.ticket_id, a.ticket_no, a.venue_id, a.effect_date, a.expire_date ,a.start_segment,a.end_segment, ",
            "if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name) as ticket_type_name, a.service_id, a.group_tag, a.group_ticekt_id, b.ticket_type_id, b.ticket_kind, d.venue_name, d.icon_name, e.net_user_id, ",
            "g.id trade_ticket_user_id, g.net_user_id sign_net_user_id, g.use_cust_id, g.signature_name, g.child_name, g.child_age, g.phone, g.pspt_no, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times ",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id ",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join net_trade f on a.trade_id = f.trade_id ",
            "left join trade_ticket_user g on a.ticket_id = g.ticket_id and g.state ='1' ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d,  net_user_cards e ",
            "<where>",
            "a.effect_date &lt;= curdate() ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate() ",
            "and (ei.enter_out_time is null or ei.enter_out_time between curdate() and date_add(curdate(), interval 1 day))",
            "and a.state in ('0','2','3') ",
            "and d.venue_id = a.venue_id ",
            "<if test=\"playProjectTag != null\">and ifnull(b.ticket_kind,0) = '8' </if>",
            "<if test=\"playProjectTag == null\">and ifnull(b.ticket_kind,0) != '8' </if>",
            "and a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId, jdbcType=BIGINT} and e.status = '1' ",
            "and a.field_id is null ",
            "and a.entry_ticket_id is null ",
            "and (ei.ticket_id is null or ei.enter_in_time between curdate() and date_add(curdate(), interval 1 day))",
            "and not exists (select 1 from trade_ticket tt where tt.group_tag = '0' and a.ticket_id = tt.group_ticekt_id and tt.entry_ticket_id is not null)",
            "</where>",
            "union ",
            "select aa.trade_id, aa.ticket_id, aa.ticket_no, aa.venue_id, aa.effect_date, aa.expire_date ,aa.start_segment,aa.end_segment, ",
            "if(aa.group_tag='1',if(bb.ticket_type_name is not null,bb.ticket_type_name,cc.value_name), if(b.ticket_type_name  is not null,b.ticket_type_name,c.value_name)) as ticket_type_name,",
            "aa.service_id, aa.group_tag, aa.group_ticekt_id, b.ticket_type_id, b.ticket_kind, d.venue_name, d.icon_name, null, ",
            "g.id trade_ticket_user_id, g.net_user_id sign_net_user_id, g.use_cust_id, g.signature_name, g.child_name, g.child_age, g.phone, g.pspt_no, ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times ",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id ",
            "left join static_param c on attr_code = 'ticket_name' and c.attr_value = a.service_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, trade_ticket aa ",
            "left join ticket_type bb on aa.ticket_type = bb.ticket_type_id",
            "left join static_param cc on attr_code = 'ticket_name' and cc.attr_value = aa.service_id,",
            "trade_ticket_user g, venue d ",
            "<where>",
            "a.effect_date &lt;= curdate() ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate() ",
            "and (ei.enter_out_time is null or ei.enter_out_time between curdate() and date_add(curdate(), interval 1 day))",
            "and a.state in ('0','2','3') ",
            "and d.venue_id = a.venue_id ",
            "and a.field_id is null ",
            "and a.entry_ticket_id is null ",
            "and (ei.ticket_id is null or ei.enter_in_time between curdate() and date_add(curdate(), interval 1 day))",
            "and not exists (select 1 from trade_ticket tt where tt.group_tag = '0' and a.ticket_id = tt.group_ticekt_id and tt.entry_ticket_id is not null)",
            "and a.ticket_id = g.ticket_id ",
            "and (a.ticket_id = aa.ticket_id or a.group_ticekt_id = aa.ticket_id) ",
            "and g.state = '1' ",
            "<if test=\"playProjectTag != null\">and ifnull(b.ticket_kind,0) = '8' </if>",
            "<if test=\"playProjectTag == null\">and ifnull(b.ticket_kind,0) != '8' </if>",
            "and g.net_user_id = #{netUserId, jdbcType=BIGINT} ",
            "and a.trade_id not in(select f.trade_id from net_trade f where f.net_user_id = g.net_user_id) ",
            "and ifnull(a.cust_id, 0) not in(select e.venue_cust_id from net_user_cards e where e.net_user_id = g.net_user_id) ",
            "</where>",
            "order by if(enter_state = '1', '-1', enter_state), trade_id desc, group_tag ",
            "</script>",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "group_ticekt_id", property = "groupTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_kind", property = "ticketKind", jdbcType = JdbcType.CHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_ticket_user_id", property = "tradeTicketUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "sign_net_user_id", property = "signNetUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "use_cust_id", property = "useCustId", jdbcType = JdbcType.BIGINT),
            @Result(column = "signature_name", property = "signatureName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "child_name", property = "childName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "child_age", property = "childAge", jdbcType = JdbcType.INTEGER),
            @Result(column = "phone", property = "phone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_no", property = "psptNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_timeout", property = "ticketTimeout", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_times", property = "enterTimes", jdbcType = JdbcType.INTEGER),
    })
    List<DataMap> selectEnterHallTicketList(@Param("netUserId") Long netUserId,
                                            @Param("playProjectTag") String playProjectTag);

    @Select({
            "select ",
            "a.trade_id, a.ticket_id, a.ticket_no, a.venue_id, a.effect_date, a.expire_date ,a.start_segment,a.end_segment, a.player_num,",
            "ifnull(bl.ticket_type_name, b.ticket_type_name) ticket_type_name, (select ifnull(ffl.field_name, ff.field_name) from ",
            "field ff left join field_lang ffl on ff.field_id = ffl.field_id and ffl.language=#{lang,jdbcType=VARCHAR} where ff.field_id = a.field_id) field_name,",
            "a.service_id, a.group_tag, a.group_ticekt_id, b.ticket_type_id, dl.venue_name, d.icon_name, f.net_user_id, d.center_id,",
            "ei.enter_in_time, ei.last_paid_time, a.field_id, bl.remark, b.remark ticket_remark",
            "from trade_ticket a left join (ticket_type b left join ticket_type_lang bl on b.ticket_type_id = bl.ticket_type_id and bl.language=#{lang,jdbcType=VARCHAR}) on a.ticket_type = b.ticket_type_id  ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id ",
            "left join (venue d left join venue_lang dl on d.venue_id = dl.venue_id and dl.language=#{lang,jdbcType=VARCHAR}) on d.venue_id = a.venue_id ",
            "left join net_trade f on a.trade_id = f.trade_id ",
            "where a.ticket_id = #{ticketIdOrNo,jdbcType=VARCHAR} or a.ticket_no = #{ticketIdOrNo,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "group_ticekt_id", property = "groupTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_remark", property = "ticketRemark", jdbcType = JdbcType.VARCHAR)
    })
    DataMap selectTicketDetailsLang(@Param("ticketIdOrNo") String ticketIdOrNo,
                                    @Param("lang") String lang);


    @Select({
            "select ",
            "b.ticket_id continue_ticket_id, b.service_id, b.entry_trade_id, b.key_id, b.key_bind_time, b.key_unbind_time, ",
            "b.venue_id, b.enter_out_time, b.state, b.trade_id, b.enter_state, c.key_no, a.start_time, a.end_time",
            "from trade_ticket a, entry_info b left join key_info c on b.key_id = c.key_id and b.venue_id = c.venue_id ",
            "where a.ticket_id = b.ticket_id ",
            "and (a.ticket_id = #{ticketId,jdbcType=BIGINT} or a.entry_ticket_id = #{ticketId,jdbcType=BIGINT}) ",
            "order by b.enter_in_time desc ",
            "limit 1 "
    })
    @Results({
            @Result(column = "continue_ticket_id", property = "continueTicketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "entry_trade_id", property = "entryTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "key_id", property = "keyId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "key_bind_time", property = "keyBindTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "key_unbind_time", property = "keyUnbindTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_out_time", property = "enterOutTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "key_no", property = "keyNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
    })
    Map selectEntryInfo(@Param("ticketId") Long ticketId);

    @Select({
            "select sum(tta.attr_value) ticket_timeout ",
            "from ticket_type_attr tta, trade_ticket tt ",
            "where tta.ticket_type_id = tt.ticket_type ",
            "and tt.state in ('0', '2', '3')",
            "and (tt.ticket_id = #{ticketId,jdbcType=BIGINT} or tt.entry_ticket_id = #{ticketId,jdbcType=BIGINT}) ",
            "and tta.attr_code = 'ticket_type_timeout' "
    })
    @Results({
            @Result(column = "ticket_timeout", property = "ticketTimeout", jdbcType = JdbcType.INTEGER),
    })
    Integer selectTicketTypeTimeout(@Param("ticketId") Long ticketId);

    @Select({
            "select sum(tta.attr_value) buffer_time ",
            "from ticket_type_attr tta, trade_ticket tt ",
            "where tta.ticket_type_id = tt.ticket_type ",
            "and tt.state in ('0', '2', '3')",
            "and (tt.ticket_id = #{ticketId,jdbcType=BIGINT} or tt.entry_ticket_id = #{ticketId,jdbcType=BIGINT}) ",
            "and tta.attr_code = 'buffer_time' "
    })
    @Results({
            @Result(column = "buffer_time", property = "bufferTime", jdbcType = JdbcType.INTEGER),
    })
    Integer selectTicketTypeBufferTime(@Param("ticketId") Long ticketId);

    @Select({
            "<script>",
            "select a.trade_id, a.ticket_id, a.ticket_no, a.venue_id, a.service_id, b.id trade_ticket_user_id ",
            "from trade_ticket a ",
            "left join trade_ticket_user b on a.ticket_id = b.ticket_id ",
            "and b.state = '1' ",
            "where (a.ticket_id = #{ticketId,jdbcType=BIGINT} or a.group_ticekt_id = #{ticketId,jdbcType=BIGINT}) ",
            "and a.group_tag = '0' ",
            "and a.effect_date &lt;= curdate() ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate() ",
            "<if test=\"validTicketTag != null\">and b.id is null</if>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_ticket_user_id", property = "tradeTicketUserId", jdbcType = JdbcType.BIGINT)
    })
    List<Map> selectTradeTicketUserInfo(@Param("ticketId") Long ticketId,
                                        @Param("validTicketTag") String validTicketTag);

    @Select({
            "<script>",
            "select a.trade_id, a.ticket_id, a.ticket_no, a.venue_id, a.service_id, a.ticket_type, b.id trade_ticket_user_id, ",
            "b.net_user_id, b.use_cust_id, b.signature_name, b.child_name, b.child_age, b.phone, b.pspt_no, c.enter_in_time, c.enter_out_time, c.enter_state, c.enter_times, ",
            "if(c.enter_out_time is null ,0 , TIMESTAMPDIFF(MINUTE,c.enter_in_time,now())) total_enter_minutes",
            "from trade_ticket a, trade_ticket_user b, entry_info c ",
            "where a.ticket_id = b.ticket_id ",
            "and b.state = '1' ",
            "and (a.ticket_id = #{ticketId,jdbcType=BIGINT} or a.group_ticekt_id = #{ticketId,jdbcType=BIGINT}) ",
            "and a.group_tag = '0' ",
            "and a.ticket_id = c.ticket_id ",
            "and c.enter_times is not null ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_ticket_user_id", property = "tradeTicketUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "use_cust_id", property = "useCustId", jdbcType = JdbcType.BIGINT),
            @Result(column = "signature_name", property = "signatureName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "child_name", property = "childName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "child_age", property = "childAge", jdbcType = JdbcType.INTEGER),
            @Result(column = "phone", property = "phone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_no", property = "psptNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_out_time", property = "enterOutTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "enter_times", property = "enterTimes", jdbcType = JdbcType.INTEGER),
            @Result(column = "total_enter_minutes", property = "totalEnterMinutes", jdbcType = JdbcType.INTEGER)
    })
    List<Map> selectContinueUsersByTicketId(@Param("ticketId") Long ticketId);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, (a.end_segment+1) end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, a.check_mode, a.effect_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount, a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price, a.player_num",
            "from trade_ticket a left join trade b on a.trade_id = b.trade_id",
            "<where>",
            "and a.trade_id = #{tradeId,jdbcType=BIGINT}",
            "and (a.state = '0' or a.state = '2' or a.state = '9')",
            "and if(b.trade_type_code = '53', a.ticket_id in (select c.ticket_id from fixed_occupy_conflict c where a.trade_id = c.conflict_trade_id and (a.effect_date = c.occupy_date or a.ticket_id = c.ticket_id)), 1=1)",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<Map<String, Object>> selectForHandelConflict(@Param("tradeId") Long tradeId);

    //查看这个订单这个时段这个场地的一张ticekt,由于会出现同一个tradeId对应多个ticekt的关系(活动占场解决冲突会出现),所以添加了state判断
    @Select({
            "select",
            "ticket_id, trade_id, service_id, venue_id, field_id, field_name, start_segment, ",
            "end_segment, pay_money, discount, state, create_time, fetch_ticket_time, check_ticket_time, ",
            "deposit_id, ticket_source_type, ticket_drawer, ticket_collector, ticket_cancel_person, ",
            "check_mode, effect_date, expire_date, ecard_no, cust_id, cust_name, product_id, ",
            "ticket_type, price_item, round, remark, coupon_amount, cancel_trade_id, group_ticekt_id, ",
            "group_tag, prom_id, pspt_id, full_tag, regular_price, player_num",
            "from trade_ticket",
            "where trade_id = #{tradeId,jdbcType=BIGINT}",
            "and state != '1'",
            "and #{segment,jdbcType=DECIMAL} between start_segment and end_segment"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<TicketInfo> selectTicketInfoByTradeIdFieldIdSegment(@Param("tradeId") Long tradeId, @Param("segment") Short segment);

    //计算某人某天已经买了某种票多少张
    @Select({
            "select count(1)",
            "from trade_ticket a",
            "where a.effect_date = #{effectDate,jdbcType=DATE} ",
            "and a.cust_id = #{custId,jdbcType=BIGINT} and a.ticket_type = #{ticketType,jdbcType=BIGINT} and a.state !='1'"
    })
    int countByNum(@Param("effectDate") Date effectDate, @Param("custId") Long custId, @Param("ticketType") Long ticketType);

    /**
     * 用于场地预定查询
     *
     * @param fieldType
     * @param mobileNum
     * @param startDate
     * @param endDate
     * @param venueId
     * @return
     */
    @Select({
            "<script>",
            "select * from (",
            "select a.ticket_id ticket_id, a.ticket_no, ifnull(nullif(a.cust_name,''), d.cust_name) cust_name, a.ecard_no ecard_no, a.effect_date effect_date, a.expire_date expire_date, a.ticket_source_type ticket_source_type,",
            "a.start_segment start_segment, a.end_segment end_segment, a.state state, a.pay_money pay_money, a.discount discount,",
            "b.field_name field_name, b.field_no field_no,",
            "c.field_type_name field_type_name,",
            "e.expire_time trade_expire_time, e.subscribe_state subscribe_state, e.channel_id channel_id, e.trade_id trade_id,",
            "ifnull((select fb.phone from field_book fb where fb.trade_id = a.trade_id limit 1),d.contact_phone) contact_phone,",
            "f.coach_name coach_name, e.accept_date, e.remark",
            "from trade_ticket a left join trade e on a.trade_id = e.trade_id left join customer d on a.cust_id = d.cust_id left join coach f on a.coach_id = f.coach_id, field b, field_type c",
            "where a.field_id = b.field_id",
            "and b.field_type = c.field_type",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.state != '1'",
            "and (e.subscribe_state = '1' or ( e.subscribe_state = '0' and now() &lt; e.expire_time or e.expire_time is null))",
            "<if test=\"startDate != null\">and a.effect_date &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and a.effect_date &lt;= #{endDate,jdbcType=DATE}</if>",
            "<if test=\"fieldType != null\">and b.field_type = #{fieldType,jdbcType=BIGINT}</if>",
            "<if test=\"fieldNoList.size &gt;0\">and b.field_no in " +
                    " <foreach item=\"fieldNo\" collection=\"fieldNoList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
                    " #{fieldNo, jdbcType=INTEGER}" +
                    "</foreach>  " +
                    "</if>",
            "<if test=\"ecardNo != null\">and a.ecard_no = #{ecardNo,jdbcType=VARCHAR}</if>",
            "<if test=\"startSegment != null\">and a.start_segment &gt;= #{startSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"endSegment != null\">and a.end_segment &lt;= #{endSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"coachId != null\">and a.coach_id = #{coachId,jdbcType=BIGINT}</if>",
            "union all",
            "select null                                  ticket_id,",
            "       null                                  ticket_no,",
            "       ifnull(a.contact_person, '')          cust_name,",
            "       a.ecard_no                            ecard_no,",
            "       d.occupy_date                         effect_date,",
            "       d.occupy_date                         expire_date,",
            "       null                                  ticket_source_type,",
            "       d.segment                             start_segment,",
            "       if(d.book_hour = 0, d.segment, d.segment + d.book_hour * 2 - 1)   end_segment,",
            "       null                                  state,",
            "       0                                     pay_money,",
            "       0                                     discount,",
            "       b.field_name                          field_name,",
            "       b.field_no                            field_no,",
            "       c.field_type_name                     field_type_name,",
            "       e.expire_time                         trade_expire_time,",
            "       e.subscribe_state                     subscribe_state,",
            "       e.channel_id                          channel_id,",
            "       e.trade_id                            trade_id,",
            "       a.phone                               contact_phone,",
            "       null                                  coach_name,",
            "       e.accept_date,",
            "       e.remark",
            "from field_book a",
            "left join trade e on a.trade_id = e.trade_id",
            "left join trade_ticket f on a.trade_id = f.trade_id",
            "left join field_segment_state d on a.trade_id = d.trade_id,",
            "field b, field_type c",
            "where d.field_id = b.field_id",
            "and b.field_type = c.field_type",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.book_state = '4'",
            "and (e.subscribe_state = '1' or ( e.subscribe_state = '0' and now() &lt; e.expire_time or e.expire_time is null))",
            "<if test=\"startDate != null\">and d.occupy_date &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and d.occupy_date &lt;= #{endDate,jdbcType=DATE}</if>",
            "<if test=\"fieldType != null\">and b.field_type = #{fieldType,jdbcType=BIGINT}</if>",
            "<if test=\"fieldNoList.size &gt;0\">and b.field_no in " +
                    " <foreach item=\"fieldNo\" collection=\"fieldNoList\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
                    " #{fieldNo, jdbcType=INTEGER}" +
                    "</foreach>  " +
                    "</if>",
            "<if test=\"ecardNo != null\">and a.ecard_no = #{ecardNo,jdbcType=VARCHAR}</if>",
            "<if test=\"startSegment != null\">and d.segment &gt;= #{startSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"endSegment != null\">and if(d.book_hour = 0, d.segment, d.segment + d.book_hour * 2 - 1) &lt;= #{endSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"coachId != null\">and f.coach_id = #{coachId,jdbcType=BIGINT}</if>",
            ") temp",
            "<if test=\"mobileNum != null\">where contact_phone = #{mobileNum,jdbcType=VARCHAR}</if>",
            "order by effect_date desc, start_segment asc",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_no", property = "fieldNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_expire_time", property = "tradeExpireTime", jdbcType = JdbcType.DATE),
            @Result(column = "subscribe_state", property = "subscribeState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "channel_id", property = "channelId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_name", property = "coachName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "accept_date", property = "acceptDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR)

    })
    List<Map<String, Object>> searchForFieldOccupy(@Param("fieldType") Long fieldType, @Param("fieldNoList") List<Integer> fieldNoList,
                                                   @Param("ecardNo") String ecardNo, @Param("mobileNum")String mobileNum, @Param("startSegment") Short startSegment,
                                                   @Param("endSegment") Short endSegment, @Param("startDate") Date startDate,
                                                   @Param("endDate") Date endDate, @Param("coachId") Long coachId, @Param("venueId") Long venueId, RowBounds rowBounds);

    /**
     * 根据日期获取当天的票
     *
     * @param paramMap
     * @return
     */
    @Select({
            "<script>",
            "select",
            "a.trade_id, a.cust_name, a.ecard_no, a.cust_id, a.create_time, a.service_id, a.ticket_id, a.ticket_no, a.field_id, a.field_name, ",
            "(select ticket_type_name from ticket_type where ticket_type_id = a.ticket_type) as ticket_name, a.deposit_id, a.pay_money-IFNULL(discount,0) pay_money, ",
            "a.start_segment, a.end_segment, a.start_time, a.end_time, a.effect_date, a.expire_date, b.channel_id,",
            "(select venue_name from venue where venue_id = a.venue_id) as venue_name, a.venue_id, ",
            "(select value_name from static_param where attr_code = 'state' and attr_value = a.state) as state, a.group_ticekt_id, a.group_tag",
            "from trade_ticket a, trade b left join customer cu on b.cust_id = cu.cust_id",
            "where",
            "a.venue_id = #{venueId, jdbcType=BIGINT} and a.state not in ('1', '9') and a.trade_id = b.trade_id",
            "and if(cu.cust_id is not null, cu.cust_state != '1', 1=1)",
            "<if test=\"ecardNo != null\">and a.ecard_no = #{ecardNo,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketNo != null\">and (a.ticket_id = #{ticketNo,jdbcType=VARCHAR} or a.ticket_no = #{ticketNo,jdbcType=VARCHAR})</if>",
            "<if test=\"tradeId != null\">and a.trade_id = #{tradeId,jdbcType=BIGINT}</if>",
            "<if test=\"searchDate != null\">and #{searchDate, jdbcType=DATE} between a.effect_date and ifnull(a.expire_date, a.effect_date)",
            "and if(a.group_tag = '0' and a.group_ticekt_id is not null, exists(select 1 from trade_ticket tt where tt.ticket_id = a.group_ticekt_id and tt.group_tag = '1' and #{searchDate, jdbcType=DATE} between tt.effect_date and ifnull(tt.expire_date, tt.effect_date)), 1=1)</if>",
            "order by a.trade_id desc, a.group_ticekt_id desc, a.group_tag desc",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "channel_id", property = "channelId", jdbcType = JdbcType.BIGINT)
    })
    List<TicketInfo> getTicketsByDate(ParamMap paramMap);

    /**
     * 入馆查询票信息
     *
     * @param tradeId
     * @param ticketId
     * @return
     */
    @Select({
            "<script>",
            "select",
            "a.ticket_id ticket_id, a.ticket_no, a.trade_id trade_id, a.service_id service_id, a.venue_id venue_id, a.field_id field_id, a.field_name field_name, a.start_segment start_segment,",
            "a.end_segment end_segment, a.start_time, a.end_time, a.state state, a.effect_date effect_date, a.expire_date expire_date,",
            "b.ticket_type_name ticket_type_name, a.ticket_type, a.group_ticekt_id",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id",
            "<where>",
            "a.trade_id = #{tradeId,jdbcType=BIGINT}",
            "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "and a.state in ('0', '2', '3')",
            "and a.service_id != 0",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and (b.ticket_kind != '4' or a.field_id is not null or a.product_id is not null)",//次票屏蔽这部分
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT)
    })
    List<Map<String, Object>> getByTradeIdForEnterHall(@Param("tradeId") Long tradeId, @Param("venueId") Long venueId, @Param("ticketId") Long ticketId);

    /**
     * 通过depositId查看该账户今日一共入馆的次数
     */
    @Select({
            "select COUNT(DISTINCT CASE WHEN b.limit_tag = '0' THEN a.trade_id ELSE a.ticket_id END) as num",
            "from trade_ticket a, product b",
            "where a.product_id = b.product_id",
            "and a.deposit_id = #{depositId,jdbcType=BIGINT}",
            "and a.state != '1' and a.state != '9'",
            "and a.effect_date >= #{startDate,jdbcType=DATE} and a.effect_date <= curdate()"
    })
    Integer getUseNumByUnit(@Param("depositId") Long depositId, @Param("startDate") Date startDate);

    /**
     * 查看期间卡是否没有出馆，返回入馆时间
     *
     * @param depositId
     * @return
     */
    @Select({
            "select b.enter_in_time ",
            "from trade_ticket a,entry_info b",
            "where a.ticket_id=b.ticket_id",
            "and a.deposit_id=#{depositId,jdbcType=BIGINT}",
            "and a.state !=1",
            "and a.effect_date = curdate() and b.enter_out_time is null"
    })
    List<Date> getNoEnterOutTime(Long depositId);

    @Select({
            "<script>",
            "select count(*)",
            "from trade a, trade_ticket b, trade_ticket_attr c, net_trade e",
            "where a.trade_id = b.trade_id",
            "and b.trade_id = e.trade_id",
            "and e.net_user_id = #{netUserId,jdbcType=BIGINT}",
            "and (b.state not in (1, 9) or (b.state = 9 and now()&lt;a.expire_time))",
            "and b.ticket_id = c.ticket_id ",
            "and c.attr_code = 'perform_ticket_id'",
            "and c.attr_value in",
            "(select d.id from perform_ticket d where d.perform_id = #{performId,jdbcType=VARCHAR})",
            "</script>"
    })
    Integer boughtNum(@Param("netUserId") Long netUserId, @Param("performId") String performId);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.ticket_no, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name,",
            "a.start_segment, a.end_segment, ifnull(a.pay_money,0) pay_money, ifnull(a.discount,0) discount, a.state,",
            "a.create_time, a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type,",
            "a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, a.check_mode, a.effect_date,",
            "a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark,",
            "b.ticket_type_name, c.venue_name, a.player_num",
            "from trade_ticket a, ticket_type b, venue c",
            "<where>",
            "a.ticket_type = b.ticket_type_id AND a.venue_id =c.venue_id",
            "and a.trade_id = #{tradeId,jdbcType=BIGINT}",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR)
    })
    List<Map> selectTicketListForOrderInfo(@Param("tradeId") Long tradeId);


    @Select({
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, a.end_segment, a.pay_money, a.discount, a.state, a.create_time, ",
            "a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, a.check_mode, a.effect_date, ",
            "a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount, a.cancel_trade_id, a.group_ticekt_id, ",
            "a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price, a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money, a.allowance, a.ticket_time_id",
            "from trade_ticket a, field_book b, trade c",
            "where a.trade_id = b.trade_id and a.trade_id = c.trade_id",
            "and b.cycle_trade_id = #{tradeId,jdbcType=BIGINT}",
            "and a.effect_date between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}",
            "and a.field_id = #{fieldId,jdbcType=BIGINT} and a.start_segment = #{startSegment,jdbcType=BIGINT}",
            "and a.state != '1' and a.state != '9' and c.pay_state = '1'"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeTicket> selectOccupyTickets(Map param);

    @Select({
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, a.end_segment, a.pay_money, a.discount, a.state, a.create_time, ",
            "a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, a.check_mode, a.effect_date, ",
            "a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount, a.cancel_trade_id, a.group_ticekt_id, ",
            "a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price, a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money, a.allowance, a.ticket_time_id",
            "from trade_ticket a",
            "where a.trade_id = #{tradeId,jdbcType=BIGINT}",
            "and a.effect_date between #{startDate,jdbcType=DATE} and #{endDate,jdbcType=DATE}",
            "and a.field_id = #{fieldId,jdbcType=BIGINT} and a.start_segment = #{startSegment,jdbcType=BIGINT}",
            "and a.state = '1'"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeTicket> selectCancelTickets(Map param);

    @Select({
            "select",
            "ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, ",
            "start_segment, end_segment, start_time, end_time, pay_money, discount, state, create_time, fetch_ticket_time, ",
            "check_ticket_time, deposit_id, ticket_source_type, ticket_drawer, ticket_collector, ",
            "ticket_cancel_person, check_mode, effect_date, expire_date, ecard_no, cust_id, ",
            "cust_name, product_id, ticket_type, price_item, round, remark, coupon_amount, ",
            "cancel_trade_id, group_ticekt_id, group_tag, prom_id, pspt_id, full_tag, regular_price, ",
            "player_num, coupon_no, coach_id, chnl_cust_id, refund_money, allowance, ticket_time_id",
            "from trade_ticket",
            "where ticket_id = #{ticketIdOrNo,jdbcType=VARCHAR} or ticket_no = #{ticketIdOrNo,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT)
    })
    TradeTicket selectByTicketIdOrNo(String ticketIdOrNo);


    @Select({
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, ",
            "a.start_segment, a.end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, ",
            "a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, ",
            "a.ticket_cancel_person, a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, ",
            "a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount, ",
            "a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price, ",
            "a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money, a.allowance, a.ticket_time_id,b.venue_name,c.service_name,b.serv_venue_id",
            "from trade_ticket a ",
            "left join venue b on a.venue_id = b.venue_id",
            "left join service c on a.service_id = c.service_id",
            "where ticket_id = #{ticketNo,jdbcType=VARCHAR} or ticket_no = #{ticketNo,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "serv_venue_id", property = "servVenueId", jdbcType = JdbcType.VARCHAR)
    })
    DataMap selectByTicketNo(String ticketNo);


    /**
     * 查询网络用户可入馆票
     *
     * @param netUserId
     * @return
     */
    @Select({
            "<script>",
            "select",
            "c.center_name,v.venue_name,s.service_name ,a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, ",
            "a.start_segment, a.end_segment, a.pay_money, a.discount, a.state, a.create_time, ",
            "a.deposit_id,a.effect_date, if(a.expire_date is null,a.effect_date,a.expire_date) as expire_date , a.ecard_no, a.cust_id, ",
            "a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round,tt.ticket_type_name",
            "from trade_ticket a ",
            "left join (venue v, center c )  on a.venue_id = v.venue_id and v.center_id = c.center_id",
            "left join service s on a.service_id = s.service_id ",
            "left join ticket_type tt on a.ticket_type = tt.ticket_type_id ",
            ",net_user_cards e",
            "<where>",
            "a.cust_id = e.venue_cust_id and e.net_user_id = #{netUserId,jdbcType=BIGINT} and e.status = '1' and a.state in ('0','2')",
            "and if(a.field_id is not null,a.effect_date = curdate(), curdate() between a.effect_date and a.expire_date)",
            "</where>",
            "order by expire_date ,a.venue_id,a.trade_id",
            "</script>",
    })
    @Results({
            @Result(column = "center_name", property = "centerName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR)
    })
    List<Map> getValidTicketByNetUserId(@Param("netUserId") Long netUserId);


    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, a.end_segment, a.pay_money, a.discount, a.state, ",
            "a.create_time, a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount, ",
            "a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price, a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money,",
            " a.allowance, a.ticket_time_id,b.field_type,c.book_hour, c.field_type_name, v.venue_name,",
            "(select tt.ticket_type_name from ticket_type tt where tt.ticket_type_id = a.ticket_type) ticket_type_name",
            "from trade_ticket a left join (field b left join field_type c on b.field_type = c.field_type) on a.field_id = b.field_id",
            "left join venue v on a.venue_id = v.venue_id ",
            "where a.trade_id = #{tradeId,jdbcType=BIGINT}",
            "<if test=\"date != null\">and a.effect_date &lt;= #{date, jdbcType=DATE} and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= #{date, jdbcType=DATE}</if>",
            "and a.group_tag != '1'",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.BIGINT),
            @Result(column = "book_hour", property = "bookHour", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
    })
    List<Map> queryByTradeId(@Param("tradeId") Long tradeId,
                             @Param("date") Date date);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, a.end_segment, a.pay_money, a.discount, a.state, ",
            "a.create_time, a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount, ",
            "a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price, a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money,",
            " a.allowance, a.ticket_time_id,b.field_type,c.book_hour, c.field_type_name, e.venue_name, ifnull(f.net_user_id,nuc.net_user_id) as net_user_id,",
            "(select tt.ticket_type_name from ticket_type tt where tt.ticket_type_id = a.ticket_type) ticket_type_name",
            "from trade_ticket a left join (field b left join field_type c on b.field_type = c.field_type) on a.field_id = b.field_id",
            "left join venue e on a.venue_id = e.venue_id",
            "left join net_trade f on a.trade_id = f.trade_id",
            "left join net_user_cards nuc on cast(a.cust_id as char) = nuc.venue_cust_id and nuc.status = '1' and ((now() between nuc.start_date and nuc.end_date) or nuc.end_date is null)",
            "where a.ticket_id = #{ticketId,jdbcType=BIGINT}",
            "<if test=\"date != null\"> and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= #{date, jdbcType=DATE}</if>",
            "and a.group_tag != '1'",
            "limit 1",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.BIGINT),
            @Result(column = "book_hour", property = "bookHour", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.BIGINT)
    })
    DataMap queryByTicketId(@Param("ticketId") Long ticketId,
                            @Param("date") Date date);

    @Select({
            "select a.ticket_type, a.ticket_time_id, count(1) num , b.ticket_type_name, c.name ticket_time_name ",
            "From trade_ticket a, ticket_type b , ticket_type_time c ",
            "where a.trade_id = #{tradeId,jdbcType=BIGINT} ",
            "and a.ticket_type = b.ticket_type_id ",
            "and a.ticket_time_id = c.id ",
            "and (a.group_ticekt_id is null or a.group_ticekt_id = a.ticket_id)",
            "group by a.ticket_type,a.ticket_time_id "
    })
    @Results({
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "num", property = "num", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_time_name", property = "ticketTimeName", jdbcType = JdbcType.VARCHAR)
    })
    List<Map<String, Object>> getTimeTicketList(@Param("tradeId") Long tradeId);

    /**
     * 查询阿里合作场馆的昨天过期但是仍未使用的票
     *
     * @return
     */
    @Select({
            "<script>",
            "select group_concat(a.ticket_id) as ticket_ids,chnl_trade_id",
            "from trade_ticket a,trade t",
            "<where>",
            "a.expire_date = #{date,jdbcType=DATE} and a.state in ('0','2') ",
            "and a.trade_id = t.trade_id and t.channel_id = 6  and t.center_id = #{centerId,jdbcType=BIGINT} ",
            "<if test=\"tradeId != null\">and t.trade_id = #{tradeId,jdbcType=BIGINT}</if>",
            "</where>",
            "group  by a.trade_id ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_ids", property = "ticketIds", jdbcType = JdbcType.VARCHAR),
            @Result(column = "chnl_trade_id", property = "chnlTradeId", jdbcType = JdbcType.BIGINT)
    })
    List<Map> selectUnusedTicketsByDate(@Param("centerId") Long centerId, @Param("tradeId") Long tradeId, @Param("date") Date date);

    @Select({
            "<script>",
            "select",
            "ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, start_segment, end_segment, pay_money, discount, state, create_time, fetch_ticket_time, check_ticket_time, deposit_id, ticket_source_type, ticket_drawer, ticket_collector, ticket_cancel_person, check_mode, effect_date, expire_date, ecard_no, cust_id, cust_name, product_id, ticket_type, price_item, round, remark, coupon_amount, cancel_trade_id, group_ticekt_id, group_tag, prom_id, pspt_id, full_tag, regular_price, player_num, coupon_no, coach_id, chnl_cust_id, refund_money, allowance, ticket_time_id",
            "from trade_ticket",
            "<where>",
            "if(expire_date is null, curdate() = effect_date, curdate() between effect_date and expire_date)",
            "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "and ecard_no = #{ecardNo,jdbcType=VARCHAR}",
            "and state = #{state,jdbcType=VARCHAR}",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeTicket> selectValidATMTickets(TradeTicket param);

    /**
     * 按时段查询当天的入馆次数
     *
     * @param depositId 账本Id
     * @param startTime 开始时间，格式 HHmm
     * @param endTime   结束时间，格式 HHmm
     * @return
     */
    @Select({
            "select COUNT(*) as num",
            "from trade_ticket a, entry_info b",
            "where a.ticket_id=b.ticket_id",
            "and a.deposit_id=#{depositId,jdbcType=BIGINT}",
            "and a.state !=1",
            "and b.enter_in_time between concat(date_format(now(),'%Y%m%d'), #{startTime,jdbcType=VARCHAR}, '00') and concat(date_format(now(),'%Y%m%d'), #{endTime,jdbcType=VARCHAR}, '00')",
    })
    int selectEnterTimesByTime(@Param("depositId") Long depositId,
                               @Param("startTime") String startTime,
                               @Param("endTime") String endTime);

    @Select({
            "<script>",
            "SELECT",
            "	t.trade_id,",
            "	count(tt.ticket_id) num,",
            "	t.trade_desc,",
            "	t.pay_tfee,",
            "	t.accept_date,",
            "   t.service_id",
            "FROM",
            "	trade t,",
            "	trade_ticket tt",
            "<where>",
            "	t.trade_id = tt.trade_id",
            "   and t.cancel_tag = '0'",
            "   and tt.group_tag != '1'",
            "<if test=\"tradeParam.tradeId != null\">and t.trade_id = #{tradeParam.tradeId,jdbcType=BIGINT}</if>",
            "and t.venue_id in (",
            "<foreach collection=\"venueIdSet\" item=\"venueId\" separator=\",\">",
            "#{venueId,jdbcType = BIGINT}",
            "</foreach>",
            ")",
            "<if test=\"tradeParam.contactPhone != null\">",
            "and tt.ticket_id in (",
            "select tta.ticket_id from trade_ticket_attr tta",
            "where tta.attr_code = '" + Constants.TradeTicketAttr.GUEST_PHONE + "'",
            "and tta.attr_value = #{tradeParam.contactPhone,jdbcType = VARCHAR}",
            ")",
            "</if>",
            "<if test=\"startTime != null\">and t.accept_date &gt;= #{startTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"endTime != null\">and t.accept_date &lt;= #{endTime,jdbcType=TIMESTAMP}</if>",
            "</where>",
            "GROUP BY t.trade_id",
            "ORDER BY t.trade_id DESC",
            "</script>",
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "num", property = "num", jdbcType = JdbcType.INTEGER),
            @Result(column = "trade_desc", property = "tradeDesc", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_tfee", property = "payTfee", jdbcType = JdbcType.INTEGER),
            @Result(column = "accept_date", property = "acceptDate", jdbcType = JdbcType.DATE),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeInfo> selectTradeInfos(@Param("tradeParam") Trade tradeParam, @Param("venueIdSet") Set venueIdSet,
                                     @Param("startTime") Date startTime, @Param("endTime") Date endTime,
                                     RowBounds rowBounds);

    @Select({
            "SELECT",
            "tt.ticket_id,",
            "tta.attr_value guest_name,",
            "(select tta.attr_value from trade_ticket_attr tta where tt.ticket_id= tta.ticket_id and tta.attr_code = '" + Constants.TradeTicketAttr.GUEST_PHONE + "') guest_phone,",
            "(select tta.attr_value from trade_ticket_attr tta where tt.ticket_id= tta.ticket_id and tta.attr_code = '" + Constants.TradeTicketAttr.GUEST_GENDER + "') guest_gender,",
            "(select tta.attr_value from trade_ticket_attr tta where tt.ticket_id= tta.ticket_id and tta.attr_code = '" + Constants.TradeTicketAttr.GUEST_BIRTHDAY + "')guest_birthday",
            "FROM",
            "trade_ticket tt, trade_ticket_attr tta",
            "where",
            "tt.ticket_id= tta.ticket_id",
            "and tta.attr_code='guest_name'",
            "and tt.trade_id = #{tradeId,jdbcType=BIGINT}",
            "and tta.attr_value != ''",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "guest_name", property = "guestName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "guest_phone", property = "guestPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "guest_gender", property = "guestGender", jdbcType = JdbcType.CHAR),
            @Result(column = "guest_birthday", property = "guestBirthday", jdbcType = JdbcType.DATE)
    })
    List<Map<String, Object>> selectGuestInfoByTradeId(@Param("tradeId") Long tradeId);

    @Select({
            "<script>",
            "select a.ticket_id, a.service_id, a.venue_id, a.field_id, a.effect_date, ",
            "a.start_segment, a.end_segment, a.state, a.cust_id, a.full_tag",
            "from trade_ticket a left join entry_info ei on ei.ticket_id = a.ticket_id ",
            "where a.state in ('0', '2', '3') ",
            "and a.cust_id in  <foreach collection=\"custIds\" item=\"custId\"  open=\"(\" close=\")\" separator=\",\" > ",
            "#{custId,jdbcType=BIGINT}</foreach>",
            "and if(a.field_id is null, ei.ticket_id is not null, (date_format(now(), '%H') * 2 &gt;= a.start_segment)) ",
            "and not exists (select 1 from car_parking_coupon cpc ",
            "where cpc.relate_object_id = a.ticket_id and cpc.relate_type = '1' and cpc.state = '1') ",
            "and a.effect_date = curdate() ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
    })
    List<TradeTicket> selectCustEntryInfo(@Param("custIds") List<Long> custIds);

    @Select({
            "<script>",
            "select a.ticket_id, a.service_id, a.venue_id, a.field_id, a.effect_date ",
            ", a.start_segment, a.end_segment, a.state, a.cust_id, a.full_tag ",
            "from trade_ticket a, net_user_cards nuc ",
            "where a.cust_id = nuc.venue_cust_id ",
            "and nuc.status = '1' ",
            "and nuc.net_user_id = #{netUserId,jdbcType=BIGINT} ",
            "and a.effect_date &gt;= date_add(curdate(), interval -3 month) ",
            "and a.venue_id = #{venueId,jdbcType=BIGINT} ",
            "union all ",
            "select a.ticket_id, a.service_id, a.venue_id, a.field_id, a.effect_date ",
            ", a.start_segment, a.end_segment, a.state, a.cust_id, a.full_tag ",
            "from trade_ticket a, net_trade nt ",
            "where nt.trade_id = a.trade_id ",
            "and nt.net_user_id = #{netUserId,jdbcType=BIGINT} ",
            "and a.effect_date &gt;= date_add(curdate(), interval -3 month) ",
            "and a.venue_id = #{venueId,jdbcType=BIGINT} ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
    })
    List<TradeTicket> selectLastEntryInfo(@Param("netUserId") Long netUserId,
                                          @Param("venueId") Long venueId);

    @Select({
            "<script>",
            "select count(*)",
            "from trade_ticket",
            "<where>",
            "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "<if test=\"tradeId != null\">and trade_id = #{tradeId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketNo != null\">and ticket_no = #{ticketNo,jdbcType=VARCHAR}</if>",
            "<if test=\"serviceId != null\">and service_id = #{serviceId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldId != null\">and field_id = #{fieldId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldName != null\">and field_name = #{fieldName,jdbcType=VARCHAR}</if>",
            "<if test=\"startSegment != null\">and start_segment = #{startSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"endSegment != null\">and end_segment = #{endSegment,jdbcType=DECIMAL}</if>",
            "<if test=\"payMoney != null\">and pay_money = #{payMoney,jdbcType=BIGINT}</if>",
            "<if test=\"discount != null\">and discount = #{discount,jdbcType=BIGINT}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"fetchTicketTime != null\">and fetch_ticket_time = #{fetchTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"checkTicketTime != null\">and check_ticket_time = #{checkTicketTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"depositId != null\">and deposit_id = #{depositId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketSourceType != null\">and ticket_source_type = #{ticketSourceType,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketDrawer != null\">and ticket_drawer = #{ticketDrawer,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCollector != null\">and ticket_collector = #{ticketCollector,jdbcType=BIGINT}</if>",
            "<if test=\"ticketCancelPerson != null\">and ticket_cancel_person = #{ticketCancelPerson,jdbcType=BIGINT}</if>",
            "<if test=\"checkMode != null\">and check_mode = #{checkMode,jdbcType=VARCHAR}</if>",
            "<if test=\"effectDate != null\">and effect_date = #{effectDate,jdbcType=DATE}</if>",
            "<if test=\"expireDate != null\">and expire_date = #{expireDate,jdbcType=DATE}</if>",
            "<if test=\"ecardNo != null\">and ecard_no = #{ecardNo,jdbcType=VARCHAR}</if>",
            "<if test=\"custId != null\">and cust_id = #{custId,jdbcType=BIGINT}</if>",
            "<if test=\"custName != null\">and cust_name = #{custName,jdbcType=VARCHAR}</if>",
            "<if test=\"productId != null\">and product_id = #{productId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketType != null\">and ticket_type = #{ticketType,jdbcType=BIGINT}</if>",
            "<if test=\"priceItem != null\">and price_item = #{priceItem,jdbcType=BIGINT}</if>",
            "<if test=\"round != null\">and round = #{round,jdbcType=VARCHAR}</if>",
            "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
            "<if test=\"couponAmount != null\">and coupon_amount = #{couponAmount,jdbcType=INTEGER}</if>",
            "<if test=\"cancelTradeId != null\">and cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTicektId != null\">and group_ticekt_id = #{groupTicektId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTag != null\">and group_tag = #{groupTag,jdbcType=CHAR}</if>",
            "<if test=\"promId != null\">and prom_id = #{promId,jdbcType=BIGINT}</if>",
            "<if test=\"psptId != null\">and pspt_id = #{psptId,jdbcType=VARCHAR}</if>",
            "<if test=\"fullTag != null\">and full_tag = #{fullTag,jdbcType=CHAR}</if>",
            "<if test=\"regularPrice != null\">and regular_price = #{regularPrice,jdbcType=INTEGER}</if>",
            "<if test=\"playerNum != null\">and player_num = #{playerNum,jdbcType=INTEGER}</if>",
            "<if test=\"couponNo != null\">and coupon_no = #{couponNo,jdbcType=VARCHAR}</if>",
            "<if test=\"coachId != null\">and coach_id = #{coachId,jdbcType=BIGINT}</if>",
            "<if test=\"chnlCustId != null\">and chnl_cust_id = #{chnlCustId,jdbcType=VARCHAR}</if>",
            "<if test=\"refundMoney != null\">and refund_money = #{refundMoney,jdbcType=BIGINT}</if>",
            "<if test=\"allowance != null\">and allowance = #{allowance,jdbcType=INTEGER}</if>",
            "<if test=\"ticketTimeId != null\">and ticket_time_id = #{ticketTimeId,jdbcType=BIGINT}</if>",
            "<if test=\"oldState != null\">and old_state = #{oldState,jdbcType=VARCHAR}</if>",
            "</where>",
            "</script>"
    })
    int countByFields(TradeTicket param);

    @Select({
            "SELECT a.field_id, a.start_segment, (a.end_segment+1) end_segment, a.effect_date,",
            "(select b.field_type from field b where b.field_id = a.field_id) field_type",
            "from trade_ticket a",
            "where a.trade_id = #{tradeId,jdbcType=BIGINT}",
            "order by a.field_id, a.start_segment"
    })
    @Results({
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "field_type", property = "fieldType", jdbcType = JdbcType.DATE),
    })
    List<Map> selectFieldInfoByTradeId(@Param("tradeId") Long tradeId);

    @Select({
            "select b.ticket_id, b.service_id, b.enter_in_time, b.enter_out_time, b.state, b.key_id, b.enter_method ",
            "from trade_ticket a, entry_info b ",
            "where a.ticket_id = #{ticketId,jdbcType=BIGINT} ",
            "and a.ticket_id=  b.ticket_id ",
            "and a.state = '3' ",
            "union ",
            "select b.ticket_id, b.service_id, b.enter_in_time, b.enter_out_time, b.state, b.key_id, b.enter_method  ",
            "from trade_ticket a, entry_info b ",
            "where a.entry_ticket_id = #{ticketId,jdbcType=BIGINT} ",
            "and a.ticket_id=  b.ticket_id ",
            "and a.state = '3' ",
            "order by enter_in_time desc ",
            "limit 1 "
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_out_time", property = "enterOutTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "key_id", property = "keyId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enter_method", property = "enterMethod", jdbcType = JdbcType.VARCHAR)
    })
    EntryInfo selectLastEntryInfoByEntryTicket(@Param("ticketId") Long ticketId);

    @Select({
            "select a.id trade_ticket_user_id, a.ticket_id, a.net_user_id, a.signature_name, a.child_name, ",
            "a.child_age, a.phone, a.pspt_no, c.enter_in_time, c.enter_out_time, c.last_paid_time ",
            "from trade_ticket_user a, trade_ticket b, entry_info c ",
            "where a.net_user_id = #{netUserId,jdbcType=BIGINT} ",
            "and b.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.ticket_id = b.ticket_id ",
            "and a.ticket_id = c.ticket_id ",
            "and c.enter_in_time between curdate() and date_add(curdate(), interval 1 day) ",
            "order by enter_in_time desc"
    })
    @Results({
            @Result(column = "trade_ticket_user_id", property = "tradeTicketUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "signature_name", property = "signatureName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "child_name", property = "childName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "child_age", property = "childAge", jdbcType = JdbcType.INTEGER),
            @Result(column = "phone", property = "phone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_no", property = "psptNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_out_time", property = "enterOutTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
    })
    List<DataMap> selectUserEntryTickets(@Param("netUserId") Long netUserId,
                                         @Param("venueId") Long venueId);

    /**
     * 受理员工
     *
     * @param paramMap
     * @param rowBounds
     * @return
     */
    @Select({
            "select a.accept_date,a.trade_id,b.ticket_id ,b.ticket_no ,b.pay_money,b.discount,b.product_id, c.enter_in_time, ",
            "d.cust_name, d.pspt_id, tt.ticket_type_name, d.contact_phone, d.cust_id, b.effect_date, d.photo, ",
            "b.start_segment, b.end_segment",
            "from trade a, entry_info c, ticket_type tt, trade_ticket b left join customer d on d.cust_id = b.cust_id",
            "where a.channel_id = #{channelId,jdbcType=BIGINT} and a.trade_type_code in (11,14,15,16) and a.trade_id = b.trade_id and b.product_id is null  and  b.ticket_id = c.ticket_id",
            "and a.accept_date >= #{startDate,jdbcType=BIGINT} and a.accept_date < date_add(#{endDate,jdbcType=BIGINT},interval 1 day)",
            "and a.trade_staff_id = #{staffId,jdbcType=BIGINT}",
            "AND tt.ticket_type_id = b.ticket_type "
    })
    @Results({
            @Result(column = "accept_date", property = "acceptDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "photo", property = "photo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL)
    })
    List<Map> selectCheckTicketInfo(Map<String, Object> paramMap, RowBounds rowBounds);

    /**
     * 通过健康承诺书里的客户手机号查询该使用人的票
     *
     * @param paramMap
     * @param rowBounds
     * @return
     */
    @Select({
            "<script>",
            "select a.accept_date,a.trade_id,b.ticket_id ,b.ticket_no ,b.pay_money,b.discount,b.product_id, c.enter_in_time, ",
            "d.cust_name, d.pspt_id, IFNULL(tt.ticket_type_name,p.product_name ) ticket_name, d.contact_phone, d.cust_id, b.effect_date, d.photo, ",
            "b.start_segment, b.end_segment",
            "FROM  entry_info c LEFT JOIN trade_ticket b ON b.ticket_id = c.ticket_id ",
            "LEFT JOIN trade a ON a.trade_id = b.trade_id",
            "LEFT JOIN ticket_type tt ON tt.ticket_type_id = b.ticket_type",
            "LEFT JOIN product p ON p.product_id = b.product_id,",
            "customer d , agreement_log  al, entry_info_attr e",
            "WHERE e.attr_code = 'health_agreement_log_id' AND e.attr_value = al.agreement_log_id",
            "and a.accept_date &gt;= #{startDate,jdbcType=BIGINT} and a.accept_date &lt; date_add(#{endDate,jdbcType=BIGINT},interval 1 day)",
            "AND c.ticket_id = e.ticket_id",
            "AND al.cust_id = d.cust_id ",
            "and d.contact_phone = #{contactPhone,jdbcType=VARCHAR} ",
            "<if test= \"venueId!=null\">and c.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "</script>"

    })
    @Results({
            @Result(column = "accept_date", property = "acceptDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "photo", property = "photo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL)
    })
    List<Map> selectTicketsByHealthAgreementPhone(Map<String, Object> paramMap, RowBounds rowBounds);

    /**
     * 场馆不使用健康承诺书情况下,通过客户手机号
     *
     * @param paramMap
     * @param rowBounds
     * @return
     */
    @Select({
            "<script>",
            "select a.accept_date,a.trade_id,b.ticket_id ,b.ticket_no ,b.pay_money,b.discount,b.product_id, c.enter_in_time, ",
            "d.cust_name, d.pspt_id,IFNULL(tt.ticket_type_name,p.product_name ) ticket_name, d.contact_phone, d.cust_id, b.effect_date, d.photo, ",
            "b.start_segment, b.end_segment",
            "from trade a, entry_info c, trade_ticket b left join customer d on d.cust_id = b.cust_id",
            "left join ticket_type tt on tt.ticket_type_id = b.ticket_type",
            "LEFT JOIN product p ON p.product_id = b.product_id",
            "where a.trade_id = b.trade_id  and  b.ticket_id = c.ticket_id",
            "and a.accept_date &gt;= #{startDate,jdbcType=BIGINT} and a.accept_date &lt; date_add(#{endDate,jdbcType=BIGINT},interval 1 day)",
            "and b.cust_id is not null",
            "and d.contact_phone = #{contactPhone,jdbcType=VARCHAR} ",
            "<if test= \"venueId!=null\">and c.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "</script>"

    })
    @Results({
            @Result(column = "accept_date", property = "acceptDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "photo", property = "photo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL)
    })
    List<Map> selectCheckTicketsByCustPhone(Map<String, Object> paramMap, RowBounds rowBounds);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id",
            "from trade_ticket a, trade b",
            "<where>",
            "and a.trade_id = b.trade_id and b.trade_type_code != '79'",
            "and a.cust_id = #{custId,jdbcType=BIGINT}",
            "and a.state in('0', '2') and curdate() &lt;= ifnull(a.expire_date, a.effect_date)",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT)
    })
    List<DataMap> selectEffectTicketListByCustId(Long custId);

    @Select({
            "select",
            "a.ticket_id from trade_ticket a, trade_ticket_attr b",
            "where",
            "a.ticket_id = b.ticket_id",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and b.attr_code = '" + Constants.TradeTicketAttr.PALM_PERSON_ID + "'",
            "and b.attr_value = #{palmPersonId, jdbcType=VARCHAR}",
            "and a.state in ('0', '2')",
            "order by ticket_id",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT)
    })
    List<Long> selectValidByPalmPersonId(@Param("venueId") Long venueId, @Param("palmPersonId") String palmPersonId);

    @Select({
            "<script>",
            "select",
            "ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, ",
            "start_segment, end_segment, pay_money, discount, state, create_time, fetch_ticket_time, ",
            "check_ticket_time, deposit_id, ticket_source_type, ticket_drawer, ticket_collector, ",
            "ticket_cancel_person, check_mode, effect_date, expire_date, ecard_no, cust_id, ",
            "cust_name, product_id, ticket_type, price_item, round, remark, coupon_amount, ",
            "cancel_trade_id, group_ticekt_id, group_tag, prom_id, pspt_id, full_tag, regular_price, ",
            "player_num, coupon_no, coach_id, chnl_cust_id, refund_money, allowance, ticket_time_id, ",
            "old_state, entry_ticket_id",
            "from trade_ticket",
            "where ticket_id in",
            "<foreach collection=\"ticketIds\" item=\"item\" open=\"(\" close=\")\" separator=\",\" >",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeTicket> selectTicketsById(@Param("ticketIds") List<Long> ticketIds);

    @Select({
            "<script>",
            "select",
            "a.ticket_id from trade_ticket a, trade_ticket_attr b",
            "where",
            "a.ticket_id = b.ticket_id",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"date != null\">and #{date,jdbcType=DATE} &lt;= ifnull(a.expire_date, a.effect_date)</if>",
            "and b.attr_code = '" + Constants.TradeTicketAttr.FACE_ID + "'",
            "and b.attr_value = #{faceId, jdbcType=VARCHAR}",
            "<if test=\"states != null\">and a.state in ",
            "<foreach item=\"item\" collection=\"states\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{item, jdbcType=BIGINT}",
            "</foreach> ",
            "</if>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT)
    })
    List<Long> selectValidByFaceId(@Param("venueId") Long venueId, @Param("faceId") String faceId,
                                   @Param("states") List states, @Param("date") Date date);

    @Select({
            "<script>",
            "select a.ticket_id, b.trade_id, a.start_time, a.end_time, a.start_segment, a.end_segment, a.field_id, a.cust_id",
            "from trade_ticket a, trade b",
            "<where>",
            "and a.trade_id = b.trade_id",
            "and a.rent_tag = '1'",
            "and a.effect_date = #{date,jdbcType=DATE}",
            "and a.field_id in",
            "<foreach item=\"item\" collection=\"fieldIdList\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            "and ((#{startTime, jdbcType=VARCHAR} &gt;= a.start_time and #{startTime, jdbcType=VARCHAR} &lt; a.end_time)",
            "or (#{endTime, jdbcType=VARCHAR} &gt; a.start_time and #{endTime,jdbcType=VARCHAR} &lt;= a.end_time)",
            "or (#{startTime,jdbcType=VARCHAR} &lt; a.start_time and #{endTime,jdbcType=VARCHAR} &gt; a.end_time))",
            "and (b.pay_state = '1' or b.expire_time &gt;= sysdate())",
            "and a.state != '1'",
            "<if test=\"tradeId != null\">and b.trade_id != #{tradeId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeTicket> selectOccupyRentTicketList(@Param("fieldIdList") List<Long> fieldIdList, @Param("date") Date date,
                                                 @Param("startTime") String startTime, @Param("endTime") String endTime,
                                                 @Param("tradeId") Long tradeId);

    @Select({
            "<script>",
            "select a.ticket_id, b.trade_id, a.start_time, a.end_time, a.field_id",
            "from trade_ticket a left join field c on a.field_id = c.field_id, trade b",
            "<where>",
            "and a.trade_id = b.trade_id",
            "and a.rent_tag = '1'",
            "and a.effect_date = #{date,jdbcType=DATE}",
            "and c.field_type = #{fieldTypeId,jdbcType=BIGINT}",
            "and (b.pay_state = '1' or b.expire_time &gt;= sysdate())",
            "and a.state != '1'",
            "</where>",
            "order by a.field_id, a.start_time",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT)
    })
    List<DataMap> selectRentTicketList(@Param("fieldTypeId") Long fieldTypeId, @Param("date") Date date);

    @Select({
            "select ",
            "a.trade_id, a.ticket_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, ",
            "a.start_segment, a.end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, ",
            "a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, ",
            "a.ticket_cancel_person, a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, ",
            "a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount, ",
            "a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price, ",
            "a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money, a.allowance, a.ticket_time_id, ",
            "a.old_state, a.entry_ticket_id, b.net_user_id from trade_ticket a left join net_trade b on a.trade_id = b.trade_id",
            "where a.ticket_id=#{ticketId, jdbcType=BIGINT}"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.BIGINT)
    })
    DataMap selectNetUserByTicketId(@Param("ticketId") Long ticketId);

    @Select({
            "<script>",
            "select  ",
            "a.trade_id, a.ticket_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name,  ",
            "a.start_segment, a.end_segment, a.start_time, a.end_time, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time,  ",
            "a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector,  ",
            "a.ticket_cancel_person, a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id,  ",
            "a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount,  ",
            "a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price,  ",
            "a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money, a.allowance, a.ticket_time_id,  ",
            "a.old_state, a.entry_ticket_id, b.id trade_ticket_user_id, tt.ticket_type_name ",
            "from trade_ticket a left join trade_ticket_user b on a.ticket_id = b.ticket_id and b.net_user_id = #{netUserId,jdbcType=BIGINT} and b.state &lt;&gt; 0 ",
            " LEFT JOIN ticket_type tt ON tt.ticket_type_id = a.ticket_type ",
            "where (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and (CURDATE() between a.effect_date and a.expire_date) and a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "and a.ecard_no = #{ecardNo, jdbcType=VARCHAR} ",
            "<if test=\"serviceIdList != null\">",
            "and a.service_id in",
            "<foreach item=\"serviceId\" collection=\"serviceIdList\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{serviceId, jdbcType=BIGINT}",
            "</foreach>",
            "</if>",
            "union ",
            "select  ",
            "a.trade_id, a.ticket_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name,  ",
            "a.start_segment, a.end_segment, a.start_time, a.end_time, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time,  ",
            "a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector,  ",
            "a.ticket_cancel_person, a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id,  ",
            "a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount,  ",
            "a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price,  ",
            "a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money, a.allowance, a.ticket_time_id,  ",
            "a.old_state, a.entry_ticket_id, b.id trade_ticket_user_id, tt.ticket_type_name ",
            "from trade_ticket a left join trade_ticket_user b ON a.ticket_id = b.ticket_id",
            "LEFT JOIN ticket_type tt ON tt.ticket_type_id = a.ticket_type ",
            "and b.net_user_id = #{netUserId,jdbcType=BIGINT} and b.state &lt;&gt; 0 ",
            ", net_trade f ",
            "where (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and (CURDATE() between a.effect_date and a.expire_date) and a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "and a.cust_id is null and a.trade_id = f.trade_id and f.net_user_id = #{netUserId,jdbcType=BIGINT} ",
            "<if test=\"serviceIdList != null\">",
            "and a.service_id in",
            "<foreach item=\"serviceId\" collection=\"serviceIdList\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{serviceId, jdbcType=BIGINT}",
            "</foreach>",
            "</if>",
            "union ",
            "select  ",
            "a.trade_id, a.ticket_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name,  ",
            "a.start_segment, a.end_segment, a.start_time, a.end_time, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time,  ",
            "a.check_ticket_time, a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector,  ",
            "a.ticket_cancel_person, a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id,  ",
            "a.cust_name, a.product_id, a.ticket_type, a.price_item, a.round, a.remark, a.coupon_amount,  ",
            "a.cancel_trade_id, a.group_ticekt_id, a.group_tag, a.prom_id, a.pspt_id, a.full_tag, a.regular_price,  ",
            "a.player_num, a.coupon_no, a.coach_id, a.chnl_cust_id, a.refund_money, a.allowance, a.ticket_time_id,  ",
            "a.old_state, a.entry_ticket_id, b.id trade_ticket_user_id, tt.ticket_type_name ",
            "from trade_ticket a left join ticket_type tt ON tt.ticket_type_id = a.ticket_type, trade_ticket_user b, net_trade f ",
            "where (a.state = '0' or (a.state = '3' and DATE_FORMAT(a.check_ticket_time,'%Y-%m-%d') = curdate())) ",
            "and (CURDATE() between a.effect_date and a.expire_date) and a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "and a.trade_id = f.trade_id  ",
            "and b.net_user_id = #{netUserId,jdbcType=BIGINT} ",
            "and a.ticket_id = b.ticket_id and b.state &lt;&gt; 0 ",
            "and b.net_user_id &lt;&gt; f.net_user_id ",
            "<if test=\"serviceIdList != null\">",
            "and a.service_id in",
            "<foreach item=\"serviceId\" collection=\"serviceIdList\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{serviceId, jdbcType=BIGINT}",
            "</foreach>",
            "</if>",
            "order by ticket_type, start_segment asc",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_ticket_user_id", property = "tradeTicketUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectByEcardNo(@Param("venueId") Long venueId,
                                  @Param("ecardNo") String ecardNo,
                                  @Param("netUserId") Long netUserId,
                                  @Param("serviceIdList") List<Long> serviceIdList);

    @Select({
            "select a.field_id, a.field_name, a.start_time, a.end_time, a.effect_date, a.cust_id, a.cust_name, b.ticket_type_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id ",
            "where a.ticket_id = #{ticketId, jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR)
    })
    DataMap selectTicketInfoByTicketId(@Param("ticketId") Long ticketId);

    @Select({
            "select b.venue_name, c.ticket_type_name ,c.ticket_type_id, a.ticket_id, a.ticket_no,",
            "a.field_id,a.field_name,a.start_segment,a.end_segment,a.pay_money,a.discount,a.ecard_no,a.effect_date, a.group_tag, a.group_ticekt_id, count(ticket_type_id) ticket_num ",
            "from trade_ticket a ,ticket_type c left join venue b on c.venue_id=b.venue_id",
            "where a.trade_id=#{tradeId,jdbcType=BIGINT} and a.ticket_type=c.ticket_type_id",
            "and (a.group_tag =1 or (a.group_tag = 0 and a.group_ticekt_id is null)) ",
            "group by ticket_type_id "
    })
    @Results({
            @Result(column = "center_name", property = "centerName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_num", property = "ticketNum", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "group_ticekt_id", property = "groupTicketId", jdbcType = JdbcType.CHAR)
    })
    List<DataMap> selectTicketTypesByTradeId(@Param("tradeId") Long tradeId);

    @Select({
            "<script>",
            "select a.trade_id, a.ticket_id, a.ticket_no, a.venue_id, a.service_id, b.id trade_ticket_user_id, b.net_user_id, ",
            "b.signature_name, b.create_time, b.update_time ",
            "from trade_ticket a, trade_ticket_user b",
            "where a.ticket_id = b.ticket_id and b.state = '1' ",
            "and a.ticket_id = #{ticketId,jdbcType=BIGINT} ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= curdate() ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_ticket_user_id", property = "tradeTicketUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.BIGINT),
            @Result(column = "signature_name", property = "signatureName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP)
    })
    List<DataMap> selectSharedInfoByTicketId(@Param("ticketId") Long ticketId, @Param("netUserId") Long netUserId);

    @Select({
            "<script>",
            "select DISTINCT ",
            "t.ticket_id, t.trade_id, t.ticket_no, t.service_id, t.venue_id, ",
            "IF(t.product_id is null, (select e.attr_value from ticket_type_attr e where e.ticket_type_id = c.ticket_type_id and e.attr_code = 'ticket_type_timeout'), (select e.attr_value from product_attr e where e.product_id = t.product_id and e.attr_code = 'product_timeout')) ticket_type_timeout ",
            "from trade_ticket t left join ticket_type c on t.ticket_type = c.ticket_type_id",
            "where t.entry_ticket_id = #{entryTicketId,jdbcType=BIGINT}",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_timeout", property = "ticketTypeTimeout", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectByEntryTicketId(Long entryTicketId);

    /**
     * 条件查询票信息
     *
     * @param param
     * @param rowBounds
     * @return
     */
    @Select({
            "<script>",
            "select r.email,",
            "a.trade_id, a.ticket_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.start_time, a.end_time, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, ",
            "a.product_id, a.ticket_type, a.remark, a.group_ticekt_id, a.group_tag, a.pspt_id, a.full_tag, a.coach_id, ",
            "(SELECT COUNT(net_user_id) FROM trade_ticket_user WHERE ticket_id = a.ticket_id and state!='0') num, f.player_num, ",
            "group_concat(d.username) username, e.field_no, b.pay_time, s.service_name, t.ticket_type_name,",
            "cm.contact_phone, v.venue_name",
            "FROM trade_ticket a",
            "LEFT JOIN trade b ON a.trade_id = b.trade_id ",
            "LEFT JOIN net_trade nt ON nt.trade_id  = a.trade_id",
            "LEFT JOIN net_user_account d ON d.net_user_id = nt.net_user_id",
            "LEFT JOIN net_user r ON r.net_user_id = nt.net_user_id",
            "LEFT JOIN field e ON e.field_id = a.field_id",
            "LEFT JOIN field_type f ON f.field_type = e.field_type",
            "LEFT JOIN service s ON s.service_id = a.service_id",
            "LEFT JOIN ticket_type t ON t.ticket_type_id = a.ticket_type",
            "LEFT JOIN customer cm ON cm.cust_id = a.cust_id",
            "LEFT JOIN venue v ON v.venue_id = a.venue_id",
            "WHERE b.pay_state = '1' and b.trade_type_code != '147'",
            "and a.state != '1'",
            "<if test=\"venues != null\">and a.venue_id in",
            "<foreach item=\"item\" collection=\"venues\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{item, jdbcType=BIGINT}",
            "</foreach> </if> ",
            "<if test=\"username != null\">and d.username = #{username,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketNo != null\">and a.ticket_no = #{ticketNo,jdbcType=VARCHAR}</if>",
            "<if test=\"serviceId != null\">and s.service_id = #{serviceId,jdbcType=BIGINT}</if>",
            "<if test=\"startDate != null and endDate != null\">",
            "and ((#{startDate,jdbcType=TIMESTAMP} &gt;= STR_TO_DATE(CONCAT(if(a.expire_date is null,a.effect_date,a.expire_date),a.start_time),'%Y-%m-%d%H%i') and #{startDate,jdbcType=TIMESTAMP} &lt;= STR_TO_DATE(CONCAT(a.effect_date,a.end_time),'%Y-%m-%d%H%i'))",
            "or (#{endDate,jdbcType=TIMESTAMP} &gt;= STR_TO_DATE(CONCAT(if(a.expire_date is null,a.effect_date,a.expire_date),a.start_time),'%Y-%m-%d%H%i') and #{endDate,jdbcType=TIMESTAMP} &lt;= STR_TO_DATE(CONCAT(a.effect_date,a.end_time),'%Y-%m-%d%H%i'))",
            "or (STR_TO_DATE(CONCAT(if(a.expire_date is null,a.effect_date,a.expire_date),a.start_time),'%Y-%m-%d%H%i') &gt;= #{startDate,jdbcType=TIMESTAMP} and STR_TO_DATE(CONCAT(a.effect_date,a.end_time),'%Y-%m-%d%H%i') &lt;= #{endDate,jdbcType=TIMESTAMP}))",
            "</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketTypeId != null\">and a.ticket_type = #{ticketTypeId,jdbcType=BIGINT}</if>",
            "<if test=\"fieldTag != null\">and a.field_id is not null</if>",
            "<if test=\"productTag  != null\">and a.product_id is not null</if>",
            "group by a.ticket_id ",
            "ORDER BY b.pay_time DESC",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "num", property = "num", jdbcType = JdbcType.BIGINT),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.BIGINT),
            @Result(column = "username", property = "username", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_no", property = "fieldNo", jdbcType = JdbcType.BIGINT),
            @Result(column = "pay_time", property = "payTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "email", property = "email", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectTickets(ParamMap param, RowBounds rowBounds);

    @Select({
            "<script>",
            "select a.ticket_id, b.trade_id, a.start_time, a.end_time, a.start_segment, a.end_segment, a.field_id, a.cust_id, b.venue_id ",
            "from trade_ticket a, trade b",
            "<where>",
            "and a.trade_id = b.trade_id",
            "and b.chnl_trade_id = #{chnlTradeId, jdbcType=VARCHAR}",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT)
    })
    DataMap selectByChnlTradeId(String chnlTradeId);

    /**
     * 根据ticketId查看详情
     *
     * @param ticketId
     * @return
     */
    @Select({
            "<script>",
            "select ",
            "a.trade_id, a.ticket_id, a.ticket_no, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.start_time, a.end_time, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, ",
            "a.product_id, a.ticket_type, a.remark, a.group_ticekt_id, a.group_tag, a.pspt_id, a.full_tag, a.coach_id, ",
            "(SELECT COUNT(net_user_id) FROM trade_ticket_user WHERE ticket_id = a.ticket_id and state!='0') num, f.player_num, ",
            "group_concat(d.username) username, e.field_no, b.pay_time, s.service_name, t.ticket_type_name,",
            "cm.contact_phone, v.venue_name, d.net_user_id",
            "FROM trade_ticket a",
            "LEFT JOIN trade b ON a.trade_id = b.trade_id ",
            "LEFT JOIN net_trade nt ON nt.trade_id  = a.trade_id",
            "LEFT JOIN net_user_account d ON d.net_user_id = nt.net_user_id",
            "LEFT JOIN field e ON e.field_id = a.field_id",
            "LEFT JOIN field_type f ON f.field_type = e.field_type",
            "LEFT JOIN service s ON s.service_id = e.service_id",
            "LEFT JOIN ticket_type t ON t.ticket_type_id = a.ticket_type",
            "LEFT JOIN customer cm ON cm.cust_id = a.cust_id",
            "LEFT JOIN venue v ON v.venue_id = a.venue_id",
            "WHERE a.ticket_id = #{ticketId,jdbcType=BIGINT}",
            "<if test=\"username != null\">and find_in_set(d.username, #{username,jdbcType=VARCHAR})</if>",
            "group by a.ticket_id ",
            "limit 1",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "num", property = "num", jdbcType = JdbcType.BIGINT),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.BIGINT),
            @Result(column = "username", property = "username", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_no", property = "fieldNo", jdbcType = JdbcType.BIGINT),
            @Result(column = "pay_time", property = "payTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.VARCHAR)
    })
    DataMap selectTicketByTicketId(@Param("ticketId") Long ticketId, @Param("username") String username);

    @Select({
            "<script>",
            "select count(1) buy_num, a.ticket_type",
            "from trade_ticket a ",
            "where a.state in ('0','2', '3')",
            "and a.create_time >= date_add(curdate(), interval -7 day)",
            "and a.ticket_type in ",
            "<foreach collection=\"ticketTypePriceList\" item=\"ticketTypePrice\"  open=\"(\" close=\")\" separator=\",\" > ",
            "#{ticketTypePrice.ticketTypeId,jdbcType=BIGINT}</foreach>",
            "group by a.ticket_type",
            "order by buy_num desc",
            "limit 10",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "buy_num", property = "buyNum", jdbcType = JdbcType.INTEGER)
    })
    List<DataMap> selectHotTicketsInTicketTypes(@Param("ticketTypePriceList") List<TicketTypePrice> ticketTypePriceList);

    @Select({
            "<script>",
            "select a.ticket_id, a.ticket_no, a.effect_date, a.expire_date ,a.start_segment,a.end_segment,a.trade_id,",
            "b.ticket_type_name, a.service_id, a.field_id, a.state ",
            "from trade_ticket a, ticket_type b, ticket_type_attr c, net_trade f ",
            "where a.effect_date &lt;= #{date, jdbcType=DATE} ",
            "and a.ticket_type = b.ticket_type_id ",
            "and b.ticket_type_id = c.ticket_type_id",
            "and c.attr_code = 'play_project'",
            "and (c.attr_value like CONCAT('%\"id\":\"',#{playProjectId,jdbcType=VARCHAR},'\"%')",
            "<if test=\"parentProjectId != null\">or c.attr_value like CONCAT('%\"id\":\"',#{parentProjectId,jdbcType=VARCHAR},'\"%')</if>)",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end &gt;= #{date, jdbcType=DATE}",
            "and a.state in ('0','2')",
            "and a.trade_id = f.trade_id and f.net_user_id = #{netUserId, jdbcType=BIGINT} ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectUserProjectTicket(@Param("netUserId") Long netUserId,
                                          @Param("playProjectId") Long playProjectId,
                                          @Param("parentProjectId") Long parentProjectId,
                                          @Param("date") Date date);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.ticket_no, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, a.end_segment, ",
            "a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, a.deposit_id, a.ticket_source_type, ",
            "a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, a.check_mode, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, ",
            "a.product_id, a.remark, a.coupon_amount, a.group_ticekt_id, a.group_tag, a.coach_id, a.ticket_time_id, b.ticket_type_id, b.ticket_type_name",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id ",
            "where trade_id=#{tradeId,jdbcType=BIGINT} and a.group_tag != '1' group by ifnull(a.group_ticekt_id, a.ticket_id) ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectGetKeyTickets(@Param("tradeId") Long tradeId);


    @Select({
            "select a.trade_id, a.ticket_id, a.ticket_no, a.venue_id, a.effect_date, a.expire_date ,a.start_segment,a.end_segment, ",
            "b.ticket_type_name, a.service_id, a.group_tag, a.group_ticekt_id, b.ticket_type_id, b.ticket_kind, d.venue_name, ",
            "d.icon_name ,ifnull(ei.enter_state, 0) enter_state, ei.enter_in_time, ei.last_paid_time, ei.enter_times ",
            "from trade_ticket a left join ticket_type b on a.ticket_type = b.ticket_type_id ",
            "left join entry_info ei on a.ticket_id = ei.ticket_id, venue d",
            "where a.effect_date <= curdate() ",
            "and case when a.expire_date is not null then a.expire_date else a.effect_date end >= curdate() ",
            "and (ei.enter_out_time is null or ei.enter_out_time between curdate() and date_add(curdate(), interval 1 day))",
            "and a.state in ('0','2','3') ",
            "and a.trade_id = #{tradeId, jdbcType=BIGINT}",
            "and d.venue_id = a.venue_id ",
            "and a.field_id is null ",
            "and a.group_tag != '1'",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "group_ticekt_id", property = "groupTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_kind", property = "ticketKind", jdbcType = JdbcType.CHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "icon_name", property = "iconName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_times", property = "enterTimes", jdbcType = JdbcType.INTEGER),
    })
    List<DataMap> selectChildTicketList(@Param("tradeId") Long tradeId);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, start_segment, end_segment, ",
            "start_time, end_time, pay_money, discount, state, create_time, fetch_ticket_time, check_ticket_time, deposit_id, ",
            "ticket_source_type, ticket_drawer, ticket_collector, ticket_cancel_person, check_mode, effect_date, expire_date, ",
            "ecard_no, cust_id, cust_name, product_id, ticket_type, price_item, round, remark, coupon_amount, cancel_trade_id, ",
            "group_ticekt_id, group_tag, prom_id, pspt_id, full_tag, regular_price, player_num, coupon_no, coach_id, chnl_cust_id, ",
            "refund_money, allowance, ticket_time_id, old_state, entry_ticket_id, rent_tag, b.attr_value group_buying_ticket_id, ",
            "c.attr_value group_buying_ticket_item ",
            "from trade_ticket a ",
            "left join trade_ticket_attr b on a.ticket_id = b.ticket_id and b.attr_code = 'group_buying_ticket_id' ",
            "left join trade_ticket_attr c on a.ticket_id = c.ticket_id and c.attr_code = 'group_buying_ticket_item' ",
            "where a.trade_id = #{tradeId, jdbcType=BIGINT}",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "rent_tag", property = "rentTag", jdbcType = JdbcType.CHAR),
            @Result(column = "group_buying_ticket_id", property = "groupBuyingTicketId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_buying_ticket_item", property = "groupBuyingTicketItem", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectGroupTicketByTradeId(Long tradeId);

    /**
     * 获取票信息
     *
     * @param ticketId
     * @return
     */
    @Select({
            "SELECT IF(b.ticket_type_name  IS NOT NULL,b.ticket_type_name,c.value_name) AS ticket_type_name ",
            "FROM trade_ticket a",
            "LEFT JOIN ticket_type b ON a.ticket_type = b.ticket_type_id",
            "LEFT JOIN static_param c ON attr_code = 'ticket_name' AND c.attr_value = a.service_id",
            "WHERE a.ticket_id = #{ticketId, jdbcType=BIGINT}",
            "limit 1"
    })
    @Results({
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR)
    })
    String selectByTicketId(@Param("ticketId") Long ticketId);

    /**
     * 查询网络用户运动数据(不包括刷卡消费入馆)
     *
     * @param param
     * @return
     */
    @Select({
            "<script>",
            "SELECT a.ticket_id, a.start_time, a.end_time, a.service_id, a.state, ifnull(sl.service_name, d.service_name) service_name, a.effect_date, d.pic_url, ",
            "a.ticket_type, b.enter_time enter_in_time, b.exit_time enter_out_time, TIMESTAMPDIFF(SECOND,b.enter_time,IFNULL(b.exit_time, NOW())) sport_time",
            "FROM trade_ticket a ",
            "left join entry_info t on t.ticket_id = a.ticket_id, entry_info_detail b, trade_ticket_user c, service d",
            "left join service_lang sl on sl.service_id = d.service_id AND sl.language = #{lang,jdbcType=VARCHAR}",
            "WHERE a.ticket_id = c.ticket_id AND a.service_id = d.service_id",
            "and b.ticket_user_id = c.id",
            "AND a.state = '3'",
            "AND b.enter_time IS NOT NULL",
            "AND c.net_user_id =  #{netUserId, jdbcType=BIGINT}",
            "<if test=\"startDate!=null and endDate != null\">",
            "AND (a.effect_date BETWEEN  #{startDate, jdbcType=TIMESTAMP} AND #{endDate, jdbcType=TIMESTAMP})",
            "</if>",
            "<if test=\"serviceId!=null\">AND a.service_id = #{serviceId, jdbcType=BIGINT}</if>",
            "union",
            "SELECT a.ticket_id, a.start_time, a.end_time, a.service_id, a.state, ifnull(sl.service_name, d.service_name) service_name, a.effect_date, d.pic_url,",
            "a.ticket_type, b.enter_in_time enter_in_time, b.enter_out_time enter_out_time, TIMESTAMPDIFF(SECOND,b.enter_in_time,IFNULL(b.enter_out_time, NOW())) sport_time",
            "FROM trade_ticket a, entry_info b left join entry_info_detail e on b.ticket_id = e.ticket_id, ",
            "net_user_cards c,",
            "service d left join service_lang sl on sl.service_id = d.service_id AND sl.language = #{lang,jdbcType=VARCHAR}",
            "WHERE c.net_user_id = #{netUserId, jdbcType=BIGINT} and c.status = '1' and a.state = '3' ",
            "AND a.ticket_id = b.ticket_id ",
            "AND e.ticket_id IS NULL ",
            "AND a.cust_id = c.venue_cust_id ",
            "AND a.service_id = d.service_id ",
            "<if test=\"startDate!=null and endDate != null\">",
            "AND (a.effect_date BETWEEN  #{startDate, jdbcType=TIMESTAMP} AND #{endDate, jdbcType=TIMESTAMP})",
            "</if>",
            "<if test=\"serviceId!=null\">AND a.service_id = #{serviceId, jdbcType=BIGINT}</if>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_out_time", property = "enterOutTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "sport_time", property = "sportTime", jdbcType = JdbcType.BIGINT),
            @Result(column = "pic_url", property = "picUrl", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectTradeTicketsAndLang(ParamMap param);


    @Select({
            // -- 卡消费数据记录
            "<script>",
            "SELECT DISTINCT c.start_time, c.end_time, s.service_id, s.service_name, c.effect_date, s.pic_url,  b.`ticket_id`,",
            "b.enter_in_time, b.enter_out_time, TIMESTAMPDIFF(SECOND,b.enter_in_time,IFNULL(b.enter_out_time, NOW())) sport_time",
            "FROM trade a, entry_info b, trade_ticket c, service s",
            "WHERE a.`trade_id` = b.`entry_trade_id` AND b.`ticket_id` = c.`ticket_id` AND c.service_id = s.`service_id`",
            "AND a.`trade_type_code` IN (14, 15, 16, 11)",//-- 验票入馆(期间卡) 场地票 次票(余额卡或一卡通)
            "AND b.`enter_in_time` IS NOT NULL",
            "AND c.state = '3'",
            "AND a.`subscribe_state` = '1'",
            "<if test=\"custIds == null\"> and false</if>",
            "<if test=\"custIds != null and custIds.size &gt;0\"> AND a.`cust_id` IN",
            "<foreach collection=\"custIds\" item=\"custId\" index=\"index\" separator=\",\" open=\"(\" close=\")\">",
            "#{custId}",
            "</foreach>",
            "</if>",
            "AND a.`center_id` = #{centerId, jdbcType=BIGINT}",
            "<if test=\"startDate!=null and endDate != null\">",
            "AND (c.effect_date BETWEEN  #{startDate, jdbcType=TIMESTAMP} AND #{endDate, jdbcType=TIMESTAMP})",
            "</if>",
            "<if test=\"serviceId!=null\">AND s.service_id = #{serviceId, jdbcType=BIGINT}</if>",
            "UNION",
            // -- 散客
            "SELECT DISTINCT a.start_time, a.end_time, d.service_id, d.service_name, a.effect_date, d.pic_url, t.`ticket_id`,",
            "t.enter_in_time, t.enter_out_time, TIMESTAMPDIFF(SECOND,t.enter_in_time,IFNULL(t.enter_out_time, NOW())) sport_time",
            "FROM trade_ticket a , entry_info t , trade b, net_trade c, service d",
            "WHERE a.trade_id = b.trade_id AND a.service_id = d.service_id",
            "AND b.`trade_type_code` IN (14, 15, 16, 11)",
            "AND t.ticket_id = a.ticket_id",
            "AND a.state = '3'",
            "AND b.`subscribe_state` = '1'",
            "AND b.trade_id = c.trade_id",
            "AND t.enter_in_time IS NOT NULL",
            "AND c.net_user_id =  #{netUserId, jdbcType=BIGINT}",
            "AND b.`center_id` = #{centerId, jdbcType=BIGINT}",
            "<if test=\"startDate!=null and endDate != null\">",
            "AND (a.effect_date BETWEEN  #{startDate, jdbcType=TIMESTAMP} AND #{endDate, jdbcType=TIMESTAMP})",
            "</if>",
            "<if test=\"serviceId!=null\">AND d.service_id = #{serviceId, jdbcType=BIGINT}</if>",
            // -- 课程运动数据
            "UNION",
            "SELECT DISTINCT e.start_time, e.`end_time`, s.service_id, s.`service_name`, d.lesson_date effect_date, s.`pic_url`,b.`ticket_id`,",
            "b.enter_in_time, b.enter_out_time, TIMESTAMPDIFF(SECOND,b.enter_in_time,IFNULL(b.enter_out_time, NOW())) sport_time",
            "FROM trade a, entry_info b, service s , tc_lesson_attn c",
            "LEFT JOIN tc_lesson d ON c.`lesson_id` = d.`lesson_id`",
            "LEFT JOIN tc_time e ON d.time_id = e.`time_id`",
            "WHERE a.trade_id = b.`entry_trade_id` AND b.`service_id` = s.`service_id` AND a.`trade_id` = c.`trade_id`",
            "AND c.state = '1'",
            "AND a.`subscribe_state` = '1'",
            "AND a.`trade_type_code` = 79",
            "AND b.`enter_in_time` IS NOT NULL",
            "<if test=\"custIds == null\"> and false</if>",
            "<if test=\"custIds != null and custIds.size &gt;0\"> AND a.`cust_id` IN",
            "<foreach collection=\"custIds\" item=\"custId\" index=\"index\" separator=\",\" open=\"(\" close=\")\">",
            "#{custId}",
            "</foreach>",
            "</if>",
            "AND a.`center_id` = #{centerId, jdbcType=BIGINT}",
            "<if test=\"startDate!=null and endDate != null\">",
            "AND (d.lesson_date BETWEEN  #{startDate, jdbcType=TIMESTAMP} AND #{endDate, jdbcType=TIMESTAMP})",
            "</if>",
            "<if test=\"serviceId!=null\">AND s.service_id = #{serviceId, jdbcType=BIGINT}</if>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_out_time", property = "enterOutTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "sport_time", property = "sportTime", jdbcType = JdbcType.BIGINT),
            @Result(column = "pic_url", property = "picUrl", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectUserSportInfo(ParamMap param);

    @Select({
            "select",
            "ticket_id, trade_id, ticket_no, service_id, venue_id, field_id, field_name, ",
            "start_segment, end_segment, start_time, end_time, pay_money, discount, state, ",
            "create_time, fetch_ticket_time, check_ticket_time, deposit_id, ticket_source_type, ",
            "ticket_drawer, ticket_collector, ticket_cancel_person, check_mode, effect_date, ",
            "expire_date, ecard_no, cust_id, cust_name, product_id, ticket_type, price_item, ",
            "round, remark, coupon_amount, cancel_trade_id, group_ticekt_id, group_tag, prom_id, ",
            "pspt_id, full_tag, regular_price, player_num, coupon_no, coach_id, chnl_cust_id, ",
            "refund_money, allowance, ticket_time_id, old_state, entry_ticket_id, rent_tag",
            "from trade_ticket",
            "where trade_id = #{tradeId,jdbcType=BIGINT} and ticket_type = #{ticketTypeId,jdbcType=BIGINT}",
            "and effect_date = #{effectDate,jdbcType=DATE} and ticket_time_id = #{timeId,jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "rent_tag", property = "rentTag", jdbcType = JdbcType.CHAR)
    })
    TradeTicket selectRoomOccupyTicket(@Param("tradeId") Long tradeId, @Param("ticketTypeId") Long ticketTypeId,
                                       @Param("effectDate") Date effectDate, @Param("timeId") Long timeId);

    @Select({
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, ",
            "b.key_id ",
            "from trade_ticket a ",
            "left join entry_info b on a.ticket_id = b.ticket_id ",
            "where a.ticket_no = #{ticketNo,jdbcType=VARCHAR} ",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "key_id", property = "keyId", jdbcType = JdbcType.BIGINT)
    })
    DataMap getTicketAndEntryInfoByTicketNo(String ticketNo);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, ",
            "b.key_id, c.attr_value key_return_tag, a.deposit_id, b.state",
            "from trade_ticket a, ",
            "entry_info b left join entry_info_attr c on b.ticket_id = c.ticket_id and c.attr_code = 'key_return_tag', trade t ",
            "where a.ticket_id = b.ticket_id and a.cust_id in ",
            "<foreach collection='custIds' item='item' open='(' close=')' separator=','>",
            "#{item, jdbcType=BIGINT}",
            "</foreach>",
            "and a.venue_id = #{venueId, jdbcType=BIGINT} and a.effect_date = curdate() and b.state = '0' and b.enter_state in  ",
            "<foreach collection='enterStates' item='item' open='(' close=')' separator=','>",
            "#{item, jdbcType=VARCHAR}",
            "</foreach>",
            "and t.trade_id = a.trade_id ",
            /*"<if test=\"shareDepositId != null\">",
            "and (exists (select 1 from entry_info_attr eia where b.ticket_id = eia.ticket_id and eia.attr_code = 'share_deposit' and eia.attr_value = #{shareDepositId,jdbcType=VARCHAR}))",
            "</if>",*/
            "<choose>",
            "<when test=\"shareDepositId != null\" >",
            "and (exists (select 1 from entry_info_attr eia where b.ticket_id = eia.ticket_id and eia.attr_code = 'share_deposit' and eia.attr_value = #{shareDepositId,jdbcType=VARCHAR}))",
            "</when>",
            "<otherwise>",
            "and not exists (select 1 from entry_info_attr eia where b.ticket_id = eia.ticket_id and eia.attr_code = 'share_deposit') ",
            "</otherwise>",
            "</choose>",
            "<if test=\"excludeTradeTypes != null\">",
            "and t.trade_type_code not in ",
            "<foreach collection=\"excludeTradeTypes\" item=\"item\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            "</if>",
            "order by a.deposit_id desc,(IF(b.enter_method = '3', 2, 1)), b.enter_in_time asc",
            "</script>",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "key_id", property = "keyId", jdbcType = JdbcType.BIGINT),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.BIGINT),
            @Result(column = "key_return_tag", property = "keyReturnTag", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectValidEntryInfoByCustIds(@Param("custIds") List<Long> custIds,
                                                @Param("venueId") Long venueId,
                                                @Param("enterStates") List<String> enterStates,
                                                @Param("shareDepositId") String shareDepositId,
                                                @Param("excludeTradeTypes") List<Long> excludeTradeTypes);

    @Select({
            "SELECT c.field_type_name ",
            "FROM trade_ticket a",
            "LEFT JOIN field b ON a.field_id= b.field_id",
            "LEFT JOIN field_type c ON b.field_type = c.field_type",
            "WHERE a.ticket_id = #{ticketId, jdbcType=BIGINT}",
            "limit 1"
    })
    @Results({
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR)
    })
    String selectFieldTypeNameByTicketId(@Param("ticketId") Long ticketId);

    @Select({
            "<script>",
            "select a.ticket_id out_ticket_id, tt.ticket_id in_ticket_id ",
            "from trade_ticket a , entry_info b, net_trade c, ",
            "trade_ticket tt, net_trade nt ",
            "where a.venue_id in ",
            "<foreach collection='venues' item='venue' open='(' close=')' separator=','>",
            "#{venue.venueId, jdbcType=BIGINT}",
            "</foreach>",
            "and a.ticket_id = b.ticket_id ",
            "and b.enter_in_time &gt; curdate() ",
            "and b.state = '0' ",
            "and (b.enter_state != '2' or b.enter_state is null) ",
            "and find_in_set(a.service_id, #{serviceIds,jdbcType=VARCHAR}) ",
            "and date_format(now(), '%H%i') &gt; a.end_time ",
            "and a.trade_id = c.trade_id ",
            "and tt.trade_id = nt.trade_id ",
            "and tt.ticket_type = a.ticket_type ",
            "and nt.net_user_id = c.net_user_id  ",
            "and tt.effect_date = a.effect_date ",
            "and tt.service_id = a.service_id ",
            "and tt.start_segment = a.end_segment ",
            "and tt.venue_id = a.venue_id ",
            "and tt.state in (0,2,3) ",
            "</script>",
    })
    @Results({
            @Result(column = "out_ticket_id", property = "outTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "in_ticket_id", property = "inTicketId", jdbcType = JdbcType.BIGINT)
    })
    List<DataMap> selectAutoOutTickets(@Param("venues") List<Venue> venues,
                                       @Param("serviceIds") String serviceIds);

    /**
     * 获取票列表
     *
     * @param param
     * @param rowBounds
     * @return
     */
    @Select({
            "<script>",
            "SELECT a.ticket_id, a.ticket_no, IFNULL(a.`field_name`, b.ticket_type_name) ticket_name, a.`effect_date`, e.pay_time, a.start_time, a.end_time,",
            "d.`mobile_num`, a.`venue_id`, v.`center_id`, a.`pay_money`, a.`state`, STR_TO_DATE(CONCAT(a.effect_date,a.`end_time`),'%Y-%m-%d%H%i') expire_date",
            "FROM trade_ticket a LEFT JOIN ticket_type b ON a.`ticket_type` = b.ticket_type_id",
            "LEFT JOIN venue v ON a.`venue_id` = v.`venue_id`,",
            "net_trade c, net_user d, trade e",
            "WHERE a.`trade_id` = c.`trade_id` AND c.`net_user_id` = d.`net_user_id`",
            "AND a.`trade_id` = e.`trade_id`",
            "AND e.`pay_state` = '1'",
            "AND a.`state` IN ('0','2','3')",
            "<if test=\"venueId != null\">and a.venue_Id = #{venueId, jdbcType=BIGINT} </if>",
            "<if test=\"keyword != null\">and (a.ticket_no = #{keyword, jdbcType=VARCHAR} or d.mobile_num = #{keyword, jdbcType=VARCHAR}",
            "or a.`field_name` like concat('%', #{keyword,jdbcType=VARCHAR},'%') or  b.ticket_type_name like concat('%', #{keyword,jdbcType=VARCHAR},'%') )</if>",
            "<if test=\"startDate != null\">and a.effect_date &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and a.effect_date &lt;= #{endDate,jdbcType=DATE}</if>",
            "<if test=\"filterTag==1\">and (a.state = '2' or a.state = '0') AND now() &lt;= STR_TO_DATE(CONCAT(a.effect_date,a.`end_time`),'%Y-%m-%d%H%i')</if>",
            "<if test=\"filterTag==2\">and a.state = '3'</if>",
            "<if test=\"filterTag==3\">AND now() &gt; STR_TO_DATE(CONCAT(a.effect_date,a.`end_time`),'%Y-%m-%d%H%i') AND (a.state = '2' or a.state = '0')</if>",
            "ORDER BY a.effect_date DESC, e.pay_time desc",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_time", property = "payTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "mobile_num", property = "mobileNum", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectTicketList(ParamMap param, RowBounds rowBounds);

    /**
     * 票详情
     *
     * @param ticketIdOrNo
     * @return
     */
    @Select({
            "SELECT a.ticket_id, a.ticket_no,IF(a.`field_id` IS NULL, b.ticket_type_name, a.`field_name`) ticket_name, a.`effect_date`,",
            "a.`field_id`, a.`field_name`, b.ticket_type_name, a.`ticket_type`,a.start_time, a.end_time, a.ticket_collector, check_ticket_time, e.pay_time,",
            "d.`mobile_num`, a.`venue_id`, v.`center_id`, a.`pay_money`, a.`state`, STR_TO_DATE(CONCAT(a.effect_date,a.`end_time`),'%Y-%m-%d%H%i') expire_date, f.staff_name",
            "FROM trade_ticket a LEFT JOIN ticket_type b ON a.`ticket_type` = b.ticket_type_id",
            "left join staff f on a.ticket_collector = f.staff_id",
            "LEFT JOIN venue v ON a.`venue_id` = v.`venue_id`,",
            "net_trade c, net_user d, trade e",
            "WHERE a.`trade_id` = c.`trade_id` AND c.`net_user_id` = d.`net_user_id`",
            "AND a.`trade_id` = e.`trade_id`",
            "AND e.`pay_state` = '1'",
            "and (a.ticket_no = #{ticketIdOrNo,jdbcType=VARCHAR} or a.ticket_id = #{ticketIdOrNo,jdbcType=VARCHAR})",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "pay_time", property = "payTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "mobile_num", property = "mobileNum", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "staff_name", property = "staffName", jdbcType = JdbcType.VARCHAR),
    })
    DataMap selectTicketDetail(String ticketIdOrNo);

    /**
     * 当日票统计
     *
     * @param filterTag
     * @return
     */
    @Select({
            "<script>",
            "SELECT COUNT(a.`ticket_id`)",
            "FROM trade_ticket a  LEFT JOIN venue v ON a.`venue_id` = v.`venue_id`, net_trade c, net_user d, trade e",
            "WHERE a.`trade_id` = c.`trade_id` AND c.`net_user_id` = d.`net_user_id`",
            "AND a.`trade_id` = e.`trade_id` AND e.`pay_state` = '1'",
            "AND a.`effect_date` = CURDATE()",
            "<if test=\"venueId != null\">and a.venue_Id = #{venueId, jdbcType=BIGINT} </if>",
            "<if test=\"filterTag=='1'.toString()\">AND a.`state` IN ('0','2','3')</if>",
            "<if test=\"filterTag=='2'.toString()\">and a.state = '3'</if>",
            "<if test=\"filterTag=='3'.toString()\">AND now() &gt; STR_TO_DATE(CONCAT(a.effect_date,a.`end_time`),'%Y-%m-%d%H%i') AND (a.state = '2' or a.state = '0')</if>",
            "</script>"
    })
    Integer selectTicketCount(@Param("filterTag") String filterTag, @Param("venueId") Long venueId);

    /**
     * 根据日期查询票统计
     *
     * @return
     */
    @Select({
            "<script>",
            "SELECT COUNT(a.`ticket_id`) num, a.effect_date",
            "FROM trade_ticket a  LEFT JOIN venue v ON a.`venue_id` = v.`venue_id`, net_trade c, net_user d, trade e",
            "WHERE a.`trade_id` = c.`trade_id` AND c.`net_user_id` = d.`net_user_id`",
            "AND a.`trade_id` = e.`trade_id` AND e.`pay_state` = '1'",
            "<if test=\"startDate != null\">and a.effect_date &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and a.effect_date &lt;= #{endDate,jdbcType=DATE}</if>",
            "<if test=\"venueId != null\">and a.venue_Id = #{venueId, jdbcType=BIGINT} </if>",
            "<if test=\"typeTag != null and typeTag=='0'.toString()\">and a.field_id is null</if>",
            "<if test=\"typeTag != null and typeTag=='1'.toString()\">and a.field_id is not null</if>",
            "<if test=\"filterTag=='1'.toString()\">AND a.`state` IN ('0','2','3')</if>",
            "<if test=\"filterTag=='2'.toString()\">and a.state = '3'</if>",
            "<if test=\"filterTag=='3'.toString()\">AND now() &gt; STR_TO_DATE(CONCAT(a.effect_date,a.`end_time`),'%Y-%m-%d%H%i') AND (a.state = '2' or a.state = '0')</if>",
            "group by a.effect_date",
            "</script>"
    })
    @Results({
            @Result(column = "num", property = "num", jdbcType = JdbcType.INTEGER),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
    })
    List<DataMap> selectTicketStatByDate(ParamMap paramMap);

    /**
     * 根据venueId和aliuid查询可用票
     *
     * @param aliuid
     * @param centerId
     * @return
     */
    @Select({
            "SELECT ",
            "trt.ticket_no, trt.field_id, trt.expire_date, trt.end_time, tit.ticket_type_name ",
            "FROM customer c, aliuid_cust_relation acr, trade_ticket trt ",
            "LEFT JOIN ticket_type tit ON tit.ticket_type_id = trt.ticket_type ",
            "WHERE acr.aliuid = #{aliuid, jdbcType=VARCHAR} AND acr.cust_id = trt.cust_id  AND trt.cust_id = c.cust_id ",
            "AND c.cust_state = '0' AND (trt.state = '0' OR trt.state = '2' OR (trt.state = '3' AND DATE_FORMAT(trt.check_ticket_time, '%Y-%m-%d') = CURDATE())) ",
            "AND acr.center_id =  #{centerId, jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectValidTicketByAliUid(@Param("aliuid") String aliuid, @Param("centerId") Long centerId);

    /**
     * 根据ticketId查询次票的使用情况
     *
     * @param ticketId
     * @return
     */
    @Select({
            "SELECT ",
            "a.ticket_id, a.state, b.cust_name, d.attr_value ",
            "FROM trade_ticket a, customer b, net_user_cards c ",
            "LEFT JOIN net_user_attr d ON d.net_user_id = c.net_user_id and d.attr_code = 'net_user_role' ",
            "WHERE a.cust_id = b.cust_id and c.venue_cust_id = CAST(b.cust_id AS char) and a.ticket_id = #{ticketId, jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "ticketState", jdbcType = JdbcType.INTEGER),
            @Result(column = "attr_value", property = "attrValue", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectTicketStateByTicketId(@Param("ticketId") Long ticketId);

    /**
     * 根据ticketId查询票所占教室信息
     * 用来区分包场票和次票
     *
     * @param ticketId
     * @return
     */
    @Select({
            "SELECT ",
            "distinct a.ticket_type_id, tr.name, tr.place_id",
            "FROM tc_room tr, tc_room_state trs, trade_ticket_user ttu, ticket_type a",
            "WHERE tr.state = '1' AND tr.id = trs.room_id  AND trs.state = '1' AND trs.occupy_ticket_id = ttu.ticket_id ",
            "AND tr.ticket_type_id = a.ticket_type_id AND trs.occupy_date = curdate() AND ttu.ticket_id = #{ticketId, jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT),
    })
    DataMap selectBlockSiteTicketByTicketId(@Param("ticketId") Long ticketId);

    /**
     * 查询当天账本下的完成的次票的订单下的票数量
     *
     * @param depositId
     * @param tradeTypeCode
     * @return
     */
    @Select({
            "select tt.ticket_id, tt.group_ticekt_id, tt.group_tag",
            " from trade t,",
            "     trade_ticket tt,",
            "     trade_pay_log tpl",
            "  where t.trade_id = tt.trade_id",
            "  and tt.trade_id = tpl.trade_id",
            "  and tpl.deposit_id = #{depositId, jdbcType=BIGINT}",
            "  and t.trade_type_code = #{tradeTypeCode, jdbcType=BIGINT}",
            "  and t.subscribe_state = 1",
            "  and t.pay_state = 1",
            "  and tt.state in (0, 2, 3)",
            "  and to_days(t.accept_date) = to_days(now())"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR)
    })
    List<DataMap> selectPayedTicketsByDepositIdAndTradeTypeCode(@Param("depositId") Long depositId, @Param("tradeTypeCode") long tradeTypeCode);

    /**
     * 根据交易流程查一下当前的票数
     *
     * @param tradeId
     * @return
     */
    @Select({
            "<script>",
            "select",
            "ticket_id, group_ticekt_id, group_tag",
            "from trade_ticket  where trade_id=#{tradeId,jdbcType=BIGINT} ",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR)
    })
    List<DataMap> selectTradeTicketDtoByTradeId(@Param("tradeId") Long tradeId);

    /**
     * 查询要导出分享票的售卖信息
     *
     * @param param
     * @return
     */
    @Select({
            "<script>",
            "select ",
            "a.trade_id, group_concat(d.username) username, cm.contact_phone, a.ticket_no, t.ticket_type_name, ",
            "s.service_name, a.field_name, a.effect_date, a.expire_date, a.start_time, a.end_time, ",
            "(SELECT COUNT(net_user_id) FROM trade_ticket_user WHERE ticket_id = a.ticket_id and state!='0') num, f.player_num, ",
            "a.field_id, a.ticket_type, a.product_id, a.ticket_id ",
            "FROM trade_ticket a",
            "LEFT JOIN trade b ON a.trade_id = b.trade_id ",
            "LEFT JOIN net_trade nt ON nt.trade_id  = a.trade_id",
            "LEFT JOIN net_user_account d ON d.net_user_id = nt.net_user_id",
            "LEFT JOIN field e ON e.field_id = a.field_id",
            "LEFT JOIN field_type f ON f.field_type = e.field_type",
            "LEFT JOIN service s ON s.service_id = a.service_id",
            "LEFT JOIN ticket_type t ON t.ticket_type_id = a.ticket_type",
            "LEFT JOIN customer cm ON cm.cust_id = a.cust_id",
            "WHERE b.pay_state = '1'",
            "and a.state != '1'",
            "and a.venue_id in",
            "<foreach item=\"item\" collection=\"venues\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{item, jdbcType=BIGINT}",
            "</foreach>",
            "<if test=\"username != null\">and d.username = #{username,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketNo != null\">and a.ticket_no = #{ticketNo,jdbcType=VARCHAR}</if>",
            "<if test=\"serviceId != null\">and s.service_id = #{serviceId,jdbcType=BIGINT}</if>",
            "<if test=\"type == " + Constants.Tag.YES + "\">",
            "and ((#{startDate,jdbcType=TIMESTAMP} &gt;= STR_TO_DATE(CONCAT(if(a.expire_date is null,a.effect_date,a.expire_date),a.start_time),'%Y-%m-%d%H%i') and #{startDate,jdbcType=TIMESTAMP} &lt;= STR_TO_DATE(CONCAT(a.effect_date,a.end_time),'%Y-%m-%d%H%i'))",
            "or (#{endDate,jdbcType=TIMESTAMP} &gt;= STR_TO_DATE(CONCAT(if(a.expire_date is null,a.effect_date,a.expire_date),a.start_time),'%Y-%m-%d%H%i') and #{endDate,jdbcType=TIMESTAMP} &lt;= STR_TO_DATE(CONCAT(a.effect_date,a.end_time),'%Y-%m-%d%H%i'))",
            "or (STR_TO_DATE(CONCAT(if(a.expire_date is null,a.effect_date,a.expire_date),a.start_time),'%Y-%m-%d%H%i') &gt;= #{startDate,jdbcType=TIMESTAMP} and STR_TO_DATE(CONCAT(a.effect_date,a.end_time),'%Y-%m-%d%H%i') &lt;= #{endDate,jdbcType=TIMESTAMP}))",
            "</if>",
            "<if test=\"type == " + Constants.Tag.NO + "\">and b.pay_time &gt;= #{startDate,jdbcType=TIMESTAMP} and b.pay_time &lt;= #{endDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "group by a.ticket_id ",
            "ORDER BY b.pay_time DESC",
            "</script>"
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "num", property = "num", jdbcType = JdbcType.BIGINT),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.BIGINT),
            @Result(column = "username", property = "username", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> getTicketSaleInfo(ParamMap param);

    /**
     * 查询微信用户当天买了一种类型的票买了多少
     *
     * @param netUserId
     * @param ticketTypeId
     * @param buyDate
     * @return
     */
    @Select({
            "<script>",
            "select count(tt.ticket_id)",
            "from net_trade nt,trade_ticket tt",
            "  left join trade_ticket_attr tta on tta.ticket_id = tt.ticket_id and tta.attr_code='" + Constants.TradeTicketAttr.BUY_DATE + "'",
            "where nt.trade_id = tt.trade_id",
            "  and nt.net_user_id = #{netUserId,jdbcType=BIGINT}",
            "  and ticket_type = #{ticketTypeId,jdbcType=BIGINT}",
            "  and tt.state != '1'",
            "  and tta.attr_value = #{buyDate}",
            "AND NOT EXISTS(select 1 from trade t WHERE t.trade_id = tt.trade_id and t.subscribe_state='0' and t.expire_time &lt; now())",
            "</script>"
    })
    Integer selectDayBuyTicketCount(@Param("netUserId") Long netUserId, @Param("ticketTypeId") Long ticketTypeId, @Param("buyDate") String buyDate);


    /**
     * 根据ticket_id查询产品名（票的名称或购票的卡种类名称）
     *
     * @param ticketId 票id
     * @return
     */
    @Select({
            "<script>",
            "SELECT ",
            "p.product_name, tt.ecard_no, t.ticket_type_name ",
            "FROM ",
            "trade_ticket tt ",
            "LEFT JOIN deposit d ON d.deposit_id = tt.deposit_id ",
            "LEFT JOIN product p ON d.product_id = p.product_id ",
            "LEFT JOIN ticket_type t ON tt.ticket_type = t. ticket_type_id ",
            "where tt.ticket_id = #{ticketId,jdbcType=BIGINT}",
            "</script>"
    })
    @Results({
            @Result(column = "product_name", property = "productName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
    })
    DataMap getProductInfoByTicketId(@Param("ticketId") Long ticketId);

    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id ,b.accept_date,d.cust_name,d.birthday,d.gender,d.contact_phone,d.pspt_id,d.cust_id,a.effect_date,a.start_time,a.end_time,",
            "TIMESTAMPDIFF(YEAR, d.birthday, CURDATE()) age,c.attr_value advice_type,",
            "(select 1 from entry_info info where info.ticket_id = a.ticket_id) advice_state",
            "from trade_ticket a left join ticket_type_attr c on a.ticket_type = c.ticket_type_id and c.attr_code='advice_type', trade b",
            "left join customer d on b.cust_id = d.cust_id and d.cust_state = '0'",
            "where",
            "a.trade_id = b.trade_id and b.trade_type_code = '16'",
            "and a.state in('0', '2', '3')  and (CURRENT_DATE() between a.effect_date and ifnull(a.expire_date, a.effect_date))",
            "<if test=\"venueId != null\">and a.venue_id=#{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"serviceId != null\">and a.service_id=#{serviceId,jdbcType=BIGINT}</if>",
            "<if test=\"adviceType != null\">and c.attr_value=#{adviceType,jdbcType=VARCHAR}</if>",
            "<if test=\"input != null\">and (d.cust_name like concat('%', #{input,jdbcType=VARCHAR},'%') or a.trade_id like concat('%', #{input,jdbcType=VARCHAR},'%')) </if>",
            "order by a.create_time desc",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "accept_date", property = "acceptDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "birthday", property = "birthday", jdbcType = JdbcType.DATE),
            @Result(column = "gender", property = "gender", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "age", property = "age", jdbcType = JdbcType.INTEGER),
            @Result(column = "advice_type", property = "adviceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "advice_state", property = "adviceState", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> getCurrentDayTickets(ParamMap paramMap);

    @Select({
            "<script>",
            "select a.trade_id,a.ticket_id,a.start_segment,a.end_segment,a.field_id,a.trade_id as conflictTradeId,b.occupy_date from trade_ticket a,field_segment_state b",
            "where b.trade_id = a.trade_id and b.field_id=#{fieldId,jdbcType=BIGINT} and b.occupy_date=#{occupyDate,jdbcType=DATE} and b.segment=#{segment,jdbcType=DECIMAL}",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> getTicketsByFieldSegment(ParamMap paramMap);

    @Select({
            "<script>",
            "select a.trade_id,a.ticket_id,a.start_segment,a.end_segment,a.field_id,a.trade_id as conflict_trade_id,b.occupy_date from trade_ticket a,field_segment_state b",
            "where b.trade_id = a.trade_id and b.field_id=#{fieldId,jdbcType=BIGINT} and b.occupy_date=#{occupyDate,jdbcType=DATE} and b.segment &gt;=#{startSegment,jdbcType=DECIMAL} and b.segment &lt;=#{endSegment,jdbcType=DECIMAL}",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "conflict_trade_id", property = "conflictTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "occupy_date", property = "occupyDate", jdbcType = JdbcType.DATE),
    })
    List<DataMap> getTicketsByFieldSegmentNew(ParamMap paramMap);

    // 查询符合黑名单规则的用户信息
    @Select({
            "SELECT cust_id, cust_name, net_user_id, count(distinct ticket_id) AS times, group_concat(expire_date) as expire_date, group_concat(distinct ticket_id) as ticket_ids, group_concat(distinct trade_id) trade_ids",
            "FROM (",
            "   SELECT a.cust_id AS cust_id, c.cust_name AS cust_name, a.ticket_id as ticket_id, b.net_user_id AS net_user_id, a.expire_date, t.trade_id as trade_id",
            "   FROM",
            "   trade_ticket a, net_user_cards b, customer c, trade t",
            "   WHERE CONVERT(a.cust_id, CHAR) = b.venue_cust_id AND CONVERT(c.cust_id, CHAR) = b.venue_cust_id ",
            "   AND a.venue_id = #{venueId, jdbcType=BIGINT} AND b.center_id = #{centerId, jdbcType=BIGINT} and c.center_id = #{centerId, jdbcType=BIGINT}  ",
            "   AND not exists(select 1 from entry_info ei where a.ticket_id = ei.ticket_id)",
            "   and a.state in (0,2)",
            "   and a.cust_id = c.cust_id",
            "   AND a.expire_date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE}",
            "   AND a.trade_id = t.trade_id ",
            "   AND t.trade_type_code != 53 ",
            "   UNION ",
            "   SELECT c.cust_id AS cust_id, c.cust_name AS cust_name, a.ticket_id as ticket_id, ttu.net_user_id AS net_user_id, a.expire_date, t.trade_id as trade_id",
            "   FROM trade_ticket_user ttu, trade_ticket a, customer c, net_user_cards d, trade t",
            "   WHERE CONVERT(d.venue_cust_id, SIGNED) = c.cust_id AND ttu.net_user_id = d.net_user_id AND ttu.ticket_id = a.ticket_id AND ttu.state !='0'",
            "   AND ttu.center_id = #{centerId, jdbcType=BIGINT} AND c.center_id = #{centerId, jdbcType=BIGINT} AND a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "   AND d.status = 1 and now() between d.start_date and d.end_date ",
            "   AND a.trade_id = t.trade_id ",
            "   AND t.trade_type_code != 53 ",
            "   AND not exists(select 1 from entry_info_detail eid where eid.ticket_id = ttu.ticket_id and eid.ticket_user_id = ttu.id)",
            "   and a.state in (0,2)",
            "   AND a.expire_date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE} ",
            "   ) AS result ",
            "GROUP BY cust_id HAVING  times >= #{times, jdbcType=INTEGER}",
    })
    @Results({
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "times", property = "times", jdbcType = JdbcType.BIGINT),
            @Result(column = "net_user_id", property = "netUserId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_ids", property = "tradeIds", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_ids", property = "ticketIds", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectUnCheckCustomer(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("times") Integer times,
                                        @Param("centerId") Long centerId, @Param("venueId") Long venueId);

    /**
     * 查询用户名下未核销的票数
     *
     * @param netUserId
     * @param centerId
     * @param venueId
     * @param startDate 票据过期的起始时间
     * @param endDate   票据过期的终止时间
     * @return
     */
    @Select({
            "SELECT COUNT(ticket_id) AS times, trade_id ",
            "FROM (",
            "   SELECT a.ticket_id AS ticket_id, a.trade_id",
            "   FROM",
            "   trade_ticket a, net_user_cards b, customer c",
            "   WHERE b.net_user_id = #{netUserId, jdbcType=BIGINT} AND a.cust_id = b.venue_cust_id AND c.cust_id = b.venue_cust_id ",
            "   AND a.venue_id = #{venueId, jdbcType=BIGINT} AND b.center_id = #{centerId, jdbcType=BIGINT} and c.center_id = #{centerId, jdbcType=BIGINT}  ",
            "   AND not exists (select 1 from entry_info ei where a.ticket_id = ei.ticket_id) and a.state in (0,2) ",
            "   AND a.expire_date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE}",
            "   UNION ",
            "   SELECT a.ticket_id AS ticket_id, a.trade_id",
            "   FROM trade_ticket_user ttu, trade_ticket a, customer c",
            "   WHERE ttu.net_user_id = #{netUserId, jdbcType=BIGINT} AND a.cust_id = c.cust_id AND ttu.ticket_id = a.ticket_id AND ttu.state !='0'",
            "   AND ttu.center_id = #{centerId, jdbcType=BIGINT} AND c.center_id = #{centerId, jdbcType=BIGINT} AND a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "   AND not exists(select 1 from entry_info_detail eid where eid.ticket_id = ttu.ticket_id and eid.ticket_user_id = ttu.id) and a.state in (0,2) ",
            "   AND a.expire_date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE} ",
            "   ) AS result ",
    })
    @Results({
            @Result(column = "times", property = "times", jdbcType = JdbcType.INTEGER),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
    })
    DataMap queryUnCheckTicketTimes(@Param("netUserId") Long netUserId, @Param("centerId") Long centerId, @Param("venueId") Long venueId,
                                    @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 根据场馆id和时段segment查询场地票信息
     *
     * @param venueId
     * @param startSegment
     * @return
     */
    @Select({
            "SELECT ticket_id, field_id ",
            "FROM trade_ticket a",
            "WHERE a.effect_date BETWEEN curdate() AND curdate()",
            "AND a.state in (0,2,3)",
            "AND a.venue_id = #{venueId, jdbcType=BIGINT}",
            "AND a.field_id IS NOT NULL",
            "AND a.start_segment = #{startSegment, jdbcType=INTEGER}",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT)
    })
    List<TradeTicket> selectFieldTicketInfoByVenueIdAndTime(@Param("venueId") Long venueId, @Param("startSegment") int startSegment);

    @Select({
            "SELECT  c.trade_id, a.enter_in_time, b.accept_date,  e.service_name, c.ticket_id, c.field_id, c.start_time, c.end_time, ca.attr_value as cardCars, ifnull(tta.attr_value, ca.attr_value) as ticketCars",
            "FROM entry_info a, trade b LEFT JOIN cust_attr ca ON ca.cust_id = b.cust_id AND ca.attr_code = 'cars',",
            "trade_ticket c left join service e on c.service_id = e.service_id left join trade_ticket_attr tta on tta.attr_code = 'car_number' and c.ticket_id = tta.ticket_id",
            "WHERE b.cust_id =  #{custId, jdbcType=VARCHAR}",
            "AND b.center_id = #{centerId, jdbcType=INTEGER}",
            "AND a.entry_trade_id = b.trade_id ",
            "AND DATE_FORMAT(a.enter_in_time, '%Y-%m-%d') = #{date,jdbcType=DATE} ",
            "AND a.ticket_id = c.ticket_id",
            "AND c.deposit_id = #{depositId,jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterTime", jdbcType = JdbcType.DATE),
            @Result(column = "accept_date", property = "tradeDate", jdbcType = JdbcType.DATE),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "ticketStartTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "ticketEndTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cardCars", property = "cardCars", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticketCars", property = "ticketCars", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectEntryInfoByCustId(@Param("centerId") Long centerId, @Param("custId") String custId, @Param("depositId") Long depositId, @Param("date") Date entryInDate);

    /**
     * 根据账本id查询对应用户今日的入馆记录
     * @param centerId
     * @param custId
     * @param depositId
     * @return
     */
    @Select({
            "SELECT  c.trade_id, a.enter_in_time, b.accept_date,  e.service_name, c.ticket_id, c.field_id, c.start_time, c.end_time, ca.attr_value as cardCars, ifnull(tta.attr_value, ca.attr_value) as ticketCars ",
            "FROM entry_info a, trade b LEFT JOIN cust_attr ca ON ca.cust_id = b.cust_id AND ca.attr_code = 'cars',",
            "trade_ticket c left join service e on c.service_id = e.service_id left join trade_ticket_attr tta on tta.attr_code = 'car_number' and c.ticket_id = tta.ticket_id, ",
            "res_deposit_change f, deposit g, product h ",
            "WHERE b.cust_id =  #{custId, jdbcType=VARCHAR}",
            "AND b.center_id = #{centerId, jdbcType=INTEGER}",
            "AND f.res_type = 1",
            "AND f.deposit_id = g.deposit_id",
            "AND g.product_id = h.product_id",
            "AND b.trade_id = f.trade_id",
            "AND a.entry_trade_id = b.trade_id ",
            "AND DATE_FORMAT(a.enter_in_time, '%Y-%m-%d') = #{date,jdbcType=DATE}",
            "AND a.ticket_id = c.ticket_id",
            "AND f.deposit_id = #{depositId,jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterTime", jdbcType = JdbcType.DATE),
            @Result(column = "accept_date", property = "tradeDate", jdbcType = JdbcType.DATE),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "ticketStartTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "ticketEndTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cardCars", property = "cardCars", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticketCars", property = "ticketCars", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectSpecialCardEntryInfoByDepositId(@Param("centerId") Long centerId, @Param("custId") String custId, @Param("depositId") Long depositId, @Param("date") Date entryInDate);

    @Select({
            "SELECT  c.trade_id, a.enter_in_time, b.accept_date,  e.service_name, c.ticket_id, c.field_id, c.start_time, c.end_time, ca.attr_value as cardCars, ifnull(tta.attr_value, ca.attr_value) as ticketCars ",
            "FROM entry_info a, trade b LEFT JOIN cust_attr ca ON ca.cust_id = b.cust_id AND ca.attr_code = 'cars',",
            "trade_ticket c left join service e on c.service_id = e.service_id left join trade_ticket_attr tta on tta.attr_code = 'car_number' and c.ticket_id = tta.ticket_id, ",
            "course_enroll_change f ",
            "WHERE b.cust_id =  #{custId, jdbcType=VARCHAR}",
            "AND b.center_id = #{centerId, jdbcType=INTEGER}",
            "AND f.enroll_id = #{enrollId,jdbcType=BIGINT}",
            "AND b.trade_id = f.trade_id",
            "AND a.entry_trade_id = b.trade_id ",
            "AND DATE_FORMAT(a.enter_in_time, '%Y-%m-%d') = #{date,jdbcType=DATE}",
            "AND a.ticket_id = c.ticket_id",
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterTime", jdbcType = JdbcType.DATE),
            @Result(column = "accept_date", property = "tradeDate", jdbcType = JdbcType.DATE),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "ticketStartTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "ticketEndTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cardCars", property = "cardCars", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticketCars", property = "ticketCars", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectCourseEntryInfoByEnrollId(@Param("centerId") Long centerId, @Param("custId") String custId, @Param("enrollId") Long enrollId, @Param("date") Date entryInDate);

    @Select({
            "SELECT  c.trade_id, a.enter_in_time, b.accept_date,  e.service_name, c.ticket_id, c.field_id, c.start_time, c.end_time, ca.attr_value as cardCars, ifnull(tta.attr_value, ca.attr_value) as ticketCars ",
            "FROM entry_info a, trade b LEFT JOIN cust_attr ca ON ca.cust_id = b.cust_id AND ca.attr_code = 'cars',",
            "trade_ticket c left join service e on c.service_id = e.service_id left join trade_ticket_attr tta on tta.attr_code = 'car_number' and c.ticket_id = tta.ticket_id,",
            "ecard_change f ",
            "WHERE b.cust_id = #{custId, jdbcType=VARCHAR}",
            "AND b.center_id = #{centerId, jdbcType=INTEGER}",
            "AND a.entry_trade_id = b.trade_id ",
            "AND b.trade_id = f.trade_id ",
            "AND DATE_FORMAT(a.enter_in_time, '%Y-%m-%d') = #{date,jdbcType=DATE}",
            "AND a.ticket_id = c.ticket_id",
            "AND f.acct_id = #{acctId, jdbcType=BIGINT}",
            "UNION",
            "SELECT  c.trade_id, a.enter_in_time, b.accept_date,  e.service_name, c.ticket_id, c.field_id, c.start_time, c.end_time, ca.attr_value as cardCars, ifnull(tta.attr_value, ca.attr_value) as ticketCars ",
            "FROM entry_info a, trade b LEFT JOIN cust_attr ca ON ca.cust_id = b.cust_id AND ca.attr_code = 'cars',",
            "trade_ticket c left join service e on c.service_id = e.service_id left join trade_ticket_attr tta on tta.attr_code = 'car_number' and c.ticket_id = tta.ticket_id,",
            "trade_pay_log f ",
            "WHERE b.cust_id = #{custId, jdbcType=VARCHAR}",
            "AND b.center_id = #{centerId, jdbcType=INTEGER}",
            "AND a.entry_trade_id = b.trade_id ",
            "AND b.trade_id = f.trade_id ",
            "AND DATE_FORMAT(a.enter_in_time, '%Y-%m-%d') = #{date,jdbcType=DATE}",
            "AND a.ticket_id = c.ticket_id",
            "AND f.pay_mode_code <> " + Constants.PayMode.ECARD,
            "AND f.pay_mode_code <> " + Constants.PayMode.SPECIAL_CARD,
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterTime", jdbcType = JdbcType.DATE),
            @Result(column = "accept_date", property = "tradeDate", jdbcType = JdbcType.DATE),
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "ticketStartTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "ticketEndTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cardCars", property = "cardCars", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticketCars", property = "ticketCars", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectECardEntryInfoByAcctId(@Param("centerId") Long centerId, @Param("custId") String custId, @Param("acctId") Long acctId, @Param("date") Date entryInDate);

    /**
     * 根据netUserId查询可出馆票（有效的入馆记录）
     *
     * @param netUserId
     * @param centerId
     * @param venueId
     * @param serviceId
     * @return
     */
    @Select({
            "SELECT",
            "s.service_name, a.ticket_id, a.trade_id, a.ticket_no, a.service_id, a.field_id, a.field_name, ",
            "a.pay_money, a.state, a.ticket_type, tt.ticket_type_name, ttf.money ",
            "FROM trade_ticket a LEFT JOIN trade_ticket_foregift ttf ON a.ticket_id = ttf.ticket_id ",
            "LEFT JOIN (venue v, center c) ON a.venue_id = #{venueId, jdbcType=BIGINT} AND a.venue_id = v.venue_id AND v.center_id = c.center_id ",
            "LEFT JOIN service s ON a.service_id = s.service_id LEFT JOIN ticket_type tt ON a.ticket_type = tt.ticket_type_id, ",
            "net_user_cards e, entry_info i ",
            "WHERE ",
            "a.cust_id = e.venue_cust_id ",
            "AND e.net_user_id = #{netUserId, jdbcType=BIGINT}",
            "AND e.status = '1' ",
            "AND a.ticket_id = i.ticket_id ",
            "AND a.service_id = #{serviceId, jdbcType=BIGINT} ",
            "AND i.state = 0 ",
            "AND DATE(i.enter_in_time) = CURDATE() ",
            "ORDER BY expire_date , a.venue_id , a.trade_id desc",
    })
    @Results({
            @Result(column = "service_name", property = "serviceName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "money", property = "cashPledge", jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> selectEntryInfoListByNetUserId(@Param("netUserId") Long netUserId, @Param("centerId") Long centerId,
                                                 @Param("venueId") Long venueId, @Param("serviceId") Long serviceId);


    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.ticket_no, a.effect_date, a.expire_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, ",
            "b.key_id, c.attr_value key_return_tag, a.deposit_id, b.state",
            "from trade_ticket a, ",
            "entry_info b left join entry_info_attr c on b.ticket_id = c.ticket_id and c.attr_code = 'key_return_tag', trade t ",
            "where a.ticket_id = b.ticket_id and a.cust_id in ",
            "<foreach collection='custIds' item='item' open='(' close=')' separator=','>",
            "#{item, jdbcType=BIGINT}",
            "</foreach>",
            "and a.venue_id = #{venueId, jdbcType=BIGINT} and a.effect_date = curdate() ",
            "and c.attr_value = '0'",
            "and t.trade_id = a.trade_id ",
            "<if test=\"shareDepositId != null\">",
            "and (exists (select 1 from entry_info_attr eia where b.ticket_id = eia.ticket_id and eia.attr_code = 'share_deposit' and eia.attr_value = #{shareDepositId,jdbcType=VARCHAR}))",
            "</if>",
            "<if test=\"excludeTradeTypes != null\">",
            "and t.trade_type_code not in ",
            "<foreach collection=\"excludeTradeTypes\" item=\"item\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            "</if>",
            "order by b.enter_in_time asc",
            "</script>",
    })
    @Results({
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
            @Result(column="ticket_no", property="ticketNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="effect_date", property="effectDate", jdbcType=JdbcType.DATE),
            @Result(column="expire_date", property="expireDate", jdbcType=JdbcType.DATE),
            @Result(column="ecard_no", property="ecardNo", jdbcType=JdbcType.VARCHAR),
            @Result(column="cust_id", property="custId", jdbcType=JdbcType.BIGINT),
            @Result(column="cust_name", property="custName", jdbcType=JdbcType.VARCHAR),
            @Result(column="product_id", property="productId", jdbcType=JdbcType.BIGINT),
            @Result(column="key_id", property="keyId", jdbcType=JdbcType.BIGINT),
            @Result(column="deposit_id", property="depositId", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.BIGINT),
            @Result(column="key_return_tag", property="keyReturnTag", jdbcType=JdbcType.VARCHAR)
    })
    List<DataMap> getUnReturnKeyEntryInfoByCustIds(@Param("custIds")List<Long> custIds, @Param("venueId") Long venueId,
                                                   @Param("shareDepositId") String shareDepositId, @Param("excludeTradeTypes") List<Long> excludeTradeTypes);

    @Select({
            "<script>",
            "select * from(",
            "select a.ticket_id,a.ticket_no,ifNULL(e.enter_in_time,a.check_ticket_time) entry_time,a.cust_id,a.ecard_no,",
            "a.trade_id, t.accept_date, a.start_time, a.end_time, s.service_name, a.field_id, attr.attr_value as car_numbers",
            "from trade_ticket a,trade_ticket_attr attr,entry_info e left join service s on e.service_id = s.service_id,trade t",
            "<where>",
            "a.ticket_id = attr.ticket_id and a.ticket_id = e.ticket_id",
            "and a.state = '3' and e.state IN ('0','1','2')",
            "<if test=\" venueId != null \"> and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "and e.entry_trade_id = t.trade_id and t.center_id = #{centerId,jdbcType=BIGINT}",
            "and attr.attr_code = '" + Constants.TradeTicketAttr.CAR_NUMBER + "'",
            "and attr.attr_value like CONCAT('%', #{carNumber,jdbcType=VARCHAR},'%')",
            "and ifNULL(e.enter_in_time,a.check_ticket_time) between #{date,jdbcType=DATE} and DATE_ADD(#{date,jdbcType=DATE},INTERVAL 1 DAY)",
            "</where>",
            ") u ",
            "order by u.entry_time desc",
            "</script>",
    })
    @Results({
            @Result(column = "ticket_id",property = "ticketId",jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no",property = "ticketNo",jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_time",property = "entryTime",jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "cust_id",property = "custId",jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id",property = "tradeId",jdbcType = JdbcType.BIGINT),
            @Result(column = "accept_date",property = "tradeDate",jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "start_time",property = "ticketStartTime",jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time",property = "ticketEndTime",jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_name",property = "serviceName",jdbcType = JdbcType.VARCHAR),
            @Result(column = "ecard_no",property = "ecardNo",jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_id",property = "fieldId",jdbcType = JdbcType.BIGINT),
            @Result(column = "car_numbers",property = "carNumbers",jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> ticketCarNumberEntryInfo(@Param("centerId") Long centerId, @Param("venueId") Long venueId,
                                     @Param("carNumber") String carNumber, @Param("date") Date date);

    /**
     * 查询用户的在馆记录（查询本人购买场地票和领取的场地票）
     *
     * @param centerId
     * @param venueId
     * @param netUserId
     * @param custId
     * @return
     */
    @Select({
            "SELECT a.cust_id AS cust_id, c.cust_name AS cust_name, a.ticket_id as ticket_id, a.start_segment, a.end_segment, a.service_id, a.end_time ",
            "FROM",
            "trade_ticket a, customer c",
            "WHERE a.venue_id = #{venueId, jdbcType=BIGINT} AND c.center_id = #{centerId, jdbcType=BIGINT}  ",
            "AND c.cust_id = #{custId, jdbcType=BIGINT} ",
            "AND a.cust_id = c.cust_id ",
            "AND date(a.check_ticket_time) = curdate()",
            "AND exists(select 1 from entry_info ei where a.ticket_id = ei.ticket_id and ei.state = '0')",
            "AND a.state in (0,2,3)",
            "AND a.field_id is not null",
            "UNION ",
            "SELECT c.cust_id AS cust_id, c.cust_name AS cust_name, a.ticket_id as ticket_id, a.start_segment, a.end_segment, a.service_id, a.end_time ",
            "FROM trade_ticket_user ttu, trade_ticket a, customer c, net_user_cards d",
            "WHERE d.venue_cust_id = CONVERT(c.cust_id, CHAR) AND ttu.net_user_id = d.net_user_id AND ttu.ticket_id = a.ticket_id AND ttu.state !='0'",
            "AND ttu.center_id = #{centerId, jdbcType=BIGINT} AND c.center_id = #{centerId, jdbcType=BIGINT} AND a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "AND ttu.net_user_id = #{netUserId, jdbcType=BIGINT} AND d.status = '1' ",
            "AND exists(select 1 from entry_info_detail eid where eid.ticket_id = ttu.ticket_id and eid.ticket_user_id = ttu.id and eid.state = '1')",
            "AND a.state in (0,2,3)",
            "AND date(a.check_ticket_time) = curdate()",
            "AND a.field_id is not null",
            "LIMIT 1",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_time", property = "entryTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.INTEGER),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.INTEGER),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
    })
    DataMap queryInGymEntryInfo(@Param("centerId") Long centerId, @Param("venueId") Long venueId,
                                @Param("netUserId") Long netUserId, @Param("custId") Long custId);

    /**
     * 查询特定时间段内的可入馆场地票
     *
     * @param centerId
     * @param venueId
     * @param netUserId
     * @param custId
     * @return
     */
    @Select({
            "SELECT DISTINCT cust_id, ticket_id, start_segment, end_segment, end_time, ticket_user_id FROM ( ",
            "SELECT a.cust_id AS cust_id, a.ticket_id as ticket_id, a.start_segment, a.end_segment, a.end_time, null as ticket_user_id ",
            "FROM",
            "trade_ticket a, customer c",
            "WHERE a.venue_id = #{venueId, jdbcType=BIGINT} and c.center_id = #{centerId, jdbcType=BIGINT} ",
            "AND c.cust_id = #{custId, jdbcType=BIGINT} ",
            "AND a.cust_id = c.cust_id ",
            "AND curdate() between a.effect_date and a.expire_date ",
            "AND not exists(select 1 from entry_info ei where a.ticket_id = ei.ticket_id)",
            "AND a.state in (0,2,3)",
            "AND a.service_id = #{serviceId,jdbcType=BIGINT}",
            "AND a.start_segment > #{startSegment, jdbcType=INTEGER}",
            "AND a.end_segment <= #{endSegment, jdbcType=INTEGER}",
            "AND a.field_id is not null",
            "UNION ",
            "SELECT c.cust_id AS cust_id, a.ticket_id as ticket_id, a.start_segment, a.end_segment, a.end_time, ttu.id as ticket_user_id ",
            "FROM trade_ticket_user ttu, trade_ticket a, customer c, net_user_cards d",
            "WHERE CONVERT(d.venue_cust_id, SIGNED) = c.cust_id AND ttu.net_user_id = d.net_user_id AND ttu.ticket_id = a.ticket_id AND ttu.state !='0'",
            "AND ttu.center_id = #{centerId, jdbcType=BIGINT} AND c.center_id = #{centerId, jdbcType=BIGINT} AND a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "AND ttu.net_user_id = #{netUserId, jdbcType=BIGINT} AND d.status = '1' ",
            "AND not exists(select 1 from entry_info_detail eid where eid.ticket_id = ttu.ticket_id and eid.ticket_user_id = ttu.id)",
            "AND a.state in (0,2,3)",
            "AND a.service_id = #{serviceId,jdbcType=BIGINT}",
            "AND a.start_segment > #{startSegment, jdbcType=INTEGER}",
            "AND a.end_segment <= #{endSegment, jdbcType=INTEGER}",
            "AND curdate() between a.effect_date and a.expire_date",
            "AND a.field_id is not null) AS tb",
            "GROUP BY cust_id , ticket_id , start_segment , end_segment , end_time",
            "ORDER BY end_segment",
    })
    @Results({
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.INTEGER),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.INTEGER),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_user_id", property = "ticketUserId", jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> queryValidTicketBetweenSegement(@Param("centerId") Long centerId, @Param("venueId") Long venueId,
                                                  @Param("netUserId") Long netUserId, @Param("custId") Long custId,
                                                  @Param("startSegment") int startSegment, @Param("endSegment") int endSegment,
                                                  @Param("serviceId") Long serviceId);

    /**
     * 查询用户名下未核销的票数
     *
     * @param netUserId
     * @param centerId
     * @param venueId
     * @param startDate 票据过期的起始时间
     * @param endDate   票据过期的终止时间
     * @return
     */
    @Select({
            "SELECT COUNT(ticket_id) AS times, trade_id ",
            "FROM (",
            "   SELECT a.ticket_id AS ticket_id, a.trade_id",
            "   FROM",
            "   trade_ticket a, net_user_cards b, customer c",
            "   WHERE a.cust_id = b.venue_cust_id AND convert(c.cust_id, char) = b.venue_cust_id  ",
            "   AND c.cust_id = #{custId, jdbcType=BIGINT}  ",
            "   AND a.venue_id = #{venueId, jdbcType=BIGINT} AND b.center_id = #{centerId, jdbcType=BIGINT} and c.center_id = #{centerId, jdbcType=BIGINT}  ",
            "   AND not exists (select 1 from entry_info ei where a.ticket_id = ei.ticket_id) and a.state in (0,2,3) ",
            "   AND a.expire_date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE}",
            "   UNION ",
            "   SELECT a.ticket_id AS ticket_id, a.trade_id",
            "   FROM trade_ticket_user ttu, trade_ticket a, customer c",
            "   WHERE c.cust_id = #{custId, jdbcType=BIGINT} AND a.cust_id = c.cust_id AND ttu.ticket_id = a.ticket_id AND ttu.state !='0'",
            "   AND ttu.center_id = #{centerId, jdbcType=BIGINT} AND c.center_id = #{centerId, jdbcType=BIGINT} AND a.venue_id = #{venueId, jdbcType=BIGINT} ",
            "   AND not exists(select 1 from entry_info_detail eid where eid.ticket_id = ttu.ticket_id and eid.ticket_user_id = ttu.id) and a.state in (0,2,3) ",
            "   AND a.expire_date BETWEEN #{startDate,jdbcType=DATE} AND #{endDate,jdbcType=DATE} ",
            "   ) AS result ",
    })
    @Results({
            @Result(column = "times", property = "times", jdbcType = JdbcType.INTEGER),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
    })
    DataMap queryUnCheckTicketTimesByCustId(@Param("custId") Long custId, @Param("centerId") Long centerId, @Param("venueId") Long venueId,
                                    @Param("startDate") Date startDate, @Param("endDate") Date endDate);


    @Select({
            "select a.ticket_id,a.ticket_no,a.venue_id,a.start_time,a.end_time,a.effect_date,a.expire_date,a.state," ,
            "       a.ticket_type as ticket_type_id,b.ticket_type_name ,a.ticket_time_id,c.name as ticket_time_name" ,
            "from trade_ticket a left join ticket_type b on a.ticket_type=b.ticket_type_id" ,
            "left join ticket_type_time c on a.ticket_time_id = c.id,trade t" ,
            "where a.trade_id = t.trade_id and a.trade_id = #{tradeId} and a.state!='1' and t.pay_state='1'"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ticket_type_id", property = "ticketTypeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type_name", property = "ticketTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_time_name", property = "ticketTimeName", jdbcType = JdbcType.VARCHAR),
    })
    List<Map<String, Object>> selectAppSellTicketList(@Param("tradeId") Long tradeId);

    @Select({
            "<script>",
            "select",
            "tt.trade_id, tt.ticket_no,tt.field_name,tt.start_time,tt.end_time,v.venue_name,ft.field_type_name,tt.effect_date,tt.expire_date",
            "from trade_ticket tt LEFT JOIN venue v ON tt.venue_id = v.venue_id LEFT JOIN field f ON f.field_id = tt.field_id LEFT JOIN field_type ft ON f.field_type = ft.field_type ",
            " where tt.trade_id=#{tradeId,jdbcType=BIGINT} ",
            "</script>"
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "field_type_name", property = "fieldTypeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
    })
    List<DataMap> selectInfoByTradeId(Long tradeId);

    @Select({
            "select count(1) total" ,
            "from trade_ticket tc " ,
            "where tc.state in (0,2,3) and tc.venue_id = #{ticketVenueId, jdbcType=BIGINT} and tc.ticket_type = #{ticketTypeId, jdbcType=BIGINT}" ,
    })
    @Results({
            @Result(column = "total", property = "total", jdbcType = JdbcType.BIGINT, id = true)
    })
    Integer selectTicketSalesCount(@Param("ticketVenueId") Long ticketVenueId,@Param("ticketTypeId") Long ticketTypeId);


    @Select({
            "<script>",
            "select",
            "a.ticket_id, a.trade_id, a.service_id, a.venue_id, a.field_id, a.field_name, a.start_segment, ",
            "a.end_segment, a.pay_money, a.discount, a.state, a.create_time, a.fetch_ticket_time, a.check_ticket_time, ",
            "a.deposit_id, a.ticket_source_type, a.ticket_drawer, a.ticket_collector, a.ticket_cancel_person, ",
            "a.check_mode, a.effect_date, a.ecard_no, a.cust_id, a.cust_name, a.product_id, a.ticket_type, ",
            "a.price_item, a.round, a.player_num",
            "from trade_ticket a,",
            "     trade b",
            "         left join trade_extra c on b.trade_id = c.trade_id and c.extra_key = '" + Constants.Extra.MINI_APP_OPEN_ID + "'",
            "where a.trade_id = b.trade_id",
            "  and c.value = #{miniAppOpenId}",
            "<if test=\"tradeId != null\"> and a.trade_id != #{tradeId,jdbcType=BIGINT} </if>",
            "and ((b.pay_state = '0' AND TIMESTAMPDIFF(SECOND, b.expire_time, NOW()) &lt;0) or b.pay_state = '1')",
            "and a.effect_date = #{effectDate,jdbcType=DATE}",
            "and a.state in ('0','2','3','9')",
            "and a.venue_id = #{venueId,jdbcType=BIGINT}",
            "and a.service_id = #{serviceId,jdbcType=BIGINT}",
            "and a.field_id is not null and a.field_id&lt;&gt;''",
            "order by field_id,start_segment",
            "</script>"

    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER)
    })
    List<TradeTicket> selectTicketByMiniAppOpenId(@Param("effectDate") Date effectDate,
                                                  @Param("serviceId") Long serviceId,
                                                  @Param("venueId") Long venueId,
                                                  @Param("tradeId") Long tradeId,
                                                  @Param("miniAppOpenId") String miniAppOpenId);

    @Select({
            "<script>",
            "select a.ticket_id,a.trade_id,a.remark,a.state" ,
            "from trade_ticket a,field f" ,
            "where a.field_id = f.field_id" ,
            "and f.center_id = #{centerId,jdbcType=BIGINT}" ,
            "and f.venue_id in " ,
            "<foreach collection=\"venueList\" item=\"item\"  open=\"(\" close=\")\" separator=\",\" >",
            "#{item,jdbcType=BIGINT}",
            "</foreach>",
            "and a.expire_date = #{date,jdbcType=DATE}" ,
            "and a.state in ('0','2')",
            "</script>",

    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),

    })
    List<TradeTicket> selectAutoCheckTicketList(@Param("centerId") Long centerId,
                                                @Param("venueList") List<Long> venueList,
                                                @Param("date") Date date);


    @Update({
            "<script>",
            "<foreach collection=\"items\" item=\"item\" separator=\";\">",
            "update trade_ticket",
            "<set>",
            "<if test=\"item.tradeId != null\">trade_id = #{item.tradeId,jdbcType=BIGINT},</if>",
            "<if test=\"item.ticketNo != null\">ticket_no = #{item.ticketNo,jdbcType=VARCHAR},</if>",
            "<if test=\"item.serviceId != null\">service_id = #{item.serviceId,jdbcType=BIGINT},</if>",
            "<if test=\"item.venueId != null\">venue_id = #{item.venueId,jdbcType=BIGINT},</if>",
            "<if test=\"item.fieldId != null\">field_id = #{item.fieldId,jdbcType=BIGINT},</if>",
            "<if test=\"item.fieldName != null\">field_name = #{item.fieldName,jdbcType=VARCHAR},</if>",
            "<if test=\"item.startSegment != null\">start_segment = #{item.startSegment,jdbcType=DECIMAL},</if>",
            "<if test=\"item.endSegment != null\">end_segment = #{item.endSegment,jdbcType=DECIMAL},</if>",
            "<if test=\"item.startTime != null\">start_time = #{item.startTime,jdbcType=VARCHAR},</if>",
            "<if test=\"item.endTime != null\">end_time = #{item.endTime,jdbcType=VARCHAR},</if>",
            "<if test=\"item.payMoney != null\">pay_money = #{item.payMoney,jdbcType=BIGINT},</if>",
            "<if test=\"item.discount != null\">discount = #{item.discount,jdbcType=BIGINT},</if>",
            "<if test=\"item.state != null\">state = #{item.state,jdbcType=VARCHAR},</if>",
            "<if test=\"item.createTime != null\">create_time = #{item.createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"item.fetchTicketTime != null\">fetch_ticket_time = #{item.fetchTicketTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"item.checkTicketTime != null\">check_ticket_time = #{item.checkTicketTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"item.depositId != null\">deposit_id = #{item.depositId,jdbcType=BIGINT},</if>",
            "<if test=\"item.ticketSourceType != null\">ticket_source_type = #{item.ticketSourceType,jdbcType=VARCHAR},</if>",
            "<if test=\"item.ticketDrawer != null\">ticket_drawer = #{item.ticketDrawer,jdbcType=BIGINT},</if>",
            "<if test=\"item.ticketCollector != null\">ticket_collector = #{item.ticketCollector,jdbcType=BIGINT},</if>",
            "<if test=\"item.ticketCancelPerson != null\">ticket_cancel_person = #{item.ticketCancelPerson,jdbcType=BIGINT},</if>",
            "<if test=\"item.checkMode != null\">check_mode = #{item.checkMode,jdbcType=VARCHAR},</if>",
            "<if test=\"item.effectDate != null\">effect_date = #{item.effectDate,jdbcType=DATE},</if>",
            "<if test=\"item.expireDate != null\">expire_date = #{item.expireDate,jdbcType=DATE},</if>",
            "<if test=\"item.ecardNo != null\">ecard_no = #{item.ecardNo,jdbcType=VARCHAR},</if>",
            "<if test=\"item.custId != null\">cust_id = #{item.custId,jdbcType=BIGINT},</if>",
            "<if test=\"item.custName != null\">cust_name = #{item.custName,jdbcType=VARCHAR},</if>",
            "<if test=\"item.productId != null\">product_id = #{item.productId,jdbcType=BIGINT},</if>",
            "<if test=\"item.ticketType != null\">ticket_type = #{item.ticketType,jdbcType=BIGINT},</if>",
            "<if test=\"item.priceItem != null\">price_item = #{item.priceItem,jdbcType=BIGINT},</if>",
            "<if test=\"item.round != null\">round = #{item.round,jdbcType=VARCHAR},</if>",
            "<if test=\"item.remark != null\">remark = #{item.remark,jdbcType=VARCHAR},</if>",
            "<if test=\"item.couponAmount != null\">coupon_amount = #{item.couponAmount,jdbcType=INTEGER},</if>",
            "<if test=\"item.cancelTradeId != null\">cancel_trade_id = #{item.cancelTradeId,jdbcType=BIGINT},</if>",
            "<if test=\"item.groupTicektId != null\">group_ticekt_id = #{item.groupTicektId,jdbcType=BIGINT},</if>",
            "<if test=\"item.groupTag != null\">group_tag = #{item.groupTag,jdbcType=CHAR},</if>",
            "<if test=\"item.promId != null\">prom_id = #{item.promId,jdbcType=BIGINT},</if>",
            "<if test=\"item.psptId != null\">pspt_id = #{item.psptId,jdbcType=VARCHAR},</if>",
            "<if test=\"item.fullTag != null\">full_tag = #{item.fullTag,jdbcType=CHAR},</if>",
            "<if test=\"item.regularPrice != null\">regular_price = #{item.regularPrice,jdbcType=INTEGER},</if>",
            "<if test=\"item.playerNum != null\">player_num = #{item.playerNum,jdbcType=INTEGER},</if>",
            "<if test=\"item.couponNo != null\">coupon_no = #{item.couponNo,jdbcType=VARCHAR},</if>",
            "<if test=\"item.coachId != null\">coach_id = #{item.coachId,jdbcType=BIGINT},</if>",
            "<if test=\"item.chnlCustId != null\">chnl_cust_id = #{item.chnlCustId,jdbcType=VARCHAR},</if>",
            "<if test=\"item.refundMoney != null\">refund_money = #{item.refundMoney,jdbcType=BIGINT},</if>",
            "<if test=\"item.allowance != null\">allowance = #{item.allowance,jdbcType=INTEGER},</if>",
            "<if test=\"item.ticketTimeId != null\">ticket_time_id = #{item.ticketTimeId,jdbcType=BIGINT},</if>",
            "<if test=\"item.oldState != null\">old_state = #{item.oldState,jdbcType=VARCHAR},</if>",
            "<if test=\"item.entryTicketId != null\">entry_ticket_id = #{item.entryTicketId,jdbcType=BIGINT},</if>",
            "<if test=\"item.rentTag != null\">rent_tag = #{item.rentTag,jdbcType=CHAR},</if>",
            "</set>",
            "where ticket_id = #{item.ticketId,jdbcType=BIGINT}",
            "</foreach>",
            "</script>",

    })
    int batchUpdate(@Param("items") List<TradeTicket> list);

    @Select({
            "<script>",
            "SELECT *" ,
            "FROM trade_ticket" ,
            "where field_id = #{fieldId,jdbcType=BIGINT} AND CURRENT_DATE BETWEEN effect_date AND expire_date" ,
            "AND #{time,jdbcType=VARCHAR} BETWEEN CONCAT( SUBSTRING( start_time, 1, 2 ), ':', SUBSTRING( start_time, 3, 2 )) ",
            "AND CONCAT( SUBSTRING( end_time, 1, 2 ), ':', SUBSTRING( end_time, 3, 2 ))" ,
            "AND state in (0,2,3)",
            "</script>",

    })
    TradeTicket selectFieldUsedInfo(@Param("fieldId") Long fieldId, @Param("time")String time);


    @Select({
            "select ifnull(count(a.ticket_id),0)" ,
            "from trade_ticket a,net_trade b" ,
            "where a.trade_id = b.trade_id" ,
            "  and a.state!='1'" ,
            "  and a.ticket_type = #{ticketTypeId}" ,
            "  and b.net_user_id = #{netUserId}" ,
            "  and not exists(select 1 from trade t WHERE t.trade_id = a.trade_id and t.subscribe_state='0' and t.expire_time < now())"
    })
    int selectPersonBuyNum(@Param("netUserId") Long netUserId, @Param("ticketTypeId") Long ticketTypeId);

    @Select({
            "select tt.ticket_id" ,
            "from trade_ticket tt," ,
            "     net_trade n" ,
            "where case when tt.expire_date is not null then tt.expire_date else tt.effect_date end >= current_date" ,
            "  and find_in_set(tt.state,#{state})" ,
            "  and tt.venue_id = #{venueId}" ,
            "  and tt.cust_id is null" ,
            "  and tt.trade_id = n.trade_id" ,
            "  and n.net_user_id = #{netUserId}" ,
            "  and not exists(select 1 from trade_ticket_user ttu where ttu.ticket_id = tt.ticket_id and ttu.net_user_id = #{netUserId})" ,
            "union" ,
            "select tt.ticket_id" ,
            "from trade_ticket tt," ,
            "     net_user_cards c" ,
            "where case when tt.expire_date is not null then tt.expire_date else tt.effect_date end >= current_date" ,
            "  and find_in_set(tt.state,#{state})" ,
            "  and tt.cust_id = cast(c.venue_cust_id as signed)" ,
            "  and tt.venue_id = #{venueId}" ,
            "  and c.net_user_id = #{netUserId}" ,
            "  and c.status = '1'" ,
            "  and not exists(select 1 from trade_ticket_user ttu where ttu.ticket_id = tt.ticket_id and ttu.net_user_id = #{netUserId})"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),

    })
    List<DataMap> selectValidEntryTicketsByNetUserId(@Param("venueId") Long venueId,
                                                     @Param("netUserId") Long netUserId,
                                                     @Param("state") String state);


    /**
     * 查询网络用户是否存在同一时间段内预定的场地
     *
     * @param netUserId
     * @param startSegment
     * @param endSegment
     * @param date
     * @return
     */
    @Select({
            "select count(1)\n" +
            "from trade_ticket tt\n" +
            "         LEFT JOIN net_trade nt ON tt.trade_id = nt.trade_id\n" +
            "where nt.net_user_id = #{netUserId}\n" +
            "and tt.field_id is not null"+
            "  and tt.effect_date = DATE_FORMAT(#{date},'%Y-%m-%d')\n" +
            "  and ((start_segment <= #{startSegment} and #{startSegment} < end_segment) or (start_segment < #{endSegment} and #{endSegment} <= end_segment))\n" +
                    // 未支付订单查询下订单的有效时间，防止未支付多笔预占的情况
            "  and if(tt.state = '9', exists(select 1 from trade t where t.trade_id = tt.trade_id and now() <= expire_time),\n" +
            "         tt.state != '1')",
    })
    int selectCountByTimeIdAndDate(@Param("netUserId") Long netUserId,
                                   @Param("startSegment") Long startSegment,
                                   @Param("endSegment") Long endSegment,
                                   @Param("date") Date date);


    @Select({
            "select",
            "a.ticket_id, b.attr_value",
            "from trade_ticket a ",
            "left join ticket_type_attr b ON a.ticket_type = b.ticket_type_id  ",
            // attr为深水证类型属性
            "where b.attr_code = 'water_certificate_validity' and a.trade_id = #{tradeId,jdbcType=BIGINT}",
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "attr_value", property = "attrValue", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectWaterTicketAttrInfoByTradeId(Long tradeId);

    /**
     * 根据ticketId查看详情
     *
     * @param ticketId
     * @return
     */
    @Select({
            "<script>",
            "select ",
            "a.trade_id, a.ticket_id, a.ticket_no,",
            "cm.contact_phone,cm.cust_name ",
            "FROM trade_ticket a",
            "LEFT JOIN customer cm ON cm.cust_id = a.cust_id",
            "WHERE a.ticket_id = #{ticketId,jdbcType=BIGINT}",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR)
    })
    DataMap selectUserInfoByTicketId(@Param("ticketId") Long ticketId);

    @Select({
            "select tt.ticket_id",
            "from trade_ticket tt,",
            "     net_trade n",
            "where case when tt.expire_date is not null then tt.expire_date else tt.effect_date end >= current_date",
            "  and find_in_set(tt.state,#{state})",
            "  and tt.venue_id = #{venueId}",
            "  and tt.cust_id is null",
            "  and tt.trade_id = n.trade_id",
            "  and n.net_user_id = #{netUserId}",
            "  and not exists(select 1 from trade_ticket_user ttu where ttu.ticket_id = tt.ticket_id and ttu.net_user_id = #{netUserId})",
            "  and exists(select 1 from trade_extra te where te.trade_id =tt.trade_id and te.extra_key='" + Constants.Extra.SECKILL_ID + "')",

            "union",
            "select tt.ticket_id",
            "from trade_ticket tt,",
            "     net_user_cards c",
            "where case when tt.expire_date is not null then tt.expire_date else tt.effect_date end >= current_date",
            "  and find_in_set(tt.state,#{state})",
            "  and tt.cust_id = cast(c.venue_cust_id as signed)",
            "  and tt.venue_id = #{venueId}",
            "  and c.net_user_id = #{netUserId}",
            "  and c.status = '1'",
            "  and not exists(select 1 from trade_ticket_user ttu where ttu.ticket_id = tt.ticket_id and ttu.net_user_id = #{netUserId})",
            "  and exists(select 1 from trade_extra te where te.trade_id =tt.trade_id and te.extra_key='" + Constants.Extra.SECKILL_ID + "')"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),

    })
    List<DataMap> selectValidSillEntryTicketsByNetUserId(@Param("venueId") Long venueId,
                                                         @Param("netUserId") Long netUserId,
                                                         @Param("state") String state);

    @Select({
            "select tt.ticket_id",
            "from trade_ticket tt,",
            "     net_trade n",
            "where case when tt.expire_date is not null then tt.expire_date else tt.effect_date end >= current_date",
            "  and find_in_set(tt.state,#{state})",
            "  and tt.venue_id = #{venueId}",
            "  and tt.cust_id is null",
            "  and tt.trade_id = n.trade_id",
            "  and n.net_user_id = #{netUserId}",
            "  and not exists(select 1 from trade_ticket_user ttu where ttu.ticket_id = tt.ticket_id and ttu.net_user_id = #{netUserId})",
            "  and not exists(select 1 from trade_extra te where te.trade_id =tt.trade_id and te.extra_key='" + Constants.Extra.SECKILL_ID + "')",

            "union",
            "select tt.ticket_id",
            "from trade_ticket tt,",
            "     net_user_cards c",
            "where case when tt.expire_date is not null then tt.expire_date else tt.effect_date end >= current_date",
            "  and find_in_set(tt.state,#{state})",
            "  and tt.cust_id = cast(c.venue_cust_id as signed)",
            "  and tt.venue_id = #{venueId}",
            "  and c.net_user_id = #{netUserId}",
            "  and c.status = '1'",
            "  and not exists(select 1 from trade_ticket_user ttu where ttu.ticket_id = tt.ticket_id and ttu.net_user_id = #{netUserId})",
            "  and not exists(select 1 from trade_extra te where te.trade_id =tt.trade_id and te.extra_key='" + Constants.Extra.SECKILL_ID + "')"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),

    })
    List<DataMap> selectValidEntryTicketsByNetUserIdNoSkill(@Param("venueId") Long venueId,
                                                         @Param("netUserId") Long netUserId,
                                                         @Param("state") String state);


    @Update({
            "<script>",
            "update trade_ticket",
            "<set>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"ecardNo != null\">ecard_no = #{ecardNo,jdbcType=VARCHAR},</if>",
            "<if test=\"custId != null\">cust_id = #{custId,jdbcType=BIGINT},</if>",
            "<if test=\"custName != null\">cust_name = #{custName, jdbcType=VARCHAR},</if>",
            "<if test=\"psptId != null\">pspt_id = #{psptId, jdbcType=VARCHAR},</if>",
            "<if test=\"depositId != null\">deposit_id = #{depositId,jdbcType=BIGINT},</if>",
            "<if test=\"productId != null\">product_id = #{productId,jdbcType=BIGINT},</if>",
            "</set>",
            "where trade_id = #{tradeId,jdbcType=BIGINT} and cust_id is null",
            "</script>"
    })
    int updateNoCustIdDataByTradeIdSelective(TradeTicket record);

    @Update({
            "update trade_ticket",
            "set state = #{state,jdbcType=VARCHAR}, ecard_no = #{ecardNo,jdbcType=VARCHAR}",
            "where trade_id = #{tradeId,jdbcType=BIGINT}"
    })
    int updateTicketStateAndEcardNo(@Param("tradeId") long tradeId, @Param("state") String state, @Param("ecardNo") String ecardNo);



    @Select({
            "select a.*" ,
            "from trade_ticket a left join trade_ticket_attr t on a.ticket_id = t.ticket_id and t.attr_code='perform_ticket_id'" ,
            "where t.attr_value = #{performTicketId}" ,
            "limit 1"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_id", property = "fieldId", jdbcType = JdbcType.BIGINT),
            @Result(column = "field_name", property = "fieldName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_segment", property = "startSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "end_segment", property = "endSegment", jdbcType = JdbcType.DECIMAL),
            @Result(column = "start_time", property = "startTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "end_time", property = "endTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pay_money", property = "payMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "discount", property = "discount", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "fetch_ticket_time", property = "fetchTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deposit_id", property = "depositId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_source_type", property = "ticketSourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_drawer", property = "ticketDrawer", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_collector", property = "ticketCollector", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_cancel_person", property = "ticketCancelPerson", jdbcType = JdbcType.BIGINT),
            @Result(column = "check_mode", property = "checkMode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "effect_date", property = "effectDate", jdbcType = JdbcType.DATE),
            @Result(column = "expire_date", property = "expireDate", jdbcType = JdbcType.DATE),
            @Result(column = "ecard_no", property = "ecardNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cust_id", property = "custId", jdbcType = JdbcType.BIGINT),
            @Result(column = "cust_name", property = "custName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_id", property = "productId", jdbcType = JdbcType.BIGINT),
            @Result(column = "ticket_type", property = "ticketType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_item", property = "priceItem", jdbcType = JdbcType.BIGINT),
            @Result(column = "round", property = "round", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coupon_amount", property = "couponAmount", jdbcType = JdbcType.INTEGER),
            @Result(column = "cancel_trade_id", property = "cancelTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_ticekt_id", property = "groupTicektId", jdbcType = JdbcType.BIGINT),
            @Result(column = "group_tag", property = "groupTag", jdbcType = JdbcType.CHAR),
            @Result(column = "prom_id", property = "promId", jdbcType = JdbcType.BIGINT),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "full_tag", property = "fullTag", jdbcType = JdbcType.CHAR),
            @Result(column = "regular_price", property = "regularPrice", jdbcType = JdbcType.INTEGER),
            @Result(column = "player_num", property = "playerNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "coupon_no", property = "couponNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "coach_id", property = "coachId", jdbcType = JdbcType.BIGINT),
            @Result(column = "chnl_cust_id", property = "chnlCustId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "refund_money", property = "refundMoney", jdbcType = JdbcType.BIGINT),
            @Result(column = "allowance", property = "allowance", jdbcType = JdbcType.INTEGER),
            @Result(column = "ticket_time_id", property = "ticketTimeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "old_state", property = "oldState", jdbcType = JdbcType.VARCHAR),
            @Result(column = "entry_ticket_id", property = "entryTicketId", jdbcType = JdbcType.BIGINT),
            @Result(column = "rent_tag", property = "rentTag", jdbcType = JdbcType.CHAR)
    })
    TradeTicket selectByPerformTicket(@Param("performTicketId") String performTicketId);


    @Select({
            "<script>",
            "select b.ticket_id,c.attr_value as perform_ticket_id",
            "from trade a, trade_ticket b left join trade_ticket_attr c on c.ticket_id = b.ticket_id and c.attr_code = 'perform_ticket_id', net_trade e",
            "where a.trade_id = b.trade_id",
            "and b.trade_id = e.trade_id",
            "and e.net_user_id = #{netUserId,jdbcType=BIGINT}",
            "and (b.state not in ('1', '9') or (b.state = '9' and now() &lt; a.expire_time))",
            "and c.attr_value in",
            "(select d.id from perform_new_ticket d where d.perform_id = #{performId,jdbcType=VARCHAR})",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "perform_ticket_id", property = "performTicketId", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectBoughtTicket(@Param("netUserId") Long netUserId, @Param("performId") String performId);


    @Select({
            "<script>",
            "select b.ticket_id,c.attr_value as perform_ticket_id,p.pspt_id,p.name as pspt_name" ,
            "from trade a, trade_ticket b" ,
            "    left join trade_ticket_attr c on c.ticket_id = b.ticket_id and c.attr_code = 'perform_ticket_id'," ,
            "    net_trade e,trade_ticket_person p" ,
            "where a.trade_id = b.trade_id" ,
            "and b.trade_id = e.trade_id" ,
            "and b.ticket_id = p.ticket_id" ,
            "and (b.state not in ('1', '9') or (b.state = '9' and now() &lt; a.expire_time))" ,
            "and c.attr_value in",
            "(select d.id from perform_new_ticket d where d.perform_id = #{performId,jdbcType=VARCHAR})",
            "and p.pspt_id in",
            "<foreach collection=\"psptIdList\" item=\"psptId\" open=\"(\" close=\")\" separator=\",\">",
            " #{psptId}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "perform_ticket_id", property = "performTicketId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_id", property = "psptId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pspt_name", property = "psptName", jdbcType = JdbcType.VARCHAR),
    })
    List<DataMap> selectBoughtTicketByPsptId(@Param("psptIdList") List<String> psptIdList, @Param("performId") String performId);

    @Select({
            "<script>",
            "select count(b.ticket_id) as sale_num," ,
            "IFNULL(SUM(CASE WHEN b.state = '3' THEN 1 ELSE 0 END),0) AS verified_off_num",
            "from trade a, trade_ticket b, trade_ticket_attr c" ,
            "where a.trade_id = b.trade_id",
            "and (b.state not in ('1', '9')",
            "and b.ticket_id = c.ticket_id ",
            "and c.attr_code = 'perform_ticket_id'",
            "and c.attr_value in",
            "<foreach collection=\"list\" item=\"performTicketId\" open=\"(\" close=\")\" separator=\",\">",
            " #{performTicketId}",
            "</foreach>",
            ")",
            "</script>"
    })
    @Results({
            @Result(column = "sale_num", property = "saleNum", jdbcType = JdbcType.INTEGER),
            @Result(column = "verified_off_num", property = "verifiedOffNum", jdbcType = JdbcType.INTEGER),
    })
    DataMap selectStatistics(@Param("list") List<Long> performTicketIdList);

    @Select({
            "<script>",
            "select t.trade_id,tt.ticket_id,tt.state,pt.name ticket_name,s.name site_name,tt.create_time,t.pay_tfee,ifnull(tt.check_ticket_time, '') check_ticket_time" ,
            "from trade t," ,
            "     trade_ticket tt" ,
            "         left join trade_ticket_attr tta on tta.ticket_id = tt.ticket_id and tta.attr_code = 'perform_ticket_id'," ,
            "     perform_new_ticket pt," ,
            "     perform p," ,
            "     site s" ,
            "where tt.trade_id = t.trade_id" ,
            "and tta.attr_value = pt.id" ,
            "and pt.perform_id = p.id" ,
            "and pt.site_id = s.id" ,
            "<if test=\"projectId != null and projectId != ''\">and p.project_id = #{projectId,jdbcType=BIGINT}</if>",
            "<if test=\"stockId != null\">and pt.stock_id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"performId != null and performId != ''\">and p.id = #{performId,jdbcType=VARCHAR}</if>",
            "group by tt.ticket_id",
            "</script>"
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "site_name", property = "siteName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "pay_tfee", property = "payTfee", jdbcType = JdbcType.DECIMAL),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP)
    })
    List<DataMap> selectPerformTickets(@Param("projectId") String projectId,
                                       @Param("performId") String performId,
                                       @Param("stockId") Long stockId,
                                       RowBounds rowBounds);

    @Select({
            "<script>",
            "select t.trade_id,tt.ticket_id,tt.ticket_no,tt.state,pt.name ticket_name,s.name site_name,tt.create_time,t.pay_tfee,ifnull(tt.check_ticket_time, '') check_ticket_time" ,
            "from trade t" ,
            "         left join trade t2 on t.trade_id_b = t2.trade_id" ,
            "         left join trade_extra t3 on t2.trade_id = t3.trade_id," ,
            "     trade_ticket tt" ,
            "         left join trade_ticket_attr tta on tta.ticket_id = tt.ticket_id and tta.attr_code = 'perform_ticket_id'" ,
            "         left join trade_ticket_person ttp on tt.ticket_id = ttp.ticket_id,",
            "     perform_new_ticket pt," ,
            "     perform p," ,
            "     site s" ,
            "where tt.trade_id = t.trade_id" ,
            "and tta.attr_value = pt.id" ,
            "and pt.perform_id = p.id" ,
            "and pt.site_id = s.id" ,
            "and t3.extra_key = 'perform_team_ticket_send_id' and t3.value = #{recordId,jdbcType=BIGINT}" ,
            "<if test=\"keyword != null and keyword != ''\">and (ttp.name like concat('%', #{keyword,jdbcType=VARCHAR},'%') or ttp.pspt_id like concat('%', #{keyword,jdbcType=VARCHAR},'%') or ttp.mobile_num like concat('%', #{keyword,jdbcType=VARCHAR},'%'))</if>",
            "<if test=\"state != null and state != ''\">and tt.state = #{state,jdbcType=VARCHAR}</if>",
            "group by tt.ticket_id",
            "</script>"
    })
    @Results({
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "site_name", property = "siteName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "pay_tfee", property = "payTfee", jdbcType = JdbcType.DECIMAL),
            @Result(column = "check_ticket_time", property = "checkTicketTime", jdbcType = JdbcType.TIMESTAMP)
    })
    List<DataMap> selectPerformTeamTickets(@Param("recordId") String recordId,
                                           @Param("keyword") String keyword,
                                           @Param("state") String state,
                                           RowBounds rowBounds);


    @Select({
            "select",
            "a.ticket_id, a.trade_id, a.state, d.id perform_id",
            "from trade_ticket a" ,
            "left join trade_ticket_attr b on b.ticket_id = a.ticket_id and b.attr_code = 'perform_ticket_id'" ,
            "left join perform_new_ticket c on b.attr_value = c.id",
            "left join perform d on c.perform_id = d.id",
            "where a.ticket_id = #{ticketId,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "perform_id", property = "performId", jdbcType = JdbcType.BIGINT)
    })
    DataMap selectTeamTicketInfo(@Param("ticketId") Long ticketId);

    @Select({
            "<script>",
            "select tt.ticket_id, tt.ticket_no, p.name perform_name, pt.name ticket_name, ps.name stock_name, s.name site_name, t.pay_tfee price" ,
            "from trade t" ,
            "         left join trade t2 on t.trade_id_b = t2.trade_id" ,
            "         left join trade_extra t3 on t2.trade_id = t3.trade_id," ,
            "     trade_ticket tt" ,
            "         left join trade_ticket_attr tta on tta.ticket_id = tt.ticket_id and tta.attr_code = 'perform_ticket_id'" ,
            "         left join trade_ticket_person ttp on tt.ticket_id = ttp.ticket_id,",
            "     perform_new_ticket pt," ,
            "     perform_stock ps," ,
            "     perform p," ,
            "     site s" ,
            "where tt.trade_id = t.trade_id" ,
            "and tta.attr_value = pt.id" ,
            "and pt.perform_id = p.id" ,
            "and ps.perform_id = p.id" ,
            "and pt.site_id = s.id" ,
            "and t3.extra_key = 'perform_team_ticket_send_id' and t3.value = #{recordId,jdbcType=BIGINT}" ,
            "<if test=\"keyword != null and keyword != ''\">and (ttp.name like concat('%', #{keyword,jdbcType=VARCHAR},'%') or ttp.pspt_id like concat('%', #{keyword,jdbcType=VARCHAR},'%') or ttp.mobile_num like concat('%', #{keyword,jdbcType=VARCHAR},'%'))</if>",
            "<if test=\"state != null and state != ''\">and tt.state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketIds != null and ticketIds.size() > 0\">",
            "   and tt.ticket_id in",
            "   <foreach collection='ticketIds' item='id' open='(' separator=',' close=')'>",
            "       #{id,jdbcType=BIGINT}",
            "   </foreach>",
            "</if>",
            "group by tt.ticket_id",
            "</script>"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "ticket_no", property = "ticketNo", jdbcType = JdbcType.VARCHAR),
            @Result(column = "perform_name", property = "performName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ticket_name", property = "ticketName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "stock_name", property = "stockName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "site_name", property = "siteName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "price", property = "price", jdbcType = JdbcType.DECIMAL)
    })
    List<Map<String, Object>> queryQrcodes(@Param("recordId") String recordId,
                                           @Param("keyword") String keyword,
                                           @Param("state") String state,
                                           @Param("ticketIds") List<Long> ticketIds);
}
