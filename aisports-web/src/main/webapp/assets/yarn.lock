# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


abbrev@1, abbrev@~1.0.9:
  version "1.0.9"
  resolved "http://registry.npm.taobao.org/abbrev/download/abbrev-1.0.9.tgz#91b4792588a7738c25f35dd6f63752a2f8776135"

accepts@1.3.3, accepts@~1.3.3:
  version "1.3.3"
  resolved "http://registry.npm.taobao.org/accepts/download/accepts-1.3.3.tgz#c3ca7434938648c3e0d9c1e328dd68b622c284ca"
  dependencies:
    mime-types "~2.1.11"
    negotiator "0.6.1"

acorn-jsx@^3.0.0, acorn-jsx@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/acorn-jsx/download/acorn-jsx-3.0.1.tgz#afdf9488fb1ecefc8348f6fb22f464e32a58b36b"
  dependencies:
    acorn "^3.0.4"

acorn-object-spread@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/acorn-object-spread/download/acorn-object-spread-1.0.0.tgz#48ead0f4a8eb16995a17a0db9ffc6acaada4ba68"
  dependencies:
    acorn "^3.1.0"

acorn@^3.0.0, acorn@^3.0.4, acorn@^3.1.0:
  version "3.3.0"
  resolved "http://registry.npm.taobao.org/acorn/download/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"

acorn@^5.0.1:
  version "5.0.3"
  resolved "http://registry.npm.taobao.org/acorn/download/acorn-5.0.3.tgz#c460df08491463f028ccb82eab3730bf01087b3d"

after@0.8.1:
  version "0.8.1"
  resolved "http://registry.npm.taobao.org/after/download/after-0.8.1.tgz#ab5d4fb883f596816d3515f8f791c0af486dd627"

ajv-keywords@^1.0.0:
  version "1.5.1"
  resolved "http://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-1.5.1.tgz#314dd0a4b3368fad3dfcdc54ede6171b886daf3c"

ajv@^4.7.0, ajv@^4.9.1:
  version "4.11.8"
  resolved "http://registry.npm.taobao.org/ajv/download/ajv-4.11.8.tgz#82ffb02b29e662ae53bdc20af15947706739c536"
  dependencies:
    co "^4.6.0"
    json-stable-stringify "^1.0.1"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/align-text/download/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

alphanum-sort@^1.0.1, alphanum-sort@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz#97a1119649b211ad33691d9f9f486a8ec9fbe0a3"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/amdefine/download/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"

ansi-escapes@^1.1.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/ansi-escapes/download/ansi-escapes-1.4.0.tgz#d3a8a83b319aa67793662b13e761c7911422306e"

ansi-regex@^0.2.0, ansi-regex@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-0.2.1.tgz#0d8e946967a3d8143f93e24e298525fc1b2235f9"

ansi-regex@^1.0.0, ansi-regex@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-1.1.1.tgz#41c847194646375e6a1a5d10c3ca054ef9fc980d"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-styles@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-1.1.0.tgz#eaecbf66cd706882760b2f4691582b8f55d7a7de"

ansi-styles@^2.0.1, ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-1.0.0.tgz#cb102df1c56f5123eab8b67cd7b98027a0279178"

ansicolors@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.taobao.org/ansicolors/download/ansicolors-0.3.2.tgz#665597de86a9ffe3aa9bfbe6cae5c6ea426b4979"

ansistyles@~0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/ansistyles/download/ansistyles-0.1.3.tgz#5de60415bda071bb37127854c864f41b23254539"

anymatch@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/anymatch/download/anymatch-1.3.0.tgz#a3e52fa39168c825ff57b0248126ce5a8ff95507"
  dependencies:
    arrify "^1.0.0"
    micromatch "^2.1.5"

aproba@^1.0.3, aproba@~1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/aproba/download/aproba-1.0.4.tgz#2713680775e7614c8ba186c065d4e2e52d1072c0"

archive-type@^3.0.0, archive-type@^3.0.1:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/archive-type/download/archive-type-3.2.0.tgz#9cd9c006957ebe95fadad5bd6098942a813737f6"
  dependencies:
    file-type "^3.1.0"

archy@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/archy/download/archy-1.0.0.tgz#f9c8c13757cc1dd7bc379ac77b2c62a5c2868c40"

are-we-there-yet@~1.1.2:
  version "1.1.4"
  resolved "http://registry.npm.taobao.org/are-we-there-yet/download/are-we-there-yet-1.1.4.tgz#bb5dca382bb94f05e15194373d16fd3ba1ca110d"
  dependencies:
    delegates "^1.0.0"
    readable-stream "^2.0.6"

argparse@^1.0.7:
  version "1.0.9"
  resolved "http://registry.npm.taobao.org/argparse/download/argparse-1.0.9.tgz#73d83bc263f86e97f8cc4f6bae1b0e90a7d22c86"
  dependencies:
    sprintf-js "~1.0.2"

"argparse@~ 0.1.11":
  version "0.1.16"
  resolved "http://registry.npm.taobao.org/argparse/download/argparse-0.1.16.tgz#cfd01e0fbba3d6caed049fbd758d40f65196f57c"
  dependencies:
    underscore "~1.7.0"
    underscore.string "~2.4.0"

arr-diff@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/arr-diff/download/arr-diff-2.0.0.tgz#8f3b827f955a8bd669697e4a4256ac3ceae356cf"
  dependencies:
    arr-flatten "^1.0.1"

arr-flatten@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.0.3.tgz#a274ed85ac08849b6bd7847c4580745dc51adfb1"

array-differ@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/array-differ/download/array-differ-1.0.0.tgz#eff52e3758249d33be402b8bb8e564bb2b5d4031"

array-find-index@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/array-find-index/download/array-find-index-1.0.2.tgz#df010aa1287e164bbda6f9723b0a96a1ec4187a1"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"

array-index@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/array-index/download/array-index-1.0.0.tgz#ec56a749ee103e4e08c790b9c353df16055b97f9"
  dependencies:
    debug "^2.2.0"
    es6-symbol "^3.0.2"

array-union@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.0, array-uniq@^1.0.1, array-uniq@^1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/array-uniq/download/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"

array-unique@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/array-unique/download/array-unique-0.2.1.tgz#a1d97ccafcbc2625cc70fadceb36a50c58b01a53"

array.prototype.find@^2.0.1:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/array.prototype.find/download/array.prototype.find-2.0.4.tgz#556a5c5362c08648323ddaeb9de9d14bc1864c90"
  dependencies:
    define-properties "^1.1.2"
    es-abstract "^1.7.0"

arraybuffer.slice@0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.taobao.org/arraybuffer.slice/download/arraybuffer.slice-0.0.6.tgz#f33b2159f0532a3f3107a272c0ccfbd1ad2979ca"

arrify@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/arrify/download/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"

asap@^2.0.0, asap@~2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.taobao.org/asap/download/asap-2.0.5.tgz#522765b50c3510490e52d7dcfe085ef9ba96958f"

asn1@~0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.taobao.org/asn1/download/asn1-0.2.3.tgz#dac8787713c9966849fc8180777ebe9c1ddf3b86"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"

assert-plus@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/assert-plus/download/assert-plus-0.2.0.tgz#d74e1b87e7affc0db8aadb7021f3fe48101ab234"

assert@^1.1.1:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/assert/download/assert-1.4.1.tgz#99912d591836b5a6f5b345c0f07eefc08fc65d91"
  dependencies:
    util "0.10.3"

ast-types@0.9.6:
  version "0.9.6"
  resolved "http://registry.npm.taobao.org/ast-types/download/ast-types-0.9.6.tgz#102c9e9e9005d3e7e3829bf0c4fa24ee862ee9b9"

async-each-series@0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/async-each-series/download/async-each-series-0.1.1.tgz#7617c1917401fd8ca4a28aadce3dbae98afeb432"

async-each-series@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/async-each-series/download/async-each-series-1.1.0.tgz#f42fd8155d38f21a5b8ea07c28e063ed1700b138"

async-each@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/async-each/download/async-each-1.0.1.tgz#19d386a1d9edc6e7c1c85d388aedbcc56d33602d"

async-foreach@^0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/async-foreach/download/async-foreach-0.1.3.tgz#36121f845c0578172de419a97dbeb1d16ec34542"

async-validator@1.6.9:
  version "1.6.9"
  resolved "http://registry.npm.taobao.org/async-validator/download/async-validator-1.6.9.tgz#a8309daa8b83421cdbd4628e026d6abb25192d34"

async@1.5.2, async@^1.3.0, async@^1.5.2:
  version "1.5.2"
  resolved "http://registry.npm.taobao.org/async/download/async-1.5.2.tgz#ec6a61ae56480c0c3cb241c95618e20892f9672a"

async@^0.9.0:
  version "0.9.2"
  resolved "http://registry.npm.taobao.org/async/download/async-0.9.2.tgz#aea74d5e61c1f899613bf64bda66d4c78f2fd17d"

async@^2.0.1:
  version "2.4.1"
  resolved "http://registry.npm.taobao.org/async/download/async-2.4.1.tgz#62a56b279c98a11d0987096a01cc3eeb8eb7bbd7"
  dependencies:
    lodash "^4.14.0"

async@~0.1.22:
  version "0.1.22"
  resolved "http://registry.npm.taobao.org/async/download/async-0.1.22.tgz#0fc1aaa088a0e3ef0ebe2d8831bab0dcf8845061"

async@~0.2.6, async@~0.2.9:
  version "0.2.10"
  resolved "http://registry.npm.taobao.org/async/download/async-0.2.10.tgz#b6bbe0b0674b9d719708ca38de8c237cb526c3d1"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

autoprefixer-core@^5.1.7:
  version "5.2.1"
  resolved "http://registry.npm.taobao.org/autoprefixer-core/download/autoprefixer-core-5.2.1.tgz#e640c414ae419aae21c1ad43c8ea0f3db82a566d"
  dependencies:
    browserslist "~0.4.0"
    caniuse-db "^1.0.30000214"
    num2fraction "^1.1.0"
    postcss "~4.1.12"

autoprefixer@^6.0.3, autoprefixer@^6.3.1:
  version "6.7.7"
  resolved "http://registry.npm.taobao.org/autoprefixer/download/autoprefixer-6.7.7.tgz#1dbd1c835658e35ce3f9984099db00585c782014"
  dependencies:
    browserslist "^1.7.6"
    caniuse-db "^1.0.30000634"
    normalize-range "^0.1.2"
    num2fraction "^1.2.2"
    postcss "^5.2.16"
    postcss-value-parser "^3.2.3"

aws-sign2@~0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.6.0.tgz#14342dd38dbcc94d0e5b87d763cd63612c0e794f"

aws4@^1.2.1:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/aws4/download/aws4-1.6.0.tgz#83ef5ca860b2b32e4a0deedee8c771b9db57471e"

babel-code-frame@^6.16.0, babel-code-frame@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-code-frame/download/babel-code-frame-6.22.0.tgz#027620bee567a88c32561574e7fd0801d33118e4"
  dependencies:
    chalk "^1.1.0"
    esutils "^2.0.2"
    js-tokens "^3.0.0"

babel-core@^6.14.0, babel-core@^6.24.1:
  version "6.25.0"
  resolved "http://registry.npm.taobao.org/babel-core/download/babel-core-6.25.0.tgz#7dd42b0463c742e9d5296deb3ec67a9322dad729"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-generator "^6.25.0"
    babel-helpers "^6.24.1"
    babel-messages "^6.23.0"
    babel-register "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.25.0"
    babel-traverse "^6.25.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    convert-source-map "^1.1.0"
    debug "^2.1.1"
    json5 "^0.5.0"
    lodash "^4.2.0"
    minimatch "^3.0.2"
    path-is-absolute "^1.0.0"
    private "^0.1.6"
    slash "^1.0.0"
    source-map "^0.5.0"

babel-generator@^6.25.0:
  version "6.25.0"
  resolved "http://registry.npm.taobao.org/babel-generator/download/babel-generator-6.25.0.tgz#33a1af70d5f2890aeb465a4a7793c1df6a9ea9fc"
  dependencies:
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-types "^6.25.0"
    detect-indent "^4.0.0"
    jsesc "^1.3.0"
    lodash "^4.2.0"
    source-map "^0.5.0"
    trim-right "^1.0.1"

babel-helper-bindify-decorators@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-bindify-decorators/download/babel-helper-bindify-decorators-6.24.1.tgz#14c19e5f142d7b47f19a52431e52b1ccbc40a330"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-builder-binary-assignment-operator-visitor@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-builder-binary-assignment-operator-visitor/download/babel-helper-builder-binary-assignment-operator-visitor-6.24.1.tgz#cce4517ada356f4220bcae8a02c2b346f9a56664"
  dependencies:
    babel-helper-explode-assignable-expression "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-call-delegate@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-call-delegate/download/babel-helper-call-delegate-6.24.1.tgz#ece6aacddc76e41c3461f88bfc575bd0daa2df8d"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-define-map@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-define-map/download/babel-helper-define-map-6.24.1.tgz#7a9747f258d8947d32d515f6aa1c7bd02204a080"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"
    lodash "^4.2.0"

babel-helper-explode-assignable-expression@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-explode-assignable-expression/download/babel-helper-explode-assignable-expression-6.24.1.tgz#f25b82cf7dc10433c55f70592d5746400ac22caa"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-explode-class@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-explode-class/download/babel-helper-explode-class-6.24.1.tgz#7dc2a3910dee007056e1e31d640ced3d54eaa9eb"
  dependencies:
    babel-helper-bindify-decorators "^6.24.1"
    babel-runtime "^6.22.0"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-function-name@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-function-name/download/babel-helper-function-name-6.24.1.tgz#d3475b8c03ed98242a25b48351ab18399d3580a9"
  dependencies:
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-get-function-arity@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-get-function-arity/download/babel-helper-get-function-arity-6.24.1.tgz#8f7782aa93407c41d3aa50908f89b031b1b6853d"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-hoist-variables@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-hoist-variables/download/babel-helper-hoist-variables-6.24.1.tgz#1ecb27689c9d25513eadbc9914a73f5408be7a76"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-optimise-call-expression@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-optimise-call-expression/download/babel-helper-optimise-call-expression-6.24.1.tgz#f7a13427ba9f73f8f4fa993c54a97882d1244257"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-helper-regex@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-regex/download/babel-helper-regex-6.24.1.tgz#d36e22fab1008d79d88648e32116868128456ce8"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"
    lodash "^4.2.0"

babel-helper-remap-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-remap-async-to-generator/download/babel-helper-remap-async-to-generator-6.24.1.tgz#5ec581827ad723fecdd381f1c928390676e4551b"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-replace-supers@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helper-replace-supers/download/babel-helper-replace-supers-6.24.1.tgz#bf6dbfe43938d17369a213ca8a8bf74b6a90ab1a"
  dependencies:
    babel-helper-optimise-call-expression "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-helper-vue-jsx-merge-props@^2.0.0, babel-helper-vue-jsx-merge-props@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/babel-helper-vue-jsx-merge-props/download/babel-helper-vue-jsx-merge-props-2.0.2.tgz#aceb1c373588279e2755ea1cfd35c22394fd33f8"

babel-helpers@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-helpers/download/babel-helpers-6.24.1.tgz#3471de9caec388e5c850e597e58a26ddf37602b2"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-loader@^6.2.5:
  version "6.4.1"
  resolved "http://registry.npm.taobao.org/babel-loader/download/babel-loader-6.4.1.tgz#0b34112d5b0748a8dcdbf51acf6f9bd42d50b8ca"
  dependencies:
    find-cache-dir "^0.1.1"
    loader-utils "^0.2.16"
    mkdirp "^0.5.1"
    object-assign "^4.0.1"

babel-messages@^6.23.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-messages/download/babel-messages-6.23.0.tgz#f3cdf4703858035b2a2951c6ec5edf6c62f2630e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-check-es2015-constants@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-check-es2015-constants/download/babel-plugin-check-es2015-constants-6.22.0.tgz#35157b101426fd2ffd3da3f75c7d1e91835bbf8a"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-syntax-async-functions@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-async-functions/download/babel-plugin-syntax-async-functions-6.13.0.tgz#cad9cad1191b5ad634bf30ae0872391e0647be95"

babel-plugin-syntax-async-generators@^6.5.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-async-generators/download/babel-plugin-syntax-async-generators-6.13.0.tgz#6bc963ebb16eccbae6b92b596eb7f35c342a8b9a"

babel-plugin-syntax-class-properties@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-class-properties/download/babel-plugin-syntax-class-properties-6.13.0.tgz#d7eb23b79a317f8543962c505b827c7d6cac27de"

babel-plugin-syntax-decorators@^6.13.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-decorators/download/babel-plugin-syntax-decorators-6.13.0.tgz#312563b4dbde3cc806cee3e416cceeaddd11ac0b"

babel-plugin-syntax-dynamic-import@^6.18.0:
  version "6.18.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-dynamic-import/download/babel-plugin-syntax-dynamic-import-6.18.0.tgz#8d6a26229c83745a9982a441051572caa179b1da"

babel-plugin-syntax-exponentiation-operator@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-exponentiation-operator/download/babel-plugin-syntax-exponentiation-operator-6.13.0.tgz#9ee7e8337290da95288201a6a57f4170317830de"

babel-plugin-syntax-jsx@^6.18.0:
  version "6.18.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-jsx/download/babel-plugin-syntax-jsx-6.18.0.tgz#0af32a9a6e13ca7a3fd5069e62d7b0f58d0d8946"

babel-plugin-syntax-object-rest-spread@^6.8.0:
  version "6.13.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-object-rest-spread/download/babel-plugin-syntax-object-rest-spread-6.13.0.tgz#fd6536f2bce13836ffa3a5458c4903a597bb3bf5"

babel-plugin-syntax-trailing-function-commas@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-syntax-trailing-function-commas/download/babel-plugin-syntax-trailing-function-commas-6.22.0.tgz#ba0360937f8d06e40180a43fe0d5616fff532cf3"

babel-plugin-transform-async-generator-functions@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-async-generator-functions/download/babel-plugin-transform-async-generator-functions-6.24.1.tgz#f058900145fd3e9907a6ddf28da59f215258a5db"
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-generators "^6.5.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-async-to-generator@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-async-to-generator/download/babel-plugin-transform-async-to-generator-6.24.1.tgz#6536e378aff6cb1d5517ac0e40eb3e9fc8d08761"
  dependencies:
    babel-helper-remap-async-to-generator "^6.24.1"
    babel-plugin-syntax-async-functions "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-class-properties@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-class-properties/download/babel-plugin-transform-class-properties-6.24.1.tgz#6a79763ea61d33d36f37b611aa9def81a81b46ac"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-plugin-syntax-class-properties "^6.8.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-decorators@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-decorators/download/babel-plugin-transform-decorators-6.24.1.tgz#788013d8f8c6b5222bdf7b344390dfd77569e24d"
  dependencies:
    babel-helper-explode-class "^6.24.1"
    babel-plugin-syntax-decorators "^6.13.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-arrow-functions@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-arrow-functions/download/babel-plugin-transform-es2015-arrow-functions-6.22.0.tgz#452692cb711d5f79dc7f85e440ce41b9f244d221"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoped-functions@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoped-functions/download/babel-plugin-transform-es2015-block-scoped-functions-6.22.0.tgz#bbc51b49f964d70cb8d8e0b94e820246ce3a6141"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-block-scoping@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-block-scoping/download/babel-plugin-transform-es2015-block-scoping-6.24.1.tgz#76c295dc3a4741b1665adfd3167215dcff32a576"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"
    lodash "^4.2.0"

babel-plugin-transform-es2015-classes@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-classes/download/babel-plugin-transform-es2015-classes-6.24.1.tgz#5a4c58a50c9c9461e564b4b2a3bfabc97a2584db"
  dependencies:
    babel-helper-define-map "^6.24.1"
    babel-helper-function-name "^6.24.1"
    babel-helper-optimise-call-expression "^6.24.1"
    babel-helper-replace-supers "^6.24.1"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-computed-properties@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-computed-properties/download/babel-plugin-transform-es2015-computed-properties-6.24.1.tgz#6fe2a8d16895d5634f4cd999b6d3480a308159b3"
  dependencies:
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-destructuring@^6.22.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-destructuring/download/babel-plugin-transform-es2015-destructuring-6.23.0.tgz#997bb1f1ab967f682d2b0876fe358d60e765c56d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-duplicate-keys@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-duplicate-keys/download/babel-plugin-transform-es2015-duplicate-keys-6.24.1.tgz#73eb3d310ca969e3ef9ec91c53741a6f1576423e"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-for-of@^6.22.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-for-of/download/babel-plugin-transform-es2015-for-of-6.23.0.tgz#f47c95b2b613df1d3ecc2fdb7573623c75248691"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-function-name@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-function-name/download/babel-plugin-transform-es2015-function-name-6.24.1.tgz#834c89853bc36b1af0f3a4c5dbaa94fd8eacaa8b"
  dependencies:
    babel-helper-function-name "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-literals@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-literals/download/babel-plugin-transform-es2015-literals-6.22.0.tgz#4f54a02d6cd66cf915280019a31d31925377ca2e"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-modules-amd@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-amd/download/babel-plugin-transform-es2015-modules-amd-6.24.1.tgz#3b3e54017239842d6d19c3011c4bd2f00a00d154"
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-commonjs@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-commonjs/download/babel-plugin-transform-es2015-modules-commonjs-6.24.1.tgz#d3e310b40ef664a36622200097c6d440298f2bfe"
  dependencies:
    babel-plugin-transform-strict-mode "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-modules-systemjs@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-systemjs/download/babel-plugin-transform-es2015-modules-systemjs-6.24.1.tgz#ff89a142b9119a906195f5f106ecf305d9407d23"
  dependencies:
    babel-helper-hoist-variables "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-modules-umd@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-modules-umd/download/babel-plugin-transform-es2015-modules-umd-6.24.1.tgz#ac997e6285cd18ed6176adb607d602344ad38468"
  dependencies:
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"

babel-plugin-transform-es2015-object-super@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-object-super/download/babel-plugin-transform-es2015-object-super-6.24.1.tgz#24cef69ae21cb83a7f8603dad021f572eb278f8d"
  dependencies:
    babel-helper-replace-supers "^6.24.1"
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-parameters@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-parameters/download/babel-plugin-transform-es2015-parameters-6.24.1.tgz#57ac351ab49caf14a97cd13b09f66fdf0a625f2b"
  dependencies:
    babel-helper-call-delegate "^6.24.1"
    babel-helper-get-function-arity "^6.24.1"
    babel-runtime "^6.22.0"
    babel-template "^6.24.1"
    babel-traverse "^6.24.1"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-shorthand-properties@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-shorthand-properties/download/babel-plugin-transform-es2015-shorthand-properties-6.24.1.tgz#24f875d6721c87661bbd99a4622e51f14de38aa0"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-spread@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-spread/download/babel-plugin-transform-es2015-spread-6.22.0.tgz#d6d68a99f89aedc4536c81a542e8dd9f1746f8d1"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-sticky-regex@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-sticky-regex/download/babel-plugin-transform-es2015-sticky-regex-6.24.1.tgz#00c1cdb1aca71112cdf0cf6126c2ed6b457ccdbc"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-es2015-template-literals@^6.22.0:
  version "6.22.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-template-literals/download/babel-plugin-transform-es2015-template-literals-6.22.0.tgz#a84b3450f7e9f8f1f6839d6d687da84bb1236d8d"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-typeof-symbol@^6.22.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-typeof-symbol/download/babel-plugin-transform-es2015-typeof-symbol-6.23.0.tgz#dec09f1cddff94b52ac73d505c84df59dcceb372"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-es2015-unicode-regex@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-es2015-unicode-regex/download/babel-plugin-transform-es2015-unicode-regex-6.24.1.tgz#d38b12f42ea7323f729387f18a7c5ae1faeb35e9"
  dependencies:
    babel-helper-regex "^6.24.1"
    babel-runtime "^6.22.0"
    regexpu-core "^2.0.0"

babel-plugin-transform-exponentiation-operator@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-exponentiation-operator/download/babel-plugin-transform-exponentiation-operator-6.24.1.tgz#2ab0c9c7f3098fa48907772bb813fe41e8de3a0e"
  dependencies:
    babel-helper-builder-binary-assignment-operator-visitor "^6.24.1"
    babel-plugin-syntax-exponentiation-operator "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-object-rest-spread@^6.22.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-object-rest-spread/download/babel-plugin-transform-object-rest-spread-6.23.0.tgz#875d6bc9be761c58a2ae3feee5dc4895d8c7f921"
  dependencies:
    babel-plugin-syntax-object-rest-spread "^6.8.0"
    babel-runtime "^6.22.0"

babel-plugin-transform-regenerator@^6.14.0, babel-plugin-transform-regenerator@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-regenerator/download/babel-plugin-transform-regenerator-6.24.1.tgz#b8da305ad43c3c99b4848e4fe4037b770d23c418"
  dependencies:
    regenerator-transform "0.9.11"

babel-plugin-transform-runtime@^6.12.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-runtime/download/babel-plugin-transform-runtime-6.23.0.tgz#88490d446502ea9b8e7efb0fe09ec4d99479b1ee"
  dependencies:
    babel-runtime "^6.22.0"

babel-plugin-transform-strict-mode@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-strict-mode/download/babel-plugin-transform-strict-mode-6.24.1.tgz#d5faf7aa578a65bbe591cf5edae04a0c67020758"
  dependencies:
    babel-runtime "^6.22.0"
    babel-types "^6.24.1"

babel-plugin-transform-vue-jsx@^3.4.1:
  version "3.4.3"
  resolved "http://registry.npm.taobao.org/babel-plugin-transform-vue-jsx/download/babel-plugin-transform-vue-jsx-3.4.3.tgz#de57d8dd7d619333c981867728f3e6fdf68982ff"
  dependencies:
    esutils "^2.0.2"

babel-preset-es2015@^6.14.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-preset-es2015/download/babel-preset-es2015-6.24.1.tgz#d44050d6bc2c9feea702aaf38d727a0210538939"
  dependencies:
    babel-plugin-check-es2015-constants "^6.22.0"
    babel-plugin-transform-es2015-arrow-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoped-functions "^6.22.0"
    babel-plugin-transform-es2015-block-scoping "^6.24.1"
    babel-plugin-transform-es2015-classes "^6.24.1"
    babel-plugin-transform-es2015-computed-properties "^6.24.1"
    babel-plugin-transform-es2015-destructuring "^6.22.0"
    babel-plugin-transform-es2015-duplicate-keys "^6.24.1"
    babel-plugin-transform-es2015-for-of "^6.22.0"
    babel-plugin-transform-es2015-function-name "^6.24.1"
    babel-plugin-transform-es2015-literals "^6.22.0"
    babel-plugin-transform-es2015-modules-amd "^6.24.1"
    babel-plugin-transform-es2015-modules-commonjs "^6.24.1"
    babel-plugin-transform-es2015-modules-systemjs "^6.24.1"
    babel-plugin-transform-es2015-modules-umd "^6.24.1"
    babel-plugin-transform-es2015-object-super "^6.24.1"
    babel-plugin-transform-es2015-parameters "^6.24.1"
    babel-plugin-transform-es2015-shorthand-properties "^6.24.1"
    babel-plugin-transform-es2015-spread "^6.22.0"
    babel-plugin-transform-es2015-sticky-regex "^6.24.1"
    babel-plugin-transform-es2015-template-literals "^6.22.0"
    babel-plugin-transform-es2015-typeof-symbol "^6.22.0"
    babel-plugin-transform-es2015-unicode-regex "^6.24.1"
    babel-plugin-transform-regenerator "^6.24.1"

babel-preset-stage-2@^6.13.0:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-preset-stage-2/download/babel-preset-stage-2-6.24.1.tgz#d9e2960fb3d71187f0e64eec62bc07767219bdc1"
  dependencies:
    babel-plugin-syntax-dynamic-import "^6.18.0"
    babel-plugin-transform-class-properties "^6.24.1"
    babel-plugin-transform-decorators "^6.24.1"
    babel-preset-stage-3 "^6.24.1"

babel-preset-stage-3@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-preset-stage-3/download/babel-preset-stage-3-6.24.1.tgz#836ada0a9e7a7fa37cb138fb9326f87934a48395"
  dependencies:
    babel-plugin-syntax-trailing-function-commas "^6.22.0"
    babel-plugin-transform-async-generator-functions "^6.24.1"
    babel-plugin-transform-async-to-generator "^6.24.1"
    babel-plugin-transform-exponentiation-operator "^6.24.1"
    babel-plugin-transform-object-rest-spread "^6.22.0"

babel-register@^6.24.1:
  version "6.24.1"
  resolved "http://registry.npm.taobao.org/babel-register/download/babel-register-6.24.1.tgz#7e10e13a2f71065bdfad5a1787ba45bca6ded75f"
  dependencies:
    babel-core "^6.24.1"
    babel-runtime "^6.22.0"
    core-js "^2.4.0"
    home-or-tmp "^2.0.0"
    lodash "^4.2.0"
    mkdirp "^0.5.1"
    source-map-support "^0.4.2"

babel-runtime@^6.11.6, babel-runtime@^6.18.0, babel-runtime@^6.22.0:
  version "6.23.0"
  resolved "http://registry.npm.taobao.org/babel-runtime/download/babel-runtime-6.23.0.tgz#0a9489f144de70efb3ce4300accdb329e2fc543b"
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.10.0"

babel-template@^6.24.1, babel-template@^6.25.0:
  version "6.25.0"
  resolved "http://registry.npm.taobao.org/babel-template/download/babel-template-6.25.0.tgz#665241166b7c2aa4c619d71e192969552b10c071"
  dependencies:
    babel-runtime "^6.22.0"
    babel-traverse "^6.25.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    lodash "^4.2.0"

babel-traverse@^6.24.1, babel-traverse@^6.25.0:
  version "6.25.0"
  resolved "http://registry.npm.taobao.org/babel-traverse/download/babel-traverse-6.25.0.tgz#2257497e2fcd19b89edc13c4c91381f9512496f1"
  dependencies:
    babel-code-frame "^6.22.0"
    babel-messages "^6.23.0"
    babel-runtime "^6.22.0"
    babel-types "^6.25.0"
    babylon "^6.17.2"
    debug "^2.2.0"
    globals "^9.0.0"
    invariant "^2.2.0"
    lodash "^4.2.0"

babel-types@^6.19.0, babel-types@^6.24.1, babel-types@^6.25.0:
  version "6.25.0"
  resolved "http://registry.npm.taobao.org/babel-types/download/babel-types-6.25.0.tgz#70afb248d5660e5d18f811d91c8303b54134a18e"
  dependencies:
    babel-runtime "^6.22.0"
    esutils "^2.0.2"
    lodash "^4.2.0"
    to-fast-properties "^1.0.1"

babylon@^6.17.2:
  version "6.17.4"
  resolved "http://registry.npm.taobao.org/babylon/download/babylon-6.17.4.tgz#3e8b7402b88d22c3423e137a1577883b15ff869a"

backo2@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/backo2/download/backo2-1.0.2.tgz#31ab1ac8b129363463e35b3ebb69f4dfcfba7947"

balanced-match@^0.4.2:
  version "0.4.2"
  resolved "http://registry.npm.taobao.org/balanced-match/download/balanced-match-0.4.2.tgz#cb3f3e3c732dc0f01ee70b403f302e61d7709838"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

base64-arraybuffer@0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/base64-arraybuffer/download/base64-arraybuffer-0.1.5.tgz#73926771923b5a19747ad666aa5cd4bf9c6e9ce8"

base64-js@^1.0.2:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/base64-js/download/base64-js-1.2.0.tgz#a39992d723584811982be5e290bb6a53d86700f1"

base64id@0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/base64id/download/base64id-0.1.0.tgz#02ce0fdeee0cef4f40080e1e73e834f0b1bfce3f"

batch@0.5.3:
  version "0.5.3"
  resolved "http://registry.npm.taobao.org/batch/download/batch-0.5.3.tgz#3f3414f380321743bfc1042f9a83ff1d5824d464"

bcrypt-pbkdf@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.1.tgz#63bc5dcb61331b92bc05fd528953c33462a06f8d"
  dependencies:
    tweetnacl "^0.14.3"

beeper@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/beeper/download/beeper-1.1.1.tgz#e6d5ea8c5dad001304a70b22638447f69cb2f809"

better-assert@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/better-assert/download/better-assert-1.0.2.tgz#40866b9e1b9e0b55b481894311e68faffaebc522"
  dependencies:
    callsite "1.0.0"

big.js@^3.1.3:
  version "3.1.3"
  resolved "http://registry.npm.taobao.org/big.js/download/big.js-3.1.3.tgz#4cada2193652eb3ca9ec8e55c9015669c9806978"

bin-build@^2.0.0:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/bin-build/download/bin-build-2.2.0.tgz#11f8dd61f70ffcfa2bdcaa5b46f5e8fedd4221cc"
  dependencies:
    archive-type "^3.0.1"
    decompress "^3.0.0"
    download "^4.1.2"
    exec-series "^1.0.0"
    rimraf "^2.2.6"
    tempfile "^1.0.0"
    url-regex "^3.0.0"

bin-check@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/bin-check/download/bin-check-2.0.0.tgz#86f8e6f4253893df60dc316957f5af02acb05930"
  dependencies:
    executable "^1.0.0"

bin-version-check@^2.0.0, bin-version-check@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/bin-version-check/download/bin-version-check-2.1.0.tgz#e4e5df290b9069f7d111324031efc13fdd11a5b0"
  dependencies:
    bin-version "^1.0.0"
    minimist "^1.1.0"
    semver "^4.0.3"
    semver-truncate "^1.0.0"

bin-version@^1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/bin-version/download/bin-version-1.0.4.tgz#9eb498ee6fd76f7ab9a7c160436f89579435d78e"
  dependencies:
    find-versions "^1.0.0"

bin-wrapper@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/bin-wrapper/download/bin-wrapper-3.0.2.tgz#67d3306262e4b1a5f2f88ee23464f6a655677aeb"
  dependencies:
    bin-check "^2.0.0"
    bin-version-check "^2.1.0"
    download "^4.0.0"
    each-async "^1.1.1"
    lazy-req "^1.0.0"
    os-filter-obj "^1.0.0"

binary-extensions@^1.0.0:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/binary-extensions/download/binary-extensions-1.8.0.tgz#48ec8d16df4377eae5fa5884682480af4d95c774"

bl@^1.0.0, bl@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/bl/download/bl-1.1.2.tgz#fdca871a99713aa00d19e3bbba41c44787a65398"
  dependencies:
    readable-stream "~2.0.5"

blob@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/blob/download/blob-0.0.4.tgz#bcf13052ca54463f30f9fc7e95b9a47630a94921"

block-stream@*:
  version "0.0.9"
  resolved "http://registry.npm.taobao.org/block-stream/download/block-stream-0.0.9.tgz#13ebfe778a03205cfe03751481ebb4b3300c126a"
  dependencies:
    inherits "~2.0.0"

bluebird@^3.1.1:
  version "3.5.0"
  resolved "http://registry.npm.taobao.org/bluebird/download/bluebird-3.5.0.tgz#791420d7f551eea2897453a8a77653f96606d67c"

boom@2.x.x:
  version "2.10.1"
  resolved "http://registry.npm.taobao.org/boom/download/boom-2.10.1.tgz#39c8918ceff5799f83f9492a848f625add0c766f"
  dependencies:
    hoek "2.x.x"

brace-expansion@^1.0.0, brace-expansion@^1.1.7:
  version "1.1.8"
  resolved "http://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.8.tgz#c07b211c7c952ec1f8efd51a77ef0d1d3990a292"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@^1.8.2:
  version "1.8.5"
  resolved "http://registry.npm.taobao.org/braces/download/braces-1.8.5.tgz#ba77962e12dff969d6b76711e914b737857bf6a7"
  dependencies:
    expand-range "^1.8.1"
    preserve "^0.2.0"
    repeat-element "^1.1.2"

browser-sync-client@2.5.1:
  version "2.5.1"
  resolved "http://registry.npm.taobao.org/browser-sync-client/download/browser-sync-client-2.5.1.tgz#ec1ad69a49c2e2d4b645b18b1c06c29b3d9af8eb"
  dependencies:
    etag "^1.7.0"
    fresh "^0.3.0"

browser-sync-ui@0.6.3:
  version "0.6.3"
  resolved "http://registry.npm.taobao.org/browser-sync-ui/download/browser-sync-ui-0.6.3.tgz#640a537c180689303d5be92bc476b9ebc441c0bc"
  dependencies:
    async-each-series "0.1.1"
    connect-history-api-fallback "^1.1.0"
    immutable "^3.7.6"
    server-destroy "1.0.1"
    stream-throttle "^0.1.3"
    weinre "^2.0.0-pre-I0Z7U9OV"

browser-sync@^2.6.4:
  version "2.18.12"
  resolved "http://registry.npm.taobao.org/browser-sync/download/browser-sync-2.18.12.tgz#bbaa0a17a961e2b5f0a8e760e695027186664779"
  dependencies:
    browser-sync-client "2.5.1"
    browser-sync-ui "0.6.3"
    bs-recipes "1.3.4"
    chokidar "1.7.0"
    connect "3.5.0"
    dev-ip "^1.0.1"
    easy-extender "2.3.2"
    eazy-logger "3.0.2"
    emitter-steward "^1.0.0"
    fs-extra "3.0.1"
    http-proxy "1.15.2"
    immutable "3.8.1"
    localtunnel "1.8.2"
    micromatch "2.3.11"
    opn "4.0.2"
    portscanner "2.1.1"
    qs "6.2.1"
    resp-modifier "6.0.2"
    rx "4.1.0"
    serve-index "1.8.0"
    serve-static "1.12.2"
    server-destroy "1.0.1"
    socket.io "1.6.0"
    socket.io-client "1.6.0"
    ua-parser-js "0.7.12"
    yargs "6.4.0"

browserify-aes@0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.taobao.org/browserify-aes/download/browserify-aes-0.4.0.tgz#067149b668df31c4b58533e02d01e806d8608e2c"
  dependencies:
    inherits "^2.0.1"

browserify-zlib@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.1.4.tgz#bb35f8a519f600e0fa6b8485241c979d0141fb2d"
  dependencies:
    pako "~0.2.0"

browserslist@^1.3.6, browserslist@^1.5.2, browserslist@^1.7.6:
  version "1.7.7"
  resolved "http://registry.npm.taobao.org/browserslist/download/browserslist-1.7.7.tgz#0bd76704258be829b2398bb50e4b62d1a166b0b9"
  dependencies:
    caniuse-db "^1.0.30000639"
    electron-to-chromium "^1.2.7"

browserslist@~0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.taobao.org/browserslist/download/browserslist-0.4.0.tgz#3bd4ab9199dc1b9150d4d6dba4d9d3aabbc86dd4"
  dependencies:
    caniuse-db "^1.0.30000153"

bs-recipes@1.3.4:
  version "1.3.4"
  resolved "http://registry.npm.taobao.org/bs-recipes/download/bs-recipes-1.3.4.tgz#0d2d4d48a718c8c044769fdc4f89592dc8b69585"

buble@^0.12.0:
  version "0.12.5"
  resolved "http://registry.npm.taobao.org/buble/download/buble-0.12.5.tgz#c66ffe92f9f4a3c65d3256079b711e2bd0bc5013"
  dependencies:
    acorn "^3.1.0"
    acorn-jsx "^3.0.1"
    acorn-object-spread "^1.0.0"
    chalk "^1.1.3"
    magic-string "^0.14.0"
    minimist "^1.2.0"
    os-homedir "^1.0.1"

bubleify@^0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/bubleify/download/bubleify-0.5.1.tgz#f65c47cee31b80cad8b9e747bbe187d7fe51e927"
  dependencies:
    buble "^0.12.0"
    object-assign "^4.0.1"

buffer-crc32@~0.2.3:
  version "0.2.13"
  resolved "http://registry.npm.taobao.org/buffer-crc32/download/buffer-crc32-0.2.13.tgz#0d333e3f00eac50aa1454abd30ef8c2a5d9a7242"

buffer-shims@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/buffer-shims/download/buffer-shims-1.0.0.tgz#9978ce317388c649ad8793028c3477ef044a8b51"

buffer-to-vinyl@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/buffer-to-vinyl/download/buffer-to-vinyl-1.1.0.tgz#00f15faee3ab7a1dda2cde6d9121bffdd07b2262"
  dependencies:
    file-type "^3.1.0"
    readable-stream "^2.0.2"
    uuid "^2.0.1"
    vinyl "^1.0.0"

buffer@^4.9.0:
  version "4.9.1"
  resolved "http://registry.npm.taobao.org/buffer/download/buffer-4.9.1.tgz#6d1bb601b07a4efced97094132093027c95bc298"
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-modules@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/builtin-modules/download/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"

builtin-status-codes@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz#85982878e21b98e1c66425e03d0174788f569ee8"

builtins@0.0.7:
  version "0.0.7"
  resolved "http://registry.npm.taobao.org/builtins/download/builtins-0.0.7.tgz#355219cd6cf18dbe7c01cc7fd2dce765cfdc549a"

builtins@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/builtins/download/builtins-1.0.3.tgz#cb94faeb61c8696451db36534e1422f94f0aee88"

bytes@2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/bytes/download/bytes-2.3.0.tgz#d5b680a165b6201739acb611542aabc2d8ceb070"

caller-path@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/caller-path/download/caller-path-0.1.0.tgz#94085ef63581ecd3daa92444a8fe94e82577751f"
  dependencies:
    callsites "^0.2.0"

callsite@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/callsite/download/callsite-1.0.0.tgz#280398e5d664bd74038b6f0905153e6e8af1bc20"

callsites@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/callsites/download/callsites-0.2.0.tgz#afab96262910a7f33c19a5775825c69f34e350ca"

camel-case@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz#ca3c3688a4e9cf3a4cda777dc4dcbc713249cf73"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.1"

camelcase-keys@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/camelcase-keys/download/camelcase-keys-2.1.0.tgz#308beeaffdf28119051efa1d932213c91b8f92e7"
  dependencies:
    camelcase "^2.0.0"
    map-obj "^1.0.0"

camelcase@^1.0.2, camelcase@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/camelcase/download/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"

camelcase@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/camelcase/download/camelcase-2.1.1.tgz#7c1d16d679a1bbe59ca02cacecfb011e201f5a1f"

camelcase@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/camelcase/download/camelcase-3.0.0.tgz#32fc4b9fcdaf845fcdf7e73bb97cac2261f0ab0a"

caniuse-api@^1.5.2:
  version "1.6.1"
  resolved "http://registry.npm.taobao.org/caniuse-api/download/caniuse-api-1.6.1.tgz#b534e7c734c4f81ec5fbe8aca2ad24354b962c6c"
  dependencies:
    browserslist "^1.3.6"
    caniuse-db "^1.0.30000529"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-db@^1.0.30000153, caniuse-db@^1.0.30000214, caniuse-db@^1.0.30000529, caniuse-db@^1.0.30000634, caniuse-db@^1.0.30000639:
  version "1.0.30000692"
  resolved "http://registry.npm.taobao.org/caniuse-db/download/caniuse-db-1.0.30000692.tgz#3da9a99353adbcea1e142b99f60ecc6216df47a5"

capture-stack-trace@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/capture-stack-trace/download/capture-stack-trace-1.0.0.tgz#4a6fa07399c26bba47f0b2496b4d0fb408c5550d"

caseless@~0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.taobao.org/caseless/download/caseless-0.11.0.tgz#715b96ea9841593cc33067923f5ec60ebda4f7d7"

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"

caw@^1.0.1:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/caw/download/caw-1.2.0.tgz#ffb226fe7efc547288dc62ee3e97073c212d1034"
  dependencies:
    get-proxy "^1.0.1"
    is-obj "^1.0.0"
    object-assign "^3.0.0"
    tunnel-agent "^0.4.0"

center-align@^0.1.1:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/center-align/download/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@^0.5.0, chalk@~0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/chalk/download/chalk-0.5.1.tgz#663b3a648b68b55d04690d49167aa837858f2174"
  dependencies:
    ansi-styles "^1.1.0"
    escape-string-regexp "^1.0.0"
    has-ansi "^0.1.0"
    strip-ansi "^0.3.0"
    supports-color "^0.2.0"

chalk@^1.0.0, chalk@^1.1.0, chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@~0.4.0:
  version "0.4.0"
  resolved "http://registry.npm.taobao.org/chalk/download/chalk-0.4.0.tgz#5199a3ddcd0c1efe23bc08c1b027b06176e0c64f"
  dependencies:
    ansi-styles "~1.0.0"
    has-color "~0.1.0"
    strip-ansi "~0.1.0"

chalk@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/chalk/download/chalk-1.0.0.tgz#b3cf4ed0ff5397c99c75b8f679db2f52831f96dc"
  dependencies:
    ansi-styles "^2.0.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^1.0.3"
    strip-ansi "^2.0.1"
    supports-color "^1.3.0"

change-case@3.0.x:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/change-case/download/change-case-3.0.1.tgz#ee5f5ad0415ad1ad9e8072cf49cd4cfa7660a554"
  dependencies:
    camel-case "^3.0.0"
    constant-case "^2.0.0"
    dot-case "^2.1.0"
    header-case "^1.0.0"
    is-lower-case "^1.1.0"
    is-upper-case "^1.1.0"
    lower-case "^1.1.1"
    lower-case-first "^1.0.0"
    no-case "^2.2.0"
    param-case "^2.1.0"
    pascal-case "^2.0.0"
    path-case "^2.1.0"
    sentence-case "^2.1.0"
    snake-case "^2.1.0"
    swap-case "^1.1.0"
    title-case "^2.1.0"
    upper-case "^1.1.1"
    upper-case-first "^1.1.0"

chokidar@1.7.0, chokidar@^1.0.0:
  version "1.7.0"
  resolved "http://registry.npm.taobao.org/chokidar/download/chokidar-1.7.0.tgz#798e689778151c8076b4b360e5edd28cda2bb468"
  dependencies:
    anymatch "^1.3.0"
    async-each "^1.0.0"
    glob-parent "^2.0.0"
    inherits "^2.0.1"
    is-binary-path "^1.0.0"
    is-glob "^2.0.0"
    path-is-absolute "^1.0.0"
    readdirp "^2.0.0"
  optionalDependencies:
    fsevents "^1.0.0"

chownr@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/chownr/download/chownr-1.0.1.tgz#e2a75042a9551908bebd25b8523d5f9769d79181"

circular-json@^0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.taobao.org/circular-json/download/circular-json-0.3.1.tgz#be8b36aefccde8b3ca7aa2d6afc07a37242c0d2d"

clap@^1.0.9:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/clap/download/clap-1.2.0.tgz#59c90fe3e137104746ff19469a27a634ff68c857"
  dependencies:
    chalk "^1.1.3"

clean-css@3.4.x:
  version "3.4.27"
  resolved "http://registry.npm.taobao.org/clean-css/download/clean-css-3.4.27.tgz#adef75b31c160ffa5d72f4de67966e2660c1a255"
  dependencies:
    commander "2.8.x"
    source-map "0.4.x"

clean-css@~2.2.0:
  version "2.2.23"
  resolved "http://registry.npm.taobao.org/clean-css/download/clean-css-2.2.23.tgz#0590b5478b516c4903edc2d89bd3fdbdd286328c"
  dependencies:
    commander "2.2.x"

cli-cursor@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/cli-cursor/download/cli-cursor-1.0.2.tgz#64da3f7d56a54412e59794bd62dc35295e8f2987"
  dependencies:
    restore-cursor "^1.0.1"

cli-width@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/cli-width/download/cli-width-2.1.0.tgz#b234ca209b29ef66fc518d9b98d5847b00edf00a"

cliui@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/cliui/download/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^3.0.3, cliui@^3.2.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/cliui/download/cliui-3.2.0.tgz#120601537a916d29940f934da3b48d585a39213d"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wrap-ansi "^2.0.0"

clone-stats@^0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/clone-stats/download/clone-stats-0.0.1.tgz#b88f94a82cf38b8791d58046ea4029ad88ca99d1"

clone@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/clone/download/clone-0.2.0.tgz#c6126a90ad4f72dbf5acdb243cc37724fe93fc1f"

clone@^1.0.0, clone@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/clone/download/clone-1.0.2.tgz#260b7a99ebb1edfe247538175f783243cb19d149"

cmd-shim@~2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/cmd-shim/download/cmd-shim-2.0.2.tgz#6fcbda99483a8fd15d7d30a196ca69d688a2efdb"
  dependencies:
    graceful-fs "^4.1.2"
    mkdirp "~0.5.0"

co@3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/co/download/co-3.1.0.tgz#4ea54ea5a08938153185e15210c68d9092bc1b78"

co@^4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.taobao.org/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"

coa@~1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/coa/download/coa-1.0.3.tgz#1b54a5e1dcf77c990455d4deea98c564416dc893"
  dependencies:
    q "^1.1.2"

code-point-at@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/code-point-at/download/code-point-at-1.1.0.tgz#0d070b4d043a5bea33a2f1a40e2edb3d9a4ccf77"

coffee-script@~1.3.3:
  version "1.3.3"
  resolved "http://registry.npm.taobao.org/coffee-script/download/coffee-script-1.3.3.tgz#150d6b4cb522894369efed6a2101c20bc7f4a4f4"

color-convert@^1.3.0:
  version "1.9.0"
  resolved "http://registry.npm.taobao.org/color-convert/download/color-convert-1.9.0.tgz#1accf97dd739b983bf994d56fec8f95853641b7a"
  dependencies:
    color-name "^1.1.1"

color-name@^1.0.0, color-name@^1.1.1:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/color-name/download/color-name-1.1.2.tgz#5c8ab72b64bd2215d617ae9559ebb148475cf98d"

color-string@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/color-string/download/color-string-0.3.0.tgz#27d46fb67025c5c2fa25993bfbf579e47841b991"
  dependencies:
    color-name "^1.0.0"

color@^0.11.0:
  version "0.11.4"
  resolved "http://registry.npm.taobao.org/color/download/color-0.11.4.tgz#6d7b5c74fb65e841cd48792ad1ed5e07b904d764"
  dependencies:
    clone "^1.0.2"
    color-convert "^1.3.0"
    color-string "^0.3.0"

colormin@^1.0.5:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/colormin/download/colormin-1.1.2.tgz#ea2f7420a72b96881a38aae59ec124a6f7298133"
  dependencies:
    color "^0.11.0"
    css-color-names "0.0.4"
    has "^1.0.1"

colors@~0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.taobao.org/colors/download/colors-0.6.2.tgz#2423fe6678ac0c5dae8852e5d0e5be08c997abcc"

colors@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/colors/download/colors-1.1.2.tgz#168a4701756b6a7f51a12ce0c97bfa28c084ed63"

columnify@~1.5.4:
  version "1.5.4"
  resolved "http://registry.npm.taobao.org/columnify/download/columnify-1.5.4.tgz#4737ddf1c7b69a8a7c340570782e947eec8e78bb"
  dependencies:
    strip-ansi "^3.0.0"
    wcwidth "^1.0.0"

combined-stream@^1.0.5, combined-stream@~1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.5.tgz#938370a57b4a51dea2c77c15d5c5fdf895164009"
  dependencies:
    delayed-stream "~1.0.0"

commander@2.2.x:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/commander/download/commander-2.2.0.tgz#175ad4b9317f3ff615f201c1e57224f55a3e91df"

commander@2.8.x, commander@~2.8.1:
  version "2.8.1"
  resolved "http://registry.npm.taobao.org/commander/download/commander-2.8.1.tgz#06be367febfda0c330aa1e2a072d3dc9762425d4"
  dependencies:
    graceful-readlink ">= 1.0.0"

commander@2.9.x, commander@^2.2.0, commander@^2.9.0:
  version "2.9.0"
  resolved "http://registry.npm.taobao.org/commander/download/commander-2.9.0.tgz#9c99094176e12240cb22d6c5146098400fe0f7d4"
  dependencies:
    graceful-readlink ">= 1.0.0"

commondir@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"

component-bind@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/component-bind/download/component-bind-1.0.0.tgz#00c608ab7dcd93897c0009651b1d3a8e1e73bbd1"

component-emitter@1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/component-emitter/download/component-emitter-1.1.2.tgz#296594f2753daa63996d2af08d15a95116c9aec3"

component-emitter@1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/component-emitter/download/component-emitter-1.2.1.tgz#137918d6d78283f7df7a6b7c5a63e140e69425e6"

component-inherit@0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.taobao.org/component-inherit/download/component-inherit-0.0.3.tgz#645fc4adf58b72b649d5cae65135619db26ff143"

compressible@~2.0.8:
  version "2.0.10"
  resolved "http://registry.npm.taobao.org/compressible/download/compressible-2.0.10.tgz#feda1c7f7617912732b29bf8cf26252a20b9eecd"
  dependencies:
    mime-db ">= 1.27.0 < 2"

compression@^1.5.2:
  version "1.6.2"
  resolved "http://registry.npm.taobao.org/compression/download/compression-1.6.2.tgz#cceb121ecc9d09c52d7ad0c3350ea93ddd402bc3"
  dependencies:
    accepts "~1.3.3"
    bytes "2.3.0"
    compressible "~2.0.8"
    debug "~2.2.0"
    on-headers "~1.0.1"
    vary "~1.1.0"

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@^1.4.1, concat-stream@^1.4.6, concat-stream@^1.4.7, concat-stream@^1.5.2:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.0.tgz#0aac662fd52be78964d5532f694784e70110acf7"
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

config-chain@~1.1.11:
  version "1.1.11"
  resolved "http://registry.npm.taobao.org/config-chain/download/config-chain-1.1.11.tgz#aba09747dfbe4c3e70e766a6e41586e1859fc6f2"
  dependencies:
    ini "^1.3.4"
    proto-list "~1.2.1"

connect-history-api-fallback@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.1.0.tgz#5a6dee82d9a648cb29131d3f9dd400ffa4593742"

connect-history-api-fallback@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.3.0.tgz#e51d17f8f0ef0db90a64fdb47de3051556e9f169"

connect@1.x:
  version "1.9.2"
  resolved "http://registry.npm.taobao.org/connect/download/connect-1.9.2.tgz#42880a22e9438ae59a8add74e437f58ae8e52807"
  dependencies:
    formidable "1.0.x"
    mime ">= 0.0.1"
    qs ">= 0.4.0"

connect@3.5.0:
  version "3.5.0"
  resolved "http://registry.npm.taobao.org/connect/download/connect-3.5.0.tgz#b357525a0b4c1f50599cd983e1d9efeea9677198"
  dependencies:
    debug "~2.2.0"
    finalhandler "0.5.0"
    parseurl "~1.3.1"
    utils-merge "1.0.0"

console-browserify@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/console-browserify/download/console-browserify-1.1.0.tgz#f0241c45730a9fc6323b206dbf38edc741d0bb10"
  dependencies:
    date-now "^0.1.4"

console-control-strings@^1.0.0, console-control-strings@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/console-control-strings/download/console-control-strings-1.1.0.tgz#3d7cf4464db6446ea644bf4b39507f9851008e8e"

console-stream@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/console-stream/download/console-stream-0.1.1.tgz#a095fe07b20465955f2fafd28b5d72bccd949d44"

consolidate@^0.14.0:
  version "0.14.5"
  resolved "http://registry.npm.taobao.org/consolidate/download/consolidate-0.14.5.tgz#5a25047bc76f73072667c8cb52c989888f494c63"
  dependencies:
    bluebird "^3.1.1"

constant-case@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/constant-case/download/constant-case-2.0.0.tgz#4175764d389d3fa9c8ecd29186ed6005243b6a46"
  dependencies:
    snake-case "^2.1.0"
    upper-case "^1.1.1"

constants-browserify@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/constants-browserify/download/constants-browserify-1.0.0.tgz#c20b96d8c617748aaf1c16021760cd27fcb8cb75"

content-disposition@0.5.2:
  version "0.5.2"
  resolved "http://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.2.tgz#0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4"

content-type@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/content-type/download/content-type-1.0.2.tgz#b7d113aee7a8dd27bd21133c4dc2529df1721eed"

convert-source-map@^1.1.0, convert-source-map@^1.1.1:
  version "1.5.0"
  resolved "http://registry.npm.taobao.org/convert-source-map/download/convert-source-map-1.5.0.tgz#9acd70851c6d5dfdd93d9282e5edf94a03ff46b5"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"

cookie@0.3.1:
  version "0.3.1"
  resolved "http://registry.npm.taobao.org/cookie/download/cookie-0.3.1.tgz#e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb"

core-js@^2.4.0:
  version "2.4.1"
  resolved "http://registry.npm.taobao.org/core-js/download/core-js-2.4.1.tgz#4de911e667b0eae9124e34254b53aea6fc618d3e"

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

create-error-class@^3.0.1:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/create-error-class/download/create-error-class-3.0.2.tgz#06be7abef947a3f14a30fd610671d401bca8b7b6"
  dependencies:
    capture-stack-trace "^1.0.0"

cross-spawn@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-3.0.1.tgz#1256037ecb9f0c5f79e3d6ef135e30770184b982"
  dependencies:
    lru-cache "^4.0.1"
    which "^1.2.9"

cryptiles@2.x.x:
  version "2.0.5"
  resolved "http://registry.npm.taobao.org/cryptiles/download/cryptiles-2.0.5.tgz#3bdfecdc608147c1c67202fa291e7dca59eaa3b8"
  dependencies:
    boom "2.x.x"

crypto-browserify@3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.3.0.tgz#b9fc75bb4a0ed61dcf1cd5dae96eb30c9c3e506c"
  dependencies:
    browserify-aes "0.4.0"
    pbkdf2-compat "2.0.1"
    ripemd160 "0.2.0"
    sha.js "2.2.6"

css-color-names@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/css-color-names/download/css-color-names-0.0.4.tgz#808adc2e79cf84738069b646cb20ec27beb629e0"

css-loader@^0.23.1:
  version "0.23.1"
  resolved "http://registry.npm.taobao.org/css-loader/download/css-loader-0.23.1.tgz#9fa23f2b5c0965235910ad5ecef3b8a36390fe50"
  dependencies:
    css-selector-tokenizer "^0.5.1"
    cssnano ">=2.6.1 <4"
    loader-utils "~0.2.2"
    lodash.camelcase "^3.0.1"
    object-assign "^4.0.1"
    postcss "^5.0.6"
    postcss-modules-extract-imports "^1.0.0"
    postcss-modules-local-by-default "^1.0.1"
    postcss-modules-scope "^1.0.0"
    postcss-modules-values "^1.1.0"
    source-list-map "^0.1.4"

css-selector-tokenizer@^0.5.1:
  version "0.5.4"
  resolved "http://registry.npm.taobao.org/css-selector-tokenizer/download/css-selector-tokenizer-0.5.4.tgz#139bafd34a35fd0c1428487049e0699e6f6a2c21"
  dependencies:
    cssesc "^0.1.0"
    fastparse "^1.1.1"

css-selector-tokenizer@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.taobao.org/css-selector-tokenizer/download/css-selector-tokenizer-0.7.0.tgz#e6988474ae8c953477bf5e7efecfceccd9cf4c86"
  dependencies:
    cssesc "^0.1.0"
    fastparse "^1.1.1"
    regexpu-core "^1.0.0"

cssesc@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/cssesc/download/cssesc-0.1.0.tgz#c814903e45623371a0477b40109aaafbeeaddbb4"

"cssnano@>=2.6.1 <4":
  version "3.10.0"
  resolved "http://registry.npm.taobao.org/cssnano/download/cssnano-3.10.0.tgz#4f38f6cea2b9b17fa01490f23f1dc68ea65c1c38"
  dependencies:
    autoprefixer "^6.3.1"
    decamelize "^1.1.2"
    defined "^1.0.0"
    has "^1.0.1"
    object-assign "^4.0.1"
    postcss "^5.0.14"
    postcss-calc "^5.2.0"
    postcss-colormin "^2.1.8"
    postcss-convert-values "^2.3.4"
    postcss-discard-comments "^2.0.4"
    postcss-discard-duplicates "^2.0.1"
    postcss-discard-empty "^2.0.1"
    postcss-discard-overridden "^0.1.1"
    postcss-discard-unused "^2.2.1"
    postcss-filter-plugins "^2.0.0"
    postcss-merge-idents "^2.1.5"
    postcss-merge-longhand "^2.0.1"
    postcss-merge-rules "^2.0.3"
    postcss-minify-font-values "^1.0.2"
    postcss-minify-gradients "^1.0.1"
    postcss-minify-params "^1.0.4"
    postcss-minify-selectors "^2.0.4"
    postcss-normalize-charset "^1.1.0"
    postcss-normalize-url "^3.0.7"
    postcss-ordered-values "^2.1.0"
    postcss-reduce-idents "^2.2.2"
    postcss-reduce-initial "^1.0.0"
    postcss-reduce-transforms "^1.0.3"
    postcss-svgo "^2.1.1"
    postcss-unique-selectors "^2.0.2"
    postcss-value-parser "^3.2.3"
    postcss-zindex "^2.0.1"

csso@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/csso/download/csso-2.0.0.tgz#178b43a44621221c27756086f531e02f42900ee8"
  dependencies:
    clap "^1.0.9"
    source-map "^0.5.3"

csso@~2.3.1:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/csso/download/csso-2.3.2.tgz#ddd52c587033f49e94b71fc55569f252e8ff5f85"
  dependencies:
    clap "^1.0.9"
    source-map "^0.5.3"

currently-unhandled@^0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.taobao.org/currently-unhandled/download/currently-unhandled-0.4.1.tgz#988df33feab191ef799a61369dd76c17adf957ea"
  dependencies:
    array-find-index "^1.0.1"

d@1:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/d/download/d-1.0.0.tgz#754bb5bfe55451da69a58b94d45f4c5b0462d58f"
  dependencies:
    es5-ext "^0.10.9"

dargs@^2.0.3:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/dargs/download/dargs-2.1.0.tgz#46c27ffab1ffb1378ef212597213719fe602bc93"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  dependencies:
    assert-plus "^1.0.0"

date-now@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/date-now/download/date-now-0.1.4.tgz#eaf439fd4d4848ad74e5cc7dbef200672b9e345b"

dateformat@1.0.2-1.2.3:
  version "1.0.2-1.2.3"
  resolved "http://registry.npm.taobao.org/dateformat/download/dateformat-1.0.2-1.2.3.tgz#b0220c02de98617433b72851cf47de3df2cdbee9"

dateformat@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/dateformat/download/dateformat-2.0.0.tgz#2743e3abb5c3fc2462e527dca445e04e9f4dee17"

de-indent@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/de-indent/download/de-indent-1.0.2.tgz#b2038e846dc33baa5796128d0804b455b8c1e21d"

debug@2.2.0, debug@~2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/debug/download/debug-2.2.0.tgz#f87057e995b1a1f6ae6a4960664137bc56f039da"
  dependencies:
    ms "0.7.1"

debug@2.3.3:
  version "2.3.3"
  resolved "http://registry.npm.taobao.org/debug/download/debug-2.3.3.tgz#40c453e67e6e13c901ddec317af8986cda9eff8c"
  dependencies:
    ms "0.7.2"

debug@2.6.4:
  version "2.6.4"
  resolved "http://registry.npm.taobao.org/debug/download/debug-2.6.4.tgz#7586a9b3c39741c0282ae33445c4e8ac74734fe0"
  dependencies:
    ms "0.7.3"

debug@2.6.7:
  version "2.6.7"
  resolved "http://registry.npm.taobao.org/debug/download/debug-2.6.7.tgz#92bad1f6d05bbb6bba22cca88bcd0ec894c2861e"
  dependencies:
    ms "2.0.0"

debug@^2.1.1, debug@^2.2.0, debug@^2.6.6:
  version "2.6.8"
  resolved "http://registry.npm.taobao.org/debug/download/debug-2.6.8.tgz#e731531ca2ede27d188222427da17821d68ff4fc"
  dependencies:
    ms "2.0.0"

debug@~0.7.0:
  version "0.7.4"
  resolved "http://registry.npm.taobao.org/debug/download/debug-0.7.4.tgz#06e1ea8082c2cb14e39806e22e2f6f757f92af39"

debuglog@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/debuglog/download/debuglog-1.0.1.tgz#aa24ffb9ac3df9a2351837cfb2d279360cd78492"

decamelize@^1.0.0, decamelize@^1.1.1, decamelize@^1.1.2:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"

decompress-tar@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/decompress-tar/download/decompress-tar-3.1.0.tgz#217c789f9b94450efaadc5c5e537978fc333c466"
  dependencies:
    is-tar "^1.0.0"
    object-assign "^2.0.0"
    strip-dirs "^1.0.0"
    tar-stream "^1.1.1"
    through2 "^0.6.1"
    vinyl "^0.4.3"

decompress-tarbz2@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/decompress-tarbz2/download/decompress-tarbz2-3.1.0.tgz#8b23935681355f9f189d87256a0f8bdd96d9666d"
  dependencies:
    is-bzip2 "^1.0.0"
    object-assign "^2.0.0"
    seek-bzip "^1.0.3"
    strip-dirs "^1.0.0"
    tar-stream "^1.1.1"
    through2 "^0.6.1"
    vinyl "^0.4.3"

decompress-targz@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/decompress-targz/download/decompress-targz-3.1.0.tgz#b2c13df98166268991b715d6447f642e9696f5a0"
  dependencies:
    is-gzip "^1.0.0"
    object-assign "^2.0.0"
    strip-dirs "^1.0.0"
    tar-stream "^1.1.1"
    through2 "^0.6.1"
    vinyl "^0.4.3"

decompress-unzip@^3.0.0:
  version "3.4.0"
  resolved "http://registry.npm.taobao.org/decompress-unzip/download/decompress-unzip-3.4.0.tgz#61475b4152066bbe3fee12f9d629d15fe6478eeb"
  dependencies:
    is-zip "^1.0.0"
    read-all-stream "^3.0.0"
    stat-mode "^0.2.0"
    strip-dirs "^1.0.0"
    through2 "^2.0.0"
    vinyl "^1.0.0"
    yauzl "^2.2.1"

decompress@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/decompress/download/decompress-3.0.0.tgz#af1dd50d06e3bfc432461d37de11b38c0d991bed"
  dependencies:
    buffer-to-vinyl "^1.0.0"
    concat-stream "^1.4.6"
    decompress-tar "^3.0.0"
    decompress-tarbz2 "^3.0.0"
    decompress-targz "^3.0.0"
    decompress-unzip "^3.0.0"
    stream-combiner2 "^1.1.1"
    vinyl-assign "^1.0.1"
    vinyl-fs "^2.2.0"

deep-extend@~0.4.0:
  version "0.4.2"
  resolved "http://registry.npm.taobao.org/deep-extend/download/deep-extend-0.4.2.tgz#48b699c27e334bf89f10892be432f6e4c7d34a7f"

deep-is@~0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/deep-is/download/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"

deepmerge@^1.2.0:
  version "1.4.3"
  resolved "http://registry.npm.taobao.org/deepmerge/download/deepmerge-1.4.3.tgz#f8c9ecb11c176b3dbfc8167b58cc5674c5e658bb"

defaults@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz#c656051e9817d9ff08ed881477f3fe4019f3ef7d"
  dependencies:
    clone "^1.0.2"

define-properties@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/define-properties/download/define-properties-1.1.2.tgz#83a73f2fea569898fb737193c8f873caf6d45c94"
  dependencies:
    foreach "^2.0.5"
    object-keys "^1.0.8"

defined@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/defined/download/defined-1.0.0.tgz#c98d9bcef75674188e110969151199e39b1fa693"

del@^2.0.2:
  version "2.2.2"
  resolved "http://registry.npm.taobao.org/del/download/del-2.2.2.tgz#c12c981d067846c84bcaf862cff930d907ffd1a8"
  dependencies:
    globby "^5.0.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

delegates@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/delegates/download/delegates-1.0.0.tgz#84c6e159b81904fdca59a0ef44cd870d31250f9a"

depd@1.1.0, depd@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/depd/download/depd-1.1.0.tgz#e1bd82c6aab6ced965b97b88b17ed3e528ca18c3"

destroy@~1.0.4:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"

detect-indent@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/detect-indent/download/detect-indent-4.0.0.tgz#f76d064352cdf43a1cb6ce619c4ee3a9475de208"
  dependencies:
    repeating "^2.0.0"

dev-ip@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/dev-ip/download/dev-ip-1.0.1.tgz#a76a3ed1855be7a012bb8ac16cb80f3c00dc28f0"

dezalgo@^1.0.0, dezalgo@^1.0.1, dezalgo@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/dezalgo/download/dezalgo-1.0.3.tgz#7f742de066fc748bc8db820569dddce49bf0d456"
  dependencies:
    asap "^2.0.0"
    wrappy "1"

diff@~1.3.0:
  version "1.3.2"
  resolved "http://registry.npm.taobao.org/diff/download/diff-1.3.2.tgz#fd07a1f1f891519d9905a4c9a89dcf5a70b66037"

doctrine@^1.2.2:
  version "1.5.0"
  resolved "http://registry.npm.taobao.org/doctrine/download/doctrine-1.5.0.tgz#379dce730f6166f76cefa4e6707a159b02c5a6fa"
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

doctrine@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/doctrine/download/doctrine-2.0.0.tgz#c73d8d2909d22291e1a007a395804da8b665fe63"
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

domain-browser@^1.1.1:
  version "1.1.7"
  resolved "http://registry.npm.taobao.org/domain-browser/download/domain-browser-1.1.7.tgz#867aa4b093faa05f1de08c06f4d7b21fdf8698bc"

dot-case@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/dot-case/download/dot-case-2.1.1.tgz#34dcf37f50a8e93c2b3bca8bb7fb9155c7da3bee"
  dependencies:
    no-case "^2.2.0"

download@^4.0.0, download@^4.1.2:
  version "4.4.3"
  resolved "http://registry.npm.taobao.org/download/download/download-4.4.3.tgz#aa55fdad392d95d4b68e8c2be03e0c2aa21ba9ac"
  dependencies:
    caw "^1.0.1"
    concat-stream "^1.4.7"
    each-async "^1.0.0"
    filenamify "^1.0.1"
    got "^5.0.0"
    gulp-decompress "^1.2.0"
    gulp-rename "^1.2.0"
    is-url "^1.2.0"
    object-assign "^4.0.1"
    read-all-stream "^3.0.0"
    readable-stream "^2.0.2"
    stream-combiner2 "^1.1.1"
    vinyl "^1.0.0"
    vinyl-fs "^2.2.0"
    ware "^1.2.0"

duplexer2@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.taobao.org/duplexer2/download/duplexer2-0.0.2.tgz#c614dcf67e2fb14995a91711e5a617e8a60a31db"
  dependencies:
    readable-stream "~1.1.9"

duplexer2@^0.1.4, duplexer2@~0.1.0:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/duplexer2/download/duplexer2-0.1.4.tgz#8b12dab878c0d69e3e7891051662a32fc6bddcc1"
  dependencies:
    readable-stream "^2.0.2"

duplexify@^3.2.0:
  version "3.5.0"
  resolved "http://registry.npm.taobao.org/duplexify/download/duplexify-3.5.0.tgz#1aa773002e1578457e9d9d4a50b0ccaaebcbd604"
  dependencies:
    end-of-stream "1.0.0"
    inherits "^2.0.1"
    readable-stream "^2.0.0"
    stream-shift "^1.0.0"

each-async@^1.0.0, each-async@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/each-async/download/each-async-1.1.1.tgz#dee5229bdf0ab6ba2012a395e1b869abf8813473"
  dependencies:
    onetime "^1.0.0"
    set-immediate-shim "^1.0.0"

easy-extender@2.3.2:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/easy-extender/download/easy-extender-2.3.2.tgz#3d3248febe2b159607316d8f9cf491c16648221d"
  dependencies:
    lodash "^3.10.1"

eazy-logger@3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/eazy-logger/download/eazy-logger-3.0.2.tgz#a325aa5e53d13a2225889b2ac4113b2b9636f4fc"
  dependencies:
    tfunk "^3.0.1"

ecc-jsbn@~0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.1.tgz#0fc73a9ed5f0d53c38193398523ef7e543777505"
  dependencies:
    jsbn "~0.1.0"

editor@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/editor/download/editor-1.0.0.tgz#60c7f87bd62bcc6a894fa8ccd6afb7823a24f742"

ee-first@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"

electron-to-chromium@^1.2.7:
  version "1.3.14"
  resolved "http://registry.npm.taobao.org/electron-to-chromium/download/electron-to-chromium-1.3.14.tgz#64af0f9efd3c3c6acd57d71f83b49ca7ee9c4b43"

element-theme-default@^1.2.9:
  version "1.3.7"
  resolved "http://registry.npm.taobao.org/element-theme-default/download/element-theme-default-1.3.7.tgz#71a2fc6b743a1c66b26c493014a613959bbcca0e"

element-ui@^1.3.7:
  version "1.3.7"
  resolved "http://registry.npm.taobao.org/element-ui/download/element-ui-1.3.7.tgz#ed6e2e371b767fd7b8a8015dc284e4af5e940026"
  dependencies:
    async-validator "1.6.9"
    babel-helper-vue-jsx-merge-props "^2.0.0"
    deepmerge "^1.2.0"
    throttle-debounce "^1.0.1"

emitter-steward@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/emitter-steward/download/emitter-steward-1.0.0.tgz#f3411ade9758a7565df848b2da0cbbd1b46cbd64"

emojis-list@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz#4daa4d9db00f9819880c79fa457ae5b09a1fd389"

encodeurl@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.1.tgz#79e3d58655346909fe6f0f45a5de68103b294d20"

end-of-stream@1.0.0, end-of-stream@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.0.0.tgz#d4596e702734a93e40e9af864319eabd99ff2f0e"
  dependencies:
    once "~1.3.0"

engine.io-client@1.8.0:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/engine.io-client/download/engine.io-client-1.8.0.tgz#7b730e4127414087596d9be3c88d2bc5fdb6cf5c"
  dependencies:
    component-emitter "1.2.1"
    component-inherit "0.0.3"
    debug "2.3.3"
    engine.io-parser "1.3.1"
    has-cors "1.1.0"
    indexof "0.0.1"
    parsejson "0.0.3"
    parseqs "0.0.5"
    parseuri "0.0.5"
    ws "1.1.1"
    xmlhttprequest-ssl "1.5.3"
    yeast "0.1.2"

engine.io-parser@1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/engine.io-parser/download/engine.io-parser-1.3.1.tgz#9554f1ae33107d6fbd170ca5466d2f833f6a07cf"
  dependencies:
    after "0.8.1"
    arraybuffer.slice "0.0.6"
    base64-arraybuffer "0.1.5"
    blob "0.0.4"
    has-binary "0.1.6"
    wtf-8 "1.0.0"

engine.io@1.8.0:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/engine.io/download/engine.io-1.8.0.tgz#3eeb5f264cb75dbbec1baaea26d61f5a4eace2aa"
  dependencies:
    accepts "1.3.3"
    base64id "0.1.0"
    cookie "0.3.1"
    debug "2.3.3"
    engine.io-parser "1.3.1"
    ws "1.1.1"

enhanced-resolve@~0.9.0:
  version "0.9.1"
  resolved "http://registry.npm.taobao.org/enhanced-resolve/download/enhanced-resolve-0.9.1.tgz#4d6e689b3725f86090927ccc86cd9f1635b89e2e"
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.2.0"
    tapable "^0.1.8"

errno@^0.1.3:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/errno/download/errno-0.1.4.tgz#b896e23a9e5e8ba33871fc996abd3635fc9a1c7d"
  dependencies:
    prr "~0.0.0"

error-ex@^1.2.0:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/error-ex/download/error-ex-1.3.1.tgz#f855a86ce61adc4e8621c3cda21e7a7612c3a8dc"
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.taobao.org/es-abstract/download/es-abstract-1.7.0.tgz#dfade774e01bfcd97f96180298c449c8623fb94c"
  dependencies:
    es-to-primitive "^1.1.1"
    function-bind "^1.1.0"
    is-callable "^1.1.3"
    is-regex "^1.0.3"

es-to-primitive@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.1.1.tgz#45355248a88979034b6792e19bb81f2b7975dd0d"
  dependencies:
    is-callable "^1.1.1"
    is-date-object "^1.0.1"
    is-symbol "^1.0.1"

es5-ext@^0.10.14, es5-ext@^0.10.9, es5-ext@~0.10.14:
  version "0.10.23"
  resolved "http://registry.npm.taobao.org/es5-ext/download/es5-ext-0.10.23.tgz#7578b51be974207a5487821b56538c224e4e7b38"
  dependencies:
    es6-iterator "2"
    es6-symbol "~3.1"

es6-iterator@2, es6-iterator@^2.0.1, es6-iterator@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/es6-iterator/download/es6-iterator-2.0.1.tgz#8e319c9f0453bf575d374940a655920e59ca5512"
  dependencies:
    d "1"
    es5-ext "^0.10.14"
    es6-symbol "^3.1"

es6-map@^0.1.3:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/es6-map/download/es6-map-0.1.5.tgz#9136e0503dcc06a301690f0bb14ff4e364e949f0"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-set "~0.1.5"
    es6-symbol "~3.1.1"
    event-emitter "~0.3.5"

es6-promise@~2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/es6-promise/download/es6-promise-2.3.0.tgz#96edb9f2fdb01995822b263dd8aadab6748181bc"

es6-set@~0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/es6-set/download/es6-set-0.1.5.tgz#d2b3ec5d4d800ced818db538d28974db0a73ccb1"
  dependencies:
    d "1"
    es5-ext "~0.10.14"
    es6-iterator "~2.0.1"
    es6-symbol "3.1.1"
    event-emitter "~0.3.5"

es6-symbol@3.1.1, es6-symbol@^3.0.2, es6-symbol@^3.1, es6-symbol@^3.1.1, es6-symbol@~3.1, es6-symbol@~3.1.1:
  version "3.1.1"
  resolved "http://registry.npm.taobao.org/es6-symbol/download/es6-symbol-3.1.1.tgz#bf00ef4fdab6ba1b46ecb7b629b4c7ed5715cc77"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

es6-templates@^0.2.2:
  version "0.2.3"
  resolved "http://registry.npm.taobao.org/es6-templates/download/es6-templates-0.2.3.tgz#5cb9ac9fb1ded6eb1239342b81d792bbb4078ee4"
  dependencies:
    recast "~0.11.12"
    through "~2.3.6"

es6-weak-map@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/es6-weak-map/download/es6-weak-map-2.0.2.tgz#5e3ab32251ffd1538a1f8e5ffa1357772f92d96f"
  dependencies:
    d "1"
    es5-ext "^0.10.14"
    es6-iterator "^2.0.1"
    es6-symbol "^3.1.1"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"

escape-string-regexp@^1.0.0, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

escope@^3.6.0:
  version "3.6.0"
  resolved "http://registry.npm.taobao.org/escope/download/escope-3.6.0.tgz#e01975e812781a163a6dadfdd80398dc64c889c3"
  dependencies:
    es6-map "^0.1.3"
    es6-weak-map "^2.0.1"
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint-loader@^1.5.0:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/eslint-loader/download/eslint-loader-1.8.0.tgz#8261f08cca4bd2ea263b77733e93cf0f21e20aa9"
  dependencies:
    loader-fs-cache "^1.0.0"
    loader-utils "^1.0.2"
    object-assign "^4.0.1"
    object-hash "^1.1.4"
    rimraf "^2.6.1"

eslint-plugin-react@^6.1.0:
  version "6.10.3"
  resolved "http://registry.npm.taobao.org/eslint-plugin-react/download/eslint-plugin-react-6.10.3.tgz#c5435beb06774e12c7db2f6abaddcbf900cd3f78"
  dependencies:
    array.prototype.find "^2.0.1"
    doctrine "^1.2.2"
    has "^1.0.1"
    jsx-ast-utils "^1.3.4"
    object.assign "^4.0.4"

eslint@^3.3.0:
  version "3.19.0"
  resolved "http://registry.npm.taobao.org/eslint/download/eslint-3.19.0.tgz#c8fc6201c7f40dd08941b87c085767386a679acc"
  dependencies:
    babel-code-frame "^6.16.0"
    chalk "^1.1.3"
    concat-stream "^1.5.2"
    debug "^2.1.1"
    doctrine "^2.0.0"
    escope "^3.6.0"
    espree "^3.4.0"
    esquery "^1.0.0"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    file-entry-cache "^2.0.0"
    glob "^7.0.3"
    globals "^9.14.0"
    ignore "^3.2.0"
    imurmurhash "^0.1.4"
    inquirer "^0.12.0"
    is-my-json-valid "^2.10.0"
    is-resolvable "^1.0.0"
    js-yaml "^3.5.1"
    json-stable-stringify "^1.0.0"
    levn "^0.3.0"
    lodash "^4.0.0"
    mkdirp "^0.5.0"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.1"
    pluralize "^1.2.1"
    progress "^1.1.8"
    require-uncached "^1.0.2"
    shelljs "^0.7.5"
    strip-bom "^3.0.0"
    strip-json-comments "~2.0.1"
    table "^3.7.8"
    text-table "~0.2.0"
    user-home "^2.0.0"

espree@^3.4.0:
  version "3.4.3"
  resolved "http://registry.npm.taobao.org/espree/download/espree-3.4.3.tgz#2910b5ccd49ce893c2ffffaab4fd8b3a31b82374"
  dependencies:
    acorn "^5.0.1"
    acorn-jsx "^3.0.0"

esprima@^2.6.0:
  version "2.7.3"
  resolved "http://registry.npm.taobao.org/esprima/download/esprima-2.7.3.tgz#96e3b70d5779f6ad49cd032673d1c312767ba581"

esprima@^3.1.1, esprima@~3.1.0:
  version "3.1.3"
  resolved "http://registry.npm.taobao.org/esprima/download/esprima-3.1.3.tgz#fdca51cee6133895e3c88d535ce49dbff62a4633"

"esprima@~ 1.0.2":
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/esprima/download/esprima-1.0.4.tgz#9f557e08fc3b4d26ece9dd34f8fbf476b62585ad"

esquery@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/esquery/download/esquery-1.0.0.tgz#cfba8b57d7fba93f17298a8a006a04cda13d80fa"
  dependencies:
    estraverse "^4.0.0"

esrecurse@^4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.taobao.org/esrecurse/download/esrecurse-4.1.0.tgz#4713b6536adf7f2ac4f327d559e7756bff648220"
  dependencies:
    estraverse "~4.1.0"
    object-assign "^4.0.1"

estraverse@^4.0.0, estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.taobao.org/estraverse/download/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"

estraverse@~4.1.0:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/estraverse/download/estraverse-4.1.1.tgz#f6caca728933a850ef90661d0e17982ba47111a2"

esutils@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/esutils/download/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"

etag@^1.7.0, etag@~1.8.0:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/etag/download/etag-1.8.0.tgz#6f631aef336d6c46362b51764044ce216be3c051"

event-emitter@~0.3.5:
  version "0.3.5"
  resolved "http://registry.npm.taobao.org/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter2@~0.4.13:
  version "0.4.14"
  resolved "http://registry.npm.taobao.org/eventemitter2/download/eventemitter2-0.4.14.tgz#8f61b75cde012b2e9eb284d4545583b5643b61ab"

eventemitter3@1.x.x:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/eventemitter3/download/eventemitter3-1.2.0.tgz#1c86991d816ad1e504750e73874224ecf3bec508"

events@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/events/download/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"

eventsource@0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.taobao.org/eventsource/download/eventsource-0.1.6.tgz#0acede849ed7dd1ccc32c811bb11b944d4f29232"
  dependencies:
    original ">=0.0.5"

exec-buffer@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/exec-buffer/download/exec-buffer-2.0.1.tgz#0028a31be0b1460b61d075f96af4583b9e335ea0"
  dependencies:
    rimraf "^2.2.6"
    tempfile "^1.0.0"

exec-series@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/exec-series/download/exec-series-1.0.3.tgz#6d257a9beac482a872c7783bc8615839fc77143a"
  dependencies:
    async-each-series "^1.1.0"
    object-assign "^4.1.0"

executable@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/executable/download/executable-1.1.0.tgz#877980e9112f3391066da37265de7ad8434ab4d9"
  dependencies:
    meow "^3.1.0"

exit-hook@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/exit-hook/download/exit-hook-1.1.1.tgz#f05ca233b48c05d54fff07765df8507e95c02ff8"

exit@~0.1.1:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/exit/download/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"

expand-brackets@^0.1.4:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/expand-brackets/download/expand-brackets-0.1.5.tgz#df07284e342a807cd733ac5af72411e581d1177b"
  dependencies:
    is-posix-bracket "^0.1.0"

expand-range@^1.8.1:
  version "1.8.2"
  resolved "http://registry.npm.taobao.org/expand-range/download/expand-range-1.8.2.tgz#a299effd335fe2721ebae8e257ec79644fc85337"
  dependencies:
    fill-range "^2.1.0"

express@2.5.x:
  version "2.5.11"
  resolved "http://registry.npm.taobao.org/express/download/express-2.5.11.tgz#4ce8ea1f3635e69e49f0ebb497b6a4b0a51ce6f0"
  dependencies:
    connect "1.x"
    mime "1.2.4"
    mkdirp "0.3.0"
    qs "0.4.x"

express@^4.13.3:
  version "4.15.3"
  resolved "http://registry.npm.taobao.org/express/download/express-4.15.3.tgz#bab65d0f03aa80c358408972fc700f916944b662"
  dependencies:
    accepts "~1.3.3"
    array-flatten "1.1.1"
    content-disposition "0.5.2"
    content-type "~1.0.2"
    cookie "0.3.1"
    cookie-signature "1.0.6"
    debug "2.6.7"
    depd "~1.1.0"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.0"
    finalhandler "~1.0.3"
    fresh "0.5.0"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.1"
    path-to-regexp "0.1.7"
    proxy-addr "~1.1.4"
    qs "6.4.0"
    range-parser "~1.2.0"
    send "0.15.3"
    serve-static "1.12.3"
    setprototypeof "1.0.3"
    statuses "~1.3.1"
    type-is "~1.6.15"
    utils-merge "1.0.0"
    vary "~1.1.1"

extend-shallow@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz#51af7d614ad9a9f610ea1bafbb989d6b1c56890f"
  dependencies:
    is-extendable "^0.1.0"

extend@^3.0.0, extend@~3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/extend/download/extend-3.0.1.tgz#a755ea7bc1adfcc5a31ce7e762dbaadc5e636444"

extglob@^0.3.1:
  version "0.3.2"
  resolved "http://registry.npm.taobao.org/extglob/download/extglob-0.3.2.tgz#2e18ff3d2f49ab2765cec9023f011daa8d8349a1"
  dependencies:
    is-extglob "^1.0.0"

extsprintf@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/extsprintf/download/extsprintf-1.0.2.tgz#e1080e0658e300b06294990cc70e1502235fd550"

fancy-log@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/fancy-log/download/fancy-log-1.3.0.tgz#45be17d02bb9917d60ccffd4995c999e6c8c9948"
  dependencies:
    chalk "^1.1.1"
    time-stamp "^1.0.0"

fast-levenshtein@~2.0.4:
  version "2.0.6"
  resolved "http://registry.npm.taobao.org/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"

fastparse@^1.0.0, fastparse@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/fastparse/download/fastparse-1.1.1.tgz#d1e2643b38a94d7583b479060e6c4affc94071f8"

faye-websocket@^0.10.0:
  version "0.10.0"
  resolved "http://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.10.0.tgz#4e492f8d04dfb6f89003507f6edbf2d501e7c6f4"
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.11.0:
  version "0.11.1"
  resolved "http://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.11.1.tgz#f0efe18c4f56e4f40afc7e06c719fd5ee6188f38"
  dependencies:
    websocket-driver ">=0.5.1"

faye-websocket@~0.4.3:
  version "0.4.4"
  resolved "http://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.4.4.tgz#c14c5b3bf14d7417ffbfd990c0a7495cd9f337bc"

fd-slicer@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/fd-slicer/download/fd-slicer-1.0.1.tgz#8b5bcbd9ec327c5041bf9ab023fd6750f1177e65"
  dependencies:
    pend "~1.2.0"

figures@^1.0.1, figures@^1.3.5:
  version "1.7.0"
  resolved "http://registry.npm.taobao.org/figures/download/figures-1.7.0.tgz#cbe1e3affcf1cd44b80cadfed28dc793a9701d2e"
  dependencies:
    escape-string-regexp "^1.0.5"
    object-assign "^4.1.0"

file-entry-cache@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/file-entry-cache/download/file-entry-cache-2.0.0.tgz#c392990c3e684783d838b8c84a45d8a048458361"
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

file-loader@^0.11.2:
  version "0.11.2"
  resolved "http://registry.npm.taobao.org/file-loader/download/file-loader-0.11.2.tgz#4ff1df28af38719a6098093b88c82c71d1794a34"
  dependencies:
    loader-utils "^1.0.2"

file-type@^3.1.0:
  version "3.9.0"
  resolved "http://registry.npm.taobao.org/file-type/download/file-type-3.9.0.tgz#257a078384d1db8087bc449d107d52a52672b9e9"

filename-regex@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/filename-regex/download/filename-regex-2.0.1.tgz#c1c4b9bee3e09725ddb106b75c1e301fe2f18b26"

filename-reserved-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/filename-reserved-regex/download/filename-reserved-regex-1.0.0.tgz#e61cf805f0de1c984567d0386dc5df50ee5af7e4"

filenamify@^1.0.1:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/filenamify/download/filenamify-1.2.1.tgz#a9f2ffd11c503bed300015029272378f1f1365a5"
  dependencies:
    filename-reserved-regex "^1.0.0"
    strip-outer "^1.0.0"
    trim-repeated "^1.0.0"

fill-range@^2.1.0:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/fill-range/download/fill-range-2.2.3.tgz#50b77dfd7e469bc7492470963699fe7a8485a723"
  dependencies:
    is-number "^2.1.0"
    isobject "^2.0.0"
    randomatic "^1.1.3"
    repeat-element "^1.1.2"
    repeat-string "^1.5.2"

finalhandler@0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.taobao.org/finalhandler/download/finalhandler-0.5.0.tgz#e9508abece9b6dba871a6942a1d7911b91911ac7"
  dependencies:
    debug "~2.2.0"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    statuses "~1.3.0"
    unpipe "~1.0.0"

finalhandler@~1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/finalhandler/download/finalhandler-1.0.3.tgz#ef47e77950e999780e86022a560e3217e0d0cc89"
  dependencies:
    debug "2.6.7"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.1"
    statuses "~1.3.1"
    unpipe "~1.0.0"

find-cache-dir@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-0.1.1.tgz#c8defae57c8a52a8a784f9e31c57c742e993a0b9"
  dependencies:
    commondir "^1.0.1"
    mkdirp "^0.5.1"
    pkg-dir "^1.0.0"

find-index@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/find-index/download/find-index-0.1.1.tgz#675d358b2ca3892d795a1ab47232f8b6e2e0dde4"

find-up@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/find-up/download/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-versions@^1.0.0:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/find-versions/download/find-versions-1.2.1.tgz#cbde9f12e38575a0af1be1b9a2c5d5fd8f186b62"
  dependencies:
    array-uniq "^1.0.0"
    get-stdin "^4.0.1"
    meow "^3.5.0"
    semver-regex "^1.0.0"

findup-sync@~0.1.2:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/findup-sync/download/findup-sync-0.1.3.tgz#7f3e7a97b82392c653bf06589bd85190e93c3683"
  dependencies:
    glob "~3.2.9"
    lodash "~2.4.1"

first-chunk-stream@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/first-chunk-stream/download/first-chunk-stream-1.0.0.tgz#59bfb50cd905f60d7c394cd3d9acaab4e6ad934e"

flat-cache@^1.2.1:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/flat-cache/download/flat-cache-1.2.2.tgz#fa86714e72c21db88601761ecf2f555d1abc6b96"
  dependencies:
    circular-json "^0.3.1"
    del "^2.0.2"
    graceful-fs "^4.1.2"
    write "^0.2.1"

flatten@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/flatten/download/flatten-1.0.2.tgz#dae46a9d78fbe25292258cc1e780a41d95c03782"

for-in@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz#81068d295a8142ec0ac726c6e2200c30fb6d5e80"

for-own@^0.1.4:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/for-own/download/for-own-0.1.5.tgz#5265c681a4f294dabbf17c9509b6763aa84510ce"
  dependencies:
    for-in "^1.0.1"

foreach@^2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.taobao.org/foreach/download/foreach-2.0.5.tgz#0bee005018aeb260d0a3af3ae658dd0136ec1b99"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"

form-data@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/form-data/download/form-data-2.0.0.tgz#6f0aebadcc5da16c13e1ecc11137d85f9b883b25"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.5"
    mime-types "^2.1.11"

form-data@~2.1.1:
  version "2.1.4"
  resolved "http://registry.npm.taobao.org/form-data/download/form-data-2.1.4.tgz#33c183acf193276ecaa98143a69e94bfee1750d1"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.5"
    mime-types "^2.1.12"

formidable@1.0.x:
  version "1.0.17"
  resolved "http://registry.npm.taobao.org/formidable/download/formidable-1.0.17.tgz#ef5491490f9433b705faa77249c99029ae348559"

forwarded@~0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/forwarded/download/forwarded-0.1.0.tgz#19ef9874c4ae1c297bcf078fde63a09b66a84363"

fresh@0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.taobao.org/fresh/download/fresh-0.5.0.tgz#f474ca5e6a9246d6fd8e0953cfa9b9c805afa78e"

fresh@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/fresh/download/fresh-0.3.0.tgz#651f838e22424e7566de161d8358caa199f83d4f"

fs-extra@3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/fs-extra/download/fs-extra-3.0.1.tgz#3794f378c58b342ea7dbbb23095109c4b3b62291"
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^3.0.0"
    universalify "^0.1.0"

fs-vacuum@~1.2.9:
  version "1.2.10"
  resolved "http://registry.npm.taobao.org/fs-vacuum/download/fs-vacuum-1.2.10.tgz#b7629bec07a4031a2548fdf99f5ecf1cc8b31e36"
  dependencies:
    graceful-fs "^4.1.2"
    path-is-inside "^1.0.1"
    rimraf "^2.5.2"

fs-write-stream-atomic@~1.0.8:
  version "1.0.10"
  resolved "http://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz#b47df53493ef911df75731e70a9ded0189db40c9"
  dependencies:
    graceful-fs "^4.1.2"
    iferr "^0.1.5"
    imurmurhash "^0.1.4"
    readable-stream "1 || 2"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

fsevents@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/fsevents/download/fsevents-1.1.2.tgz#3282b713fb3ad80ede0e9fcf4611b5aa6fc033f4"
  dependencies:
    nan "^2.3.0"
    node-pre-gyp "^0.6.36"

fstream-ignore@^1.0.0, fstream-ignore@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/fstream-ignore/download/fstream-ignore-1.0.5.tgz#9c31dae34767018fe1d249b24dada67d092da105"
  dependencies:
    fstream "^1.0.0"
    inherits "2"
    minimatch "^3.0.0"

fstream-npm@~1.2.0:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/fstream-npm/download/fstream-npm-1.2.1.tgz#08c4a452f789dcbac4c89a4563c902b2c862fd5b"
  dependencies:
    fstream-ignore "^1.0.0"
    inherits "2"

fstream@^1.0.0, fstream@^1.0.10, fstream@^1.0.2, fstream@~1.0.10:
  version "1.0.11"
  resolved "http://registry.npm.taobao.org/fstream/download/fstream-1.0.11.tgz#5c1fb1f117477114f0632a0eb4b71b3cb0fd3171"
  dependencies:
    graceful-fs "^4.1.2"
    inherits "~2.0.0"
    mkdirp ">=0.5 0"
    rimraf "2"

function-bind@^1.0.2, function-bind@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/function-bind/download/function-bind-1.1.0.tgz#16176714c801798e4e8f2cf7f7529467bb4a5771"

gauge@~2.6.0:
  version "2.6.0"
  resolved "http://registry.npm.taobao.org/gauge/download/gauge-2.6.0.tgz#d35301ad18e96902b4751dcbbe40f4218b942a46"
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-color "^0.1.7"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gauge@~2.7.1:
  version "2.7.4"
  resolved "http://registry.npm.taobao.org/gauge/download/gauge-2.7.4.tgz#2c03405c7538c39d7eb37b317022e325fb018bf7"
  dependencies:
    aproba "^1.0.3"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.0"
    object-assign "^4.1.0"
    signal-exit "^3.0.0"
    string-width "^1.0.1"
    strip-ansi "^3.0.1"
    wide-align "^1.1.0"

gaze@^0.5.1, gaze@~0.5.1:
  version "0.5.2"
  resolved "http://registry.npm.taobao.org/gaze/download/gaze-0.5.2.tgz#40b709537d24d1d45767db5a908689dfe69ac44f"
  dependencies:
    globule "~0.1.0"

gaze@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/gaze/download/gaze-1.1.2.tgz#847224677adb8870d679257ed3388fdb61e40105"
  dependencies:
    globule "^1.0.0"

generate-function@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/generate-function/download/generate-function-2.0.0.tgz#6858fe7c0969b7d4e9093337647ac79f60dfbe74"

generate-object-property@^1.1.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/generate-object-property/download/generate-object-property-1.2.0.tgz#9c0e1c40308ce804f4783618b937fa88f99d50d0"
  dependencies:
    is-property "^1.0.0"

get-caller-file@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/get-caller-file/download/get-caller-file-1.0.2.tgz#f702e63127e7e231c160a80c1554acb70d5047e5"

get-proxy@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/get-proxy/download/get-proxy-1.1.0.tgz#894854491bc591b0f147d7ae570f5c678b7256eb"
  dependencies:
    rc "^1.1.2"

get-stdin@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/get-stdin/download/get-stdin-4.0.1.tgz#b968c6b0a04384324902e8bf1a5df32579a450fe"

getobject@~0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/getobject/download/getobject-0.1.0.tgz#047a449789fa160d018f5486ed91320b6ec7885c"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  dependencies:
    assert-plus "^1.0.0"

gifsicle@^3.0.0:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/gifsicle/download/gifsicle-3.0.4.tgz#f45cb5ed10165b665dc929e0e9328b6c821dfa3b"
  dependencies:
    bin-build "^2.0.0"
    bin-wrapper "^3.0.0"
    logalot "^2.0.0"

glob-base@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/glob-base/download/glob-base-0.3.0.tgz#dbb164f6221b1c0b1ccf82aea328b497df0ea3c4"
  dependencies:
    glob-parent "^2.0.0"
    is-glob "^2.0.0"

glob-parent@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/glob-parent/download/glob-parent-2.0.0.tgz#81383d72db054fcccf5336daa902f182f6edbb28"
  dependencies:
    is-glob "^2.0.0"

glob-parent@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/glob-parent/download/glob-parent-3.1.0.tgz#9e6af6299d8d3bd2bd40430832bd113df906c5ae"
  dependencies:
    is-glob "^3.1.0"
    path-dirname "^1.0.0"

glob-stream@^4.0.1:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/glob-stream/download/glob-stream-4.1.1.tgz#b842df10d688c7eb6bcfcebd846f3852296b3200"
  dependencies:
    glob "^4.3.1"
    glob2base "^0.0.12"
    minimatch "^2.0.1"
    ordered-read-streams "^0.1.0"
    through2 "^0.6.1"
    unique-stream "^2.0.2"

glob-stream@^5.3.2:
  version "5.3.5"
  resolved "http://registry.npm.taobao.org/glob-stream/download/glob-stream-5.3.5.tgz#a55665a9a8ccdc41915a87c701e32d4e016fad22"
  dependencies:
    extend "^3.0.0"
    glob "^5.0.3"
    glob-parent "^3.0.0"
    micromatch "^2.3.7"
    ordered-read-streams "^0.3.0"
    through2 "^0.6.0"
    to-absolute-glob "^0.1.1"
    unique-stream "^2.0.2"

glob-watcher@^0.0.8:
  version "0.0.8"
  resolved "http://registry.npm.taobao.org/glob-watcher/download/glob-watcher-0.0.8.tgz#68aeb661e7e2ce8d3634381b2ec415f00c6bc2a4"
  dependencies:
    gaze "^0.5.1"

glob2base@^0.0.12:
  version "0.0.12"
  resolved "http://registry.npm.taobao.org/glob2base/download/glob2base-0.0.12.tgz#9d419b3e28f12e83a362164a277055922c9c0d56"
  dependencies:
    find-index "^0.1.1"

glob@^4.3.1:
  version "4.5.3"
  resolved "http://registry.npm.taobao.org/glob/download/glob-4.5.3.tgz#c6cb73d3226c1efef04de3c56d012f03377ee15f"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "^2.0.1"
    once "^1.3.0"

glob@^5.0.3:
  version "5.0.15"
  resolved "http://registry.npm.taobao.org/glob/download/glob-5.0.15.tgz#1bc936b9e02f4a603fcc222ecf7633d30b8b93b1"
  dependencies:
    inflight "^1.0.4"
    inherits "2"
    minimatch "2 || 3"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^7.0.0, glob@^7.0.3, glob@^7.0.5, glob@^7.1.1, glob@~7.1.0, glob@~7.1.1:
  version "7.1.2"
  resolved "http://registry.npm.taobao.org/glob/download/glob-7.1.2.tgz#c19c9df9a028702d678612384a6552404c636d15"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@~3.1.21:
  version "3.1.21"
  resolved "http://registry.npm.taobao.org/glob/download/glob-3.1.21.tgz#d29e0a055dea5138f4d07ed40e8982e83c2066cd"
  dependencies:
    graceful-fs "~1.2.0"
    inherits "1"
    minimatch "~0.2.11"

glob@~3.2.9:
  version "3.2.11"
  resolved "http://registry.npm.taobao.org/glob/download/glob-3.2.11.tgz#4a973f635b9190f715d10987d5c00fd2815ebe3d"
  dependencies:
    inherits "2"
    minimatch "0.3"

globals@^9.0.0, globals@^9.14.0:
  version "9.18.0"
  resolved "http://registry.npm.taobao.org/globals/download/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"

globby@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.taobao.org/globby/download/globby-5.0.0.tgz#ebd84667ca0dbb330b99bcfc68eac2bc54370e0d"
  dependencies:
    array-union "^1.0.1"
    arrify "^1.0.0"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

globule@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/globule/download/globule-1.2.0.tgz#1dc49c6822dd9e8a2fa00ba2a295006e8664bd09"
  dependencies:
    glob "~7.1.1"
    lodash "~4.17.4"
    minimatch "~3.0.2"

globule@~0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/globule/download/globule-0.1.0.tgz#d9c8edde1da79d125a151b79533b978676346ae5"
  dependencies:
    glob "~3.1.21"
    lodash "~1.0.1"
    minimatch "~0.2.11"

glogg@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/glogg/download/glogg-1.0.0.tgz#7fe0f199f57ac906cf512feead8f90ee4a284fc5"
  dependencies:
    sparkles "^1.0.0"

got@^5.0.0:
  version "5.7.1"
  resolved "http://registry.npm.taobao.org/got/download/got-5.7.1.tgz#5f81635a61e4a6589f180569ea4e381680a51f35"
  dependencies:
    create-error-class "^3.0.1"
    duplexer2 "^0.1.4"
    is-redirect "^1.0.0"
    is-retry-allowed "^1.0.0"
    is-stream "^1.0.0"
    lowercase-keys "^1.0.0"
    node-status-codes "^1.0.0"
    object-assign "^4.0.1"
    parse-json "^2.1.0"
    pinkie-promise "^2.0.0"
    read-all-stream "^3.0.0"
    readable-stream "^2.0.5"
    timed-out "^3.0.0"
    unzip-response "^1.0.2"
    url-parse-lax "^1.0.0"

graceful-fs@^3.0.0:
  version "3.0.11"
  resolved "http://registry.npm.taobao.org/graceful-fs/download/graceful-fs-3.0.11.tgz#7613c778a1afea62f25c630a086d7f3acbbdd818"
  dependencies:
    natives "^1.1.0"

graceful-fs@^4.0.0, graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@~4.1.9:
  version "4.1.11"
  resolved "http://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.1.11.tgz#0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658"

graceful-fs@~1.2.0:
  version "1.2.3"
  resolved "http://registry.npm.taobao.org/graceful-fs/download/graceful-fs-1.2.3.tgz#15a4806a57547cb2d2dbf27f42e89a8c3451b364"

"graceful-readlink@>= 1.0.0":
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/graceful-readlink/download/graceful-readlink-1.0.1.tgz#4cafad76bc62f02fa039b2f94e9a3dd3a391a725"

grunt-autoprefixer@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/grunt-autoprefixer/download/grunt-autoprefixer-3.0.4.tgz#fe42e247bcfab9c292a12c062dad4f35bde902c5"
  dependencies:
    autoprefixer-core "^5.1.7"
    chalk "~1.0.0"
    diff "~1.3.0"
    postcss "^4.1.11"

grunt-available-tasks@^0.5.7:
  version "0.5.8"
  resolved "http://registry.npm.taobao.org/grunt-available-tasks/download/grunt-available-tasks-0.5.8.tgz#8d068131199930302d438803f3d1903d8238bdc4"
  dependencies:
    chalk "^1.1.0"
    lodash "~3.10.0"
    underscore.string "^3.1.1"

grunt-browser-sync@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/grunt-browser-sync/download/grunt-browser-sync-2.2.0.tgz#a0e9c1fd1ccb5c454c25ec5170113ffff06a4772"
  dependencies:
    browser-sync "^2.6.4"

grunt-contrib-compass@^1.0.3:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/grunt-contrib-compass/download/grunt-contrib-compass-1.1.1.tgz#3c25d209e018825aac9fa82315bbe6dfc1c636dc"
  dependencies:
    async "^1.5.2"
    bin-version-check "^2.0.0"
    dargs "^2.0.3"
    onetime "^1.0.0"
    tmp "0.0.28"
    which "^1.0.9"

grunt-contrib-copy@~0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.taobao.org/grunt-contrib-copy/download/grunt-contrib-copy-0.7.0.tgz#c6de48e0df731449aedb0f089c095dbc2a55050f"
  dependencies:
    chalk "~0.5.1"

grunt-contrib-cssmin@~0.10.0:
  version "0.10.0"
  resolved "http://registry.npm.taobao.org/grunt-contrib-cssmin/download/grunt-contrib-cssmin-0.10.0.tgz#e05f341e753a9674b2b1070220fdcbac22079418"
  dependencies:
    chalk "~0.4.0"
    clean-css "~2.2.0"
    maxmin "~0.2.0"

grunt-contrib-imagemin@~0.9.3:
  version "0.9.4"
  resolved "http://registry.npm.taobao.org/grunt-contrib-imagemin/download/grunt-contrib-imagemin-0.9.4.tgz#dba724b0b8978facedec1e0c6235f2253006c1fa"
  dependencies:
    async "^0.9.0"
    chalk "^1.0.0"
    gulp-rename "^1.2.0"
    imagemin "^3.1.0"
    pretty-bytes "^1.0.1"

grunt-contrib-watch@~0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/grunt-contrib-watch/download/grunt-contrib-watch-0.6.1.tgz#64fdcba25a635f5b4da1b6ce6f90da0aeb6e3f15"
  dependencies:
    async "~0.2.9"
    gaze "~0.5.1"
    lodash "~2.4.1"
    tiny-lr-fork "0.0.5"

grunt-legacy-log-utils@~0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/grunt-legacy-log-utils/download/grunt-legacy-log-utils-0.1.1.tgz#c0706b9dd9064e116f36f23fe4e6b048672c0f7e"
  dependencies:
    colors "~0.6.2"
    lodash "~2.4.1"
    underscore.string "~2.3.3"

grunt-legacy-log@~0.1.0:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/grunt-legacy-log/download/grunt-legacy-log-0.1.3.tgz#ec29426e803021af59029f87d2f9cd7335a05531"
  dependencies:
    colors "~0.6.2"
    grunt-legacy-log-utils "~0.1.1"
    hooker "~0.2.3"
    lodash "~2.4.1"
    underscore.string "~2.3.3"

grunt-legacy-util@~0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/grunt-legacy-util/download/grunt-legacy-util-0.2.0.tgz#93324884dbf7e37a9ff7c026dff451d94a9e554b"
  dependencies:
    async "~0.1.22"
    exit "~0.1.1"
    getobject "~0.1.0"
    hooker "~0.2.3"
    lodash "~0.9.2"
    underscore.string "~2.2.1"
    which "~1.0.5"

grunt-sass@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/grunt-sass/download/grunt-sass-1.0.0.tgz#84602a9ab0fcb0fe340737612a4ba56cc361bbc4"
  dependencies:
    each-async "^1.0.0"
    node-sass "^3.0.0"
    object-assign "^2.0.0"

grunt-webpack@^1.0.14:
  version "1.0.18"
  resolved "http://registry.npm.taobao.org/grunt-webpack/download/grunt-webpack-1.0.18.tgz#ff26c43ff35bae6cca707a93c4bcdd950a3ecbb7"
  dependencies:
    lodash "^4.7.0"

grunt@^0.4.5:
  version "0.4.5"
  resolved "http://registry.npm.taobao.org/grunt/download/grunt-0.4.5.tgz#56937cd5194324adff6d207631832a9d6ba4e7f0"
  dependencies:
    async "~0.1.22"
    coffee-script "~1.3.3"
    colors "~0.6.2"
    dateformat "1.0.2-1.2.3"
    eventemitter2 "~0.4.13"
    exit "~0.1.1"
    findup-sync "~0.1.2"
    getobject "~0.1.0"
    glob "~3.1.21"
    grunt-legacy-log "~0.1.0"
    grunt-legacy-util "~0.2.0"
    hooker "~0.2.3"
    iconv-lite "~0.2.11"
    js-yaml "~2.0.5"
    lodash "~0.9.2"
    minimatch "~0.2.12"
    nopt "~1.0.10"
    rimraf "~2.2.8"
    underscore.string "~2.2.1"
    which "~1.0.5"

gulp-decompress@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/gulp-decompress/download/gulp-decompress-1.2.0.tgz#8eeb65a5e015f8ed8532cafe28454960626f0dc7"
  dependencies:
    archive-type "^3.0.0"
    decompress "^3.0.0"
    gulp-util "^3.0.1"
    readable-stream "^2.0.2"

gulp-rename@^1.2.0:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/gulp-rename/download/gulp-rename-1.2.2.tgz#3ad4428763f05e2764dec1c67d868db275687817"

gulp-sourcemaps@1.6.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/gulp-sourcemaps/download/gulp-sourcemaps-1.6.0.tgz#b86ff349d801ceb56e1d9e7dc7bbcb4b7dee600c"
  dependencies:
    convert-source-map "^1.1.1"
    graceful-fs "^4.1.2"
    strip-bom "^2.0.0"
    through2 "^2.0.0"
    vinyl "^1.0.0"

gulp-util@^3.0.1:
  version "3.0.8"
  resolved "http://registry.npm.taobao.org/gulp-util/download/gulp-util-3.0.8.tgz#0054e1e744502e27c04c187c3ecc505dd54bbb4f"
  dependencies:
    array-differ "^1.0.0"
    array-uniq "^1.0.2"
    beeper "^1.0.0"
    chalk "^1.0.0"
    dateformat "^2.0.0"
    fancy-log "^1.1.0"
    gulplog "^1.0.0"
    has-gulplog "^0.1.0"
    lodash._reescape "^3.0.0"
    lodash._reevaluate "^3.0.0"
    lodash._reinterpolate "^3.0.0"
    lodash.template "^3.0.0"
    minimist "^1.1.0"
    multipipe "^0.1.2"
    object-assign "^3.0.0"
    replace-ext "0.0.1"
    through2 "^2.0.0"
    vinyl "^0.5.0"

gulplog@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/gulplog/download/gulplog-1.0.0.tgz#e28c4d45d05ecbbed818363ce8f9c5926229ffe5"
  dependencies:
    glogg "^1.0.0"

gzip-size@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/gzip-size/download/gzip-size-0.2.0.tgz#e3a2a191205fe56ee326f5c271435dfaecfb3e1c"
  dependencies:
    browserify-zlib "^0.1.4"
    concat-stream "^1.4.1"

har-schema@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/har-schema/download/har-schema-1.0.5.tgz#d263135f43307c02c602afc8fe95970c0151369e"

har-validator@~2.0.6:
  version "2.0.6"
  resolved "http://registry.npm.taobao.org/har-validator/download/har-validator-2.0.6.tgz#cdcbc08188265ad119b6a5a7c8ab70eecfb5d27d"
  dependencies:
    chalk "^1.1.1"
    commander "^2.9.0"
    is-my-json-valid "^2.12.4"
    pinkie-promise "^2.0.0"

har-validator@~4.2.1:
  version "4.2.1"
  resolved "http://registry.npm.taobao.org/har-validator/download/har-validator-4.2.1.tgz#33481d0f1bbff600dd203d75812a6a5fba002e2a"
  dependencies:
    ajv "^4.9.1"
    har-schema "^1.0.5"

has-ansi@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/has-ansi/download/has-ansi-0.1.0.tgz#84f265aae8c0e6a88a12d7022894b7568894c62e"
  dependencies:
    ansi-regex "^0.2.0"

has-ansi@^1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/has-ansi/download/has-ansi-1.0.3.tgz#c0b5b1615d9e382b0ff67169d967b425e48ca538"
  dependencies:
    ansi-regex "^1.1.0"
    get-stdin "^4.0.1"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-binary@0.1.6:
  version "0.1.6"
  resolved "http://registry.npm.taobao.org/has-binary/download/has-binary-0.1.6.tgz#25326f39cfa4f616ad8787894e3af2cfbc7b6e10"
  dependencies:
    isarray "0.0.1"

has-binary@0.1.7:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/has-binary/download/has-binary-0.1.7.tgz#68e61eb16210c9545a0a5cce06a873912fe1e68c"
  dependencies:
    isarray "0.0.1"

has-color@^0.1.7, has-color@~0.1.0:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/has-color/download/has-color-0.1.7.tgz#67144a5260c34fc3cca677d041daf52fe7b78b2f"

has-cors@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/has-cors/download/has-cors-1.1.0.tgz#5e474793f7ea9843d1bb99c23eef49ff126fff39"

has-flag@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/has-flag/download/has-flag-1.0.0.tgz#9d9e793165ce017a00f00418c43f942a7b1d11fa"

has-gulplog@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/has-gulplog/download/has-gulplog-0.1.0.tgz#6414c82913697da51590397dafb12f22967811ce"
  dependencies:
    sparkles "^1.0.0"

has-unicode@^2.0.0, has-unicode@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/has-unicode/download/has-unicode-2.0.1.tgz#e0e6fe6a28cf51138855e086d1691e771de2a8b9"

has@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/has/download/has-1.0.1.tgz#8461733f538b0837c9361e39a9ab9e9704dc2f28"
  dependencies:
    function-bind "^1.0.2"

hash-sum@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz#33b40777754c6432573c120cc3808bbd10d47f04"

hawk@~3.1.3:
  version "3.1.3"
  resolved "http://registry.npm.taobao.org/hawk/download/hawk-3.1.3.tgz#078444bd7c1640b0fe540d2c9b73d59678e8e1c4"
  dependencies:
    boom "2.x.x"
    cryptiles "2.x.x"
    hoek "2.x.x"
    sntp "1.x.x"

he@1.1.x:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/he/download/he-1.1.1.tgz#93410fd21b009735151f8868c2f271f3427e23fd"

header-case@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/header-case/download/header-case-1.0.1.tgz#9535973197c144b09613cd65d317ef19963bd02d"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.1.3"

hoek@2.x.x:
  version "2.16.3"
  resolved "http://registry.npm.taobao.org/hoek/download/hoek-2.16.3.tgz#20bb7403d3cea398e91dc4710a8ff1b8274a25ed"

home-or-tmp@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/home-or-tmp/download/home-or-tmp-2.0.0.tgz#e36c3f2d2cae7d746a857e38d18d5f32a7882db8"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.1"

hooker@~0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.taobao.org/hooker/download/hooker-0.2.3.tgz#b834f723cc4a242aa65963459df6d984c5d3d959"

hosted-git-info@^2.1.4, hosted-git-info@^2.1.5, hosted-git-info@~2.1.5:
  version "2.1.5"
  resolved "http://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.1.5.tgz#0ba81d90da2e25ab34a332e6ec77936e1598118b"

html-comment-regex@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/html-comment-regex/download/html-comment-regex-1.1.1.tgz#668b93776eaae55ebde8f3ad464b307a4963625e"

html-minifier@^2.1.5:
  version "2.1.7"
  resolved "http://registry.npm.taobao.org/html-minifier/download/html-minifier-2.1.7.tgz#9051d6fcbbcf214ed307e1ad74f432bb9ad655cc"
  dependencies:
    change-case "3.0.x"
    clean-css "3.4.x"
    commander "2.9.x"
    he "1.1.x"
    ncname "1.0.x"
    relateurl "0.2.x"
    uglify-js "2.6.x"

http-errors@~1.5.0:
  version "1.5.1"
  resolved "http://registry.npm.taobao.org/http-errors/download/http-errors-1.5.1.tgz#788c0d2c1de2c81b9e6e8c01843b6b97eb920750"
  dependencies:
    inherits "2.0.3"
    setprototypeof "1.0.2"
    statuses ">= 1.3.1 < 2"

http-errors@~1.6.1:
  version "1.6.1"
  resolved "http://registry.npm.taobao.org/http-errors/download/http-errors-1.6.1.tgz#5f8b8ed98aca545656bf572997387f904a722257"
  dependencies:
    depd "1.1.0"
    inherits "2.0.3"
    setprototypeof "1.0.3"
    statuses ">= 1.3.1 < 2"

http-proxy@1.15.2, http-proxy@^1.11.2:
  version "1.15.2"
  resolved "http://registry.npm.taobao.org/http-proxy/download/http-proxy-1.15.2.tgz#642fdcaffe52d3448d2bda3b0079e9409064da31"
  dependencies:
    eventemitter3 "1.x.x"
    requires-port "1.x.x"

http-signature@~1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/http-signature/download/http-signature-1.1.1.tgz#df72e267066cd0ac67fb76adf8e134a8fbcf91bf"
  dependencies:
    assert-plus "^0.2.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

https-browserify@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/https-browserify/download/https-browserify-0.0.1.tgz#3f91365cabe60b77ed0ebba24b454e3e09d95a82"

iconv-lite@~0.2.11:
  version "0.2.11"
  resolved "http://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.2.11.tgz#1ce60a3a57864a292d1321ff4609ca4bb965adc8"

icss-replace-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz#06ea6f83679a7749e386cfe1fe812ae5db223ded"

ieee754@^1.1.4:
  version "1.1.8"
  resolved "http://registry.npm.taobao.org/ieee754/download/ieee754-1.1.8.tgz#be33d40ac10ef1926701f6f08a2d86fbfd1ad3e4"

iferr@^0.1.5, iferr@~0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz#c60eed69e6d8fdb6b3104a1fcbca1c192dc5b501"

ignore@^3.2.0:
  version "3.3.3"
  resolved "http://registry.npm.taobao.org/ignore/download/ignore-3.3.3.tgz#432352e57accd87ab3110e82d3fea0e47812156d"

imagemin-gifsicle@^4.0.0:
  version "4.2.0"
  resolved "http://registry.npm.taobao.org/imagemin-gifsicle/download/imagemin-gifsicle-4.2.0.tgz#0fef9bbad3476e6b76885736cc5b0b87a08757ca"
  dependencies:
    gifsicle "^3.0.0"
    is-gif "^1.0.0"
    through2 "^0.6.1"

imagemin-jpegtran@^4.0.0:
  version "4.3.2"
  resolved "http://registry.npm.taobao.org/imagemin-jpegtran/download/imagemin-jpegtran-4.3.2.tgz#1bc6d1e2bd13fdb64d245526d635a7e5dfeb12fc"
  dependencies:
    is-jpg "^1.0.0"
    jpegtran-bin "^3.0.0"
    through2 "^2.0.0"

imagemin-optipng@^4.0.0:
  version "4.3.0"
  resolved "http://registry.npm.taobao.org/imagemin-optipng/download/imagemin-optipng-4.3.0.tgz#7604663ab2ee315733274726fd1c374d2b44adb6"
  dependencies:
    exec-buffer "^2.0.0"
    is-png "^1.0.0"
    optipng-bin "^3.0.0"
    through2 "^0.6.1"

imagemin-svgo@^4.0.0:
  version "4.2.1"
  resolved "http://registry.npm.taobao.org/imagemin-svgo/download/imagemin-svgo-4.2.1.tgz#54f07dc56f47260462df6a61c54befb44b57be55"
  dependencies:
    is-svg "^1.0.0"
    svgo "^0.6.0"
    through2 "^2.0.0"

imagemin@^3.1.0:
  version "3.2.2"
  resolved "http://registry.npm.taobao.org/imagemin/download/imagemin-3.2.2.tgz#17a8ad67e0e8ee6ae1ae0c5cb752b26579452366"
  dependencies:
    buffer-to-vinyl "^1.0.0"
    concat-stream "^1.4.6"
    get-stdin "^4.0.1"
    meow "^3.3.0"
    optional "^0.1.0"
    path-exists "^1.0.0"
    readable-stream "^2.0.0"
    stream-combiner2 "^1.1.1"
    vinyl-fs "^1.0.0"
  optionalDependencies:
    imagemin-gifsicle "^4.0.0"
    imagemin-jpegtran "^4.0.0"
    imagemin-optipng "^4.0.0"
    imagemin-svgo "^4.0.0"

immutable@3.8.1, immutable@^3.7.6:
  version "3.8.1"
  resolved "http://registry.npm.taobao.org/immutable/download/immutable-3.8.1.tgz#200807f11ab0f72710ea485542de088075f68cd2"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"

in-publish@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/in-publish/download/in-publish-2.0.0.tgz#e20ff5e3a2afc2690320b6dc552682a9c7fadf51"

indent-string@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/indent-string/download/indent-string-2.1.0.tgz#8e2d48348742121b4a8218b7a137e9a52049dc80"
  dependencies:
    repeating "^2.0.0"

indexes-of@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz#f30f716c8e2bd346c7b67d3df3915566a7c05607"

indexof@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/indexof/download/indexof-0.0.1.tgz#82dc336d232b9062179d05ab3293a66059fd435d"

inflight@^1.0.4, inflight@~1.0.5:
  version "1.0.6"
  resolved "http://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/inherits/download/inherits-1.0.2.tgz#ca4309dadee6b54cc0b8d247e8d7c7a0975bdc9b"

inherits@2, inherits@2.0.3, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.0, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

inherits@2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/inherits/download/inherits-2.0.1.tgz#b17d08d326b4423e568eff719f91b0b1cbdf69f1"

ini@^1.3.4, ini@~1.3.0, ini@~1.3.4:
  version "1.3.4"
  resolved "http://registry.npm.taobao.org/ini/download/ini-1.3.4.tgz#0537cb79daf59b59a1a517dff706c86ec039162e"

init-package-json@~1.9.4:
  version "1.9.6"
  resolved "http://registry.npm.taobao.org/init-package-json/download/init-package-json-1.9.6.tgz#789fc2b74466a4952b9ea77c0575bc78ebd60a61"
  dependencies:
    glob "^7.1.1"
    npm-package-arg "^4.0.0 || ^5.0.0"
    promzard "^0.3.0"
    read "~1.0.1"
    read-package-json "1 || 2"
    semver "2.x || 3.x || 4 || 5"
    validate-npm-package-license "^3.0.1"
    validate-npm-package-name "^3.0.0"

inquirer@^0.12.0:
  version "0.12.0"
  resolved "http://registry.npm.taobao.org/inquirer/download/inquirer-0.12.0.tgz#1ef2bfd63504df0bc75785fff8c2c41df12f077e"
  dependencies:
    ansi-escapes "^1.1.0"
    ansi-regex "^2.0.0"
    chalk "^1.0.0"
    cli-cursor "^1.0.1"
    cli-width "^2.0.0"
    figures "^1.3.5"
    lodash "^4.3.0"
    readline2 "^1.0.1"
    run-async "^0.1.0"
    rx-lite "^3.1.2"
    string-width "^1.0.1"
    strip-ansi "^3.0.0"
    through "^2.3.6"

install@^0.8.1:
  version "0.8.9"
  resolved "http://registry.npm.taobao.org/install/download/install-0.8.9.tgz#9f4b5c0d1851ef872e9df85e4f7162d4e5dcdbed"

interpret@^0.6.4:
  version "0.6.6"
  resolved "http://registry.npm.taobao.org/interpret/download/interpret-0.6.6.tgz#fecd7a18e7ce5ca6abfb953e1f86213a49f1625b"

interpret@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/interpret/download/interpret-1.0.3.tgz#cbc35c62eeee73f19ab7b10a801511401afc0f90"

invariant@^2.2.0:
  version "2.2.2"
  resolved "http://registry.npm.taobao.org/invariant/download/invariant-2.2.2.tgz#9e1f56ac0acdb6bf303306f338be3b204ae60360"
  dependencies:
    loose-envify "^1.0.0"

invert-kv@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/invert-kv/download/invert-kv-1.0.0.tgz#104a8e4aaca6d3d8cd157a8ef8bfab2d7a3ffdb6"

ip-regex@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/ip-regex/download/ip-regex-1.0.3.tgz#dc589076f659f419c222039a33316f1c7387effd"

ipaddr.js@1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.3.0.tgz#1e03a52fdad83a8bbb2b25cbf4998b4cffcd3dec"

is-absolute-url@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz#50530dfb84fcc9aa7dbe7852e83a37b93b9f2aa6"

is-absolute@^0.1.5:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/is-absolute/download/is-absolute-0.1.7.tgz#847491119fccb5fb436217cc737f7faad50f603f"
  dependencies:
    is-relative "^0.1.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-binary-path@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz#75f16642b480f187a711c814161fd3a4a7655898"
  dependencies:
    binary-extensions "^1.0.0"

is-buffer@^1.1.5:
  version "1.1.5"
  resolved "http://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.5.tgz#1f3b26ef613b214b88cbca23cc6c01d87961eecc"

is-builtin-module@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-builtin-module/download/is-builtin-module-1.0.0.tgz#540572d34f7ac3119f8f76c30cbc1b1e037affbe"
  dependencies:
    builtin-modules "^1.0.0"

is-bzip2@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-bzip2/download/is-bzip2-1.0.0.tgz#5ee58eaa5a2e9c80e21407bedf23ae5ac091b3fc"

is-callable@^1.1.1, is-callable@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/is-callable/download/is-callable-1.1.3.tgz#86eb75392805ddc33af71c92a0eedf74ee7604b2"

is-date-object@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-date-object/download/is-date-object-1.0.1.tgz#9aa20eb6aeebbff77fbd33e74ca01b33581d3a16"

is-dotfile@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/is-dotfile/download/is-dotfile-1.0.3.tgz#a6a2f32ffd2dfb04f5ca25ecd0f6b83cf798a1e1"

is-equal-shallow@^0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/is-equal-shallow/download/is-equal-shallow-0.1.3.tgz#2238098fc221de0bcfa5d9eac4c45d638aa1c534"
  dependencies:
    is-primitive "^2.0.0"

is-extendable@^0.1.0, is-extendable@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz#62b110e289a471418e3ec36a617d472e301dfc89"

is-extglob@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-extglob/download/is-extglob-1.0.0.tgz#ac468177c4943405a092fc8f29760c6ffc6206c0"

is-extglob@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"

is-finite@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/is-finite/download/is-finite-1.0.2.tgz#cc6677695602be550ef11e8b4aa6305342b6d0aa"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz#ef9e31386f031a7f0d643af82fde50c457ef00cb"
  dependencies:
    number-is-nan "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-gif@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-gif/download/is-gif-1.0.0.tgz#a6d2ae98893007bffa97a1d8c01d63205832097e"

is-glob@^2.0.0, is-glob@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/is-glob/download/is-glob-2.0.1.tgz#d096f926a3ded5600f3fdfd91198cb0888c2d863"
  dependencies:
    is-extglob "^1.0.0"

is-glob@^3.1.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz#7ba5ae24217804ac70707b96922567486cc3e84a"
  dependencies:
    is-extglob "^2.1.0"

is-gzip@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-gzip/download/is-gzip-1.0.0.tgz#6ca8b07b99c77998025900e555ced8ed80879a83"

is-jpg@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-jpg/download/is-jpg-1.0.0.tgz#2959c17e73430db38264da75b90dd54f2d86da1c"

is-lower-case@^1.1.0:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/is-lower-case/download/is-lower-case-1.1.3.tgz#7e147be4768dc466db3bfb21cc60b31e6ad69393"
  dependencies:
    lower-case "^1.1.0"

is-my-json-valid@^2.10.0, is-my-json-valid@^2.12.4:
  version "2.16.0"
  resolved "http://registry.npm.taobao.org/is-my-json-valid/download/is-my-json-valid-2.16.0.tgz#f079dd9bfdae65ee2038aae8acbc86ab109e3693"
  dependencies:
    generate-function "^2.0.0"
    generate-object-property "^1.1.0"
    jsonpointer "^4.0.0"
    xtend "^4.0.0"

is-natural-number@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/is-natural-number/download/is-natural-number-2.1.1.tgz#7d4c5728377ef386c3e194a9911bf57c6dc335e7"

is-number-like@^1.0.3:
  version "1.0.7"
  resolved "http://registry.npm.taobao.org/is-number-like/download/is-number-like-1.0.7.tgz#a38d6b0fd2cd4282449128859eed86c03fd23552"
  dependencies:
    bubleify "^0.5.1"
    lodash.isfinite "^3.3.2"

is-number@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/is-number/download/is-number-2.1.0.tgz#01fcbbb393463a548f2f466cce16dece49db908f"
  dependencies:
    kind-of "^3.0.2"

is-number@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz#24fd6201a4782cf50561c810276afc7d12d71195"
  dependencies:
    kind-of "^3.0.2"

is-obj@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-obj/download/is-obj-1.0.1.tgz#3e4729ac1f5fde025cd7d83a896dab9f4f67db0f"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"

is-path-in-cwd@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-path-in-cwd/download/is-path-in-cwd-1.0.0.tgz#6477582b8214d602346094567003be8a9eac04dc"
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-path-inside/download/is-path-inside-1.0.0.tgz#fc06e5a1683fbda13de667aff717bbc10a48f37f"
  dependencies:
    path-is-inside "^1.0.1"

is-plain-obj@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-plain-obj/download/is-plain-obj-1.1.0.tgz#71a50c8429dfca773c92a390a4a03b39fcd51d3e"

is-png@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-png/download/is-png-1.1.0.tgz#d574b12bf275c0350455570b0e5b57ab062077ce"

is-posix-bracket@^0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/is-posix-bracket/download/is-posix-bracket-0.1.1.tgz#3334dc79774368e92f016e6fbc0a88f5cd6e6bc4"

is-primitive@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/is-primitive/download/is-primitive-2.0.0.tgz#207bab91638499c07b2adf240a41a87210034575"

is-property@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/is-property/download/is-property-1.0.2.tgz#57fe1c4e48474edd65b09911f26b1cd4095dda84"

is-redirect@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-redirect/download/is-redirect-1.0.0.tgz#1d03dded53bd8db0f30c26e4f95d36fc7c87dc24"

is-regex@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/is-regex/download/is-regex-1.0.4.tgz#5517489b547091b0930e095654ced25ee97e9491"
  dependencies:
    has "^1.0.1"

is-relative@^0.1.0:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/is-relative/download/is-relative-0.1.3.tgz#905fee8ae86f45b3ec614bc3c15c869df0876e82"

is-resolvable@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.0.0.tgz#8df57c61ea2e3c501408d100fb013cf8d6e0cc62"
  dependencies:
    tryit "^1.0.1"

is-retry-allowed@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-retry-allowed/download/is-retry-allowed-1.1.0.tgz#11a060568b67339444033d0125a61a20d564fb34"

is-stream@^1.0.0, is-stream@^1.0.1:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz#12d4a3dd4e68e0b79ceb8dbc84173ae80d91ca44"

is-svg@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/is-svg/download/is-svg-1.1.1.tgz#ac0efaafb653ac58473708b1f873636ca110e31b"

is-svg@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/is-svg/download/is-svg-2.1.0.tgz#cf61090da0d9efbcab8722deba6f032208dbb0e9"
  dependencies:
    html-comment-regex "^1.1.0"

is-symbol@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/is-symbol/download/is-symbol-1.0.1.tgz#3cc59f00025194b6ab2e38dbae6689256b660572"

is-tar@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-tar/download/is-tar-1.0.0.tgz#2f6b2e1792c1f5bb36519acaa9d65c0d26fe853d"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"

is-upper-case@^1.1.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/is-upper-case/download/is-upper-case-1.1.2.tgz#8d0b1fa7e7933a1e58483600ec7d9661cbaf756f"
  dependencies:
    upper-case "^1.1.0"

is-url@^1.2.0:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/is-url/download/is-url-1.2.2.tgz#498905a593bf47cc2d9e7f738372bbf7696c7f26"

is-utf8@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/is-utf8/download/is-utf8-0.2.1.tgz#4b0da1442104d1b336340e80797e865cf39f7d72"

is-valid-glob@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/is-valid-glob/download/is-valid-glob-0.3.0.tgz#d4b55c69f51886f9b65c70d6c2622d37e29f48fe"

is-zip@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/is-zip/download/is-zip-1.0.0.tgz#47b0a8ff4d38a76431ccfd99a8e15a4c86ba2325"

isarray@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"

isarray@1.0.0, isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"

isobject@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz#f065561096a3f1da2ef46272f815c840d87e0c89"
  dependencies:
    isarray "1.0.0"

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"

jju@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/jju/download/jju-1.3.0.tgz#dadd9ef01924bc728b03f2f7979bdbd62f7a2aaa"

jpegtran-bin@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/jpegtran-bin/download/jpegtran-bin-3.2.0.tgz#f60ecf4ae999c0bdad2e9fbcdf2b6f0981e7a29b"
  dependencies:
    bin-build "^2.0.0"
    bin-wrapper "^3.0.0"
    logalot "^2.0.0"

js-base64@^2.1.8, js-base64@^2.1.9, js-base64@~2.1.8:
  version "2.1.9"
  resolved "http://registry.npm.taobao.org/js-base64/download/js-base64-2.1.9.tgz#f0e80ae039a4bd654b5f281fc93f04a914a7fcce"

js-tokens@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/js-tokens/download/js-tokens-3.0.1.tgz#08e9f132484a2c45a30907e9dc4d5567b7f114d7"

js-yaml@^3.5.1:
  version "3.8.4"
  resolved "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.8.4.tgz#520b4564f86573ba96662af85a8cafa7b4b5a6f6"
  dependencies:
    argparse "^1.0.7"
    esprima "^3.1.1"

js-yaml@~2.0.5:
  version "2.0.5"
  resolved "http://registry.npm.taobao.org/js-yaml/download/js-yaml-2.0.5.tgz#a25ae6509999e97df278c6719da11bd0687743a8"
  dependencies:
    argparse "~ 0.1.11"
    esprima "~ 1.0.2"

js-yaml@~3.6.0:
  version "3.6.1"
  resolved "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.6.1.tgz#6e5fe67d8b205ce4d22fad05b7781e8dadcc4b30"
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

js-yaml@~3.7.0:
  version "3.7.0"
  resolved "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.7.0.tgz#5c967ddd837a9bfdca5f2de84253abe8a1c03b80"
  dependencies:
    argparse "^1.0.7"
    esprima "^2.6.0"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"

jsesc@^1.3.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/jsesc/download/jsesc-1.3.0.tgz#46c3fec8c1892b12b0833db9bc7622176dbab34b"

jsesc@~0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz#e7dee66e35d6fc16f710fe91d5cf69f70f08911d"

json-parse-helpfulerror@^1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/json-parse-helpfulerror/download/json-parse-helpfulerror-1.0.3.tgz#13f14ce02eed4e981297b64eb9e3b932e2dd13dc"
  dependencies:
    jju "^1.1.0"

json-schema@0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz#b480c892e59a2f05954ce727bd3f2a4e882f9e13"

json-stable-stringify@^1.0.0, json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/json-stable-stringify/download/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
  dependencies:
    jsonify "~0.0.0"

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"

json3@3.3.2, json3@^3.3.2:
  version "3.3.2"
  resolved "http://registry.npm.taobao.org/json3/download/json3-3.3.2.tgz#3c0434743df93e2f5c42aee7b19bcb483575f4e1"

json5@^0.5.0:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz#1eade7acc012034ad84e2396767ead9fa5495821"

jsonfile@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/jsonfile/download/jsonfile-3.0.0.tgz#92e7c7444e5ffd5fa32e6a9ae8b85034df8347d0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.taobao.org/jsonify/download/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"

jsonpointer@^4.0.0:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/jsonpointer/download/jsonpointer-4.0.1.tgz#4fd92cb34e0e9db3c89c8622ecf51f9b978c6cb9"

jsprim@^1.2.2:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/jsprim/download/jsprim-1.4.0.tgz#a3b87e40298d8c380552d8cc7628a0bb95a22918"
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.0.2"
    json-schema "0.2.3"
    verror "1.3.6"

jsx-ast-utils@^1.3.4:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/jsx-ast-utils/download/jsx-ast-utils-1.4.1.tgz#3867213e8dd79bf1e8f2300c0cfc1efb182c0df1"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  dependencies:
    is-buffer "^1.1.5"

kind-of@^4.0.0:
  version "4.0.0"
  resolved "http://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz#20813df3d712928b207378691a45066fae72dd57"
  dependencies:
    is-buffer "^1.1.5"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/lazy-cache/download/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"

lazy-req@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/lazy-req/download/lazy-req-1.1.0.tgz#bdaebead30f8d824039ce0ce149d4daa07ba1fac"

lazystream@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/lazystream/download/lazystream-1.0.0.tgz#f6995fe0f820392f61396be89462407bb77168e4"
  dependencies:
    readable-stream "^2.0.5"

lcid@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/lcid/download/lcid-1.0.0.tgz#308accafa0bc483a3867b4b6f2b9506251d1b835"
  dependencies:
    invert-kv "^1.0.0"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/levn/download/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

limiter@^1.0.5:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/limiter/download/limiter-1.1.0.tgz#6e2bd12ca3fcdaa11f224e2e53c896df3f08d913"

load-json-file@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/load-json-file/download/load-json-file-1.1.0.tgz#956905708d58b4bab4c2261b04f59f31c99374c0"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    strip-bom "^2.0.0"

loader-fs-cache@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/loader-fs-cache/download/loader-fs-cache-1.0.1.tgz#56e0bf08bd9708b26a765b68509840c8dec9fdbc"
  dependencies:
    find-cache-dir "^0.1.1"
    mkdirp "0.5.1"

loader-utils@^0.2.10, loader-utils@^0.2.11, loader-utils@^0.2.15, loader-utils@^0.2.16, loader-utils@^0.2.7, loader-utils@~0.2.2:
  version "0.2.17"
  resolved "http://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz#f86e6374d43205a6e6c60e9196f17c0299bfb348"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"
    object-assign "^4.0.1"

loader-utils@^1.0.2:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/loader-utils/download/loader-utils-1.1.0.tgz#c98aef488bcceda2ffb5e2de646d6a754429f5cd"
  dependencies:
    big.js "^3.1.3"
    emojis-list "^2.0.0"
    json5 "^0.5.0"

localtunnel@1.8.2:
  version "1.8.2"
  resolved "http://registry.npm.taobao.org/localtunnel/download/localtunnel-1.8.2.tgz#913051e8328b51f75ad8a22ad1f5c5b8c599a359"
  dependencies:
    debug "2.2.0"
    openurl "1.1.0"
    request "2.78.0"
    yargs "3.29.0"

lockfile@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/lockfile/download/lockfile-1.0.3.tgz#2638fc39a0331e9cac1a04b71799931c9c50df79"

lodash._basecopy@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/lodash._basecopy/download/lodash._basecopy-3.0.1.tgz#8da0e6a876cf344c0ad8a54882111dd3c5c7ca36"

lodash._basetostring@^3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/lodash._basetostring/download/lodash._basetostring-3.0.1.tgz#d1861d877f824a52f669832dcaf3ee15566a07d5"

lodash._baseuniq@~4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.taobao.org/lodash._baseuniq/download/lodash._baseuniq-4.6.0.tgz#0ebb44e456814af7905c6212fa2c9b2d51b841e8"
  dependencies:
    lodash._createset "~4.0.0"
    lodash._root "~3.0.0"

lodash._basevalues@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/lodash._basevalues/download/lodash._basevalues-3.0.0.tgz#5b775762802bde3d3297503e26300820fdf661b7"

lodash._createcompounder@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/lodash._createcompounder/download/lodash._createcompounder-3.0.0.tgz#5dd2cb55372d6e70e0e2392fb2304d6631091075"
  dependencies:
    lodash.deburr "^3.0.0"
    lodash.words "^3.0.0"

lodash._createset@~4.0.0:
  version "4.0.3"
  resolved "http://registry.npm.taobao.org/lodash._createset/download/lodash._createset-4.0.3.tgz#0f4659fbb09d75194fa9e2b88a6644d363c9fe26"

lodash._getnative@^3.0.0:
  version "3.9.1"
  resolved "http://registry.npm.taobao.org/lodash._getnative/download/lodash._getnative-3.9.1.tgz#570bc7dede46d61cdcde687d65d3eecbaa3aaff5"

lodash._isiterateecall@^3.0.0:
  version "3.0.9"
  resolved "http://registry.npm.taobao.org/lodash._isiterateecall/download/lodash._isiterateecall-3.0.9.tgz#5203ad7ba425fae842460e696db9cf3e6aac057c"

lodash._reescape@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/lodash._reescape/download/lodash._reescape-3.0.0.tgz#2b1d6f5dfe07c8a355753e5f27fac7f1cde1616a"

lodash._reevaluate@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/lodash._reevaluate/download/lodash._reevaluate-3.0.0.tgz#58bc74c40664953ae0b124d806996daca431e2ed"

lodash._reinterpolate@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/lodash._reinterpolate/download/lodash._reinterpolate-3.0.0.tgz#0ccf2d89166af03b3663c796538b75ac6e114d9d"

lodash._root@^3.0.0, lodash._root@~3.0.0:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/lodash._root/download/lodash._root-3.0.1.tgz#fba1c4524c19ee9a5f8136b4609f017cf4ded692"

lodash.assign@^4.2.0:
  version "4.2.0"
  resolved "http://registry.npm.taobao.org/lodash.assign/download/lodash.assign-4.2.0.tgz#0d99f3ccd7a6d261d19bdaeb9245005d285808e7"

lodash.camelcase@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/lodash.camelcase/download/lodash.camelcase-3.0.1.tgz#932c8b87f8a4377897c67197533282f97aeac298"
  dependencies:
    lodash._createcompounder "^3.0.0"

lodash.clonedeep@^4.3.2, lodash.clonedeep@~4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.taobao.org/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"

lodash.deburr@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/lodash.deburr/download/lodash.deburr-3.2.0.tgz#6da8f54334a366a7cf4c4c76ef8d80aa1b365ed5"
  dependencies:
    lodash._root "^3.0.0"

lodash.escape@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/lodash.escape/download/lodash.escape-3.2.0.tgz#995ee0dc18c1b48cc92effae71a10aab5b487698"
  dependencies:
    lodash._root "^3.0.0"

lodash.isarguments@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/lodash.isarguments/download/lodash.isarguments-3.1.0.tgz#2f573d85c6a24289ff00663b491c1d338ff3458a"

lodash.isarray@^3.0.0:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/lodash.isarray/download/lodash.isarray-3.0.4.tgz#79e4eb88c36a8122af86f844aa9bcd851b5fbb55"

lodash.isequal@^4.0.0:
  version "4.5.0"
  resolved "http://registry.npm.taobao.org/lodash.isequal/download/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"

lodash.isfinite@^3.3.2:
  version "3.3.2"
  resolved "http://registry.npm.taobao.org/lodash.isfinite/download/lodash.isfinite-3.3.2.tgz#fb89b65a9a80281833f0b7478b3a5104f898ebb3"

lodash.keys@^3.0.0:
  version "3.1.2"
  resolved "http://registry.npm.taobao.org/lodash.keys/download/lodash.keys-3.1.2.tgz#4dbc0472b156be50a0b286855d1bd0b0c656098a"
  dependencies:
    lodash._getnative "^3.0.0"
    lodash.isarguments "^3.0.0"
    lodash.isarray "^3.0.0"

lodash.memoize@^4.1.2:
  version "4.1.2"
  resolved "http://registry.npm.taobao.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz#bcc6c49a42a2840ed997f323eada5ecd182e0bfe"

lodash.restparam@^3.0.0:
  version "3.6.1"
  resolved "http://registry.npm.taobao.org/lodash.restparam/download/lodash.restparam-3.6.1.tgz#936a4e309ef330a7645ed4145986c85ae5b20805"

lodash.template@^3.0.0:
  version "3.6.2"
  resolved "http://registry.npm.taobao.org/lodash.template/download/lodash.template-3.6.2.tgz#f8cdecc6169a255be9098ae8b0c53d378931d14f"
  dependencies:
    lodash._basecopy "^3.0.0"
    lodash._basetostring "^3.0.0"
    lodash._basevalues "^3.0.0"
    lodash._isiterateecall "^3.0.0"
    lodash._reinterpolate "^3.0.0"
    lodash.escape "^3.0.0"
    lodash.keys "^3.0.0"
    lodash.restparam "^3.0.0"
    lodash.templatesettings "^3.0.0"

lodash.templatesettings@^3.0.0:
  version "3.1.1"
  resolved "http://registry.npm.taobao.org/lodash.templatesettings/download/lodash.templatesettings-3.1.1.tgz#fb307844753b66b9f1afa54e262c745307dba8e5"
  dependencies:
    lodash._reinterpolate "^3.0.0"
    lodash.escape "^3.0.0"

lodash.union@~4.6.0:
  version "4.6.0"
  resolved "http://registry.npm.taobao.org/lodash.union/download/lodash.union-4.6.0.tgz#48bb5088409f16f1821666641c44dd1aaae3cd88"

lodash.uniq@^4.5.0, lodash.uniq@~4.5.0:
  version "4.5.0"
  resolved "http://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"

lodash.without@~4.4.0:
  version "4.4.0"
  resolved "http://registry.npm.taobao.org/lodash.without/download/lodash.without-4.4.0.tgz#3cd4574a00b67bae373a94b748772640507b7aac"

lodash.words@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/lodash.words/download/lodash.words-3.2.0.tgz#4e2a8649bc08745b17c695b1a3ce8fee596623b3"
  dependencies:
    lodash._root "^3.0.0"

lodash@^3.10.1, lodash@~3.10.0:
  version "3.10.1"
  resolved "http://registry.npm.taobao.org/lodash/download/lodash-3.10.1.tgz#5bf45e8e49ba4189e17d482789dfd15bd140b7b6"

lodash@^4.0.0, lodash@^4.14.0, lodash@^4.16.1, lodash@^4.2.0, lodash@^4.3.0, lodash@^4.7.0, lodash@~4.17.4:
  version "4.17.4"
  resolved "http://registry.npm.taobao.org/lodash/download/lodash-4.17.4.tgz#78203a4d1c328ae1d86dca6460e369b57f4055ae"

lodash@~0.9.2:
  version "0.9.2"
  resolved "http://registry.npm.taobao.org/lodash/download/lodash-0.9.2.tgz#8f3499c5245d346d682e5b0d3b40767e09f1a92c"

lodash@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/lodash/download/lodash-1.0.2.tgz#8f57560c83b59fc270bd3d561b690043430e2551"

lodash@~2.4.1:
  version "2.4.2"
  resolved "http://registry.npm.taobao.org/lodash/download/lodash-2.4.2.tgz#fadd834b9683073da179b3eae6d9c0d15053f73e"

logalot@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/logalot/download/logalot-2.1.0.tgz#5f8e8c90d304edf12530951a5554abb8c5e3f552"
  dependencies:
    figures "^1.3.5"
    squeak "^1.0.0"

longest@^1.0.0, longest@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/longest/download/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"

loose-envify@^1.0.0:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/loose-envify/download/loose-envify-1.3.1.tgz#d1a8ad33fa9ce0e713d65fdd0ac8b748d478c848"
  dependencies:
    js-tokens "^3.0.0"

loud-rejection@^1.0.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/loud-rejection/download/loud-rejection-1.6.0.tgz#5b46f80147edee578870f086d04821cf998e551f"
  dependencies:
    currently-unhandled "^0.4.1"
    signal-exit "^3.0.0"

lower-case-first@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/lower-case-first/download/lower-case-first-1.0.2.tgz#e5da7c26f29a7073be02d52bac9980e5922adfa1"
  dependencies:
    lower-case "^1.1.2"

lower-case@^1.1.0, lower-case@^1.1.1, lower-case@^1.1.2:
  version "1.1.4"
  resolved "http://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz#9a2cabd1b9e8e0ae993a4bf7d5875c39c42e8eac"

lowercase-keys@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/lowercase-keys/download/lowercase-keys-1.0.0.tgz#4e3366b39e7f5457e35f1324bdf6f88d0bfc7306"

lpad-align@^1.0.1:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/lpad-align/download/lpad-align-1.1.2.tgz#21f600ac1c3095c3c6e497ee67271ee08481fe9e"
  dependencies:
    get-stdin "^4.0.1"
    indent-string "^2.1.0"
    longest "^1.0.0"
    meow "^3.3.0"

lru-cache@2, lru-cache@^2.7.0:
  version "2.7.3"
  resolved "http://registry.npm.taobao.org/lru-cache/download/lru-cache-2.7.3.tgz#6d4524e8b955f95d4f5b58851ce21dd72fb4e952"

lru-cache@^4.0.1:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.1.tgz#622e32e82488b49279114a4f9ecf45e7cd6bba55"
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

macaddress@^0.2.8:
  version "0.2.8"
  resolved "http://registry.npm.taobao.org/macaddress/download/macaddress-0.2.8.tgz#5904dc537c39ec6dbefeae902327135fa8511f12"

magic-string@^0.14.0:
  version "0.14.0"
  resolved "http://registry.npm.taobao.org/magic-string/download/magic-string-0.14.0.tgz#57224aef1701caeed273b17a39a956e72b172462"
  dependencies:
    vlq "^0.2.1"

map-obj@^1.0.0, map-obj@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/map-obj/download/map-obj-1.0.1.tgz#d933ceb9205d82bdcf4886f6742bdc2b4dea146d"

math-expression-evaluator@^1.2.14:
  version "1.2.17"
  resolved "http://registry.npm.taobao.org/math-expression-evaluator/download/math-expression-evaluator-1.2.17.tgz#de819fdbcd84dccd8fae59c6aeb79615b9d266ac"

maxmin@~0.2.0:
  version "0.2.2"
  resolved "http://registry.npm.taobao.org/maxmin/download/maxmin-0.2.2.tgz#a36ced8cc22e3abcd108cfb797a3a4b40275593f"
  dependencies:
    chalk "^0.5.0"
    figures "^1.0.1"
    gzip-size "^0.2.0"
    pretty-bytes "^0.1.0"

media-typer@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"

memory-fs@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/memory-fs/download/memory-fs-0.2.0.tgz#f2bb25368bc121e391c2520de92969caee0a0290"

memory-fs@~0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/memory-fs/download/memory-fs-0.3.0.tgz#7bcc6b629e3a43e871d7e29aca6ae8a7f15cbb20"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

memory-fs@~0.4.1:
  version "0.4.1"
  resolved "http://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz#3a9a20b8462523e447cfbc7e8bb80ed667bfc552"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^3.1.0, meow@^3.3.0, meow@^3.5.0, meow@^3.7.0:
  version "3.7.0"
  resolved "http://registry.npm.taobao.org/meow/download/meow-3.7.0.tgz#72cb668b425228290abbfa856892587308a801fb"
  dependencies:
    camelcase-keys "^2.0.0"
    decamelize "^1.1.2"
    loud-rejection "^1.0.0"
    map-obj "^1.0.1"
    minimist "^1.1.3"
    normalize-package-data "^2.3.4"
    object-assign "^4.0.1"
    read-pkg-up "^1.0.1"
    redent "^1.0.0"
    trim-newlines "^1.0.0"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"

merge-stream@^0.1.7:
  version "0.1.8"
  resolved "http://registry.npm.taobao.org/merge-stream/download/merge-stream-0.1.8.tgz#48a07b3b4a121d74a3edbfdcdb4b08adbf0240b1"
  dependencies:
    through2 "^0.6.1"

merge-stream@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/merge-stream/download/merge-stream-1.0.1.tgz#4041202d508a342ba00174008df0c251b8c135e1"
  dependencies:
    readable-stream "^2.0.1"

methods@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"

micromatch@2.3.11, micromatch@^2.1.5, micromatch@^2.3.7:
  version "2.3.11"
  resolved "http://registry.npm.taobao.org/micromatch/download/micromatch-2.3.11.tgz#86677c97d1720b363431d04d0d15293bd38c1565"
  dependencies:
    arr-diff "^2.0.0"
    array-unique "^0.2.1"
    braces "^1.8.2"
    expand-brackets "^0.1.4"
    extglob "^0.3.1"
    filename-regex "^2.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.1"
    kind-of "^3.0.2"
    normalize-path "^2.0.1"
    object.omit "^2.0.0"
    parse-glob "^3.0.4"
    regex-cache "^0.4.2"

"mime-db@>= 1.27.0 < 2", mime-db@~1.27.0:
  version "1.27.0"
  resolved "http://registry.npm.taobao.org/mime-db/download/mime-db-1.27.0.tgz#820f572296bbd20ec25ed55e5b5de869e5436eb1"

mime-types@^2.1.11, mime-types@^2.1.12, mime-types@~2.1.11, mime-types@~2.1.15, mime-types@~2.1.7:
  version "2.1.15"
  resolved "http://registry.npm.taobao.org/mime-types/download/mime-types-2.1.15.tgz#a4ebf5064094569237b8cf70046776d09fc92aed"
  dependencies:
    mime-db "~1.27.0"

mime@1.2.4:
  version "1.2.4"
  resolved "http://registry.npm.taobao.org/mime/download/mime-1.2.4.tgz#11b5fdaf29c2509255176b80ad520294f5de92b7"

mime@1.3.4:
  version "1.3.4"
  resolved "http://registry.npm.taobao.org/mime/download/mime-1.3.4.tgz#115f9e3b6b3daf2959983cb38f149a2d40eb5d53"

mime@1.3.x, "mime@>= 0.0.1", mime@^1.3.4:
  version "1.3.6"
  resolved "http://registry.npm.taobao.org/mime/download/mime-1.3.6.tgz#591d84d3653a6b0b4a3b9df8de5aa8108e72e5e0"

minimatch@0.3:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/minimatch/download/minimatch-0.3.0.tgz#275d8edaac4f1bb3326472089e7949c8394699dd"
  dependencies:
    lru-cache "2"
    sigmund "~1.0.0"

"minimatch@2 || 3", minimatch@^3.0.0, minimatch@^3.0.2, minimatch@^3.0.4, minimatch@~3.0.2:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^2.0.1:
  version "2.0.10"
  resolved "http://registry.npm.taobao.org/minimatch/download/minimatch-2.0.10.tgz#8d087c39c6b38c001b97fca7ce6d0e1e80afbac7"
  dependencies:
    brace-expansion "^1.0.0"

minimatch@~0.2.11, minimatch@~0.2.12:
  version "0.2.14"
  resolved "http://registry.npm.taobao.org/minimatch/download/minimatch-0.2.14.tgz#c74e780574f63c6f9a090e90efbe6ef53a6a756a"
  dependencies:
    lru-cache "2"
    sigmund "~1.0.0"

minimist@0.0.8, minimist@~0.0.1:
  version "0.0.8"
  resolved "http://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

minimist@^1.1.0, minimist@^1.1.3, minimist@^1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz#a35008b20f41383eec1fb914f4cd5df79a264284"

mkdirp@0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/mkdirp/download/mkdirp-0.3.0.tgz#1bbf5ab1ba827af23575143490426455f481fe1e"

mkdirp@0.5.1, "mkdirp@>=0.5 0", mkdirp@^0.5.0, mkdirp@^0.5.1, mkdirp@~0.5.0, mkdirp@~0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

ms@0.7.1:
  version "0.7.1"
  resolved "http://registry.npm.taobao.org/ms/download/ms-0.7.1.tgz#9cd13c03adbff25b65effde7ce864ee952017098"

ms@0.7.2:
  version "0.7.2"
  resolved "http://registry.npm.taobao.org/ms/download/ms-0.7.2.tgz#ae25cf2512b3885a1d95d7f037868d8431124765"

ms@0.7.3:
  version "0.7.3"
  resolved "http://registry.npm.taobao.org/ms/download/ms-0.7.3.tgz#708155a5e44e33f5fd0fc53e81d0d40a91be1fff"

ms@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/ms/download/ms-1.0.0.tgz#59adcd22edc543f7b5381862d31387b1f4bc9473"

ms@2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

multipipe@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/multipipe/download/multipipe-0.1.2.tgz#2a8f2ddf70eed564dff2d57f1e1a137d9f05078b"
  dependencies:
    duplexer2 "0.0.2"

mute-stream@0.0.5, mute-stream@~0.0.4:
  version "0.0.5"
  resolved "http://registry.npm.taobao.org/mute-stream/download/mute-stream-0.0.5.tgz#8fbfabb0a98a253d3184331f9e8deb7372fac6c0"

nan@^2.3.0, nan@^2.3.2:
  version "2.6.2"
  resolved "http://registry.npm.taobao.org/nan/download/nan-2.6.2.tgz#e4ff34e6c95fdfb5aecc08de6596f43605a7db45"

natives@^1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/natives/download/natives-1.1.0.tgz#e9ff841418a6b2ec7a495e939984f78f163e6e31"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"

ncname@1.0.x:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/ncname/download/ncname-1.0.0.tgz#5b57ad18b1ca092864ef62b0b1ed8194f383b71c"
  dependencies:
    xml-char-classes "^1.0.0"

negotiator@0.6.1:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/negotiator/download/negotiator-0.6.1.tgz#2b327184e8992101177b28563fb5e7102acd0ca9"

no-case@^2.2.0:
  version "2.3.1"
  resolved "http://registry.npm.taobao.org/no-case/download/no-case-2.3.1.tgz#7aeba1c73a52184265554b7dc03baf720df80081"
  dependencies:
    lower-case "^1.1.1"

node-gyp@^3.3.1, node-gyp@~3.4.0:
  version "3.4.0"
  resolved "http://registry.npm.taobao.org/node-gyp/download/node-gyp-3.4.0.tgz#dda558393b3ecbbe24c9e6b8703c71194c63fa36"
  dependencies:
    fstream "^1.0.0"
    glob "^7.0.3"
    graceful-fs "^4.1.2"
    minimatch "^3.0.2"
    mkdirp "^0.5.0"
    nopt "2 || 3"
    npmlog "0 || 1 || 2 || 3"
    osenv "0"
    path-array "^1.0.0"
    request "2"
    rimraf "2"
    semver "2.x || 3.x || 4 || 5"
    tar "^2.0.0"
    which "1"

node-libs-browser@^0.7.0:
  version "0.7.0"
  resolved "http://registry.npm.taobao.org/node-libs-browser/download/node-libs-browser-0.7.0.tgz#3e272c0819e308935e26674408d7af0e1491b83b"
  dependencies:
    assert "^1.1.1"
    browserify-zlib "^0.1.4"
    buffer "^4.9.0"
    console-browserify "^1.1.0"
    constants-browserify "^1.0.0"
    crypto-browserify "3.3.0"
    domain-browser "^1.1.1"
    events "^1.0.0"
    https-browserify "0.0.1"
    os-browserify "^0.2.0"
    path-browserify "0.0.0"
    process "^0.11.0"
    punycode "^1.2.4"
    querystring-es3 "^0.2.0"
    readable-stream "^2.0.5"
    stream-browserify "^2.0.1"
    stream-http "^2.3.1"
    string_decoder "^0.10.25"
    timers-browserify "^2.0.2"
    tty-browserify "0.0.0"
    url "^0.11.0"
    util "^0.10.3"
    vm-browserify "0.0.4"

node-pre-gyp@^0.6.36:
  version "0.6.36"
  resolved "http://registry.npm.taobao.org/node-pre-gyp/download/node-pre-gyp-0.6.36.tgz#db604112cb74e0d477554e9b505b17abddfab786"
  dependencies:
    mkdirp "^0.5.1"
    nopt "^4.0.1"
    npmlog "^4.0.2"
    rc "^1.1.7"
    request "^2.81.0"
    rimraf "^2.6.1"
    semver "^5.3.0"
    tar "^2.2.1"
    tar-pack "^3.4.0"

node-sass@^3.0.0:
  version "3.13.1"
  resolved "http://registry.npm.taobao.org/node-sass/download/node-sass-3.13.1.tgz#7240fbbff2396304b4223527ed3020589c004fc2"
  dependencies:
    async-foreach "^0.1.3"
    chalk "^1.1.1"
    cross-spawn "^3.0.0"
    gaze "^1.0.0"
    get-stdin "^4.0.1"
    glob "^7.0.3"
    in-publish "^2.0.0"
    lodash.assign "^4.2.0"
    lodash.clonedeep "^4.3.2"
    meow "^3.7.0"
    mkdirp "^0.5.1"
    nan "^2.3.2"
    node-gyp "^3.3.1"
    npmlog "^4.0.0"
    request "^2.61.0"
    sass-graph "^2.1.1"

node-status-codes@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/node-status-codes/download/node-status-codes-1.0.0.tgz#5ae5541d024645d32a58fcddc9ceecea7ae3ac2f"

node-uuid@~1.4.7:
  version "1.4.8"
  resolved "http://registry.npm.taobao.org/node-uuid/download/node-uuid-1.4.8.tgz#b040eb0923968afabf8d32fb1f17f1167fdab907"

"nopt@2 || 3", nopt@3.0.x, nopt@~3.0.6:
  version "3.0.6"
  resolved "http://registry.npm.taobao.org/nopt/download/nopt-3.0.6.tgz#c6465dbf08abcd4db359317f79ac68a646b28ff9"
  dependencies:
    abbrev "1"

nopt@^4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/nopt/download/nopt-4.0.1.tgz#d0d4685afd5415193c8c7505602d0d17cd64474d"
  dependencies:
    abbrev "1"
    osenv "^0.1.4"

nopt@~1.0.10:
  version "1.0.10"
  resolved "http://registry.npm.taobao.org/nopt/download/nopt-1.0.10.tgz#6ddd21bd2a31417b92727dd585f8a6f37608ebee"
  dependencies:
    abbrev "1"

nopt@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/nopt/download/nopt-2.0.0.tgz#ca7416f20a5e3f9c3b86180f96295fa3d0b52e0d"
  dependencies:
    abbrev "1"

noptify@~0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.taobao.org/noptify/download/noptify-0.0.3.tgz#58f654a73d9753df0c51d9686dc92104a67f4bbb"
  dependencies:
    nopt "~2.0.0"

normalize-git-url@~3.0.2:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/normalize-git-url/download/normalize-git-url-3.0.2.tgz#8e5f14be0bdaedb73e07200310aa416c27350fc4"

normalize-package-data@^2.0.0, normalize-package-data@^2.3.2, normalize-package-data@^2.3.4, "normalize-package-data@~1.0.1 || ^2.0.0", normalize-package-data@~2.3.5:
  version "2.3.8"
  resolved "http://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.3.8.tgz#d819eda2a9dedbd1ffa563ea4071d936782295bb"
  dependencies:
    hosted-git-info "^2.1.4"
    is-builtin-module "^1.0.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^2.0.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz#1ab28b556e198363a8c1a6f7e6fa20137fe6aed9"
  dependencies:
    remove-trailing-separator "^1.0.1"

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"

normalize-url@^1.4.0:
  version "1.9.1"
  resolved "http://registry.npm.taobao.org/normalize-url/download/normalize-url-1.9.1.tgz#2cc0d66b31ea23036458436e3620d85954c66c3c"
  dependencies:
    object-assign "^4.0.1"
    prepend-http "^1.0.0"
    query-string "^4.1.0"
    sort-keys "^1.0.0"

npm-cache-filename@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/npm-cache-filename/download/npm-cache-filename-1.0.2.tgz#ded306c5b0bfc870a9e9faf823bc5f283e05ae11"

npm-install-checks@~3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/npm-install-checks/download/npm-install-checks-3.0.0.tgz#d4aecdfd51a53e3723b7b2f93b2ee28e307bc0d7"
  dependencies:
    semver "^2.3.0 || 3.x || 4 || 5"

"npm-package-arg@^3.0.0 || ^4.0.0", "npm-package-arg@^4.0.0 || ^5.0.0", npm-package-arg@^4.1.1, npm-package-arg@~4.2.0:
  version "4.2.1"
  resolved "http://registry.npm.taobao.org/npm-package-arg/download/npm-package-arg-4.2.1.tgz#593303fdea85f7c422775f17f9eb7670f680e3ec"
  dependencies:
    hosted-git-info "^2.1.5"
    semver "^5.1.0"

npm-registry-client@~7.2.1:
  version "7.2.1"
  resolved "http://registry.npm.taobao.org/npm-registry-client/download/npm-registry-client-7.2.1.tgz#c792266b088cc313f8525e7e35248626c723db75"
  dependencies:
    concat-stream "^1.5.2"
    graceful-fs "^4.1.6"
    normalize-package-data "~1.0.1 || ^2.0.0"
    npm-package-arg "^3.0.0 || ^4.0.0"
    once "^1.3.3"
    request "^2.74.0"
    retry "^0.10.0"
    semver "2 >=2.2.1 || 3.x || 4 || 5"
    slide "^1.1.3"
  optionalDependencies:
    npmlog "~2.0.0 || ~3.1.0"

npm-user-validate@~0.1.5:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/npm-user-validate/download/npm-user-validate-0.1.5.tgz#52465d50c2d20294a57125b996baedbf56c5004b"

npm@^3.10.5:
  version "3.10.10"
  resolved "http://registry.npm.taobao.org/npm/download/npm-3.10.10.tgz#5b1d577e4c8869d6c8603bc89e9cd1637303e46e"
  dependencies:
    abbrev "~1.0.9"
    ansicolors "~0.3.2"
    ansistyles "~0.1.3"
    aproba "~1.0.4"
    archy "~1.0.0"
    asap "~2.0.5"
    chownr "~1.0.1"
    cmd-shim "~2.0.2"
    columnify "~1.5.4"
    config-chain "~1.1.11"
    dezalgo "~1.0.3"
    editor "~1.0.0"
    fs-vacuum "~1.2.9"
    fs-write-stream-atomic "~1.0.8"
    fstream "~1.0.10"
    fstream-npm "~1.2.0"
    glob "~7.1.0"
    graceful-fs "~4.1.9"
    has-unicode "~2.0.1"
    hosted-git-info "~2.1.5"
    iferr "~0.1.5"
    inflight "~1.0.5"
    inherits "~2.0.3"
    ini "~1.3.4"
    init-package-json "~1.9.4"
    lockfile "~1.0.2"
    lodash._baseuniq "~4.6.0"
    lodash.clonedeep "~4.5.0"
    lodash.union "~4.6.0"
    lodash.uniq "~4.5.0"
    lodash.without "~4.4.0"
    mkdirp "~0.5.1"
    node-gyp "~3.4.0"
    nopt "~3.0.6"
    normalize-git-url "~3.0.2"
    normalize-package-data "~2.3.5"
    npm-cache-filename "~1.0.2"
    npm-install-checks "~3.0.0"
    npm-package-arg "~4.2.0"
    npm-registry-client "~7.2.1"
    npm-user-validate "~0.1.5"
    npmlog "~4.0.0"
    once "~1.4.0"
    opener "~1.4.2"
    osenv "~0.1.3"
    path-is-inside "~1.0.2"
    read "~1.0.7"
    read-cmd-shim "~1.0.1"
    read-installed "~4.0.3"
    read-package-json "~2.0.4"
    read-package-tree "~5.1.5"
    readable-stream "~2.1.5"
    realize-package-specifier "~3.0.3"
    request "~2.75.0"
    retry "~0.10.0"
    rimraf "~2.5.4"
    semver "~5.3.0"
    sha "~2.0.1"
    slide "~1.1.6"
    sorted-object "~2.0.1"
    strip-ansi "~3.0.1"
    tar "~2.2.1"
    text-table "~0.2.0"
    uid-number "0.0.6"
    umask "~1.1.0"
    unique-filename "~1.1.0"
    unpipe "~1.0.0"
    validate-npm-package-name "~2.2.2"
    which "~1.2.11"
    wrappy "~1.0.2"
    write-file-atomic "~1.2.0"

"npmlog@0 || 1 || 2 || 3", "npmlog@~2.0.0 || ~3.1.0":
  version "3.1.2"
  resolved "http://registry.npm.taobao.org/npmlog/download/npmlog-3.1.2.tgz#2d46fa874337af9498a2f12bb43d8d0be4a36873"
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.6.0"
    set-blocking "~2.0.0"

npmlog@^4.0.0, npmlog@^4.0.2, npmlog@~4.0.0:
  version "4.0.2"
  resolved "http://registry.npm.taobao.org/npmlog/download/npmlog-4.0.2.tgz#d03950e0e78ce1527ba26d2a7592e9348ac3e75f"
  dependencies:
    are-we-there-yet "~1.1.2"
    console-control-strings "~1.1.0"
    gauge "~2.7.1"
    set-blocking "~2.0.0"

num2fraction@^1.1.0, num2fraction@^1.2.2:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz#6f682b6a027a4e9ddfa4564cd2589d1d4e669ede"

number-is-nan@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz#097b602b53422a522c1afb8790318336941a011d"

oauth-sign@~0.8.1:
  version "0.8.2"
  resolved "http://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.8.2.tgz#46a6ab7f0aead8deae9ec0565780b7d4efeb9d43"

object-assign@4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.taobao.org/object-assign/download/object-assign-4.1.0.tgz#7a3b3d0e98063d43f4c03f2e8ae6cd51a86883a0"

object-assign@^2.0.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/object-assign/download/object-assign-2.1.1.tgz#43c36e5d569ff8e4816c4efa8be02d26967c18aa"

object-assign@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/object-assign/download/object-assign-3.0.0.tgz#9bedd5ca0897949bca47e7ff408062d549f587f2"

object-assign@^4.0.0, object-assign@^4.0.1, object-assign@^4.1.0:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

object-component@0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.taobao.org/object-component/download/object-component-0.0.3.tgz#f0c69aa50efc95b866c186f400a33769cb2f1291"

object-hash@^1.1.4:
  version "1.1.8"
  resolved "http://registry.npm.taobao.org/object-hash/download/object-hash-1.1.8.tgz#28a659cf987d96a4dabe7860289f3b5326c4a03c"

object-keys@^1.0.10, object-keys@^1.0.8:
  version "1.0.11"
  resolved "http://registry.npm.taobao.org/object-keys/download/object-keys-1.0.11.tgz#c54601778ad560f1142ce0e01bcca8b56d13426d"

object-path@^0.9.0:
  version "0.9.2"
  resolved "http://registry.npm.taobao.org/object-path/download/object-path-0.9.2.tgz#0fd9a74fc5fad1ae3968b586bda5c632bd6c05a5"

object.assign@^4.0.4:
  version "4.0.4"
  resolved "http://registry.npm.taobao.org/object.assign/download/object.assign-4.0.4.tgz#b1c9cc044ef1b9fe63606fc141abbb32e14730cc"
  dependencies:
    define-properties "^1.1.2"
    function-bind "^1.1.0"
    object-keys "^1.0.10"

object.omit@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/object.omit/download/object.omit-2.0.1.tgz#1a9c744829f39dbb858c76ca3579ae2a54ebd1fa"
  dependencies:
    for-own "^0.1.4"
    is-extendable "^0.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/on-headers/download/on-headers-1.0.1.tgz#928f5d0f470d49342651ea6794b0857c100693f7"

once@^1.3.0, once@^1.3.3, once@~1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

once@~1.3.0:
  version "1.3.3"
  resolved "http://registry.npm.taobao.org/once/download/once-1.3.3.tgz#b2e261557ce4c314ec8304f3fa82663e4297ca20"
  dependencies:
    wrappy "1"

onetime@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/onetime/download/onetime-1.1.0.tgz#a1f7838f8314c516f05ecefcbc4ccfe04b4ed789"

opener@~1.4.2:
  version "1.4.3"
  resolved "http://registry.npm.taobao.org/opener/download/opener-1.4.3.tgz#5c6da2c5d7e5831e8ffa3964950f8d6674ac90b8"

openurl@1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/openurl/download/openurl-1.1.0.tgz#e2f2189d999c04823201f083f0f1a7cd8903187a"

opn@4.0.2:
  version "4.0.2"
  resolved "http://registry.npm.taobao.org/opn/download/opn-4.0.2.tgz#7abc22e644dff63b0a96d5ab7f2790c0f01abc95"
  dependencies:
    object-assign "^4.0.1"
    pinkie-promise "^2.0.0"

optimist@~0.6.0:
  version "0.6.1"
  resolved "http://registry.npm.taobao.org/optimist/download/optimist-0.6.1.tgz#da3ea74686fa21a19a111c326e90eb15a0196686"
  dependencies:
    minimist "~0.0.1"
    wordwrap "~0.0.2"

optional@^0.1.0:
  version v0.1.3
  resolved "http://registry.npm.taobao.org/optional/download/optional-0.1.3.tgz#f87537517b59a5e732cfd8f18e4f7eea7ab4761e"

optionator@^0.8.2:
  version "0.8.2"
  resolved "http://registry.npm.taobao.org/optionator/download/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.4"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    wordwrap "~1.0.0"

options@>=0.0.5:
  version "0.0.6"
  resolved "http://registry.npm.taobao.org/options/download/options-0.0.6.tgz#ec22d312806bb53e731773e7cdaefcf1c643128f"

optipng-bin@^3.0.0:
  version "3.1.4"
  resolved "http://registry.npm.taobao.org/optipng-bin/download/optipng-bin-3.1.4.tgz#95d34f2c488704f6fd70606bfea0c659f1d95d84"
  dependencies:
    bin-build "^2.0.0"
    bin-wrapper "^3.0.0"
    logalot "^2.0.0"

ordered-read-streams@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/ordered-read-streams/download/ordered-read-streams-0.1.0.tgz#fd565a9af8eb4473ba69b6ed8a34352cb552f126"

ordered-read-streams@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/ordered-read-streams/download/ordered-read-streams-0.3.0.tgz#7137e69b3298bb342247a1bbee3881c80e2fd78b"
  dependencies:
    is-stream "^1.0.1"
    readable-stream "^2.0.1"

original@>=0.0.5:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/original/download/original-1.0.0.tgz#9147f93fa1696d04be61e01bd50baeaca656bd3b"
  dependencies:
    url-parse "1.0.x"

os-browserify@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/os-browserify/download/os-browserify-0.2.1.tgz#63fc4ccee5d2d7763d26bbf8601078e6c2e0044f"

os-filter-obj@^1.0.0:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/os-filter-obj/download/os-filter-obj-1.0.3.tgz#5915330d90eced557d2d938a31c6dd214d9c63ad"

os-homedir@^1.0.0, os-homedir@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/os-homedir/download/os-homedir-1.0.2.tgz#ffbc4988336e0e833de0c168c7ef152121aa7fb3"

os-locale@^1.4.0:
  version "1.4.0"
  resolved "http://registry.npm.taobao.org/os-locale/download/os-locale-1.4.0.tgz#20f9f17ae29ed345e8bde583b13d2009803c14d9"
  dependencies:
    lcid "^1.0.0"

os-tmpdir@^1.0.0, os-tmpdir@^1.0.1, os-tmpdir@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

osenv@0, osenv@^0.1.4, osenv@~0.1.3:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/osenv/download/osenv-0.1.4.tgz#42fe6d5953df06c8064be6f176c3d05aaaa34644"
  dependencies:
    os-homedir "^1.0.0"
    os-tmpdir "^1.0.0"

pako@~0.2.0:
  version "0.2.9"
  resolved "http://registry.npm.taobao.org/pako/download/pako-0.2.9.tgz#f3f7522f4ef782348da8161bad9ecfd51bf83a75"

param-case@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/param-case/download/param-case-2.1.1.tgz#df94fd8cf6531ecf75e6bef9a0858fbc72be2247"
  dependencies:
    no-case "^2.2.0"

parse-glob@^3.0.4:
  version "3.0.4"
  resolved "http://registry.npm.taobao.org/parse-glob/download/parse-glob-3.0.4.tgz#b2c376cfb11f35513badd173ef0bb6e3a388391c"
  dependencies:
    glob-base "^0.3.0"
    is-dotfile "^1.0.0"
    is-extglob "^1.0.0"
    is-glob "^2.0.0"

parse-json@^2.1.0, parse-json@^2.2.0:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/parse-json/download/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

parse5@^2.1.0:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/parse5/download/parse5-2.2.3.tgz#0c4fc41c1000c5e6b93d48b03f8083837834e9f6"

parsejson@0.0.3:
  version "0.0.3"
  resolved "http://registry.npm.taobao.org/parsejson/download/parsejson-0.0.3.tgz#ab7e3759f209ece99437973f7d0f1f64ae0e64ab"
  dependencies:
    better-assert "~1.0.0"

parseqs@0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.taobao.org/parseqs/download/parseqs-0.0.5.tgz#d5208a3738e46766e291ba2ea173684921a8b89d"
  dependencies:
    better-assert "~1.0.0"

parseuri@0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.taobao.org/parseuri/download/parseuri-0.0.5.tgz#80204a50d4dbb779bfdc6ebe2778d90e4bce320a"
  dependencies:
    better-assert "~1.0.0"

parseurl@~1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/parseurl/download/parseurl-1.3.1.tgz#c8ab8c9223ba34888aa64a297b28853bec18da56"

pascal-case@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/pascal-case/download/pascal-case-2.0.1.tgz#2d578d3455f660da65eca18ef95b4e0de912761e"
  dependencies:
    camel-case "^3.0.0"
    upper-case-first "^1.1.0"

path-array@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/path-array/download/path-array-1.0.1.tgz#7e2f0f35f07a2015122b868b7eac0eb2c4fec271"
  dependencies:
    array-index "^1.0.0"

path-browserify@0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.0.tgz#a0b870729aae214005b7d5032ec2cbbb0fb4451a"

path-case@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/path-case/download/path-case-2.1.1.tgz#94b8037c372d3fe2906e465bb45e25d226e8eea5"
  dependencies:
    no-case "^2.2.0"

path-dirname@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz#cc33d24d525e099a5388c0336c6e32b9160609e0"

path-exists@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/path-exists/download/path-exists-1.0.0.tgz#d5a8998eb71ef37a74c34eb0d9eba6e878eea081"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/path-exists/download/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-is-inside@^1.0.1, path-is-inside@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"

path-parse@^1.0.5:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/path-parse/download/path-parse-1.0.5.tgz#3c1adf871ea9cd6c9431b6ea2bd74a0ff055c4c1"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"

path-type@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/path-type/download/path-type-1.1.0.tgz#59c44f7ee491da704da415da5a4070ba4f8fe441"
  dependencies:
    graceful-fs "^4.1.2"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

pbkdf2-compat@2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/pbkdf2-compat/download/pbkdf2-compat-2.0.1.tgz#b6e0c8fa99494d94e0511575802a59a5c142f288"

pend@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/pend/download/pend-1.2.0.tgz#7a57eb550a6783f9115331fcf4663d5c8e007a50"

performance-now@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/performance-now/download/performance-now-0.2.0.tgz#33ef30c5c77d4ea21c5a53869d91b56d8f2555e5"

pify@^2.0.0:
  version "2.3.0"
  resolved "http://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/pkg-dir/download/pkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  dependencies:
    find-up "^1.0.0"

pluralize@^1.2.1:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/pluralize/download/pluralize-1.2.1.tgz#d1a21483fd22bb41e58a12fa3421823140897c45"

portscanner@2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/portscanner/download/portscanner-2.1.1.tgz#eabb409e4de24950f5a2a516d35ae769343fbb96"
  dependencies:
    async "1.5.2"
    is-number-like "^1.0.3"

postcss-calc@^5.2.0:
  version "5.3.1"
  resolved "http://registry.npm.taobao.org/postcss-calc/download/postcss-calc-5.3.1.tgz#77bae7ca928ad85716e2fda42f261bf7c1d65b5e"
  dependencies:
    postcss "^5.0.2"
    postcss-message-helpers "^2.0.0"
    reduce-css-calc "^1.2.6"

postcss-colormin@^2.1.8:
  version "2.2.2"
  resolved "http://registry.npm.taobao.org/postcss-colormin/download/postcss-colormin-2.2.2.tgz#6631417d5f0e909a3d7ec26b24c8a8d1e4f96e4b"
  dependencies:
    colormin "^1.0.5"
    postcss "^5.0.13"
    postcss-value-parser "^3.2.3"

postcss-convert-values@^2.3.4:
  version "2.6.1"
  resolved "http://registry.npm.taobao.org/postcss-convert-values/download/postcss-convert-values-2.6.1.tgz#bbd8593c5c1fd2e3d1c322bb925dcae8dae4d62d"
  dependencies:
    postcss "^5.0.11"
    postcss-value-parser "^3.1.2"

postcss-discard-comments@^2.0.4:
  version "2.0.4"
  resolved "http://registry.npm.taobao.org/postcss-discard-comments/download/postcss-discard-comments-2.0.4.tgz#befe89fafd5b3dace5ccce51b76b81514be00e3d"
  dependencies:
    postcss "^5.0.14"

postcss-discard-duplicates@^2.0.1:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/postcss-discard-duplicates/download/postcss-discard-duplicates-2.1.0.tgz#b9abf27b88ac188158a5eb12abcae20263b91932"
  dependencies:
    postcss "^5.0.4"

postcss-discard-empty@^2.0.1:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/postcss-discard-empty/download/postcss-discard-empty-2.1.0.tgz#d2b4bd9d5ced5ebd8dcade7640c7d7cd7f4f92b5"
  dependencies:
    postcss "^5.0.14"

postcss-discard-overridden@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/postcss-discard-overridden/download/postcss-discard-overridden-0.1.1.tgz#8b1eaf554f686fb288cd874c55667b0aa3668d58"
  dependencies:
    postcss "^5.0.16"

postcss-discard-unused@^2.2.1:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/postcss-discard-unused/download/postcss-discard-unused-2.2.3.tgz#bce30b2cc591ffc634322b5fb3464b6d934f4433"
  dependencies:
    postcss "^5.0.14"
    uniqs "^2.0.0"

postcss-filter-plugins@^2.0.0:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/postcss-filter-plugins/download/postcss-filter-plugins-2.0.2.tgz#6d85862534d735ac420e4a85806e1f5d4286d84c"
  dependencies:
    postcss "^5.0.4"
    uniqid "^4.0.0"

postcss-merge-idents@^2.1.5:
  version "2.1.7"
  resolved "http://registry.npm.taobao.org/postcss-merge-idents/download/postcss-merge-idents-2.1.7.tgz#4c5530313c08e1d5b3bbf3d2bbc747e278eea270"
  dependencies:
    has "^1.0.1"
    postcss "^5.0.10"
    postcss-value-parser "^3.1.1"

postcss-merge-longhand@^2.0.1:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/postcss-merge-longhand/download/postcss-merge-longhand-2.0.2.tgz#23d90cd127b0a77994915332739034a1a4f3d658"
  dependencies:
    postcss "^5.0.4"

postcss-merge-rules@^2.0.3:
  version "2.1.2"
  resolved "http://registry.npm.taobao.org/postcss-merge-rules/download/postcss-merge-rules-2.1.2.tgz#d1df5dfaa7b1acc3be553f0e9e10e87c61b5f721"
  dependencies:
    browserslist "^1.5.2"
    caniuse-api "^1.5.2"
    postcss "^5.0.4"
    postcss-selector-parser "^2.2.2"
    vendors "^1.0.0"

postcss-message-helpers@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/postcss-message-helpers/download/postcss-message-helpers-2.0.0.tgz#a4f2f4fab6e4fe002f0aed000478cdf52f9ba60e"

postcss-minify-font-values@^1.0.2:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/postcss-minify-font-values/download/postcss-minify-font-values-1.0.5.tgz#4b58edb56641eba7c8474ab3526cafd7bbdecb69"
  dependencies:
    object-assign "^4.0.1"
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-minify-gradients@^1.0.1:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/postcss-minify-gradients/download/postcss-minify-gradients-1.0.5.tgz#5dbda11373703f83cfb4a3ea3881d8d75ff5e6e1"
  dependencies:
    postcss "^5.0.12"
    postcss-value-parser "^3.3.0"

postcss-minify-params@^1.0.4:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/postcss-minify-params/download/postcss-minify-params-1.2.2.tgz#ad2ce071373b943b3d930a3fa59a358c28d6f1f3"
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.2"
    postcss-value-parser "^3.0.2"
    uniqs "^2.0.0"

postcss-minify-selectors@^2.0.4:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/postcss-minify-selectors/download/postcss-minify-selectors-2.1.1.tgz#b2c6a98c0072cf91b932d1a496508114311735bf"
  dependencies:
    alphanum-sort "^1.0.2"
    has "^1.0.1"
    postcss "^5.0.14"
    postcss-selector-parser "^2.0.0"

postcss-modules-extract-imports@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.0.tgz#66140ecece38ef06bf0d3e355d69bf59d141ea85"
  dependencies:
    postcss "^6.0.1"

postcss-modules-local-by-default@^1.0.1:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz#f7d80c398c5a393fa7964466bd19500a7d61c069"
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-scope@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz#d6ea64994c79f97b62a72b426fbe6056a194bb90"
  dependencies:
    css-selector-tokenizer "^0.7.0"
    postcss "^6.0.1"

postcss-modules-values@^1.1.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz#ecffa9d7e192518389f42ad0e83f72aec456ea20"
  dependencies:
    icss-replace-symbols "^1.1.0"
    postcss "^6.0.1"

postcss-normalize-charset@^1.1.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/postcss-normalize-charset/download/postcss-normalize-charset-1.1.1.tgz#ef9ee71212d7fe759c78ed162f61ed62b5cb93f1"
  dependencies:
    postcss "^5.0.5"

postcss-normalize-url@^3.0.7:
  version "3.0.8"
  resolved "http://registry.npm.taobao.org/postcss-normalize-url/download/postcss-normalize-url-3.0.8.tgz#108f74b3f2fcdaf891a2ffa3ea4592279fc78222"
  dependencies:
    is-absolute-url "^2.0.0"
    normalize-url "^1.4.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"

postcss-ordered-values@^2.1.0:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/postcss-ordered-values/download/postcss-ordered-values-2.2.3.tgz#eec6c2a67b6c412a8db2042e77fe8da43f95c11d"
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.1"

postcss-reduce-idents@^2.2.2:
  version "2.4.0"
  resolved "http://registry.npm.taobao.org/postcss-reduce-idents/download/postcss-reduce-idents-2.4.0.tgz#c2c6d20cc958284f6abfbe63f7609bf409059ad3"
  dependencies:
    postcss "^5.0.4"
    postcss-value-parser "^3.0.2"

postcss-reduce-initial@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/postcss-reduce-initial/download/postcss-reduce-initial-1.0.1.tgz#68f80695f045d08263a879ad240df8dd64f644ea"
  dependencies:
    postcss "^5.0.4"

postcss-reduce-transforms@^1.0.3:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/postcss-reduce-transforms/download/postcss-reduce-transforms-1.0.4.tgz#ff76f4d8212437b31c298a42d2e1444025771ae1"
  dependencies:
    has "^1.0.1"
    postcss "^5.0.8"
    postcss-value-parser "^3.0.1"

postcss-selector-parser@^1.1.2:
  version "1.3.3"
  resolved "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-1.3.3.tgz#d2ee19df7a64f8ef21c1a71c86f7d4835c88c281"
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-selector-parser@^2.0.0, postcss-selector-parser@^2.2.2:
  version "2.2.3"
  resolved "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-2.2.3.tgz#f9437788606c3c9acee16ffe8d8b16297f27bb90"
  dependencies:
    flatten "^1.0.2"
    indexes-of "^1.0.1"
    uniq "^1.0.1"

postcss-svgo@^2.1.1:
  version "2.1.6"
  resolved "http://registry.npm.taobao.org/postcss-svgo/download/postcss-svgo-2.1.6.tgz#b6df18aa613b666e133f08adb5219c2684ac108d"
  dependencies:
    is-svg "^2.0.0"
    postcss "^5.0.14"
    postcss-value-parser "^3.2.3"
    svgo "^0.7.0"

postcss-unique-selectors@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/postcss-unique-selectors/download/postcss-unique-selectors-2.0.2.tgz#981d57d29ddcb33e7b1dfe1fd43b8649f933ca1d"
  dependencies:
    alphanum-sort "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss-value-parser@^3.0.1, postcss-value-parser@^3.0.2, postcss-value-parser@^3.1.1, postcss-value-parser@^3.1.2, postcss-value-parser@^3.2.3, postcss-value-parser@^3.3.0:
  version "3.3.0"
  resolved "http://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-3.3.0.tgz#87f38f9f18f774a4ab4c8a232f5c5ce8872a9d15"

postcss-zindex@^2.0.1:
  version "2.2.0"
  resolved "http://registry.npm.taobao.org/postcss-zindex/download/postcss-zindex-2.2.0.tgz#d2109ddc055b91af67fc4cb3b025946639d2af22"
  dependencies:
    has "^1.0.1"
    postcss "^5.0.4"
    uniqs "^2.0.0"

postcss@^4.1.11, postcss@~4.1.12:
  version "4.1.16"
  resolved "http://registry.npm.taobao.org/postcss/download/postcss-4.1.16.tgz#4c449b4c8af9df3caf6d37f8e1e575d0361758dc"
  dependencies:
    es6-promise "~2.3.0"
    js-base64 "~2.1.8"
    source-map "~0.4.2"

postcss@^5.0.10, postcss@^5.0.11, postcss@^5.0.12, postcss@^5.0.13, postcss@^5.0.14, postcss@^5.0.16, postcss@^5.0.2, postcss@^5.0.4, postcss@^5.0.5, postcss@^5.0.6, postcss@^5.0.8, postcss@^5.2.16:
  version "5.2.17"
  resolved "http://registry.npm.taobao.org/postcss/download/postcss-5.2.17.tgz#cf4f597b864d65c8a492b2eabe9d706c879c388b"
  dependencies:
    chalk "^1.1.3"
    js-base64 "^2.1.9"
    source-map "^0.5.6"
    supports-color "^3.2.3"

postcss@^6.0.1:
  version "6.0.2"
  resolved "http://registry.npm.taobao.org/postcss/download/postcss-6.0.2.tgz#5c4fea589f0ac3b00caa75b1cbc3a284195b7e5d"
  dependencies:
    chalk "^1.1.3"
    source-map "^0.5.6"
    supports-color "^3.2.3"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/prelude-ls/download/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"

prepend-http@^1.0.0, prepend-http@^1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/prepend-http/download/prepend-http-1.0.4.tgz#d4f4562b0ce3696e41ac52d0e002e57a635dc6dc"

preserve@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/preserve/download/preserve-0.2.0.tgz#815ed1f6ebc65926f865b310c0713bcb3315ce4b"

pretty-bytes@^0.1.0:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/pretty-bytes/download/pretty-bytes-0.1.2.tgz#cd90294d58a1ca4e8a5d0fb9c8225998881acf00"

pretty-bytes@^1.0.1:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/pretty-bytes/download/pretty-bytes-1.0.4.tgz#0a22e8210609ad35542f8c8d5d2159aff0751c84"
  dependencies:
    get-stdin "^4.0.1"
    meow "^3.1.0"

private@^0.1.6, private@~0.1.5:
  version "0.1.7"
  resolved "http://registry.npm.taobao.org/private/download/private-0.1.7.tgz#68ce5e8a1ef0a23bb570cc28537b5332aba63ef1"

process-nextick-args@~1.0.6:
  version "1.0.7"
  resolved "http://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-1.0.7.tgz#150e20b756590ad3f91093f25a4f2ad8bff30ba3"

process@^0.11.0:
  version "0.11.10"
  resolved "http://registry.npm.taobao.org/process/download/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"

progress@^1.1.8:
  version "1.1.8"
  resolved "http://registry.npm.taobao.org/progress/download/progress-1.1.8.tgz#e260c78f6161cdd9b0e56cc3e0a85de17c7a57be"

promzard@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/promzard/download/promzard-0.3.0.tgz#26a5d6ee8c7dee4cb12208305acfb93ba382a9ee"
  dependencies:
    read "1"

proto-list@~1.2.1:
  version "1.2.4"
  resolved "http://registry.npm.taobao.org/proto-list/download/proto-list-1.2.4.tgz#212d5bfe1318306a420f6402b8e26ff39647a849"

proxy-addr@~1.1.4:
  version "1.1.4"
  resolved "http://registry.npm.taobao.org/proxy-addr/download/proxy-addr-1.1.4.tgz#27e545f6960a44a627d9b44467e35c1b6b4ce2f3"
  dependencies:
    forwarded "~0.1.0"
    ipaddr.js "1.3.0"

prr@~0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.taobao.org/prr/download/prr-0.0.0.tgz#1a84b85908325501411853d0081ee3fa86e2926a"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"

punycode@1.3.2:
  version "1.3.2"
  resolved "http://registry.npm.taobao.org/punycode/download/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"

punycode@^1.2.4, punycode@^1.4.1:
  version "1.4.1"
  resolved "http://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz#c0d5a63b2718800ad8e1eb0fa5269c84dd41845e"

q@^1.1.2:
  version "1.5.0"
  resolved "http://registry.npm.taobao.org/q/download/q-1.5.0.tgz#dd01bac9d06d30e6f219aecb8253ee9ebdc308f1"

qs@0.4.x:
  version "0.4.2"
  resolved "http://registry.npm.taobao.org/qs/download/qs-0.4.2.tgz#3cac4c861e371a8c9c4770ac23cda8de639b8e5f"

qs@6.2.1, qs@~6.2.0:
  version "6.2.1"
  resolved "http://registry.npm.taobao.org/qs/download/qs-6.2.1.tgz#ce03c5ff0935bc1d9d69a9f14cbd18e568d67625"

qs@6.4.0, qs@~6.4.0:
  version "6.4.0"
  resolved "http://registry.npm.taobao.org/qs/download/qs-6.4.0.tgz#13e26d28ad6b0ffaa91312cd3bf708ed351e7233"

"qs@>= 0.4.0", qs@~6.3.0:
  version "6.3.2"
  resolved "http://registry.npm.taobao.org/qs/download/qs-6.3.2.tgz#e75bd5f6e268122a2a0e0bda630b2550c166502c"

qs@~0.5.2:
  version "0.5.6"
  resolved "http://registry.npm.taobao.org/qs/download/qs-0.5.6.tgz#31b1ad058567651c526921506b9a8793911a0384"

query-string@^4.1.0:
  version "4.3.4"
  resolved "http://registry.npm.taobao.org/query-string/download/query-string-4.3.4.tgz#bbb693b9ca915c232515b228b1a02b609043dbeb"
  dependencies:
    object-assign "^4.1.0"
    strict-uri-encode "^1.0.0"

querystring-es3@^0.2.0:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz#9ec61f79049875707d69414596fd907a4d711e73"

querystring@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/querystring/download/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"

querystringify@0.0.x:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/querystringify/download/querystringify-0.0.4.tgz#0cf7f84f9463ff0ae51c4c4b142d95be37724d9c"

querystringify@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/querystringify/download/querystringify-1.0.0.tgz#6286242112c5b712fa654e526652bf6a13ff05cb"

randomatic@^1.1.3:
  version "1.1.7"
  resolved "http://registry.npm.taobao.org/randomatic/download/randomatic-1.1.7.tgz#c7abe9cc8b87c0baa876b19fde83fd464797e38c"
  dependencies:
    is-number "^3.0.0"
    kind-of "^4.0.0"

range-parser@^1.0.3, range-parser@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/range-parser/download/range-parser-1.2.0.tgz#f49be6b487894ddc40dcc94a322f611092e00d5e"

rc@^1.1.2, rc@^1.1.7:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/rc/download/rc-1.2.1.tgz#2e03e8e42ee450b8cb3dce65be1bf8974e1dfd95"
  dependencies:
    deep-extend "~0.4.0"
    ini "~1.3.0"
    minimist "^1.2.0"
    strip-json-comments "~2.0.1"

read-all-stream@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/read-all-stream/download/read-all-stream-3.1.0.tgz#35c3e177f2078ef789ee4bfafa4373074eaef4fa"
  dependencies:
    pinkie-promise "^2.0.0"
    readable-stream "^2.0.0"

read-cmd-shim@~1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/read-cmd-shim/download/read-cmd-shim-1.0.1.tgz#2d5d157786a37c055d22077c32c53f8329e91c7b"
  dependencies:
    graceful-fs "^4.1.2"

read-installed@~4.0.3:
  version "4.0.3"
  resolved "http://registry.npm.taobao.org/read-installed/download/read-installed-4.0.3.tgz#ff9b8b67f187d1e4c29b9feb31f6b223acd19067"
  dependencies:
    debuglog "^1.0.1"
    read-package-json "^2.0.0"
    readdir-scoped-modules "^1.0.0"
    semver "2 || 3 || 4 || 5"
    slide "~1.1.3"
    util-extend "^1.0.1"
  optionalDependencies:
    graceful-fs "^4.1.2"

"read-package-json@1 || 2", read-package-json@^2.0.0, read-package-json@~2.0.4:
  version "2.0.5"
  resolved "http://registry.npm.taobao.org/read-package-json/download/read-package-json-2.0.5.tgz#f93a64e641529df68a08c64de46389e8a3f88845"
  dependencies:
    glob "^7.1.1"
    json-parse-helpfulerror "^1.0.2"
    normalize-package-data "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.2"

read-package-tree@~5.1.5:
  version "5.1.6"
  resolved "http://registry.npm.taobao.org/read-package-tree/download/read-package-tree-5.1.6.tgz#4f03e83d0486856fb60d97c94882841c2a7b1b7a"
  dependencies:
    debuglog "^1.0.1"
    dezalgo "^1.0.0"
    once "^1.3.0"
    read-package-json "^2.0.0"
    readdir-scoped-modules "^1.0.0"

read-pkg-up@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/read-pkg-up/download/read-pkg-up-1.0.1.tgz#9d63c13276c065918d57f002a57f40a1b643fb02"
  dependencies:
    find-up "^1.0.0"
    read-pkg "^1.0.0"

read-pkg@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/read-pkg/download/read-pkg-1.1.0.tgz#f5ffaa5ecd29cb31c0474bca7d756b6bb29e3f28"
  dependencies:
    load-json-file "^1.0.0"
    normalize-package-data "^2.3.2"
    path-type "^1.0.0"

read@1, read@~1.0.1, read@~1.0.7:
  version "1.0.7"
  resolved "http://registry.npm.taobao.org/read/download/read-1.0.7.tgz#b3da19bd052431a97671d44a42634adf710b40c4"
  dependencies:
    mute-stream "~0.0.4"

"readable-stream@1 || 2", readable-stream@^2.0.0, readable-stream@^2.0.1, readable-stream@^2.0.2, readable-stream@^2.0.4, readable-stream@^2.0.5, readable-stream@^2.0.6, readable-stream@^2.1.4, readable-stream@^2.1.5, readable-stream@^2.2.2, readable-stream@^2.2.6:
  version "2.2.11"
  resolved "http://registry.npm.taobao.org/readable-stream/download/readable-stream-2.2.11.tgz#0796b31f8d7688007ff0b93a8088d34aa17c0f72"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    safe-buffer "~5.0.1"
    string_decoder "~1.0.0"
    util-deprecate "~1.0.1"

"readable-stream@>=1.0.33-1 <1.1.0-0":
  version "1.0.34"
  resolved "http://registry.npm.taobao.org/readable-stream/download/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@~1.1.9:
  version "1.1.14"
  resolved "http://registry.npm.taobao.org/readable-stream/download/readable-stream-1.1.14.tgz#7cf4c54ef648e3813084c636dd2079e166c081d9"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readable-stream@~2.0.5:
  version "2.0.6"
  resolved "http://registry.npm.taobao.org/readable-stream/download/readable-stream-2.0.6.tgz#8f90341e68a53ccc928788dacfcd11b36eb9b78e"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    string_decoder "~0.10.x"
    util-deprecate "~1.0.1"

readable-stream@~2.1.5:
  version "2.1.5"
  resolved "http://registry.npm.taobao.org/readable-stream/download/readable-stream-2.1.5.tgz#66fa8b720e1438b364681f2ad1a63c618448c9d0"
  dependencies:
    buffer-shims "^1.0.0"
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    string_decoder "~0.10.x"
    util-deprecate "~1.0.1"

readdir-scoped-modules@^1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/readdir-scoped-modules/download/readdir-scoped-modules-1.0.2.tgz#9fafa37d286be5d92cbaebdee030dc9b5f406747"
  dependencies:
    debuglog "^1.0.1"
    dezalgo "^1.0.0"
    graceful-fs "^4.1.2"
    once "^1.3.0"

readdirp@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/readdirp/download/readdirp-2.1.0.tgz#4ed0ad060df3073300c48440373f72d1cc642d78"
  dependencies:
    graceful-fs "^4.1.2"
    minimatch "^3.0.2"
    readable-stream "^2.0.2"
    set-immediate-shim "^1.0.1"

readline2@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/readline2/download/readline2-1.0.1.tgz#41059608ffc154757b715d9989d199ffbf372e35"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    mute-stream "0.0.5"

realize-package-specifier@~3.0.3:
  version "3.0.3"
  resolved "http://registry.npm.taobao.org/realize-package-specifier/download/realize-package-specifier-3.0.3.tgz#d0def882952b8de3f67eba5e91199661271f41f4"
  dependencies:
    dezalgo "^1.0.1"
    npm-package-arg "^4.1.1"

recast@~0.11.12:
  version "0.11.23"
  resolved "http://registry.npm.taobao.org/recast/download/recast-0.11.23.tgz#451fd3004ab1e4df9b4e4b66376b2a21912462d3"
  dependencies:
    ast-types "0.9.6"
    esprima "~3.1.0"
    private "~0.1.5"
    source-map "~0.5.0"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://registry.npm.taobao.org/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  dependencies:
    resolve "^1.1.6"

redent@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/redent/download/redent-1.0.0.tgz#cf916ab1fd5f1f16dfb20822dd6ec7f730c2afde"
  dependencies:
    indent-string "^2.1.0"
    strip-indent "^1.0.1"

reduce-css-calc@^1.2.6:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/reduce-css-calc/download/reduce-css-calc-1.3.0.tgz#747c914e049614a4c9cfbba629871ad1d2927716"
  dependencies:
    balanced-match "^0.4.2"
    math-expression-evaluator "^1.2.14"
    reduce-function-call "^1.0.1"

reduce-function-call@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/reduce-function-call/download/reduce-function-call-1.0.2.tgz#5a200bf92e0e37751752fe45b0ab330fd4b6be99"
  dependencies:
    balanced-match "^0.4.2"

regenerate@^1.2.1:
  version "1.3.2"
  resolved "http://registry.npm.taobao.org/regenerate/download/regenerate-1.3.2.tgz#d1941c67bad437e1be76433add5b385f95b19260"

regenerator-runtime@^0.10.0:
  version "0.10.5"
  resolved "http://registry.npm.taobao.org/regenerator-runtime/download/regenerator-runtime-0.10.5.tgz#336c3efc1220adcedda2c9fab67b5a7955a33658"

regenerator-transform@0.9.11:
  version "0.9.11"
  resolved "http://registry.npm.taobao.org/regenerator-transform/download/regenerator-transform-0.9.11.tgz#3a7d067520cb7b7176769eb5ff868691befe1283"
  dependencies:
    babel-runtime "^6.18.0"
    babel-types "^6.19.0"
    private "^0.1.6"

regex-cache@^0.4.2:
  version "0.4.3"
  resolved "http://registry.npm.taobao.org/regex-cache/download/regex-cache-0.4.3.tgz#9b1a6c35d4d0dfcef5711ae651e8e9d3d7114145"
  dependencies:
    is-equal-shallow "^0.1.3"
    is-primitive "^2.0.0"

regexpu-core@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-1.0.0.tgz#86a763f58ee4d7c2f6b102e4764050de7ed90c6b"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regexpu-core@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-2.0.0.tgz#49d038837b8dcf8bfa5b9a42139938e6ea2ae240"
  dependencies:
    regenerate "^1.2.1"
    regjsgen "^0.2.0"
    regjsparser "^0.1.4"

regjsgen@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/regjsgen/download/regjsgen-0.2.0.tgz#6c016adeac554f75823fe37ac05b92d5a4edb1f7"

regjsparser@^0.1.4:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/regjsparser/download/regjsparser-0.1.5.tgz#7ee8f84dc6fa792d3fd0ae228d24bd949ead205c"
  dependencies:
    jsesc "~0.5.0"

relateurl@0.2.x:
  version "0.2.7"
  resolved "http://registry.npm.taobao.org/relateurl/download/relateurl-0.2.7.tgz#54dbf377e51440aca90a4cd274600d3ff2d888a9"

remove-trailing-separator@^1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.0.2.tgz#69b062d978727ad14dc6b56ba4ab772fd8d70511"

repeat-element@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.2.tgz#ef089a178d1483baae4d93eb98b4f9e4e11d990a"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "http://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"

repeating@^2.0.0:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/repeating/download/repeating-2.0.1.tgz#5214c53a926d3552707527fbab415dbc08d06dda"
  dependencies:
    is-finite "^1.0.0"

replace-ext@0.0.1:
  version "0.0.1"
  resolved "http://registry.npm.taobao.org/replace-ext/download/replace-ext-0.0.1.tgz#29bbd92078a739f0bcce2b4ee41e837953522924"

request@2, request@^2.61.0, request@^2.74.0, request@~2.75.0:
  version "2.75.0"
  resolved "http://registry.npm.taobao.org/request/download/request-2.75.0.tgz#d2b8268a286da13eaa5d01adf5d18cc90f657d93"
  dependencies:
    aws-sign2 "~0.6.0"
    aws4 "^1.2.1"
    bl "~1.1.2"
    caseless "~0.11.0"
    combined-stream "~1.0.5"
    extend "~3.0.0"
    forever-agent "~0.6.1"
    form-data "~2.0.0"
    har-validator "~2.0.6"
    hawk "~3.1.3"
    http-signature "~1.1.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.7"
    node-uuid "~1.4.7"
    oauth-sign "~0.8.1"
    qs "~6.2.0"
    stringstream "~0.0.4"
    tough-cookie "~2.3.0"
    tunnel-agent "~0.4.1"

request@2.78.0:
  version "2.78.0"
  resolved "http://registry.npm.taobao.org/request/download/request-2.78.0.tgz#e1c8dec346e1c81923b24acdb337f11decabe9cc"
  dependencies:
    aws-sign2 "~0.6.0"
    aws4 "^1.2.1"
    caseless "~0.11.0"
    combined-stream "~1.0.5"
    extend "~3.0.0"
    forever-agent "~0.6.1"
    form-data "~2.1.1"
    har-validator "~2.0.6"
    hawk "~3.1.3"
    http-signature "~1.1.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.7"
    node-uuid "~1.4.7"
    oauth-sign "~0.8.1"
    qs "~6.3.0"
    stringstream "~0.0.4"
    tough-cookie "~2.3.0"
    tunnel-agent "~0.4.1"

request@^2.81.0:
  version "2.81.0"
  resolved "http://registry.npm.taobao.org/request/download/request-2.81.0.tgz#c6928946a0e06c5f8d6f8a9333469ffda46298a0"
  dependencies:
    aws-sign2 "~0.6.0"
    aws4 "^1.2.1"
    caseless "~0.12.0"
    combined-stream "~1.0.5"
    extend "~3.0.0"
    forever-agent "~0.6.1"
    form-data "~2.1.1"
    har-validator "~4.2.1"
    hawk "~3.1.3"
    http-signature "~1.1.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.7"
    oauth-sign "~0.8.1"
    performance-now "^0.2.0"
    qs "~6.4.0"
    safe-buffer "^5.0.1"
    stringstream "~0.0.4"
    tough-cookie "~2.3.0"
    tunnel-agent "^0.6.0"
    uuid "^3.0.0"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"

require-main-filename@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/require-main-filename/download/require-main-filename-1.0.1.tgz#97f717b69d48784f5f526a6c5aa8ffdda055a4d1"

require-uncached@^1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/require-uncached/download/require-uncached-1.0.3.tgz#4e0d56d6c9662fd31e43011c4b95aa49955421d3"
  dependencies:
    caller-path "^0.1.0"
    resolve-from "^1.0.0"

requires-port@1.0.x, requires-port@1.x.x:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"

resolve-from@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/resolve-from/download/resolve-from-1.0.1.tgz#26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226"

resolve@^1.1.6:
  version "1.3.3"
  resolved "http://registry.npm.taobao.org/resolve/download/resolve-1.3.3.tgz#655907c3469a8680dc2de3a275a8fdd69691f0e5"
  dependencies:
    path-parse "^1.0.5"

resp-modifier@6.0.2:
  version "6.0.2"
  resolved "http://registry.npm.taobao.org/resp-modifier/download/resp-modifier-6.0.2.tgz#b124de5c4fbafcba541f48ffa73970f4aa456b4f"
  dependencies:
    debug "^2.2.0"
    minimatch "^3.0.2"

restore-cursor@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/restore-cursor/download/restore-cursor-1.0.1.tgz#34661f46886327fed2991479152252df92daa541"
  dependencies:
    exit-hook "^1.0.0"
    onetime "^1.0.0"

retry@^0.10.0, retry@~0.10.0:
  version "0.10.1"
  resolved "http://registry.npm.taobao.org/retry/download/retry-0.10.1.tgz#e76388d217992c252750241d3d3956fed98d8ff4"

right-align@^0.1.1:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/right-align/download/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  dependencies:
    align-text "^0.1.1"

rimraf@2, rimraf@^2.2.6, rimraf@^2.2.8, rimraf@^2.5.1, rimraf@^2.5.2, rimraf@^2.6.1:
  version "2.6.1"
  resolved "http://registry.npm.taobao.org/rimraf/download/rimraf-2.6.1.tgz#c2338ec643df7a1b7fe5c54fa86f57428a55f33d"
  dependencies:
    glob "^7.0.5"

rimraf@~2.2.8:
  version "2.2.8"
  resolved "http://registry.npm.taobao.org/rimraf/download/rimraf-2.2.8.tgz#e439be2aaee327321952730f99a8929e4fc50582"

rimraf@~2.5.4:
  version "2.5.4"
  resolved "http://registry.npm.taobao.org/rimraf/download/rimraf-2.5.4.tgz#96800093cbf1a0c86bd95b4625467535c29dfa04"
  dependencies:
    glob "^7.0.5"

ripemd160@0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/ripemd160/download/ripemd160-0.2.0.tgz#2bf198bde167cacfa51c0a928e84b68bbe171fce"

run-async@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/run-async/download/run-async-0.1.0.tgz#c8ad4a5e110661e402a7d21b530e009f25f8e389"
  dependencies:
    once "^1.3.0"

rx-lite@^3.1.2:
  version "3.1.2"
  resolved "http://registry.npm.taobao.org/rx-lite/download/rx-lite-3.1.2.tgz#19ce502ca572665f3b647b10939f97fd1615f102"

rx@4.1.0:
  version "4.1.0"
  resolved "http://registry.npm.taobao.org/rx/download/rx-4.1.0.tgz#a5f13ff79ef3b740fe30aa803fb09f98805d4782"

safe-buffer@^5.0.1, safe-buffer@~5.0.1:
  version "5.0.1"
  resolved "http://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.0.1.tgz#d263ca54696cd8a306b5ca6551e92de57918fbe7"

sass-graph@^2.1.1:
  version "2.2.4"
  resolved "http://registry.npm.taobao.org/sass-graph/download/sass-graph-2.2.4.tgz#13fbd63cd1caf0908b9fd93476ad43a51d1e0b49"
  dependencies:
    glob "^7.0.0"
    lodash "^4.0.0"
    scss-tokenizer "^0.2.3"
    yargs "^7.0.0"

sass-loader@^4.0.0:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/sass-loader/download/sass-loader-4.1.1.tgz#79ef9468cf0bf646c29529e1f2cba6bd6e51c7bc"
  dependencies:
    async "^2.0.1"
    loader-utils "^0.2.15"
    object-assign "^4.1.0"

sax@~1.2.1:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/sax/download/sax-1.2.2.tgz#fd8631a23bc7826bef5d871bdb87378c95647828"

scss-tokenizer@^0.2.3:
  version "0.2.3"
  resolved "http://registry.npm.taobao.org/scss-tokenizer/download/scss-tokenizer-0.2.3.tgz#8eb06db9a9723333824d3f5530641149847ce5d1"
  dependencies:
    js-base64 "^2.1.8"
    source-map "^0.4.2"

seajs-loader@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/seajs-loader/download/seajs-loader-0.0.4.tgz#0b0f515219e01f39943a2d366cb020b4efb8a8ce"

seek-bzip@^1.0.3:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/seek-bzip/download/seek-bzip-1.0.5.tgz#cfe917cb3d274bcffac792758af53173eb1fabdc"
  dependencies:
    commander "~2.8.1"

semver-regex@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/semver-regex/download/semver-regex-1.0.0.tgz#92a4969065f9c70c694753d55248fc68f8f652c9"

semver-truncate@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/semver-truncate/download/semver-truncate-1.1.2.tgz#57f41de69707a62709a7e0104ba2117109ea47e8"
  dependencies:
    semver "^5.3.0"

"semver@2 >=2.2.1 || 3.x || 4 || 5", "semver@2 || 3 || 4 || 5", "semver@2.x || 3.x || 4 || 5", "semver@^2.3.0 || 3.x || 4 || 5", semver@^5.1.0, semver@^5.3.0, semver@~5.3.0:
  version "5.3.0"
  resolved "http://registry.npm.taobao.org/semver/download/semver-5.3.0.tgz#9b2ce5d3de02d17c6012ad326aa6b4d0cf54f94f"

semver@^4.0.3:
  version "4.3.6"
  resolved "http://registry.npm.taobao.org/semver/download/semver-4.3.6.tgz#300bc6e0e86374f7ba61068b5b1ecd57fc6532da"

send@0.15.2:
  version "0.15.2"
  resolved "http://registry.npm.taobao.org/send/download/send-0.15.2.tgz#f91fab4403bcf87e716f70ceb5db2f578bdc17d6"
  dependencies:
    debug "2.6.4"
    depd "~1.1.0"
    destroy "~1.0.4"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.0"
    fresh "0.5.0"
    http-errors "~1.6.1"
    mime "1.3.4"
    ms "1.0.0"
    on-finished "~2.3.0"
    range-parser "~1.2.0"
    statuses "~1.3.1"

send@0.15.3:
  version "0.15.3"
  resolved "http://registry.npm.taobao.org/send/download/send-0.15.3.tgz#5013f9f99023df50d1bd9892c19e3defd1d53309"
  dependencies:
    debug "2.6.7"
    depd "~1.1.0"
    destroy "~1.0.4"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.0"
    fresh "0.5.0"
    http-errors "~1.6.1"
    mime "1.3.4"
    ms "2.0.0"
    on-finished "~2.3.0"
    range-parser "~1.2.0"
    statuses "~1.3.1"

sentence-case@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/sentence-case/download/sentence-case-2.1.1.tgz#1f6e2dda39c168bf92d13f86d4a918933f667ed4"
  dependencies:
    no-case "^2.2.0"
    upper-case-first "^1.1.2"

serve-index@1.8.0, serve-index@^1.7.2:
  version "1.8.0"
  resolved "http://registry.npm.taobao.org/serve-index/download/serve-index-1.8.0.tgz#7c5d96c13fb131101f93c1c5774f8516a1e78d3b"
  dependencies:
    accepts "~1.3.3"
    batch "0.5.3"
    debug "~2.2.0"
    escape-html "~1.0.3"
    http-errors "~1.5.0"
    mime-types "~2.1.11"
    parseurl "~1.3.1"

serve-static@1.12.2:
  version "1.12.2"
  resolved "http://registry.npm.taobao.org/serve-static/download/serve-static-1.12.2.tgz#e546e2726081b81b4bcec8e90808ebcdd323afba"
  dependencies:
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    parseurl "~1.3.1"
    send "0.15.2"

serve-static@1.12.3:
  version "1.12.3"
  resolved "http://registry.npm.taobao.org/serve-static/download/serve-static-1.12.3.tgz#9f4ba19e2f3030c547f8af99107838ec38d5b1e2"
  dependencies:
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    parseurl "~1.3.1"
    send "0.15.3"

server-destroy@1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/server-destroy/download/server-destroy-1.0.1.tgz#f13bf928e42b9c3e79383e61cc3998b5d14e6cdd"

set-blocking@^2.0.0, set-blocking@~2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz#045f9782d011ae9a6803ddd382b24392b3d890f7"

set-immediate-shim@^1.0.0, set-immediate-shim@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/set-immediate-shim/download/set-immediate-shim-1.0.1.tgz#4b2b1b27eb808a9f8dcc481a58e5e56f599f3f61"

setimmediate@^1.0.4:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz#290cbb232e306942d7d7ea9b83732ab7856f8285"

setprototypeof@1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.0.2.tgz#81a552141ec104b88e89ce383103ad5c66564d08"

setprototypeof@1.0.3:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.0.3.tgz#66567e37043eeb4f04d91bd658c0cbefb55b8e04"

sha.js@2.2.6:
  version "2.2.6"
  resolved "http://registry.npm.taobao.org/sha.js/download/sha.js-2.2.6.tgz#17ddeddc5f722fb66501658895461977867315ba"

sha@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/sha/download/sha-2.0.1.tgz#6030822fbd2c9823949f8f72ed6411ee5cf25aae"
  dependencies:
    graceful-fs "^4.1.2"
    readable-stream "^2.0.2"

shelljs@^0.7.5:
  version "0.7.8"
  resolved "http://registry.npm.taobao.org/shelljs/download/shelljs-0.7.8.tgz#decbcf874b0d1e5fb72e14b164a9683048e9acb3"
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

sigmund@~1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/sigmund/download/sigmund-1.0.1.tgz#3ff21f198cad2175f9f3b781853fd94d0d19b590"

signal-exit@^3.0.0:
  version "3.0.2"
  resolved "http://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

slash@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz#c41f2f6c39fc16d1cd17ad4b5d896114ae470d55"

slice-ansi@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/slice-ansi/download/slice-ansi-0.0.4.tgz#edbf8903f66f7ce2f8eafd6ceed65e264c831b35"

slide@^1.1.3, slide@^1.1.5, slide@~1.1.3, slide@~1.1.6:
  version "1.1.6"
  resolved "http://registry.npm.taobao.org/slide/download/slide-1.1.6.tgz#56eb027d65b4d2dce6cb2e2d32c4d4afc9e1d707"

snake-case@^2.1.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/snake-case/download/snake-case-2.1.0.tgz#41bdb1b73f30ec66a04d4e2cad1b76387d4d6d9f"
  dependencies:
    no-case "^2.2.0"

sntp@1.x.x:
  version "1.0.9"
  resolved "http://registry.npm.taobao.org/sntp/download/sntp-1.0.9.tgz#6541184cc90aeea6c6e7b35e2659082443c66198"
  dependencies:
    hoek "2.x.x"

socket.io-adapter@0.5.0:
  version "0.5.0"
  resolved "http://registry.npm.taobao.org/socket.io-adapter/download/socket.io-adapter-0.5.0.tgz#cb6d4bb8bec81e1078b99677f9ced0046066bb8b"
  dependencies:
    debug "2.3.3"
    socket.io-parser "2.3.1"

socket.io-client@1.6.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/socket.io-client/download/socket.io-client-1.6.0.tgz#5b668f4f771304dfeed179064708386fa6717853"
  dependencies:
    backo2 "1.0.2"
    component-bind "1.0.0"
    component-emitter "1.2.1"
    debug "2.3.3"
    engine.io-client "1.8.0"
    has-binary "0.1.7"
    indexof "0.0.1"
    object-component "0.0.3"
    parseuri "0.0.5"
    socket.io-parser "2.3.1"
    to-array "0.1.4"

socket.io-parser@2.3.1:
  version "2.3.1"
  resolved "http://registry.npm.taobao.org/socket.io-parser/download/socket.io-parser-2.3.1.tgz#dd532025103ce429697326befd64005fcfe5b4a0"
  dependencies:
    component-emitter "1.1.2"
    debug "2.2.0"
    isarray "0.0.1"
    json3 "3.3.2"

socket.io@1.6.0:
  version "1.6.0"
  resolved "http://registry.npm.taobao.org/socket.io/download/socket.io-1.6.0.tgz#3e40d932637e6bd923981b25caf7c53e83b6e2e1"
  dependencies:
    debug "2.3.3"
    engine.io "1.8.0"
    has-binary "0.1.7"
    object-assign "4.1.0"
    socket.io-adapter "0.5.0"
    socket.io-client "1.6.0"
    socket.io-parser "2.3.1"

sockjs-client@^1.0.3:
  version "1.1.4"
  resolved "http://registry.npm.taobao.org/sockjs-client/download/sockjs-client-1.1.4.tgz#5babe386b775e4cf14e7520911452654016c8b12"
  dependencies:
    debug "^2.6.6"
    eventsource "0.1.6"
    faye-websocket "~0.11.0"
    inherits "^2.0.1"
    json3 "^3.3.2"
    url-parse "^1.1.8"

sockjs@^0.3.15:
  version "0.3.18"
  resolved "http://registry.npm.taobao.org/sockjs/download/sockjs-0.3.18.tgz#d9b289316ca7df77595ef299e075f0f937eb4207"
  dependencies:
    faye-websocket "^0.10.0"
    uuid "^2.0.2"

sort-keys@^1.0.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/sort-keys/download/sort-keys-1.1.2.tgz#441b6d4d346798f1b4e49e8920adfba0e543f9ad"
  dependencies:
    is-plain-obj "^1.0.0"

sorted-object@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/sorted-object/download/sorted-object-2.0.1.tgz#7d631f4bd3a798a24af1dffcfbfe83337a5df5fc"

source-list-map@^0.1.4, source-list-map@~0.1.7:
  version "0.1.8"
  resolved "http://registry.npm.taobao.org/source-list-map/download/source-list-map-0.1.8.tgz#c550b2ab5427f6b3f21f5afead88c4f5587b2106"

source-map-support@^0.4.2:
  version "0.4.15"
  resolved "http://registry.npm.taobao.org/source-map-support/download/source-map-support-0.4.15.tgz#03202df65c06d2bd8c7ec2362a193056fef8d3b1"
  dependencies:
    source-map "^0.5.6"

source-map@0.4.x, source-map@^0.4.2, source-map@~0.4.1, source-map@~0.4.2:
  version "0.4.4"
  resolved "http://registry.npm.taobao.org/source-map/download/source-map-0.4.4.tgz#eba4f5da9c0dc999de68032d8b4f76173652036b"
  dependencies:
    amdefine ">=0.0.4"

source-map@^0.5.0, source-map@^0.5.3, source-map@^0.5.6, source-map@~0.5.0, source-map@~0.5.1:
  version "0.5.6"
  resolved "http://registry.npm.taobao.org/source-map/download/source-map-0.5.6.tgz#75ce38f52bf0733c5a7f0c118d81334a2bb5f412"

sparkles@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/sparkles/download/sparkles-1.0.0.tgz#1acbbfb592436d10bbe8f785b7cc6f82815012c3"

spdx-correct@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/spdx-correct/download/spdx-correct-1.0.2.tgz#4b3073d933ff51f3912f03ac5519498a4150db40"
  dependencies:
    spdx-license-ids "^1.0.2"

spdx-expression-parse@~1.0.0:
  version "1.0.4"
  resolved "http://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-1.0.4.tgz#9bdf2f20e1f40ed447fbe273266191fced51626c"

spdx-license-ids@^1.0.2:
  version "1.2.2"
  resolved "http://registry.npm.taobao.org/spdx-license-ids/download/spdx-license-ids-1.2.2.tgz#c9df7a3424594ade6bd11900d596696dc06bac57"

sprintf-js@^1.0.3:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.1.1.tgz#36be78320afe5801f6cea3ee78b6e5aab940ea0c"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

squeak@^1.0.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/squeak/download/squeak-1.3.0.tgz#33045037b64388b567674b84322a6521073916c3"
  dependencies:
    chalk "^1.0.0"
    console-stream "^0.1.1"
    lpad-align "^1.0.1"

sshpk@^1.7.0:
  version "1.13.1"
  resolved "http://registry.npm.taobao.org/sshpk/download/sshpk-1.13.1.tgz#512df6da6287144316dc4c18fe1cf1d940739be3"
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    dashdash "^1.12.0"
    getpass "^0.1.1"
  optionalDependencies:
    bcrypt-pbkdf "^1.0.0"
    ecc-jsbn "~0.1.1"
    jsbn "~0.1.0"
    tweetnacl "~0.14.0"

stat-mode@^0.2.0:
  version "0.2.2"
  resolved "http://registry.npm.taobao.org/stat-mode/download/stat-mode-0.2.2.tgz#e6c80b623123d7d80cf132ce538f346289072502"

"statuses@>= 1.3.1 < 2", statuses@~1.3.0, statuses@~1.3.1:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/statuses/download/statuses-1.3.1.tgz#faf51b9eb74aaef3b3acf4ad5f61abf24cb7b93e"

stream-browserify@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.1.tgz#66266ee5f9bdb9940a4e4514cafb43bb71e5c9db"
  dependencies:
    inherits "~2.0.1"
    readable-stream "^2.0.2"

stream-cache@~0.0.1:
  version "0.0.2"
  resolved "http://registry.npm.taobao.org/stream-cache/download/stream-cache-0.0.2.tgz#1ac5ad6832428ca55667dbdee395dad4e6db118f"

stream-combiner2@^1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/stream-combiner2/download/stream-combiner2-1.1.1.tgz#fb4d8a1420ea362764e21ad4780397bebcb41cbe"
  dependencies:
    duplexer2 "~0.1.0"
    readable-stream "^2.0.2"

stream-http@^2.3.1:
  version "2.7.2"
  resolved "http://registry.npm.taobao.org/stream-http/download/stream-http-2.7.2.tgz#40a050ec8dc3b53b33d9909415c02c0bf1abfbad"
  dependencies:
    builtin-status-codes "^3.0.0"
    inherits "^2.0.1"
    readable-stream "^2.2.6"
    to-arraybuffer "^1.0.0"
    xtend "^4.0.0"

stream-shift@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.0.tgz#d5c752825e5367e786f78e18e445ea223a155952"

stream-throttle@^0.1.3:
  version "0.1.3"
  resolved "http://registry.npm.taobao.org/stream-throttle/download/stream-throttle-0.1.3.tgz#add57c8d7cc73a81630d31cd55d3961cfafba9c3"
  dependencies:
    commander "^2.2.0"
    limiter "^1.0.5"

strict-uri-encode@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/strict-uri-encode/download/strict-uri-encode-1.1.0.tgz#279b225df1d582b1f54e65addd4352e18faa0713"

string-width@^1.0.1, string-width@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/string-width/download/string-width-1.0.2.tgz#118bdf5b8cdc51a2a7e70d211e07e2b0b9b107d3"
  dependencies:
    code-point-at "^1.0.0"
    is-fullwidth-code-point "^1.0.0"
    strip-ansi "^3.0.0"

string-width@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/string-width/download/string-width-2.0.0.tgz#635c5436cc72a6e0c387ceca278d4e2eec52687e"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^3.0.0"

string_decoder@^0.10.25, string_decoder@~0.10.x:
  version "0.10.31"
  resolved "http://registry.npm.taobao.org/string_decoder/download/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"

string_decoder@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/string_decoder/download/string_decoder-1.0.2.tgz#b29e1f4e1125fa97a10382b8a533737b7491e179"
  dependencies:
    safe-buffer "~5.0.1"

stringstream@~0.0.4:
  version "0.0.5"
  resolved "http://registry.npm.taobao.org/stringstream/download/stringstream-0.0.5.tgz#4e484cd4de5a0bbbee18e46307710a8a81621878"

strip-ansi@^0.3.0:
  version "0.3.0"
  resolved "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-0.3.0.tgz#25f48ea22ca79187f3174a4db8759347bb126220"
  dependencies:
    ansi-regex "^0.2.1"

strip-ansi@^2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-2.0.1.tgz#df62c1aa94ed2f114e1d0f21fd1d50482b79a60e"
  dependencies:
    ansi-regex "^1.0.0"

strip-ansi@^3.0.0, strip-ansi@^3.0.1, strip-ansi@~3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@~0.1.0:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-0.1.1.tgz#39e8a98d044d150660abe4a6808acf70bb7bc991"

strip-bom-stream@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/strip-bom-stream/download/strip-bom-stream-1.0.0.tgz#e7144398577d51a6bed0fa1994fa05f43fd988ee"
  dependencies:
    first-chunk-stream "^1.0.0"
    strip-bom "^2.0.0"

strip-bom@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/strip-bom/download/strip-bom-1.0.0.tgz#85b8862f3844b5a6d5ec8467a93598173a36f794"
  dependencies:
    first-chunk-stream "^1.0.0"
    is-utf8 "^0.2.0"

strip-bom@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/strip-bom/download/strip-bom-2.0.0.tgz#6219a85616520491f35788bdbf1447a99c7e6b0e"
  dependencies:
    is-utf8 "^0.2.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/strip-bom/download/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"

strip-dirs@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/strip-dirs/download/strip-dirs-1.1.1.tgz#960bbd1287844f3975a4558aa103a8255e2456a0"
  dependencies:
    chalk "^1.0.0"
    get-stdin "^4.0.1"
    is-absolute "^0.1.5"
    is-natural-number "^2.0.0"
    minimist "^1.1.0"
    sum-up "^1.0.1"

strip-indent@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/strip-indent/download/strip-indent-1.0.1.tgz#0c7962a6adefa7bbd4ac366460a638552ae1a0a2"
  dependencies:
    get-stdin "^4.0.1"

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "http://registry.npm.taobao.org/strip-json-comments/download/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"

strip-outer@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/strip-outer/download/strip-outer-1.0.0.tgz#aac0ba60d2e90c5d4f275fd8869fd9a2d310ffb8"
  dependencies:
    escape-string-regexp "^1.0.2"

style-loader@^0.13.1:
  version "0.13.2"
  resolved "http://registry.npm.taobao.org/style-loader/download/style-loader-0.13.2.tgz#74533384cf698c7104c7951150b49717adc2f3bb"
  dependencies:
    loader-utils "^1.0.2"

sum-up@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/sum-up/download/sum-up-1.0.3.tgz#1c661f667057f63bcb7875aa1438bc162525156e"
  dependencies:
    chalk "^1.0.0"

supports-color@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-0.2.0.tgz#d92de2694eb3f67323973d7ae3d8b55b4c22190a"

supports-color@^1.3.0:
  version "1.3.1"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-1.3.1.tgz#15758df09d8ff3b4acc307539fabe27095e1042d"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

supports-color@^3.1.0, supports-color@^3.1.1, supports-color@^3.2.3:
  version "3.2.3"
  resolved "http://registry.npm.taobao.org/supports-color/download/supports-color-3.2.3.tgz#65ac0504b3954171d8a64946b2ae3cbb8a5f54f6"
  dependencies:
    has-flag "^1.0.0"

svgo@^0.6.0:
  version "0.6.6"
  resolved "http://registry.npm.taobao.org/svgo/download/svgo-0.6.6.tgz#b340889036f20f9b447543077d0f5573ed044c08"
  dependencies:
    coa "~1.0.1"
    colors "~1.1.2"
    csso "~2.0.0"
    js-yaml "~3.6.0"
    mkdirp "~0.5.1"
    sax "~1.2.1"
    whet.extend "~0.9.9"

svgo@^0.7.0:
  version "0.7.2"
  resolved "http://registry.npm.taobao.org/svgo/download/svgo-0.7.2.tgz#9f5772413952135c6fefbf40afe6a4faa88b4bb5"
  dependencies:
    coa "~1.0.1"
    colors "~1.1.2"
    csso "~2.3.1"
    js-yaml "~3.7.0"
    mkdirp "~0.5.1"
    sax "~1.2.1"
    whet.extend "~0.9.9"

swap-case@^1.1.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/swap-case/download/swap-case-1.1.2.tgz#c39203a4587385fad3c850a0bd1bcafa081974e3"
  dependencies:
    lower-case "^1.1.1"
    upper-case "^1.1.1"

table@^3.7.8:
  version "3.8.3"
  resolved "http://registry.npm.taobao.org/table/download/table-3.8.3.tgz#2bbc542f0fda9861a755d3947fefd8b3f513855f"
  dependencies:
    ajv "^4.7.0"
    ajv-keywords "^1.0.0"
    chalk "^1.1.1"
    lodash "^4.0.0"
    slice-ansi "0.0.4"
    string-width "^2.0.0"

tapable@^0.1.8, tapable@~0.1.8:
  version "0.1.10"
  resolved "http://registry.npm.taobao.org/tapable/download/tapable-0.1.10.tgz#29c35707c2b70e50d07482b5d202e8ed446dafd4"

tar-pack@^3.4.0:
  version "3.4.0"
  resolved "http://registry.npm.taobao.org/tar-pack/download/tar-pack-3.4.0.tgz#23be2d7f671a8339376cbdb0b8fe3fdebf317984"
  dependencies:
    debug "^2.2.0"
    fstream "^1.0.10"
    fstream-ignore "^1.0.5"
    once "^1.3.3"
    readable-stream "^2.1.4"
    rimraf "^2.5.1"
    tar "^2.2.1"
    uid-number "^0.0.6"

tar-stream@^1.1.1:
  version "1.5.4"
  resolved "http://registry.npm.taobao.org/tar-stream/download/tar-stream-1.5.4.tgz#36549cf04ed1aee9b2a30c0143252238daf94016"
  dependencies:
    bl "^1.0.0"
    end-of-stream "^1.0.0"
    readable-stream "^2.0.0"
    xtend "^4.0.0"

tar@^2.0.0, tar@^2.2.1, tar@~2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/tar/download/tar-2.2.1.tgz#8e4d2a256c0e2185c6b18ad694aec968b83cb1d1"
  dependencies:
    block-stream "*"
    fstream "^1.0.2"
    inherits "2"

tempfile@^1.0.0:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/tempfile/download/tempfile-1.1.1.tgz#5bcc4eaecc4ab2c707d8bc11d99ccc9a2cb287f2"
  dependencies:
    os-tmpdir "^1.0.0"
    uuid "^2.0.1"

text-table@~0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"

tfunk@^3.0.1:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/tfunk/download/tfunk-3.1.0.tgz#38e4414fc64977d87afdaa72facb6d29f82f7b5b"
  dependencies:
    chalk "^1.1.1"
    object-path "^0.9.0"

throttle-debounce@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/throttle-debounce/download/throttle-debounce-1.0.1.tgz#dad0fe130f9daf3719fdea33dc36a8e6ba7f30b5"

through2-filter@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/through2-filter/download/through2-filter-2.0.0.tgz#60bc55a0dacb76085db1f9dae99ab43f83d622ec"
  dependencies:
    through2 "~2.0.0"
    xtend "~4.0.0"

through2@^0.6.0, through2@^0.6.1:
  version "0.6.5"
  resolved "http://registry.npm.taobao.org/through2/download/through2-0.6.5.tgz#41ab9c67b29d57209071410e1d7a7a968cd3ad48"
  dependencies:
    readable-stream ">=1.0.33-1 <1.1.0-0"
    xtend ">=4.0.0 <4.1.0-0"

through2@^2.0.0, through2@~2.0.0:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/through2/download/through2-2.0.3.tgz#0004569b37c7c74ba39c43f3ced78d1ad94140be"
  dependencies:
    readable-stream "^2.1.5"
    xtend "~4.0.1"

through@^2.3.6, through@~2.3.6:
  version "2.3.8"
  resolved "http://registry.npm.taobao.org/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

time-stamp@^1.0.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/time-stamp/download/time-stamp-1.1.0.tgz#764a5a11af50561921b133f3b44e618687e0f5c3"

timed-out@^3.0.0:
  version "3.1.3"
  resolved "http://registry.npm.taobao.org/timed-out/download/timed-out-3.1.3.tgz#95860bfcc5c76c277f8f8326fd0f5b2e20eba217"

timers-browserify@^2.0.2:
  version "2.0.2"
  resolved "http://registry.npm.taobao.org/timers-browserify/download/timers-browserify-2.0.2.tgz#ab4883cf597dcd50af211349a00fbca56ac86b86"
  dependencies:
    setimmediate "^1.0.4"

tiny-lr-fork@0.0.5:
  version "0.0.5"
  resolved "http://registry.npm.taobao.org/tiny-lr-fork/download/tiny-lr-fork-0.0.5.tgz#1e99e1e2a8469b736ab97d97eefa98c71f76ed0a"
  dependencies:
    debug "~0.7.0"
    faye-websocket "~0.4.3"
    noptify "~0.0.3"
    qs "~0.5.2"

title-case@^2.1.0:
  version "2.1.1"
  resolved "http://registry.npm.taobao.org/title-case/download/title-case-2.1.1.tgz#3e127216da58d2bc5becf137ab91dae3a7cd8faa"
  dependencies:
    no-case "^2.2.0"
    upper-case "^1.0.3"

tmp@0.0.28:
  version "0.0.28"
  resolved "http://registry.npm.taobao.org/tmp/download/tmp-0.0.28.tgz#172735b7f614ea7af39664fa84cf0de4e515d120"
  dependencies:
    os-tmpdir "~1.0.1"

to-absolute-glob@^0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/to-absolute-glob/download/to-absolute-glob-0.1.1.tgz#1cdfa472a9ef50c239ee66999b662ca0eb39937f"
  dependencies:
    extend-shallow "^2.0.1"

to-array@0.1.4:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/to-array/download/to-array-0.1.4.tgz#17e6c11f73dd4f3d74cda7a4ff3238e9ad9bf890"

to-arraybuffer@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz#7d229b1fcc637e466ca081180836a7aabff83f43"

to-fast-properties@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-1.0.3.tgz#b83571fa4d8c25b82e231b06e3a3055de4ca1a47"

tough-cookie@~2.3.0:
  version "2.3.2"
  resolved "http://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.3.2.tgz#f081f76e4c85720e6c37a5faced737150d84072a"
  dependencies:
    punycode "^1.4.1"

trim-newlines@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/trim-newlines/download/trim-newlines-1.0.0.tgz#5887966bb582a4503a41eb524f7d35011815a613"

trim-repeated@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/trim-repeated/download/trim-repeated-1.0.0.tgz#e3646a2ea4e891312bf7eace6cfb05380bc01c21"
  dependencies:
    escape-string-regexp "^1.0.2"

trim-right@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/trim-right/download/trim-right-1.0.1.tgz#cb2e1203067e0c8de1f614094b9fe45704ea6003"

tryit@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/tryit/download/tryit-1.0.3.tgz#393be730a9446fd1ead6da59a014308f36c289cb"

tty-browserify@0.0.0:
  version "0.0.0"
  resolved "http://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz#a157ba402da24e9bf957f9aa69d524eed42901a6"

tunnel-agent@^0.4.0, tunnel-agent@~0.4.1:
  version "0.4.3"
  resolved "http://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.4.3.tgz#6373db76909fe570e08d73583365ed828a74eeeb"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"

type-check@~0.3.2:
  version "0.3.2"
  resolved "http://registry.npm.taobao.org/type-check/download/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  dependencies:
    prelude-ls "~1.1.2"

type-is@~1.6.15:
  version "1.6.15"
  resolved "http://registry.npm.taobao.org/type-is/download/type-is-1.6.15.tgz#cab10fb4909e441c82842eafe1ad646c81804410"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.15"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

ua-parser-js@0.7.12:
  version "0.7.12"
  resolved "http://registry.npm.taobao.org/ua-parser-js/download/ua-parser-js-0.7.12.tgz#04c81a99bdd5dc52263ea29d24c6bf8d4818a4bb"

uglify-js@2.6.x:
  version "2.6.4"
  resolved "http://registry.npm.taobao.org/uglify-js/download/uglify-js-2.6.4.tgz#65ea2fb3059c9394692f15fed87c2b36c16b9adf"
  dependencies:
    async "~0.2.6"
    source-map "~0.5.1"
    uglify-to-browserify "~1.0.0"
    yargs "~3.10.0"

uglify-js@^2.7.3:
  version "2.8.29"
  resolved "http://registry.npm.taobao.org/uglify-js/download/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-js@~2.7.3:
  version "2.7.5"
  resolved "http://registry.npm.taobao.org/uglify-js/download/uglify-js-2.7.5.tgz#4612c0c7baaee2ba7c487de4904ae122079f2ca8"
  dependencies:
    async "~0.2.6"
    source-map "~0.5.1"
    uglify-to-browserify "~1.0.0"
    yargs "~3.10.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"

uid-number@0.0.6, uid-number@^0.0.6:
  version "0.0.6"
  resolved "http://registry.npm.taobao.org/uid-number/download/uid-number-0.0.6.tgz#0ea10e8035e8eb5b8e4449f06da1c730663baa81"

ultron@1.0.x:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/ultron/download/ultron-1.0.2.tgz#ace116ab557cd197386a4e88f4685378c8b2e4fa"

umask@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/umask/download/umask-1.1.0.tgz#f29cebf01df517912bb58ff9c4e50fde8e33320d"

underscore.string@^3.1.1:
  version "3.3.4"
  resolved "http://registry.npm.taobao.org/underscore.string/download/underscore.string-3.3.4.tgz#2c2a3f9f83e64762fdc45e6ceac65142864213db"
  dependencies:
    sprintf-js "^1.0.3"
    util-deprecate "^1.0.2"

underscore.string@~2.2.1:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/underscore.string/download/underscore.string-2.2.1.tgz#d7c0fa2af5d5a1a67f4253daee98132e733f0f19"

underscore.string@~2.3.3:
  version "2.3.3"
  resolved "http://registry.npm.taobao.org/underscore.string/download/underscore.string-2.3.3.tgz#71c08bf6b428b1133f37e78fa3a21c82f7329b0d"

underscore.string@~2.4.0:
  version "2.4.0"
  resolved "http://registry.npm.taobao.org/underscore.string/download/underscore.string-2.4.0.tgz#8cdd8fbac4e2d2ea1e7e2e8097c42f442280f85b"

underscore@1.7.x, underscore@~1.7.0:
  version "1.7.0"
  resolved "http://registry.npm.taobao.org/underscore/download/underscore-1.7.0.tgz#6bbaf0877500d36be34ecaa584e0db9fef035209"

uniq@^1.0.1:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz#b31c5ae8254844a3a8281541ce2b04b865a734ff"

uniqid@^4.0.0:
  version "4.1.1"
  resolved "http://registry.npm.taobao.org/uniqid/download/uniqid-4.1.1.tgz#89220ddf6b751ae52b5f72484863528596bb84c1"
  dependencies:
    macaddress "^0.2.8"

uniqs@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz#ffede4b36b25290696e6e165d4a59edb998e6b02"

unique-filename@~1.1.0:
  version "1.1.0"
  resolved "http://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.0.tgz#d05f2fe4032560871f30e93cbe735eea201514f3"
  dependencies:
    unique-slug "^2.0.0"

unique-slug@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/unique-slug/download/unique-slug-2.0.0.tgz#db6676e7c7cc0629878ff196097c78855ae9f4ab"
  dependencies:
    imurmurhash "^0.1.4"

unique-stream@^2.0.2:
  version "2.2.1"
  resolved "http://registry.npm.taobao.org/unique-stream/download/unique-stream-2.2.1.tgz#5aa003cfbe94c5ff866c4e7d668bb1c4dbadb369"
  dependencies:
    json-stable-stringify "^1.0.0"
    through2-filter "^2.0.0"

universalify@^0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/universalify/download/universalify-0.1.0.tgz#9eb1c4651debcc670cc94f1a75762332bb967778"

unpipe@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"

unzip-response@^1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/unzip-response/download/unzip-response-1.0.2.tgz#b984f0877fc0a89c2c773cc1ef7b5b232b5b06fe"

upper-case-first@^1.1.0, upper-case-first@^1.1.2:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/upper-case-first/download/upper-case-first-1.1.2.tgz#5d79bedcff14419518fd2edb0a0507c9b6859115"
  dependencies:
    upper-case "^1.1.1"

upper-case@^1.0.3, upper-case@^1.1.0, upper-case@^1.1.1, upper-case@^1.1.3:
  version "1.1.3"
  resolved "http://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz#f6b4501c2ec4cdd26ba78be7222961de77621598"

url-loader@^0.5.7:
  version "0.5.9"
  resolved "http://registry.npm.taobao.org/url-loader/download/url-loader-0.5.9.tgz#cc8fea82c7b906e7777019250869e569e995c295"
  dependencies:
    loader-utils "^1.0.2"
    mime "1.3.x"

url-parse-lax@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/url-parse-lax/download/url-parse-lax-1.0.0.tgz#7af8f303645e9bd79a272e7a14ac68bc0609da73"
  dependencies:
    prepend-http "^1.0.1"

url-parse@1.0.x:
  version "1.0.5"
  resolved "http://registry.npm.taobao.org/url-parse/download/url-parse-1.0.5.tgz#0854860422afdcfefeb6c965c662d4800169927b"
  dependencies:
    querystringify "0.0.x"
    requires-port "1.0.x"

url-parse@^1.1.8:
  version "1.1.9"
  resolved "http://registry.npm.taobao.org/url-parse/download/url-parse-1.1.9.tgz#c67f1d775d51f0a18911dd7b3ffad27bb9e5bd19"
  dependencies:
    querystringify "~1.0.0"
    requires-port "1.0.x"

url-regex@^3.0.0:
  version "3.2.0"
  resolved "http://registry.npm.taobao.org/url-regex/download/url-regex-3.2.0.tgz#dbad1e0c9e29e105dd0b1f09f6862f7fdb482724"
  dependencies:
    ip-regex "^1.0.1"

url@^0.11.0:
  version "0.11.0"
  resolved "http://registry.npm.taobao.org/url/download/url-0.11.0.tgz#3838e97cfc60521eb73c525a8e55bfdd9e2e28f1"
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

user-home@^2.0.0:
  version "2.0.0"
  resolved "http://registry.npm.taobao.org/user-home/download/user-home-2.0.0.tgz#9c70bfd8169bc1dcbf48604e0f04b8b49cde9e9f"
  dependencies:
    os-homedir "^1.0.0"

util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

util-extend@^1.0.1:
  version "1.0.3"
  resolved "http://registry.npm.taobao.org/util-extend/download/util-extend-1.0.3.tgz#a7c216d267545169637b3b6edc6ca9119e2ff93f"

util@0.10.3, util@^0.10.3:
  version "0.10.3"
  resolved "http://registry.npm.taobao.org/util/download/util-0.10.3.tgz#7afb1afe50805246489e3db7fe0ed379336ac0f9"
  dependencies:
    inherits "2.0.1"

utils-merge@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.0.tgz#0294fb922bb9375153541c4f7096231f287c8af8"

uuid@^2.0.1, uuid@^2.0.2:
  version "2.0.3"
  resolved "http://registry.npm.taobao.org/uuid/download/uuid-2.0.3.tgz#67e2e863797215530dff318e5bf9dcebfd47b21a"

uuid@^3.0.0:
  version "3.1.0"
  resolved "http://registry.npm.taobao.org/uuid/download/uuid-3.1.0.tgz#3dd3d3e790abc24d7b0d3a034ffababe28ebbc04"

vali-date@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/vali-date/download/vali-date-1.0.0.tgz#1b904a59609fb328ef078138420934f6b86709a6"

validate-npm-package-license@^3.0.1:
  version "3.0.1"
  resolved "http://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.1.tgz#2804babe712ad3379459acfbe24746ab2c303fbc"
  dependencies:
    spdx-correct "~1.0.0"
    spdx-expression-parse "~1.0.0"

validate-npm-package-name@^3.0.0:
  version "3.0.0"
  resolved "http://registry.npm.taobao.org/validate-npm-package-name/download/validate-npm-package-name-3.0.0.tgz#5fa912d81eb7d0c74afc140de7317f0ca7df437e"
  dependencies:
    builtins "^1.0.3"

validate-npm-package-name@~2.2.2:
  version "2.2.2"
  resolved "http://registry.npm.taobao.org/validate-npm-package-name/download/validate-npm-package-name-2.2.2.tgz#f65695b22f7324442019a3c7fa39a6e7fd299085"
  dependencies:
    builtins "0.0.7"

vary@~1.1.0, vary@~1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/vary/download/vary-1.1.1.tgz#67535ebb694c1d52257457984665323f587e8d37"

vendors@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/vendors/download/vendors-1.0.1.tgz#37ad73c8ee417fb3d580e785312307d274847f22"

verror@1.3.6:
  version "1.3.6"
  resolved "http://registry.npm.taobao.org/verror/download/verror-1.3.6.tgz#cff5df12946d297d2baaefaa2689e25be01c005c"
  dependencies:
    extsprintf "1.0.2"

vinyl-assign@^1.0.1:
  version "1.2.1"
  resolved "http://registry.npm.taobao.org/vinyl-assign/download/vinyl-assign-1.2.1.tgz#4d198891b5515911d771a8cd9c5480a46a074a45"
  dependencies:
    object-assign "^4.0.1"
    readable-stream "^2.0.0"

vinyl-fs@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/vinyl-fs/download/vinyl-fs-1.0.0.tgz#d15752e68c2dad74364e7e853473735354692edf"
  dependencies:
    duplexify "^3.2.0"
    glob-stream "^4.0.1"
    glob-watcher "^0.0.8"
    graceful-fs "^3.0.0"
    merge-stream "^0.1.7"
    mkdirp "^0.5.0"
    object-assign "^2.0.0"
    strip-bom "^1.0.0"
    through2 "^0.6.1"
    vinyl "^0.4.0"

vinyl-fs@^2.2.0:
  version "2.4.4"
  resolved "http://registry.npm.taobao.org/vinyl-fs/download/vinyl-fs-2.4.4.tgz#be6ff3270cb55dfd7d3063640de81f25d7532239"
  dependencies:
    duplexify "^3.2.0"
    glob-stream "^5.3.2"
    graceful-fs "^4.0.0"
    gulp-sourcemaps "1.6.0"
    is-valid-glob "^0.3.0"
    lazystream "^1.0.0"
    lodash.isequal "^4.0.0"
    merge-stream "^1.0.0"
    mkdirp "^0.5.0"
    object-assign "^4.0.0"
    readable-stream "^2.0.4"
    strip-bom "^2.0.0"
    strip-bom-stream "^1.0.0"
    through2 "^2.0.0"
    through2-filter "^2.0.0"
    vali-date "^1.0.0"
    vinyl "^1.0.0"

vinyl@^0.4.0, vinyl@^0.4.3:
  version "0.4.6"
  resolved "http://registry.npm.taobao.org/vinyl/download/vinyl-0.4.6.tgz#2f356c87a550a255461f36bbeb2a5ba8bf784847"
  dependencies:
    clone "^0.2.0"
    clone-stats "^0.0.1"

vinyl@^0.5.0:
  version "0.5.3"
  resolved "http://registry.npm.taobao.org/vinyl/download/vinyl-0.5.3.tgz#b0455b38fc5e0cf30d4325132e461970c2091cde"
  dependencies:
    clone "^1.0.0"
    clone-stats "^0.0.1"
    replace-ext "0.0.1"

vinyl@^1.0.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/vinyl/download/vinyl-1.2.0.tgz#5c88036cf565e5df05558bfc911f8656df218884"
  dependencies:
    clone "^1.0.0"
    clone-stats "^0.0.1"
    replace-ext "0.0.1"

vlq@^0.2.1:
  version "0.2.2"
  resolved "http://registry.npm.taobao.org/vlq/download/vlq-0.2.2.tgz#e316d5257b40b86bb43cb8d5fea5d7f54d6b0ca1"

vm-browserify@0.0.4:
  version "0.0.4"
  resolved "http://registry.npm.taobao.org/vm-browserify/download/vm-browserify-0.0.4.tgz#5d7ea45bbef9e4a6ff65f95438e0a87c357d5a73"
  dependencies:
    indexof "0.0.1"

vue-hot-reload-api@^1.2.0:
  version "1.3.3"
  resolved "http://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-1.3.3.tgz#54d22d83786a878493f639cc76bca7992a23be46"

vue-html-loader@^1.2.3:
  version "1.2.4"
  resolved "http://registry.npm.taobao.org/vue-html-loader/download/vue-html-loader-1.2.4.tgz#54ce489be06065c91dc2a1173122f3e004e0a253"
  dependencies:
    es6-templates "^0.2.2"
    fastparse "^1.0.0"
    html-minifier "^2.1.5"
    loader-utils "^1.0.2"
    object-assign "^4.1.0"

vue-html5-editor@^0.5.1:
  version "0.5.1"
  resolved "http://registry.npm.taobao.org/vue-html5-editor/download/vue-html5-editor-0.5.1.tgz#76cc9b0ce1b76ffe9155798307ec4c6dfb5aecaa"

vue-loader@^8.5.3:
  version "8.7.0"
  resolved "http://registry.npm.taobao.org/vue-loader/download/vue-loader-8.7.0.tgz#a9881ead751e75e0084ccd022e7fbdb737806b2a"
  dependencies:
    autoprefixer "^6.0.3"
    consolidate "^0.14.0"
    de-indent "^1.0.0"
    hash-sum "^1.0.2"
    loader-utils "^0.2.10"
    lru-cache "^2.7.0"
    object-assign "^4.0.0"
    parse5 "^2.1.0"
    postcss "^5.0.10"
    postcss-selector-parser "^1.1.2"
    source-map "^0.5.3"
    vue-template-validator "^1.0.0"

vue-router@^2.2.0:
  version "2.6.0"
  resolved "http://registry.npm.taobao.org/vue-router/download/vue-router-2.6.0.tgz#77b271f6e0ac6d57e8e556da58c6582fce0ab712"

vue-style-loader@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-1.0.0.tgz#abeb7bd0f46313083741244d3079d4f14449e049"
  dependencies:
    loader-utils "^0.2.7"

vue-template-validator@^1.0.0:
  version "1.1.5"
  resolved "http://registry.npm.taobao.org/vue-template-validator/download/vue-template-validator-1.1.5.tgz#22d1ee77d0647c1ab14ff7eb01865942d9b3c458"
  dependencies:
    chalk "^1.1.1"

vuex@^1.0.0-rc.2:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/vuex/download/vuex-1.0.1.tgz#b93c3415b5e86e54660164a1a17e2b376c759feb"

ware@^1.2.0:
  version "1.3.0"
  resolved "http://registry.npm.taobao.org/ware/download/ware-1.3.0.tgz#d1b14f39d2e2cb4ab8c4098f756fe4b164e473d4"
  dependencies:
    wrap-fn "^0.1.0"

watchpack@^0.2.1:
  version "0.2.9"
  resolved "http://registry.npm.taobao.org/watchpack/download/watchpack-0.2.9.tgz#62eaa4ab5e5ba35fdfc018275626e3c0f5e3fb0b"
  dependencies:
    async "^0.9.0"
    chokidar "^1.0.0"
    graceful-fs "^4.1.2"

wcwidth@^1.0.0:
  version "1.0.1"
  resolved "http://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  dependencies:
    defaults "^1.0.3"

webpack-core@~0.6.9:
  version "0.6.9"
  resolved "http://registry.npm.taobao.org/webpack-core/download/webpack-core-0.6.9.tgz#fc571588c8558da77be9efb6debdc5a3b172bdc2"
  dependencies:
    source-list-map "~0.1.7"
    source-map "~0.4.1"

webpack-dev-middleware@^1.4.0:
  version "1.11.0"
  resolved "http://registry.npm.taobao.org/webpack-dev-middleware/download/webpack-dev-middleware-1.11.0.tgz#09691d0973a30ad1f82ac73a12e2087f0a4754f9"
  dependencies:
    memory-fs "~0.4.1"
    mime "^1.3.4"
    path-is-absolute "^1.0.0"
    range-parser "^1.0.3"

webpack-dev-server@1.14.1:
  version "1.14.1"
  resolved "http://registry.npm.taobao.org/webpack-dev-server/download/webpack-dev-server-1.14.1.tgz#e51de228071258b0db6d55e0f5fee55eec6755de"
  dependencies:
    compression "^1.5.2"
    connect-history-api-fallback "1.1.0"
    express "^4.13.3"
    http-proxy "^1.11.2"
    optimist "~0.6.0"
    serve-index "^1.7.2"
    sockjs "^0.3.15"
    sockjs-client "^1.0.3"
    stream-cache "~0.0.1"
    strip-ansi "^3.0.0"
    supports-color "^3.1.1"
    webpack-dev-middleware "^1.4.0"

webpack@^1.13.1:
  version "1.15.0"
  resolved "http://registry.npm.taobao.org/webpack/download/webpack-1.15.0.tgz#4ff31f53db03339e55164a9d468ee0324968fe98"
  dependencies:
    acorn "^3.0.0"
    async "^1.3.0"
    clone "^1.0.2"
    enhanced-resolve "~0.9.0"
    interpret "^0.6.4"
    loader-utils "^0.2.11"
    memory-fs "~0.3.0"
    mkdirp "~0.5.0"
    node-libs-browser "^0.7.0"
    optimist "~0.6.0"
    supports-color "^3.1.0"
    tapable "~0.1.8"
    uglify-js "~2.7.3"
    watchpack "^0.2.1"
    webpack-core "~0.6.9"

websocket-driver@>=0.5.1:
  version "0.6.5"
  resolved "http://registry.npm.taobao.org/websocket-driver/download/websocket-driver-0.6.5.tgz#5cb2556ceb85f4373c6d8238aa691c8454e13a36"
  dependencies:
    websocket-extensions ">=0.1.1"

websocket-extensions@>=0.1.1:
  version "0.1.1"
  resolved "http://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.1.tgz#76899499c184b6ef754377c2dbb0cd6cb55d29e7"

weinre@^2.0.0-pre-I0Z7U9OV:
  version "2.0.0-pre-I0Z7U9OV"
  resolved "http://registry.npm.taobao.org/weinre/download/weinre-2.0.0-pre-I0Z7U9OV.tgz#fef8aa223921f7b40bbbbd4c3ed4302f6fd0a813"
  dependencies:
    express "2.5.x"
    nopt "3.0.x"
    underscore "1.7.x"

whet.extend@~0.9.9:
  version "0.9.9"
  resolved "http://registry.npm.taobao.org/whet.extend/download/whet.extend-0.9.9.tgz#f877d5bf648c97e5aa542fadc16d6a259b9c11a1"

which-module@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/which-module/download/which-module-1.0.0.tgz#bba63ca861948994ff307736089e3b96026c2a4f"

which@1, which@^1.2.9, which@~1.2.11:
  version "1.2.14"
  resolved "http://registry.npm.taobao.org/which/download/which-1.2.14.tgz#9a87c4378f03e827cecaf1acdf56c736c01c14e5"
  dependencies:
    isexe "^2.0.0"

which@^1.0.9, which@~1.0.5:
  version "1.0.9"
  resolved "http://registry.npm.taobao.org/which/download/which-1.0.9.tgz#460c1da0f810103d0321a9b633af9e575e64486f"

wide-align@^1.1.0:
  version "1.1.2"
  resolved "http://registry.npm.taobao.org/wide-align/download/wide-align-1.1.2.tgz#571e0f1b0604636ebc0dfc21b0339bbe31341710"
  dependencies:
    string-width "^1.0.2"

window-size@0.1.0:
  version "0.1.0"
  resolved "http://registry.npm.taobao.org/window-size/download/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"

window-size@^0.1.2:
  version "0.1.4"
  resolved "http://registry.npm.taobao.org/window-size/download/window-size-0.1.4.tgz#f8e1aa1ee5a53ec5bf151ffa09742a6ad7697876"

window-size@^0.2.0:
  version "0.2.0"
  resolved "http://registry.npm.taobao.org/window-size/download/window-size-0.2.0.tgz#b4315bb4214a3d7058ebeee892e13fa24d98b075"

wordwrap@0.0.2:
  version "0.0.2"
  resolved "http://registry.npm.taobao.org/wordwrap/download/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"

wordwrap@~0.0.2:
  version "0.0.3"
  resolved "http://registry.npm.taobao.org/wordwrap/download/wordwrap-0.0.3.tgz#a3d5da6cd5c0bc0008d37234bbaf1bed63059107"

wordwrap@~1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/wordwrap/download/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"

wrap-ansi@^2.0.0:
  version "2.1.0"
  resolved "http://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-2.1.0.tgz#d8fc3d284dd05794fe84973caecdd1cf824fdd85"
  dependencies:
    string-width "^1.0.1"
    strip-ansi "^3.0.1"

wrap-fn@^0.1.0:
  version "0.1.5"
  resolved "http://registry.npm.taobao.org/wrap-fn/download/wrap-fn-0.1.5.tgz#f21b6e41016ff4a7e31720dbc63a09016bdf9845"
  dependencies:
    co "3.1.0"

wrappy@1, wrappy@~1.0.2:
  version "1.0.2"
  resolved "http://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

write-file-atomic@~1.2.0:
  version "1.2.0"
  resolved "http://registry.npm.taobao.org/write-file-atomic/download/write-file-atomic-1.2.0.tgz#14c66d4e4cb3ca0565c28cf3b7a6f3e4d5938fab"
  dependencies:
    graceful-fs "^4.1.2"
    imurmurhash "^0.1.4"
    slide "^1.1.5"

write@^0.2.1:
  version "0.2.1"
  resolved "http://registry.npm.taobao.org/write/download/write-0.2.1.tgz#5fc03828e264cea3fe91455476f7a3c566cb0757"
  dependencies:
    mkdirp "^0.5.1"

ws@1.1.1:
  version "1.1.1"
  resolved "http://registry.npm.taobao.org/ws/download/ws-1.1.1.tgz#082ddb6c641e85d4bb451f03d52f06eabdb1f018"
  dependencies:
    options ">=0.0.5"
    ultron "1.0.x"

wtf-8@1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/wtf-8/download/wtf-8-1.0.0.tgz#392d8ba2d0f1c34d1ee2d630f15d0efb68e1048a"

xml-char-classes@^1.0.0:
  version "1.0.0"
  resolved "http://registry.npm.taobao.org/xml-char-classes/download/xml-char-classes-1.0.0.tgz#64657848a20ffc5df583a42ad8a277b4512bbc4d"

xmlhttprequest-ssl@1.5.3:
  version "1.5.3"
  resolved "http://registry.npm.taobao.org/xmlhttprequest-ssl/download/xmlhttprequest-ssl-1.5.3.tgz#185a888c04eca46c3e4070d99f7b49de3528992d"

"xtend@>=4.0.0 <4.1.0-0", xtend@^4.0.0, xtend@~4.0.0, xtend@~4.0.1:
  version "4.0.1"
  resolved "http://registry.npm.taobao.org/xtend/download/xtend-4.0.1.tgz#a5c6d532be656e23db820efb943a1f04998d63af"

y18n@^3.2.0, y18n@^3.2.1:
  version "3.2.1"
  resolved "http://registry.npm.taobao.org/y18n/download/y18n-3.2.1.tgz#6d15fba884c08679c0d77e88e7759e811e07fa41"

yallist@^2.1.2:
  version "2.1.2"
  resolved "http://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"

yargs-parser@^4.1.0:
  version "4.2.1"
  resolved "http://registry.npm.taobao.org/yargs-parser/download/yargs-parser-4.2.1.tgz#29cceac0dc4f03c6c87b4a9f217dd18c9f74871c"
  dependencies:
    camelcase "^3.0.0"

yargs-parser@^5.0.0:
  version "5.0.0"
  resolved "http://registry.npm.taobao.org/yargs-parser/download/yargs-parser-5.0.0.tgz#275ecf0d7ffe05c77e64e7c86e4cd94bf0e1228a"
  dependencies:
    camelcase "^3.0.0"

yargs@3.29.0:
  version "3.29.0"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-3.29.0.tgz#1aab9660eae79d8b8f675bcaeeab6ee34c2cf69c"
  dependencies:
    camelcase "^1.2.1"
    cliui "^3.0.3"
    decamelize "^1.0.0"
    os-locale "^1.4.0"
    window-size "^0.1.2"
    y18n "^3.2.0"

yargs@6.4.0:
  version "6.4.0"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-6.4.0.tgz#816e1a866d5598ccf34e5596ddce22d92da490d4"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    window-size "^0.2.0"
    y18n "^3.2.1"
    yargs-parser "^4.1.0"

yargs@^7.0.0:
  version "7.1.0"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-7.1.0.tgz#6ba318eb16961727f5d284f8ea003e8d6154d0c8"
  dependencies:
    camelcase "^3.0.0"
    cliui "^3.2.0"
    decamelize "^1.1.1"
    get-caller-file "^1.0.1"
    os-locale "^1.4.0"
    read-pkg-up "^1.0.1"
    require-directory "^2.1.1"
    require-main-filename "^1.0.1"
    set-blocking "^2.0.0"
    string-width "^1.0.2"
    which-module "^1.0.0"
    y18n "^3.2.1"
    yargs-parser "^5.0.0"

yargs@~3.10.0:
  version "3.10.0"
  resolved "http://registry.npm.taobao.org/yargs/download/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yauzl@^2.2.1:
  version "2.8.0"
  resolved "http://registry.npm.taobao.org/yauzl/download/yauzl-2.8.0.tgz#79450aff22b2a9c5a41ef54e02db907ccfbf9ee2"
  dependencies:
    buffer-crc32 "~0.2.3"
    fd-slicer "~1.0.1"

yeast@0.1.2:
  version "0.1.2"
  resolved "http://registry.npm.taobao.org/yeast/download/yeast-0.1.2.tgz#008e06d8094320c372dbc2f8ed76a0ca6c8ac419"
