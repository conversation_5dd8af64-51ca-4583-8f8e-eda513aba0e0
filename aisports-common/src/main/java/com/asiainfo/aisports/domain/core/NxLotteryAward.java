package com.asiainfo.aisports.domain.core;

import java.math.BigDecimal;
import java.util.Date;

public class NxLotteryAward {
    private Long awardId;

    private Integer awardType;

    private Integer channelType;

    private Long couponId;

    private String awardName;

    private Integer totalAmount;

    private Integer remainAmount;

    private Integer lockAmount;

    private Integer version;

    private String status;

    private String awardValue;

    private String saleCode;

    private String awardImg;

    private Date createTime;

    private Long createStaffId;

    private Date updateTime;

    private Long updateStaffId;

    private String description;

    public Long getAwardId() {
        return awardId;
    }

    public void setAwardId(Long awardId) {
        this.awardId = awardId;
    }

    public Integer getAwardType() {
        return awardType;
    }

    public void setAwardType(Integer awardType) {
        this.awardType = awardType;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Long getCouponId() {
        return couponId;
    }

    public void setCouponId(Long couponId) {
        this.couponId = couponId;
    }

    public String getAwardName() {
        return awardName;
    }

    public void setAwardName(String awardName) {
        this.awardName = awardName == null ? null : awardName.trim();
    }

    public Integer getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(Integer remainAmount) {
        this.remainAmount = remainAmount;
    }

    public Integer getLockAmount() {
        return lockAmount;
    }

    public void setLockAmount(Integer lockAmount) {
        this.lockAmount = lockAmount;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getAwardValue() {
        return awardValue;
    }

    public void setAwardValue(String awardValue) {
        this.awardValue = awardValue;
    }

    public String getAwardImg() {
        return awardImg;
    }

    public void setAwardImg(String awardImg) {
        this.awardImg = awardImg == null ? null : awardImg.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public String getSaleCode() {
        return saleCode;
    }

    public void setSaleCode(String saleCode) {
        this.saleCode = saleCode;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("NxLotteryAward{");
        sb.append("awardId=").append(awardId);
        sb.append(", awardType=").append(awardType);
        sb.append(", channelType=").append(channelType);
        sb.append(", awardName='").append(awardName).append('\'');
        sb.append(", totalAmount=").append(totalAmount);
        sb.append(", remainAmount=").append(remainAmount);
        sb.append(", lockAmount=").append(lockAmount);
        sb.append(", version=").append(version);
        sb.append(", status='").append(status).append('\'');
        sb.append(", awardValue=").append(awardValue);
        sb.append(", saleCode='").append(saleCode).append('\'');
        sb.append(", awardImg='").append(awardImg).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", createStaffId=").append(createStaffId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append(", description='").append(description).append('\'');
        sb.append('}');
        return sb.toString();
    }
}
