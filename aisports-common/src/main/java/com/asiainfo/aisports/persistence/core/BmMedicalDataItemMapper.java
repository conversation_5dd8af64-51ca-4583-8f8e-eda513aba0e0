package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.BmMedicalDataItem;
import com.asiainfo.aisports.domain.core.BmPrescriptionItem;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface BmMedicalDataItemMapper {
    @Delete({
        "delete from bm_medical_data_item",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into bm_medical_data_item (id, member_id, ",
        "item_group_code, item_code, ",
        "item_value, state, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{memberId,jdbcType=BIGINT}, ",
        "#{itemGroupCode,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, ",
        "#{itemValue,jdbcType=VARCHAR}, #{state,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(BmMedicalDataItem record);

    @Select({
        "select",
        "id, member_id, item_group_code, item_code, item_value, state, create_time, create_staff_id, ",
        "update_time, update_staff_id",
        "from bm_medical_data_item",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="member_id", property="memberId", jdbcType=JdbcType.BIGINT),
        @Result(column="item_group_code", property="itemGroupCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_code", property="itemCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_value", property="itemValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    BmMedicalDataItem selectByPrimaryKey(Long id);

    @Update({
        "update bm_medical_data_item",
        "set member_id = #{memberId,jdbcType=BIGINT},",
          "item_group_code = #{itemGroupCode,jdbcType=VARCHAR},",
          "item_code = #{itemCode,jdbcType=VARCHAR},",
          "item_value = #{itemValue,jdbcType=VARCHAR},",
          "state = #{state,jdbcType=CHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(BmMedicalDataItem record);

    @Select({
        "<script>",
        "select",
        "id, member_id, item_group_code, item_code, item_value, state, create_time, create_staff_id, update_time, update_staff_id",
        "from bm_medical_data_item",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"memberId != null\">and member_id = #{memberId,jdbcType=BIGINT}</if>",
        "<if test=\"itemGroupCode != null\">and item_group_code = #{itemGroupCode,jdbcType=VARCHAR}</if>",
        "<if test=\"itemCode != null\">and item_code = #{itemCode,jdbcType=VARCHAR}</if>",
        "<if test=\"itemValue != null\">and item_value = #{itemValue,jdbcType=VARCHAR}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="member_id", property="memberId", jdbcType=JdbcType.BIGINT),
        @Result(column="item_group_code", property="itemGroupCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_code", property="itemCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="item_value", property="itemValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<BmMedicalDataItem> selectByFields(BmMedicalDataItem param);

    @Update({
        "<script>",
        "update bm_medical_data_item",
        "<set>",
        "<if test=\"memberId != null\">member_id = #{memberId,jdbcType=BIGINT},</if>",
        "<if test=\"itemGroupCode != null\">item_group_code = #{itemGroupCode,jdbcType=VARCHAR},</if>",
        "<if test=\"itemCode != null\">item_code = #{itemCode,jdbcType=VARCHAR},</if>",
        "<if test=\"itemValue != null\">item_value = #{itemValue,jdbcType=VARCHAR},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(BmMedicalDataItem record);

    @Select({
            "SELECT",
            "id, member_id, item_group_code, item_code, item_value, state, create_time, create_staff_id, update_time, update_staff_id",
            "FROM bm_medical_data_item di, (SELECT MAX(bri.id) as item_id FROM bm_medical_data_item bri WHERE bri.member_id = #{memberId,jdbcType=BIGINT}",
            "group by bri.item_code) tem WHERE di.id =  tem.item_id"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="member_id", property="memberId", jdbcType=JdbcType.BIGINT),
            @Result(column="item_group_code", property="itemGroupCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_code", property="itemCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_value", property="itemValue", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<BmMedicalDataItem> findBmMedicalDataArchives(@Param("memberId") Long memberId);

    @Select({
            "<script>",
            "SELECT",
            "id, member_id, item_group_code, item_code, item_value, state, create_time, create_staff_id, update_time, update_staff_id",
            "FROM bm_medical_data_item ",
            "WHERE member_id = #{memberId,jdbcType=BIGINT} and item_code = #{itemCode,jdbcType=VARCHAR} and state = '1'",
            "<if test=\"itemGroupCode != null\">and item_group_code = #{itemGroupCode,jdbcType=VARCHAR}</if>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="member_id", property="memberId", jdbcType=JdbcType.BIGINT),
            @Result(column="item_group_code", property="itemGroupCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_code", property="itemCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="item_value", property="itemValue", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> bmMedicalMetricDataHis(@Param("memberId") Long memberId, @Param("itemGroupCode") String bmMedicalGroupItemCode, @Param("itemCode") String itemCode);

    @Insert({
            "<script>",
            "insert into bm_medical_data_item (id, member_id, item_group_code, item_code, item_value, state, create_time, create_staff_id, update_time, update_staff_id)",
            "values",
            "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
            "(#{item.id,jdbcType=BIGINT}, #{item.memberId,jdbcType=BIGINT}, #{item.itemGroupCode,jdbcType=VARCHAR}, #{item.itemCode,jdbcType=VARCHAR}, ",
            "#{item.itemValue,jdbcType=VARCHAR}, #{item.state,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP}, ",
            "#{item.createStaffId,jdbcType=BIGINT}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateStaffId,jdbcType=BIGINT})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(List<BmMedicalDataItem> inputList);
}