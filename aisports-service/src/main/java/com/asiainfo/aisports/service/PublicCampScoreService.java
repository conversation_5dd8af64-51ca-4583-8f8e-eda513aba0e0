package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.PublicCampScore;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.persistence.core.PublicCampScoreMapper;
import com.google.common.collect.Lists;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PublicCampScoreService {
    @Autowired
    private PublicCampScoreMapper publicCampScoreMapper;

    /**
     * 查询团队成绩
     *
     * @param campId
     * @param campItemId
     * @param teamId
     * @return
     */
    @Transactional(readOnly = true)
    public PublicCampScore findPublicCampScore(Long campId, Long campItemId, Long teamId) {
        PublicCampScore publicCampScore = new PublicCampScore();
        publicCampScore.setCampId(campId);
        publicCampScore.setCampItemId(campItemId);
        publicCampScore.setTeamId(teamId);
        publicCampScore.setState(Constants.Status.VALID);
        List<PublicCampScore> publicCampScores = publicCampScoreMapper.selectByFields(publicCampScore);
        if (publicCampScores.isEmpty()) {
            return null;
        }
        publicCampScore = publicCampScores.get(0);
        return publicCampScore;
    }

    /**
     * 查询赛事活动排行榜
     *
     * @param campId
     * @param campItemId
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> findCampScoreRank(Long campId, Long campItemId, Integer pageNum, Integer pageSize) {
        ParamMap param = new ParamMap();
        param.put("campId", campId);
        param.put("campItemId", campItemId);
        return publicCampScoreMapper.selectCampTeamScore(param, new RowBounds(pageNum, pageSize));
    }

    /**
     * 根据netUserId查询团队(个人)成绩
     * @param netUserId
     * @param campId
     * @param campItemId
     * @return
     */
    @Transactional(readOnly = true)
    public DataMap findCampTeamScore(Long netUserId, Long campId, Long campItemId) {
        ParamMap param = new ParamMap();
        param.put("campId", campId);
        param.put("campItemId", campItemId);
        param.put("netUserId", netUserId);
        return publicCampScoreMapper.selectByNetUserId(param);
    }
}
