package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class PerformNewTicket {
    private String id;

    private String name;

    private String state;

    private String performId;

    private Long ticketType;

    private Long stockId;

    private Long siteId;

    private String type;

    private Integer adultNum;

    private Integer minorNum;

    private String rule;

    private Date createTime;

    private Long createStaffId;

    private Date updateTime;

    private Long updateStaffId;

    private String teamName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getPerformId() {
        return performId;
    }

    public void setPerformId(String performId) {
        this.performId = performId == null ? null : performId.trim();
    }

    public Long getTicketType() {
        return ticketType;
    }

    public void setTicketType(Long ticketType) {
        this.ticketType = ticketType;
    }

    public Long getStockId() {
        return stockId;
    }

    public void setStockId(Long stockId) {
        this.stockId = stockId;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public Integer getAdultNum() {
        return adultNum;
    }

    public void setAdultNum(Integer adultNum) {
        this.adultNum = adultNum;
    }

    public Integer getMinorNum() {
        return minorNum;
    }

    public void setMinorNum(Integer minorNum) {
        this.minorNum = minorNum;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule == null ? null : rule.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", state=").append(state);
        sb.append(", performId=").append(performId);
        sb.append(", ticketType=").append(ticketType);
        sb.append(", stockId=").append(stockId);
        sb.append(", siteId=").append(siteId);
        sb.append(", type=").append(type);
        sb.append(", adultNum=").append(adultNum);
        sb.append(", minorNum=").append(minorNum);
        sb.append(", rule=").append(rule);
        sb.append(", createTime=").append(createTime);
        sb.append(", createStaffId=").append(createStaffId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append(", teamName=").append(teamName);
        sb.append("]");
        return sb.toString();
    }
}
