package com.asiainfo.aisports.service.app;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.AppRecommend;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.model.TicketTypePrice;
import com.asiainfo.aisports.persistence.core.AppRecommendMapper;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.TicketTypeService;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by xzeng on 2019/12/11.
 * app推荐
 */
@Service
public class AppRecommendService {
    @Autowired
    private AppRecommendMapper appRecommendMapper;
    @Autowired
    private TicketTypeService ticketTypeService;

    /**
     * 查询推荐票
     *
     * @param centerId
     * @param venueId
     * @param serviceId
     * @param appId
     * @param channelId
     * @param playProject
     * @param entityType
     * @return
     */
    @Transactional
    public List<TicketTypePrice> findRecommendTickets(Long centerId, Long venueId, Long serviceId, Long appId, Long channelId, String playProject, String entityType) {
        // 1.获取当前场馆可以销售的票
        ServiceResult serviceResult = ticketTypeService.findApiTicketList(centerId, channelId, venueId, serviceId, DateCalUtil.trim(new Date()), playProject, null, null); // 不校验是否闭馆，传-1肯定没有
        if (serviceResult.getError() != 0 || !serviceResult.containsKey("ticketTypeList")) {
            //如果查不到，明显就是没有
            return Lists.newArrayList();
        }
        List<TicketTypePrice> ticketTypePriceList = (List<TicketTypePrice>) serviceResult.get("ticketTypeList");
        Map<Long, List<TicketTypePrice>> ticketTypePriceListMap = ticketTypePriceList.stream().collect(Collectors.groupingBy(TicketTypePrice::getTicketTypeId));
        // 2.查询推荐的票
        List<TicketTypePrice> recommendList = Lists.newArrayList();
        ParamMap param = new ParamMap();
        param.put("type", Constants.AppRecommendType.MINI_APP_INDEX_RECOMMEND);
        param.put("appId", appId);
        param.put("associatedEntityType", entityType);
        param.put("state", Constants.Status.VALID);
        param.put("centerId", centerId);
        param.put("venueId", venueId);
        List<AppRecommend> recommends = appRecommendMapper.selectValidRecommends(param);
        // 3.在热门推荐中，把能卖的票一个一个加进去
        for (AppRecommend recommend : recommends) {
            if (ticketTypePriceListMap.containsKey(recommend.getAssociatedEntityId())) {
                List<TicketTypePrice> tempList = ticketTypePriceListMap.get(recommend.getAssociatedEntityId());
                if (!tempList.isEmpty()) {
                    recommendList.add(tempList.get(0));
                }
            }
            //值展示2张
            if (recommendList.size() > 2) {
                break;
            }
        }
        return recommendList;
    }
}
