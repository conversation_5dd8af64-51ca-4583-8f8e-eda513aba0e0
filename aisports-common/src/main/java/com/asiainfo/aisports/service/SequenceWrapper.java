package com.asiainfo.aisports.service;

import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.model.Sequence;
import com.asiainfo.aisports.persistence.core.SequenceMapper;
import com.google.common.base.Strings;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * Created by micha<PERSON> on 14/12/12.
 */
@Component
@Transactional(propagation = Propagation.REQUIRES_NEW)
public class SequenceWrapper {
    // 日期格式
    private static final ThreadLocal<DateFormat> dateFormat = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));

    @Autowired
    SequenceMapper sequenceMapper;

    @TargetDataSource(name = "center")
    public Long get() {
        return get(null);
    }

    private Long centerFormatSequence(Long centerId, String name) {
        String seq = get(name).toString();
        return Long.parseLong(
                new StringBuilder(centerId.toString())
                        .append(StringUtils.leftPad(seq, 8, "0"))
                        .toString()
        );
    }

    private Long dateFormatSequence(String name) {
        String seq = get(name).toString();
        return Long.parseLong(
                new StringBuilder(dateFormat.get().format(new Date()))
                        .append(StringUtils.leftPad(seq, 8, "0"))
                        .toString()
        );
    }

    private Long[] dateFormatSequence(String name, int num) {
        Long[] sequences = get(name, num);
        String date = dateFormat.get().format(new Date());

        for (int i = 0; i < sequences.length; i++) {
            sequences[i] = Long.parseLong(
                    new StringBuilder(date)
                            .append(StringUtils.leftPad(sequences[i].toString(), 8, "0"))
                            .toString()
            );
        }
        return sequences;
    }

    private Long get(String name) {
        Sequence sequence = new Sequence(name);
        sequenceMapper.getSequence(sequence);
        return sequence.getId();
    }

    private Long[] get(String name, int num) {
        Sequence sequence = new Sequence(name, num);
        sequenceMapper.getSequenceWithNum(sequence);
        Long maxId = sequence.getId();

        // 获得sequence的配置信息
        sequence = sequenceMapper.selectByPrimaryKey(sequence.getName());

        Long[] sequences = new Long[num];
        for (int i = 0; i < num; i++) {
            if (maxId - i >= sequence.getMin()) {
                sequences[num - i - 1] = maxId - i;
            } else {
                sequences[num - i - 1] = sequence.getMax() + maxId - i;
            }

        }
        return sequences;
    }

    /**
     * 获得业务流水序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tradeSequence() {
        return dateFormatSequence("trade");
    }

    /**
     * 批量获得业务流水序列
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] tradeSequence(int num) {
        return dateFormatSequence("trade", num);
    }

    /**
     * 获得票序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long ticketSequence() {
        return dateFormatSequence("ticket");
    }

    /**
     * 批量获得票序列
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] ticketSequence(int num) {
        return dateFormatSequence("ticket", num);
    }

    /**
     * 获得账本序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long depositSequence() {
        return dateFormatSequence("deposit");
    }

    /**
     * 批量获得账本序列
     *
     * @param num 序列号数量
     * @return
     */
    public Long[] depositSequence(int num) {
        return dateFormatSequence("deposit", num);
    }

    /**
     * 获得客户Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long customerSequence() {
        return dateFormatSequence("customer");
    }

    /**
     * 获得客户成员Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long custMemberSequence() {
        return dateFormatSequence("cust_member");
    }

    /**
     * 获得专项卡使用人Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long depositUserSequence() {
        return dateFormatSequence("deposit_user");
    }

    /**
     * 批量获得客户Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] customerSequence(int num) {
        return dateFormatSequence("customer", num);
    }

    /**
     * 获得账户Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long accountSequence() {
        return dateFormatSequence("account");
    }

    /**
     * 批量获得账户Id序列
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] accountSequence(int num) {
        return dateFormatSequence("account", num);
    }

    /**
     * 获得账本用户Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long userSequence() {
        return dateFormatSequence("user");
    }

    /**
     * 批量获得账本用户Id序列
     *
     * @param num 序列号数量
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] userSequence(int num) {
        return dateFormatSequence("user", num);
    }

    /**
     * 获得班级编号序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long classSequence() {
        return dateFormatSequence("class");
    }

    /**
     * 获得多个班级编号序列
     *
     * @param num 序列数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] classSequence(int num) {
        return dateFormatSequence("class", num);

    }

    /**
     * 获得学生序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long studentSequence() {
        return dateFormatSequence("student");
    }

    /**
     * 批量获得学生序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] studentSequence(int num) {
        return dateFormatSequence("student", num);
    }

    /**
     * 获得商品序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsSequence() {
        return get("goods");
    }

    /**
     * 批量获得商品序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] goodsSequence(int num) {
        return get("goods", num);
    }

    /**
     * 获得产品序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long productSequence() {
        return get("product");
    }

    /**
     * 批量获得产品序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] productSequence(int num) {
        return get("product", num);
    }

    /**
     * 获得场地序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long fieldSequence() {
        return get("field");
    }

    /**
     * 批量获得场地序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] fieldSequence(int num) {
        return get("field", num);
    }

    /**
     * 获得场地类型序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long fieldTypeSequence() {
        return get("field_type");
    }

    /**
     * 批量获得场地类型序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] fieldTypeSequence(int num) {
        return get("field_type", num);
    }

    /**
     * 获得营销活动序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long promotionSequence() {
        return get("promotion");
    }

    /**
     * 批量获得营销活动序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] promotionSequence(int num) {
        return get("promotion", num);
    }

    /**
     * 获得柜子序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long cabinetSequence() {
        return get("cabinet");
    }

    /**
     * 批量获得柜子序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] cabinetSequence(int num) {
        return get("cabinet", num);
    }

    /**
     * 获得课程序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long courseSequence() {
        return get("course");
    }

    /**
     * 批量获得课程序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] courseSequence(int num) {
        return get("course", num);
    }

    /**
     * 获得公共课序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long openCourseSequence() {
        return get("course_table");
    }

    /**
     * 批量获得公共课序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] openCourseSequence(int num) {
        return get("course_table", num);
    }

    /**
     * 获得价格序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long priceSequence() {
        return dateFormatSequence("price");
    }

    /**
     * 批量获得价格序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] priceSequence(int num) {
        return dateFormatSequence("price", num);
    }

    /**
     * 获得员工序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long staffSequence() {
        return get("staff");
    }

    /**
     * 批量获得员工序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] staffSequence(int num) {
        return get("staff", num);
    }

    /**
     * 获得会员序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long memberSequence() {
        return dateFormatSequence("member");
    }

    /**
     * 批量获得会员序列号
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] memberSequence(int num) {
        return dateFormatSequence("member", num);
    }

    /**
     * 获得费用规则序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long feeRuleSequence() {
        return get("fee_rule");
    }

    /**
     * 获得出入闸日志序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long autoGateLogSequence() {
        return dateFormatSequence("auto_gate_log");
    }

    /**
     * 获得券号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String couponNoSequence() {
        return DigestUtils.md5Hex(dateFormatSequence("coupon_no").toString()).substring(8, 24);
    }

    /**
     * 获得券号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String[] couponNoSequence(int num) {
        Long[] sequences = dateFormatSequence("coupon_no", num);
        String[] couponNos = new String[num];
        for (int i = 0; i < num; i++) {
            couponNos[i] = DigestUtils.md5Hex(sequences[i].toString() + new Date().getTime()).substring(8, 24);
        }
        return couponNos;
    }

    /**
     * 生成券Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long couponIdSequence() {
        return dateFormatSequence("coupon_id");
    }

    @TargetDataSource(name = "center")
    public Long carBindingSequence() {
        return dateFormatSequence("car_binding_id");
    }

    /**
     * 生成优惠券批次Id
     */
    @TargetDataSource(name = "center")
    public Long couponTradeIdSequence() {
        return dateFormatSequence("coupon_trade_id");
    }

    /**
     * 生成优惠券活动Id
     */
    @TargetDataSource(name = "center")
    public Long saleCampSequence() {
        return dateFormatSequence("campaign_id");
    }

    /**
     * 电子卡号(微信的电子卡,WEB端企业办卡电子卡,WEB端个人办卡电子卡)
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String ecardSequence(Long centerId) {
        Long tempCenterId = this.convertCenterId(centerId);

        Sequence sequence = new Sequence("ecard_no_" + centerId);
        sequenceMapper.getSequence(sequence);
        return "E" + Strings.padEnd(tempCenterId.toString(), 8, '0') + Strings.padStart(sequence.getId().toString(), 8, '0');
    }

    /**
     * 批量获取电子卡号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String[] batchEcardSequence(Long centerId, int num) {
        Long tempCenterId = this.convertCenterId(centerId);

        String[] stringCards = new String[num];
        Long[] cards = this.get("ecard_no_" + centerId, num);
        for (int i = 0; i < cards.length; i++) {
            stringCards[i] = "E" + Strings.padEnd(tempCenterId.toString(), 8, '0') + Strings.padStart(cards[i].toString(), 8, '0');
        }

        return stringCards;
    }

    private Long convertCenterId(Long centerId) {
        Long tempCenterId = centerId;
        // 由于初期上线时中心编码不规范, 现在硬编码进行转换
        if (centerId == 10000000) { // 五台山体育中心
            tempCenterId = 32010001L;
        } else if (centerId == 20000000) { // 镇江市体育会展中心
            tempCenterId = 32110001L;
        } else if (centerId == 30000000) { // 无锡博威体育
            tempCenterId = 32020001L;
        }
        return tempCenterId;
    }

    /**
     * 获取微信支付日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long wechatPayLogSequence() {
        return dateFormatSequence("wechat_log");
    }

    /**
     * 企业客户协议序列
     */
    @TargetDataSource(name = "center")
    public Long entAgreementSequence() {
        return dateFormatSequence("ent_agreement_id");
    }

    /**
     * 课时实例序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long lessonInstanceSequence() {
        return dateFormatSequence("lesson_instance");
    }

    /**
     * 活动id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long campaignSequence() {
        return dateFormatSequence("campaign_id");
    }

    /**
     * 活动占场冲突序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long fixedOccupySequence() {
        return dateFormatSequence("fixed_occupy");
    }

    /**
     * 员工工作时间表主键id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String staffWorkSequence() {
        return get("staff_work_id").toString();
    }

    /**
     * 教练历史价格表主键id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long coachPriceHistSequence() {
        return get("coach_price_hist_id");
    }

    /**
     * attrId 属性Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long attrIdSequence() {
        return get("attr_id");
    }

    /**
     * attrValue
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long attrValueSequece() {
        return get("attr_value");
    }

    /**
     * 网络用户序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long netUserSequence() {
        return dateFormatSequence("net_user_id");
    }

    /**
     * venueStaticParam属性param_value
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long paramValueSequence() {
        return get("param_value");
    }

    /**
     * 商品出入库日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsStockLogSequence() {
        return dateFormatSequence("goods_stock_log");
    }

    /**
     * 节假日类型的id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long holidayTypeSequence() {
        return dateFormatSequence("holiday_type");
    }

    /**
     * 物业ID的sequence
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String getPropertySequence() {
        return get("property_id").toString();
    }

    /**
     * 物业价格历史记录表ID的sequence
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String getPropertyHistSequence() {
        return get("property_hist_id").toString();
    }

    /**
     * 获取异常id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long crashLogSequence() {
        return dateFormatSequence("crash_log");
    }

    /**
     * 外屏广告Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long externalDisplayAd() {
        return get("external_display_ad");
    }

    /**
     * 获取任务序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String taskSequence() {
        return dateFormatSequence("task").toString();
    }

    /**
     * 获取自定义费用序号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long customFeeitemSequence() {
        return get("custom_feeitem_id");
    }

    /**
     * 提醒规则序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String reminderRuleSequence() {
        return get("reminder_rule_id").toString();
    }

    @TargetDataSource(name = "center")
    public String roleIdSequence() {
        return get("role_id").toString();
    }

    /**
     * smsSend序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long smsSendSequence() {
        return dateFormatSequence("sms_send_log_id");
    }

    /**
     * smsAcctChangeId
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long smsAcctChangeIdSequence() {
        return get("sms_acct_change_id");
    }

    /**
     * 第三方支付回调记录Log表中的Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long thirdPayNotifyId() {
        return get("third_pay_notify_id");
    }

    /**
     * 商品skuId
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String skuIdSequence() {
        return get("sku_id").toString();
    }

    /**
     * 商品价格Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsPriceIdSequence() {
        return get("goods_price_id");
    }

    @TargetDataSource(name = "center")
    public Long shopIdSequence() {
        return get("shop_id");
    }

    /**
     * 库存盘点ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsStockCheckId() {
        return get("goods_stock_check_id");
    }

    /**
     * 库存盘点明细ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsStockCheckDetailId() {
        return get("stock_check_detail_id");
    }

    @TargetDataSource(name = "center")
    public Long supplierId() {
        return get("supplier_id");
    }

    /**
     * 赛事报名序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long campEnrollSequence() {
        return dateFormatSequence("common_enroll");
    }

    /**
     * 赛事活动项目分组Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long campItemGroupSequence() {
        return get("camp_item_group_id");
    }

    /**
     * 赛事活动项目Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long campItemSequence() {
        return get("camp_item_id");
    }

    /**
     * 比赛相关的id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long matchSequence() {
        return dateFormatSequence("match");
    }

    /**
     * 获取sysInvoiceLogeId
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long sysInvoiceLogSequence() {
        return get("sys_invoice_log_id");
    }

    /**
     * 获取ticketTypeTimeId
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long ticketTypeTimeSequence() {
        return get("ticket_type_time");
    }

    @TargetDataSource(name = "center")
    public Long coopMerchantSequence() {
        return get("coop_merchant");
    }

    @TargetDataSource(name = "center")
    public Long siteSequence() {
        return get("site");
    }

    @TargetDataSource(name = "center")
    public Long goodsSpecSequence() {
        return get("goods_spec");
    }

    @TargetDataSource(name = "center")
    public Long journalIdSequence() {
        return dateFormatSequence("journal_id");
    }

    @TargetDataSource(name = "center")
    public Long couponChangeSequence() {
        return dateFormatSequence("coupon_change");
    }

    @TargetDataSource(name = "center")
    public Long[] couponChangeSequence(int num) {
        return dateFormatSequence("coupon_change", num);
    }

    /**
     * 次课教练预约的课时序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long courseHourSequence() {
        return get("course_hour_id");
    }

    /**
     * 获得商品条形码序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsSkuBarcodeSequence() {
        return dateFormatSequence("goods_sku_barcode");
    }

    /**
     * 学生等级生成序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long studentLevelSequence() {
        return dateFormatSequence("student_level");
    }

    /**
     * 培训教练评价序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long trainingReviewSequence() {
        return dateFormatSequence("training_reviews");
    }

    /**
     * 设备序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long deviceSequence() {
        return get("device");
    }

    /**
     * 发票申请序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long invoiceApplySequence() {
        return dateFormatSequence("invoice_apply");
    }

    /**
     * 发票申请序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long invoiceApplyDetailSequence() {
        return dateFormatSequence("invoice_apply_detail");
    }

    /**
     * 第三方支付账单序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long thirdPayBillSequence() {
        return dateFormatSequence("third_pay_bill");
    }

    /**
     * 第三方支付账单日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long thirdPayBillLogSequence() {
        return dateFormatSequence("third_pay_bill_log");
    }

    /**
     * 角色对应功能ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long roleMenuFunctionSequence() {
        return get("role_menu_function");
    }


    /**
     * 预约序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long salesSequence() {
        return get("sales");
    }

    /**
     * 票务购物车Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long performShoppingCartSequece() {
        return get("perform_shopping_cart");
    }

    /**
     * 操作日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long operationLogSequence() {
        return dateFormatSequence("operation_log");
    }

    /**
     * 操作日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] operationLogSequence(int num) {
        return dateFormatSequence("operation_log", num);
    }

    /**
     * 赛事项目序列
     */
    @TargetDataSource(name = "center")
    public String projectSequence() {
        return get("project").toString();
    }

    /**
     * 成绩序列
     *
     * @return
     */
    public Long scoreSequence() {
        return get("score");
    }

    /**
     * 赛道赛点相关序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long trackSequence() {
        return get("racing_track");
    }

    /**
     * 人脸识别的sequence,与配置的prefix有关
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String faceRecognitionSequence() {
        return dateFormatSequence("face_recognition").toString();
    }

    /**
     * 体测档案序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmMemberSequence() {
        return get("bm_member");
    }

    /**
     * 体测预约序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmApptSequence() {
        return dateFormatSequence("bm_appt");
    }

    /**
     * 体测站点序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmSiteSequence() {
        return dateFormatSequence("bm_site");
    }

    @TargetDataSource(name = "center")
    public Long bmAppointBlackListSequence() {
        return dateFormatSequence("bm_appoint_black_list");
    }

    @TargetDataSource(name = "center")
    public Long bmAppointBlackListRuleSequence() {
        return dateFormatSequence("bm_appoint_black_list_rule");
    }

    @TargetDataSource(name = "center")
    public Long bmSiteAppointDateSequence() {
        return dateFormatSequence("bm_site_appoint_date");
    }

    @TargetDataSource(name = "center")
    public Long bmSiteAppointTimeSequence() {
        return dateFormatSequence("bm_site_appoint_time");
    }

    @TargetDataSource(name = "center")
    public Long bmSiteAppointSequence() {
        return dateFormatSequence("bm_site_appoint");
    }

    @TargetDataSource(name = "center")
    public Long bmSiteAppointUserSequence() {
        return dateFormatSequence("bm_site_appoint_user");
    }

    /**
     * 体测报告序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public String acmewayReportSequence() {
        return get("acmeway_report").toString();
    }

    /**
     * 工作组分配人员ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long assignIdSequence() {
        return get("assign_id");
    }

    /**
     * 工作组序列
     *
     * @param centerId
     * @return
     */
    @TargetDataSource(name = "center")
    public Long workGroupSequence(Long centerId) {
        return centerFormatSequence(centerId, "work_group");
    }

    /**
     * pos机订单支付日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long posTradeLogSequence() {
        return dateFormatSequence("pos_trade_log");
    }

    /**
     * 获得退票规则的序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long refundRuleSequenceId() {
        return dateFormatSequence("ticket_refund_rule_id");
    }

    /**
     * 文件ID
     * 为了防止服务器文件重名
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long fileIdSequence() {
        return dateFormatSequence("file_id");
    }

    @TargetDataSource(name = "center")
    public Long cmCustCollectIdSequence() {
        return get("cm_cust_collect_id");
    }

    /**
     * 批量上传ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long batchTaskIdSequence() {
        return get("batch_task_id");
    }

    /**
     * 获得上课地点的序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcPlaceSequenceId() {
        return get("tc_place_id");
    }

    @TargetDataSource(name = "center")
    public Long tcRoomSequenceId() {
        return get("tc_room");
    }

    /**
     * 课程预约序列或
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLessonResvSequence() {
        return dateFormatSequence("tc_lesson_resv");
    }

    /**
     * 服务项预约序列或
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long serviceResvSequence() {
        return dateFormatSequence("service_resv");
    }

    /**
     * 培训课统一sequence
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLessonSequence() {
        return get("tc_lesson");
    }

    @TargetDataSource(name = "center")
    public Long[] tcLessonSequence(int num) {
        return get("tc_lesson", num);
    }

    /**
     * 获得上课时间的序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcTimeSequenceId() {
        return get("tc_time_id");
    }

    /**
     * 获得跟进记录主键
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long todoListSequenceId() {
        return get("todo_list_id");
    }

    /**
     * 获得黑名单主键
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long blacklistSequenceId() {
        return get("blacklist_id");
    }

    /**
     * 课程执行sequence
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLessonExecSequence() {
        return get("tc_lesson_exec");
    }

    /**
     * 课程签到sequence
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLessonAttnSequence() {
        return get("tc_lesson_attn");
    }

    @TargetDataSource(name = "center")
    public Long[] tcLessonAttnSequence(int num) {
        return get("tc_lesson_attn", num);
    }

    /**
     * 获得学校的序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long schoolSequenceId() {
        return get("school_id");
    }

    @TargetDataSource(name = "center")
    public Long schoolClassSequenceId() {
        return dateFormatSequence("school_class_id");
    }

    /**
     * 获得科目的序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcSubjectSequence() {
        return get("tc_subject_id");
    }

    /**
     * 获得外屏设备的序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long externalDisplayDeviceSequence() {
        return get("external_display_d_id");
    }

    /**
     * 获得启迪课程价格序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLessonPriceSequence() {
        return get("price_id");
    }

    /**
     * 获得启迪课程价格详情序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLessonPriceItemSequence() {
        return get("price_item_id");
    }

    /**
     * 订金序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long downPaymentSequence() {
        return dateFormatSequence("down_payment");
    }

    /**
     * 获取闸机序列
     */
    @TargetDataSource(name = "center")
    public Long autoGateSequence() {
        return get("auto_gate");
    }

    /**
     * ali推送序列
     */
    @TargetDataSource(name = "center")
    public Long aliPushSequence() {
        return get("ali_push");
    }

    /**
     * 员工管理卡序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long staffCardSequence() {
        return get("staff_card");
    }

    /**
     * 协议占场序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long occupySequence() {
        return dateFormatSequence("cycle_occupy");
    }

    /**
     * 会员等级日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long memberGradeLogSequence() {
        return dateFormatSequence("member_grade_log");
    }

    /**
     * 级别序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long gradeIdSequence() {
        return get("grade_id");
    }

    /**
     * 商品实例序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsEntitySequence() {
        return get("goods_entity");
    }

    /**
     * 商品属性序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsAttrSequence() {
        return get("goods_attr");
    }

    /**
     * 商品类型属性序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long goodsTypeAttrSequence() {
        return get("goods_type_attr");
    }

    /**
     * 获取客户人像id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long custFaceSequence() {
        return get("cust_face");
    }

    /**
     * 退卡规则
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long backcardRuleSequence() {
        return get("backcard_rule");
    }

    /**
     * 下载任务Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long downloadTaskSequence() {
        return dateFormatSequence("download_task_id");
    }

    /**
     * 限制名单Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long limitListSequence() {
        return get("limit_list_id");
    }


    @TargetDataSource(name = "center")
    public Long[] limitListSequence(int num) {
        return get("limit_list_id", num);
    }

    /**
     * 黑名单规则ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long blackRuleSequence() {
        return get("black_list_rule");
    }

    /**
     * 限制名单批次Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long limitListBatchSequence() {
        return get("limit_list_batch_id");
    }

    @TargetDataSource(name = "center")
    public Long coachSequence() {
        return get("coach");
    }

    @TargetDataSource(name = "center")
    public String electronicInvoiceLogSequence() {
        return "YXT" + dateFormatSequence("elec_invoice_id") + "0";
    }

    /**
     * 定时任务日志Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long scheduleJobLogSequence() {
        return dateFormatSequence("schedule_job_log_id");
    }

    @TargetDataSource(name = "center")
    public Long auditId() {
        return dateFormatSequence("audit_id");
    }

    @TargetDataSource(name = "center")
    public Long auditTaskId() {
        return dateFormatSequence("audit_task_id");
    }

    /**
     * app账单序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long appBillFileSequence() {
        return dateFormatSequence("app_bill_file");
    }

    /**
     * app账单日志序列
     */
    @TargetDataSource(name = "center")
    public Long appBillFileLogSequence() {
        return dateFormatSequence("app_bill_file_log");
    }

    /**
     * 部门sequence
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long deptId() {
        return get("dept_id");
    }

    /**
     * 公司sequence
     */
    @TargetDataSource(name = "center")
    public Long enterpriseId() {
        return get("enterprise_id");
    }

    /**
     * 仓库sequence
     */
    @TargetDataSource(name = "center")
    public Long warehouseId() {
        return get("warehouse_id");
    }

    /**
     * 仓库员工关系sequence
     */
    @TargetDataSource(name = "center")
    public Long warehouseStaffId() {
        return get("warehouse_staff_id");
    }

    /**
     * 协议sequence
     */
    @TargetDataSource(name = "center")
    public Long agreementId() {
        return get("agreement_id");
    }

    /**
     * 期次sequence
     */
    @TargetDataSource(name = "center")
    public Long termId() {
        return get("term_id");
    }

    /**
     * 签到sequence
     */
    @TargetDataSource(name = "center")
    public Long classAttendLogSequence() {
        return dateFormatSequence("class_attend_log");
    }

    /**
     * 课程报名sequence
     */
    @TargetDataSource(name = "center")
    public Long courseEnrollSequence() {
        return dateFormatSequence("course_enroll");
    }

    /**
     * 课时报名sequence
     */
    @TargetDataSource(name = "center")
    public Long courseEnrollDaysSequence() {
        return dateFormatSequence("course_enroll_days");
    }

    /**
     * 短信账户提醒sequence
     */
    @TargetDataSource(name = "center")
    public Long smsAcctWarningSequence() {
        return dateFormatSequence("sms_acct_warning");
    }

    /**
     * 客户提醒sequence
     */
    @TargetDataSource(name = "center")
    public Long custReminderSequence() {
        return dateFormatSequence("cust_reminder");
    }

    /**
     * 密码变更sequence
     */
    @TargetDataSource(name = "center")
    public Long passwordChangeSequence() {
        return dateFormatSequence("password_change");
    }

    /**
     * 入馆详情sequence
     */
    @TargetDataSource(name = "center")
    public Long entryInfoDetailIdSequence() {
        return dateFormatSequence("entry_info_detail_id");
    }

    /**
     * 教练预约sequence
     */
    @TargetDataSource(name = "center")
    public Long coachAppointmentSequence() {
        return dateFormatSequence("coach_appointment");
    }

    /**
     * 挂账结算支付日志sequence
     */
    @TargetDataSource(name = "center")
    public Long tradePayOncreditLogSequence() {
        return dateFormatSequence("trade_pay_oncredit_log");
    }

    /**
     * 房产物业sequence
     */
    @TargetDataSource(name = "center")
    public Long housePropertySequence() {
        return dateFormatSequence("house_property");
    }

    /**
     * 打印sequence
     */
    @TargetDataSource(name = "center")
    public Long printSequence() {
        return dateFormatSequence("print");
    }

    /**
     * 课程优惠sequence
     */
    @TargetDataSource(name = "center")
    public Long trainingPromId() {
        return get("training_prom_id");
    }

    /**
     * 商户协议sequence
     */
    @TargetDataSource(name = "center")
    public Long merchantAgreementSequence() {
        return dateFormatSequence("merchant_agreement_id");
    }

    /**
     * 商户sequence
     */
    @TargetDataSource(name = "center")
    public Long merchantId() {
        return get("merchant_id");
    }

    /**
     * 产品充值规则sequence
     */
    @TargetDataSource(name = "center")
    public Long productRechargeRuleSequence() {
        return dateFormatSequence("product_recharge_rule");
    }

    /**
     * 场馆位置sequence
     */
    @TargetDataSource(name = "center")
    public Long venueLocationSequence() {
        return dateFormatSequence("venue_location");
    }

    /**
     * 合作商家账单sequence
     */
    @TargetDataSource(name = "center")
    public Long coopMerchantBillSequence() {
        return dateFormatSequence("coop_merchant_bill");
    }

    /**
     * 票优惠sequence
     */
    @TargetDataSource(name = "center")
    public Long ticketPromId() {
        return get("ticket_prom_id");
    }

    /**
     * 场馆媒体资源sequence
     */
    @TargetDataSource(name = "center")
    public Long venueMediaResourceSequence() {
        return dateFormatSequence("venue_media_resource");
    }

    /**
     * 产品渠道sequence
     */
    @TargetDataSource(name = "center")
    public Long productChannelSequence() {
        return dateFormatSequence("product_channel");
    }

    /**
     * 短信通知设置sequence
     */
    @TargetDataSource(name = "center")
    public Long smsNoticeSettingsSequence() {
        return dateFormatSequence("sms_notice_settings");
    }

    /**
     * 短信账户sequence
     */
    @TargetDataSource(name = "center")
    public Long smsAcctSequence() {
        return dateFormatSequence("sms_acct");
    }

    /**
     * 账本关系sequence
     */
    @TargetDataSource(name = "center")
    public Long depositRelationSequence() {
        return dateFormatSequence("deposit_relation");
    }

    /**
     * 场馆协议sequence
     */
    @TargetDataSource(name = "center")
    public Long venueAgreementSequence() {
        return dateFormatSequence("venue_agreement");
    }

    /**
     * 批量业务sequence
     */
    @TargetDataSource(name = "center")
    public Long batchSequence() {
        return dateFormatSequence("batch");
    }

    /**
     * 访客sequence
     */
    @TargetDataSource(name = "center")
    public Long visitorSequence() {
        return dateFormatSequence("visitor");
    }

    /**
     * 客户反馈sequence
     */
    @TargetDataSource(name = "center")
    public Long custFeedbackSequence() {
        return dateFormatSequence("cust_feedback");
    }

    /**
     * 预约sequence
     */
    @TargetDataSource(name = "center")
    public Long reserveSequence() {
        return dateFormatSequence("reserve");
    }

    /**
     * 通用日志sequence
     */
    @TargetDataSource(name = "center")
    public Long commonLogSequence() {
        return dateFormatSequence("common_log");
    }

    /**
     * app广告sequence
     */
    @TargetDataSource(name = "center")
    public Long appAdSequence() {
        return get("app_ad");
    }

    /**
     * 推送sequence
     */
    @TargetDataSource(name = "center")
    public Long pushSequence() {
        return dateFormatSequence("push");
    }

    /**
     * 获得押金序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tradeForegiftSequence() {
        return dateFormatSequence("trade_foregift");
    }

    /**
     * 水电费序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long venueCostEntry() {
        return dateFormatSequence("venue_costentry");
    }

    /**
     * 账本明细序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long depositDetailId() {
        return dateFormatSequence("deposit_detail_id");
    }

    /**
     * 公司id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long companySequence() {
        return get("company");
    }

    /**
     * 租户id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tenantSequence() {
        return get("tenant");
    }

    /**
     * 租户服务
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tenantServiceSequence() {
        return get("tenant_service");
    }

    /**
     * 租户菜单包序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tenantSubscriptionsSequence() {
        return get("tenant_subscriptions");
    }

    /**
     * 菜单布局元素序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long menuElementSequence() {
        return get("menu_element");
    }

    @TargetDataSource(name = "center")
    public Long[] menuElementSequence(int num) {
        return get("menu_element", num);
    }

    /**
     * 票使用人Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tradeTicketUserSequence() {
        return dateFormatSequence("trade_ticket_user");
    }

    /**
     * * 积分异动元素序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long pointsChangeSequence() {
        return dateFormatSequence("points_change");
    }

    /**
     * 积分异动与积分明细关系元素序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long pointsChangeDetailSequence() {
        return dateFormatSequence("points_change_detail");
    }

    /**
     * 客户积分明细序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long pointsAccountDetailSequence() {
        return dateFormatSequence("points_account_detail");
    }

    /**
     * 积分获取日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long pointsGetLogSequence() {
        return dateFormatSequence("points_get_log");
    }

    /**
     * 积分支付日志元素序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tradePointsPayLogSequence() {
        return dateFormatSequence("trade_points_pay_log");
    }

    /**
     * 积分账户元素序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long poAccountSequence() {
        return dateFormatSequence("po_account");
    }

    /**
     * 摄像头设备Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long cameraDeviceId() {
        return get("camera_device_id");
    }

    /**
     * 客流上报Id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long passengerFlowId() {
        return get("passenger_flow_id");
    }

    @TargetDataSource(name = "center")
    public Long[] cameraDeviceId(int num) {
        return get("camera_device_id", num);
    }

    /**
     * 网络用户与阿里会员关系日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long aliuidNetuserLogSequence() {
        return dateFormatSequence("aliuid_netuser_log");
    }

    /**
     * 对外接口日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long openApiOutLogSequence() {
        return dateFormatSequence("open_api_out_log");
    }

    /**
     * 自助机设备Id序列
     *
     * @return
     */
    public Long atmDeviceId() {
        return get("atm_device_id");
    }

    /**
     * 应用Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long appIdSequence() {
        return get("app_id");
    }

    /**
     * 菜单Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long menuIdSequence() {
        return get("menu_id");
    }

    /**
     * 微信账户Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long wechatAccountIdSequence() {
        return get("wechat_account_id");
    }

    /**
     * 票使用运动娱乐项目信息序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tradeTicketPlaySequence() {
        return dateFormatSequence("trade_ticket_play");
    }

    /**
     * 大屏设备的序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long largeDisplayDeviceId() {
        return get("large_display_device_id");
    }

    /**
     * 定时任务Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long scheduleJobIdSequence() {
        return get("schedule_job_id");
    }

    @TargetDataSource(name = "center")
    public Long productTypeSequence() {
        return get("product_type_id");
    }

    @TargetDataSource(name = "center")
    public Long[] productTypeSequence(int num) {
        return get("product_type_id", num);
    }

    /**
     * 外部会员与cust对应关系id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long outUserCustRelationSequence() {
        return get("out_user_cust_relation");
    }

    /**
     * 球员id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long gamePlayerSequence() {
        return dateFormatSequence("game_player");
    }

    /**
     * 球队id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long gameTeamSequence() {
        return dateFormatSequence("game_team");
    }

    /**
     * 队员id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long gameTeamMemberSequence() {
        return dateFormatSequence("game_team_member");
    }

    /**
     * 约赛id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long gameSequence() {
        return dateFormatSequence("game");
    }

    /**
     * 约赛比赛队伍id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long gamePlayingTeamSequence() {
        return dateFormatSequence("game_playing_team");
    }

    @TargetDataSource(name = "center")
    public Long venueCloseDayId() {
        return dateFormatSequence("venue_close_day");
    }

    /**
     * 销售线索序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long salesLeadsSequence() {
        return dateFormatSequence("sales_leads");
    }

    /**
     * 教案序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcPlanSequence() {
        return dateFormatSequence("tc_plan");
    }

    /**
     * 娱乐项目序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long playProjectSequence() {
        return dateFormatSequence("play_project");
    }

    /**
     * 教案步骤序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcPlanStepSequence() {
        return dateFormatSequence("tc_plan_step");
    }

    /**
     * 教案步骤项目序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcPlanStepItemSequence() {
        return dateFormatSequence("tc_plan_step_item");
    }

    /**
     * 积分获取规则序列
     * <p>
     * * @return
     */
    @TargetDataSource(name = "center")
    public Long poGetRuleSequence() {
        return dateFormatSequence("po_get_rule");
    }

    /**
     * 获得教师的序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long teacherSequenceId() {
        return get("teacher_id");
    }

    /**
     * 课程分类的序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long courseTypeSequenceId() {
        return dateFormatSequence("course_type");
    }

    @TargetDataSource(name = "center")
    public Long venuePayModeId() {
        return get("venue_pay_mode");
    }

    /**
     * 课程请假记录
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLeaveSequence() {
        return dateFormatSequence("tc_leave");
    }

    /**
     * 课程请假申请
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLeaveApplySequence() {
        return dateFormatSequence("tc_leave_apply");
    }

    /**
     * 发票信息序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long invoiceInfoSequence() {
        return dateFormatSequence("invoice_info");
    }

    /**
     * 课程变动序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tradeCourseChange() {
        return get("trade_course_change");
    }

    /**
     * 公司序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long companyIdSequence() {
        return get("company_id");
    }

    /**
     * 发票信息序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] invoicePrintRelationSequence(int num) {
        return dateFormatSequence("invoice_print_relation", num);
    }

    /**
     * 公司角色对应关系Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long companyRoleId() {
        return get("company_role_id");
    }

    @TargetDataSource(name = "center")
    public Long[] companyRoleId(int num) {
        return get("company_role_id", num);
    }

    @TargetDataSource(name = "center")
    public Long appAdTypeRelationId() {
        return get("app_ad_type_relation");
    }

    @TargetDataSource(name = "center")
    public Long reportComId() {
        return get("report_com_id");
    }

    @TargetDataSource(name = "center")
    public Long reportComAttrId() {
        return get("report_com_attr_id");
    }

    @TargetDataSource(name = "center")
    public Long reportDefId() {
        return get("report_def_id");
    }

    @TargetDataSource(name = "center")
    public Long reportId() {
        return get("report_id");
    }

    /**
     * 卡回收序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long cardRecycleSequence() {
        return dateFormatSequence("card_recycle");
    }

    /**
     * 卡回收明细序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long cardRecycleDetailSequence() {
        return dateFormatSequence("card_recycle_detail");
    }

    /**
     * 掌静脉用户序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long palmPersonSequence() {
        return dateFormatSequence("palm_person");
    }

    /**
     * 掌静脉用户分组序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long palmSetPersonSequence() {
        return dateFormatSequence("palm_set_person");
    }

    /**
     * 课程级别生成序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long tcLevelSequence() {
        return get("tc_level");
    }

    @TargetDataSource(name = "center")
    public Long palmPersonBinding() {
        return dateFormatSequence("palm_person_binding");
    }

    @TargetDataSource(name = "center")
    public Long lockerSetId() {
        return get("locker_set");
    }

    @TargetDataSource(name = "center")
    public Long lockerId() {
        return get("locker");
    }

    /**
     * 柜子开关柜日志id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long lockerLogId() {
        return dateFormatSequence("locker_log_id");
    }

    @TargetDataSource(name = "center")
    public Long couponDrawRuleId() {
        return get("coupon_draw_rule_id");
    }

    @TargetDataSource(name = "center")
    public Long tenantFile() {
        return get("tenant_file");
    }

    @TargetDataSource(name = "center")
    public Long faceSetPerson() {
        return get("face_set_person");
    }

    /**
     * 小程序模板Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long miniAppTemplateIdSequence() {
        return get("mini_app_template_id");
    }

    /**
     * 小程序模板发布Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long miniAppTemplateReleaseIdSequence() {
        return get("template_release_id");
    }

    /**
     * 小程序发布Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long miniAppReleaseIdSequence() {
        return get("mini_app_release_id");
    }

    @TargetDataSource(name = "center")
    public Long tcTermCourseId() {
        return get("tc_term_course_id");
    }

    @TargetDataSource(name = "center")
    public Long fieldTimingId() {
        return dateFormatSequence("field_timing_id");
    }

    @TargetDataSource(name = "center")
    public Long fieldTimingLogId() {
        return dateFormatSequence("field_timing_log_id");
    }

    /**
     * 体测报告序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmReportSequence() {
        return get("bm_report");
    }

    /**
     * 体测报告项目表
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmReportItemSequence() {
        return get("bm_report_item");
    }

    @TargetDataSource(name = "center")
    public Long lockerIntfCmd() {
        return get("locker_intf_cmd");
    }

    /**
     * 常用购票人序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long commonPersonIdSequence() {
        return get("common_person_id");
    }

    /**
     * 团购票id序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long groupBuyingTicketIdSequence() {
        return dateFormatSequence("group_buying_ticket_id");
    }

    @TargetDataSource(name = "center")
    public Long poExchangeProductSequence() {
        return dateFormatSequence("po_exchange_product");
    }

    /**
     * 请求日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long intfRequestLog() {
        return dateFormatSequence("intf_request_log");
    }

    /**
     * 小程序员工序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long miniAppStaff() {
        return get("mini_app_staff");
    }

    /**
     * 体测报告附件
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmReportAttachmentSequence() {
        return dateFormatSequence("bm_report_attachment");
    }

    /**
     * 开放平台商铺
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long openPlatformShopSequence() {
        return dateFormatSequence("open_platform_shop");
    }

    /**
     * 设备子项目
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long deviceItemSequence() {
        return get("device_item");
    }

    /**
     * 批量获得设备子项目序列
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] deviceItemSequence(int num) {
        return get("device_item", num);
    }

    /**
     * 获得自助售货机日志序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long vmGoodsStockLogSequence() {
        return get("vm_goods_stock_log");
    }

    /**
     * 日收入统计
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long staDayIncomeSequence() {
        return dateFormatSequence("sta_day_income");
    }

    /**
     * 柜子租用id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long lockerRentIdSequence() {
        return dateFormatSequence("locker_rent_id");
    }

    /**
     * 柜子租用支付id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long lockerRentPayIdSequence() {
        return dateFormatSequence("locker_rent_pay_id");
    }

    /**
     * 社团活动id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long clubActivityIdSequence() {
        return dateFormatSequence("club_activity_id");
    }

    /**
     * 社团活动票id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long clubActivityTicketIdSequence() {
        return dateFormatSequence("club_activity_ticket_id");
    }

    @TargetDataSource(name = "center")
    public Long roomStateSequence() {
        return dateFormatSequence("room_state");
    }

    @TargetDataSource(name = "center")
    public Long roomStateDetailSequence() {
        return dateFormatSequence("room_state_detail");
    }

    public String openUserId() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 柜子租用支付id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long agreementLogIdSequence() {
        return dateFormatSequence("agreement_log");
    }

    /**
     * 柜子租用支付id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long agreementLogAttachmentSequence() {
        return dateFormatSequence("agreement_log_attachment");
    }


    /**
     * 活动申报
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opActivitySequence() {
        return dateFormatSequence("op_activity");
    }

    /**
     * 活动申报附件
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opActivityAttachmentSequence() {
        return get("op_activity_attachment");
    }

    @TargetDataSource(name = "center")
    public Long cameraFlowRecordId() {
        return dateFormatSequence("camera_flow_record");
    }

    @TargetDataSource(name = "center")
    public Long[] cameraFlowRecordId(int num) {
        return dateFormatSequence("camera_flow_record", num);
    }

    /**
     * 短链接Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long shortUrlIdSequence() {
        return dateFormatSequence("short_url");
    }

    @TargetDataSource(name = "center")
    public Long woOrderIdSequence() {
        return dateFormatSequence("wo_order_id");
    }

    @TargetDataSource(name = "center")
    public Long opScoreSubjectId() {
        return get("op_score_subject_id");
    }

    /**
     * 运营评分周期
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opScoreSubjectCycleId() {
        return get("op_score_subject_cycle_id");
    }

    /**
     * 运营评分项
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opScoreSubjectItemId() {
        return get("op_score_subject_item_id");
    }

    /**
     * 运营项目
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opProjectSequence() {
        return dateFormatSequence("op_project");
    }

    /**
     * 运营项目资源
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opProjectResourceSequence() {
        return get("op_project_resource");
    }

    /**
     * 运营评分详情
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opScoreDetailSequence() {
        return dateFormatSequence("op_score_detail");
    }

    @TargetDataSource(name = "center")
    public Long mailSendSequence() {
        return dateFormatSequence("mail_send_history_id");
    }

    /**
     * 评价序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long commentSequence() {
        return dateFormatSequence("comment_id");
    }

    /**
     * 评价序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long favoriteSequence() {
        return dateFormatSequence("favorite_id");
    }

    /**
     * 运营评分表
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opScoreSequence() {
        return get("op_score");
    }

    @TargetDataSource(name = "center")
    public Long goodsRentDetailSequence() {
        return dateFormatSequence("goods_rent_detail");
    }

    /**
     * app推荐
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long appRecommendSequence() {
        return dateFormatSequence("app_recommend");
    }

    @TargetDataSource(name = "center")
    public Long quQueueDefSequence() {
        return dateFormatSequence("qu_queue_def");
    }

    /**
     * 队伍取号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long quQueueNumSequence() {
        return get("qu_queue_num_id");
    }

    @TargetDataSource(name = "center")
    public Long quQueueSequence() {
        return dateFormatSequence("qu_queue");
    }

    /**
     * 统计组客流记录
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long cameraFlowCountId() {
        return dateFormatSequence("camera_flow_count");
    }

    /**
     * 协会id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long associationId() {
        return dateFormatSequence("association_id");
    }

    /**
     * 台账id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long standingBookId() {
        return dateFormatSequence("standing_book");
    }

    /**
     * 运营报告ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opReportId() {
        return dateFormatSequence("op_report");
    }

    /**
     * 运营阅读ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long opReadId() {
        return dateFormatSequence("op_read");
    }

    /**
     * 会籍变更历史
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long memberConsultantId() {
        return dateFormatSequence("member_consultant");
    }

    @TargetDataSource(name = "center")
    public Long batchGrantId() {
        return dateFormatSequence("batch_grant");
    }

    /**
     * 手环绑定日志
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long keyBindLogId() {
        return get("key_bind_log_id");
    }


    /**
     * 表单元素id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long formElementId() {
        return dateFormatSequence("form_element");
    }

    /**
     * 表单实例
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long formInstanceId() {
        return get("form_instance_id");
    }

    @TargetDataSource(name = "center")
    public Long buyPresentId() {
        return get("buy_present");
    }

    /**
     * 预约日历
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long reservationCalendarSequence() {
        return get("reservation_calendar_id");
    }

    /**
     * 预约活动订单
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long reservationOrderSequence() {
        return get("reservation_order_id");
    }

    /**
     * 营销活动参与表序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long saleCampOrderSequence() {
        return dateFormatSequence("sale_camp_order_id");
    }

    /**
     * 包场序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long roomOccupySequence() {
        return dateFormatSequence("room_occupy");
    }

    /**
     * 包场明细序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long roomOccupyItemSequence() {
        return dateFormatSequence("room_occupy_item");
    }

    /**
     * 包场日期序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long roomOccupyDetailSequence() {
        return dateFormatSequence("room_occupy_detail");
    }

    /**
     * 包场冲突序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long roomOccupyConflictSequence() {
        return dateFormatSequence("room_occupy_conflict");
    }

    @TargetDataSource(name = "center")
    public Long couponEntityRelationSequence() {
        return get("coupon_entity_relation");
    }

    @TargetDataSource(name = "center")
    public Long chargeFeeSequence() {
        return dateFormatSequence("charge_fee");
    }

    /**
     * 活动成绩序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long campScoreSequence() {
        return dateFormatSequence("camp_score_id");
    }

    /**
     * 教练级别
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long coachLevelId() {
        return get("coach_level_id");
    }

    /**
     * 课时费规则
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long classFeeId() {
        return get("class_fee_id");
    }

    /**
     * 上课课时费
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long classAttnFeeId() {
        return dateFormatSequence("class_attn_fee_id");
    }

    @TargetDataSource(name = "center")
    public Long auditConfigId() {
        return get("audit_config_id");
    }

    @TargetDataSource(name = "center")
    public Long auditConfigProcessId() {
        return get("audit_config_process");
    }

    @TargetDataSource(name = "center")
    public Long microlectureId() {
        return get("microlecture_id");
    }

    @TargetDataSource(name = "center")
    public Long appNotificationHistoryId() {
        return dateFormatSequence("app_notification_history");
    }

    @TargetDataSource(name = "center")
    public Long userReadId() {
        return dateFormatSequence("user_read");
    }

    @TargetDataSource(name = "center")
    public Long[] userReadIds(int num) {
        return dateFormatSequence("user_read", num);
    }

    /**
     * 爆款活动时间点Id
     */
    @TargetDataSource(name = "center")
    public Long saleCampaignSeckillTimeSequence() {
        return dateFormatSequence("sale_campaign_seckill_time");
    }

    /**
     * 批量获得活动秒杀表序列
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] saleCampaignSeckillsSequence(int num) {
        return dateFormatSequence("sale_campaign_seckill", num);
    }

    /**
     * 批量获得活动秒杀单元序列
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] saleUnitSequence(int num) {
        return dateFormatSequence("sale_unit", num);
    }

    /**
     * 获取虚拟商品序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long saleProductSequence() {
        return dateFormatSequence("sale_product");
    }

    @TargetDataSource(name = "center")
    public Long commentGradeTypeRelationId() {
        return get("comment_dimension_id");
    }

    @TargetDataSource(name = "center")
    public Long locationId() {
        return get("location_id");
    }

    /**
     * 视频分类id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long getVideoUseType() {
        return get("video_use_type");
    }


    /**
     * 视频合集id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long getVideoCollectionId() {
        return get("video_collection");
    }

    /**
     * 视频资源id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long getCommonVideoResourceId() {
        return get("common_video_resource");
    }

    /**
     * 视频资源id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long getVideoCollectionRelationId() {
        return get("video_collection_relation");
    }

    @TargetDataSource(name = "center")
    public Long AsMemberFeeRuleId() {
        return get("as_member_fee_rule_id");
    }

    /**
     * 优惠券使用时间限制表序列
     *
     * @param num 序列号数量
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] couponUseRuleTimeSequence(int num) {
        return dateFormatSequence("coupon_use_rule_time", num);
    }

    /**
     * 购物车id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long shoppingCartSequence() {
        return get("shopping_cart");
    }

    @TargetDataSource(name = "center")
    public Long userAddressId() {
        return get("user_address_id");
    }

    @TargetDataSource(name = "center")
    public Long userAddressTagId() {
        return get("user_address_tag_id");
    }

    @TargetDataSource(name = "center")
    public Long groupingGradeInfoSequence() {
        return get("grouping_grade_info");
    }

    @TargetDataSource(name = "center")
    public Long goodsCategorySequence() {
        return get("goods_category");
    }

    @TargetDataSource(name = "center")
    public Long goodsCategoryRelationSequence() {
        return get("goods_category_relation");
    }

    @TargetDataSource(name = "center")
    public Long messageNotificationSequence() {
        return get("message_notification_id");
    }

    @TargetDataSource(name = "center")
    public Long[] messageNotificationSequence(int num) {
        return get("message_notification_id", num);
    }

    @TargetDataSource(name = "center")
    public Long contractElementSequence() {
        return get("contract_element_id");
    }

    @TargetDataSource(name = "center")
    public Long contractTaskFormSequence() {
        return dateFormatSequence("contract_task_form_id");
    }

    @TargetDataSource(name = "center")
    public Long venueCampInvoiceSequence() {
        return dateFormatSequence("venue_camp_invoice_id");
    }

    @TargetDataSource(name = "center")
    public Long couponSaleConfigSequence() {
        return get("coupon_sale_config_id");
    }

    @TargetDataSource(name = "center")
    public Long couponSaleRelationSequence() {
        return get("coupon_sale_relation_id");
    }

    @TargetDataSource(name = "center")
    public Long divingId() {
        return get("diving_id");
    }

    @TargetDataSource(name = "center")
    public Long divingCourseId() {
        return get("diving_course_id");
    }

    @TargetDataSource(name = "center")
    public Long divingCoachId() {
        return get("diving_coach_id");
    }

    @TargetDataSource(name = "center")
    public Long divingShopId() {
        return get("diving_shop_id");
    }

    @TargetDataSource(name = "center")
    public Long showerRechargeRuleSequence() {
        return get("shower_recharge_rule_id");
    }

    @TargetDataSource(name = "center")
    public Long appLoginLogSequence() {
        return get("app_login_log_id");
    }

    @TargetDataSource(name = "center")
    public Long divingStudentId() {
        return get("diving_student_id");
    }

    @TargetDataSource(name = "center")
    public Long divingShopBusinessId() {
        return get("diving_shop_business_id");
    }

    @TargetDataSource(name = "center")
    public Long divingShopFileId() {
        return get("diving_shop_file_id");
    }

    @TargetDataSource(name = "center")
    public Long divingFileId() {
        return get("diving_file_id");
    }

    @TargetDataSource(name = "center")
    public Long divingCertificateId() {
        return get("diving_certificate_id");
    }

    @TargetDataSource(name = "center")
    public Long divingTradeId() {
        return dateFormatSequence("diving_trade_id");
    }

    @TargetDataSource(name = "center")
    public Long divingAccidentId() {
        return dateFormatSequence("diving_accident_id");
    }

    @TargetDataSource(name = "center")
    public Long divingAnnualCheckId() {
        return dateFormatSequence("diving_annual_check_id");
    }

    @TargetDataSource(name = "center")
    public Long fieldWaitNoticeId() {
        return dateFormatSequence("field_wait_notice_id");
    }


    @TargetDataSource(name = "center")
    public Long miniReminderNoticeId() {
        return get("mini_reminder_notice_id");
    }

    @TargetDataSource(name = "center")
    public Long memberSignInLogId() {
        return get("member_signin_log_id");
    }

    @TargetDataSource(name = "center")
    public Long gradeBenefitsId() {
        return get("grade_benefits_id");
    }

    @TargetDataSource(name = "center")
    public Long captainId() {
        return get("captain_id");
    }

    @TargetDataSource(name = "center")
    public Long stewardId() {
        return get("steward_id");
    }

    @TargetDataSource(name = "center")
    public Long yachtClubId() {
        return get("yacht_club_id");
    }

    /**
     * 协会会员id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long asMemberId() {
        return dateFormatSequence("as_member_id");
    }

    @TargetDataSource(name = "center")
    public Long asMemberApplyId() {
        return dateFormatSequence("as_member_apply_id");
    }


    @TargetDataSource(name = "center")
    public Long appInvoiceApplyId() {
        return dateFormatSequence("app_invoice_apply_id");
    }

    @TargetDataSource(name = "center")
    public Long appInvoiceTradeRelationId() {
        return dateFormatSequence("app_invoice_trade_relation");
    }

    @TargetDataSource(name = "center")
    public Long appInvoiceSendLogId() {
        return dateFormatSequence("app_invoice_send_log_id");
    }

    @TargetDataSource(name = "center")
    public Long tradeCustTypeId() {
        return dateFormatSequence("trade_cust_type_id");
    }

    @TargetDataSource(name = "center")
    public Long commonMediaResource() {
        return dateFormatSequence("common_media_resource_id");
    }

    @TargetDataSource(name = "center")
    public Long getNewsId() {
        return dateFormatSequence("news_id");
    }

    /**
     * 实名认证序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long netUserVerifySequence() {
        return get("net_user_verify_id");
    }

    /**
     * 实名认证序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long netUserVerifyPhoneSequence() {
        return get("net_user_verify_phone_id");
    }

    /**
     * 健康跑活动报名序列号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long activityEnrollSequence() {
        return dateFormatSequence("pub_activity_enroll_id");
    }

    /**
     * 健康跑活动赛事报名队员id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long activityPlayerSequence() {
        return dateFormatSequence("pub_activity_player_id");
    }


    @TargetDataSource(name = "center")
    public Long electronicSealSequence() {
        return get("electronic_seal_id");
    }

    @TargetDataSource(name = "center")
    public Long netUserCarId() {
        return get("net_user_car_id");
    }

    @TargetDataSource(name = "center")
    public Long netUserEcardCarId() {
        return get("net_user_ecard_car_id");
    }

    /**
     * 网络用户请求日志序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long netUserRequestLogSequence() {
        return dateFormatSequence("net_user_request_log");
    }

    /**
     * 网络用户访问限制序列
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long netUserAccessLimitSequence() {
        return dateFormatSequence("net_user_access_limit");
    }


    /**
     * 订场须知Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bookingReminderId() {
        return get("booking_manual");
    }

    @TargetDataSource(name = "center")
    public Long serviceBookingManualId() {
        return get("service_manual");
    }


    /**
     * 秒杀赠送主键
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long saleUnitPresentSequence() {
        return dateFormatSequence("sale_unit_present");
    }


    @TargetDataSource(name = "center")
    public Long[] saleUnitPresentSequence(int num) {
        return dateFormatSequence("sale_unit_present", num);
    }

    @TargetDataSource(name = "center")
    public Long groupPurchaseSequence() {
        return dateFormatSequence("group_purchase");
    }

    @TargetDataSource(name = "center")
    public Long groupPurchaseItemSequence() {
        return dateFormatSequence("group_purchase_item");
    }

    @TargetDataSource(name = "center")
    public Long groupPurchaseRecordSequence() {
        return dateFormatSequence("group_purchase_record");
    }

    @TargetDataSource(name = "center")
    public Long orderTempStoreSequence() {
        return dateFormatSequence("order_temp_store");
    }

    /**
     * 砍价活动表主键
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bargainActivitySequence() {
        return dateFormatSequence("bargain_activity");
    }

    /**
     * 砍价活动表主键
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bargainActivityItemSequence() {
        return dateFormatSequence("bargain_activity_item");
    }

    /**
     * 砍价发起者表主键
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bargainInitiatorSequence() {
        return dateFormatSequence("bargain_initiator");
    }

    /**
     * 砍价参与者表主键
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bargainParticipantSequence() {
        return dateFormatSequence("bargain_participant");
    }

    /**
     * 期次上课计划
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long termLessonPlanSequence() {
        return dateFormatSequence("term_lesson_plan");
    }

    /**
     * 运营商ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long operatorSequence() {
        return get("operator_id");
    }

    /**
     * 约球房间ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long chamberSequence() {
        return get("chamber_id");
    }

    /**
     * 约球投诉记录ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long chamberComplaintRecordSequence() {
        return get("chamber_complaint_record_id");
    }

    /**
     * 约球投诉记录文件ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long chamberComplaintFileSequence() {
        return get("chamber_complaint_file_id");
    }

    @TargetDataSource(name = "center")
    public Long visitorApplySequence() {
        return dateFormatSequence("visitor_apply");
    }

    @TargetDataSource(name = "center")
    public Long companyMemberFollowApplySequence() {
        return dateFormatSequence("company_member_follow_apply");
    }

    @TargetDataSource(name = "center")
    public Long merchantServerCategorySequence() {
        return dateFormatSequence("merchant_server_category_id");
    }

    @TargetDataSource(name = "center")
    public Long categoryAttributeSequence() {
        return dateFormatSequence("category_attribute_id");
    }

    @TargetDataSource(name = "center")
    public Long productPublishSequence() {
        return dateFormatSequence("product_publish_id");
    }

    @TargetDataSource(name = "center")
    public Long productAttributeSequence() {
        return dateFormatSequence("product_attribute_id");
    }
    /**
     * 银行对账单
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bankReconciliationSequence() {
        return dateFormatSequence("bank_reconciliation");
    }

    /**
     * 银行对账单
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] bankReconciliationSequence(int num) {
        return dateFormatSequence("bank_reconciliation", num);
    }
    @TargetDataSource(name = "center")

    public Long companyVisitorFollowSequence() {
        return dateFormatSequence("company_visitor_follow");
    }

    @TargetDataSource(name = "center")
    public Long companyMemberFollowSequence() {
        return dateFormatSequence("company_member_follow");
    }

    /**
     * 蓄力点赞奖品id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long saleCampAwardSequence() {
        return dateFormatSequence("sale_campaign_award");
    }

    /**
     * 蓄力点赞保存活动项产品id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long saleCampProductSequence() {
        return dateFormatSequence("sale_campaign_product");
    }

    /**
     * 国信产品绑定的id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long gxProductBindSequence() {
        return get("gx_product_bind");
    }
    @TargetDataSource(name = "center")
    public Long companyVisitorDistributeSequence() {
        return dateFormatSequence("company_visitor_distribute");

    }

    @TargetDataSource(name = "center")
    public Long companyMemberDistributeSequence() {
        return dateFormatSequence("company_member_distribute");
    }

    /**
     * 号码牌规则ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long numberPlateRuleSequence() {
        return get("number_plate_rule_id");
    }

    /**
     * 培训机构审核备案申请配置ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long institutionalConfigSequence() {
        return dateFormatSequence("institutional_config");
    }

    /**
     * 培训机构审核备案材料信息配置ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long institutionalDocConfigSequence() {
        return get("institutional_apply_doc");
    }

    /**
     * 号码牌规则明细ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long numberPlateRuleItemSequence() {
        return get("number_plate_rule_item_id");
    }
    /**
     * 运动处方
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmPrescriptionSequence() {
        return dateFormatSequence("bm_prescription");
    }

    /**
     * 体测医疗补充数据
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmMedicalDataItemSequence() {
        return dateFormatSequence("bm_medical_data_item");
    }

    /**
     * 体测医疗补充数据
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long bmExerciseDataSequence() {
        return dateFormatSequence("bm_exercise_data");
    }

    /**
     * 号码牌主键
     * @return
     */
    @TargetDataSource(name = "center")
    public Long[] getPlayerNoId(int num) {
        return get("public_camp_player_no_id",num);
    }

    /**
     * 发票冲红主键
     * @return
     */
    @TargetDataSource(name = "center")
    public Long appInvoiceRedRelationId() {
        return dateFormatSequence("app_invoice_red_relation");
    }

    @TargetDataSource(name = "center")
    public Long[] braceletFetchLog(int num) {
        return dateFormatSequence("bracelet_fetch_log", num);
    }

    /**
     * 停车场接口调用的日志id
     * @return
     */
    @TargetDataSource(name = "center")
    public Long parkingLogIdSequence() {
        return dateFormatSequence("parking_log_id");
    }

    @TargetDataSource(name = "center")
    public Long salaryTemplate() {
        return dateFormatSequence("salary_template");
    }
    @TargetDataSource(name = "center")
    public Long performanceTarget() {
        return dateFormatSequence("performance_target");
    }

    @TargetDataSource(name = "center")
    public Long salaryCalcRecord() {
        return dateFormatSequence("salary_calc_record");
    }

    @TargetDataSource(name = "center")
    public Long coachCommentId() {
        return dateFormatSequence("coach_comment");
    }

    /**
     * institutionalApplyAuditId 机构申请审批ID
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long institutionalApplyAuditId() {
        return dateFormatSequence("instit_apply_audit_id");
    }


    /**
     * 课时包优惠配置
     * @return
     */
    @TargetDataSource(name = "center")
    public Long promClassBagSequence() {
        return dateFormatSequence("training_prom_class_bag");
    }
    ///**
    // * 课时包优惠配置组Id
    // * @return
    // */
    //@TargetDataSource(name = "center")
    //public Long promClassBagGroupSequence() {
    //    return dateFormatSequence("training_prom_class_bag_group");
    //}
    /**
     * 课时包优惠配置
     * @return
     */
    @TargetDataSource(name = "center")
    public Long trainingFreeCourseSequence() {
        return dateFormatSequence("training_free_course");
    }



    /**
     * 跑步机信息主键
     * @return
     */
    @TargetDataSource(name = "center")
    public Long getTreadmillInfoId() {
        return get("treadmill_info_id");
    }

    /**
     * 客流上报单位Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long customerFlowAreaId() {
        return dateFormatSequence("customer_flow_area");
    }

    /**
     * 客流上报单位Id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long customerFlowEquipmentId() {
        return dateFormatSequence("customer_flow_equipment");
    }

    @TargetDataSource(name = "center")
    public Long hikGroupRealtimeDataId() {
        return dateFormatSequence("hik_group_realtime_data_id");
    }

    @TargetDataSource(name = "center")
    public Long hikGroupRealtimeDataHourId() {
        return dateFormatSequence("hik_group_realtime_data_hour_id");
    }
    @TargetDataSource(name = "center")
    public Long fieldGroupOccupySequence() {
        return dateFormatSequence("field_group_occupy");
    }


    @TargetDataSource(name = "center")
    public Long fieldGroupOccupyItemSequence() {
        return dateFormatSequence("field_group_occupy_item");
    }


    @TargetDataSource(name = "center")
    public Long fieldGroupOccupyDetailSequence() {
        return dateFormatSequence("field_group_occupy_detail");
    }


    @TargetDataSource(name = "center")
    public Long[] fieldGroupOccupyDetailSequence(int num) {
        return dateFormatSequence("field_group_occupy_detail", num);
    }


    @TargetDataSource(name = "center")
    public Long chamberSignSequence() {
        return dateFormatSequence("chamber_sign");
    }




    @TargetDataSource(name = "center")
    public Long salaryCalcExprParam() {
        return dateFormatSequence("salary_calc_expr_param");
    }

    /**
     * 资源分享表的主键id
     * @return
     */
    @TargetDataSource(name = "center")
    public Long shareDepositSequence() {
        return dateFormatSequence("share_deposit");
    }

    @TargetDataSource(name = "center")
    public Long salaryCalcJob() {
        return dateFormatSequence("salary_calc_job");
    }

    @TargetDataSource(name = "center")
    public Long gradeAttrSequence() {
        return dateFormatSequence("grade_attr");
    }

    @TargetDataSource(name = "center")
    public Long keyUpdateLogId() {
        return get("key_update_log_id");
    }

    /**
     * 获取分润周期表主键
     *
     * @return Long
     */
    @TargetDataSource(name = "center")
    public Long benefitItemSequence() {
        return dateFormatSequence("operator_benefit_item");
    }

    @TargetDataSource(name = "center")
    public Long schoolCardBindLogSequence() {
        return dateFormatSequence("school_card_bind_log");
    }

    @TargetDataSource(name = "center")
    public Long agreementRelationId() {
        return dateFormatSequence("agreement_relation_id");
    }

    /**
     * 购票限制规则id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long ticketRestrictionSequence() {
        return dateFormatSequence("restriction_id");
    }
    @TargetDataSource(name = "center")
    public Long groupCourseLabelSequence() {
        return dateFormatSequence("group_course_label");

    }

    @TargetDataSource(name = "center")
    public Long[] groupCourseLabelSequence(int num) {
        return dateFormatSequence("group_course_label", num);

    }

    @TargetDataSource(name = "center")
    public Long gcLessonBookRuleSequence() {
        return dateFormatSequence("gc_lesson_book_rule");

    }

    @TargetDataSource(name = "center")
    public Long gcLessonSeatHistSequence() {
        return get("gc_lesson_seat_hist");

    }

    @TargetDataSource(name = "center")
    public Long auditObjectInfoSequence() {
        return dateFormatSequence("audit_object_info");

    }

    @TargetDataSource(name = "center")
    public Long gcLessonBookSequence() {
        return dateFormatSequence("gc_lesson_book");

    }
    @TargetDataSource(name = "center")
    public Long gcLessonWaitingSequence() {
        return dateFormatSequence("gc_lesson_waiting");

    }


    @TargetDataSource(name = "center")
    public Long commentLabelDefSequence() {
        return get("comment_label_def");
    }

    @TargetDataSource(name = "center")

    public Long gcCommentLabelSequence() {
        return get("gc_comment_label");

    }

    @TargetDataSource(name = "center")

    public Long commentLabelSequence() {
        return get("comment_label");

    }

    @TargetDataSource(name = "center")

    public Long[] commentLabelSequence(int num) {
        return get("comment_label", num);

    }

    @TargetDataSource(name = "center")
    public Long[] CCBPandaBillSequence(int num) {
        return dateFormatSequence("ccb_panda_bill", num);

    }

    /**
     * 预约开放计划id
     * @return
     */
    @TargetDataSource(name = "center")
    public Long reservationActivityPlanId() {
        return dateFormatSequence("reservation_activity_plan_id");
    }

    @TargetDataSource(name = "center")
    public Long reservationActivityTimeId() {
        return dateFormatSequence("reservation_activity_time_id");
    }

    /**
     * 百度直播记录id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long baiduLiveSequence() {
        return dateFormatSequence("baidu_live_id");
    }

    /**
     * 百度直播记录时段id
     *
     * @return
     */
    @TargetDataSource(name = "center")
    public Long baiduLiveSegmentSequence() {
        return dateFormatSequence("baidu_live_segment_id");
    }

    @TargetDataSource(name = "center")
    public Long enrollFreezeTrade() {
        return dateFormatSequence("enroll_freeze_trade");
    }

    @TargetDataSource(name = "center")
    public Long enrollFreezePeriodId() {
        return get("enroll_freeze_period");

    }

    @TargetDataSource(name = "center")
    public Long unfreezeEnrollTradeId() {
        return dateFormatSequence("unfreeze_enroll_trade");
    }

    @TargetDataSource(name = "center")
    public Long parkingAreaSequence() {
        return get("parking_area");
    }




    @TargetDataSource(name = "center")
    public Long parkingResvCampSequence() {
        return dateFormatSequence("parking_resv_camp");

    }

    @TargetDataSource(name = "center")
    public Long parkingResvCampFreeSequence() {

        return dateFormatSequence("parking_resv_camp_free");
    }

    @TargetDataSource(name = "center")
    public Long[] parkingResvCampFreeSequence(int num) {

        return dateFormatSequence("parking_resv_camp_free", num);
    }

    @TargetDataSource(name = "center")
    public Long parkingStockSequence() {
        return get("parking_stock");



    }

    @TargetDataSource(name = "center")

    public Long parkingStockLogSequence() {
        return get("parking_stock_log");


    }


    @TargetDataSource(name = "center")

    public Long parkingReserveSequence() {
        return get("parking_reserve");


    }

    @TargetDataSource(name = "center")
    public Long groupCardPackageId() {
        return get("group_card_package");
    }

    @TargetDataSource(name = "center")
    public Long depositPackageLogId() {
        return get("deposit_package_log");
    }

    @TargetDataSource(name = "center")
    public Long groupDepositMemberId() {
        return get("group_deposit_member");
    }

    @TargetDataSource(name = "center")
    public Long[] groupDepositMemberId(int num) {
        return get("group_deposit_member",num);
    }

    @TargetDataSource(name = "center")
    public Long groupDepositAssignLogId() {
        return dateFormatSequence("group_deposit_assign_log");
    }

    @TargetDataSource(name = "center")
    public Long[] groupDepositAssignLog(int num) {
        return dateFormatSequence("group_deposit_assign_log",num);
    }

    @TargetDataSource(name = "center")
    public Long groupDepositMemberChangeId() {
        return dateFormatSequence("group_deposit_member_change");
    }


    @TargetDataSource(name = "center")
    public Long groupGroupMemberBindLogId() {
        return dateFormatSequence("group_member_bind_log");
    }


    @TargetDataSource(name = "center")
    public Long[] groupGroupMemberBindLogId(int num) {
        return dateFormatSequence("group_member_bind_log", num);
    }


    @TargetDataSource(name = "center")
    public Long cardIdSequence() {
        return dateFormatSequence("cardId");
    }

    @TargetDataSource(name = "center")
    public Long daLianCardIdSequence() {
        return get("da_lian_cardId");
    }

    @TargetDataSource(name = "center")
    public Long humanNearWarnInfoIdSequence() {
        return get("human_near_warn_info_id");
    }

    @TargetDataSource(name = "center")
    public Long parkingLogId() {
        return dateFormatSequence("parking_discount_log");
    }
    @TargetDataSource(name = "center")
    public Long oauthCodeHistId() {
        return get("oauth_code_hist");

    }

    @TargetDataSource(name = "center")

    public Long oauthAccessTokenId() {
        return get("oauth_access_token");
    }

    @TargetDataSource(name = "center")

    public Long[] oauthAccessTokenId(int num) {
        return get("oauth_access_token", num);
    }

    @TargetDataSource(name = "center")
    public Long clientUserId() {
        return dateFormatSequence("client_user_info");
    }


    @TargetDataSource(name = "center")
    public Long dataSyncLogId() {
        return dateFormatSequence("data_sync_log");
    }

    @TargetDataSource(name = "center")
    public Long thirdPointsChangeId() {
        return dateFormatSequence("third_points_change_id");
    }

    @TargetDataSource(name = "center")
    public Long changShuAssociationIdSequence() {
        return get("chang_shu_association_id");
    }

    @TargetDataSource(name = "center")
    public Long dqCustomerFlowId() {
        return dateFormatSequence("dq_customer_flow_id");
    }


    @TargetDataSource(name = "center")
    public Long userOrderLog() {
        return dateFormatSequence("user_order_log");
    }

    @TargetDataSource(name = "center")
    public Long[] userOrderLog(int num) {
        return dateFormatSequence("user_order_log", num);
    }

    @TargetDataSource(name = "center")
    public Long facePersonId() {
        return dateFormatSequence("face_person_id");
    }

    @TargetDataSource(name = "center")
    public Long facePhotoId() {
        return dateFormatSequence("face_photo_id");
    }


    @TargetDataSource(name = "center")
    public Long netUserPalmsId() {
        return dateFormatSequence("net_user_palms");
    }

    @TargetDataSource(name = "center")
    public Long palmsWxCertId() {
        return dateFormatSequence("palms_wx_cert");

    }
    @TargetDataSource(name = "center")
    public Long keyRandomHistId() {
        return dateFormatSequence("key_random_hist");

    }

    @TargetDataSource(name = "center")
    public Long palmsLockerRelationId() {
        return dateFormatSequence("palms_locker_relation");




    }


    @TargetDataSource(name = "center")
    public Long waterCertificateId() {
        return get("water_certificate_id");
    }

    @TargetDataSource(name = "center")
    public Long netUserMedalsId() {
        return get("net_user_medals_id");
    }

    @TargetDataSource(name = "center")
    public Long getCommonMediaResourceId() {
        return dateFormatSequence("common_media_resource_id");
    }

    @TargetDataSource(name = "center")
    public Long interactiveActivityId() {
        return dateFormatSequence("interactive_activity");


    }

    @TargetDataSource(name = "center")
    public Long interactiveFormId() {
        return get("interactive_form");
    }

    @TargetDataSource(name = "center")
    public Long[] interactiveFormIds(int num) {
        return get("interactive_form",num);
    }


    @TargetDataSource(name = "center")
    public Long interactiveSignId() {
        return dateFormatSequence("interactive_sign");

    }

    @TargetDataSource(name = "center")
    public Long[] interactiveSignId(int num) {
        return dateFormatSequence("interactive_sign", num);

    }

    @TargetDataSource(name = "center")
    public Long interactiveLotteryId() {
        return dateFormatSequence("interactive_lottery_setting");

    }

    @TargetDataSource(name = "center")
    public Long interactiveMessageId() {
        return dateFormatSequence("interactive_message");

    }

    @TargetDataSource(name = "center")
    public Long interactiveAuditId() {
        return get("interactive_message_audit");

    }


    @TargetDataSource(name = "center")
    public Long interactiveWinnerId() {
        return dateFormatSequence("interactive_winning_results");

    }

    @TargetDataSource(name = "center")
    public Long[] interactiveWinnerId(int num) {
        return dateFormatSequence("interactive_winning_results", num);

    }

    @TargetDataSource(name = "center")
    public Long[] palmsLockerId(int num) {
        return dateFormatSequence("palms_locker", num);

    }

    @TargetDataSource(name = "center")
    public Long palmsLockerShieldId() {
        return dateFormatSequence("palms_locker_shield");

    }


    @TargetDataSource(name = "center")
    public Long palmsLockerConflictId() {
        return dateFormatSequence("palms_locker_conflict");

    }

    @TargetDataSource(name = "center")
    public Long aptitudeAuditId() {
        return dateFormatSequence("aptitude_audit_id");

    }

    @TargetDataSource(name = "center")
    public Long aptitudeAuditUserId() {
        return dateFormatSequence("aptitude_audit_user_id");

    }

    @TargetDataSource(name = "center")
    public Long asVideoAuditLogId() {
        return dateFormatSequence("as_video_audit_log");

    }

    @TargetDataSource(name = "center")
    public Long cusTabId() {
        return dateFormatSequence("cus_tab_id");

    }

    @TargetDataSource(name = "center")
    public Long cusEcardTabId() {
        return dateFormatSequence("cus_ecard_tab_id");

    }

    @TargetDataSource(name = "center")
    public Long courseEnrollAuditId() {
        return dateFormatSequence("course_enroll_audit_id");

    }

    @TargetDataSource(name = "center")
    public Long subVenueId() {
        return dateFormatSequence("sub_venue_id");

    }

    @TargetDataSource(name = "center")
    public Long getNxLotteryActivityId() {
        return dateFormatSequence("nx_lottery_activity_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxLotteryAwardId() {
        return dateFormatSequence("nx_lottery_award_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxLotteryActivityAwardId() {
        return dateFormatSequence("nx_lottery_activity_award_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxLotteryActivityAwardUserId() {
        return dateFormatSequence("nx_lottery_activity_award_user_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxLotteryActivityUserId() {
        return dateFormatSequence("nx_lottery_activity_user_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxQuestionTypeId() {
        return get("nx_question_type_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxQuestionId() {
        return dateFormatSequence("nx_question_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxAnswerId() {
        return get("nx_answer_id");
    }

    @TargetDataSource(name = "center")
    public Long getNxAnswerActivityId() {
        return dateFormatSequence("nx_answer_activity_id");
    }

    @TargetDataSource(name = "center")
    public Long tcHsApplyId() {

        return dateFormatSequence("tc_hs_apply");

    }
    @TargetDataSource(name = "center")
    public Long getNxAnswerActivityModeId() {
        return dateFormatSequence("nx_answer_activity_mode_id");
    }
    @TargetDataSource(name = "center")
    public Long getNxAnswerModeAwardsId() {
        return get("nx_answer_mode_awards_id");
    }
    @TargetDataSource(name = "center")
    public Long getNxAnswerRecordId() {
        return dateFormatSequence("nx_answer_record_id");
    }
    @TargetDataSource(name = "center")
    public Long getNxAnswerRecordQuestionId() {
        return get("nx_answer_record_question_id");
    }
    @TargetDataSource(name = "center")
    public Long getNxAnswerModeUserId() {
        return get("nx_answer_mode_user_id");
    }

    @TargetDataSource(name = "center")
    public Long gcPersonId() {
        return get("gc_person");
    }

    @TargetDataSource(name = "center")
    public Long gcTradeId() {
        return get("gc_trade");
    }

    @TargetDataSource(name = "center")
    public Long batchEnrollScoreId() {
        return dateFormatSequence("batch_enroll_score");
    }

    @TargetDataSource(name = "center")
    public Long enrollScoreScoreId() {
        return dateFormatSequence("enroll_score");
    }

    @TargetDataSource(name = "center")
    public Long[] enrollScoreScoreId(int num) {
        return dateFormatSequence("enroll_score", num);
    }

    @TargetDataSource(name = "center")
    public Long netUserCertId() {
        return dateFormatSequence("net_user_cert");



    }

    @TargetDataSource(name = "center")
    public Long productStockId() {
        return dateFormatSequence("product_stock");
    }

    @TargetDataSource(name = "center")
    public Long[] commonSequence(String key, int num) {
        return dateFormatSequence(key, num);
    }

    @TargetDataSource(name = "center")
    public Long performStockId() {
        return get("perform_stock");
    }

    @TargetDataSource(name = "center")
    public Long performTicketRightsId() {
        return get("perform_ticket_rights");



    }
    @TargetDataSource(name = "center")
    public Long activityMerchantId() {
        return dateFormatSequence("activity_merchant_id");
    }

    @TargetDataSource(name = "center")
    public Long getActivityBlackListId() {
        return dateFormatSequence("activity_black_list_id");
    }

    @TargetDataSource(name = "center")
    public Long getTeamTicketSendRecordId() {
        return dateFormatSequence("team_ticket_send_id");
    }

    @TargetDataSource(name = "center")
    public Long stockSeatId() {
        return get("stock_seat");

    }

    @TargetDataSource(name = "center")
    public Long stockSeatId(int num) {
        return get("stock_seat");

    }

    @TargetDataSource(name = "center")
    public Long takeSeatLogId() {
        return get("take_seat_log");


    }
}
