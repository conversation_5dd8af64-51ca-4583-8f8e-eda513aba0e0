<!--
 * @Descripttion:
 * @version:
 * @Author: wh
 * @Date: 2022-01-12 15:41:53
 * @LastEditTime: 2022-03-16 15:53:37
-->
<template>
    <div class="title-box batch-insertion">
        <h1 style="display: block">
            <span>长期课排课</span>
            <a
                href="#/"
                class="btn-return"
                title="后退"
                @click.prevent="goprev">
                <i class="iconfont icon-zuojiantou"></i>
            </a>
        </h1>
        <div class="form-box">
            <div class="fields">
                <schedule-fields
                    ref="fields"
                    :longterm="true"
                    v-model="fields"></schedule-fields>
                <div class="form-submit-line">
                    <button
                        class="btn btn-lg btn-cancel"
                        @click="goprev">取消</button>
                    <button
                        class="btn btn-lg btn-primary"
                        @click="submit">保存</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import moment from 'moment'
import ScheduleFields from '../components/ScheduleFields.vue'
import { insertLongTermSchedule } from '../components/service'
import { showSuccess, showWrong } from 'mAlert'

export default {
    components: {
        ScheduleFields,
    },
    data() {
        return {
            fields: {
                placeId: [],
                subjectId: '',
                levelId: '',
                lessonType: '',
                coachIds: [],
                trialTag: '0',
                capacity: '',
                lessonDate: '',
                timeId: '',
                remark: '',
                serviceId: '',
                weekDays: '',
                startDate:
					moment().startOf('month').add(1, 'months').format('YYYY-MM-DD'),
                endDate: '2030-12-31',
                value1:[],
                assistCoachIds: []
		        },
        }
    },
    methods: {
        goprev() {
            vueRouter.go(-1)
        },
        submit() {
            if (!this.$refs.fields.validate()) {
                return
            }

            const fields = this.fields
            //delete fields.termClassId
            if (!fields.weekDays) {
                showWrong('请选择在星期几上课')
                return
            }
            const data = {
                ...fields,
                coachIds: fields.coachIds.join(','),
                assistCoachIds: fields.assistCoachIds.join(','),
                weekday: fields.weekDays,
                placeId: fields.placeId[0],
                roomId: fields.placeId[1] ? fields.placeId[1] : undefined,
                // termClassId:fields.value1[1]

            }
            insertLongTermSchedule(data)
                .then(() => {
                    showSuccess('添加长期课成功')
                    vueRouter.push('/longterm')
                })
        },
    },
}
</script>
