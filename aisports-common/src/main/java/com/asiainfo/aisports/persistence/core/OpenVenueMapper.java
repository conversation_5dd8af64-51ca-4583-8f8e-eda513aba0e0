package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.OpenVenue;
import com.asiainfo.aisports.domain.core.Venue;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface OpenVenueMapper {
    @Delete({
        "delete from open_venue",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into open_venue (id, venue_id, ",
        "venue_name, channel_id, ",
        "center_id, state, alipay_account, ",
        "alipay_name, last_sync_time, ",
        "create_time, create_staff_id, ",
        "update_time, update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{venueId,jdbcType=BIGINT}, ",
        "#{venueName,jdbcType=VARCHAR}, #{channelId,jdbcType=BIGINT}, ",
        "#{centerId,jdbcType=BIGINT}, #{state,jdbcType=CHAR}, #{alipayAccount,jdbcType=VARCHAR}, ",
        "#{alipayName,jdbcType=VARCHAR}, #{lastSyncTime,jdbcType=TIMESTAMP}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(OpenVenue record);

    @Select({
        "select",
        "id, venue_id, venue_name, channel_id, center_id, state, alipay_account, alipay_name, ",
        "last_sync_time, create_time, create_staff_id, update_time, update_staff_id",
        "from open_venue",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_name", property="venueName", jdbcType=JdbcType.VARCHAR),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="alipay_account", property="alipayAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="alipay_name", property="alipayName", jdbcType=JdbcType.VARCHAR),
        @Result(column="last_sync_time", property="lastSyncTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    OpenVenue selectByPrimaryKey(Long id);

    @Update({
        "update open_venue",
        "set venue_id = #{venueId,jdbcType=BIGINT},",
          "venue_name = #{venueName,jdbcType=VARCHAR},",
          "channel_id = #{channelId,jdbcType=BIGINT},",
          "center_id = #{centerId,jdbcType=BIGINT},",
          "state = #{state,jdbcType=CHAR},",
          "alipay_account = #{alipayAccount,jdbcType=VARCHAR},",
          "alipay_name = #{alipayName,jdbcType=VARCHAR},",
          "last_sync_time = #{lastSyncTime,jdbcType=TIMESTAMP},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(OpenVenue record);

    @Select({
        "<script>",
        "select",
        "id, venue_id, venue_name, channel_id, center_id, state, alipay_account, alipay_name, last_sync_time, create_time, create_staff_id, update_time, update_staff_id",
        "from open_venue",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"venueName != null\">and venue_name = #{venueName,jdbcType=VARCHAR}</if>",
        "<if test=\"channelId != null\">and channel_id = #{channelId,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"alipayAccount != null\">and alipay_account = #{alipayAccount,jdbcType=VARCHAR}</if>",
        "<if test=\"alipayName != null\">and alipay_name = #{alipayName,jdbcType=VARCHAR}</if>",
        "<if test=\"lastSyncTime != null\">and last_sync_time = #{lastSyncTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_name", property="venueName", jdbcType=JdbcType.VARCHAR),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="alipay_account", property="alipayAccount", jdbcType=JdbcType.VARCHAR),
        @Result(column="alipay_name", property="alipayName", jdbcType=JdbcType.VARCHAR),
        @Result(column="last_sync_time", property="lastSyncTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<OpenVenue> selectByFields(OpenVenue param);

    @Update({
        "<script>",
        "update open_venue",
        "<set>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "<if test=\"venueName != null\">venue_name = #{venueName,jdbcType=VARCHAR},</if>",
        "<if test=\"channelId != null\">channel_id = #{channelId,jdbcType=BIGINT},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"alipayAccount != null\">alipay_account = #{alipayAccount,jdbcType=VARCHAR},</if>",
        "<if test=\"alipayName != null\">alipay_name = #{alipayName,jdbcType=VARCHAR},</if>",
        "<if test=\"lastSyncTime != null\">last_sync_time = #{lastSyncTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(OpenVenue record);

    @Select({
            "<script>",
            "select",
            "a.venue_id, a.center_id, a.venue_name, a.phone, a.start_date, a.end_date, a.business_hours, ",
            "a.ecard_cust_id, a.pinyin, a.city_code, a.icon_name, a.location_id, a.create_time, a.create_staff_id, ",
            "a.update_time, a.update_staff_id",
            "from venue a, open_venue b",
            "<where>",
            "and a.venue_id = b.venue_id and b.state = '1'",
            "<if test=\"centerId != null\">and b.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueType != null\">and a.venue_type = #{venueType,jdbcType=BIGINT}</if>",
            "<if test=\"channelId != null\">and b.channel_id = #{channelId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="venue_name", property="venueName", jdbcType=JdbcType.VARCHAR),
            @Result(column="phone", property="phone", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="business_hours", property="businessHours", jdbcType=JdbcType.VARCHAR),
            @Result(column="ecard_cust_id", property="ecardCustId", jdbcType=JdbcType.BIGINT),
            @Result(column="pinyin", property="pinyin", jdbcType=JdbcType.VARCHAR),
            @Result(column="city_code", property="cityCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="icon_name", property="iconName", jdbcType=JdbcType.VARCHAR),
            @Result(column="location_id", property="locationId", jdbcType=JdbcType.BIGINT),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<Venue> selectVenues2Push(@Param("centerId") Long centerId, @Param("venueType") String venueType,
                                  @Param("channelId")Long channelId);

    @Select({
            "select",
            "id, venue_id, venue_name, channel_id, center_id, state, alipay_account, alipay_name, ",
            "last_sync_time, create_time, create_staff_id, update_time, update_staff_id",
            "from open_venue",
            "where venue_id = #{venueId,jdbcType=BIGINT}",
            "and channel_id = #{channelId,jdbcType=BIGINT}",
            "and state = '1'"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
            @Result(column="venue_name", property="venueName", jdbcType=JdbcType.VARCHAR),
            @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="alipay_account", property="alipayAccount", jdbcType=JdbcType.VARCHAR),
            @Result(column="alipay_name", property="alipayName", jdbcType=JdbcType.VARCHAR),
            @Result(column="last_sync_time", property="lastSyncTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    OpenVenue selectByVenueIdAndChannelId(@Param("venueId") Long venueId,
                                          @Param("channelId") Long channelId);
}