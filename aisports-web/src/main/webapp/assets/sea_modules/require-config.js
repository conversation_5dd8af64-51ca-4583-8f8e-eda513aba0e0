//版本号
var TAG = '1.0.1';

var agreementTag;
var agreementVersion;
try{
    agreementTag = document.getElementById('socketServerUrl').value;
    console.log('使用新版本协议逻辑');
}
catch(e){
    console.log('无新版本协议逻辑,使用旧版');
}
agreementVersion = agreementTag === 'new' ? 'agreementNew' : 'agreement';
require.config({
    //baseUrl: '../sea_modules',
    baseUrl: '/assets/sea_modules',
    paths: {
        'jquery': 'jquery/1.11.1/jquery',
        'zepto':'zepto/1.1.6/zepto',
        'slimScroll': 'slimScroll/slimscroll',
        'dropdown': 'bootstrap/assets/javascripts/bootstrap/dropdown',
        'modal': 'bootstrap/assets/javascripts/bootstrap/modal',
        'tab': 'bootstrap/assets/javascripts/bootstrap/tab',
        'qselect': 'bootstrap-select/bootstrap-select',
        'collapse': 'bootstrap/assets/javascripts/bootstrap/collapse',
        'select': 'easyDropdown/easydropdown',
        'nprogress': 'nprogress/nprogress',
        'validation': 'myplugins/validation',
        'xValidate': 'myplugins/xValidate',
        'xAjax': 'myplugins/xAjax',
        'xUtil': 'myplugins/xUtil',
        'toolTip': 'bootstrap/assets/javascripts/bootstrap/tooltip',
        'popover': 'bootstrap/assets/javascripts/bootstrap/popover',
        'scrollSteps': 'myplugins/scrollSteps',
        'dropDownTab': 'myplugins/dropDownTab',
        'form-control': 'myplugins/form-control',
        'textareaCount': 'myplugins/textareaCount',
        'frame': 'myplugins/frame',
        'selectToggle': 'myplugins/select-toggle',
        'ecardQuery': 'myplugins/ecardQuery',
        'print': 'myplugins/print',

        'spinner': 'myplugins/spinner',
        'partialSpinner': 'myplugins/partialSpinner',
        'checkBoxAll': 'myplugins/checkBoxAll',
        'datePlugin': 'myplugins/datePlugin',
        'ionRangeSlider': 'ionRangeSlider/ionRangeSlider',
        'ajaxUpload': 'ajaxFileUpload/ajaxfileupload',
        'model': 'myplugins/model',
        'markup': 'Markup/src/markup',
        'paginator': 'bootstrap/assets/javascripts/bootstrap/paginator',
        'bootstrapDatepicker': 'bootstrap/assets/javascripts/bootstrap/bootstrapDatepicker',
        'bootstrapDateRangepicker': 'bootstrap/assets/javascripts/bootstrap/daterangepicker',
        'moment': 'moment/2.8.1/moment',
        'momentRange' : 'moment/2.8.1/moment-range',
        'copyFixedTableHead': 'myplugins/copyFixedTableHead',
        'tabs': 'myplugins/tabs',
        'aisportsConfig': 'aisportsConfig',
        'turnUpDown': 'myplugins/turnUpDown',
        'backbone': 'Backbone/backbone',
        'underscore': 'Backbone/underscore',
        'timepicker': 'timepicker/js/bootstrap-timepicker.js',
        'cardReader': 'myplugins/cardReader',
        'Util': 'myplugins/util',
        'moment_i18n': 'myplugins/moment_i18n',
        'kalendae': 'Kalendae/kalendae',
        'mock': 'simulationData/mock',
        'printTicket': 'myplugins/printTicket',
        'getUserMedia': 'getUserMedia/getUserMedia',
        "webcam2": "libs/webcam2/build.js",
        'swiper':'swiper/swiper.jquery.umd',
        'drawDoughnutChart': 'drawDoughnutChart/drawDoughnutChart',
        'socketio': 'socketIO/socket.io',
        'pcSocketClient': 'myplugins/pcSocketClient',
        'pcSocketClientNew': 'myplugins/pcSocketClientNew',
        'padSocketClient': 'myplugins/padSocketClient',
        'mAlert': 'myplugins/mAlert',
        'switch': 'myplugins/bootstrap-switch',
        'html2canvas': 'html2canvas/html2canvas',
        'jSignature': 'jSignature/jSignature',
        'jSignatureUndoButton': 'jSignature/jSignatureUndoButton',
        'superSearch': 'myplugins/superSearch',
        'agreement': 'myplugins/agreement',
        'd3': 'd3/d3',
        'dimple': 'dimple/dimple.v2.1.6',
        'xCharts': 'myplugins/xCharts',
        'iscroll': 'iscroll/iscroll',
        'printReportForm': 'myplugins/printReportForm',
        'menu': 'myplugins/menu',

        'interact': 'dragAndDrop/interact-1.2.4.js',
        'umEditor': 'umEditor/umeditor.js',
        'umEditorConfig': 'umEditor/umeditor.config.js',
        'handlebars': 'handlebars/handlebars-v3.0.1',
        'selectMultiItems': 'myplugins/SelectMultiItems',
        'sticky': 'myplugins/sticky',
        'cookie': "cookie/cookie",
        'keyUtil': "myplugins/keyUtil",
        "beautifulConfirmBox" : 'myplugins/beautifulConfirmBox',
        'umEditorStyle' : 'myplugins/umEditorStyle',
        'checkMachineInput':'myplugins/checkMachineInput',
        'checkDateValid' : 'myplugins/checkDateValid',
        'checkObjectBeforeSend' : 'myplugins/checkObjectBeforeSend',
        'masonry': 'masonry/masonry.pkgd.js',
        'personalMsg': 'myplugins/personalMsg',
        'vent': 'myplugins/vent',
        'selectable': 'myplugins/selectable',
        'globals': 'myplugins/globals',
        'switchTab': 'myplugins/switchTab',
        'enterpriseQuery': 'myplugins/superEnterpriseSearch',
        'groupUserQuery': 'myplugins/superGroupUserSearch',
        'showPrompt': 'myplugins/showPrompt'
    },
    preload: ['jquery'],
    map: [
        [ /^(.*\/static\/.*\.(?:js))(?:.*)$/i, '$1?v='+TAG ],
        ['agreement.js',agreementVersion + '.js']
    ]
});

/**
 * 错误处理
 */
require.onError = function(err) {
    console.log(err.requireType);
    if (err.requireType === 'timeout') {
        alert('modules: ' + err.requireModules);
    }

    throw err;
};