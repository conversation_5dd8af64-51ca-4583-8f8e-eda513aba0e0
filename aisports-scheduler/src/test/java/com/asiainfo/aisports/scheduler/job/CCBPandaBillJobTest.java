package com.asiainfo.aisports.scheduler.job;

import com.asiainfo.aisports.scheduler.TestApplicationContext;
import net.sf.json.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CCBPandaBillJobTest extends TestApplicationContext {

    @Autowired
    private CCBPandaBillJob ccbPandaBillJob;

    @Test
    public void execute() {
        JSONObject args = JSONObject.fromObject("{\"startDate\":\"2022-12-09\",\"endDate\":\"2022-12-13\"}");

        ccbPandaBillJob.execute(10000000L, args);
    }
}
