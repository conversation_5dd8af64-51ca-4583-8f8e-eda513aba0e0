package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.BmAcmewayReport;
import com.asiainfo.aisports.domain.core.BmReportItem;
import com.asiainfo.aisports.persistence.core.BmAcmewayReportMapper;
import com.asiainfo.aisports.persistence.core.BmReportItemMapper;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by xzeng on 17/8/29.
 */
@Service
public class BmAcmewayReportService {
    @Autowired
    BmAcmewayReportMapper bmAcmewayReportMapper;
    @Autowired
    BmReportItemMapper bmReportItemMapper;

    /**
     * 获取微信展示的体测结果
     *
     * @param centerId
     * @param outerTestOrder
     * @return
     */
    @Transactional(readOnly = true)
    public ServiceResult getBmGeneralReport(Long centerId, String outerTestOrder) {
        ServiceResult result = new ServiceResult();
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.TEST_REPORT, Constants.AcmewayRptCode.MC180, "mc"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.TEST_REPORT, Constants.AcmewayRptCode.SONOST2000, "sonost"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.TEST_REPORT, Constants.AcmewayRptCode.EET, "ettload"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.TEST_REPORT, Constants.AcmewayRptCode.MWT, "mwt"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.TEST_REPORT, Constants.AcmewayRptCode.GMTZCS, "gmtzcs"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.TEST_REPORT, Constants.AcmewayRptCode.INDIVIDUAL_ASSESS, "individualAssess"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.EXERCISE_RISK_RPT, Constants.AcmewayRptCode.INDIVIDUAL_ASSESS, "exerciseRiskRpt"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.EXERCISE_CAPACITY_RPT, Constants.AcmewayRptCode.INDIVIDUAL_ASSESS, "exerciseCapacityRpt"));
        result.putAll(getValidReportByType(centerId, outerTestOrder, Constants.Status.VALID, Constants.AcmewayRptType.DIRECTION_REPORT, Constants.AcmewayRptCode.INDIVIDUAL_ASSESS, "directionReport"));
        return result;
    }

    /**
     * 获取某一个体测项目的报告
     *
     * @param centerId
     * @param outerTestOrder
     * @param state
     * @param rptType
     * @param rptCode
     * @param resultName
     * @return
     */
    @Transactional(readOnly = true)
    public ServiceResult getValidReportByType(Long centerId, String outerTestOrder, String state, String rptType, String rptCode, String resultName) {
        ServiceResult result = new ServiceResult();
        //1.查询报告信息
        BmAcmewayReport report = new BmAcmewayReport();
        report.setCenterId(centerId);
        report.setOuterTestOrder(outerTestOrder);
        report.setState(state);
        report.setRptType(rptType);
        report.setRptCode(rptCode);
        List<BmAcmewayReport> bmAcmewayReportList = bmAcmewayReportMapper.selectByFields(report);
        if (bmAcmewayReportList.isEmpty()) {
            return new ServiceResult();
        }
        report = bmAcmewayReportList.get(0);
        //2.查询该类型测试的具体值
        BmReportItem item = new BmReportItem();
        item.setRptId(report.getId());
        List<BmReportItem> bmReportItemList = bmReportItemMapper.selectByFields(item);
        if (!Strings.isNullOrEmpty(resultName)) {
            result.put(resultName, report);
            result.set(resultName + "List", bmReportItemList);
        } else {
            result.put("report", report);
            result.set("reportItemList", bmReportItemList);
        }
        return result;
    }
}
