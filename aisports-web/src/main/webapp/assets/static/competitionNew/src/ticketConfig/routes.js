import CompList from './CompList.vue'
import CompInfo from './CompInfo.vue'
import PerformInfo from './PerformInfo.vue'
import compDetail from './compDetail.vue'
import VenueStaticParamManage from 'business/VenueStaticParamManage'
import teamTicket from './teamTicket.vue'
import teamTicketDetail from './teamTicketDetail.vue'

const NoMatch = { template: '<div>页面未找到！</div>' }

const routes = [
    {
        path: '/',
        component: CompList,
    },
    {
        path: '/comp',
        component: CompInfo,
    },
    {
        path: '/perform',
        component: PerformInfo,
    },
    {
        path: '/detail',
        component: compDetail,
    },
    {
        path: '/teamTicket',
        component: teamTicket,
    },
    {
        path: '*',
        component: NoMatch,
    },
    {
        path: '/teamTicketDetail',
        component: teamTicketDetail,
    },
    VenueStaticParamManage,
]

export default routes
