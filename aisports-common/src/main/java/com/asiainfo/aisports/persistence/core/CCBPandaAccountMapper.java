package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.CCBPandaAccount;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface CCBPandaAccountMapper {
    @Delete({
        "delete from ccb_panda_account",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into ccb_panda_account (id, center_id, ",
        "venue_id, merchant_id, ",
        "pos_id, branch_id, ",
        "customer_id, user_id, ",
        "`password`, cdtr_wlt_id, ",
        "sub_merchant_id, pub_key, ",
        "ft_corp_id, ft_tile_id, ",
        "ft_scenario, ft_pub_key_1, ",
        "ft_pri_key_1, ft_pub_key_2, ",
        "access_token, communication_key, ",
        "ft_server, voucher_update_time, ",
        "state, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{centerId,jdbcType=BIGINT}, ",
        "#{venueId,jdbcType=BIGINT}, #{merchantId,jdbcType=VARCHAR}, ",
        "#{posId,jdbcType=VARCHAR}, #{branchId,jdbcType=VARCHAR}, ",
        "#{customerId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, ",
        "#{password,jdbcType=VARCHAR}, #{cdtrWltId,jdbcType=VARCHAR}, ",
        "#{subMerchantId,jdbcType=VARCHAR}, #{pubKey,jdbcType=VARCHAR}, ",
        "#{ftCorpId,jdbcType=VARCHAR}, #{ftTileId,jdbcType=VARCHAR}, ",
        "#{ftScenario,jdbcType=VARCHAR}, #{ftPubKey1,jdbcType=VARCHAR}, ",
        "#{ftPriKey1,jdbcType=VARCHAR}, #{ftPubKey2,jdbcType=VARCHAR}, ",
        "#{accessToken,jdbcType=VARCHAR}, #{communicationKey,jdbcType=VARCHAR}, ",
        "#{ftServer,jdbcType=VARCHAR}, #{voucherUpdateTime,jdbcType=TIMESTAMP}, ",
        "#{state,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(CCBPandaAccount record);

    @Select({
        "select",
        "id, center_id, venue_id, merchant_id, pos_id, branch_id, customer_id, user_id, ",
        "`password`, cdtr_wlt_id, sub_merchant_id, pub_key, ft_corp_id, ft_tile_id, ft_scenario, ",
        "ft_pub_key_1, ft_pri_key_1, ft_pub_key_2, access_token, communication_key, ft_server, ",
        "voucher_update_time, state, create_time, create_staff_id, update_time, update_staff_id",
        "from ccb_panda_account",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="merchant_id", property="merchantId", jdbcType=JdbcType.VARCHAR),
        @Result(column="pos_id", property="posId", jdbcType=JdbcType.VARCHAR),
        @Result(column="branch_id", property="branchId", jdbcType=JdbcType.VARCHAR),
        @Result(column="customer_id", property="customerId", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_id", property="userId", jdbcType=JdbcType.VARCHAR),
        @Result(column="password", property="password", jdbcType=JdbcType.VARCHAR),
        @Result(column="cdtr_wlt_id", property="cdtrWltId", jdbcType=JdbcType.VARCHAR),
        @Result(column="sub_merchant_id", property="subMerchantId", jdbcType=JdbcType.VARCHAR),
        @Result(column="pub_key", property="pubKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_corp_id", property="ftCorpId", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_tile_id", property="ftTileId", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_scenario", property="ftScenario", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_pub_key_1", property="ftPubKey1", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_pri_key_1", property="ftPriKey1", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_pub_key_2", property="ftPubKey2", jdbcType=JdbcType.VARCHAR),
        @Result(column="access_token", property="accessToken", jdbcType=JdbcType.VARCHAR),
        @Result(column="communication_key", property="communicationKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_server", property="ftServer", jdbcType=JdbcType.VARCHAR),
        @Result(column="voucher_update_time", property="voucherUpdateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    CCBPandaAccount selectByPrimaryKey(Long id);

    @Update({
        "update ccb_panda_account",
        "set center_id = #{centerId,jdbcType=BIGINT},",
          "venue_id = #{venueId,jdbcType=BIGINT},",
          "merchant_id = #{merchantId,jdbcType=VARCHAR},",
          "pos_id = #{posId,jdbcType=VARCHAR},",
          "branch_id = #{branchId,jdbcType=VARCHAR},",
          "customer_id = #{customerId,jdbcType=VARCHAR},",
          "user_id = #{userId,jdbcType=VARCHAR},",
          "`password`= #{password,jdbcType=VARCHAR},",
          "cdtr_wlt_id = #{cdtrWltId,jdbcType=VARCHAR},",
          "sub_merchant_id = #{subMerchantId,jdbcType=VARCHAR},",
          "pub_key = #{pubKey,jdbcType=VARCHAR},",
          "ft_corp_id = #{ftCorpId,jdbcType=VARCHAR},",
          "ft_tile_id = #{ftTileId,jdbcType=VARCHAR},",
          "ft_scenario = #{ftScenario,jdbcType=VARCHAR},",
          "ft_pub_key_1 = #{ftPubKey1,jdbcType=VARCHAR},",
          "ft_pri_key_1 = #{ftPriKey1,jdbcType=VARCHAR},",
          "ft_pub_key_2 = #{ftPubKey2,jdbcType=VARCHAR},",
          "access_token = #{accessToken,jdbcType=VARCHAR},",
          "communication_key = #{communicationKey,jdbcType=VARCHAR},",
          "ft_server = #{ftServer,jdbcType=VARCHAR},",
          "voucher_update_time = #{voucherUpdateTime,jdbcType=TIMESTAMP},",
          "state = #{state,jdbcType=VARCHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(CCBPandaAccount record);

    @Select({
        "<script>",
        "select",
        "id, center_id, venue_id, merchant_id, pos_id, branch_id, customer_id, user_id, `password`, cdtr_wlt_id, sub_merchant_id, pub_key, ft_corp_id, ft_tile_id, ft_scenario, ft_pub_key_1, ft_pri_key_1, ft_pub_key_2, access_token, communication_key, ft_server, voucher_update_time, state, create_time, create_staff_id, update_time, update_staff_id",
        "from ccb_panda_account",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"merchantId != null\">and merchant_id = #{merchantId,jdbcType=VARCHAR}</if>",
        "<if test=\"posId != null\">and pos_id = #{posId,jdbcType=VARCHAR}</if>",
        "<if test=\"branchId != null\">and branch_id = #{branchId,jdbcType=VARCHAR}</if>",
        "<if test=\"customerId != null\">and customer_id = #{customerId,jdbcType=VARCHAR}</if>",
        "<if test=\"userId != null\">and user_id = #{userId,jdbcType=VARCHAR}</if>",
        "<if test=\"password != null\">and password = #{password,jdbcType=VARCHAR}</if>",
        "<if test=\"cdtrWltId != null\">and cdtr_wlt_id = #{cdtrWltId,jdbcType=VARCHAR}</if>",
        "<if test=\"subMerchantId != null\">and sub_merchant_id = #{subMerchantId,jdbcType=VARCHAR}</if>",
        "<if test=\"pubKey != null\">and pub_key = #{pubKey,jdbcType=VARCHAR}</if>",
        "<if test=\"ftCorpId != null\">and ft_corp_id = #{ftCorpId,jdbcType=VARCHAR}</if>",
        "<if test=\"ftTileId != null\">and ft_tile_id = #{ftTileId,jdbcType=VARCHAR}</if>",
        "<if test=\"ftScenario != null\">and ft_scenario = #{ftScenario,jdbcType=VARCHAR}</if>",
        "<if test=\"ftPubKey1 != null\">and ft_pub_key_1 = #{ftPubKey1,jdbcType=VARCHAR}</if>",
        "<if test=\"ftPriKey1 != null\">and ft_pri_key_1 = #{ftPriKey1,jdbcType=VARCHAR}</if>",
        "<if test=\"ftPubKey2 != null\">and ft_pub_key_2 = #{ftPubKey2,jdbcType=VARCHAR}</if>",
        "<if test=\"accessToken != null\">and access_token = #{accessToken,jdbcType=VARCHAR}</if>",
        "<if test=\"communicationKey != null\">and communication_key = #{communicationKey,jdbcType=VARCHAR}</if>",
        "<if test=\"ftServer != null\">and ft_server = #{ftServer,jdbcType=VARCHAR}</if>",
        "<if test=\"voucherUpdateTime != null\">and voucher_update_time = #{voucherUpdateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="merchant_id", property="merchantId", jdbcType=JdbcType.VARCHAR),
        @Result(column="pos_id", property="posId", jdbcType=JdbcType.VARCHAR),
        @Result(column="branch_id", property="branchId", jdbcType=JdbcType.VARCHAR),
        @Result(column="customer_id", property="customerId", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_id", property="userId", jdbcType=JdbcType.VARCHAR),
        @Result(column="password", property="password", jdbcType=JdbcType.VARCHAR),
        @Result(column="cdtr_wlt_id", property="cdtrWltId", jdbcType=JdbcType.VARCHAR),
        @Result(column="sub_merchant_id", property="subMerchantId", jdbcType=JdbcType.VARCHAR),
        @Result(column="pub_key", property="pubKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_corp_id", property="ftCorpId", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_tile_id", property="ftTileId", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_scenario", property="ftScenario", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_pub_key_1", property="ftPubKey1", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_pri_key_1", property="ftPriKey1", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_pub_key_2", property="ftPubKey2", jdbcType=JdbcType.VARCHAR),
        @Result(column="access_token", property="accessToken", jdbcType=JdbcType.VARCHAR),
        @Result(column="communication_key", property="communicationKey", jdbcType=JdbcType.VARCHAR),
        @Result(column="ft_server", property="ftServer", jdbcType=JdbcType.VARCHAR),
        @Result(column="voucher_update_time", property="voucherUpdateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<CCBPandaAccount> selectByFields(CCBPandaAccount param);

    @Update({
        "<script>",
        "update ccb_panda_account",
        "<set>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "<if test=\"merchantId != null\">merchant_id = #{merchantId,jdbcType=VARCHAR},</if>",
        "<if test=\"posId != null\">pos_id = #{posId,jdbcType=VARCHAR},</if>",
        "<if test=\"branchId != null\">branch_id = #{branchId,jdbcType=VARCHAR},</if>",
        "<if test=\"customerId != null\">customer_id = #{customerId,jdbcType=VARCHAR},</if>",
        "<if test=\"userId != null\">user_id = #{userId,jdbcType=VARCHAR},</if>",
        "<if test=\"password != null\">`password` = #{password,jdbcType=VARCHAR},</if>",
        "<if test=\"cdtrWltId != null\">cdtr_wlt_id = #{cdtrWltId,jdbcType=VARCHAR},</if>",
        "<if test=\"subMerchantId != null\">sub_merchant_id = #{subMerchantId,jdbcType=VARCHAR},</if>",
        "<if test=\"pubKey != null\">pub_key = #{pubKey,jdbcType=VARCHAR},</if>",
        "<if test=\"ftCorpId != null\">ft_corp_id = #{ftCorpId,jdbcType=VARCHAR},</if>",
        "<if test=\"ftTileId != null\">ft_tile_id = #{ftTileId,jdbcType=VARCHAR},</if>",
        "<if test=\"ftScenario != null\">ft_scenario = #{ftScenario,jdbcType=VARCHAR},</if>",
        "<if test=\"ftPubKey1 != null\">ft_pub_key_1 = #{ftPubKey1,jdbcType=VARCHAR},</if>",
        "<if test=\"ftPriKey1 != null\">ft_pri_key_1 = #{ftPriKey1,jdbcType=VARCHAR},</if>",
        "<if test=\"ftPubKey2 != null\">ft_pub_key_2 = #{ftPubKey2,jdbcType=VARCHAR},</if>",
        "<if test=\"accessToken != null\">access_token = #{accessToken,jdbcType=VARCHAR},</if>",
        "<if test=\"communicationKey != null\">communication_key = #{communicationKey,jdbcType=VARCHAR},</if>",
        "<if test=\"ftServer != null\">ft_server = #{ftServer,jdbcType=VARCHAR},</if>",
        "<if test=\"voucherUpdateTime != null\">voucher_update_time = #{voucherUpdateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(CCBPandaAccount record);
}
