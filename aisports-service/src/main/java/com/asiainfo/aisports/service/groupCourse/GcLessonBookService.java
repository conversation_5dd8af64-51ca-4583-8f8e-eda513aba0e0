package com.asiainfo.aisports.service.groupCourse;

import com.asiainfo.aisports.domain.core.groupCourse.GcLessonBook;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.GcGeneralBookRule;
import com.asiainfo.aisports.model.GcResvDto;
import com.asiainfo.aisports.persistence.core.groupCourse.GcLessonBookMapper;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.tools.DateCalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-09-23 9:58
 */
@Service
public class GcLessonBookService {


    @Autowired
    private GcLessonBookMapper gcLessonBookMapper;
    @Autowired
    private GcLessonBookRuleService gcLessonBookRuleService;


    public int saveBookInfo(GcLessonBook gcLessonBook) {
        return gcLessonBookMapper.insert(gcLessonBook);
    }

    public GcLessonBook getByTradeId(Long tradeId) {
        GcLessonBook book = new GcLessonBook();
        book.setCreateTradeId(tradeId);

        List<GcLessonBook> gcLessonBooks = gcLessonBookMapper.selectByFields(book);
        if (!gcLessonBooks.isEmpty()) {
            return gcLessonBooks.get(0);
        }
        return null;
    }

    public GcLessonBook getById(Long bookId) {
        return gcLessonBookMapper.selectByPrimaryKey(bookId);
    }

    public int updateBySelective(GcLessonBook gcLessonBook) {
        return gcLessonBookMapper.updateByPrimaryKeySelective(gcLessonBook);
    }

    public List<GcLessonBook> getValidBookByLessonId(Long lessonId) {
        return gcLessonBookMapper.selectValidBylessonId(lessonId);
    }

    public List<Map<String, Object>> getLessonBookList(Long lessonId) {
        return gcLessonBookMapper.selectLessonBookList(lessonId);

    }

    public GcLessonBook selectByBookNo(String bookNo) {
        return gcLessonBookMapper.selectByBookNo(bookNo);
    }

    public List<Map<String, Object>> getBookInfo(Map<String, Object> param) {

        return gcLessonBookMapper.selectBookInfo(param);
    }

    public List<DataMap> getLessonBookInfoList(Long lessonId) {
        return gcLessonBookMapper.selectBookInfoList(lessonId);

    }

    @Transactional(readOnly = true)
    public ServiceResult groupLessonBookCheck(Long lessonId, Long netUserId, Long venueId){
        GcGeneralBookRule bookRule = gcLessonBookRuleService.getGeneralBookRule(venueId);
        DataMap totalBookNum = gcLessonBookRuleService.getTotalBookNum(bookRule, netUserId);
        Integer validTotalBookNum = gcLessonBookRuleService.selectTodayGroupCourseValidTrade(netUserId, null, venueId);
        // 校验用户是否还有预订次数
        return checkHasResidueNum(totalBookNum,validTotalBookNum,venueId,netUserId);
    }

    private ServiceResult checkHasResidueNum(DataMap totalBookNum, Integer validTotalBookNum,Long venueId,Long netUserId) {
        DataMap dataMap = new DataMap();
        Integer number = 0;
        Integer number2 = 0;
        if (totalBookNum != null){
            Integer num = totalBookNum.getInteger("number");
            if (validTotalBookNum >= num ){
                //超过预定次数
                number = 0;
                dataMap.put("isBook",0);
            }else {
                if (totalBookNum.getBoolean("isBook")){
                    //计次卡 还有预定次数
                    number = num - validTotalBookNum;
                    dataMap.put("isBook",1);
                }else {
                    // 非计次卡 有未支付的课程订单
                    if (gcLessonBookMapper.selectUserLessonBookNum(netUserId,venueId)>0){
                        dataMap.put("isBook",4);
                        dataMap.put("message","您有未支付的订单,请先支付");
                    }else {
                        //非计次卡 不能排队
                        dataMap.put("isBook",2);
                    }
                    number = 0;
                }
            }
            number2 = num;
        }
        // 封装数据
        dataMap.put("canBookNum",number);
        dataMap.put("totalBookNum",number2);
        return new ServiceResult().set("data",dataMap);
    }

    public boolean checkGroupLessonSignState(String bookNo) {
        DataMap info = gcLessonBookMapper.checkGroupLessonSignState(bookNo);
        Date now = new Date();
        if (info != null && info.getDate("lessonDate") != null){
            Date lessonDate = info.getDate("lessonDate");
            String startTime = info.getString("startTime", "0000");
            Date lessonStartTime = DateCalUtil.getDateByOffset(lessonDate, startTime);
            if (lessonStartTime.after(now)){
                String period = info.getString("period", "0");
                Date deadline = DateCalUtil.getDateWithOffset(lessonStartTime, "-" + period + "m");
                if (deadline.after(now)){
                    return true;
                }
            }
        }
        return false;
    }

    public List<GcResvDto> getResvListByDate(Date lessonDate, Long netUserId, Long venueId, Long centerId) {
        return gcLessonBookMapper.selectBookListByDate(netUserId, lessonDate, venueId, centerId);
    }

    public List<GcLessonBook> selectTodayByTelephone(Long netUserId, String telephone, Long venueId, Long centerId) {
        return gcLessonBookMapper.selectTodayByTelephone(netUserId, telephone, venueId, centerId);
    }
}
