# 座位分配算法测试建议

## 测试数据准备

### 1. 座位数据设置
```sql
-- 示例：创建一个包含多个连坐区域的票区
-- 第1排：1-10座 (subRow=1)
-- 第1排：12-20座 (subRow=2) 
-- 第2排：1-15座 (subRow=3)
-- 第3排：1-8座 (subRow=4)

INSERT INTO stock_seat (stock_id, row, seat, sub_row, state) VALUES
-- 第1排连坐区域1
(1001, 1, 1, 1, '1'), (1001, 1, 2, 1, '1'), (1001, 1, 3, 1, '1'), 
(1001, 1, 4, 1, '1'), (1001, 1, 5, 1, '1'), (1001, 1, 6, 1, '1'),
(1001, 1, 7, 1, '1'), (1001, 1, 8, 1, '1'), (1001, 1, 9, 1, '1'), (1001, 1, 10, 1, '1'),

-- 第1排连坐区域2 (中间有间隔)
(1001, 1, 12, 2, '1'), (1001, 1, 13, 2, '1'), (1001, 1, 14, 2, '1'),
(1001, 1, 15, 2, '1'), (1001, 1, 16, 2, '1'), (1001, 1, 17, 2, '1'),
(1001, 1, 18, 2, '1'), (1001, 1, 19, 2, '1'), (1001, 1, 20, 2, '1'),

-- 第2排大连坐区域
(1001, 2, 1, 3, '1'), (1001, 2, 2, 3, '1'), (1001, 2, 3, 3, '1'),
(1001, 2, 4, 3, '1'), (1001, 2, 5, 3, '1'), (1001, 2, 6, 3, '1'),
(1001, 2, 7, 3, '1'), (1001, 2, 8, 3, '1'), (1001, 2, 9, 3, '1'),
(1001, 2, 10, 3, '1'), (1001, 2, 11, 3, '1'), (1001, 2, 12, 3, '1'),
(1001, 2, 13, 3, '1'), (1001, 2, 14, 3, '1'), (1001, 2, 15, 3, '1'),

-- 第3排小连坐区域
(1001, 3, 1, 4, '1'), (1001, 3, 2, 4, '1'), (1001, 3, 3, 4, '1'),
(1001, 3, 4, 4, '1'), (1001, 3, 5, 4, '1'), (1001, 3, 6, 4, '1'),
(1001, 3, 7, 4, '1'), (1001, 3, 8, 4, '1');
```

### 2. 票务数据设置
```sql
-- 团体票数据 (8人团体)
INSERT INTO trade_ticket_person (id, ticket_id, ...) VALUES
(1, 1001, ...), (2, 1002, ...), (3, 1003, ...), (4, 1004, ...),
(5, 1005, ...), (6, 1006, ...), (7, 1007, ...), (8, 1008, ...);

-- 套票数据 (4人套票)
INSERT INTO trade_ticket_person (id, ticket_id, ...) VALUES
(11, 2001, ...), (12, 2001, ...), (13, 2001, ...), (14, 2001, ...);

-- 套票数据 (2人套票)
INSERT INTO trade_ticket_person (id, ticket_id, ...) VALUES
(21, 3001, ...), (22, 3001, ...);

-- 单人票数据
INSERT INTO trade_ticket_person (id, ticket_id, ...) VALUES
(31, 4001, ...), (32, 4002, ...), (33, 4003, ...);
```

## 测试用例

### 测试用例1：团体票优先分配
**目标**: 验证团体票从最小排号和座位号开始连续分配

**期望结果**:
- 8人团体票应该分配到：1排1-8座
- 分配顺序：1排1座 → 1排2座 → ... → 1排8座

### 测试用例2：套票连坐优化
**目标**: 验证套票尽可能在连坐区域内分配

**期望结果**:
- 4人套票应该分配到：1排9-10座 + 1排12-13座 (同排不同连坐区域)
- 或者分配到：2排1-4座 (单个连坐区域内)

### 测试用例3：混合分配场景
**输入**:
- 1个8人团体票
- 2个4人套票  
- 1个2人套票
- 3个单人票

**期望结果**:
1. 8人团体票：1排1-8座
2. 第一个4人套票：1排9-10座 + 1排12-13座
3. 第二个4人套票：1排14-17座
4. 2人套票：1排18-19座
5. 单人票：填补剩余座位

### 测试用例4：座位不足场景
**目标**: 验证异常处理

**输入**: 总需求座位数 > 可用座位数

**期望结果**: 抛出 `ServiceException("可分配座位不足")`

## 验证方法

### 1. 单元测试
```java
@Test
public void testTeamTicketAssignment() {
    // 准备测试数据
    List<TicketPersonForSeat> teamPersons = createTeamTicketData(8);
    List<StockSeat> availableSeats = createSeatData();
    
    // 执行分配
    projectSeatService.takeSeat(takeSeatLog);
    
    // 验证结果
    List<StockSeat> assignedSeats = getAssignedSeats();
    assertEquals(8, assignedSeats.size());
    
    // 验证连续性
    for (int i = 1; i < assignedSeats.size(); i++) {
        StockSeat prev = assignedSeats.get(i-1);
        StockSeat curr = assignedSeats.get(i);
        assertTrue(isConsecutive(prev, curr));
    }
}

@Test
public void testGroupTicketOptimization() {
    // 测试套票连坐优化
}
```

### 2. 集成测试
```java
@Test
@Transactional
public void testFullSeatAssignmentProcess() {
    // 创建完整的测试场景
    String performId = "TEST_PERFORM_001";
    Long stockId = 1001L;
    
    // 执行完整的座位分配流程
    projectSeatService.takeSeat(performId, new Long[]{stockId}, staff);
    
    // 验证数据库状态
    verifyDatabaseState(stockId);
}
```

### 3. 日志验证
检查日志输出，确认分配过程：
```
Team ticket assignment successful, team size: 8, assigned seats: 1排1座, 1排2座, 1排3座, 1排4座, 1排5座, 1排6座, 1排7座, 1排8座
Group ticket assignment successful, group size: 4, assigned seats: 1排9座, 1排10座, 1排12座, 1排13座
Seat assignment completed, stock ID: 1001, assigned seats count: 25
```

### 4. 数据库验证
```sql
-- 验证座位分配状态
SELECT row, seat, person_id, state 
FROM stock_seat 
WHERE stock_id = 1001 
ORDER BY row, seat;

-- 验证任务状态
SELECT state, finish_time 
FROM take_seat_log 
WHERE stock_id = 1001;
```

## 性能测试

### 大数据量测试
- 1000个座位
- 100个团体票/套票
- 验证分配时间 < 5秒

### 并发测试
- 多个线程同时分配不同票区
- 验证 Redis 锁机制有效性

## 回归测试检查点

1. ✅ 团体票从最小排号开始分配
2. ✅ 套票优先在连坐区域内分配
3. ✅ 大套票优先分配（按人数降序）
4. ✅ 座位状态正确更新为 "已分配"
5. ✅ 任务状态更新为 "成功"
6. ✅ 异常情况正确处理
7. ✅ 日志信息完整准确
8. ✅ 事务回滚机制正常
