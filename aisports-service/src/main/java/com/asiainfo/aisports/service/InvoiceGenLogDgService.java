package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.InvoiceGenLogInfo;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.pay.client.DigestUtils;
import com.asiainfo.aisports.persistence.core.InvoiceApplyDetailDgMapper;
import com.asiainfo.aisports.persistence.core.InvoiceGenLogMapper;
import com.asiainfo.aisports.persistence.core.InvoicePrintLogExtraMapper;
import com.asiainfo.aisports.persistence.core.InvoicePrintLogRelationMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class InvoiceGenLogDgService {
    @Autowired
    InvoiceGenLogMapper invoiceGenLogMapper;
    @Autowired
    SequenceWrapper sequenceWrapper;

    @Autowired
    InvoicePrintLogRelationMapper invoicePrintLogRelationMapper;
    @Autowired
    InvoicePrintLogDgService invoicePrintLogDgService;

    @Autowired
    InvoiceInfoService invoiceInfoService;

    @Autowired
    private InvoicePrintLogExtraMapper invoicePrintLogExtraMapper;

    @Autowired
    private InvoiceApplyDetailDgMapper invoiceApplyDetailDgMapper;

    /**
     * 保存到发票生成日志表
     *
     * @param record
     * @return
     */
    @Transactional
    public int saveInvoiceGenLog(InvoiceGenLog record) {
        record.setInvoiceCode(DigestUtils.md5DigestAsHex(String.valueOf(record.getInvoiceId()).getBytes(StandardCharsets.UTF_8)));
        return invoiceGenLogMapper.insert(record);
    }


    /**
     * 根据网络用户的手机号查询网络用户发票生成流水
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> selectByNetUserPhone(Map<String, Object> param, Integer pageNo, Integer pageSize) {
        return invoiceGenLogMapper.selectByNetUserPhone(param, new RowBounds(pageNo, pageSize));
    }

    /**
     * 通过发票号等信息查看发票补录数据
     *
     * @param param
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> getInvoiceGenLogByTaxNo(Map<String, Object> param, Integer pageNo, Integer pageSize) {
        return invoiceGenLogMapper.selectByTaxNoParam(param, new RowBounds(pageNo, pageSize));
    }

    @Transactional(readOnly = true)
    public List<DataMap> findByParam(Map<String, Object> param, Integer pageNo, Integer pageSize) {
        return invoiceGenLogMapper.selectByFieldsAll(param, new RowBounds(pageNo, pageSize));
    }

    @Transactional(readOnly = true)
    public List<DataMap> findByPhoneNum(Map<String, Object> param, Integer pageNo, Integer pageSize) {
        return invoiceGenLogMapper.selectByPhoneNum(param, new RowBounds(pageNo, pageSize));
    }

    @Transactional
    public void makeOutInvoice(InvoiceApplyLog invoiceApplyLog, String taxNo, Long expressId, String expressNo, LoginStaff staff) {
        InvoicePrintLog invoicePrintLogParam = new InvoicePrintLog();
        invoicePrintLogParam.setTaxNo(taxNo);
        invoicePrintLogParam.setPrintStaffId(staff.getStaffId());
        invoicePrintLogParam.setInvoiceTitle(invoiceApplyLog.getInvoiceTitle());
        invoicePrintLogParam.setTotalFee(invoiceApplyLog.getAmount());
        invoicePrintLogParam.setIdentifyNum(invoiceApplyLog.getIdentifyNum());
        invoicePrintLogParam.setRemark(invoiceApplyLog.getRemark());
        invoicePrintLogParam.setApplyId(invoiceApplyLog.getId());
        Long printId = invoicePrintLogDgService.saveInvoicePrintLog(invoicePrintLogParam);

        List<InvoiceApplyDetail> invoiceApplyDetails = invoiceApplyDetailDgMapper.selectByApplyId(invoiceApplyLog.getId());
        List<InvoiceGenLogInfo> invoiceGenLogInfos = invoiceApplyDetails.stream().map(invoiceApplyDetail -> {
            InvoiceGenLogInfo invoiceGenLogInfo = new InvoiceGenLogInfo();
            invoiceGenLogInfo.setInvoiceId(invoiceApplyDetail.getInvoiceId());
            invoiceGenLogInfo.setFee(invoiceApplyDetail.getAmount());
            return invoiceGenLogInfo;
        }).collect(Collectors.toList());

        savePrintLogRelation(invoiceGenLogInfos, printId);
    }

    /**
     * 直接开票
     *
     * @param invoiceIds
     * @param invoicePrintLogParam
     * @param staff
     */
    @Transactional
    public void makeOutInvoiceDirectly(String invoiceIds, InvoicePrintLog invoicePrintLogParam, LoginStaff staff,String invoiceType) {
        List<InvoiceGenLogInfo> invoiceGenLogsSelected = saveInvoiceGenLogInfos(invoiceIds, invoicePrintLogParam.getTotalFee());
        Long printId = invoicePrintLogDgService.saveInvoicePrintLog(invoicePrintLogParam);
        saveInvoiceInfo(invoicePrintLogParam, staff);
        savePrintLogRelation(invoiceGenLogsSelected, printId);
        // 保存直接开票的发票类型
        saveInvoicePrintLogAttr(printId,invoiceType);
    }

    /**
     * 根据开票金额获取开票的收据集合（根据申请日期拆分金额）
     *
     * @param invoiceIds
     * @param totalFee
     * @return
     */
    @Transactional
    public List<InvoiceGenLogInfo> saveInvoiceGenLogInfos(String invoiceIds, Long totalFee) {
        // 获取收据集合
        List<InvoiceGenLog> invoiceGenLogsForSelected = invoiceGenLogMapper.selectInvoiceGenLogListByIds(Arrays.asList(invoiceIds.split(",")));

        // 获取拆分金额后实际的开票收据集合
        return this.saveInvoiceGenLogInfos(totalFee, invoiceGenLogsForSelected);
    }

    /**
     * 查询发票生成日志表
     *
     * @param paramMap
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceGenLog> queryInvoiceGenLogList(Map<String, Object> paramMap) {
        return invoiceGenLogMapper.selectInvoiceGenLogList(paramMap);
    }

    @Transactional
    public void makeOutInvoiceFreely(Map<String, Object> paramMap, InvoicePrintLog invoicePrintLogParam, LoginStaff staff) {
        List<InvoiceGenLog> invoiceGenLogsForSelected = invoiceGenLogMapper.selectInvoiceGenLogList(paramMap);
        List<InvoiceGenLogInfo> invoiceGenLogsSelected = saveInvoiceGenLogInfos(invoicePrintLogParam.getTotalFee(), invoiceGenLogsForSelected);
        Long printId = invoicePrintLogDgService.saveInvoicePrintLog(invoicePrintLogParam);
        saveInvoiceInfo(invoicePrintLogParam, staff);
        savePrintLogRelation(invoiceGenLogsSelected, printId);
    }

    private void savePrintLogRelation(List<InvoiceGenLogInfo> invoiceGenLogsSelected, Long printId) {
        Long[] relationId = sequenceWrapper.invoicePrintRelationSequence(invoiceGenLogsSelected.size());
        List<InvoicePrintLogRelation> invoicePrintLogRelationList = Lists.newArrayList();
        for (int i = 0; i < invoiceGenLogsSelected.size(); i++) {
            InvoicePrintLogRelation invoicePrintLogRelation = new InvoicePrintLogRelation();
            invoicePrintLogRelation.setPrintId(printId);
            invoicePrintLogRelation.setRelationId(relationId[i]);
            invoicePrintLogRelation.setInvoiceId(invoiceGenLogsSelected.get(i).getInvoiceId());
            invoicePrintLogRelation.setFee(invoiceGenLogsSelected.get(i).getFee());
            invoicePrintLogRelation.setCancelTag(Constants.Tag.NO);
            invoicePrintLogRelationList.add(invoicePrintLogRelation);
        }
        invoicePrintLogRelationMapper.insertBatch(invoicePrintLogRelationList);
    }

    private void saveInvoiceInfo(InvoicePrintLog invoicePrintLogParam, LoginStaff staff) {
        if (!Strings.isNullOrEmpty(invoicePrintLogParam.getInvoiceTitle())) {
            InvoiceInfo invoiceInfoParam = new InvoiceInfo();
            invoiceInfoParam.setInvoiceTitle(invoicePrintLogParam.getInvoiceTitle());
            invoiceInfoParam.setIdentifyNum(invoicePrintLogParam.getIdentifyNum());
            invoiceInfoService.saveInvoiceInfo(invoiceInfoParam, staff);
        }
    }

    /**
     * 获取拆分后实际开票的收据集合
     *
     * @param totalFee
     * @param invoiceGenLogsForSelected
     * @return
     */
    private List<InvoiceGenLogInfo> saveInvoiceGenLogInfos(Long totalFee, List<InvoiceGenLog> invoiceGenLogsForSelected) {
        List<InvoiceGenLogInfo> invoiceGenLogsSelected = Lists.newArrayList();

        // 根据收据开具时间排序
        List<InvoiceGenLog> sortedInvoiceGenLog = invoiceGenLogsForSelected.stream().sorted(Comparator.comparing(InvoiceGenLog::getOperateTime)).collect(Collectors.toList());
        for (InvoiceGenLog invoiceGenLog : sortedInvoiceGenLog) {
            if (totalFee <= 0) {
                break;
            }

            // 获取收据可开票金额
            Long balance = invoiceGenLog.getFpayFee() - (invoiceGenLog.getRefundMoney() == null ? 0L : invoiceGenLog.getRefundMoney()) - (invoiceGenLog.getPrintedMoney() == null ? 0L : invoiceGenLog.getPrintedMoney());
            Long fee = totalFee >= balance ? balance : totalFee;

            InvoiceGenLogInfo invoiceGenLogInfo = new InvoiceGenLogInfo(invoiceGenLog);
            invoiceGenLogInfo.setPrintedMoney((invoiceGenLog.getPrintedMoney() == null ? 0L : invoiceGenLog.getPrintedMoney()) + fee);
            invoiceGenLogInfo.setFee(fee);
            invoiceGenLogsSelected.add(invoiceGenLogInfo);

            totalFee -= balance;
        }
        if (totalFee > 0) {
            throw new IllegalArgumentException("订单可开票金额不足");
        }

        if (invoiceGenLogsSelected.isEmpty()) {
            throw new IllegalArgumentException("订单无可开票数据");
        }

        invoiceGenLogMapper.batchUpdate(invoiceGenLogsSelected);

        return invoiceGenLogsSelected;
    }

    /**
     * 保存打印发票的类型
     *
     * @param printId
     * @param invoiceType
     */
    public void saveInvoicePrintLogAttr(Long printId,String invoiceType){
        InvoicePrintLogExtra invoicePrintLogExtra = new InvoicePrintLogExtra();
        invoicePrintLogExtra.setPrintId(printId);
        invoicePrintLogExtra.setExtraKey("invoiceType");
        invoicePrintLogExtra.setValue(invoiceType);
        invoicePrintLogExtraMapper.insert(invoicePrintLogExtra);
    }

    public void cancelInvoiceApply(Long applyId) {
        invoiceGenLogMapper.cancelByApplyId(applyId);
    }
}
