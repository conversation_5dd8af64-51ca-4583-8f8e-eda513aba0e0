package com.asiainfo.aisports.scheduler.service.humanResource.resolver.v2;

import com.asiainfo.aisports.domain.core.SalaryCalcExprParam;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.persistence.core.StaffMapper;
import com.asiainfo.aisports.persistence.core.TradeMapper;
import com.asiainfo.aisports.scheduler.service.humanResource.resolver.VariableResolver;
import com.asiainfo.aisports.tools.TradeConstants;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * 会籍销售
 *
 * @auther: zhouwei
 * @date: 2022/5/16 17:31
 */
@Component
public class ConsultantSaleVariableResolver implements VariableResolver {

    // 1-办专项卡, 2-退专项卡，3-课程报名，4-课程退课
    private static final String TRADE_TYPE_CODE_OPEN_CARD = "1";
    private static final String TRADE_TYPE_CODE_CARD_WITHDRAW = "2";
    private static final String TRADE_TYPE_CODE_COURSE_ENROLL = "3";
    private static final String TRADE_TYPE_CODE_COURSE_WITHDRAW = "4";

    private static final String NOT_FILTERED = "1";//全选


    private static final String SUM_BY_STAFF = "1";//个人业绩
    private static final String SUM_BY_STAFF_DEPT = "2";//部门业绩

    @Autowired
    private TradeMapper tradeMapper;
    @Autowired
    private StaffMapper staffMapper;

    /**
     * aviatorVars.put("staffId", calcStaffId);
     * aviatorVars.put("salaryStartDate", parseSalaryDate(item.getStartIndex(), item.getStartDate(), salaryMonth));
     * aviatorVars.put("salaryEndDate", parseSalaryDate(item.getEndIndex(), item.getEndDate(), salaryMonth));
     * aviatorVars.put("salaryMonth", salaryMonth);
     * aviatorVars.put("payRoll", item.getPayRoll());// 计薪方式
     * aviatorVars.put("centerId", staff.getCenterId());
     * aviatorVars.put("exprParams", exprParams);
     * <p>
     * <p>
     * tradeTypeCode ： 业务类型,0-办专项卡,1-退专项卡,2-课程报名,3-课程退课
     * serviceId ： 服务项目,多选 -1 全部
     * sumBy： 业绩统计口径,0-个人业绩，1-部门业绩
     * belong：业绩归属, 0-发展人 1-会籍顾问
     * tradePay：订单支付类型, -1-全部支付类型, 0-非专项卡支付订单
     * outPut：输出参数, 0-金额
     *
     * @param vars
     * @return
     */
    @Override
    public Object resolve(Map<String, Object> vars) {
        // 计薪方式
        Long staffId = MapUtils.getLong(vars, "staffId");
        SalaryCalcExprParam exprParams = (SalaryCalcExprParam) MapUtils.getObject(vars, "exprParams");
        String exprParam = exprParams.getExprParam();
        JSONObject jsonObject = JSONObject.fromObject(exprParam);
        String[] tradeTypeCodes = MapUtils.getString(jsonObject, "tradeTypeCode").split(",");
        String serviceId = MapUtils.getString(jsonObject, "serviceId");
        String sumBy = MapUtils.getString(jsonObject, "sumBy");
        String belong = MapUtils.getString(jsonObject, "belong");
        String tradePay = MapUtils.getString(jsonObject, "tradePay");
        String outPut = MapUtils.getString(jsonObject, "outPut");

        ParamMap param = new ParamMap();
        param.putAll(vars);
        StringBuilder tradeTypeCodeBuilder = new StringBuilder();
        for (String code : tradeTypeCodes) {
            if (TRADE_TYPE_CODE_OPEN_CARD.equals(code)) {
                tradeTypeCodeBuilder.append(TradeConstants.TradeTypeCode.OPEN_CARD).append(",").append(TradeConstants.TradeTypeCode.BATCH_CARD_TRADE).append(",");
            } else if (TRADE_TYPE_CODE_CARD_WITHDRAW.equals(code)) {
                tradeTypeCodeBuilder.append(TradeConstants.TradeTypeCode.CARD_WITHDRAW).append(",");
            } else if (TRADE_TYPE_CODE_COURSE_ENROLL.equals(code)) {
                tradeTypeCodeBuilder.append(TradeConstants.TradeTypeCode.COURSE_ENROLL).append(",").append(TradeConstants.TradeTypeCode.PRIVATE_COURSE_ENROLL).append(",");
            } else if (TRADE_TYPE_CODE_COURSE_WITHDRAW.equals(code)) {
                tradeTypeCodeBuilder.append(TradeConstants.TradeTypeCode.REFUND_COURSE).append(",");
            }
        }
        if (tradeTypeCodeBuilder.lastIndexOf(",") != -1) {
            param.put("tradeTypeCode", tradeTypeCodeBuilder.substring(0, tradeTypeCodeBuilder.lastIndexOf(",")));
        }
        if (!NOT_FILTERED.equals(serviceId)) {
            param.put("serviceId", serviceId);
        }

        param.put("sumBy", sumBy);
        if (SUM_BY_STAFF_DEPT.equals(sumBy)) {
            param.put("deptId", staffMapper.selectByPrimaryKey(staffId).getDeptId());
        }
        param.put("belong", belong);
        if (!NOT_FILTERED.equals(tradePay)) {
            param.put("tradePay", tradePay);
        }
        // 细化业务类型
        BigDecimal percent = BigDecimal.valueOf(100);
        return BigDecimal.valueOf(tradeMapper.resolveConsultantSaleOutPut0(param)).divide(percent,2, RoundingMode.HALF_UP).doubleValue();
    }
}