<template>
    <div class="title-box">
        <div class="sub-box">
            <div
                ref="searchLine"
                class="search-line">
                <select
                    v-model="serviceSelect"
                    class="qselect"
                    placeholder="请选择项目"
                    @change="changeService">
                    <option
                        v-for="service in serviceList"
                        :value="service.serviceId">{{ service.serviceName }}</option>
                </select>
                <span ref="coachWrap">
                    <select
                        v-model="shadowCoachSelect"
                        class="qselect"
                        placeholder="请选择教练">
                        <option
                            v-for="coach in coachList"
                            :value="coach.coachId">{{ coach.coachName }}</option>
                    </select>
                </span>
                <button
                    class="btn btn-md btn-primary"
                    @click="queryCoachSchedules">查询</button>
            </div>
            <div
                v-if="showPlaceholder"
                class="search-placeholder">
                <div class="search-placeholder-inner">
                    <i class="iconfont icon-chazhaoyonghu"></i>
                    <span class="placeholder-text">请选择一个教练</span>
                </div>
            </div>
            <template v-else>
                <div class="operation-line">
                    <div class="date-week-selector">
                        <div
                            class="date-week-selector-prev"
                            @click="goPrevWeek">
                            <i class="iconfont icon-xiangzuo"></i>
                        </div>
                        <div class="date-week-selector-text">{{ startDate }}～{{ endDate }}</div>
                        <div
                            class="date-week-selector-next"
                            @click="goNextWeek">
                            <i class="iconfont icon-xiangyou"></i>
                        </div>
                    </div>
                    <div class="operation-btns">
                        <button
                            v-if="operationState==='view'"
                            class="btn btn-md btn-default add-btn-gray"
                            @click="operate">
                            <i class="iconfont icon-bianji"></i>
                            <span>编辑</span>
                        </button>
                        <button
                            v-if="operationState==='edit'"
                            class="btn btn-md btn-default add-btn-gray"
                            @click="cancelOperations">
                            <span>取消</span>
                        </button>
                        <button
                            class="btn btn-md btn-default add-btn-gray"
                            @click="copyLastWeek">
                            <i class="iconfont icon-fuzhi"></i>
                            <span>复制上周课程</span>
                        </button>
                    </div>
                </div>

                <div
                    :class="{viewing: operationState ==='view'}"
                    class="course-schedule-table">
                    <div class="course-schedule-table-hd">
                        <span
                            :class="{disabled: decTimeDisabled()}"
                            class="left"
                            @click="decTime"></span>
                        <ol class="course-schedule-times">
                            <li
                                v-for="hour in renderHours"
                                class="course-schedule-time-item">
                                {{ hour }}
                            </li>
                        </ol>
                        <span
                            :class="{disabled: incTimeDisabled()}"
                            class="right"
                            @click="incTime"></span>
                    </div>
                    <div class="course-schedule-table-bd">
                        <dl
                            v-for="(schedulesday,i) in renderSchedules"
                            class="course-schedule-day">
                            <dt class="course-schedule-day-title">
                                {{ schedulesday.date }}
                            </dt>
                            <dd class="course-schedule-day-content">
                                <ul class="course-schedules">
                                    <li
                                        v-for="schedule in schedulesday.schedules"
                                        :class="getScheduleClass(schedules[i].schedules, schedule)"
                                        class="course-schedule-item">
                                        <ul
                                            v-if="schedule.showContextMenu"
                                            class="action-select-box">
                                            <li
                                                class="action-select"
                                                @click="renderEditBox(schedules[i].schedules, schedule, $event)">编辑</li>
                                            <li
                                                class="action-select"
                                                @click="deleteSchedule(schedules[i].schedules, schedule, $event)">删除</li>
                                        </ul>
                                        <course-detail-pop-box
                                            v-if="schedule.showEditBox"
                                            :schedule="schedule"
                                            :level-list="levelList"
                                            :course-list="courseList"
                                            @submit="saveDetailBox"
                                            @cancel="cancelDetailBox">
                                        </course-detail-pop-box>

                                        <div
                                            class="capacity"
                                            @click="editSchedule(schedules[i].schedules, schedule)">
                                            <template v-if="schedule.capacity">
                                                {{ schedule.capacity }}人
                                            </template>
                                        </div>
                                    </li>
                                </ul>
                            </dd>
                        </dl>
                    </div>
                </div>

                <div
                    v-if="operationState==='edit'"
                    class="submit-line">
                    <button
                        class="btn btn-lg btn-primary btn-submit"
                        @click="submit">保存</button>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import { initSelectPicker } from 'utils/initPlugins'
import _ from 'lodash'
import CourseDetailPopBox from './CourseDetailPopBox.vue'
import getUrlQuery from 'utils/getUrlQuery'
import moment from 'moment'
import 'utils/confirm'
import { showSuccess, showWrong, showWarning } from 'mAlert'

function getSchedule(trainingCourseHours, date, hour) {
    const startHourStr = time2back(hour.start)
    const endHourStr = time2back(hour.end)

    const schedule = getDefaultSchedule(date.date, hour.start, hour.end)

    trainingCourseHours.forEach((trainingHour) => {
        if (trainingHour.trainingDate.substr(0, 10) === date.date) {
            if (startHourStr >= trainingHour.startTime &&
                           endHourStr <= trainingHour.endTime) {
                // schedule.id = trainingHour.id
                _.extend(schedule, trainingHour)
                schedule.scheduled = true
                if (startHourStr === trainingHour.startTime) {
                    schedule.first = true
                }
                if (endHourStr === trainingHour.endTime) {
                    schedule.last = true
                }
            }
        }
    })
    return schedule
}

function time2back(time) {
    return time.substr(0, 2) + time.substr(3, 2)
}

function getScheduleHours(hours) {
    const scheduleHours = []

    for (let i = 0; i < hours.length - 1; i++) {
        const hour = hours[i]
        const hm = moment(hour, 'HH:mm')

        hm.add(30, 'minutes')
        scheduleHours.push({
            start: hour,
            end: hm.format('HH:mm'),
        })
        scheduleHours.push({
            start: hm.format('HH:mm'),
            end: hours[i + 1],
        })
    }

    return scheduleHours
}

function getHeadSchedule(scheduleList, schedule) {
    let index = scheduleList.indexOf(schedule)
    while (scheduleList[index - 1] && scheduleList[index - 1].id === schedule.id) {
        index -= 1
    }

    return scheduleList[index]
}

// 新增的id设置为负值， 方便编辑， 提交时不传
const getAddId = (function () {
    let index = -1
    return function () {
        return index--
    }
}())

function patchData(schedules, trainingCourseHours) {
    const data = _.chain(schedules)
        .map(scs => scs.schedules)
        .flatten()
        .filter(sc => typeof sc.id !== 'undefined')
        .groupBy(sc => sc.date + sc.id)
        .toPairs()
        .map((pair) => {
            const schedule = pair[1]
            const data = {
                tag: schedule[0].id < 0 ? 'add' : 'update',
                trainingDate: schedule[0].date,
                startTime: time2back(schedule[0].startHour),
                endTime: time2back(schedule[schedule.length - 1].endHour),
                trainingLevel: schedule[0].trainingLevel,
                capacity: schedule[0].capacity,
                courseId: schedule[0].courseId,
            }
            if (schedule[0].id >= 0) {
                data.id = schedule[0].id
            }

            return data
        })
        .value()

    trainingCourseHours.forEach((scheduleOriginal) => {
        const schedule = _.find(data, { id: scheduleOriginal.id })
        if (!schedule) {
            data.push({
                tag: 'delete',
                id: scheduleOriginal.id,
            })
        } else if (schedule.trainingLevel === scheduleOriginal.trainingLevel
                              && schedule.capacity === scheduleOriginal.capacity
                              && schedule.courseId === scheduleOriginal.courseId) {
            schedule.tag = 'unModified'
        }
    })

    const schedulePatched = data.filter(sc => !(sc.tag === 'unModified'))

    // console.log(schedulePatched)
    return schedulePatched
}

function sub7days(day) {
    return moment(day, 'YYYY/MM/DD').subtract(7, 'day').format('YYYY-MM-DD')
}

function add7days(day) {
    return moment(day.substr(0, 10), 'YYYY-MM-DD').add(7, 'day').format('YYYY-MM-DD')
}

const weekdays = ['日', '一', '二', '三', '四', '五', '六']

function getDefaultSchedule(date, start, end) {
    return {
        showContextMenu: false,
        showEditBox: false,
        editStart: false,
        editEnd: false,
        startHour: start,
        endHour: end,
        date,
    }
}

function copySchedules(schedules) {
    return _.map(schedules, scheduleDate => ({
        date: scheduleDate.date,
        schedules: _.map(scheduleDate.schedules, _.clone),
    }))
}

const maxTimeSection = 8
// 用来记录class和颜色的关联
const themes = ['cyan', 'green', 'orange', 'magenta', 'purple', 'tan', 'blueness']
const colors = []

function getCurrentWeek() {
    if (moment().day() === 0) {
        return {
            startDate: moment().subtract(6, 'days').format('YYYY/MM/DD'),
            endDate: moment().format('YYYY/MM/DD'),
        }
    }
    return {
        startDate: moment().startOf('week').add(1, 'days').format('YYYY/MM/DD'),
        endDate: moment().endOf('week').add(1, 'days').format('YYYY/MM/DD'),
    }

}

export default {
    components: {
        CourseDetailPopBox,
    },
    data() {
        return {
            serviceList: [],
            coachList: [],

            coachSelect: '',
            serviceSelect: '',

            shadowCoachSelect: '',
            shadowMaxStartTime: '08:00',
            shadowMaxEndTime: '19:00',

            termId: getUrlQuery('termId'),
            startDate: getCurrentWeek().startDate,
            endDate: getCurrentWeek().endDate,

            levelList: [],
            maxStartTime: '08:00',
            maxEndTime: '19:00',
            startTime: '',
            endTime: '',

            hours: [],
            renderHours: [],
            schedules: [],
            renderSchedules: [],
            courseList: [],

            trainingCourseHours: [],
            showPlaceholder: true,
            // 'edit', 'view'
            operationState: 'edit',
        }
    },
    watch: {
        maxStartTime() {
            this.getRenderTimeLine()
        },
        maxEndTime() {
            this.getRenderTimeLine()
        },
        startTime() {
            this.getRenderTimeLine()
        },
        renderHours() {
            this.getRenderSchedules()
        },
        schedules() {
            this.getRenderSchedules()
        },
    },
    created() {
        this.getInitData()
            .then(this.initSearchLine)
            .then(() => {
                // this.hours = this.getTimeLine(this.startTime, this.endTime)
                if (this.coachSelect) {
                    this.queryCoachSchedules()
                }
            })
    },
    methods: {
        getScheduleClass(scheduleList, schedule) {
            const emptySchedules = this.getEmptySchedules(scheduleList, schedule)
            const scheduleIndex = emptySchedules.indexOf(schedule)
            const section = this.getAddSection(emptySchedules)

            const startIndex = section.startIndex
            const endIndex = section.endIndex

            let selected = false
            let first = false
            let last = false

            if (startIndex >= 0 && endIndex >= 0) {
                if (scheduleIndex >= startIndex && scheduleIndex <= endIndex) {
                    selected = true
                }
                if (scheduleIndex === startIndex) {
                    first = true
                }
                if (scheduleIndex === endIndex) {
                    last = true
                }
            }

            const colorTheme = {}
            if (schedule.id && schedule.courseId) {
                const currentTheme = _.find(colors, { courseId: schedule.courseId })
                if (currentTheme) {
                    colorTheme[currentTheme.theme] = true
                } else {
                    const newTheme = themes[colors.length]
                    colorTheme[newTheme] = true
                    colors.push({
                        theme: newTheme,
                        courseId: schedule.courseId,
                    })
                }
            }

            return {
                ...colorTheme,
                selected: schedule.id || selected,
                first: schedule.first || first,
                last: schedule.last || last,
                editing: schedule.editStart || schedule.editEnd,
                deleted: schedule.deleted,
            }
        },
        getAddSection(scheduleList) {
            let startIndex = _.findIndex(scheduleList, sc => sc.editStart)
            let endIndex = _.findIndex(scheduleList, sc => sc.editEnd)

            if (startIndex > endIndex) {
                const temp = startIndex
                startIndex = endIndex
                endIndex = temp
            }

            return {
                startIndex,
                endIndex,
            }
        },
        getInitData() {
            return $.get('/coachCourse/init/data', (res) => {
                this.serviceList = res.serviceList
                this.coachList = res.coachList

                this.levelList = res.levelList

                if (res.serviceList && res.serviceList.length > 0) {
                    this.serviceSelect = res.serviceList[0].serviceId
                }
                if (res.coachList && res.coachList.length > 0) {
                    this.shadowCoachSelect = (res.coachList[0] || {}).coachId;
                }
                this.shadowMaxStartTime = seg2time(res.startSegment)
                this.shadowMaxEndTime = seg2time(res.endSegment)
                this.hours = this.getTimeLinePoints(this.maxStartTime, this.maxEndTime)

                this.startTime = this.maxStartTime
            })
        },
        changeService() {
            $.get('/coachCourse/getDataByService', {
                serviceId: this.serviceSelect,
            }, (res) => {
                this.coachList = res.coachList
                this.shadowCoachSelect = (res.coachList[0] || {}).coachId
                this.shadowMaxStartTime = seg2time(res.startSegment)
                this.shadowMaxEndTime = seg2time(res.endSegment)

                this.initCoachWrap()
            })
        },
        confirmSwicthWeek(callback) {
            if (patchData(this.schedules, this.trainingCourseHours).length > 0) {
                this.$confirm('本周课程已修改未保存，是否继续')
                    .then(callback)
            } else {
                callback()
            }
        },
        goPrevWeek() {
            function subdays(day) {
                return moment(day, 'YYYY/MM/DD').subtract(7, 'day').format('YYYY/MM/DD')
            }

            this.confirmSwicthWeek(() => {
                this.startDate = subdays(this.startDate)
                this.endDate = subdays(this.endDate)

                this.queryCoachSchedules()
            })
        },
        goNextWeek() {
            function adddays(day) {
                return moment(day, 'YYYY/MM/DD').add(7, 'day').format('YYYY/MM/DD')
            }
            this.confirmSwicthWeek(() => {
                this.startDate = adddays(this.startDate)
                this.endDate = adddays(this.endDate)

                this.queryCoachSchedules()
            })
        },
        incTimeDisabled() {
            const startTime = moment(this.startTime, 'HH:mm').add(1, 'hour')
            const endTime = startTime.add(maxTimeSection, 'hour')
            const maxEndTime = moment(this.maxEndTime, 'HH:mm')
            return endTime.isAfter(maxEndTime)
        },
        incTime() {
            const startTime = moment(this.startTime, 'HH:mm').add(1, 'hour')
            const startTimeStr = startTime.format('HH:mm')
            const endTime = startTime.add(maxTimeSection, 'hour')
            const endTimeStr = endTime.format('HH:mm')

            const maxEndTime = moment(this.maxEndTime, 'HH:mm')
            if (endTime.isAfter(maxEndTime)) {
                showWarning('已达到最迟的时间')
                return
            }

            this.startTime = startTimeStr
            this.endTime = endTimeStr
        },
        decTimeDisabled() {
            const startTime = moment(this.startTime, 'HH:mm').subtract(1, 'hour')
            const maxStartTime = moment(this.maxStartTime, 'HH:mm')
            return startTime.isBefore(maxStartTime)
        },
        decTime() {
            const startTime = moment(this.startTime, 'HH:mm').subtract(1, 'hour')
            const maxStartTime = moment(this.maxStartTime, 'HH:mm')
            if (startTime.isBefore(maxStartTime)) {
                showWarning('已达到最早的时间')
                return
            }

            this.startTime = startTime.format('HH:mm')
            const endTime = startTime.add(maxTimeSection)
            const maxEndTime = moment(this.maxEndTime, 'HH:mm')

            if (endTime.isAfter(maxEndTime)) {
                this.endTime = maxEndTime.format('HH:mm')
            } else {
                this.endTime = endTime.format('HH:mm')
            }
        },
        initCoachWrap() {
            this.$nextTick(() => {
                initSelectPicker(this.$refs.coachWrap)
            })
        },
        initSearchLine() {
            this.$nextTick(() => {
                initSelectPicker(this.$refs.searchLine)
            })
        },
        getSchedules(trainingCourseHours) {
            const dates = this.getDays(this.startDate, this.endDate)
            const hours = this.hours

            const schedules = dates.map(date => ({
                date: date.weekdayStr,
                schedules: getScheduleHours(hours)
                    .map(hour => getSchedule(trainingCourseHours, date, hour)),
            }))

            return schedules
        },
        getRenderSchedules() {
            this.renderSchedules = this.schedules.map((schedulesDay) => {
                const schedules = schedulesDay.schedules.filter(sc => moment(sc.startHour, 'HH:mm').isSameOrAfter(moment(this.startTime, 'HH:mm')) && moment(sc.endHour, 'HH:mm').isSameOrBefore(moment(this.endTime, 'HH:mm')))
                return {
                    ...schedulesDay,
                    schedules,
                }
            })
        },
        queryCoachSchedules() {
            if (!this.shadowCoachSelect) {
                showWrong('请选择教练')
                return
            }

            this.coachSelect = this.shadowCoachSelect
            this.maxStartTime = this.shadowMaxStartTime
            this.maxEndTime = this.shadowMaxEndTime
            this.hours = this.getTimeLinePoints(this.maxStartTime, this.maxEndTime)

            $.get('/coachCourse/getCourseByCoachId', {
                coachId: this.coachSelect,
            }, (res) => {
                this.courseList = res.courseList
            }).then((res) => {
                if (this.courseList.length === 0) {
                    showWrong('该教练未配置课程')
                    this.schedules = []
                    return
                }

                $.get('/coachCourse/querySingleCourses', {
                    coachId: this.coachSelect,
                    startDate: this.startDate.replace(/\//g, '-'),
                    endDate: this.endDate.replace(/\//g, '-'),
                }, (res) => {
                    this.trainingCourseHours = res.trainingCourseHours
                    this.schedules = this.getSchedules(res.trainingCourseHours)
                    // 作为备份， 取消保存时可以恢复
                    this.originalSchedules = copySchedules(this.schedules)

                    this.showPlaceholder = false
                    this.operationState = 'view'
                })
            })
        },
        copyLastWeek() {
            const callback = () => {
                $.get('/coachCourse/querySingleCourses', {
                    coachId: this.coachSelect,
                    startDate: sub7days(this.startDate),
                    endDate: sub7days(this.endDate),
                }, (res) => {
                    const trainingCourseHours = res.trainingCourseHours.map(tch => ({
                        ...tch,
                        id: getAddId(),
                        trainingDate: add7days(tch.trainingDate),
                    }))

                    this.schedules = this.getSchedules(trainingCourseHours)
                    this.operationState = 'edit'
                })
            }

            if (this.trainingCourseHours.length > 0 ||
                              patchData(this.schedules, this.trainingCourseHours).length > 0) {
                this.$confirm('复制上周会覆盖本周的课程和已经编辑的课程，是否继续?')
                    .then(callback)
            } else {
                callback()
            }
        },
        getTimeLinePoints(startTime, endTime) {
            const start = moment(startTime, 'HH:mm')
            const end = moment(endTime, 'HH:mm')
            const hours = []
            while (start.diff(end) <= 0) {
                hours.push(start.format('HH:mm'))
                start.add(1, 'hours')
            }

            return hours
        },
        getRenderTimeLine() {
            if (moment(this.startTime, 'HH:mm').isBefore(moment(this.maxStartTime, 'HH:mm'))) {
                this.startTime = this.maxStartTime
            }
            const start = moment(this.startTime, 'HH:mm')
            const end = moment(this.startTime, 'HH:mm').add(maxTimeSection, 'hour')
            const maxEndTime = moment(this.maxEndTime, 'HH:mm')
            if (end.isAfter(maxEndTime)) {
                this.endTime = maxEndTime.format('HH:mm')
            } else {
                this.endTime = end.format('HH:mm')
            }

            // console.log(111111)

            this.renderHours = this.getTimeLinePoints(this.startTime, this.endTime)
        },
        getDays(startDate, endDate) {
            const start = moment(startDate, 'YYYY/MM/DD')
            const end = moment(endDate, 'YYYY/MM/DD')
            const dates = []
            while (!start.isAfter(end)) {
                dates.push({
                    date: start.format('YYYY-MM-DD'),
                    weekday: start.day(),
                    weekdayStr: `${start.format('MM/DD')} 周${weekdays[start.day()]}`,
                })
                start.add(1, 'days')
            }

            return dates
        },
        editSchedule(scheduleList, schedule) {
            if (this.operationState === 'view') {
                return
            }

            this.clearEditStatus()
            if (schedule.id) {
                schedule.showContextMenu = true
            } else {
                this.setAddSchedules(scheduleList, schedule)
            }
        },
        setAddSchedules(scheduleList, schedule) {
            this.clearAddStatus(scheduleList, schedule)

            const emptySchedules = this.getEmptySchedules(scheduleList, schedule)
            const scheduleStart = _.find(emptySchedules, { editStart: true })

            if (!scheduleStart) {
                schedule.editStart = true
            } else {
                emptySchedules.forEach((sc) => { sc.editEnd = false })
                schedule.editEnd = true

                // 解决首尾动画同步问题
                scheduleStart.editStart = false
                setTimeout(() => {
                    scheduleStart.editStart = true

                    const editSection = this.getAddSection(scheduleList)

                    if (editSection.startIndex >= 0 && editSection.endIndex >= 0) {
                        // schedule.showEditBox = true
                        scheduleList[editSection.startIndex].showEditBox = true
                    }
                }, 0)
            }
        },
        // 获取和点击的课程时间连续的空闲时间段
        getEmptySchedules(scheduleList, schedule) {
            const index = scheduleList.indexOf(schedule)
            let startIndex = index
            let endIndex = index

            while (startIndex > 0 && !scheduleList[startIndex - 1].scheduled) {
                startIndex -= 1
            }

            while (endIndex < (scheduleList.length - 1) &&
                                    !scheduleList[endIndex + 1].scheduled) {
                endIndex += 1
            }

            return scheduleList.slice(startIndex, endIndex + 1)
        },
        deleteSchedule(scheduleList, schedule, e) {
            e.stopPropagation()
            schedule.showContextMenu = false
            this.$confirm('确认删除该课程吗？')
                .then(() => {
                    for (let i = 0; i < scheduleList.length; i++) {
                        if (scheduleList[i].id === schedule.id) {
                            scheduleList[i] = {
                                ...getDefaultSchedule(schedule.date, schedule.startHour, schedule.endHour),
                                deleted: true,
                            }
                        }
                    }

                    this.getRenderSchedules()
                    this.$forceUpdate()
                })
        },
        renderEditBox(schedules, schedule, e) {
            e.stopPropagation()
            schedule.showContextMenu = false
            schedule.showEditBox = true

            this.$forceUpdate()
        },
        clearEditStatus() {
            this.schedules.forEach((schedulesDay) => {
                schedulesDay.schedules.forEach((schedule) => {
                    schedule.showContextMenu = false
                    schedule.showEditBox = false
                })
            })
        },
        clearAddStatus(scheduleList, schedule) {
            this.schedules.forEach((schedulesDay) => {
                if (schedulesDay.schedules !== scheduleList) {
                    schedulesDay.schedules.forEach((sc) => {
                        sc.editStart = false
                        sc.editEnd = false
                    })
                }
            })

            if (scheduleList && schedule) {
                const emptySchedules = this.getEmptySchedules(scheduleList, schedule)
                scheduleList.forEach((sc) => {
                    if (emptySchedules.indexOf(sc) < 0) {
                        sc.editStart = false
                        sc.editEnd = false
                    }
                })
            }
        },
        saveDetailBox(schedule, scheduleCp) {
            let list
            if (!this.validateLesson(scheduleCp.courseId, scheduleCp.date)) {
                showWrong('日期超过了所选课程时间范围')
                return
            }

            if (schedule.id) {
                list = _.chain(this.schedules)
                    .map(sc => sc.schedules)
                    .flatten()
                    .filter({ id: schedule.id })
                    .value()
            } else {
                for (let i = 0; i < this.schedules.length; i++) {
                    const section = this.getAddSection(this.schedules[i].schedules)
                    if (section.startIndex >= 0 && section.endIndex >= 0) {
                        list = this.schedules[i].schedules.slice(section.startIndex, section.endIndex + 1)

                        break
                    }
                }
            }

            list.forEach((sc) => {
                sc.capacity = scheduleCp.capacity
                sc.trainingLevel = scheduleCp.trainingLevel
                sc.courseId = scheduleCp.courseId
            })

            if (!schedule.id) {
                list.forEach((sc, i) => {
                    sc.id = getAddId()
                    sc.editStart = false
                    sc.editEnd = false
                    if (i === 0) {
                        sc.first = true
                    }
                    if (i === list.length - 1) {
                        sc.last = true
                    }
                })
            }

            schedule.showEditBox = false
        },
        cancelDetailBox() {
            this.clearAddStatus()
        },
        operate() {
            this.operationState = 'edit'
        },
        cancelOperations() {
            this.operationState = 'view'
            this.schedules = copySchedules(this.originalSchedules)
            this.$forceUpdate()
        },
        validateLesson(courseId, date) {
            const lessonDate = moment(date, 'YYYY-MM-DD')
            const course = _.find(this.courseList, { courseId })
            const courseStart = moment(course.startDate, 'YYYY-MM-DD')
            const courseEnd = moment(course.endDate, 'YYYY-MM-DD')

            return !(lessonDate.isBefore(courseStart) || lessonDate.isAfter(courseEnd))
        },
        validateLessons(lessons) {
            function timeFormat(time) {
                return `${time.substr(0, 2)}:${time.substr(2, 2)}`
            }
            for (let i = 0; i < lessons.length; i++) {
                const lesson = lessons[i]

                if (lesson.courseId && !this.validateLesson(lesson.courseId, lesson.trainingDate)) {
                    const lessonDate = moment(lesson.trainingDate, 'YYYY-MM-DD')
                    showWrong(`${lessonDate.month() + 1}月${
                        lessonDate.date()}日${timeFormat(lesson.startTime)}到${timeFormat(lesson.endTime)}超过了所选课程的时间范围`)
                    return false
                }
            }

            return true
        },
        submit() {
            const data = patchData(this.schedules, this.trainingCourseHours)

            if (!this.validateLessons(data)) {
                return
            }

            $.post('/coachCourse/modifyLesson', {
                coachId: this.coachSelect,
                lessonInfo: JSON.stringify(data),
            }, (res) => {
                showSuccess('修改成功')
                this.queryCoachSchedules()
            })
        },
    },
}
</script>
