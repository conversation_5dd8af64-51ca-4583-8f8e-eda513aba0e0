package com.asiainfo.aisports.service.job;

import com.asiainfo.aisports.annotation.RoutingKey;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.Trade;
import com.asiainfo.aisports.domain.core.TradeBatchRecharge;
import com.asiainfo.aisports.model.CustInfo;
import com.asiainfo.aisports.persistence.core.TradeBatchRechargeMapper;
import com.asiainfo.aisports.persistence.core.TradeMapper;
import com.asiainfo.aisports.service.CustQueryService;
import com.asiainfo.aisports.service.EcardTradeService;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.tools.TradeConstants;
import net.sf.json.JSONObject;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 一卡通批量充值
 */
@Component
public class BatchRechargeJob implements Job {
    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(BatchRechargeJob.class);
    @Autowired
    TradeBatchRechargeMapper tradeBatchRechargeMapper;
    @Autowired
    CustQueryService custQueryService;
    @Autowired
    TradeMapper tradeMapper;
    @Autowired
    EcardTradeService ecardTradeService;

    @Transactional
    @TargetDataSource(name = "{}")
    @Override
    public void execute(@RoutingKey Long centerId, JSONObject params) {
        long tradeId = params.getLong("tradeId");
        Trade trade = tradeMapper.selectByPrimaryKey(tradeId);
        if (trade != null && trade.getPayState().equals(Constants.PayState.PAID)) { //已支付
            //查询待充值记录
            TradeBatchRecharge rechargeParam = new TradeBatchRecharge();
            rechargeParam.setTradeId(tradeId);
            rechargeParam.setState("0");
            List<TradeBatchRecharge> batchRechargeList = tradeBatchRechargeMapper.selectByFields(rechargeParam);

            //用于存放本次执行了哪些主流水下的业务
            for (TradeBatchRecharge batchRecharge : batchRechargeList) {
                //获取必要的用户信息
                CustInfo custInfo = custQueryService.findByEcardNo(batchRecharge.getEcardNo(), trade.getCenterId());
                if (custInfo.getEcardCustId() == null || custInfo.getEcardCustId() == 0) {
                    batchRecharge.setState("2"); //2表示缺少EcardCustId信息
                    tradeBatchRechargeMapper.updateByPrimaryKey(batchRecharge);
                    continue;
                }

                //充值一卡通
                Trade subTrade = new Trade();
                subTrade.setTradeId(tradeId);
                subTrade.setTradeTypeCode(TradeConstants.TradeTypeCode.BATCH_RECHARGE.getLongValue());
                subTrade.setPayTfee(batchRecharge.getRechargeMoney());
                subTrade.setVenueId(trade.getVenueId());
                subTrade.setCenterId(trade.getCenterId());
                subTrade.setTradeStaffId(trade.getTradeStaffId());

                ServiceResult serviceResult = ecardTradeService.cardRecharge(subTrade, custInfo, batchRecharge.getRechargeMoney(), null, false, false);
                if (serviceResult.getError() != 0) {
                    LOGGER.error("卡号[{}]充值失败：{}", custInfo.getEcardNo(), serviceResult.getMessage());
                    batchRecharge.setState("3"); //3表示充值过程中异常
                } else {
                    batchRecharge.setState("1"); //1表示充值成功
                }
                tradeBatchRechargeMapper.updateByPrimaryKey(batchRecharge);
            }

            trade.setSubscribeState(Constants.SubscribeState.COMPLETED);
            trade.setFinishDate(new Date());
            tradeMapper.updateByPrimaryKeySelective(trade);
        }
    }
}
