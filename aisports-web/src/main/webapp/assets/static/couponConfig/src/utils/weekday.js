export const weekdayNameMap = {
    '0': '全选',
    '1': '周一',
    '2': '周二',
    '3': '周三',
    '4': '周四',
    '5': '周五',
    '6': '周六',
    '7': '周日',
    '8': '节假日',
}

export const weekdayCheckboxs = Object.keys(weekdayNameMap).map(weekday => ({
    weekDay: weekday,
    weekDayName: weekdayNameMap[weekday],
}))

export function isSegmentEditable(arr) {
    return arr.filter(i => i.startSegment !== 0 && i.endSegment !== 47).length !== 0
}

export function segment2time(num) {
    let hour = parseInt(num/2).toString()
    let min = (num % 2 === 1 ? '30' : '00')
    if(hour.length === 1) hour = '0' + hour
    return `${hour}:${min}`
}

export function time2segment(time) {
    return parseInt(time.substr(0, 2)) * 2 + ( parseInt(time.substr(3, 2)) >= 30 ? 1 : 0 )
}