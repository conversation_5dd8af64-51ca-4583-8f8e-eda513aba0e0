package com.asiainfo.aisports.model.project;

/**
 * <AUTHOR>
 * @date 2025/7/3 14:58
 */
public class SeatImpResult extends SeatImpDto{

    private boolean success;

    private String message;

    public SeatImpResult() {}

    public SeatImpResult(SeatImpDto seatImpDto, String message) {
        this.row = seatImpDto.getRow();
        this.startSeat = seatImpDto.getStartSeat();
        this.endSeat = seatImpDto.getEndSeat();
        this.invalidSeat = seatImpDto.getInvalidSeat();
        this.num = seatImpDto.getNum();
        this.distType = seatImpDto.getDistType();
        this.message = message;
        this.success = false;

    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
