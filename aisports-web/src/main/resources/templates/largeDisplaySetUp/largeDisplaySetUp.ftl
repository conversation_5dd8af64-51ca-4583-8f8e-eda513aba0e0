<#include "../public/macros.ftl" />
<!DOCTYPE html>
<html>
<head>
<#assign assetState="compressed" />
    <@head_declares />
    <meta charset="UTF-8">
    <title>大屏配置</title>
    <@head_includes path="largeDisplaySetup" filename="largeDisplaySetup" />
</head>
<body>
<div class="wrapper">
    <!--header-->
<#include "../public/header.ftl">
    <!-- end header -->

    <!--left menu-->
<#include "../public/aside.ftl">
    <!-- end left menu -->

    <!--main content-->
    <div class="main" ${bodyStyle!}>
        <div class="largeDisplaySetup">
            <div class="title-box">
                <h1>大屏配置</h1>
                <div class="sub-box">
                    <div class="setup-items-page" id="setup-items-page" style="display: ">
                        <#if subVenueList?? && (subVenueList?size > 0)>
                            <ul class="tab small-tab" id="page-tabs">
                                <#list subVenueList as subVenue>
                                    <#if subVenue?? >
                                        <#if subVenue_index?? && subVenue_index == 0>
                                            <li class="cur" venue-id="#{subVenue.venueId!""}" sub-venue-id = "#{subVenue.subVenueId!""}">${subVenue.subVenueName!""}</li>
                                        <#else>
                                            <li class="" venue-id="#{subVenue.venueId!""}" sub-venue-id = "#{subVenue.subVenueId!""}">${subVenue.subVenueName!""}</li>
                                        </#if>
                                    </#if>
                                </#list>
                            </ul>
                        </#if>
                    <div id="vue-content-add">
                        <div class="flex-box box-between m20">
                            <div>
                                <el-button :type="subVenueId == item.subVenueId ? 'primary' : 'default'"  v-for="item in tableData" @click="getData(item.subVenueId)" >{{item.subVenueName}}</el-button>
                             </div>
                            <div>
                            <el-button type="default"  @click="showDrawer" icon="el-icon-plus">游泳管理</el-button>
                             </div>
                        </div>
                        <el-drawer
                            title="泳池管理"
                            :visible.sync="drawer"
                            direction="rtl"
                            :before-close="handleClose"
                            size="600px">
                        <div class="table-box" >
                            <div class="flex-box flex-end mb20">
                                <el-button type="default" @click="showDialog" icon="el-icon-plus">添加泳池</el-button>
                             </div>

                            <el-table :data="tableData">
                                <el-table-column type="index" align="center" width="50" label="序号">
                                </el-table-column>
                                <el-table-column prop="subVenueName" align="center" label="泳池名称" width="300">
                                </el-table-column>
                                <el-table-column label="操作" align="center">
                                    <template slot-scope="scope">   
                                        <el-button type="text" class="" @click="deletePool(scope.row)">删除</el-button>
                                        <el-button type="text" class="" @click="editPool(scope.row)">编辑</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        </el-drawer>
                        <el-dialog :title="title" :visible.sync="dialogVisible" width="500px" center>
                            <div class='flex-box review-info-item'>
                                <span>泳池名称:</span>
                                <el-input  v-model="inputName"  class='input-text' maxlength="10" show-word-limit type="text" placeholder="请输入内容"></el-input>
                            </div>
                            <div class='flex-box review-info-item'>
                                <span>排序:</span>
                                <el-input  v-model="sort"  class='input-text' show-word-limit type="number" placeholder="序号越小排名越靠前"></el-input>
                            </div>
                            <span slot="footer" class="dialog-footer">
                                <el-button @click="cancleDialog">取 消</el-button>
                                <el-button type="primary" @click="submit">确 定</el-button>
                            </span>
                        </el-dialog>
                    </div>
                        <div class="tab-content">
                            <div class="setup-qts" id="setup-qts">

                            </div>
                        </div>
                    </div>

                    <div class="setup-plaintext-form" id="setup-plaintext-form" style="display: none;">
                        <div class="panel form-panel">
                            <div class="panel-heading">
                                <span>编辑公告</span>
                                <a href="#" class="btn-return setup-plaintext-return" data-click="setup-plaintext-return" title="后退">
                                    <i class="iconfont icon-zuojiantou"></i>
                                </a>
                            </div>
                            <div class="panel-body">
                                <div class="f-line form-line">
                                    <div class="f-label">标题</div>
                                    <div class="f-field">
                                        <input type="text" class="normal">
                                    </div>
                                </div>
                                <div class="f-line form-line">
                                    <div class="f-label">公告内容</div>
                                    <div class="f-field">
                                        <textarea id="remark" maxlength="150" class="remark" placeholder="请输入备注信息"></textarea>
                                        <p class="textarea-re">
                                            <label class="lyishu" >0</label>/<label class="lsheng" >150</label>
                                        </p>
                                    </div>
                                </div>
                                <div class="f-line setup-plaintext-save">
                                    <div class="f-field">
                                        <button class="btn btn-primary btn-lg" data-click="setup-plaintext-save">保存</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Root element of PhotoSwipe. Must have class pswp. -->
<div class="pswp" ref="pswp" tabindex="-1" role="dialog" aria-hidden="true">

    <!-- Background of PhotoSwipe.
         It's a separate element as animating opacity is faster than rgba(). -->
    <div class="pswp__bg"></div>

    <!-- Slides wrapper with overflow:hidden. -->
    <div class="pswp__scroll-wrap">

        <!-- Container that holds slides.
            PhotoSwipe keeps only 3 of them in the DOM to save memory.
            Don't modify these 3 pswp__item elements, data is added later on. -->
        <div class="pswp__container">
            <div class="pswp__item"></div>
            <div class="pswp__item"></div>
            <div class="pswp__item"></div>
        </div>

        <!-- Default (PhotoSwipeUI_Default) interface on top of sliding area. Can be changed. -->
        <div class="pswp__ui pswp__ui--hidden">

            <div class="pswp__top-bar">

                <!--  Controls are self-explanatory. Order can be changed. -->

                <div class="pswp__counter"></div>

                <button class="pswp__button pswp__button--close" title="关闭 (Esc)"></button>

                <button class="pswp__button pswp__button--share" title="分享"></button>

                <button class="pswp__button pswp__button--fs" title="全屏"></button>

                <button class="pswp__button pswp__button--zoom" title="放缩"></button>

                <!-- Preloader demo http://codepen.io/dimsemenov/pen/yyBWoR -->
                <!-- element will get class pswp__preloader--active when preloader is running -->
                <div class="pswp__preloader">
                    <div class="pswp__preloader__icn">
                        <div class="pswp__preloader__cut">
                            <div class="pswp__preloader__donut"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pswp__share-modal pswp__share-modal--hidden pswp__single-tap">
                <div class="pswp__share-tooltip"></div>
            </div>

            <button class="pswp__button pswp__button--arrow--left" title="上一张">
            </button>

            <button class="pswp__button pswp__button--arrow--right" title="下一张">
            </button>

            <div class="pswp__caption">
                <div class="pswp__caption__center"></div>
            </div>

        </div>
    </div>
</div>
<script type="text/template" id="setupQtTmpl">
    <dl class="setup-qt">
        <dt>
            <span class="text"><%= name %></span>
        </dt>
        <dd class="row setup-qtbd">
        </dd>
    </dl>
</script>
<script type="text/template" id="setupItemTmpl">
    <div class="col-xs-4 setup-item-w">
        <div class="setup-item" data-contcode="<%= contCode %>" data-sub-venue-id="<%= subVenueId %>">
            <div class="setup-item-hd">
                <div class="icon">
                    <i class="iconfont <%= contIcon %>"></i>
                </div>
                <div class="text"><%= contName %></div>
                <div class="edit-btns">
                    <i class="iconfont icon-bianji" <%= type==="textarea" ? 'data-click="edit-plain-text"' : 'data-click="edit-setup-item"'%> ></i>
                </div>
            </div>
            <div class="setup-item-bd" type="<%= type %>">
                <% if(preview === "images") { %>
                <div class="setup-item-image setup-item-plain-text" data-preview="<%= preview %>" data-extra='<%= JSON.stringify(extraInfo) %>' data-value='<%= value %>'>
                    <% if(content.length > 0) { %>
                    <img src="${ossUrl!}<%= content[0] %>" width="80" height="44">
                    <% } %>
                </div>
                <% } else if(preview === "videos" || preview === "mp4") { %>
                <div class="setup-item-image setup-item-plain-text" data-preview="<%= preview %>" data-extra='<%= JSON.stringify(extraInfo) %>' data-value='<%= value %>'>
                    <% if(content.length > 0) { %>
                    <video src="${ossUrl!}<%= content[0] %>" width="80" height="44">
                        <% } %>
                </div>
                <% } else if(type==="textarea"){ %>
                <div class="setup-item-plain-text" data-preview="<%= preview %>" data-extra='<%= JSON.stringify(extraInfo) %>' data-value='<%= value %>'>
                    <%= content %>
                </div>
                <% } else if(type === "text"){ %>
                <div class="setup-item-unit-text" data-value="<%= content %>" data-unit="<%= unit %>" data-required="<%= extraInfo.required %>">
                    <div class="text">
                        <span class="unit-text-value"><%= content %></span>
                        <span class="unit-text-unit"><%= unit %></span>
                    </div>
                </div>
                <% } %>
            </div>
        </div>
    </div>
</script>
<script type="text/template" id="toEditUnitBtnsTmpl">
    <i class="iconfont icon-bianji" data-click="edit-setup-item"></i>
</script>
<script type="text/template" id="saveEditUnitBtnsTmpl">
    <i class="iconfont icon-zuojiantou" data-click="edit-setup-cancel"></i>
    <i class="iconfont icon-zhengque-1" data-click="edit-setup-save"></i>
</script>
<script type="text/template" id="toEditUnitContentTmpl">
    <div class="edit-box">
        <div class="input-with-unit three-words-unit">
            <input class="normal" type="text" <%= required ? "required" : "" %> value="<%= value %>">
            <span class="unit"><%= unit %></span>
        </div>
    </div>
</script>
<script type="text/template" id="saveEditUnitContentTmpl">
    <div class="setup-item-unit-text">
        <div class="text">
            <span class="unit-text-value"><%= value %></span>
            <span class="unit-text-unit"><%= unit %></span>
        </div>
    </div>
</script>

<script type="text/template" id="plaintextFormLineTmpl">
    <% if(type === 'text') { %>
    <div class="f-line form-line" type="text" data-key="<%= key %>">
        <div class="f-label"><%= name %></div>
        <div class="f-field">
            <input type="text" class="normal field" value="<%= content %>"  <%= required ? 'required' : '' %> >
        </div>
    </div>
    <% } else if(type === 'textarea'){%>
    <div class="f-line form-line" type="textarea" data-key="<%= key %>">
        <div class="f-label"><%= name %></div>
        <div class="f-field">
            <textarea maxlength="250" class="remark field" placeholder="请输入备注信息" <%= required === 'true' ? 'required' : '' %>><%= content %></textarea>
            <p class="textarea-re">
                <label class="lyishu" >0</label>/<label class="lsheng" >250</label>
            </p>
        </div>
    </div>
    <% } else if(type === 'images') {%>
    <div class="f-line form-line" content="<% content %>" type="images" data-key="<%= key %>">
        <div class="f-label"><%= name %></div>
        <div class="f-field">
            <div class="operation-line sub-box-photo">
                <file-uploader ref="uploadImgBox" :multiple="true" :on-success="handleSuccess" :on-error="handleError" url="/largeDisplaySetUp/uploadFiles">
                    <button class="btn btn-md btn-default">上传图片</button>
                </file-uploader>
                <button class="btn btn-md btn-default" @click="batchDeleteImg">批量删除</button>
                <div class="tips">建议上传图片比例16：9（可多张上传），单张大小不超过5M，支持jpg,png,bmp,gif</div>
            </div>
            <ul class="images" id="image-upload-container">
                <% content.forEach(function(images){ %>
                <li class="img-div image-container" data-src="<%= images %>">
                    <img src="${ossUrl!}<%= images %>" width="200" height="112" alt="图片" draggable="true"/>
                    <div class="iconfont icon-gou" data-click="select-image"></div>
                    <div class="img-operation">
<#--                        <div class="iconfont icon-fangdaiconx"></div>-->
                        <div class="iconfont icon-shanchu" data-click="remove-image"></div>
                    </div>
                </li>
                <% }) %>
            </ul>
        </div>
    </div>
    <% }  else if(type === 'mp4') {%>
    <div class="f-line form-line" content="<% content %>" type="mp4" data-key="<%= key %>">
        <div class="f-label"><%= name %></div>
        <div class="f-field">
            <ul class="images">
                <% content.forEach(function(mp4){ %>
                <li class="image-container" data-src="<%= mp4 %>">
                    <i class="iconfont icon-cuowu"  data-click="remove-image"></i>
                    <video src="${ossUrl!}<%= mp4 %>"  width="200" height="112">
                </li>
                <% }) %>
                <li class="image-container image-upload-container" id="image-upload-container">
                    <div class="image-add">
                        <span class="file-uploader">
                            <i class="iconfont icon-icontj"></i>
                            <form id="media-form">
                                <input type="file" class="file-uploader-field video-upload" name="file" id="video-uploader-field" />
                            </form>
                        </span>
                    </div>
                    <div class="image-add-note">
                        上传视频
                    </div>
                    <div class="image-add-note">
                        支持mp4,flv，大小不超过500Mb
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <% } %>
</script>
<script type="text/template" id="imageDisplayTmpl">
        <% images.forEach(function(image){ %>
        <li class="image-container img-div" data-src="<%= image %>">
            <img src="${ossUrl!}<%= image %>" width="200" height="112" alt="图片" draggable="true"/>
            <div class="iconfont icon-gou" data-click="select-image"></div>
            <div class="img-operation">
<#--                <div class="iconfont icon-fangdaiconx"></div>-->
                <div class="iconfont icon-shanchu" data-click="remove-image"></div>
            </div>
        </li>
        <% }) %>
</script>
<script type="text/template" id="videoDisplayTmpl">
    <li class="image-container" data-src="<%= video %>">
        <i class="iconfont icon-cuowu" data-click="remove-image"></i>
        <video src="<%= baseUrl %><%= video %>" width="200" height="112" alt="视频" />
    </li>
</script>
<input type="hidden" id="baseUrl" value="${ossUrl!}">
<@foot_includes path="largeDisplaySetup" filename="largeDisplaySetup" />
</body>
</html>
