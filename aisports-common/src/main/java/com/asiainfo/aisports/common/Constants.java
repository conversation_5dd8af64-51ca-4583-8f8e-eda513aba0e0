package com.asiainfo.aisports.common;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * Created by micha<PERSON> on 15/1/10.
 */
public class Constants {
    // 一卡通二维码的有效时间(秒)
    public static final int ECARD_QRCODE_EFFECTTIME = 60;
    public static final long MILLISECONDS_PRE_HOUR = 1000L * 3600;
    // 租柜到期提醒时间
    public static final int CAB_REMIND_DAY = 15;
    // 余额专项卡到期提醒时间
    public static final int CARD_REMIND_DAY = 15;
    // 一卡通预占失效时间
    public static final String ECARD_INVALID_TIME_OFFSET = "15m";
    // 分页时的默认值
    public static final String DEFAULT_PAGE_SIZE = "10";
    // 分页时,显示10条记录
    public static final Integer PAGE_SIZE = 10;
    // 分页时,显示5条记录
    public static final int SMALL_PAGE_SIZE = 5;
    // 分页时,显示8条记录
    public static final int EIGHT_PAGE_SIZE = 8;
    // 分页时,显示20条记录
    public static final int LARGE_PAGE_SIZE = 20;
    // 导出数据最大条数
    public static final int EXPORT_PAGE_SIZE = 50000;
    public static final String DEFAULT_PASSWORD = "123456";
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    public static final String DEFAULT_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DEFAULT_END_DATE = "2050-12-31";
    public static final String DEFAULT_END_TIME = "2050-12-31 23:59:59";
    public static final String DEFAULT_PASSWORD_OVERTIME = "3M";
    // 普通会员级别Id
    public static final String COMMON_MEMBER_GRADE_ID = "0";
    // 微信的appId
    public static final Long WECHAT_APP_ID = 0L;
    // token失效时间,微信定的,7200秒
    public static final int TOKE_EXPIRE_TIME = 7200;
    //验证码生效时间
    public static final int VERIFY_CODE_VALID_MINUTES = 5;

    public static final int LOGIN_ERROR_TIMES = 5;

    public static final String LOGIN_ERROR_RESET_TIME = "2h";
    public static final Long COMBAT_SERVICE = 1012L; //约战项目为足球

    public static final String EMAIL_MATCHER = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$"; //邮箱校验正则

    // 手机正则
    public final static String PHONE_REGULAR = "^(0|86|17951)?^1[0-9]{10}$";
    // 身份证正则
    public final static String ID_CARD_REGULAR = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)";
    // 护照正则
    public final static String PASSPORT = "^[a-zA-Z0-9]{5,17}$";
    // 港澳通行证正则
    public final static String ISHKMACAO = "^[a-zA-Z0-9]{6,10}$";
    public final static String MESSAGE = "没有查询到您当前时段的预约信息";


    private Constants() {
    }

    public interface EnrollScoreState {
        String INVALID = "0";
        String PUBLISHED = "1";
        String INIT = "2";
        String FAILED = "3";
    }

    public static class TimeLevelType{
        // 工作日
        public static final String TC_TIME_LEVEL = "1";
        // 周末
        public static final String C_TIME_LEVEL = "2";
    }

    //多数量商品价格
    public static class ProductPriceValueType {
        //价格
        public static final String PRICE = "1";
        //折扣
        public static final String DISCOUNT = "2";
    }

    //客户状态定义
    public static class CustomerState {
        //正常
        public static final String NORMAL = "0";
        //销户
        public static final String CLOSED = "1";
        //未完成定单
        public static final String UNFINISHED = "9";
    }

    //体测站点拓展字段
    public static class BmSiteAttrCode {
        //提前天数
        public static final String ADVANCE_DAY = "advance_day";
        //性别限制 0不限，1男，2女
        public static final String SEX_LIMIT = "sex_limit";
        //年龄限制类型 0无限制。1按日期，2按年龄
        public static final String AGE_RULE_TYPE = "age_rule_type";
        //年龄限制数据详情
        public static final String AGE_RULE_CONTENT = "age_rule_content";
        //日期时段Json值
        public static final String DATE_JSON = "date_json";
    }

    /**
     * 产品标识
     */
    public static class CompanyTag {
        //个人
        public static final String INDIVIDUAL = "0";
        //团体
        public static final String GROUP = "1";
        //家庭
        public static final String FAMILY = "2";

        // 五台山团体卡
        public static final String ENT_PRODUCT = "3";
    }

    public static class CardType {
        //一卡通
        public static final String ECARD = "0";
        //专项卡
        public static final String SPECIAL_CARD = "1";
        //不记名一卡通
        public static final String ANONYMITY_CARD = "2";
    }

    /**
     * 账本来源
     */
    public static class DepositSourceType {
        //办卡获取
        public static final String PERSONAL_CARD = "0";
        //公司统一办理
        public static final String ENTERPRISE_CARD = "2";
        //转卡获取
        public static final String TRANSFER_CARD = "1";
    }

    public static class UserType {
        //普通用户
        public static final String COMMON = "0";
        //家庭卡成员
        public static final String FAMILY = "1";
    }

    public static class VideoUserType {
        //协会
        public static final String ASSOCIATION = "1";
    }

    public static class StudentStatus {
        //正常
        public static final String NORMAL = "1";
        //销号
        public static final String CLOSED = "0";
    }

    public static class RelationIdentifier {
        //家庭关系标识
        public static final String FAMILY = "10";
    }

    public static class ProductElementType {
        //资源类元素
        public static final String ELEMENT_RES = "0";
        //物品类元素
        public static final String ELEMENT_GIFT = "1";
    }

    public static class FeeRuleType {
        public static final String COMMON_RULE = "0"; // 通用手续费收取规则
        public static final String FREEZE_RULE = "1"; // 冻卡手续费收取规则
        public static final String ENUM_RULE = "2"; // 枚举手续费收取规则
        public static final String JSON_RULE = "3"; // 期限后按月设置规则
        public static final String BACK_CARD_RULE = "4";
        public static final String FIX_MONTH_RULE = "5";//固定每月手续费收取规则
    }

    /**
     * 商品退货标识
     */
    public static class GoodsReturnTag {
        //手环号退货
        public static final String RETURN_BY_KEY = "0";
        //流水号退货
        public static final String RETURN_BY_TRADE = "1";
        //卡号退货
        public static final String RETURN_BY_ECARDNO = "2";
    }

    public static class RentTag {
        //固定数量
        public static final String FIXED_AMOUNT = "0";
        //不限数量
        public static final String UNLIMITED_AMOUNT = "1";
    }

    public static class RentUnit {
        //租赁柜子按月
        public static final String RENT_BY_MONTH = "1";
        //租赁柜子按年
        public static final String RENT_BY_YEAR = "2";
    }

    /**
     * 租柜子延期单位
     */
    public static class DelayUnit {
        //日
        public static final String DAY = "0";
        //月
        public static final String MONTH = "1";
    }

    //销户标志
    public static class RemoveTag {
        //正常
        public static final String NORMAL = "0";
        //销户
        public static final String REMOVED = "1";
    }

    public static class CancelTag {
        //正常
        public static final String NORMAL = "0";
        //取消
        public static final String CANCELLED = "1";
    }

    /**
     * 订单状态
     */
    public static class SubscribeState {
        //未完工状态
        public static final String UNCOMPLETED = "0";
        //己完工
        public static final String COMPLETED = "1";
        //审批不通过
        public static final String UNAPPROVED = "2";
        //已取消
        public static final String CANCELED = "3";
        //待审核
        public static final String PENDING = "4";
        //订单完成待处理
        public static final String REFUNDING = "5";
        //提交中心财务处理
        public static final String CENTER = "6";
        //退回
        public static final String RETURN_STATE = "7";
        //拼团中
        public static final String GROUP_PURCHASING = "8";
    }

    /**
     * 子业务类型
     */
    public static class BusinessType {
        //新用户办家庭卡
        public static final String FAMILY_CARD = "0";
        //老用户办家庭卡
        public static final String FAMILY_CARD_OLD_CUST = "1";
        //老用户办专项卡
        public static final String INDIVIDUAL_OLD_CARD = "2";
        //新用户办专项卡
        public static final String INDIVIDUAL_CARD = "3";
        //新用户办一卡通
        public static final String INDIVIDUAL_ECARD = "4";
        //批量办卡
        public static final String ENTERPRISE_CARD = "7";
        //老用户升卡
        public static final String OLD_UPGRADECARD = "8";
        //批量开一卡通
        public static final String BATCH_DOCARD_ECARD = "12";
        //批量老用户开专项卡
        public static final String BATCH_DOCARD_OLD_SPECARD = "11";
        //批量新用户开专项卡
        public static final String BATCH_DOCARD_NEW_SPECARD = "13";
        //老用户办培训报名
        public static final String TRAINING_COURSE_OLD = "14";
        //新用户办培训报名
        public static final String TRAINING_COURSE_NEW = "15";
        //老用户办私教报名
        public static final String PRIVATE_COURSE_OLD = "16";
        //新用户办私教报名
        public static final String PRIVATE_COURSE_NEW = "17";
        //注册没有实体卡的电子用户
        public static final String ELECTRONIC_CARD_USER = "18";
        //商品销售
        public static final String GOODS_SALE = "19";
        //商品结算
        public static final String GOODS_SETTLEMENT = "20";
        //企业新客户--办理一卡通
        public static final String ENTERPRISE_NEW_ECARD = "21";
        //企业新客户--办理专项卡
        public static final String ENTERPRISE_NEW_SPECIAL_CARD = "22";
        //企业老客户--办理专项卡
        public static final String ENTERPRISE_OLD_SPECIAL_CARD = "23";
        //企业客户挂账协议
        public static final String ENT_CUST_AGREEMENT = "24";
        //网上商品销售
        public static final String ONLINE_GOODS_SALE = "25";
        //出馆结算
        public static final String EXIT_SETTLE = "26";
        //普通次票销售
        public static final String SIMPLE_TICKET_SALE = "27";
        //团体票销售
        public static final String GROUP_BUY_TICKET_SALE = "28";
        //扣次出馆结算
        public static final String PAY_TIMES_FOR_EXIT_VENUE = "29";
        //专项卡升级
        public static final String CARD_UPGRDE = "30";
        //新批量培训报名
        public static final String NEW_BATCH_COURSE_ENROLL = "31";
        //一卡通充值绑定手机号码
        public static final String ECARD_BIND_PHONE = "35";
        //兑换优惠券
        public static final String EXCHANGE_COUPON = "36";
        //兑换积分
        public static final String EXCHANGE_POINTS = "37";
        //场地租赁
        public static final String RENT_FIELD = "38";
        public static final String ROOM_BLOCK_BOOKING = "39"; // 房间包场
        //网上餐饮销售
        public static final String ONLINE_CATERING_GOODS_SALE = "40";
        //援鄂医护申请办理期间卡
        public static final String SPEC_APPLY_NEW_SPECARD = "41";
        //浴资结算
        public static final String SETTLE_HANG_WALL = "42";
        //平台券核销
        public static final String PLATFORM_COUPON_CHECK = "43";
        //场馆券核销
        public static final String VENUE_COUPON_CHECK = "44";
        //协会会员申请
        public static final String ASSOCIATION_MEMBER_APPLY = "45";
        //协会会员续费
        public static final String ASSOCIATION_MEMBER_RECHARGE = "46";
        // 闸机出馆时结算
        public static final String GATE_AUTO_EXIT = "47";
        // 闸机计时票计算
        public static final String GATE_POST_PAY = "48";
        // 计时票出馆
        public static final String POST_PAY_EXIT = "49";
        // 停车缴费
        public static final String PARKING_CHARGE = "50";
        // 批量冻卡
        public static final String BATCH_FREEZE = "51";
        // 资源分享
        public static final String SHARE_DEPOSIT = "52";

        public static final String ENROLL_AHEAD_UNFREEZE = "53";

        public static final String ENROLL_CANCEL_UNFREEZE = "54";


        //五台山团体卡--新用户办理专项卡
        public static final String GROUP_NEW_SPECIAL_CARD = "55";
        //五台山团体卡--办理专项卡
        public static final String GROUP_OLD_SPECIAL_CARD = "56";
        //批量冻柜
        public static final String BATCH_CABINET = "57";
        //取消批量冻柜
        public static final String BATCH_CANCEL_CABINET = "58";

        // 新的批量延课
        public static final String NEW_BATCH_DELAY = "57";

    }

    // 客户类型
    public static class CustomerType {
        // 个人
        public static final String INDIVIDUAL = "0";
        // 家庭
        public static final String FAMILY = "1";
        // 企业
        public static final String ENTERPRISE = "2";
        // 不记名一卡通
        public static final String ANONYMITY = "4";
        // 健康承诺书客户
        public static final String HEALTH_AGREEMENT = "5";

        // 团体用户
        public static final String GROUP = "6";

    }

    /**
     * 账本限制类型
     */
    public static class DepositLimitType {
        // 期间卡
        public static final String PERIOD = "0";
        // 计次卡
        public static final String TIMES = "1";
        // 余额专项卡
        public static final String MONEY = "2";
    }

    /**
     * 产品资源类型
     */
    public static class ProductResType {
        // 次数
        public static final String TIMES = "0";
        // 金额
        public static final String MONEY = "1";
        // 天数
        public static final String DAYS = "2";
    }

    public static class Service {
        // 篮球
        public static final long BASKETBALL = 1001;
        // 羽毛球
        public static final long BADMINTON = 1002;
        // 网球
        public static final long TENNIS = 1003;
        // 保龄球
        public static final long BOWLING = 1004;
        // 游泳
        public static final long SWIMMING = 1005;
        // 高温瑜伽
        public static final long YOGA = 1006;
        // 肚皮舞
        public static final long BELLY_DANCE = 1007;
        // 动感单车
        public static final long SPINNING = 1008;
        // 壁球
        public static final long SQUASH = 1009;
        // 乒乓球
        public static final long TABLE_TENNIS = 1010;
        //台球
        public static final long BILLIARDS = 1076; // 台球
        //游艇
        public static final long YACHT = 1099;
    }

    /**
     * 俱乐部属性表
     */
    public static class YachtClubAttr {
        public static final String ANNOUNCEMENT_TITLE = "announcement_title";
        public static final String ANNOUNCEMENT = "announcement";
    }

    /**
     * 帆艇租赁评价状态
     */
    public static class YachtLeaseCommentState {
        public static final String PENDING = "0"; // 待审核
        public static final String DISPLAY = "1"; // 展示评价
        public static final String UNDISPLAY = "2"; // 不展示评价
        public static final String INVALID = "3"; // 无效评价
        public static final String DELETED = "10"; // 已删除
    }

    /**
     * 主订单支付状态
     */
    public static class PayState {
        public static final String UNPAID = "0";
        public static final String PAID = "1";
        public static final String PAYING = "2"; // 正在支付
        public static final String REVERSING = "3"; // 正在撤单
    }

    public static class SmsType {
        // 验证码短信
        public static final int VERIFY_CODE = 1;
        // 生日祝福类短信
        public static final int BIRTHDAY_WISHES = 2;
        // 提醒类短信
        public static final int WARNINGS = 3;
        // 自定义短信
        public static final int CUSTOMIZE = 4;
    }

    public static class SmsState {
        // 待发送
        public static final int WAITING = 0;
        // 已发送
        public static final int SEND = 1;
        // 发送失败
        public static final int FAIL = 2;
    }

    public static class MailState {
        // 待发送
        public static final int WAITING = 0;
        // 已发送
        public static final int SEND = 1;
        // 发送失败
        public static final int FAIL = 2;
    }

    public static class TicketSourceType {
        public static final String NORMAL = "0";    //普通购票
        public static final String OCCUPY = "3";    //协议占场
    }

    public static class RoleGroup {
        // 场馆营业员
        public static final String VENUE_STAFF = "2";
        // 中心管理员
        public static final String CENTER_ADMIN = "8";
        //训练经理
        public static final String TRAINING_MANAGE = "12";
        //会籍顾问上级经理
        public static final String CONSULTANT_MANAGER = "13";

    }

    public static class Extra {
        public static final String ECARD_END_DATE = "ecard_end_date";
        public static final String OLD_ECARD_END_DATE = "old_ecard_end_date";//延期前到期时间
        public static final String DELAY_CARD = "delay_card";
        public static final String BATCH_CARD_DELAY_START_DATE = "batch_card_delay_start_date";//批量延期开始时间
        public static final String BATCH_CARD_DELAY_END_DATE = "batch_card_delay_end_date";//批量延期到日期
        public static final String BATCH_CARD_DELAY_DAYS = "batch_card_delay_days";//批量延期天数
        public static final String BATCH_CARD_DELAY_PRODUCTS = "batch_card_delay_products";//批量延期产品集合
        public static final String PASSWORD = "password";
        public static final String COACH_ID = "coach_id";
        public static final String SALE_NUM = "sale_num";
        public static final String DISCOUNT_VALUE = "discount_value";
        public static final String DISCOUNT_TYPE = "discount_type";
        public static final String CABINET_ID = "cabinet_id";
        public static final String COUPON_ID = "coupon_id";
        public static final String DEPOSIT_ID = "deposit_id";
        public static final String ECARDNO_IN = "ecardno_in";
        public static final String ECARDNO_OUT = "ecardno_out";
        public static final String CUSTID_IN = "custid_in";
        public static final String CUSTID_OUT = "custid_out";
        public static final String REMARK = "remark";
        public static final String DEPOSIT_FEE = "deposit_fee";
        public static final String RENT_GOODS = "rent_goods";
        public static final String FAMILY_CUSTOMERS = "family_customers";
        public static final String CONSULTANT_ID = "consultant_id";
        public static final String COURSE_ENROLL = "course_enroll";
        public static final String ENROLL_ID = "enroll_id";
        public static final String HOUR_ID = "hour_id";
        public static final String PRIVATE_COURSE_ENROLL = "private_course_enroll";
        // 课程转出
        public static final String TRANSFER_COURSE = "transfer_course";
        // 课程转入
        public static final String TRANSFER_COURSE_INTO = "transfer_course_into";
        public static final String COURSE_DELAY = "course_delay";
        public static final String BATCH_COURSE_DELAY = "batch_course_delay";//{"startDate":"延期开始时间","endDate":"延期结束时间","extendDays":"延期天数","remark":"延期备注"}
        public static final String SELL_COUPON_ID = "sell_coupon_id";
        public static final String COUPON_BUY_NUM = "coupon_buy_num";
        public static final String SELL_CAMP_ID = "sell_camp_id";
        public static final String COOP_MERCHANT_INFO = "coop_merchant_info";
        public static final String CONTINUE_PRICE_TAG = "continue_price_tag"; //0不享受续卡价 1享受续卡价
        public static final String PSPT_ID = "pspt_id"; //身份证
        public static final String PHONE_NUM = "phone_num"; //手机号
        public static final String TICKET_ID = "ticket_id";
        public static final String COUPON_CASH_PLEDGE = "coupon_cash_pledge";

        // 折扣办卡的折扣类型
        public static final String APPLY_DISCOUNT_TYPE = "apply_discount_type";
        // 折扣办卡的折扣值(或额度)
        public static final String APPLY_DISCOUNT_VALUE = "apply_discount_value";
        // 折扣办卡审核人的ID
        public static final String AUDIT_PERSON_ID = "audit_person_id";
        // 折扣办卡的总优惠
        public static final String TOTAL_DISCOUNT = "total_discount";
        public static final String GRUOP_TAG = "group_tag";
        // 手工减免
        public static final String MANUAL_REDUCTION = "manual_reduction";
        // 专项卡退卡金额返还方式
        public static final String RETURN_TYPE = "return_type";
        // 手工减免备注
        public static final String MANUAL_REDUCTION_REMARK = "manual_reduction_remark";
        // 手工减免折扣比例
        public static final String MANUAL_REDUCTION_DISCOUNT = "manual_reduction_discount";
        // 手工减免附件
        public static final String REDUCTION_FILE_URL = "reduction_file_url";
        // 冻卡时延期的柜子
        public static final String DELAY_CABINETS = "delay_cabinets";
        public static final String SALE_CAMPAIGIN = "sale_campaign";
        public static final String INVOICE = "invoice"; //开具发票信息
        // 订单折扣
        public static final String ORDER_DISCOUNT = "order_discount";
        // 支付组合
        public static final String PAY_GROUP = "pay_group";
        public static final String PAY_ECARD_NO = "pay_ecard_no"; //支付卡号
        public static final String SETTING_STARTDATE = "setting_startdate"; // 期间卡办卡设置开始时间
        public static final String AGREEMENT_INFO = "agreement_info";
        public static final String DISCOUNTED_FEE = "discounted_fee";//赛事活动折后金额
        public static final String TRADE_VERIFY_CODE = "trade_verify_code";//赛事活动验票验证码
        public static final String TRADE_MEMBER_ID = "trade_member_id";//企业消费员工
        public static final String RECHARGE_BATHE_INFO = "recharge_bache_info";//洗澡充值
        public static final String APPOINTMENT_ID = "appointment_id"; //教练预约ID
        public static final String RECOMMEND_CUST_ID = "recommend_cust_id"; //推荐人custId
        public static final String MERCHANT_ID = "merchant_id"; //商铺Id
        public static final String COUPON_CUST_PHOTO = "coupon_cust_photo"; //领取优惠券人照片
        public static final String OLD_ENROLL_ID = "old_enroll_id";
        public static final String NEW_ENROLL_ID = "new_enroll_id";
        public static final String OLD_HOUR_ID = "old_hour_id";
        public static final String NEW_HOUR_ID = "new_hour_id";
        public static final String EXER_GROUP_ID = "exer_group_id";
        public static final String EXER_STAFF_ID = "exer_staff_id";
        public static final String VISITOR_ID = "visitor_id";
        public static final String GRADE_ID = "grade_id";
        public static final String SOURCE_ID = "source_id";
        public static final String AUDIT_IMAGE_URL = "audit_image_url";//审核业务图片路径
        public static final String LESSON_PRICE_ITEM = "lesson_price_item";//存放课时价格主键
        public static final String LESSON_DISCOUNT = "lesson_discount";//启迪课程折扣系数
        public static final String LESSON_RESV_ID = "lesson_resv_id";
        public static final String ALI_REFUND_TICKET_STATE = "ali_refund_ticket_state";
        // 换课业务，老课折算金额
        public static final String CHANGE_COURSE_CONVERTED_AMOUNT = "change_course_converted_amount";
        public static final String PAYMENT_ID = "payment_id";//订金退款id
        public static final String DEVELOPER_ID = "developer_id";
        public static final String DOWN_PAYMENT_CUST_NAME = "down_payment_cust_name";
        public static final String DOWN_PAYMENT_PHONE_NUM = "down_payment_phone_num";
        public static final String NEW_CARD_TAG = "new_card_tag";// 是否是新办卡
        public static final String UPDATE_CUST = "update_cust";
        public static final String GROUP_BUYING_TYPE = "group_buying_type"; //团购票类型
        public static final String GROUP_BUYING_CODES = "group_buying_codes";// 团购券码
        public static final String GROUP_BUYING_DETAILS = "group_buying_details"; //团购明细
        public static final String GROUP_BUYING_CASH_PLEDGE = "group_buying_cash_pledge"; //团购交押金
        public static final String CASH_PLEDGE = "cash_pledge";// 押金
        public static final String CASH_PLEDGE_INFO = "cash_pledge_info";// 押金支付信息
        public static final String GAME_ID = "game_id";// 约赛Id
        public static final String SIGN_PHOTO = "sign_photo";// 签到照片
        public static final String SIGNATURE = "signature";// 签到照片
        //退款银行Id
        public static final String BANK_ID = "bank_id";
        //银行名称
        public static final String BANK_NAME = "bank_name";
        //银行账号
        public static final String BANK_ACCT = "bank_acct";
        //银行账户人名字
        public static final String BANK_USER_NAME = "bank_user_name";
        public static final String COUPON_COMPAIGN = "coupon_compaign"; //发放优惠券详情
        public static final String PALM_PERSON_TICKET_TAG = "palm_person_ticket_tag"; // 掌静脉用户绑定票标志，1-需要绑定票
        public static final String PALM_PERSON_PHONE_NUM = "palm_person_phone_num"; // 掌静脉用户手机号
        public static final String AD_ACCOUNT = "ad_account"; // ad帐号
        public static final String KEYIDS = "keyIds"; // 钥匙扣
        public static final String CTRIP_CANCEL_INFO = "ctrip_cancel_info"; // 携程上一次取消订单的全局序列
        public static final String LOCKER_RENT_ID = "locker_rent_id"; //租用id
        public static final String END_RENT_TAG = "end_rent_tag"; //结束使用标识
        public static final String LOCKER_OPEN_TAG = "locker_open_tag"; //开柜门标识
        public static final String ITEM_IDS = "item_ids";
        public static final String COMMON_PERSON_IDS = "common_person_ids"; // 票签协议的常用人的id集合
        public static final String RELATE_TRADE_ID = "relate_trade_id"; // 关联订单
        public static final String TRXID = "trxid";// 通联订单号
        public static final String IS_HANG_RING_MACHINE = "is_hang_ring_machine"; //是否是手环机
        public static final String CASH_PLEDGE_PAID = "cash_pledge_paid"; //押金已经支付
        public static final String PLATFORM_COUPON_NO = "platform_coupon_no";// 平台券号
        public static final String VENUE_COUPON_NO = "venue_coupon_no";// 场馆券号
        public static final String VENUE_COUPON_TRADE_TYPE = "venue_coupon_trade_type";// 场馆券核销业务类型
        public static final String SECKILL_ID = "seckill_id";// 秒杀订单的秒杀id
        public static final String SECKILL_NUM = "seckill_num";// 秒杀订单的下单数量
        public static final String BUYER_MESSAGE = "buyer_message";// 商品下单买家留言
        // 平阳全民健身平台订单同步-未支付订单
        public static final String PING_YANG_PAYING_ORDER_SYNC = "ping_yang_paying_order_sync";
        // 平阳全民健身平台订单同步-已支付订单
        public static final String PING_YANG_PAYED_ORDER_SYNC = "ping_yang_payed_order_sync";
        // 平阳全民健身平台订单同步-退款中订单
        public static final String PING_YANG_REFUNDING_ORDER_SYNC = "ping_yang_refunding_order_sync";
        // 平阳全民健身平台订单同步-已退款订单
        public static final String PING_YANG_REFUNDED_ORDER_SYNC = "ping_yang_refunded_order_sync";
        // 平阳全民健身平台订单同步-退款审核不通过订单
        public static final String PING_YANG_REFUND_FAIL_ORDER_SYNC = "ping_yang_refund_fail_sync";
        // 平阳全民健身平台用户信息同步
        public static final String PING_YANG_SYNC_USER = "ping_yang_sync_user";
        // 平阳全民健身平台押金同步-支付押金
        public static final String PING_YANG_PAYED_DEPOSIT_SYNC = "ping_yang_payed_deposit_sync";
        // 平阳全民健身平台押金同步-已退款押金
        public static final String PING_YANG_REFUNDED_DEPOSIT_SYNC = "ping_yang_refunded_deposit";
        // 当专项卡冻卡时记录每张卡的冻卡时间
        public static final String SPECIAL_CARD_FREEZE_DATE = "special_card_freeze_date";
        // 专项卡解冻的是哪个冻结业务
        public static final String UNFREEZE_FREEZE_TRADE_ID = "unfreeze_freeze_trade_id";
        //卡升级的升卡产品id
        public static final String CARD_UPGRADE_PRODUCT_ID = "card_upgrade_product_id";
        //教室占用类型
        public static final String ROOM_STATE_TYPE = "room_state_type";
        // 退票时提交的附件url
        public static final String FILE_URL_OF_REFUND_TICKET = "file_url_of_refund_ticket";
        //订单退回原因
        public static final String RETURN_ORDER_REASON = "return_order_reason";
        //订单取消原因
        public static final String CANCEL_ORDER_SEASON = "cancel_order_season";
        // 补票
        public static final String EXCESS_FARE = "excess_fare";
        // 前台订单提交支付的Staff,临时记一下作为订单完工人
        public static final String TEMP_FINISH_TRADE_STAFF = "temp_finish_trade_staff";
        // 记录用于核销停车优惠信息 {"carNumber":"xx",-"relatedTradeId":"xx","inParkTime":"",-"parkEndTime":"",-"parkTime":xx,"shouldPay":xxx,"-payedMoney":xxx,"realPay":xxx,"chargeDetail":"xx","originalData":{}}
        public static final String PARKING_CHARGE_INFO = "parking_charge_info";
        // 记录下停车的原本金额，用于报表统计
        public static final String PARKING_SHOULD_PAY = "parking_should_pay";
        // 记录核销车牌
        public static final String PARKING_CHARGE_CAR = "parking_charge_car";
        //国信通积分服务
        public static final String GXT_EARN_POINTS = "gxt_earn_points";//国信通 通积的积分数和金额 {"points":"xx","payAmount":"xx","referNo":"xx"}
        public static final String GXT_EARN_POINTS_TAG = "gxt_earn_points_tag";// 0-待通积 1-通积成功 2-通积失败
        public static final String GXT_POINTS_FAIL_REQ = "gxt_points_fail_req";//国信通积分服务请求超时或失败后保存的请求参数

        public static final String GXT_REVERT_EARN_POINTS = "gxt_revert_earn_points";//国信通 撤销通积的积分数
        public static final String GXT_REVERT_EARN_POINTS_TAG = "gxt_revert_earn_points_tag";// 0-待撤销通积 1-撤销通积成功 2-撤销通积失败

        public static final String GXT_REDUCE_POINTS = "gxt_reduce_points";//国信通 通兑的积分数
        public static final String GXT_REDUCE_POINTS_TAG = "gxt_reduce_points_tag";// 0-待通兑 1-通兑成功 2-通兑失败
        public static final String GXT_REVERT_REDUCE_POINTS = "gxt_revert_reduce_points";//国信通 通兑的积分数
        public static final String GXT_REVERT_REDUCE_POINTS_TAG = "gxt_revert_reduce_points_tag";// 0-待撤销通兑 1-撤销通兑成功 2-撤销通兑失败
        // 拼团订单
        public static final String GROUP_PURCHASE_ITEM_ID = "group_purchase_item_id";
        // 拼团详情
        public static final String GROUP_PURCHASE_INFO = "group_purchase_info";
        // 园秀通实体卡的卡号
        public static final String PARK_SHOW_CARD = "park_show_card";
        // 期次班级上课计划 [{-"termId":xx,-"classId":xx,"timeId":xx,"weekDay":"xxx"},...]
        public static final String LESSON_PLAN_INFO = "lesson_plan_info";
        // 批量冻卡申请开始时间/结束时间/冻卡数量
        public static final String BATCH_FREEZE_CARD_START_DATE = "batch_freeze_card_start_date";
        public static final String BATCH_FREEZE_CARD_END_DATE = "batch_freeze_card_end_date";
        public static final String BATCH_FREEZE_CARD_DEPOSIT_NUM = "batch_freeze_card_deposit_num";
        public static final String BARGAIN_INITIATOR_ID = "bargain_initiator_id";//砍价发起Id
        public static final String REPLACE_ORIGINAL_CABINET_ID = "replace_original_cabinet_id";//换柜时原始租柜Id
        public static final String REPLACE_CABINET_ID = "replace_cabinet_id";//换柜时新租柜Id
        // web出馆结算时是否需要出馆操作
        public static final String EXIT_FLAG = "exit_flag";
        //保存培训课入馆订单关联的教练Id
        public static final String IN_THE_LIBRARY_WITH_COACH = "in_the_library_with_coach";
        // 专项卡解冻时柜子的延期天数,同一笔订单下解冻该值会被覆盖
        public static final String CANCEL_FROZEN_DELAY_CABINET_DAYS = "cl_frozen_delay_cabinet_days";

        // 赠课ID
        public static final String COURSE_FREE_ID = "course_free_id";


        // 换课私教多课时的有效期
        public static final String SUBJECT_VALID_PERIOD = "subject_valid_period";
        // 换课培训课多课时信息
        public static final String SUBJECT_ENROLL = "subject_enroll";
        // 费用返还信息
        public static final String COST_RETURN_DETAIL = "cost_return_detail";
        public static final String COST_RETURN_NAME = "cost_return_name";
        public static final String COST_RETURN_PHONE = "cost_return_phone";

        // 校园卡创建一卡通信息
        public static final String SCHOOL_CARD_INFO = "school_card_info";
        // 非税支付订单号
        public static final String WECHAT_NON_TAX_ORDER_ID = "wechat_non_tax_order_id";
        // 非税支付缴款通知书编号
        public static final String WECHAT_NON_TAX_PAYMENT_NOTICE_NO = "wechat_non_tax_payment_notice_no";
        // 冻卡类型
        public static final String FROZEN_CARD_TYPE = "frozen_card_type";
        // 多扣次次数入馆数量保存
        public static final String CHOOSE_COURSE_NUM_ENTRY = "choose_course_num_entry";

        // 保龄球赠送券信息
        public static final String PRESENT_COUPON_ID = "present_coupon_id";
        public static final String PRESENT_COUPON_NUM = "present_coupon_num";
        public static final String PRINT_PRESENT_COUPON = "print_present_coupon";
        public static final String ENROLL_UNFREEZE_TYPE = "enroll_unfreeze_type";

        //招行推送订单订单号随机数
        public static final String CMB_ORDER_ID_RANDOM = "cmb_order_id_random";
        public static final String CMB_REFUND_NODE = "cmb_refund_mode";

        public static final String GROUP_USER_ID = "group_user_id";
        public static final String GROUP_LESSON_ID = "group_lesson_id";
        public static final String MINI_APP_OPEN_ID = "mini_app_open_id";
        // 保存一份退单的trade_ticket信息
        public static final String REFUND_TICKET_INFO = "refund_ticket_info";
        public static final String THIRD_TRADE_REFUND_CHECK_TAG = "third_trade_refund_check_tag";
        // 批量冻卡柜开始时间/结束时间/冻卡数量
        public static final String BATCH_FREEZE_CABINET_START_DATE = "batch_freeze_cabinet_start_date";
        public static final String BATCH_FREEZE_CABINET_END_DATE = "batch_freeze_cabinet_end_date";
        public static final String BATCH_FREEZE_CABINET_DEPOSIT_NUM = "batch_freeze_cabinet_deposit_num";
        public static final String WECHAT_PAY_URL_FLAG = "wechat_pay_url_flag";
        public static final String PREMIUM_AMOUNT = "premium_amount";

        public static final String PREMIUM_REMARK = "premium_remark";

        public static final String PREMIUM_BACK = "premium_back";

        public static final String FIELD_TYPE = "field_type";

        public static final String ADVANCE_OCCUPY_INFO = "advance_occupy_info";
        public static final String COURSE_ENROLL_AUDIT_ID = "course_enroll_audit_id";//洪山课程报名审核id
        public static final String COURSE_ENROLL_PHONE = "course_enroll_phone";//洪山课程报名信息-手机号
        public static final String COURSE_ENROLL_CARD_NUM = "course_enroll_card_num";//洪山课程报名信息-身份证号码
        public static final String COURSE_ENROLL_REMARK = "course_enroll_remark";//洪山课程报名信息-家长备注
        public static final String PERFORM_TEAM_TICKET_SEND_ID = "perform_team_ticket_send_id";//赛事活动票团体票发放id
    }

    public static class Owner {
        public static final String STAFF = "1";
        public static final String ROLE = "2";
    }

    public static class RefundTicketState {
        public static final String UNUSED = "0";//未用
        public static final String USED = "1";//已用
        public static final String OVERDUE = "2";//过期
    }

    public static class TicketState {
        // 未取票
        public static final String NOT_FETCH = "0";
        // 已取消
        public static final String CANCELED = "1";
        // 已取票
        public static final String FETCHED = "2";
        // 已验票
        public static final String CHECKED = "3";
        // 未付款
        public static final String UNPAYED = "9";
        // 未领取
        public static final String UNBIND = "4";
    }

    public static class FieldOccupyState {
        // 未使用
        public static final String FREE = "0";
        // 已预订
        public static final String BOOKED = "1";
        // 已使用
        public static final String OCCUPIED = "2";
    }

    /**
     * 游艇租赁类型 1-时段租赁 2-私人定制游
     */
    public static class RentType {
        public static final String SEGMENT = "1";
        public static final String FULLDAY = "2";
    }

    /**
     * 场地占用的来源
     * 1-前台 2-微信 3-协议占场 4-自助终端 5-活动占场
     * 7-app 8-阿里体育 9-场地预占
     * 10-微信小程序 11-website 12-全民健身平台 13-约球 22-鄂惠办
     */
    public static class FieldOccupySource {
        public static final String PLATFORM = "1";
        public static final String WECHAT = "2";
        public static final String AGREEMENT = "3";
        public static final String ATM = "4";
        public static final String FIXED = "5";
        public static final String APP = "7";
        public static final String ALI = "8";
        public static final String ADVANCE = "9";
        public static final String WECHAT_MINI_APP = "10";
        public static final String WEBSITE = "11";
        public static final String NATIONAL_FITNESS_PLATFORM = "12";
        public static final String PLAY_AA = "13";
        public static final String EHB_APP = "22";
        public static final String ANHUI_PROVINCE_APP = "23";
        public static final String GXT = "24";
        public static final String ABC = "25";
    }

    public static class StaticParam {
        public static final String ELEMENT_PICTURE = "element_picture";//图片
        public static final String CHESS_TYPE = "chess_type";
        public static final String TICKET_NAME = "ticket_name";
        public static final String TICKET_STATE = "state";
        public static final String SERVICE = "service";
        public static final String ROLE_LEVEL = "role_level";
        public static final String ROLE_GROUP = "role_group";
        public static final String PRODUCT_MODE = "product_mode";
        public static final String TRADE_TYPE = "trade_type";
        public static final String PAY_MODE_CODE = "pay_mode_code";
        public static final String CHANNEL_ID = "channel_id";
        public static final String BIZ_KIND = "biz_kind";
        public static final String TICKET_SOURCE_TYPE = "ticket_source_type";
        public static final String CONT_CAT = "cont_cat";
        public static final String TICKET_KIND = "ticket_kind";
        // 余额专项卡可以支付的业务类型sysdate
        public static final String PRODUCT_TRADE_TYPE = "product_trade_type";
        // 专项卡状态
        public static final String DEPOSIT_STATUS = "deposit_status";
        public static final String COURSE_STATE = "course_state"; // 课程状态
        public static final String COUPON_STATE = "coupon_state"; // 券实体状态
        public static final String REMINDER_TYPE = "reminder_type";
        public static final String CUST_REMINDER_UNIT = "cust_reminder_unit";
        public static final String NOTICE_TYPE = "notice_type"; //提醒方式
        //紧急联系人关系
        public static final String EMERGENCY_CONTACT_RELATION = "contact_relation";
        public static final String MERCHANT_MSG = "merchant_msg";//商户信息
        //快递公司
        public static final String EXPRESS_COMPANY = "express_company";
        //商品提取状态
        public static final String GOODS_FETCH_STATUS = "goods_fetch_status";
        //提货方式
        public static final String FETCH_TYPE = "fetch_type";
        //出入库类型
        public static final String LOG_TYPE = "log_type";
        //库存单状态
        public static final String GOODS_STOCK_STATE = "goods_stock_state";
        //包装率
        public static final String STAGE_STATE = "stage_state";//赛段状态
        public static final String COOP_MERCHANT_TYPE = "coop_merchant_type"; //合作商类型
        public static final String SITE_TRADE_TYPE = "site_trade_type";
        public static final String SUBSCRIBE_STATE = "subscribe_state";
        public static final String BATCH_ENROLL_STATE = "batch_enroll_state";
        public static final String FIELD_FIXED_STATUS = "field_fixed_status";
        //优惠券类型
        public static final String COUPON_TYPE = "coupon_type";
        public static final String ACCOUNT_PAY_MODE = "account_pay_mode";
        public static final String SUIT_PSERSON = "suit_person";
        public static final String PRIVATE_TAG = "private_tag";
        public static final String BASIC_TRAINING = "basic_training";//培训课程的基本分类 1-分期课 2-长训班 （如果有冰雪课，则到venue_static_param  attr_code = training_type 配置）
        public static final String THIRD_PAY_TRADE_TYPE = "third_pay_trade_type";//第三方支付商品名称显示的信息,根据不同业务展示不同信息
        public static final String PROM_ELEMENT_TYPE = "prom_element_type"; // 课程管理促销活动类型
        public static final String SMS_SEND_LOG_STATE = "sms_send_log_state";//短信发送历史状态
        public static final String LIMIT_TAG = "limit_tag";//专项卡定义
        public static final String CAMP_PLAYER_STATE = "camp_player_state";//专项卡定义
        public static final String SALES_RESERVE_STATUS = "sales_reserve_status";//预约状态
        public static final String JOB_TYPE = "job_type";//预约状态
        public static final String VISITOR_SOURCE_ID = "visitor_source_id";//访客来源渠道
        public static final String EXEC_CHANNEL_CODE = "exec_channel_code";//执行渠道编码
        public static final String CANCEL_VISIT = "cancel_visit";//取消访问原因
        public static final String NOTJOIN_REASON = "notjoin_reason";//不入会原因
        public static final String EXEC_RESULT_CODE = "exec_result_code";//离场结果
        public static final String SALES_EXEC_RESULT_CODE = "sales_exec_result_code";//跟进结果
        public static final String RETURN_VISIT_EXEC_RESULT_CODE = "return_visit_exec_result_code";//回访结果
        public static final String SALES_CALL_RESULT_CODE = "call_result_code";//通话结果
        public static final String SALES_STATUS = "sales_status";//销售状态
        public static final String JOIN_DATE_TAG = "join_date_tag";//登记时间
        public static final String OPERATION_TYPE = "operation_type";//操作日志类型
        public static final String RESERVE_TYPE_CODE = "reserve_type_code";//预约类型
        public static final String JOB_STATUS = "job_status";//预约类型
        public static final String FIXED_OCCUPY_TYPE = "fixed_occupy_type";//场地占场类型
        public static final String RECHARGE_RULE_TYPE = "recharge_rule_type";//专项卡充值赠送规则
        public static final String ROLE_TYPE = "role_type";//角色类型
        public static final String REFUND_TRADE_TYPE = "refund_trade_type";//中心退款类型
        public static final String TIMEOUT_UNIT = "timeout_unit";//超时单位
        public static final String SPECIAL_PERSON_PSPT_TYPE = "special_person_pspt_type";//特惠人群证件类型
        public static final String FIELD_OCCUPY_STATUS = "field_occupy_status";//占场状态
        public static final String CALL_RESULT_CODE = "call_result_code";//占场状态
        public static final String VISITOR_PHONE_FILTER_CODE = "visitor_phone_filter_code";//潜在客户手机号错误编码
        public static final String TODO_TYPE = "todo_type";//待办事项类型
        public static final String TODO_TYPE_STATE = "todo_type_state";//待办事项状态
        public static final String PSPT_TYPE = "pspt_type";//证件类型
        public static final String PRESENT_REASON = "present_reason";//赠送原因
        public static final String NATIONALITY = "nationality";//国籍
        public static final String STU_IDENTITY_LEVEL = "stu_identity_level";//身份等级
        public static final String DOWN_PAYMENT_TRADE_TYPE_CODE = "down_payment_trade_type_code";//订金收取可选业务类型
        public static final String DOWN_PAYMENT_STATE = "down_payment_state";//订金说明
        public static final String PROM_TYPE = "prom_type";//营销活动类型
        public static final String APP_AD_CONT_TYPE = "app_ad_cont_type";
        public static final String GRADE_CHANGE_TYPE = "grade_change_type";
        public static final String GRADE_CHANGE_METHOD = "grade_change_method";
        public static final String STAFF_RELATION_TYPE = "staff_relation_type";
        public static final String COMMON_CAN_CHANGE_PAYMODE = "common_can_change_paymode";
        public static final String VENUE_AGREEMENT_TYPE = "venue_agreement_type"; //场馆协议类型
        public static final String GENDER = "gender"; //性别
        public static final String LIMIT_LIST_TYPE = "limit_list_type";//限制名单适用类型
        public static final String TICKET_FOREGIFT_REFUND_TAG = "ticket_foregift_refund_tag";
        public static final String PRINT_AGREEMENT_TYPE = "print_agreement_type";
        public static final String MENU_GROUP = "menu_group";// 菜单包
        public static final String TC_LESSON_STATE = "tc_lesson_state";
        public static final String TC_LESSON_ATTN_STATE = "tc_lesson_attn_state";
        public static final String APP_TYPE = "app_type"; // 应用类型
        public static final String USER_STATUS = "user_status"; // 用户状态
        public static final String SOCCER_POSITION = "soccer_position"; // 球员位置
        public static final String SHIRT_COLOR = "shirt_color"; // 球衣颜色
        public static final String CARD_STATE = "card_state"; // 用户状态
        public static final String ENTER_METHOD = "enter_method"; // 入馆方式
        public static final String TRAINING_TYPE_NEW = "training_type_new";// 新模型课程类型，长训和长训期间
        public static final String ENTER_TIMES_UNIT = "enter_times_unit"; // 入馆次数限制单位
        public static final String COMMON_PAY_MODE = "common_pay_mode";
        public static final String CANCEL_GAME_REASON = "cancel_game_reason"; //取消约战理由
        public static final String APP_AD_TYPE = "app_ad_type"; // 广告类型
        public static final String INVOICE_TYPE = "invoice_type";//发票类型 1-普通发票
        public static final String APP_AD_CONTENT_TYPE = "app_ad_content_type";
        public static final String CARD_RECYCLE_STATE = "card_recycle_state";//卡回收状态
        public static final String COMPANY_MANAGEMENT_TYPE = "company_management_type"; // 公司经营类型 1-直营 2-加盟
        public static final String AGREEMENT_CHANNEL = "agreement_channel"; //协议支持的渠道
        public static final String REPORT_COLUMN_GROUPING_TYPE = "report_column_grouping_type";
        public static final String REPORT_COLUMN_GROUPING_COMPUTE = "report_column_grouping_compute";
        public static final String LOCKER_LOG_TYPE = "locker_log_type";
        public static final String KEY_USE_STATE = "key_use_state";
        public static final String STATUS = "status";
        public static final String RETURN_TYPE = "return_type"; //退款方式
        public static final String LOCKER_OPEN_MODE = "locker_open_mode";
        public static final String LOCKER_USE_STATE = "locker_use_state"; // 柜子使用状态
        public static final String LOCKER_OPEN_STATE = "locker_open_state"; // 柜子开放状态
        public static final String LESSON_SIGNIN_MODE = "lesson_signin_mode"; // 课时签到方式
        public static final String SALES_MKT_CAMP_TYPE = "sales_mkt_camp_type"; // 营销推广活动类型
        public static final String SALES_MKT_CAMP_STATE = "sales_mkt_camp_state"; // 营销推广活动状态
        public static final String MEDIA_TYPE = "media_type"; // 资源类型
        public static final String DEVICE_TYPE = "device_type";//设备类型
        public static final String SIGNIN_MODE = "signin_mode";//签到方式
        public static final String LESSON_RESV_STATE = "lesson_resv_state"; // 课程预约状态
        public static final String POINTS_CHANGE_TYPE = "points_change_type";//积分异动变更类型
        public static final String STAT_INCOME_ITEM = "stat_income_item"; // 收入统计项目
        public static final String ADDITIONAL_SERVICE = "additional_service";
        public static final String BLACK_LIST_RULE_TYPE = "black_list_rule_type"; // 黑名单规则类型
        public static final String OP_SCORE_CYCLE = "op_score_cycle"; // 运营评分周期
        public static final String OP_SCORE_CALCULATE_MODE = "op_score_calculate_mode"; // 运营评分计算方式
        public static final String POINTS_LEVEL_TYPE = "points_level_type"; // 积分规则等级
        public static final String ROOM_OCCUPY_TYPE = "room_occupy_type"; // 教室占场类型
        public static final String FIELD_OCCUPY_SOURCE = "field_occupy_source"; // 场地占用来源
        public static final String PROMOTION_CONDITION = "promotion_condition"; // 营销活动资格
        public static final String PRODUCT_CHANNEL = "product_channel"; //产品支持发放的渠道
        public static final String GROUPING_GRADE = "grouping_grade"; //分组级别
        public static final String MESSAGE_NOTIFY_TYPE = "message_notify_type";//web消息通知类型
        // 到期查询类型（0-到期查询，1-过期查询）
        public static final String DEADLINE_TYPE = "deadline_type";
        public static final String DIVING_COURSE_KIND = "diving_course_kind"; //潜水课程种类
        public static final String DIVING_COURSE_TYPE = "diving_course_type"; //潜水课程类型
        public static final String DIVING_TRADE_TYPE = "diving_trade_type"; //潜水业务类型
        public static final String DIVING_SHOP_INSPECTION_FEE = "diving_shop_inspection_fee"; //潜店年检费用
        public static final String DIVING_JOB_EDUCATION = "diving_job_education"; //学历要求
        public static final String DIVING_JOB_EXPERIENCE = "diving_job_experience"; //工作经验
        public static final String DIVING_JOB_SALARY = "diving_job_salary"; //薪资待遇
        public static final String OPERATION_LOG_PROP_KEY = "operation_log_prop_key"; //日志管理变更字段
        public static final String SOURCE_TYPE = "source_type"; //客户来源
        public static final String GROUP_BUYING_TYPE = "group_buying_type"; //团购类型
        public static final String CUSTOMER_KEY = "customer_key"; //客户修改名称
        public static final String PROMOTION_KEY = "promotion_key"; //营销活动名称
        public static final String HOUSE_PROPERTY_RENTAL_KEY = "house_property_rental_key"; //房屋租赁名称
        public static final String HOUSE_RENT_TERM = "house_rent_term";        //租赁周期
        public static final String HOUSE_RENT_STATUS = "house_rent_status";        //租赁状态
        public static final String COURSE_ATTR = "course_attr";//课程属性
        public static final String PRIVACY_AGREEMENT = "privacy_agreement";//隐私协议
        public static final String SERVER_CLASS = "server_class";//场馆业务
        public static final String LESSON_TYPE = "lesson_type";//课程分类
        public static final String TICKET_CLASS = "ticket_class";//票种类
        public static final String PRODUCT_TYPE = "product_type";//产品种类
        public static final String SETTLE_CYCLE = "settle_cycle";//结算周期
        public static final String GOODS_CLASS = "goods_class";//产品种类
        public static final String CARD_CLASS = "card_class";//一卡通种类（重体支付）
        public static final String BENEFIT_CYCLE = "benefit_cycle";//分润周期
        public static final String COURSE_LABEL = "course_label";//课程标签

        //AA约球
        public static final String INITIATOR_SERVICE = "initiator_service";//提供的服务
        public static final String PAY_TYPE = "pay_type";//约球支付方式
        public static final String VIOLATION_TYPE = "violation_type";//违规类型
        //场馆徽章背景图
        public static final String MEDAL_BACKGROUND_IMG = "medal_background_img";

        //号码牌规则
        public static final String FIX_TAG = "fix_tag";//是否固定
        public static final String NUMBER_TAG = "number_tag";//数字或字母

        public static final String SERVICE_RESV_STATE = "service_resv_state"; // 项目预约状态
        public static final String ENROLLMENT_OBJECT = "enrollment_object"; //招生对象
        public static final String TRAIN_NATURE = "train_nature"; //培训场所性质
        public static final String CHUZHOU_HOLIDAY_CONFIG = "chuzhou_holiday_config"; // 滁州的全名日配置


        public static final String GROUP_COURSE_DIFFICULTY = "group_course_difficulty";//团课难度

        public static final String NX_SERVICE_TYPE = "nx_service_type";//宁夏服务类型
        public static final String NX_COURSE_INSTRUCTOR_TYPE = "nx_course_instructor_level";//宁夏课程指导员类型
        public static final String NX_LOTTERY_AWARD_TYPE = "nx_lottery_award_type";//宁夏抽奖奖品类型

        public static final String NX_LOTTERY_AWARD_DEFAULT_IMG = "nx_lottery_award_default_img";
        public static final String NX_ANSWER_TYPE = "nx_answer_type";//宁夏题库题型
    }

    /**
     * 是否固定：1-固定 2-非固定
     */
    public static class FixTag {
        public static final String FIXED = "1";
        public static final String UNFIXED= "2";
    }

    /**
     * 数字或字母：1-数字 2-字母
     */
    public static class NumberTag {
        public static final String NUMBER = "1";
        public static final String LETTER= "2";
    }

    /**
     * 场馆业务种类
     */
    public static class ServerClass {
        public static final String LESSON = "1";//课程
        public static final String TICKET = "2";//票
        public static final String PRODUCT = "3";//产品
        public static final String GOODS = "4";//商品
        public static final String CARD = "5";//一卡通（重体支付）
    }

    /**
     * 潜水课程种类
     */
    public static class DivingCourseKind {
        public static final String SPECIALTY_DIVER = "1";//专长潜水员课程
        public static final String DIVER = "2";//潜水员课程
        public static final String COACH = "3";//教练员课程
    }

    /**
     * 潜水课程类型
     */
    public static class DivingCourseType {
        public static final String LEISURE = "1";//休闲潜水
        public static final String EMERGENCY = "2";//应急救援与公共安全潜水
    }

    /**
     * 潜水课程是否年检
     */
    public static class DivingIfInspect {
        public static final String YES = "1";//是
        public static final String NO = "0";//否
    }

    /**
     * 潜水业务状态
     */
    public static class DivingTradeStatus {
        public static final String INVALID = "0";//无效
        public static final String VALID = "1";//有效（审核通过）
        public static final String UNDER_REVIEW = "2";//审核中
        public static final String FAIL_REVIEW = "3";//审核不通过
    }

    /**
     * 潜水业务类型
     */
    public static class DivingTradeType {
        public static final String LEARNING_QUALIFICATIONS = "1";//学习资质材料审核
        public static final String CERTIFICATE_PREPARATION = "2";//制证材料审核
        public static final String SHOP_INSPECTION = "3";//潜店年检审核
        public static final String COACH_INSPECTION = "4";//教练年检审核
        public static final String ADD_COACH = "5";//新增教练审核审核
    }

    /**
     * 潜水后台消息配置名称
     */
    public static class DivingNewsConfigName {
        public static final String BirthdayWishes = "1";//生日祝福
        public static final String AnnualCheckNotice = "2";//年检通知
    }

    public static class SessionKey {
        public static final String MENU = "session_menu";
        public static final String FAVORITE_MENU = "session_favorite_menu";
        public static final String STAFF = "session_staff";
        public static final String SESSION_ID = "session_id";
        public static final String MERCHANT = "session_merchant";
        public static final String SHOP = "session_shop";
        public static final String DASHBOARD_LAYOUT = "session_dashboard_layout";
        public static final String LOGIN_TIME = "session_login_time";
        public static final String WEB_REPORT_LOGGED_IN = "session_web_report_logged_in";
        public static final String DASHBOARD_CSS = "session_dashboard_css"; // 工作台的css文件列表
        public static final String DASHBOARD_JS = "session_dashboard_js"; // 工作台的js文件列表
        public static final String LOGIN_DOMAIN = "session_login_domain"; // 登录域名
        public static final String PASSWORD_IS_OVERDATE = "session_password_is_overdate"; // 密码是否过期
        public static final String PASSWORD_OVERTIME = "session_password_overtime"; // 密码过期时间
        public static final String FIRST_LOGIN = "session_first_login"; // 首次登陆
        public static final String LOSS_MOBILE_NUM = "session_loss_mobile_num"; // 缺少手机号标识
        public static final String APP_USER = "session_app_user";
        public static final String THEME = "session_theme";
        public static final String LOGO_URL = "session_logo_url";
        public static final String LOGIN_SITE = "sessionLoginSite";
        public static final String FIELD_TICKET_QUERY_PARAMS = "sessionFieldTicketQueryParams";
        public static final String LOGIN_PASSWORD_ERROR_MESSAGE = "session_login_password_error_message";
        public static final String KEY_INFO_IMPORT_RESULT = "session_key_info_import_result";
        public static final String IE_BROWSER = "session_ie_browser";
        public static final String PARK_SHOW_TAG = "session_park_show_tag";
    }

    public static class RoleLevel {
        public static final String STAFF = "1";
        public static final String VENUE = "2";
        public static final String CENTER = "3";
        public static final String OTHER = "0";

    }

    public static class ExecResultCode {
        public static final String EXEC_NO = "0";//0：确定不入会
        public static final String EXEC_YES = "1";// 1：确定入会
        public static final String EXEC_NEXT_APT = "2";//2：下次联系
    }

    public static class TradeAuditState {
        // 已审批
        public static final String AUDIT = "1";
        // 未审批
        public static final String UN_AUDIT = "0";
        public static final String CANCELED = "2";
    }

    public static class PrintTemplateType {
        public static final String RECEIPT = "1"; // 收据
        public static final String TICKET = "2"; // 票
        public static final String GOODS = "3"; // 商品销售
        public static final String FEIE = "4"; // 飞鹅
        public static final String DETAIL_RECEIPT = "5"; // 转卡等业务收据（历史遗留）
        public static final String GROUP_TICKET = "6"; // 团体票
        public static final String WANGO_RECEIPT = "7"; // 定金（万国收据）
        public static final String COUPON = "8"; // 券
        public static final String ON_PRINT = "9"; // 套打（目前办卡）
        public static final String PRIVATE_COURSE = "10"; // 私教
        public static final String REPORT = "11"; // 报表
        public static final String PROJECT_QUEUE = "12"; // 项目排队
        public static final String GOODS_FETCH = "13"; // 待自提商品信息
        public static final String PARKING_CHARGE = "14"; // 停车小票
        public static final String ENTITY_COUPON_FRONT = "15"; // 实体券(正面)
        public static final String ENTITY_COUPON_BACK = "16"; // 实体券(反面)
    }

    public static class BookState {
        public static final String BOOKED = "0"; // 预定未取票
        public static final String TICKET_PICKED_UP = "1"; // 已取票
        public static final String CANCELED = "2"; // 未取票取消预定
        public static final String OCCUPY_BOOKED = "3"; // 占场预约未支付,占场规则
        public static final String ADVANCE_OCCUPY = "4"; // 场地预占
        public static final String UNPAID = "9"; // 预订未支付,前台售场地
    }

    public static class MedalType {
        public static final int ONE_YEAR_ANNIVERSARY = 1; // 杭州奥体一周年
    }

    /**
     * 费用项类型
     */
    public static class FeeitemType {
        public static final int HANDLING_CHARGE = 0; // 手续费
        public static final int TRADE_FEE = 1; // 业务费
        public static final int EXPRESS_FEE = 2; // 快递费
    }

    public static class ExecMode {
        public static final String CHARGE = "0"; // 费用收取
        public static final String RETURN = "1"; // 费用退还
    }

    public static class ChargeMode {
        public static final short AMOUNT = 0; // 金额
        public static final short PERCENT = 1; // 比例
    }

    public static class PromType {
        public static final String ECARD_RECHARGE = "0"; // 一卡通
        public static final String GIFTS = "1"; // 赠送
        public static final String LIMITED_TIME = "2"; // 限时优惠
    }

    public static class ProductMode {
        public static final String BASIC = "01"; // 基本产品
        public static final String GIFTS = "02"; // 赠送产品
        public static final String GIFTS_CARD = "03"; // 赠送专项卡
        public static final String COURSE = "04"; // 普通课程(不用)
        public static final String PRIVATE_TRAINING = "05"; // 私教课程(不用)
        public static final String TRAIN = "06"; // 培训
    }

    public static class ProductPrice {
        public static final String MUTIL_DISCOUNT_TYPE = "1";
        public static final String INDEX_DISCOUNT_TYPE = "2";
        public static final String LADDER_DISCOUNT_TYPE = "3";
        public static final String CASH_VALUE_TYPE = "1";
        public static final String DISCOUNT_VALUE_TYPE = "2";
        public static final int CASH_EXCHANGE_RATE = 100;
        public static final int DISCOUNT_EXCHANGE_RATE = 10;
    }

    public static class AuditTag {
        public static final String APPROVAL = "1"; // 审核通过
        public static final String REJECT = "0"; // 审核拒绝
        public static final String CANCEL = "2"; // 取消
    }

    public static class DrawbackFlag {
        public static final String YES = "1"; // 已经退款
        public static final String NO = "0"; // 未退款
    }

    public static class EnterTag {
        public static final String IN = "1"; // 入馆
        public static final String OUT = "2"; // 出馆
        public static final String IN_OR_OUT = "3"; //出入馆
    }

    public static class DepositStatus {
        public static final String NORMAL = "0"; //正常
        public static final String FROZEN = "1"; //冻结
        public static final String CARD_WITHDRAW = "2"; //退专项卡
        public static final String TRANSFER_CARD = "3"; //已转卡
        public static final String DELAY_CARD = "4"; //卡延期
        public static final String FREEZE_CARD_PENDING = "5"; //冻结审核状态
        public static final String CANCEL_CARD = "6"; //返销状态
        public static final String UPGRADE_CARD = "7"; //已升卡
        public static final String CARD_WITHDRAW_APPROVE = "8"; //退专项卡审核
        public static final String CARD_UPGRADE = "15"; //已升级
    }

    /**
     * 是否允许电子卡充值
     */
    public class ElectronicCardRecharge {
        //允许
        public static final String ALLOWED = "1";
        //不允许
        public static final String UNALLOWED = "0";
    }

    /**
     * 场馆参数定义
     */
    public static class VenueParam {
        public static final String SWITH = "swith";//场馆二维码前缀
        public static final String VENUE_QRCODE_PREFIX = "venue_qrcode_prefix";//场馆二维码前缀
        public static final String ATM_COUPON_NEED_PSPT_ID = "atm_coupon_need_pspt_id";//体验券是否要身份证验证
        public static final String VENUE_ELECTRON_CARD = "venue_electron_card"; //场馆电子卡参数
        public static final String VENUE_ENTER_TIMES = "venue_enter_times"; // 入馆次数
        public static final String VENUE_LEAVE_DURATION = "venue_leave_duration"; // 离开场馆的时长
        public static final String HAS_KEY = "has_key"; // 场馆的项目是否需要钥匙扣
        public static final String VENUE_RETURN_HOUR_WECHAT = "venue_return_hour_wechat";//微信退票时间
        public static final String WEIXIN_SERVICES = "weixin_services";//微信服务
        public static final String WECHAT_PAY_ACCOUNT = "wechat_pay_account";//中心微信支付公众号
        public static final String APP_WECHAT_PAY_ACCOUNT = "app_wechat_pay_account";//app中心微信支付账号
        public static final String APP_ALIPAY_ACCOUNT = "app_alipay_account";//app中心支付宝支付账号
        public static final String APP_ALIPAY_REFUND_ACCOUNT = "app_alipay_refund_account";//app中心支付宝退款账号
        public static final String AUDIT_ROLE = "audit_role";   //具有审核权限的角色
        public static final String AUDIT_NODE_TYPE = "audit_node_type"; //场馆审核节点类型
        public static final String AUDIT_NODE_ROLE = "audit_node_role"; //场馆审核角色
        public static final String PERIOD_CUSTOMIZE_TIME = "period_customize_time"; //办理期间卡是否可以选定时间
        public static final String VENUE_RESERVE_TAG = "venue_reserve_tag"; //场馆服务是否可以预定状态
        public static final String ECARD_QUERY_BY_POSTFIX = "ecard_query_by_postfix"; // 中心电话预定场地 自动补全卡号
        public static final String VENUE_WEB_PAY_ACCOUNT = "venue_web_pay_account"; // web场馆支付的微信账号
        public static final String VENUE_APP_PAY_ACCOUNT = "venue_app_pay_account"; // app场馆支付的微信账号
        public static final String WECHAT_MINI_APP_PAY_ACCOUNT = "wechat_mini_app_pay_account"; // 小程序场馆支付的微信账号
        public static final String VENUE_APP_ALIPAY_ACCOUNT = "venue_app_alipay_account"; // app场馆支付的支付宝账号
        public static final String VENUE_APP_ALIPAY_REFUND_ACCOUNT = "venue_app_alipay_refund_account"; // app场馆退款的支付宝账号
        public static final String CENTER_CHANNEL_ID = "center_channel_id"; //产业中心对应的渠道Id
        public static final String PAY_BY_QRCODE = "pay_by_qrcode"; //中心扫码支付
        public static final String RECHARGE_BATHE_PRICE = "recharge_bathe_price"; //洗澡充值单位
        public static final String AUTO_ENTER_HALL = "auto_enter_hall";//是否买完次票后自动入馆
        public static final String FIELD_SEGMENT_TAG = "field_segment_tag";//微信场地根据开始时段还是结束时段判断是否可订,0-开始/1-结束
        public static final String PAY_ADVANCED_SEARCH = "pay_advanced_search";//中心高级查询
        public static final String DEFAULT_MANY_PERSON = "default_many_person";//入馆时默认多人入馆
        public static final String ENTER_HALL_TRAINING_ADVANCED_QUERY = "enter_hall_training_advanced_query";   //培训入馆是否拥有高级搜索功能
        public static final String LESSON_PRICE_DISCOUNT = "lesson_price_discount";//启迪折扣
        public static final String AD_ACCOUNT_TAG = "ad_account_tag";//查询中心是否显示AD账号
        public static final String FAMILY_CARD_TAG = "family_card_tag";//查询中心是否家庭成员
        public static final String AUTHORIZE_PLATFORM_ECARD_TAG = "authorize_platform_ecard_tag";//场馆一卡通授权平台
        public static final String COURSE_AGREEMENT_SERVICE_TAG = "course_agreement_service_tag";//场馆对于服务选择项是否展示1-展示，0-不展示'
        public static final String CAMP_HAS_CITY_LEVEL_TAG = "camp_has_city_level_tag";//赛事活动市等级标志
        public static final String DIFFERENTIATE_THE_EVENT_TAG = "differentiate_the_event_tag";//是否区分活动和赛事，1：区分，0：不区分
        public static final String COUPON_PRODUCT_TAG = "coupon_product_tag";//优惠券活动是否开启专项卡筛选
        public static final String VISITOR_TAG = "visitor_tag";//是否根据权限判断全部潜客

        // 生日提醒天数, 0表示只提醒当天
        public static final String BIRTHDAY_REMIND_DYAS = "birthday_remind_days";
        // 自助终端一卡通支付是否需要密码 0-不需要密码 1-需要密码
        public static final String ATM_PASSWORD_TAG = "atm_password_tag";
        // 售票(场地票)时是否需要录入电话号码 1-是 0-否
        public static final String SELL_TICKET_PHONE_TAG = "sell_ticket_phone";
        // 场地预订 0-不启用 1-余额不足时可以选择预定不支付 2-选择立即支付或者预定不支付
        public static final String FIELD_BOOK_OCCUPY_TAG = "field_book_occupy_tag";
        // 微信一卡通充值，值里面是价格以逗号分隔，单位是分
        public static final String WECHAT_ECARD_RECHARGE = "wechat_ecard_recharge";
        // 微信一卡通充值赠送金额，值里面是价格以逗号分隔，单位是分
        public static final String WECHAT_ECARD_RECHARGE_PROM = "wechat_ecard_recharge_prom";
        public static final String CARD_GROUP_TAG = "card_group_tag"; // 中心是否有团体卡
        public static final String WECHAT_CENTER_PERMISSION = "wechat_center_permission"; //微信公众号是否允许微信支付
        // 挂帐流程
        public static final String ON_CREDIT_PROCESS = "on_credit_process";
        // 是否可以挂帐
        public static final String ON_CREDIT_TRADE_TYPES = "on_credit_trade_types";
        // 手工减免审核流程
        public static final String MANUAL_REDUCE_PROCESS = "manual_reduce_process";
        // 手工减免是否提前审核标志 0-否 1-是
        public static final String MANUAL_REDUCE_AUDIT_TAG = "manual_reduce_audit_tag";
        // 支付后立即录入发票的业务类型
        public static final String INPUT_INVOICE_TRADE_TYPES = "input_invoice_trade_types";
        // 一卡通或专项卡支付时是否需要密码
        public static final String CARD_PAY_REQUIRE_PASSWORD = "card_pay_require_password";
        // 一卡通或专项卡支付时是否需要展示默认密码
        public static final String CARD_PAY_SHOW_DEFAULTPASSWORD = "card_pay_show_defaultPassword";
        // 专项卡退卡流程(1表示根据支付方式来退款,0表示全款退到一卡通中)
        public static final String BACK_CARD_RETURN_MONEY_TAG = "back_card_return_money_tag";
        // 协议占场最早可选作日期的偏移天数
        public static final String AGREEMENT_RESERVE_MIN_DAYS = "agreement_reserve_min_days";
        // 协议占场是否可以不支付
        public static final String CYCLE_OCCUPY_UNPAID_TAG = "cycle_occupy_unpaid_tag";
        // 协议占场可以不支付，然后取消的天数
        public static final String CYCLE_OCCUPY_UNPAID_DAYS = "cycle_occupy_unpaid_days";
        // 协议占场，在每次支付的时候是否能够享受折扣，为1表示享受。
        public static final String CYCLE_OCCUPY_UNPAID_DISCOUNT = "cycle_occupy_unpaid_discount";
        // 协议占场不支付,是否支持使用一卡通自动支付
        public static final String CYCLE_OCCUPY_AUTO_PAY = "cycle_occupy_auto_pay";
        // 大屏多页面选择配置
        public static final String MULTIPLE_PAGE = "multiple_page";
        // 不能键盘输入卡号 true 或者 false
        public static final String NOT_KEYBOARD_INPUT_CARD = "not_keyboard_input_card";
        // 身份证、手机号等隐藏信息显示形式, 格式为 1,2 表示前面显示1位, 后面显示2位
        public static final String SENSITIVE_NUM_SHOW = "sensitive_num_show";
        // 场馆订场规则(用于微信,app等)
        public static final String VENUE_BOOKING_RULES = "venue_booking_rules";
        // 常熟公会包场规则
        public static final String CHANG_SHU_VENUE_BOOKING_RULES = "chang_shu_venue_booking_rules";
        // 微信次票每天每人最多购买张数
        public static final String PERSON_DAY_BUY_LIMIT = "person_day_buy_limit";
        // 场馆是否可以预约占场
        public static final String BOOKING_FIELD_TAG = "booking_field_tag";
        // 保龄球馆是否只能买当前时段的票
        public static final String BOWLING_TICKET_TAG = "bowling_ticket_tag";
        // 场地预定须知
        public static final String SERVICE_BOOKING_MANUAL = "service_booking_manual";
        // 中心一卡通名
        public static final String CENTER_ECARD_NAME = "center_ecard_name";
        // 中心一卡通支付是否需要验证码
        public static final String FIELD_COACH_SERVICES = "field_coach_services"; // 预定场地时需要选择教练的service
        public static final String IS_VERIFY_CODE_SEND = "is_verify_code_send";
        public static final String PHOTO_NOT_REQUIRED = "photo_not_required"; // 激活卡时，是否需要校验照片信息
        public static final String CABINET_NEED_FREEZE = "cabinet_need_freeze"; // 冻结卡时，柜子是否需要顺延
        public static final String FREEZE_CARD_AUDIT_CHECK = "freeze_card_audit_check"; // 是否进行冻卡审核前的检查
        public static final String PASSWORD_OVERTIME = "password_overtime"; // 密码过期时间
        public static final String DEFAULT_APPLY_CARD = "default_apply_card"; //办卡页面默认选择的卡种，1-一卡通，2-专项卡
        // 余额储值卡有效期类型  1卡有效期 2折扣有效期
        public static final String CARD_VALIDITY_TYPE = "card_validity_type";
        public static final String COMBINE_DEPOSIT_TAG = "combine_deposit_tag"; // 是否合并成一个账本
        // 办卡是否显示选择教练选项
        public static final String APPLY_CARD_SELECT_COACH = "apply_card_select_coach";
        //分期不分班课程可以入馆
        public static final String COURSE_NOT_NEED_DIVIDE = "course_not_need_divide";
        //场馆需要特殊退票
        public static final String NEED_SPECIAL_REFUND_TICKET = "need_special_refund_ticket";
        // 一卡通支付场地票时的折扣是否按会员级别设置
        public static final String ECARD_TICKET_DISCOUNT_BY_GRADE = "ecard_ticket_discount_by_grade";
        // 微信订场默认全场标志
        public static final java.lang.String WECHAT_DEFAULT_FIELD_FULL_TAG = "wechat_default_field_full_tag";
        //更换场地的标识
        public static final String CHANGE_FIELD_TAG = "change_field_tag";
        //场馆水务控制标志-出馆
        public static final String WATER_CONTROL_TAG = "water_control_tag";
        //场馆水务控制标志-入馆
        public static final String ENTER_HALL_INIT_WATER = "enter_hall_init_water";
        //场馆水务控制初始化数据量
        public static final String WATER_INIT_AMOUNT = "water_init_amount";
        //是否校验usb_key
        public static final String USB_KEY = "usb_key";
        public static final String COURSE_ENTERHALL_PRINT_TICKET = "course_enterhall_print_ticket"; //培训入馆是否需要打印
        public static final String INVENTORY_COSTING_METHOD = "inventory_costing_method"; //库存结算方式
        public static final String GOODS_SUPPORT_SUPPLIER = "goods_support_supplier"; //商品支持供应商
        public static final String CHANGE_UPGRADE_CARD_FEE = "change_upgrade_card_fee"; //升卡差价是否可以手动修改
        public static final String ENTER_HALL_REQUIRE_PASSWORD = "enter_hall_require_password";
        public static final String INVENTORY_COSTING_DAY = "inventory_costing_day"; //库存结算日
        public static final String FIELD_REPLACE_TYPE = "field_replace_type";  //场地更换类型 0为只能更换同场地类型 1可以更换不同时间、不同场地类型的
        public static final String ALIPAY_AUTH = "alipay_auth";
        public static final String KOUBEI_ALIPAY_AUTH = "koubei_alipay_auth";
        public static final String ALIPAY_ISV = "alipay_isv";
        public static final String ALI_WEB_PAY_ACCOUNT = "ali_web_pay_account";
        public static final String ALI_H5_PAY_ACCOUNT = "ali_h5_pay_account"; //支付宝h5支付支付账号
        public static final String BIND_KEY = "bind_key";
        public static final String ATM_PAGE_URL = "atm_page_url"; //登录自助终端跳转链接
        public static final String ATM_SCROLL_BAR = "atm_scroll_bar"; //自助终端滚动条
        public static final String PRINT_TICKET_AT_RECEIPT = "print_ticket_at_receipt";
        //中心充值金额是否可以自定义
        public static final String RECHARGE_CUSTOM = "recharge_custom";
        //最小充值金额，单位分
        public static final String WECHAT_ECARD_CHARGE_MIN_MONEY = "wechat_ecard_charge_min_money";
        public static final String RECOMMENDATION_SERVICES = "recommendation_services";
        //场馆营业时间格式: 0800,2330
        public static final String VENUE_BUSINESS_HOUR = "venue_business_hour";
        public static final String ECARD_GRADE_DISCOUNT_TAG = "ecard_grade_discount_tag"; // 按一卡通会员级别打折标志 1-是
        public static final String ECARD_GRADE_DISCOUNT_GRADES = "ecard_grade_discount_grades"; // 享受一卡通会员折扣的会员级别
        public static final String COURSE_ECARD_GRADE_DISCOUNT_TAG = "course_ecard_grade_discount_tag"; // 课程的一卡通会员折扣标志
        public static final String RECHARGE_GRADE_RULE = "recharge_grade_rule"; // 一卡通会员级别充值规则
        public static final String RECHARGE_GRADE_PAY = "recharge_grade_pay"; // 一卡通会员级别充值，按订单金额算，不按实付金额算
        public static final String TRAINING_ENTRY_RULE = "training_entry_rule";//培训入馆规则
        public static final String WECHAT_SIGNUP_NEW_CARD_TAG = "wechat_signup_new_card_tag"; // 网络用户注册时是否自动生成电子卡
        public static final String ECARD_GRANTS_USE_RULE = "ecard_grants_use_rule"; // 一卡通赠款使用规则，一卡通赠款账本可以支付哪些业务
        public static final String LESSON_REMIND_TIME = "lesson_remind_time";   //课程结账提醒时间
        public static final String GOODS_SALE_CHECK_STOCK = "goods_sale_check_stock"; //销售商品是否校验库存
        public static final String WECHAT_CAN_ECARD_RECHARGE = "wechat_can_ecard_recharge"; //微信是否支持一卡通充值,1-支持,0-不支持
        public static final String WECHAT_ELECTRO_CARD_RECHARGE = "wechat_electro_card_recharge"; // 是否允许电子卡在微信上充值
        public static final String COURSE_AUTO_EXECUTE = "course_auto_execute"; //场馆支持课程自动扣次
        public static final String FIELD_TICKET_ENTER_GATE_RULE = "field_ticket_enter_gate_rule";//闸机验票入馆规则
        public static final String WECHAT_DEFAULT_BOOKING_RULES = "wechat_default_booking_rules";
        public static final String WECHAT_INVOICE_TRADE_TYPE = "wechat_invoice_trade_type";//微信开具发票的业务类型
        public static final String INVOICE_TRADE_TYPE = "invoice_trade_type";//微信开具发票的业务类型
        public static final String MONEY_CARD_ENTER_HALL = "money_card_enter_hall";//余额专项卡是否支持入馆
        public static final String GOODS_TRANSFER_NOT_NEED_CHECK = "goods_transfer_not_need_check";//库存调拨校验余量
        public static final String GIFT_TIMES_CARD_ACTIVATE_LIMIT = "gift_times_card_activate_limit";//次卡赠卡的激活限制
        public static final String AUTO_GATE_ENTER_HALL_RULE = "auto_gate_enter_hall_rule";
        public static final String RACING_MIN_SIGN_NUM = "racing_min_sign_num";//定向赛本赛点签到几人后可看到下赛点信息参数
        public static final String COURSE_ENTERHALL_HAS_TICKET = "course_enterhall_has_ticket";//培训入馆是否需要生成票
        public static final String ENTER_HALL_TIMING_CARD_REPEAT_ALERT = "enter_hall_timing_card_repeat_alert";//时间卡重复入馆警告
        public static final String SALES_EXPIRE_DATE = "sales_expire_date"; //会籍顾问分配到期时间
        public static final String ENTER_HALL_RULE = "enter_hall_rule";//入馆规则
        public static final String SHOW_TICKET_WHEN_CHECK = "show_ticket_when_check";// 展示状态是验票的票
        public static final String ECARD_TRANSFER_USE_GRANTS = "ecard_transfer_use_grants"; //一卡通转账是否支持赠款
        public static final String BM_PRINT_TEMPLATE_ID = "bm_print_template_id"; // 体测打印小票ID
        public static final String BM_GET_RPT_LIMIT_DAYS = "bm_get_rpt_limit_days"; // 体测定时任务获取结果报告的天数限制
        public static final String ATM_TRADE_SEARCH_DAYS = "atm_trade_search_days"; // 自助终端查询订单的天数限制， 例如：30就是查询30天以内的订单
        public static final String EXERCISE_GUIDE_TAG = "exercise_guide_tag"; // 运动指导存在标记
        public static final String SPECIAL_PERSON_GRADE_ID = "special_person_grade_id"; // 中心特惠人群会员id
        // 自助终端是否支持人脸识别  false-不需要 true-需要
        public static final String ATM_FACE_RECOGNITION_TAG = "atm_face_recognition_tag";
        public static final String CENTER_POS_ORG = "center_pos_org"; //中心支付的pos机账号
        public static final String LESSON_RESV_DEADLINE = "lesson_resv_deadline"; //课程预约不能预约的时间
        public static final String SPECIAL_PERSON_NEED_PSPT = "special_person_need_pspt"; //特惠人群买场地票是否需要输入证件号码
        public static final String NOT_USE_DEFAULT_PASSWORD = "not_use_default_password"; //一卡通会员是否可以使用默认密码
        public static final String FIELD_BOOK_CHANGE_ECARDNO = "field_book_change_ecardno"; //场地预订支付是否可以修改卡号
        public static final String COURSE_ENTER_CAN_BRUSH_KEY = "course_enter_can_brush_key";
        public static final String COURSE_REFUND_MONEY_TO_BANK = "course_refund_money_to_bank"; //退课钱退到银行
        public static final String ENTRY_PAY_WITHOUT_EXIT = "entry_pay_without_exit";   //出馆功能缴费后不立即出馆标志（用于出馆结算）
        public static final String ENTRY_PAY_BUFFER_TIME = "entry_pay_buffer_time";   //缓冲时间单位分钟
        public static final String WECHAT_FIELD_OPEN_TIME = "wechat_field_open_time";   //微信场地开放时间段
        public static final String JNQM = "jnqm";   //微信场地开放时间段
        public static final String FIELD_BOOK_LOADING_MINUTES = "field_book_loading_minutes";   //场地预订开场前多少分钟需要加载
        public static final String WECHAT_FIELD_SHOW_DISCOUNT_PRICE_TAG = "wechat_field_show_discount_price_tag";   //微信场地展示一卡通折扣价格标志，1-展示，其他-不展示
        public static final String VISITOR_PHONE_FILTER_RULES = "visitor_phone_filter_rules"; //潜在客户手机号规则校验
        public static final String VISITOR_RECOVER_RULES = "visitor_recover_rules"; //潜在客户回收规则
        public static final String MEMBER_RECOVER_RULES = "member_recover_rules"; //会员客户回收规则
        public static final String VISITOR_REMIND_RULES = "visitor_remind_rules"; //潜在客户提醒规则
        public static final String MEMBER_REMIND_RULES = "member_remind_rules"; //会员客户提醒规则
        public static final String SMS_SEND_TIME_RANGE = "sms_send_time_range";   //短信发送范围，主要用于sms_send.plan_send_time
        public static final String SHOW_CENTER_DETAIL = "show_center_detail"; //中心大屏是否显示中心详情
        public static final String ACMEWAY_URL = "acmeway_url"; //奥美体测URL
        public static final String BM_STATION_OPEN_TIME = "bm_station_open_time"; //体测站开放时间
        public static final String NEW_DEPOSIT_TIME_DELAY_RULE = "new_deposit_time_delay_rule"; //新办专项卡开卡时间顺延规则
        public static final String ATM_TAKE_TICKET_SHOW = "atm_take_ticket_show"; //自助终端预定取票显示
        public static final String ECARD_RECHARGE_DEVELOPER_TAG = "ecard_recharge_developer_tag"; //一卡通充值是否记录发展人
        public static final String ECARD_ENTER_HALL_TAG = "ecard_enter_hall_tag";//是否支持一卡通买票入馆
        public static final String FIELD_DEFAULT_NAME = "field_default_name";//场地默认名
        public static final String COMP_PAY_TAG = "comp_pay_tag"; //是否可以合并支付增加中心参数控制,合并支付标志 1-支持 0-不支持，默认不支持
        public static final String TRAINING_MANAGE_CHOOSE_COACH = "training_manage_choose_coach";//课程管理显示选择教练
        public static final String PRIVATE_COURSE_RESERVE_TAG = "private_course_reserve_tag"; //私教是否需要预约，1-需要，其他不需要
        public static final String PRIVATE_COURSE_RESV_DAYS = "private_course_resv_days"; //私教课预约天数
        public static final String PRIVATE_COACH_UNLIMIT_TAG = "private_coach_unlimit_tag"; //私教课不限制标记，1-报名的课程下所有教练都能预约，其他只能预约报名时选择的教练
        public static final String PRIVATE_COURSE_RESV_MINUTES = "private_course_resv_minutes"; //私教提前预约分钟数
        public static final String PRIVATE_COURSE_CANCEL_HOUR = "private_course_cancel_hour"; //私教提前取消小时数
        public static final String CARD_READER_CHECK = "card_reader_check";//是否校验读卡器
        public static final String PERSONAL_CARD_CHECK_PSPT_TAG = "personal_card_check_pspt_tag";//个人办卡时，校验证件类型和证件号码，若相同则标记续会
        public static final String NONEED_AGREEMENT_TRADETYPES = "noneed_agreement_tradetypes";//不需要必须签署协议的交易类型
        public static final String ENTER_HALL_PRINT_TICKET = "enter_hall_print_ticket";//期间卡入馆票是否需要打印
        public static final String TIME_CARD_ENTER_HALL_PRINT_TICKET = "time_card_enter_hall_print_ticket";//计次卡入馆是否需要打印票，并且票状态是已取票状态
        public static final String TRAINING_ENTER_HALL_PRINT_TICKET = "training_enter_hall_print_ticket";//培训入馆是否需要打印票，并且票状态是已取票状态
        public static final String ENTERPRISE_CARD_ENTER_HALL_PRINT_TICKET = "enterprise_card_enter_hall_print_ticket";//企业卡入馆票是否需要打印
        public static final String WECHAT_FIELD_SHARE_SERVICES = "wechat_field_share_services";//场地票是否允许分享

        public static final String TRAINING_COURSE_NEW_MODEL = "training_course_new_model";//培训新模型标识
        public static final String PRIVATE_COURSE_CHECK_ENTER_HALL = "private_course_check_enter_hall";//签到检查入馆情况
        public static final String OCM_PRIVATE_COURSE_CHECK_ENTER_HALL = "ocm_private_course_check_enter_hall";//一厘米签到检查入馆情况
        public static final String CHECK_PSPT_ID = "check_pspt_id";//中心校验证件号标识
        public static final String ALI_PUSH_CUST_FACE = "ali_push_cust_face";//是否支持ali人脸识别
        public static final String COURSE_NAME_ALTER_TAG = "course_name_alter_tag";//课程发布后是否可以修改课程名称
        public static final String WECHAT_COUPON_NEED_NAME = "wechat_coupon_need_name";//微信领券是否需要填姓名，1-需要，其他-不需要
        public static final String PRIVATE_COURSE_ENTRY_INFO_TAG = "private_course_entry_info_tag";//私教扣次是否保存入馆记录
        public static final String TIMES_CARD_SKIP_UNAVAILABLE_DATE = "times_card_skip_unavailable_date";//次卡有效期是否跳过不可用的时间
        public static final String ECARD_RECHARGE_MANUAL_REDUCTION_TAG = "ecard_recharge_manual_reduction_tag";//一卡通充值是否可以减免
        public static final String DEFAULT_PAY_MODE = "default_pay_mode"; // 默认支付方式
        public static final String WECHAT_LEAVE_CLASS_TAG = "wechat_leave_class_tag"; // 场馆微信是否可以请假标志，1-可以，其他-不可以
        public static final String WECHAT_LEAVE_CLASS_ADVANCE_HOUR = "wechat_leave_class_advance_hour"; // 场馆微信可以提前请假的小时数
        public static final String WECHAT_CANCEL_LEAVE_ADVANCE_MINUTES = "wechat_cancel_leave_advance_minutes"; // 场馆微信可以提前取消请假的分钟数
        public static final String WECHAT_LEAVE_CLASS_DAYS = "wechat_leave_class_days"; // 场馆微信可以请假的天数,0-只能请当天
        public static final String TICKET_ENTERHALL_CONTINUE_TAG = "ticket_enterhall_continue_tag"; // 次票入馆是否可以续时
        public static final String APP_CASH_REGISTER_HOME_PAGE = "app_cash_register_home_page"; // 收银机app首页
        public static final String SIMPLE_TICKET_SALE_INPUT_PHONE = "simple_ticket_sale_input_phone"; // 次票销售是否录入手机号
        public static final String ANON_ECARD_DEPOSIT_VALUE = "anon_ecard_deposit_value"; // 不记名一卡通押金金额
        public static final String SIMPLE_TICKET_CASH_PLEDGE_TAG = "simple_ticket_cash_pledge_tag"; // 次票押金是否需要配置标志 1-需要配置，0-不需要
        public static final String SIMPLE_TICKET_GROUP_BUYING_TAG = "simple_ticket_group_buying_tag"; // 次票团购是否需要配置标志 1-需要配置，0-不需要
        public static final String SIMPLE_TICKET_SALE_SITES_TAG = "simple_ticket_sale_sites_tag"; // 次票服务点是否需要配置标志 1-需要配置，0-不需要
        public static final String REGISTERED_MEMBER_TAG = "registered_member_tag";//注册会员
        public static final String MEMBER_QUERY_EXPORT_DATA_TAG = "member_query_export_data_tag";//会卡综合查询导出按钮 1-可以导出，其他不可以导出，默认不可以导出
        public static final String UPDATE_KEY_STATE = "update_key_state";// 验票验卡后是否更新钥匙扣状态，默认更新
        public static final String ANONYMITY_CARD_TAG = "anonymity_card_tag"; //中心支持不记名卡标识
        // 退款方式（是否支持现金退款）
        public static final String RETURN_CASH_TAG = "return_cash_tag";
        public static final String CHANGE_LIGHT_COLOUR_TIME = "change_light_colour_time";//场地灯控距离结束时间变化时间
        public static final String SIMPLE_TICKET_CONTINUE_TIME_TAG = "simple_ticket_continue_time_tag"; //是否展示续时
        public static final String GOODS_SALE_REMOTE_PRINT_TAG = "goods_sale_remote_print_tag"; // 商品销售网络打印
        public static final String CHECK_PRODUCT_ENTER_TIMES_BY_TIME = "check_product_enter_times_by_time"; // 是否校验产品的按时段入馆次数
        public static final String EXCLUSIVE_COUPON_CAMP = "exclusive_coupon_camp"; // 互斥优惠券活动
        public static final String CHECK_BLACK_PHONE = "check_black_phone"; // 黑名单校验 0:不校验 1:提示 2:禁止
        public static final String CHECK_BLACK_TAG = "check_black_tag"; // 领取分享票黑名单校验标识 1:校验
        public static final String CHECK_SIMPLE_TICKET_BLACK_PHONE = "check_simple_ticket_black_phone"; // 黑名单校验 1:提示 2:禁止 其他不校验
        public static final String TICKET_CHANNEL_LIMIT = "ticket_channel_limit"; // 黑名单校验 1:提示 2:禁止 其他不校验
        public static final String ATM_COUPON_SKIP_FACE_TAG = "atm_coupon_skip_face_tag"; // 自助终端领券是否跳过人脸识别标志 1-跳过，其他-不跳过
        //微信订场是否需要人机验证标志，1-需要，其他-不需要
        public static final String WECHAT_COURT_HUMAN_VERIFY_TAG = "wechat_court_human_verify_tag";
        //是否需要二次校验xports-captcha标志，1-需要，其他-不需要
        public static final String NEW_CAPTCHA_CHECK = "new_captcha_check";
        //网站登录是否需要人机验证标志，1-需要，其他-不需要
        public static final String WEBSITE_LOGIN_HUMAN_VERIFY_TAG = "website_login_human_verify_tag";
        //微信端订场提交界面退票规则协议弹出标志，1-弹出，其他-不弹出
        public static final String WECHAT_FIELD_POPUP_RULE_TAG = "wechat_field_popup_rule_tag";
        public static final String COUNT_REAL_FROZEN_DAYS = "count_real_frozen_days"; //冻卡天数已实际冻卡天数为准
        public static final String COUPON_EXPORT_PDF_TAG = "coupon_export_pdf_tag"; //优惠券配置导出pdf文件标志， 1-导出，其他-不导出
        public static final String DEPOSIT_GRANT_PAY_MODE_TAG = "deposit_grant_pay_mode_tag"; //是否区分赠款
        public static final String ADD_EXTRA_ATTACHMENT = "add_extra_attachment"; //增加老年证等附件
        public static final String ADD_CHANG_SHU_EXTRA_ATTACHMENT = "add_chang_shu_extra_attachment"; //增加常熟附件
        public static final String GUO_XIN_CA_ADD_EXTRA_ATTACHMENT_NUM = "guo_xin_ca_add_extra_attachment_num"; //国信ca办卡附件数量
        public static final String FIELD_CHECK_ECARD_TRADE = "field_check_ecard_trade"; //订场校验一卡通订单
        public static final String DOWNPAYMENT_NEED_AGREEMENT = "downpayment_need_agreement"; //订金需要协议
        public static final String PRIVATE_COURSE_RESV_DEPOSIT_TAG = "private_course_resv_deposit_tag"; //私教预约是否需要有效账本，0-不需要，其他-需要
        public static final String ATM_PRINT_MINI_APP = "atm_print_mini_app"; //自助终端购票扫描二维码的小程序
        public static final String NO_ADVANCE_OCCUPY = "no_advance_occupy"; // 锁场默认展示，配参数不展示
        public static final String ATM_SCAN_CODE_FETCH_PARK_TAG = "atm_scan_code_fetch_park_tag"; // 自助终端扫码领停车券标志，1-可以，其他-不可以
        public static final String ATM_DUSHUHU_PAY_TAG = "atm_dushuhu_pay_tag"; // 自助终端扫码支付是否使用独墅湖支付，1-是，其他-否
        public static final String TIME_CARD_ACTIVE_CHECK_COMPLETED = "time_card_active_check_completed"; // 激活次卡时，是否需要校验资料完整性
        public static final String WECHAT_FIELD_OCCUPY_LIMIT = "wechat_field_occupy_limit"; // 微信订场占场提交次数限制
        public static final String ONLINE_UNPAY_FIELD_ORDER_LIMIT_NUM = "online_unpay_field_order_limit_num"; // 线上未支付订场订单限制数量，若达到该数量的未支付订单，则不可继续提交订场
        public static final String WECHAT_VENUE_SHOW_PROM_TAG = "wechat_venue_show_prom_tag"; // 微信场馆详情页面展示优惠功能标识
        //微信和小程序订单过期前30秒之内不允许支付
        public static final String WECHAT_TRADE_EXPIRETIME = "wechat_trade_expiretime";
        // 入馆票补打打印已验票
        public static final String PRINT_CHECKED_TICKET = "print_checked_ticket";
        // 私教报名校验账本
        public static final String PRIVATE_SIGN_DEPOSIT_LIMIT_TAG = "private_sign_deposit_limit_tag";
        // 场地预占时长
        public static final String FIELD_TRADE_VALID_PERIOD = "field_trade_valid_period";
        //独墅湖服务地址
        public static final String DUSHUHU_SERVER_ADDRESS = "dushuhu_server_address";
        //中心登陆默认参数
        public static final String LOGIN_SETTING_PARAM = "login_setting_param";
        public static final String TICKET_TIMEOUT_REMIND_TIME = "ticket_timeout_remind_time"; //票超时提醒，提前通知的时间，单位分钟
        //场地状态大屏配置
        public static final String FIELD_SCREEN_CONFIG = "field_screen_config";
        public static final String POS_CHECK_TICKET_PRINT_TAG = "pos_check_ticket_print_tag"; // pos机验票时是否打印小票
        public static final String GAME_TEAM_RULE = "game_team_rule"; //球队创建加入规则：{"createNum":个人可创建球队数,"joinNum",个人可加入球队数}
        public static final String GAME_RULE = "game_rule"; //约赛功能约赛规则 {"occupyTag":"创建约赛占场标志，1-占场，0-不占场", "cancelTag", "占场情况下，双方均支付后，一方取消是否取消另一方标志，1-取消，0-不取消", "margin":"不占场情况下保证金金额，单位分"}
        public static final String GAME_VENUES = "game_venues"; //约赛场馆
        public static final String GAME_RESERVE_TIME = "game_reserve_time"; //场馆下约赛在开场前提前取消的时间，单位小时
        public static final String ENTERHALL_NOT_CANCEL_FREEZE = "enterhall_not_cancel_freeze"; // 入馆不解冻
        public static final String PLAY_PROJECT_ADD_POINTS = "play_project_add_points";// 票签到娱乐项目是否产生积分
        public static final String COURSE_SCHEDULE_DAYS = "course_schedule_days";//小程序课表安排天数
        public static final String FREEZE_ACTIVATE_CARD = "freeze_activate_card"; //冻卡时激活卡
        public static final String CARD_OPERATE_BY_USEVENUE = "card_operate_by_usevenue"; //卡操作根据可使用场馆筛选
        public static final String CALORIE_MANAGE_DISPLAY_TAG = "calorie_manage_display_tag";//娱乐项目管理，卡路里管理tab页展示标志
        public static final String CUST_FACE_TYPE = "cust_face_type";// 人脸处理类型
        public static final String SHOW_CUST_FACE = "show_cust_face";// 是否展示录入人脸
        public static final String DELAY_PRIVATE_COURSE_NEED_LIMIT_TYPES = "delay_private_course_need_limit_types";// 私教延期支持有哪些有效的卡种
        public static final String CHECK_TICKET_OVER_TIME = "check_ticket_over_time";// 押金退款界面是否校验票已超时
        public static final String RELEASE_CABINET = "release_cabinet"; //出馆释放柜子
        public static final String COURSE_SCHEDULE_NEED_PLAN = "course_schedule_need_plan"; //排课是否设置教案
        public static final String OCCUPY_NEED_CHECK_TICKET = "occupy_need_check_ticket";// 是否需要验票才显示场地被占用
        public static final String CHECK_ENTER_INFO_BY_ENTER_STATE = "check_enter_info_by_enter_state";// 校验入馆信息是否校验入闸状态
        public static final String CHECK_ENTER_INFO_BY_ENTER_METHOD = "check_enter_info_by_enter_method";// 校验入馆信息是否校验入闸方式
        public static final String CARD_REFUND_WITHOUT_APPROVE = "card_refund_without_approve"; //退卡不需要审核
        public static final String CENTER_SITE_MAKE_UP_CARD_VENUE_TAG = "center_site_make_up_card_venue_tag";//1-填写办卡场馆 0-不是 默认为0
        public static final String CREDIT_ENTER_HALL = "credit_enter_hall"; // 信用入馆（即过期产品、培训、私教也可以入馆）
        public static final String HIDE_SENSITIVE_MSG = "hide_sensitive_msg"; //是否隐藏敏感信息
        public static final String ECARD_RECHARGE_CONTINUE_VALID_TIME_TAG = "ecard_recharge_continue_valid_time_tag";// 一卡通充值是否顺延有效期 0-不顺延，1-顺延
        public static final String MODIFY_CONSULTANT_CHOOSE_STARTDATE = "modify_consultant_choose_startdate"; //批量变更会籍顾问可以选择开始时间
        public static final String VENUE_MANAGE_ADD_TAG = "venue_manage_add_tag";//场馆管理是否可以新增场馆 1-可以，0-不可以，默认不可以
        public static final String ENTER_HALL_RESUME_TC_LEAVE_TAG = "enter_hall_resume_tc_leave_tag"; // 入馆是否自动销假
        public static final String GOODS_DELIVERY_CHECK_STOCK = "goods_delivery_check_stock"; //出库校验库存余量
        public static final String FIRST_LOGIN_CHANGE_PASSWORD_TAG = "first_login_change_password_tag"; //首次登录是否修改密码：1-是，0-否，默认否
        public static final String CHAIN_DEFAULT_ROLES = "chain_default_roles"; // 连锁经营默认角色
        public static final String CENTER_THIRDPAY_ACCOUNT_TRADE_TYPES = "center_thirdpay_account_trade_types"; //第三方支付方式支付时使用中心账户的业务类型，多个业务类型编码用逗号间隔
        public static final String FIXED_OCCUPY_CHECKIN_TAG = "fixed_occupy_checkin_tag"; // 活动占场二次确认
        public static final String RESOURCE_CONSUME_NOTICE = "resource_consume_notice";  // 资源消费通知
        public static final String DEPOSIT_CONSUME_NOTICE = "deposit_consume_notice"; // 专项卡消费通知第三方
        public static final String DEPOSIT_PAY_USE_GRANT_FIRST = "deposit_pay_use_grant_first"; //优先使用赠款标识
        public static final String PALM_PASS_TAG = "palm_pass_tag"; // 中心是否使用掌静脉
        public static final String ATM_REGISTER_TAG = "atm_register_tag"; // 自助终端购票注册会员标志
        public static final String ONECM_CHECK_IN_TOKEN = "onecm_check_in_token"; // 一厘米入馆的场馆授权token
        public static final String SUIETONG_PRIVATE_KEY = "suietong_private_key"; // 青岛国信隧E通请求私钥
        public static final String REFUND_NEED_CONFIRM_TRADETYPES = "refund_need_confirm_tradetypes"; //退款需要二次确认的交易类型
        public static final String POS_ENTER_HALL_RULE = "pos_enter_hall_rule"; // pos机入馆规则
        public static final String COACH_CLASS_NOTICE_ADVANCE_MINUTES = "coach_class_notice_advance_minutes";//中心参数，教练员上课通知提前分钟数
        public static final String MULTIPLE_LOGIN_CHECK = "multiple_login_check"; // 一个账号多设备登录校验
        public static final String VENUE_FIELD_LIGHT_BUFFER_TIME = "venue_field_light_buffer_time"; // 场地灯控延迟关灯时间
        public static final String LOCKER_OUTER_VENUE_ID = "locker_outer_venue_id";
        public static final String LOCKER_OPEN_TIMES = "locker_open_times"; // 更衣柜可开柜次数，默认为不限制
        public static final String WECHAT_ECARD_RECHARGE_UNIT = "wechat_ecard_recharge_unit"; // 充值金额限定
        public static final String GUEST_FACE_SET_NAME = "guest_face_set_name"; // 散客人脸服务点名称
        public static final String COURSE_USING_SUBJECT_TAG = "course_using_subject_tag"; // 课程是否按照科目来设置价格和签到
        public static final String GUEST_FACE_KEEPING_DAYS = "guest_face_keeping_days"; // 绑定手机号的散客人脸保存多少天
        public static final String COURSE_SPECIAL_LEAVE_TAG = "course_special_leave_tag"; // 请假需要特殊处理
        public static final String POINTS_TAG = "points_tag"; // 是否使用积分
        public static final String LOCKER_SECRET_KEY = "locker_secret_key"; // 常州蓝沙智能科技柜子加密key
        // 小程序登陆密钥参数
        public static final String NET_USER_LOGIN_AK = "net_user_login_ak";
        // 小程序登陆错误次数配置参数
        public static final String NET_USER_LOGIN_SETTING = "net_user_login_setting";
        // 小程序登陆外部校验域名
        public static final String NET_USER_LOGIN_BASE_URL = "net_user_login_base_url";
        // 是否设置票使用的教室
        public static final String TICKET_TYPE_ROOM_TAG = "ticket_type_room_tag";
        public static final String FIELD_TICKET_SALE_SITES_TAG = "field_ticket_sale_sites_tag"; // 场地票是否根据服务点销售
        public static final String USE_NEW_PAY = "use_new_pay";//使用新支付页面
        public static final String FIELD_TICKET_SHARE_TAG = "field_ticket_share_tag";// 场地票是否可分享
        public static final String DEPOSIT_SHARE_TAG = "deposit_share_tag";// 次卡是否可分享
        public static final String DOOR_CONTROL_BASE_URL = "door_control_base_url";// 好家庭门禁地址
        public static final String OPEN_DOOR_TAG = "open_door_tag";// 是否支持好家庭门禁开门，1-支持，其他-不支持
        // 嗨马防滑袜商品类型配置
        public static final String TICKET_RELATED_GOODSTYPE = "ticket_related_goodstype";
        // 嗨马shopId配置
        public static final String SHOP_ID = "shop_id";
        public static final String CATERING_SHOP_ID = "catering_shop_id";//餐饮购物
        public static final String FOREGIFT_PAY_TAG = "foregift_pay_tag"; // 是否可以使用押金支付
        public static final String OA_WIDGET_PRIVATE_CLASS_TAG = "oa_widget_private_class_tag"; // 是否显示悦动办公私教课时widget
        public static final String MAX_COMMON_PERSON_NUM = "max_common_person_num";//最大常用信息人数量
        public static final String PRINT_MINI_APP = "print_mini_app"; //纸质票打印小程序二维码
        public static final String PRINT_WECHAT = "print_wechat"; //纸质票打印公众号二维码-中心参数
        public static final String CARD_CASH_MONEY = "card_cash_money"; //场馆次卡、期间卡支持入馆绑手环以及收取手环押金
        public static final String USE_ONLINE_COUPON = "use_online_coupon"; //场馆次票销售是否需要核销线上券
        public static final String SCHOOL_STUDENT_ITEM = "school_student_item"; //学校课学员展示属性
        public static final String SCHOOL_STUDENT_ITEM_NEW = "school_student_item_new"; //学校课学员展示属性
        public static final String STAT_INCOME_PARAM = "stat_income_param"; //收入统计参数
        public static final String WECHAT_VENUE_LIST_PAGE = "wechat_venue_list_page"; // 场馆列表页
        public static final String THIRDPAY_ORDER_DESC_SHOW_CENTER_TAG = "thirdpay_order_desc_show_center_tag"; // 第三方支付订单描述是否展示中心名，1-展示
        public static final String PRINT_MINI_APP_QRCODE_SIZE = "print_mini_app_qrcode_size"; // 打印票上小程序码的大小
        public static final String ATM_CASH_DEPOSIT_USE_TAG = "atm_cash_deposit_use_tag"; // 自助机能否使用余额专项卡支付标志，1-可以
        public static final String WEBSITE_USER_PSPT_TYPE = "website_user_pspt_type"; // 网页证件类型筛选列表，值为static_param_lang表pspt_type对应值，多个逗号分隔
        public static final String THIRD_PAY_TYPE = "third_pay_type"; // 第三方支付类型，"allinpay"代表通联，不配是原来的微信/支付宝扫码支付
        public static final String ALLINPAY_MCH_INFO = "allinpay_mch_info"; // 通联支付商户信息
        public static final String CCBPAY_MCH_INFO = "ccbpay_mch_info"; // 建设支付商户信息
        public static final String CCBPAY_LIFE_MCH_INFO = "ccbpay_life_mch_info"; // 建设生活支付商户信息 新版
        public static final String CCBPAY_PLATFORM_CLIENT = "ccbpay_platform_client"; // 建设商户外联平台
        public static final String ACTIVITY_FILL_ELEMENT_CONFIG = "activity_fill_element_config";//活动填写配置
        public static final String HEALTH_AGREEMENT_TYPE_BY_AGE = "health_agreement_type_by_age"; //健康档案类型根据不同年龄段配置
        public static final String HEALTH_AGREEMENT_MIN_AGE = "health_agreement_min_age"; // 签署健康承诺书的最小年龄
        public static final String HEALTH_AGREEMENT_SERVICE = "health_agreement_service";  //  签署健康档案的运动项目
        public static final String HEALTH_AGREEMENT_AUDIT_AGE = "health_agreement_audit_age";  //  签署健康档案需要审核的年龄
        public static final String CUST_FACE_REMIND_BY_TRADE_TYPES = "cust_face_remind_by_trade_types";//支持人脸入馆的业务类型
        public static final String PAY_LINK_INFO = "pay_link_info";//发送支付链接信息
        public static final String SHORT_URL_ADDRESS = "short_url_address";//短链接地址
        public static final String COURSE_REFUND_BY_REAL_PAY = "course_refund_by_real_pay"; //课程根据实付分摊退款
        public static final String HEALTH_COMMITMENT_CARD_NOTE = "health_commitment_card_note"; //健康承诺卡注意事项
        public static final String TRAINING_UPLOAD_FACE = "training_upload_face"; //培训私教是否要上传人脸
        public static final String SPECIAL_CARD_PAY_GRANT_POINTS = "special_card_pay_grant_points"; //专项卡支付赠送积分
        public static final String HOMEPAGE_FRIENDSHIP_TIPS = "homepage_friendship_tips"; //小程序首页友情提示
        public static final String COMPLAINT_ORDER_TYPE_ID = "complaint_order_type_id"; // 投诉单类型ID
        public static final String WORK_ORDER_REMIND_HOUR = "work_order_remind_hour"; // 工单在受理后多长小时可以催办
        public static final String EXIT_VENUE_SHOW_PERSON_NUM = "exit_venue_show_person_num"; //出馆结算展示计时票在馆人数
        public static final String RENT_GOODS_FORGIFT_SYSTEM = "rent_goods_forgift_system"; // 系统收取器材租赁押金
        public static final String LESSON_SIGN_MINUTE_VALUE = "lesson_sign_minute_value"; // 课程签到时间的正负值（分钟）
        public static final String LESSON_SIGN_CIRCUMFERENCE = "lesson_sign_circumference"; //课程签到范围方圆半径（米）
        public static final String AGREEMENT_DEFAULT_CHECK_TAG = "agreement_default_check_tag"; //协议是否默认勾选
        public static final String AGREEMENT_DEFAULT_NOT_CHECK_TAG = "agreement_default_not_check_tag"; //微信公众号办卡，购票协议是否默认不勾选
        public static final String POST_PAY_ROUNDING_TAG = "post_pay_rounding_tag"; //后付费票价格是否四舍五入到元
        public static final String POINTS_REFUND_CLEAR_SHOW = "points_refund_clear_show"; //是否显示“一卡通退卡将积分账户置于失效”的开关
        public static final String HEALTH_AGREEMENT_NOTICE_DAYS = "health_agreement_notice_days"; //健康承诺书过期前多少天通知用户
        public static final String HEALTH_AGREEMENT_NOTICE_AGE = "health_agreement_notice_age"; //健康承诺书过期前需要通知紧急联系人的用户年龄配置
        public static final String HEALTH_AGREEMENT_FACE_SET_ID = "health_agreement_face_set_id"; //健康档案对应的人脸服务点id
        public static final String COMMON_PERSON_NEED_PSPT_TAG = "common_person_need_pspt_tag"; //常用信息人录入是否需要填身份证
        public static final String HAND_HELD_GET_NUMBER_TAG = "hand_held_get_number_tag"; //手持机是否可取号标识
        public static final String ENTER_HALL_NOT_BUY_TICKET = "enter_hall_not_buy_ticket"; //入馆不可以买票
        public static final String HIKVISION_INFO = "hikvision_info"; //海康信息
        public static final String LIMIT_TIMES_TICKET_RETURN_HOUR = "limit_times_ticket_return_hour"; //次票开始时间多少小时不可退票
        public static final String PLATFORM_SERVER_INFO = "platform_server_info";//健身平台服务请求信息
        public static final String COMPETITION_SERVER_INFO = "competition_server_info";//微服务competition服务请求信息
        public static final String MAKE_CARD_FEEITEM = "make_card_feeitem"; //制卡费项目
        public static final String SHOW_PRIVATE_COURSE_TAG = "show_private_course_tag"; //我的课包是否展示私教
        public static final String LIGHT_TYPE = "light_type"; //灯控类型
        public static final String LIGHT_LIMIT_TIME = "light_limit_time"; //灯控限制时间
        public static final String UNUSED_TICKET_REFUND_CASHPLEDGE = "unused_ticket_refund_cashpledge"; //未使用票可以退押金
        public static final String BRACELET_GATE_POSITION = "bracelet_gate_position"; //手环机位置
        public static final String SPECIAL_CARD_AUTO_BIND_CARD = "special_card_auto_bind_card"; //专项卡售卖无卡是否自动绑定电子卡
        public static final String CHANGE_KEY_CASH_MONEY = "change_key_cash_money"; //换钥匙交的押金
        public static final String STUDENT_ATTR_TAG = "student_attr_tag"; //学员扩展信息
        public static final String TC_TIME_CHANNELS = "tc_time_channels"; //上课时间渠道
        public static final String KEY_FOREGIFT = "key_foregift"; //取手环押金
        public static final String RESERVATION_ACTIVITY_ID = "reservation_activity_id"; //预约活动id
        public static final String ACCUMUL_CMAP = "accumul_camp"; //蓄力活动id
        public static final String ROOM_OCCUPY_NEED_DETAIL = "room_occupy_need_detail"; //协议包场需要补全资料
        public static final String DEFAULT_COMMENT_TIME = "default_comment_time"; //默认评价时间
        public static final String OCM_FETCH_KEY_RULE = "ocm_fetch_key_rule"; //手环机取手环规则
        public static final String PRIVATE_COURSE_LIMIT_NEW_STUDENT_TAG = "private_course_limit_new_student_tag"; //私教课预约是否限制新学员
        public static final String WECHAT_PRIVATE_SELECT_COACH_TAG = "wechat_private_select_coach_tag"; //微信私教课是否需要选教练标识
        public static final String SPECIAL_CARD_SHOW_DEVELOPER_TAG = "special_card_show_developer_tag";// 办理专项卡是否展示发展人选项
        public static final String SPECIAL_CARD_SHOW_CONSULTANT_TAG = "special_card_show_consultant_tag";// 办理专项卡是否展示会籍经理选项
        public static final String COULD_MANAGE_CAMP_SCORE_TAG = "could_manage_camp_score_tag"; //是否可以导入成绩
        public static final String PRIVATE_CLASS_FEE_MODE = "private_class_fee_mode"; // 私教课时费模式 1-按照私教课配置的课时费计算 2-按照教练级别计算
        public static final String RECORD_KEY_RETURN_TAG = "record_key_return_tag"; //场馆是否要记录钥匙归还状态，针对手环机的情况 1-要记录
        public static final String CHECK_TC_LESSON_RESV_TAG = "check_tc_lesson_resv_tag"; //签到是否校验预约标识
        public static final String GROUP_LESSON_SCHEDULE_DAYS = "group_lesson_schedule_days";//小程序团课课表安排天数
        public static final String PERSONAL_CARD_NEED_CONTRACT_TAG = "personal_card_need_contract_tag"; // 个人办卡填写合同编号标识 1-填写 0-不填写
        public static final String ATM_COURSE_AHEAD_TIME = "atm_course_ahead_time"; //自助机课程可提前入场时间（分钟）
        public static final String USE_XPORTS_FACE_TAG = "use_xports_face_tag"; //使用虹软人脸标识
        public static final String FACE_CAN_FETCH_KEY = "face_can_fetch_key";//场馆是否支持人脸取手环 1-支持
        public static final String SPECIAL_CARD_NOTICE_RULE = "special_card_notice_rule";//专项卡即将到期通知规则 {"period_card_notice_day":5,"time_card_notice_time":5}
        public static final String SETTLE_HANGWALL_EXIT_VENUE = "settle_hangwall_exit_venue"; //浴资结算是否出馆
        public static final String FIELD_CHANNEL_NEW_STYLE_TAG = "field_channel_new_style_tag"; // 场地售票渠道展示使用新样式标识
        public static final String RECORD_CAN_ENTRY_SERVICE_IDS_TAG = "record_can_entry_service_ids_tag"; //是否记录能够入馆的服务类型 1-记录
        public static final String COACH_COURSE_TAG = "coach_course_tag";//培训课程
        public static final String TERM_CLASS_COACH_TAG = "term_class_coach_tag";//新增班级增加任课教练选择
        public static final String ARC_SOFT_GROUP_ID = "arc_soft_group_id"; //虹软人脸groupId
        public static final String DA_HUA_FACE_SET_ID = "arc_soft_group_id"; //大华人脸faceSetId
        public static final String CHOOSE_COACH_MAX_NUM = "choose_coach_max_num"; //教练最多可以选几个
        public static final String RESERVATION_ACTIVITY_NEED_REGISTER = "reservation_activity_need_register"; //预约活动需要登记个人信息
        public static final String EPIDEMIC_FACE_SET_NAMES = "epidemic_face_set_names"; //防疫登记上传到哪些人脸服务组
        public static final String SHOP_GRAVITY_FACTOR = "shop_gravity_factor"; //商品展示的重力因子
        public static final String XPORTS_SEARCH_SERVER = "xports_search_server"; //搜索服务项目
        public static final String ONLY_KEY_CAN_OPEN_CABINET = "only_key_can_open_cabinet"; //只有手牌可以开柜
        public static final String SHOW_LOWEST_PRICE = "show_lowest_price"; //展示场馆各种业务的最小价格
        // 一卡通退款时，银行卡信息中“开户银行、银行卡号”填写规则，1-必填，0-非必填
        public static final String INPUT_BANK_INFO = "input_bank_info";
        public static final String IS_PINGYANG_CENTER = "is_pingyang_center"; //是否是平阳全民健身中心
        public static final String IS_QINGDAO_CENTER = "is_qingdao_center"; //是否是青岛海运中心
        public static final String PING_YANG_APP_ID = "ping_yang_app_id"; //平阳全民健身中心appId
        public static final String PING_YANG_SYNCDATA_URL = "ping_yang_syncdata_url"; //平阳全民健身中心同步数据url
        // 平阳全民健身中心-第三方支付手续费费率
        public static final String PING_YANG_THIRD_PAY_RATE = "ping_yang_third_pay_rate";
        // 平阳全民健身中心同步订单url
        public static final String PING_YANG_SYNC_ORDER_URL = "ping_yang_sync_order_url";
        // 平阳全民健身中心订单支付url
        public static final String PING_YANG_PAY_ORDER_URL = "ping_yang_pay_order_url";
        public static final String PING_YANG_PARKPUBLICKEY = "ping_yang_parkpublickey"; //平阳全民健身中心parkPublicKey
        public static final String PING_YANG_APPPRIVATEKEY = "ping_yang_appprivatekey"; //平阳全民健身中心appPrivateKey
        public static final String CAMP_INPUT_ENROLL_DATE_TAG = "camp_input_enroll_date_tag"; //手动添加赛事活动报名时间
        public static final String ALI_CONTENT_CHECK_KEY = "ali_content_check_key"; //阿里内容检测key信息
        public static final String RESERVATION_ACTIVITY_AHEAD_MINUTES = "reservation_activity_ahead_minutes"; //预约活动提前截止时间
        public static final String SCHEDULE_ENTER_HALL_SERVICES = "schedule_enter_hall_services"; //定时任务入馆的项目
        // 是否校验澡资，0为不校验，1为校验
        public static final String CHECK_WASH_FEE = "check_wash_fee";
        // 银联商务支付服务器的根地址
        public static final String UNION_PAY_ROOT_URL = "union_pay_root_url";
        // 银联商务支付-B扫C订单支付地址
        public static final String UNION_PAY_PAYMENT = "union_pay_payment";
        // 银联商务支付-B扫C订单撤销支付地址
        public static final String UNION_PAY_VOID_PAYMENT = "union_pay_void_payment";
        // 银联商务支付-B扫C订单退款地址
        public static final String UNION_PAY_REFUND = "union_pay_refund";
        // 银联商务支付-B扫C订单支付状态查询地址
        public static final String UNION_PAY_QUERY = "union_pay_query";
        // 银联商务支付-B扫C订单退款状态查询地址
        public static final String UNION_PAY_QUERY_REFUND = "union_pay_query_refund";
        // 银联商务支付-微信小程序下单地址
        public static final String UNION_PAY_MINI_APP_ORDER = "union_pay_mini_wechat_order";
        // 银联商务支付-微信公众号前台下单地址
        public static final String UNION_PAY_WEB_PAY_ORDER = "union_pay_web_pay_order";
        // 银联商务支付-微信公众号订单支付结果查询地址
        public static final String UNION_PAY_MINI_APP_QUERY = "union_pay_mini_wechat_query";
        // 银联商务支付-appId
        public static final String UNION_PAY_APP_ID = "union_pay_app_id";
        // 银联商务支付-appKey
        public static final String UNION_PAY_APP_KEY = "union_pay_app_key";
        // 银联商务支付-商户号
        public static final String UNION_PAY_MCH_CODE = "union_pay_mch_code";
        // 银联商务支付-终端号
        public static final String TERMINAL_CODE = "terminal_code";
        // 银联商务支付-微信小程序/微信公众号-后台下单-订单退款地址
        public static final String UNION_PAY_MINI_REFUND = "union_pay_mini_refund";
        // 银联商务支付-微信小程序/微信公众号-前台下单-订单退款地址
        public static final String UNION_PAY_WEB_PAY_REFUND = "union_pay_web_pay_refund";
        // 银联商务支付-ACCESS_TOKEN
        public static final String UNION_PAY_ACCESS_TOKEN = "union_pay_access_token";
        // 银联商务支付-通讯密钥
        public static final String UNION_PAY_SECRET_KEY = "union_pay_secret_key";
        // 银联商务支付-C扫B-生成支付二维码的请求地址
        public static final String UNION_PAY_QRCODE_URL = "union_pay_qrcode_url";
        // 银联商务支付-订单号标识符
        public static final String ORDER_IDENTIFY_ID = "order_identify_id";
        // 银联商务支付-异步通知地址
        public static final String UNION_PAY_NOTIFY_URL = "union_pay_notify_url";
        // 银联商务支付-经度
        public static final String LONGITUDE = "longitude";
        // 银联商务支付-纬度
        public static final String LATITUDE = "latitude";
        // 根据场次计算入馆时间标识
        public static final String ENTER_HALL_BY_TIME_TAG = "enter_hall_by_time_tag";
        // 西浦更衣室的票按入馆时间计算
        public static final String ENTER_HALL_SERVICE_TAG = "enter_hall_service_tag";
        // 获取支付列表是否来自查询服务点设置的支付方式，0为不是，1为是
        public static final String GET_PAY_LIST_FROM_SITE = "get_pay_list_from_site";
        public static final String ATM_PRINT_TEMPLATE = "atm_print_template"; //自助机打印配置
        public static final String ATM_FACE_TAG = "atm_face_tag";//自助机是否支持人脸，默认支持
        public static final String CONTRACT_TASK_FORM_ELEMENT = "contract_task_form_element"; //通知保障页面元素配置
        public static final String CONTRACT_TASK_FORM_ELEMENT_DEPARTMENT = "contract_task_form_element_department"; //默认生成的部门元素
        public static final String PROPERTY_RENT_FORM_ELEMENT = "property_rent_form_element"; //物业租赁-物品状态配置
        public static final String ENTERHALL_AHEAD_TAG = "enterHall_ahead_tag"; // 提前入馆配置
        public static final String NOT_CHECK_AUTOGATE_SERVICETIME = "not_check_autogate_servicetime"; // 不校验闸机的营业时间
        public static final String SIGN_TICKET_NOT_GET_KEY_TAG = "sign_ticket_not_get_key_tag"; // 签到票不能取手环
        public static final String GET_VENUE_COUPON_TAG = "get_venue_coupon_tag"; // 查询场馆券标识
        public static final String TICKET_PRINT_TIMES_LIMIT = "ticket_print_times_limit"; //票最大打印次数
        public static final String GET_ORDER_IGNORE_CHANNEL_ID = "get_order_ignore_channel_id"; //查询订单忽略channel_id
        public static final String VENUE_CASH_PRODUCT_BUY_TICKETS_DAY = "venue_cash_product_buy_tickets_day"; //专项卡-储值卡每日可购买的票的数量
        public static final String MONTH_UNPAY_FIELD_ORDER_LIMIT_NUM = "month_unpay_field_order_limit_num"; //当订场时校验是否当月有多少天因订场未支付被限制不能订场，若已达配置天数则限制当月内不能继续订场
        public static final String EDIT_TICKET_TYPE_TIME_TAG = "edit_ticket_type_time_tag"; //次票编辑-场次编辑标识
        public static final String TICKET_TYPE_TIMEOUT_CEILING_TAG = "ticket_type_timeout_ceiling_tag"; //次票编辑-增加封顶价格标识
        public static final String TIMEOUT_CHARGING_UNIT_TAG = "timeout_charging_unit_tag"; //次票编辑-场次阶梯超时设置增加计费单位标识
        public static final String SHOW_VALID_PRIVATE_COURSE = "show_valid_private_course"; //是否只展示有效的私教课
        public static final String SENSETIME_FACE_CONFIGURE = "sensetime_face_configure"; //商汤人脸配置
        // 判断是否是五台山健身会馆
        public static final String WU_TAI_SHAN_CENTER = "wu_tai_shan_center";
        // 计时票配置，开启闸机验票及计算入馆时长
        public static final String CREDENTIALS_CHECK_BY_GATE = "credentials_check_by_gate";
        // 计时票配置，使用前台打开出馆结算页面的时间点作为计时票时长的截止时间
        public static final String END_TIME_CHECK_BY_WEB = "end_time_check_by_web";
        public static final String FACE_TAKE_ALL_DEPOSIT = "face_take_all_deposit"; //手环机一张脸查所有专项卡 条件 1-身份证，2-手机号，3-身份证或者手机号
        // 判断入馆票中是否需要获取课程码
        public static final String QUERY_COURSE_CODE = "query_course_code";
        public static final String TENCENT_CONVENIENT_SERVICE = "tencent_convenient_service"; //腾讯云便民服务图表
        // 闸机出馆时校验更衣柜柜门是否关闭
        public static final String CHECK_LOCK_STATE = "check_lock_state";
        // 场馆平台首页，开启租柜到期提醒
        public static final String LOCKER_RENT_NOTIFY = "locker_rent_notify";
        public static final String MINI_APP_CREATE_CARD_TAG = "mini_app_create_card_tag"; //小程序展示生成电子卡按钮标识
        // 专项卡的退款方式选择中，控制是否显示“转一卡通充值”
        public static final String RETURN_MONEY_TO_CARD = "return_money_to_card";
        public static final String TICKET_SALE_OPEN_TIME = "ticket_sale_open_time";   //票销售时间
        public static final String MANUAL_REDUCE_TRADE_TYPE_CODE = "manual_reduce_trade_type_code"; //需要减免审核的业务类型
        //水量充值规则
        public static final String RECHARGE_WATER_RULE = "recharge_water_rule";
        public static final String CHECK_COUPON_BY_TRADE_TIME = "check_coupon_by_trade_time"; //西浦可用优惠券有效期限制按下单时间
        // 微信端购买专项卡的人数是否在页面显示 有且为1表示隐藏
        public static final String WECHAT_SPECIAL_CARD_HIDE_BUY_NUM = "wechat_special_card_hide_buy_num";
        // 刷卡通过闸机时优先使用票入馆：先查询卡下绑定的可用票，如果有可用票则将入馆的方式转换为票入馆
        public static final String ACROSS_GATE_PRIORITY_CHOOSE_TICKET = "acrose_gate_priority_choose_ticket";
        public static final String ASSESSMENT_SHOP_ID = "assessment_shop_id"; //咨询-评估门店id
        public static final String PHYSIOTHERAPY_SHOP_ID = "physiotherapy_shop_id"; //咨询-理疗门店id
        public static final String NOT_CHECK_SALE_SHOP_ID = "not_check_sale_shop_id"; //商品挂账时不校验是否存在其他门店挂账记录
        public static final String WECHAT_FIND_ALL_CHANNEL_TAG = "wechat_find_all_channel_tag"; //微信查询全部渠道标识
        public static final String HEALTH_ADVICE_TAG = "health_advice_tag"; //健康咨询标识
        public static final String CHECK_OA_VERIFY_CODE_TAG = "check_oa_verify_code_tag"; //oa开启二次验证标识
        public static final String WECHAT_FIELD_WAIT_NOTICE_FLAG = "wechat_field_wait_notice_flag"; //中心是否支持候场通知
        public static final String CHECK_COURSE_ENROLL_REMAIN_NUM = "check_course_enroll_remain_num"; //校验剩余未上课时
        public static final String GROUP_BUY_CASH_PLEDGE = "group_buy_cash_pledge"; //团购取票是否交押金
        public static final String BRACELET_CHECK_GENDER = "bracelet_check_gender"; //还手环校验性别
        public static final String BRACELET_CHECK_SITE = "bracelet_check_site"; //还手环校验服务点
        public static final String REFRESH_TOKEN_TAG = "refresh_token_tag"; //刷新悦动oa的token标识
        public static final String WECHAT_MSG_SHOW_TAG = "wechat_msg_show_tag"; // 订场提醒配置
        public static final String MINI_GROUP_LESSON_RESV_ADVANCE_DAYS = "mini_group_lesson_resv_advance_days"; //用来控制首页Remington课程提前展示的天数
        public static final String MINI_GROUP_LESSON_RESV_TIME = "mini_group_lesson_resv_time"; //西浦小团课开始预约时间
        // 灯控的配置信息
        public static final String LIGHT_CONFIG_INFO = "light_config_info";
        // 灯控箱继电器命令直发的地址配置信息
        public static final String LIGHT_ORDER_CONFIG = "light_order_config";
        // 灯控配置-'组模式'标识：值为1时，4路电路控制1盏灯，默认值为0
        public static final String LIGHT_HAS_GROUP = "light_has_group";
        public static final String TRAINING_COURSE_ENROLL_START_TIME = "training_course_enroll_start_time"; //西浦课程报名开始时间TrainingCourseInfo.java
        public static final String COURSE_ENTER_AHEAD_TIME = "course_enter_ahead_time"; //西浦课程提前签到时间
        public static final String DEPOSIT_FIXED_TIME_ACTIVE = "deposit_fixed_time_active"; //是否账本需要定时激活
        public static final String ACTIVATE_AGREEMENT = "activate_agreement"; //专项卡激活是否需要协议
        public static final String CARD_INVALID_DATE_SHOW = "card_invalid_date_show"; //会员查询卡信息不可用日期显示
        // 诺诺电子发票-开票公司的税号
        public static final String TEX_NUMBER = "tax_number";
        // 小程序-开发票功能是否关闭 有且为1时表示功能关闭
        public static final String APP_INVOICE_CLOSE_FLAG = "app_invoice_close_flag";
        public static final String INVOICE_DATE = "invoice_date";
        // 小程序-开发票功能可以开具发票的支付方式
        public static final String APP_INVOICE_PAY_MODE = "app_invoice_pay_mode";
        // 诺诺电子发票-接口地址
        public static final String INVOICE_SERVER_URL = "invoice_server_url";
        // 诺诺电子发票-授权后的回调地址
        public static final String INVOICE_AUTHORIZE_NOTIFY = "invoice_authorize_notify";
        // 诺诺电子发票-发票开具成功后的回调地址
        public static final String INVOICE_BILL_NOTIFY = "invoice_bill_notify";

        // 诺诺数电发票回调地址
        public static final String DIGITAL_INVOICE_BILL_NOTIFY = "digital_invoice_bill_notify";
        // 诺诺电子发票-冲红发票开具成功后的回调地址
        public static final String RED_INVOICE_BILL_NOTIFY = "red_invoice_bill_notify";
        // 诺诺电子发票-appKey
        public static final String INVOICE_APP_KEY = "invoice_app_key";
        // 诺诺电子发票-appSecret
        public static final String INVOICE_APP_SECRET = "invoice_app_secret";
        // 诺诺电子发票-TOKEN
        public static final String BILL_ACCESS_TOKEN = "bill_access_token";
        // 诺诺电子发票-TOKEN类型（自用型和第三方的区别，第三方的token需要授权）
        public static final String INVOICE_ACCESS_TOKEN_TYPE = "invoice_access_token_type";
        // 诺诺发票配置-场馆对应的部门信息和开票机号
        public static final String INVOICE_DEPARTMENT_INFO = "invoice_department_info";
        public static final String ATM_WECHAT_SPECIAL_CARD_BUY_DEMO_VIDEO = "atm_wechat_special_card_buy_demo_video"; //五台山ATM机演示微信购买专项卡视频
        public static final String ATM_WECHAT_COURSE_BUY_DEMO_VIDEO = "atm_wechat_course_buy_demo_video"; //五台山ATM机微信培训报名视频
        public static final String WEATHER_API_KEY = "weather_api_key"; //和风天气api
        // 青岛国信统一门户-根URL
        public static final String QDGXJT_OAUTH_ROOT = "qdgxjt_root_url";
        // 青岛国信统一门户-请求用户授权接口
        public static final String QDGXJT_OAUTH_URL = "qdgxjt_oauth_url";
        // 青岛国信统一门户-获取授权Token接口
        public static final String QDGXJT_OAUTH_TOKEN_URL = "qdgxjt_oauth_token_url";
        // 青岛国信统一门户-获取授取用户信息接口
        public static final String QDGXJT_OAUTH_USERINFO_URL = "qdgxjt_oauth_userinfo_url";
        // 青岛国信统一门户-应用标识
        public static final String QDGXJT_OAUTH_CLIENT_ID = "qdgxjt_oauth_client_id";
        // 青岛国信统一门户-应用secret
        public static final String QDGXJT_OAUTH_CLIENT_SECRET = "qdgxjt_oauth_client_secret";
        // 青岛国信统一门户-对称加密密钥
        public static final String QDGXJT_AES_KEY = "qdgxjt_aes_key";
        // 青岛国信-待办事项-根URL
        public static final String QDGXJT_TODO_ROOT = "qdgxjt_todo_url";
        // 青岛国信-待办事项-请求用户授权接口
        public static final String QDGXJT_TODO_OAUTH_URL = "qdgxjt_todo_oauth_url";
        // 青岛国信-待办事项-刷新accessToken
        public static final String QDGXJT_TODO_REFRESH_TOKEN_URL = "qdgxjt_todo_refresh_token_url";
        // 青岛国信-待办事项-待办发送
        public static final String QDGXJT_TODO_PUSH_URL = "qdgxjt_todo_push_url";
        // 青岛国信-待办事项-待办转已办
        public static final String QDGXJT_TODO_UPDATE_URL = "qdgxjt_todo_update_url";
        // 青岛国信-待办事项-appId
        public static final String QDGXJT_TODO_APP_ID = "qdgxjt_todo_app_id";
        // 青岛国信-待办事项-appsecret
        public static final String QDGXJT_TODO_APP_SECRET = "qdgxjt_todo_app_secret";
        // 青岛国信-待办事项-scope
        public static final String QDGXJT_TODO_SCOPE = "qdgxjt_todo_scope";
        // 青岛国信-待办事项-用户所在工作圈
        public static final String QDGXJT_TODO_EID = "qdgxjt_todo_eid";
        // 青岛国信-待办事项-待办在客户端显示的图 URL
        public static final String QDGXJT_TODO_HEAD_IMG = "qdgxjt_todo_head_img";
        // 青岛国信-待办事项-需要推送的交易类型
        public static final String QDGXJT_AUDIT_TRADE_TYPE = "qdgxjt_audit_trade_type";
        // 青岛国信-待办事项-回转回来的地址
        public static final String QDGXJT_AUDIT_REDIRECT_URL = "qdgxjt_audit_redirect_url";
        // 青岛国信-数据同步接口-属性信息-用户信息
        public static final String QDGXJT_SCHEMA_STRUCT_STAFF = "qdgxjt_schema_struct_staff";
        // 青岛国信-数据同步接口-属性信息-权限信息
        public static final String QDGXJT_SCHEMA_STRUCT_ROLE = "qdgxjt_schema_struct_role";
        // 天时同城-异步推送接口
        public static final String QDGXJT_TNCI_PUSH_URL = "qdgxjt_tnci_push_url";
        // 天时同城-分配的apiKey
        public static final String QDGXJT_TNCI_API_KEY = "qdgxjt_tnci_api_key";
        // 天时同城-分配的secretKey
        public static final String QDGXJT_TNCI_SECRET_KEY = "qdgxjt_tnci_secret_key";
        //微信订单详情地址
        public static final String WECHAT_TRADE_DETAIL_URL = "wechat_trade_detail_url";
        // 是否使用新"大家签"服务器
        public static final String USE_NEW_DJQIAN = "use_new_djqian";
        // 新"大家签"服务器的根地址
        public static final String NEW_DA_JIA_QIAN_ROOT_URL = "new_djqian_root_url";
        // 新"大家签"服务器的授权地址
        public static final String NEW_DA_JIA_QIAN_AUTH_URL = "new_djqian_auth_url";
        // "大家签"服务器的根地址
        public static final String DA_JIA_QIAN_ROOT_URL = "djqian_root_url";
        // "大家签"服务器的授权地址
        public static final String DA_JIA_QIAN_AUTH_URL = "djqian_auth_url";
        // "大家签"服务器的表单上传地址
        public static final String DA_JIA_QIAN_UPLOAD_URL = "djqian_upload_url";
        // 新"大家签"服务器的快速签署地址
        public static final String NEW_DA_JIA_QIAN_SIGN_URL = "new_djqian_sign_url";
        // "大家签"服务器的快速签署地址
        public static final String DA_JIA_QIAN_SIGN_URL = "djqian_sign_url";
        // 新"大家签"服务器的获取印章列表地址
        public static final String NEW_DA_JIA_QIAN_SEAL_LIST_URL = "new_djqian_seal_list_url";
        // "大家签"服务器的获取印章列表地址
        public static final String DA_JIA_QIAN_SEAL_LIST_URL = "djqian_seal_list_url";
        // 新"大家签"的appId
        public static final String NEW_DA_JIA_QIAN_APP_ID = "new_djqian_app_id";
        // 新"大家签"的appId
        public static final String NEW_DA_JIA_QIAN_APP_SECRET = "new_djqian_app_secret";
        // "大家签"的appId
        public static final String DA_JIA_QIAN_APP_ID = "djqian_app_id";
        // "大家签"的appId
        public static final String DA_JIA_QIAN_APP_SECRET = "djqian_app_secret";
        // 国信CA标识
        public static final String GUO_XIN_CA_FLAG = "guo_xin_ca_flag";
        // 国信CA标识
        public static final String CHECK_PIECE_TIME = "check_piece_time";
        //是否支持微信端专项卡退卡
        public static final String BACK_CARD_SUPPORT_TAG = "back_card_support_tag";
        // 最后一次出馆时校验手环是否归还
        public static final String LAST_EXIT_CHECK_KEY_RETURN_STATE = "last_exit_check_key_return_state";
        //五台山健康跑，领券是否需要实名认证标志，0-否，1-是（暂时没有相关的内容，添加是保障移植无问题）
        public static final String COUPON_NEED_VERIFY_TAG = "coupon_need_verify_tag";
        //五台山健康跑，实名认证开启人机校验标志，0-否，1-是（暂时不需要开启人机校验标志，添加是保障移植无问题）
        public static final String USER_VERIFY_HUMAN_VERIFY_TAG = "user_verify_human_verify_tag";
        //五台山健康跑，是否需要实名认证才能否报名比赛，0-不需要，1-需要
        public static final String USER_SIGN_UP_COMPETITION_TAG = "user_sign_up_competition_tag";
        // 出闸机时自动用押金扣除超时费
        public static final String EXIT_PAY_TIMEOUT_FEE_WITH_CASHPLEDGE = "exit_pay_timeout_fee_with_cashpledge";
        // 银联商务支付-微信小程序/微信公众号的支付方式-是否使用前台下单(1为使用前台下单方式，0为使用后台下单方式)
        public static final String UNION_PAY_USE_WEB_PAY = "union_pay_use_web_pay";
        // 协议使用电子章（有且为1时表示启动协议电子公章配置）
        public static final String PROTOCOL_USE_ELECTRONIC_SEAL = "protocol_use_electronic_seal";
        // 卡入馆取手环时，是否只取卡下有效入馆记录中的第一条记录（值为1时只取有效记录中的第一条，否则正常查询所有的有效入馆记录）
        public static final String FIRST_RECORD_ONLY = "first_record_only";
        // 汇泉湾微信入馆票排序规则
        public static final String HQW_WECHAT_ENTER_TICKET_ORDER = "hqw_wechat_enter_ticket_order";
        // 汇泉湾-票码链接
        public static final String QRCODE_LINK = "qrCode_link";
        // 超时费享受次票购买时的专项卡折扣
        public static final String OVERTIME_FEE_ENJOY_TICKET_DISCOUNT = "overtime_fee_enjoy_ticket_discount";
        // 微信公众号支持修改一卡通和储值卡密码
        public static final String WX_SUPPORT_UPDATE_CARD_PASSWORD = "wx_support_update_card_password";
        //微信领取优惠券是否直接领取 1-直接领取
        public static final String DIRECT_COLLECT_COUPON_TAG = "direct_collect_coupon_tag";
        // 无权限登录的角色（西浦）
        public static final String XJTLU_LOGIN_NO_PERMISSION_ROLES = "xjtlu_login_no_permission_roles";
        //宁波奥体停车系统API参数
        public static final String NBAT_CAR_API_SETTING = "nbat_car_api_setting";
        //使用app_id开关
        public static final String USE_PLATFORM_APP_ID_TAG = "use_platform_app_id_tag";
        //学员信息编辑使用权限控制
        public static final String DIVING_STUDENT_CAN_EDIT = "diving_student_can_edit";
        // 平阳中心-园秀对接-单点登录url
        public static final String PARK_SHOW_URL = "park_show_url";
        // 平阳中心-园秀对接-单点登录-key
        public static final String PARK_SHOW_LOGIN_KEY = "park_show_login_key";
        // 平阳中心-园秀对接-单点登录-重定向地址
        public static final String PARK_SHOW_LOGIN_REDIRECT_URL = "park_show_login_redirect_url";
        // 平阳中心-园秀通支付-限制购买
        public static final String YUAN_XIU_PAY_BAN_BUY_CARD = "yuan_xiu_pay_ban_buy_card";
        // 出馆结算不使用专项卡折扣
        public static final String EXIT_VENUE_NO_DEPOSIT_DISCOUNT = "exit_venue_no_deposit_discount";
        // 黑名单提示语
        public static final String BLACK_LIST_RULE_TIP = "black_list_rule_tip";
        //网络用户访问限制规则
        public static final String NET_USER_ACCESS_LIMIT_RULE = "net_user_access_limit_rule";
        // 启用短信账户
        public static final String ENABLE_SMS_ACCT = "enable_sms_acct";
        //手环赔偿须知
        public static final String BRACELET_NOTICE_OF_COMPENSATION = "bracelet_notice_of_compensation";
        //车牌正则
        public static final String CAR_PLATE_REX = "car_plate_rex";
        //车牌正则
        public static final String PARK_PAY_RULE = "park_pay_rule";
        // 学校课导入模板 每周的时间段数量
        public static final String SCHOOL_LESSON_IMPORT_WEEK_TIME = "school_lesson_import_week_time";
        // 建设支付商户信息-线下支付
        public static final String CCB_MCH_INFO = "ccb_mch_info";
        //自助机和Web快速购票提示登记车牌号-场馆参数
        public static final String ATM_BUY_TICKET_CAR_NUMBER_REGISTRATION = "atm_buy_ticket_car_number_registration";
        //一卡通绑定车牌数量限制-中心参数
        public static final String ECARD_CAR_NUMBER_AMOUNT_LIMIT = "ecard_car_number_amount_limit";
        // 限制的订单类型
        public static final String LIMIT_TRADE_TYPE_CODE = "limit_trade_type_code";
        // 微信公众号停车缴费是否选择当前中心下的场馆-中心参数
        public static final String WECHAT_CHOOSE_VENUE_THIRD_PARTY_PARKING_CHARGE = "wechat_choose_venue_third_party_parking_charge";
        // 三方停车系统编码-中心/场馆参数
        public static final String THIRD_PARTY_PARKING_LOT_CODE = "third_party_parking_lot_code";
        // 大华停车系统用户名-中心/场馆参数
        public static final String DA_HUA_PARKING_LOT_USERNAME = "da_hua_parking_lot_username";
        // 大华停车系统accessToken-中心/场馆参数
        public static final String DA_HUA_PARKING_LOT_ACCESS_TOKEN = "da_hua_parking_lot_access_token";
        // 大华停车系统serverAddr-中心/场馆参数
        public static final String DA_HUA_PARKING_LOT_SERVER_ADDR = "da_hua_parking_lot_server_addr";
        // 大华停车系统道闸信息-中心/场馆参数
        public static final String DA_HUA_PARKING_LOT_EXIT_DEVICE_CHANNEL = "da_hua_parking_lot_exit_device_channel";
        // 捷顺停车系统login服务地址-中心/场馆参数
        public static final String JIE_SHUN_PARKING_LOGIN_SERVER_ADDR = "jie_shun_parking_login_server_addr";
        // 捷顺停车系统业务服务地址-中心/场馆参数
        public static final String JIE_SHUN_PARKING_SERVICE_SERVER_ADDR = "jie_shun_parking_service_server_addr";
        // 捷顺停车系统cid-中心/场馆参数
        public static final String JIE_SHUN_PARKING_LOT_CID = "jie_shun_parking_lot_cid";
        // 捷顺停车系统usr-中心/场馆参数
        public static final String JIE_SHUN_PARKING_LOT_USR = "jie_shun_parking_lot_usr";
        // 捷顺停车系统psw-中心/场馆参数
        public static final String JIE_SHUN_PARKING_LOT_PSW = "jie_shun_parking_lot_psw";
        // 捷顺停车系统停车版本号 应该是固定的2
        public static final String JIE_SHUN_PARKING_LOT_V = "2";
        // 停车测试数据开关
        public static final String JIE_SHUN_PARKING_TEST_INFO = "jie_shun_parking_test_info";
        // 场馆部署停车系统--场馆参数
        public static final String VENUE_DEPLOYMENT_PARKING_LOT = "venue_deployment_parking_lot";
        // 停车用户场内消费后停车价格策略 -中心/场馆参数
        //{"discountType":0：减免金额，1：减免时间(分钟为单位)，2：全免, 3.捷顺的价格减免    "discountValue":优惠类型为金额时，单位为元；优惠类型为时间时，单位为分钟;全免无意义}
        public static final String PARKING_DISCOUNT_STRATEGY = "parking_discount_strategy";
        //捷顺停车-保龄球馆的配置参数 1表示开启为保龄球馆
        public static final String JIE_SHUN_SPECIAL_VENUE_TAG = "jie_shun_special_venue_tag";
        //捷顺停车-特殊场馆保龄球馆处理-保龄球场馆特殊处理的卡等级id 支持多个等级 以，分隔
        public static final String JIE_SHUN_SPECIAL_GRADE_IDS = "jie_shun_special_grade_ids";
        // 停车用户场内消费后 停车单价 {"priceValue":100分,"priceTime":30min} -中心/场馆参数
        // 五台山价格规则 {"priceWithOneHour":6,"period":"08:00-20:00","preferentialMin":"3","preferentialMax":"6","preferentialFee":"5"}
        public static final String PARKING_PRICE = "parking_price";
        public static final String FREE_PARKING_TIME_AFTER_PAY = "free_parking_time_after_pay";
        // 购买闸机票最少专项卡余额限制
        public static final String GATE_TICKET_LEAST_MONEY_LIMIT = "gate_ticket_least_money_limit";
        //控制场地块数量参数
        public static final String VENUE_FIELD_PIECE_NUM = "venue_field_piece_num";
        //国信荣成订金不校验tradeTypeCode
        public static final String CHECK_TRADE_TYPE_CODE = "check_trade_type_code";
        //帆软10.0报表用户名密码
        public static final String FINE_USERNAME_PASSWORD = "fine_username_password";

        //默认场馆参数
        public static final String DEFAULT_VENUE_ID = "default_venue_id";

        // 青岛国信-国信通-商户编码-场馆参数
        public static final String GXT_MERCHANT_CODE = "gxt_merchant_code";
        // 青岛国信-国信通-组织编码-场馆参数
        public static final String GXT_ORG_CODE = "gxt_org_code";
        // 青岛国信-国信通-系统编码-场馆参数
        public static final String GXT_SYS_ID = "gxt_sys_id";
        // 青岛国信-国信通-积分通积比例-场馆参数
        public static final String GXT_EARN_POINTS_PERCENT = "gxt_earn_points_percent";
        // 青岛国信-国信通-渠道-中心参数
        public static final String GXT_BUSINESS_CHANNEL = "gxt_business_channel";
        // 青岛国信-国信通-渠道-中心参数
        public static final String GXT_DEVICE_CHANNEL = "gxt_device_channel";
        // 青岛国信-国信通-商城地址-中心参数
        public static final String GXT_STORE_URL = "gxt_store_url";
        // 青岛国信-国信通-商城图标-中心参数
        public static final String GXT_STORE_ICON = "gxt_store_icon";
        // 青岛国信-国信通-积分通积比例-中心参数 1元-N积分,默认1
        public static final String GXT_MONEY_TO_POINTS_PERCENT = "gxt_money_to_points_percent";
        // 青岛国信-国信通-积分兑换比例-中心参数 N积分-1元,默认100
        public static final String GXT_POINTS_TO_MONEY_PERCENT = "gxt_points_to_money_percent";
        // 青岛国信-国信通-是否可用国信通积分抵扣支付-中心参数
        public static final String GXT_POINTS_PAY = "gxt_points_pay";
        // 青岛国信-国信通-积分兑换比例上限-中心参数
        public static final String GXT_POINTS_PAY_PERCENT_LIMIT = "gxt_points_pay_percent_limit";
        // 青岛国信-国信通-积分兑换倍数-中心参数
        public static final String GXT_POINTS_PAY_TIMES = "gxt_points_pay_times";
        // 青岛国信-国信通-是否开启会员服务
        public static final String GXT_MEMBER_FLAG = "gxt_member_flag";
        // 国信微信公众号获取默认的一卡通充值场馆
        public static final String GXT_RECHARGE_VENUE = "gxt_recharge_venue";
        // 五台山未激活的不展示有效期
        public static final String SHOW_VALID = "show_valid";
        public static final String CHESS_RIGHT = "chess_right"; //棋牌赛事权限
        //西浦小程序上进行退课操作，优惠券，钱直接推到会员的账户上，无需经过退款结算
        public static final String XIPU_BACKFEE_TAG = "xipu_backfee_tag";
        // 场地预定校验微信用户下是否有办理该项目的专项卡，开启此参数后，有卡用户正常预定，无卡用户只可预定当天
        public final static String CHECK_CARD_BEFORE_FIELD = "check_card_before_field";

        //新模型期次班级是否增加上课计划
        public static final String TERM_CLASS_CHOOSE_LESSON_PLAN = "term_class_choose_lesson_plan";
        // 会员查询-中心参数 展示有效期的专项卡类型 0-期间卡,1-计次卡,2-余额专项卡
        public static final String CARDS_SHOW_EFFECT_TIME = "cards_show_effect_time";
        //临沂计时票不用微信支付
        public static final String LINYI_CHARGE_MODE = "linyi_charge_mode";
        public static final String ROBOT_ADD_DELAY = "robot_add_delay";//拼团自动添加初始拼团机器人
        public static final String ROBOT_BARGAIN_DELAY = "robot_bargain_delay";//砍价发起后距砍价结束前N分钟，未砍价成功，系统生成机器人帮砍
        public static final String GROUP_PURCHASE_PIC = "group_purchase_pic";//拼团活动默认图
        public static final String GROUP_PURCHASE_SHARE_PIC = "group_purchase_share_pic";//拼团活动分享默认图
        public static final String BARGAIN_PIC = "bargain_pic";//砍价活动默认图
        public static final String BARGAIN_SHARE_PIC = "bargain_share_pic";//砍价活动分享默认图
        public static final String HOUSE_PROPERTY_CENTER_SHOW = "house_property_center_show";//物业租赁是否按中心展示
        public static final String STAFF_MODIFY_IGNORE_PASSWORD_VERIFY = "staff_modify_ignore_password_verify";//修改员工信息时不强制必须输入密码
        // 连续时段的场地票（可以不在同一片场地）支持自动入馆
        public static final String SERIES_FIELD_AUTO_ENTER = "series_field_auto_enter";
        public static final String SHOW_BANK_CARD = "show_bank_card";//银行卡是否必填
        //出馆时查询当天的入馆订单
        public static final String FIND_ENTRY_INFO_WITHIN_TODAY = "find_entry_info_within_today";
        // 散客票(次票)使用灯控-支持开灯，值为1时使用灯控，默认为0，场馆参数
        public static final String SIMPLE_TICKET_USE_LIGHT = "simple_ticket_use_light";
        // 散客票(次票)使用灯控的场地配置信息
        public static final String SIMPLE_TICKET_CONFIG = "simple_ticket_config";

        //AA约球
        public static final String SHARE_CHAMBER_MINI_APP = "share_chamber_mini_app"; //分享约球小程序二维码
        public static final String CHAMBER_TICKET = "chamber_ticket"; //约球票
        // 出馆结算时如果已经出馆了entryInfo.enterState='2',不再计算超时费
        public static final String CALCULATE_FEE_WITHOUT_OUT_GYM = "calculate_fee_without_out_gym";
        // 入闸时忽略培训课的异常信息，校验程序接着往下走，中心参数，值为1时开启，默认为0
        public static final String IGNORE_ERROR_OF_TRAINING_COURSE = "ignore_error_of_training_course";
        // 查询所有渠道的订单
        public static final String FIND_ALL_CHANNEL_TRADE = "find_all_channel_trade";
        // 专项卡退卡不需要手续费
        public static final String REFUND_CARD_NOT_NEED_HANDLING_FEE = "refund_card_not_need_handling_fee";
        // 专项卡退卡按办卡金额计算手续费
        public static final String REFUND_CARD_HANDLING_FEE_WITH_NEW_CARD = "refund_card_handling_fee_with_new_card";
        // 灯控指令重发的次数限制
        public static final String LIGHT_ORDER_RESEND_TIMES_LIMIT = "light_order_resend_times_limit";
        // 配置手环的使用时长，超过该时长则不让还手环
        public static final String LIMIT_OF_USE_TIME = "limit_of_use_time";
        // 预定场地时限制只能预定本周的场地
        public static final String LIMIT_RESERVE_FIELD_IN_CURRENT_WEEK_ONLY = "limit_reserve_field_in_current_week_only";
        // 超时出馆订单查询天数
        public static final String TIME_OUT_TRADE_DAYS_LIMIT = "time_out_trade_days_limit";
        // 预定场地票时绑定车牌
        public static final String FIELD_TICKET_BIND_CAR_NUMBER = "field_ticket_bind_car_number";
        // 悦动APP教练查询课时的学员报名过滤-中心参数 1-按当前场馆过滤
        public static final String APP_COACH_QUERY_VENUE_LESSON_ENROLL = "app_coach_query_venue_lesson_enroll";
        // 浙江公共体育馆数据元采集相关参数-到场核销-中心参数
        public static final String ENTER_VENUE_DATA_MATE_COLLECTION_APP_INFO = "enter_venue_data_mate_collection_app_info";
        // 浙江公共体育馆数据元采集相关参数-客流数据-中心参数
        public static final String FLOW_DATA_COLLECTION_APP_INFO = "flow_data_collection_app_info";
        // 浙江公共体育馆数据元采集相关参数-场地数据-中心参数
        public static final String FIELD_DATA_COLLECTION_APP_INFO = "field_data_collection_app_info";
        // 浙江公共体育馆数据元采集-场馆编码映射-中心参数
        public static final String VENUE_DATA_MATE_COLLECTION_VENUE_ID = "venue_data_mate_collection_venue_id";
        // 浙江公共体育馆数据元采集运动项目映射-全局参数
        public static final String VENUE_DATA_MATE_COLLECTION_SPORT_ITEM_MAP = "venue_data_mate_collection_sport_item_map";
        // 浙江公共体育馆数据元采集-场馆编码映射-中心参数
        public static final String VENUE_DATA_MATE_COLLECTION_VENUE_ID_MAP = "venue_data_mate_collection_venue_id_map";
        // web出馆结算时 是否展示出馆按钮
        public static final String EXIT_PAGE_WITH_EXIT_SWITCH = "exit_page_with_exit_switch";
        // 注册赠送优惠券配置是否展示有效期
        public static final String REGISTER_COUPON_WITH_VALID_PERIOD = "register_coupon_with_valid_period";
        //增加web端售票情况展示
        public static final String TICKET_CHANNEL_SALE_FLAG = "ticket_channel_sale_flag";
        // 场地灯控提前开灯
        public static final String TURN_ON_THE_FIELD_LIGHT_IN_ADVANCE = "turn_on_the_field_light_in_advance";
        // 是否是国信环境
        public static final String GUO_XIN_NEW_VISITOR_FLAG = "guo_xin_new_visitor_flag";
        // 国信新会籍教练-会员体系
        public static final String GUO_XIN_NEW_MEMBER_FLAG = "guo_xin_new_member_flag";
        // 次票入馆后发送微信公众号消息提醒
        public static final String PUSH_NOTIFY_AFTER_TICKET_CHECKED = "push_notify_after_ticket_checked";
        // 超时结算完成后发送微信公众号消息提醒
        public static final String PUSH_NOTIFY_AFTER_PAYED_OVER_FEE = "push_notify_after_payed_over_fee";
        // 分享票子票单独校验超时结算费用及状态
        public static final String SPILT_CHECK_SHARED_TICKET = "spilt_check_shared_ticket";
        //  是否开启号码牌装备短信模板   0-未开启 1-开启
        public static final String CAMP_SIGN_TEMPLATE = "camp_sign_template";
        // 通过mac地址确定设备归属的场馆
        public static final String IDENTITY_VENUE_BY_MAC_ADDRESS = "identity_venue_by_mac_address";

        public static final String PLATFORM_APP_IDS = "platform_app_ids";
        // 不校验入馆记录的状态
        public static final String NOT_CHECK_TICKET_ENTRY_INFO_STATE = "not_check_entry_info_state";
        // 中心服务点使用产品所属的场馆
        public static final String CENTER_SERVER_USE_PRODUCT_BELONG_VENUE = "center_server_use_product_belong_venue";
        // 取手环日志处理延迟-场馆参数-单位毫秒
        public static final String BRACELET_FETCH_LOG_RESOLVE_DELAY = "bracelet_fetch_log_resolve_delay";
        //天通苑定制参数 用于-入馆仅一条信息时也会进行弹窗选择 且开启培训课/私教课选择教练 1为有效参数
        public static final String TIAN_TONG_YUAN_IN_THE_LIBRARY = "tian_tong_yuan_in_the_library";
        //（临沂五台山）开启场地的停车优惠数量配置
        public static final String PARKING_DISCOUNTS_NUMBER_SWITCH = "parking_discounts_number_switch";
        //录入医学健康补充数据信息枚举
        public static final String BM_MEDICAL_DATA_ENUM = "bm_medical_data_enum";
        // 办理专项卡时将同种类卡自动延期
        public static final String AUTO_DELAY_SAME_CLASS_CARD_WHEN_OPEN_CARD = "auto_delay_same_class_card_when_open_card";
        // 办理专项卡时将同种类卡自动延期时的规则
        public static final String RULE_OF_AUTO_DELAY_SAME_CLASS_CARD = "rule_of_auto_delay_same_class_card";

        //开票内容
        public static final String CARD_COURSE_TICKET_SERVICE = "card_course_ticket_service";
        public static final String NATIONAL_FITNESS_SERVICE = "national_fitness_service";

        //开票内容对应业务类型
        public static final String CARD_TRADE_TYPE = "card_trade_type";
        public static final String COURSE_TRADE_TYPE = "course_trade_type";
        public static final String TICKET_TRADE_TYPE = "ticket_trade_type";
        public static final String NATIONAL_FITNESS_SERVICE_TRADE_TYPE = "national_fitness_service_trade_type";
        public static final String HANDLING_FEE_TRADE_TYPE = "handling_fee_trade_type";
        public static final String WARDROBE_RENT_TRADE_TYPE = "wardrobe_rent_trade_type";
        public static final String CARD_RECHARGE_TRADE_TYPE = "card_recharge_trade_type";

        //开票内容对应业务名称
        public static final String CARD_TRADE_NAME = "card_trade_name";
        public static final String COURSE_TRADE_NAME = "course_trade_name";
        public static final String TICKET_TRADE_NAME = "ticket_trade_name";
        public static final String NATIONAL_FITNESS_SERVICE_TRADE_NAME = "national_fitness_service_trade_name";
        public static final String HANDLING_FEE_TRADE_NAME = "handling_fee_trade_name";
        public static final String WARDROBE_RENT_TRADE_NAME = "wardrobe_rent_trade_name";
        public static final String CARD_RECHARGE_TRADE_NAME = "card_recharge_trade_name";

        // 新的开票内容配置，用json来设置
        public static final String INVOICE_CONTENT_NEW = "invoice_content_new";
        // 不走service类型的配置查询，存房tradeTypeCode，多个以","分隔
        public static final String NO_SERVICE_ID_INVOICE_CONTENT_TRADE_TYPE_CODE = "no_service_id_invoice_content_trade_type_code";

        // 停车场系统的根URL
        public static final String PARK_API_BASE_URL = "park_api_base_url";
        // 停车场系统的授权URL
        public static final String PARK_API_AUTHORIZE_URL = "park_api_authorize_url";
        // 停车场系统的获取泊位列表url
        public static final String PARK_API_PARKING_SPACE_URL = "park_api_parking_space_url";
        // 停车场系统的clientId
        public static final String PARK_API_CLIENT_ID = "park_api_client_id";
        // 停车场系统的clientSecret
        public static final String PARK_API_CLIENT_SECRET = "park_api_client_secret";

        //增加参数控制冻卡校验冻卡期间有入馆记录不允许冻卡
        public static final String FREEZE_ENTRY_HALL_TAG = "freeze_entry_hall_tag";
        //  跑步机mac地址（编号）
        public static final String TREADMILL_MAC_URL = "treadmill_mac_url";
        public static final String TREADMILL_QRCODE_TIME = "treadmill_qrcode_time";
        // 国信人力资源管理-薪酬计算规则 [{"itemCode":"薪酬项编码","calcMethod":"计算规则编码","calcMethodName":"计算规则名称","calcExpression":"计算公式"}]
        // 配合SalaryTemplateDto.SalaryTemplateItemCode使用
        public static final String SALARY_CALC_METHODS = "salary_calc_methods_";
        //屏蔽的手机号
        public static final String VIRTUAL_PHONE = "virtual_phone";
        // 报名时可以选择新增的赠课和课时
        public static final String TRAINING_PROM_WITH_SUBJECT_FREE_CLASS = "training_prom_with_subject_free_class";
        // 产品可通过服务点次数的规则配置
        public static final String PRODUCT_ENTER_SITE_RULE_CONFIG = "product_enter_site_rule_config";

        // 约球报名选择票发起约球的提示
        public static final String PLAYAA_TICKET_PROMPT = "playAA_ticket_prompt";
        // 安徽省智慧体育平台的appsecret(秘钥)
        public static final String ANHUI_PROVINCE_APPSECRET = "anhui_province_appsecret";
        // 安徽省智慧体育平台的appKey
        public static final String ANHUI_PROVINCE_APPKEY = "anhui_province_appkey";

        // 发起退票时判断是否有约球
        public static final String CANCEL_TICKET_WITH_CHECK_CHAMBER = "cancel_ticket_with_check_chamber";

        // 拼团-培训课-参团跟随团长选择的课程属性 1-同团长配置
        public static final String COURSE_GROUP_PURCHASE_WITH_SPONSOR = "course_group_purchase_with_sponsor";

        // 次卡激活时是否按套餐卡的一套逻辑
        public static final String ACTIVE_DIST_COMBO_TIME_CARD = "active_dist_combo_time_card";
        // 一卡通二维码缓存时间类型标识
        public static final String ECARD_QRCODE_CACHE_TYPE = "ecard_qrcode_cache_type";
        // 分享链接的有效期
        public static final String SHARE_URL_EXPIRATION_DATE = "share_url_expiration_date";
        // 分享资源的有效期
        public static final String SHARE_DEPOSIT_EXPIRATION_DATE = "share_deposit_expiration_date";
        // 分享资源的有效期用账本的有效期
        public static final String SHARE_DEPOSIT_USE_DEPSITE_TIME = "share_deposit_use_depsite_time";
        public static final String USE_GRADE_AUTO_LIFT = "use_grade_auto_lift";
        // 等级过期多少天内还可以尝试保级或者降级
        public static final String GRADE_KEEP_DAY = "grade_keep_day";
        // 私教课签到支持
        public static final String PRIVATE_LESSON_ATTEND_SUPPORT = "private_lesson_attend_support";
        // 美团团购测试开关
        public static final String MEI_TUAN_TICKET_TEST_TAG = "mei_tuan_ticket_test_tag";
        // 特殊退票编辑退款金额
        public static final String REFUND_TICKET_EDIT_MONEY = "refund_ticket_edit_money";

        public static final String POINTS_BANK_TAG = "points_bank_tag";
        // 中心参数-公众号支持微信非税支付的业务, centerId: trade_type_code1,trade_type_code2
        public static final String WEIXIN_NON_TAX_SERVICE_SUPPORT = "weixin_non_tax_service_support";
        // 根据人脸查询未归还手环的在馆记录时要去除的订单类型
        public static final String EXCLUDE_TRADE_TYPE_WHEN_QUERY_UNRETURN_KEY_ENTRY_BY_FACE = "exclude_trade_type_when_query_unreturn_key_entry_by_face";
        // 中心参数——异步计算薪酬，默认打开，配置为0则同步计算,耗时
        public static final String ASYNC_SALARY_CALC = "async_salary_calc";
        // 出闸释放柜子
        public static final String RELEASE_CABINET_AFTER_PASS_GATE = "release_cabinet_after_pass_gate";

        // 国信通小程序售卖文体产品-接口根路径
        public static final String GXT_PRODUCT_PUSH_ROOT = "gxt_product_push_root";
        // 国信通小程序售卖文体产品-产品信息推送路径
        public static final String GXT_PRODUCT_PUSH_URL = "gxt_product_push_url";
        // 国信通小程序售卖文体产品-已购资源状态变更推送路径
        public static final String GXT_RESURCE_STATE_PUSH_URL = "gxt_resource_state_push_url";
        // 国信通小程序售卖文体产品-退卡审核推送路径
        public static final String GXT_CARD_WITHDRAW_STATE_PUSH_URL = "gxt_card_withdraw_state_push_url";
        // 国信通小程序售卖文体产品-验签密钥信息
        public static final String GXT_PRODUCT_PUSH_KEY = "gxt_product_push_key";
        // 余额卡不能购买余额卡
        public static final String CASH_DEPOSIT_CANNOT_BUY_CASH_DEPOSIT = "cash_deposit_cannot_buy_cash_deposit";


        // 是否支持校园卡支付
        public static final String SUPPORT_SCHOOL_CARD = "support_school_card";
        // 校园卡办卡默认场馆
        public static final String SCHOOL_CARD_VENUE_ID = "school_card_venue_id";
        // 校园卡服务器信息
        public static final String SCHOOL_CARD_CLIENT_INFO = "school_card_client_info";
        // 是否刷新校园卡请求缓存
        public static final String REFRESH_SCHOOL_CLIENT = "refresh_school_client";
        public static final String CCB_RSA_PUBLICKEY = "ccb_rsa_publicKey"; // 建行RSA公钥
        public static final String CCB_RSA_PRIVATEKEY = "ccb_rsa_privateKey"; // 建行RSA私钥
        // 定时人脸清除配置 {"nonExercisePeriod": "-6M未锻炼时长"}
        public static final String SCHEDULE_CUST_FACE_EVICT_CONFIG = "schedule_cust_face_evict_config";
        // 会员查询展示健康承诺书信息
        public static final String MEMBER_QUERY_SHOW_HEALTH_COMMITMENT_INFO = "member_query_show_health_commitment_info";

        // 配置高龄人员创建承诺书时强制上传体检报告
        public static final String UPLOAD_PHYSICAL_EXAMINATION_REPORT = "upload_physical_examination_report";
        //超龄人员无法签署健康承诺书
        public static final String OVER_AGE_ON_SIGN_HEALTH_AGREEMENT = "over_age_on_sign_health_agreement";
        // 健康承诺书有效期。 m-分钟，h-小时，d-天，M-月，y-年，例如30d表示30天
        public static final String VALIDITY_OF_HEALTH_AGREEMENT = "validity_of_health_agreement";

        // 五台山 入馆校验健康承诺书
        public static final String ENTER_HALL_CHECK_HEALTH = "enter_hall_check_health";
        // 五台山 健康承诺书标识
        public static final String WUTAI_MOUNTAIN_HEALTH_TAG = "wutai_mountain_health_tag";
        // 五台山 普通冻卡参数控制可以不走审核流
        public static final String FROZEN_CARD_NO_AUDIT = "frozen_card_no_audit";

        // 西浦 优惠券发放是否展示 发放对象 按钮
        public static final String SHOW_ROLE_SEND_BTN = "show_role_send_btn";

        // 一卡通转账需要校验密码
        public static final String CARD_TRANSFER_NEED_VERIFY_PASSWORD = "card_transfer_need_verify_password";
        public static final String LIVE_LIST_START = "live_list_start";   //微信直播列表查询开始index



        // 西浦 是否展示购票限制规则
        public static final String SHOW_TICKET_RESTRICTION_TAG = "show_ticket_restriction_tag";

        // 五台山 转卡为未激活
        public static final String CARD_TRANSFER_NOT_ACTIVE = "card_transfer_not_active";

        public static final String GC_BOOK_RULE = "gc_book_rule";

        //冻卡手续费
        public static final String FROZEN_CARD_COMMISSION = "frozen_card_commission";
        //计次卡有效期是否显示
        public static final String COUNT_CARD_VALIDITY_SHOW = "count_card_validity_show";

        public static final String GROUP_LESSON_BOOK_DAY = "group_lesson_book_day";

        // 建行数字人民币H5
        public static final String CCB_DIGITAL_YUAN_H5= "ccb_digital_yuan_h5";
        public static final String BAIDU_API_SERVICE= "baidu_api_service";
        public static final String BAIDU_SPORTS_URL= "baidu_sports_url";
        // 查询卡包、入馆码时要去除的陪同码票类型-ticketType
        public static final String EXCLUDE_COURSE_ACCOMPANIED_TICKET_TYPE = "exclude_course_accompanied_ticket_type";
        //汉江湾预约提示语
        public static final String HJW_RESERVATION_MESSAGE = "hjw_reservation_message";
        // 保龄球赠送券
        public static final String BOWLING_PRESENT_COUPON = "bowling_present_coupon";


        public static final String GC_BOOK_ATTEND_AHEAD_TIME = "gc_book_attend_ahead_time";
        //百度直播回调接口地址
        public static final String BAIDU_LIVE_CALLBACK_URL = "baidu_live_callback_url";

        public static final String PARKING_AREA_BASIC_RULE = "parking_area_basic_rule";
        public static final String PARKING_RESV_FEE = "parking_resv_fee";
        // 是否展示团体卡功能
        public static final String GROUP_CARD_SHOW_FLAG = "group_card_show_flag";

        public static final String LZAT_SYNC_FLAG = "lzat_sync_flag";  //是否同步到兰州奥体订单
        public static final String LZAT_SYNC_URL = "lzat_sync_info";  //兰州奥体同步信息
        public static final String LZAT_TRADE_TYPE_CODE = "lzat_trade_type_code";  //兰州奥体可被查询到的订单类型
        public static final String LZAT_TRADE_LIMIT_TIME_FRAME = "lzat_trade_limit_time_frame";  //兰州奥体可被查询到的订单时间最淡范围
        //招行相关配置属性
        public static final String CMB_PAY_FLAG = "cmb_pay_flag";   //是否支持招行支付
        public static final String CMB_PAY_SUB_INFO = "cmb_pay_sub_info";   //招行支付商户信息
        public static final String CMB_PAY_NOTIFY_URL = "cmb_pay_notify_url";   //招行支付回调地址
        public static final String CMB_PAY_ENV_INFO = "cmb_pay_env_info";   //招行支付环境信息

        public static final String OPEN_COACH_JUDGE_FLAG = "open_coach_judge_flag";   //是否开启教练时期重复flag
        public static final String WEIXIN_H5_NONTAX_PAY_GET_PAYCODE_URL = "weixin_h5_nontax_pay_get_paycode_url";   //滁州微信免税支付请求url
        public static final String WEIXIN_H5_NONTAX_PAY_PARAM = "weixin_h5_nontax_pay_param";   //滁州微信免税支付配置参数
        public static final String WEIXIN_H5_NONTAX_PAY_GET_PAYSTATUS_URL = "weixin_h5_nontax_get_paystatus_url";
        public static final String WEIXIN_H5_NONTAX_PAY_KEYSTORE_URL = "weixin_h5_nontax_keystore_url";   //滁州微信免税支付配置参数
        public static final String WEIXIN_H5_NONTAX_PAY_NTISCER_URL = "weixin_h5_nontax_ntiscer_url";   //滁州微信免税支付配置参数

        public static final String FILED_UN_PAY_LIMIT_TAG = "filed_un_pay_limit_tag";   //校验

        public static final String UPLOAD_FILE_TYPE_WHITELIST = "upload_file_type_whitelist"; // 文件上传格式白名单
        public static final String UPLOAD_FILE_CONTENT_TYPE_WHITELIST = "upload_file_content_type_whitelist"; // 文件上传类型白名单

        public static final String CARD_DELAY_NO_ACTIVE_TAG = "card_delay_no_active_tag"; //  专项卡延期不校验是否激活开关

        public static final String MINI_APP_BIND_LIMIT_RULE = "mini_app_bind_limit_rule";// 小程序openId绑定次数限制
        public static final String BIND_CARD_CHECK_MOBILE_NUM = "bind_card_check_mobile_num"; // 绑定一卡通是否需要手机号相同
        public static final String CHECK_OPEN_ID_LIMIT = "check_open_id_limit"; // 校验 openid是否在黑名单
        public static final String FIELD_BOOK_LIMIT_BY_OPEN_ID = "field_book_limit_by_open_id"; //是否按照openId校验场地预定信息
        public static final String PAY_BY_WECHAT_SCAN = "pay_by_wechat_scan"; // 微信公众号扫码订单页面付款
        public static final String WECHAT_PAY_URL = "wechat_pay_url"; // 微信公众号付款地址
        public static final String ORDER_START_MONTH = "order_start_month"; // 展示多长时间的订单 单位月
        public static final String PAY_WITH_CHECK_LIMIT = "pay_with_check_limit"; // 付款是看订单是否在黑名单中
        public static final String RESERVE_TOTAL = "reserve_total"; // 五台山到馆预约处理
        public static final String DA_LIAN_RESERVE_TAG = "da_lian_reserve_tag"; // 大连到馆预约处理
        public static final String QUERY_CUSTOMER_INFO_NEW_TAG = "query_customer_info_new_tag"; // 五台山新版会员查询
        // led显示配置 server:ip和port  text:文本 x:x坐标 y:y坐标 width:宽度 height:高度 textColor:字符颜色 backGroundColor:背景颜色
        // {"reserveShow":{"text":"预约人数   人","x":"10","y":"10","width":"10","height":"10","textColor":"10","backGroundColor":"10"}}
        public static final String DA_LIAN_LED_CONFIG = "da_lian_led_config";
        public static final String FIELD_SANCTION_RULE = "field_sanction_rule";// 降权规则
        public static final String FIELD_ORDER_WITH_CASH_DEPOSIT = "field_order_with_cash_deposit";// 订场需要绑定储值卡
        public static final String IS_SHOW_NOACTIVE_CARD_VALIDITY = "is_show_noActive_card_validity";// 是否展示未激活卡的有效期
        public static final String SIGN_WITH_SELF_STUDENT = "sign_with_self_student";
        public static final String THIRD_SYNC_FLAG = "third_sync_flag";   //第三方数据推送
        public static final String THIRD_AUTH_FLAG = "third_auth_flag";   //第三方授权链接
        public static final String CHANG_SHU_BOOK_TAG = "chang_shu_book_tag";   //常熟公会包场标志
        public static final String CHANG_SHU_CHANGE_LEVEL_ID = "chang_shu_change_level_id";   //常熟公会人员修改指定会员等级
        public static final String CHANG_SHU_CHANGE_LEVEL_TAG = "chang_shu_change_level_tag";   //常熟公会人员修改指定会员等级功能开关
        public static final String TICKET_TIME_PERIOD_LIMIT_TAG = "ticket_time_period_limit_tag";   // 限制购票，一个时间段只能购买1张功能开关
        public static final String CHANG_SHU_TAG = "chang_shu_flag";   //能耗单点登录
        public static final String CHANG_SHU_BOOK_ONLY_SHOW_RESERVE_TAG = "chang_shu_book_only_show_reserve_tag";   //常熟公会包场只显示提前预定那一部分的场地标志
        public static final String FITNESS_CLIENT_ID = "fitness_client_id";//科学健身客户端id
        public static final String WEB_REPORT_URL = "web_report_url";// 按中心配置报表地址
        public static final String DQ_ADDRESS_URL = "dq_address_url";// 动恰接口地址
        public static final String DQ_APP_SN = "dq_app_sn";// 动恰接口SN
        public static final String DQ_APP_SECRET = "dq_app_secret";// 动恰接口密钥
        public static final String DQ_OBJ_CODES = "dq_obj_codes";// 动恰设备列表
        public static final String EIOT_HTML_URL = "eiot_html_url";// 安科瑞跳转页面地址
        public static final String EIOT_CID = "eiot_cid";// 安科瑞跳转页面地址
        public static final String CHANG_SHU_COURSE_ENROLL_LIMIT_NUM = "chang_shu_course_enroll_limit_num";// 常熟限制课程报名未签到数量
        public static final String AN_KE_RUI_LOGIN_NAME = "an_ke_rui_login_name";//常熟安科瑞灯控登录账号
        public static final String AN_KE_RUI_LOGIN_PASSWORD = "an_ke_rui_login_password";//常熟安科瑞灯控登录密码
        public static final String AN_KE_RUI_GETTOKEN_URL = "an_ke_rui_gettoken_url";//常熟安科瑞灯控获取token的url
        public static final String AN_KE_RUI_CONTROL_URL = "an_ke_rui_control_url";//常熟安科瑞灯控开关的url
        public static final String GET_WATER_INFO_BY_URL = "get_water_info_by_url";//常熟通过url获取水质等信息
        public static final String GET_WATER_INFO_BODY = "get_water_info_body";//常熟通过url获取水质等信息的请求内容
        public static final String DEPOSIT_PAY_USE_GRANT_LAST = "deposit_pay_use_grant_last";
        public static final String APP_BUY_CARD_LIMIT = "app_buy_card_limit";//小程序控制用户在规定时间段内才能办卡
        public static final String PREMIUM_TRADE_TYPES = "premium_trade_types"; // 是否可以溢价
        public static final String COMMON_PERSON_LIMIT_NUM = "common_person_limit_num"; // 通用联系人绑定次数的限制
        public static final String RECORD_FIELD_ORDER_LOG = "record_field_order_log";
        public static final String RESERVATION_LIMIT_NUM = "reservation_limit_num"; // 预约未核销拉入黑名单次数
        public static final String WECHAT_TICKET_ADVANCE_DAY = "wechat_ticket_advance_day";// 微信次票提前购买时间

        public static final String SIGN_PAD_WSS_SERVER_URL = "sign_pad_wss_server_url";
        public static final String RESERVATION_LIMIT_DAY = "reservation_limit_day"; // 预约未核销拉入黑名单天数
        public static final String AN_KE_RUI_ENERGY_COLLECT_URL = "an_ke_rui_energy_collect_url";//常熟安科瑞获取能耗信息的url
        public static final String TRANSFER_CARD_ACTIVE_IGNORE_DAYS = "transfer_card_active_ignore_days"; // 五台山转卡自动激活不判断自动激活时间
        public static final String CASHPLEDGE_BEFORE_NOW = "cashpledge_before_now"; //查不只是当天的入馆记录，用于退押金
        public static final String CHECK_KEY_NOT_RETURN = "check_key_not_return"; //是否未归还手环
        public static final String AN_KE_RUI_CONTROL_ZHUOQIU_BATAI = "an_ke_rui_control_zhuoqiu_batai";//常熟安科瑞桌球吧台的开关
        public static final String REFRESH_ABC_SETTING = "refresh_abc_setting";
        public static final String ORDER_SEARCH_BY_PHONE_TRADE_TYPE_CODE = "order_search_by_phone_trade_type_code";// 支持手机号查询出的订单类型
        public static final String HONGSHAN_PARKING_ACCESSKEY_ID = "hongshan_parking_accesskey_id";//洪山停车accessKeyId
        public static final String HONGSHAN_PARKING_ACCESSKEY_SECRET = "hongshan_parking_accesskey_secret";//洪山停车accessKeySecret
        public static final String HONGSHAN_PARKING_COMMKEY = "hongshan_parking_commkey";//洪山停车commKey
        public static final String HONGSHAN_PARKING_URL = "hongshan_parking_url";//洪山停车url
        public static final String PARKING_DISCOUNT_TAG = "parking_discount_tag";//是否开启停车优惠
        public static final String CANCEL_PAY_SEND_CODE_TAG = "cancel_pay_send_code_tag";//济南奥体取消订单发验证码校验
        public static final String SECKILL_MINI_QRCODE = "seckill_mini_qrcode";
        public static final String FORBIDDEN_USER_AGENT_CONFIG = "forbidden_user_agent_config";//不被允许的userAgent
        public static final String IP_WHITE_LIST = "ip_white_list";//不被允许的userAgent
        public static final String LIMIT_IP = "limit_ip";//限制IP访问限制
        public static final String CHECK_LOCKER_RETURN = "check_locker_return";
        public static final String ATM_BUY_TICKET_AGREEMENT_CTRL = "atm_buy_ticket_agreement_ctrl";//自助机购票协议控制
        public static final String DEFAULT_TIXIAO = "default_tixiao";//默认体校venueId

        public static final String LOCKER_APP_ID = "locker_app_id";

        //水立方证书类型
        //水立方证书类型 amount-值  unit-单位（1.天 2.周 3.月 4.年）
        // [{"value":"1","name":"深水考试（单日）","amount":1,"unit":"1"},{"value":"2","name":"深水考试（三年）","amount":3,"unit":"4"},{"value":"3","name":"结课考试"}]
        public static final String WATER_CERTIFICATE_TYPE = "water_certificate_type";

        public static final String END_CLASS_FREE_LESSON_NUM = "end_class_free_lesson_num";

        // 配置的唰唰对接信息 {"accountId":"唰唰分配的系统账号","signKey":"唰唰分配的签名秘钥"}
        public static final String SHUA_SHUA_APP_INFO = "shua_shua_app_info";
        // 水立方项目-第三方人脸系统分配的场馆key
        public static final String WATER_CUBE_VENUE_KEY = "water_cube_venue_key";
        // 水立方项目-接口根地址
        public static final String WATER_CUBE_ROOT_URL = "water_cube_root_url";
        // 水立方项目-腾讯人脸注册接口地址
        public static final String WATER_CUBE_FACE_REGISTER_URL = "water_cube_face_register_url";
        // 水立方项目-腾讯人脸更新接口地址
        public static final String WATER_CUBE_FACE_UPDATE_URL = "water_cube_face_update_url";
        // 水立方项目-腾讯人脸删除接口地址
        public static final String WATER_CUBE_FACE_DELETE_URL = "water_cube_face_delete_url";
        // 水立方项目-闸机远程开门
        public static final String WATER_CUBE_REMOTE_OPEN_GATE_URL = "water_cube_remote_open_gate_url";
        // 水立方项目-快诺优手环绑定通知接口地址
        public static final String WATER_CUBE_KEY_BIND_NOTIFY_URL = "water_cube_key_bind_notify_url";
        // 水立方项目-快诺优手环解绑通知接口地址
        public static final String WATER_CUBE_KEY_UNBIND_NOTIFY_URL = "water_cube_key_unbind_notify_url";
        // 水立方项目-唰唰人脸同步接口地址
        public static final String SHUA_SHUA_SYNC_FACE_URL = "shua_shua_sync_face_url";
        // 水立方项目-闸机支持的入馆方式，存在多种入馆方式时以 , 分隔
        public static final String GATE_SUPPORT_ENTER_METHOD = "gate_support_enter_method";
        public static final String HONGSHAN_PARKING_SHOP_ID = "hongshan_parking_shop_id";//洪山停车shopid配置
        // 腾讯人脸groupId
        public static final String TENCENT_GROUP_ID = "tencent_group_id";
        // 水立方深水票给同一场次入馆记录固定豁免的超时时间
        public static final String WATER_CERTIFICATE_FREE_TIME = "water_certificate_free_time";
        public static final String CONTINUE_CABINET_CTRL = "continue_cabinet_ctrl";//五台山续柜判断控制，其他场馆还按原来逻辑
        public static final String JNQM_BLOCK = "jnqm_block";
        public static final String CHECK_CABINET_STATE = "check_cabinet_state";


        public static final String INTERACTIVE_MINI_QRCODE = "interactive_mini_qrcode";


        public static final String TAX_CATEGORY = "tax_category";


        public static final String INVOICE_WITH_ZERO_TAX = "invoice_with_zero_tax" ;

        public static final String ALARM_CENTER_ID = "alarm_center_id" ;
        public static final String INVOICE_VENUE_ONLY = "invoice_venue_only";// 只能开配置的场馆的发票
        public static final String INVOICE_WITHOUT_AUDIT = "invoice_without_audit"; // 不需要审核直接尝试开票
        public static final String AT_PARKING_CLIENT = "at_parking_client";
        public static final String SEND_PARKING_COUPON = "send_parking_coupon";// 发放停车优惠券
        public static final String AS_VIDEO_AUDIT = "as_video_audit"; // 协会视频是否需要审核 中心级别参数
        public static final String IS_NEED_PHOTO = "is_need_photo"; // 是否需要上传图片 中心级别参数
        public static final String SHOW_ENROLL_CODE = "show_enroll_code";
        public static final String CHECK_CUSTOMER_INFO = "check_customer_info";
        public static final String NEED_CHECK_RANDOM_NUM = "need_check_random_num";
        public static final String BLUR_INVOICE_CONTENT = "blur_invoice_content";
        public static final String NO_ACTIVE_CANNOT_ENTER = "no_active_cannot_enter";// 未激活不能入馆
        public static final String FREEZE_CARD_ADJUST_DATE = "freeze_card_adjust_date";// 冻卡校验开始结束时间
        public static final String IS_ONLY_SHOW_THIS_COURSE_COACH = "is_only_show_this_course_coach"; // 是否只展示绑定了该课程的教练
        public static final String IS_SAVE_ELEMENT_INFO = "is_save_element_info"; // 是否保存培训报名信息
        public static final String CHECK_BOOKING_HOURS = "check_booking_hours";//分时段订场校验
        public static final String CUSTOM_FEEITEM_INVOICE = "custom_feeitem_invoice_";

        public static final String INVOICE_START_DATE = "invoice_start_date";// 从啥时候开始开票 2024-07-01
        public static final String ENTER_PRIORITY_FLAG = "enter_priority_flag";
        public static final String GROUP_TICKET_BIND_CUST_ID = "group_ticket_bind_cust_id";// 杭州亚运三馆套票绑定指定的客户信息
        public static final String FIX_SERVICE_ORDER = "fix_service_order";//洪山小程序-培训课程-指定项目顺序
        public static final String VENUE_DATA_MATE_COLLECTION_GET_TOKEN_URL = "venue_data_mate_collection_get_token_url"; //宁波奥体客流对接-获取token的url
        public static final String VENUE_DATA_MATE_COLLECTION_PUSH_DATA_URL = "venue_data_mate_collection_push_data_url"; //宁波奥体客流对接-推送数据的url
        public static final String CANCEL_TRADE_LIMIT_DAY = "cancel_trade_limit_day"; //可返销不同业务的时间
        public static final String CHECK_TRADE_MONEY = "check_trade_money";
        public static final String USE_COLLECT_COUPON_NEW = "use_collect_coupon_new";

        public static final String NX_LOTTERY_CHECK_IP_APPCODE = "nx_lottery_check_ip_appcode";  //宁夏抽奖ip校验
        public static final String NX_LOTTERY_SRCSYS_ID = "nx_lottery_srcsys_id";  //宁夏抽奖接入平台编码
        public static final String NX_LOTTERY_KEY = "nx_lottery_key";  //宁夏抽奖签名密钥
        public static final String NX_LOTTERY_CHECK_PHONE_URL = "nx_lottery_check_phone_url";  //宁夏抽奖校验手机号url
        public static final String NX_LOTTERY_SALES_ID = "nx_lottery_sales_id";  //宁夏抽奖销售品编码salesId
        public static final String WECHAT_INVOICE_AFTER_BUY = "微信购票后n天才可以开票";
        public static final String GEN_PDF_AGREEMENT = "gen_pdf_agreement"; // 自动拼接协议内容和签名

        public static final String CO_APP_ID = "co_app_id";
        public static final String HS_TRAINING = "hs_training"; //家校通
        public static final String LESSON_RESV_NOTICE_AHEAD = "lesson_resv_notice_ahead";//家校通预约上课提醒提前时间 小时
        public static final String LESSON_RESV_NOTICE_LEAD_TIME = "lesson_resv_notice_lead_time";//家校通预约上课提醒提前时间的提前时间分钟
        public static final String SPORTS_LEVEL_RULE = "sports_level_rule";
        public static final String NET_USER_CAR_FLAG = "net_user_car_flag";// 停车绑定关系在net_user_car
        public static final String SHAOQUANFACE = "shaoquan_face";// 人脸处理类型
        public static final String HUBEI_APPID = "hubei_appid";
        public static final String HUBEI_APPSECRET = "hubei_appsecret";

        public static final String AUTO_CANCEL_ROOM_OCCUPY_WHEN_CLOSE = "auto_cancel_room_occupy_when_close"; // 闭馆自动取消教室占场
        public static final String CHECK_ROOM_OCCUPY_WHEN_CLOSE = "check_room_occupy_when_close";

        public static final String RETURN_INTER_TRANSFER_TAG = "return_inter_transfer_tag";


        public static final String USE_PRODUCT_STOCK = "use_product_stock";// 使用产品库存表
        public static final String LT_PARKING_DISCOUNT_LIMIT = "lt_parking_discount_limit"; // 朗通停车折扣数量限制
        public static final String FREEZE_CARD_LIMIT = "freeze_card_limit";// 五台山冻卡限制只能选择3个月
        public static final String PROJECT_REDIS_SLOT = "project_redis_slot";
        public static final String DEFAULT_PROJECT_VENUE_ID = "default_project_venue_id";
        public static final String DEFAULT_PROJECT_SERVICE_ID = "default_project_service_id";
        public static final String WAITING_ROOM_REDIS_PREFIX = "waiting_room_redis_prefix";

        public static final String TEAM_TICKET_QRCODE_PREFIX = "team_ticket_qrcode_prefix";//苏超团体票二维码前缀

    }

    public static class EnterState {
        public static final String INIT = "0";//未入馆
        public static final String IN = "1"; // 已入馆
        public static final String OUT = "2"; // 已离馆
    }

    public static class CycleOccupyAutoPay {
        public static final String AUTO = "1"; //自动用一卡通支付
        public static final String NOT_AUTO = "0"; //不自动用一卡通支付
    }

    /**
     * 验证码类型
     */
    public static class VerifyCodeType {
        public static final String WEIXIN = "1";
        public static final String LOGIN = "0";
        public static final String APP = "3";
        public static final String ATM = "4";
        public static final String CHANGE_PASSWORD = "5";
        public static final String MINI_OA_LOGIN = "6";
        public static final String XIPU_WEBSITE = "7";
        public static final String COMPLAINT = "8"; // 投诉
        public static final String BLACK_LIST_CHECK = "9"; // 黑名单验证
        public static final String CERTIFICATION = "10"; // 实名认证
        public static final String CANCEL_ORDER_CHECK = "111"; // 取消订单校验
    }

    public static class VerifyCodeValidTag {
        public static final String VALID = "1";
        public static final String INVALID = "0";
    }

    public static class ElementType {
        public static final String PAGE = "02";
        public static final String AD = "01";
    }

    /**
     * 业务来源渠道
     */
    public static class Channel {
        public static final long ALL = -1; //全渠道
        public static final long PLATFORM = 0; //场馆平台
        public static final long WECHAT = 1; //微信平台
        public static final long AUTO_GATE = 2; //闸机
        public static final long ATM = 3; //自助终端
        public static final long HANDHELD = 4; //手持终端
        public static final long APP = 5; //苏打app
        public static final long ALISPORTS = 6; //阿里体育
        public static final long THIRD_PARTY_APP = 7; //第三方app
        public static final long CASH_REGISTER = 10; //收银机
        public static final long WECHAT_MINI_APP = 11; //微信小程序
        public static final long ALIPAY_MINI_APP = 12; //支付宝小程序
        public static final long WALL_HANGING = 13; // 挂墙终端
        public static final long VENDING_MECHINE = 14; // 自助售货机
        public static final long WEBSITE = 15; //website
        public static final long NATIONAL_FITNESS_PLATFORM = 16; //全民健身平台
        public static final long HANG_RING_MACHINE = 17; //青岛国信手环机
        public static final long PING_YANG_CENTER = 18; //平阳全民健身中心-云娱智慧平台
        public static final long DIVING = 19;//潜水平台
        public static final long TNCI = 20;//天时同城
        public static final long CAMPAIGN_PLATFORM = 21;//百姓大联赛平台
        public static final long EHB_APP = 22;//鄂惠办
        public static final long ANHUI_PROVINCE_APP = 23;//安徽省平台
        public static final long GXT = 24;//国信通
        public static final long ABC = 27;//农行
        public static final long ZHTTIYU = 30;//浙体育
        public static final long CSNSH = 31;//常熟农商行
    }

    /**
     * 营销活动渠道
     */
    public static class PromChannels {
        public static final String ALL = "1,2,3"; //全渠道
        public static final String WEB = "1";
        public static final String APP = "2";
        public static final String WECHAT = "3";
    }

    public static class PageSize {
        public static final int WECHATNEWS_PAGESIZE = 8;
        public static final int WECHATORDER_PAGESIZE = 5;
        public static final int WECHAT_REVIEW_PAGESIZE = 5;
        public static final int PUBLIC_CAMP_PAGESIZE = 6;
    }

    /**
     * 家庭关系标志
     */
    public static class FamilyRelation {
        public static final String GROUP_CUSTOMER_USER_ID = "groupUserId";
        public static final String MAIN_CUSTOMER_USER_ID = "mainCustomerUserId";
        public static final String MEMBER_CUSTOMER_USER_ID = "memberCustomerUserId";
        public static final String SPLIT_FLAG = ",";
        public static final String MEMBER_USER_RELATION_MAP = "memberUserRelationMap";
        public static final String MAIN_USER_RELATION = "mainUserRelation";
        public static final String USER_PAY_ITEM = "userPayItem";
    }

    public static class ProductPriceType {
        public static final String ACHIEVE_DISCOUNT_TYPE = "1"; // 多买优惠
        public static final String EQUAL_DISCOUNT_TYPE = "2"; // 第n件优惠
        public static final String STAIR_PRICE_TYPE = "3"; // 阶梯价格
        public static final String MEMBER_PROM = "4"; // 会员优惠
    }

    /**
     * 审核状态
     */
    public static class AuditState {
        public static final String NOT_AUDIT = "0"; // 未审核
        public static final String APPROVAL = "1"; // 审核通过
        public static final String REJECT = "2"; // 审核不通过
        public static final String CANCEL = "3"; // 取消审核
    }

    /**
     * 抽签状态
     */
    public static class DrawState {
        public static final String PENDING = "0"; // 待抽签
        public static final String SELECTED = "1"; // 中签
        public static final String UNSELECTED = "2"; // 不中签
    }

    /**
     * 教练预约状态
     */
    public static class AppointState {
        public static final String UNFINISH = "0"; // 未完工
        public static final String NORMAL = "1"; // 正常
        public static final String CANCEL = "2"; // 取消
        public static final String PAID = "3"; // 已经支付
        public static final String REFUND = "4"; // 已退款
        public static final String UNCHOOSED = "9"; // 未选择
    }

    /**
     * 用户状态
     */
    public static class UserStatus {
        //正常
        public static final String NORMAL = "0";
        //挂失
        public static final String LOSS = "1";
        //预挂失
        public static final String PRE_LOSS = "2";
        //退款冻结
        public static final String FREEZE_BY_CANCEL_FEE = "3";
        //返销
        public static final String CLOSED = "5";
    }

    /**
     * buy_gifts 表的礼品类型
     */
    public static class GiftType {
        public static final String PRODUCT = "1"; // 产品
        public static final String COUPON = "2"; // 优惠券
        public static final String RESOURSE = "3"; // 资源
        public static final String PRESENT = "4"; // 礼品
    }

    /**
     * 默认工号
     */
    public static class Staff {
        public static final long PLATFORM_STAFF_ID = 100001;
        public static final long WECHAT_STAFF_ID = 100000;
        public static final long HB_STAFF_ID = 1000010;
        public static final long APP_STAFF_ID = 1000020;
        public static final long ALI_STAFF_ID = 1000030;
        public static final long AUTO_GATE_STAFF_ID = 1000040;
        public static final long THIRD_PARTY_APP_STAFF_ID = 1000050;
        public static final long WECHAT_MINI_APP_STAFF_ID = 1000060;
        public static final long ALIPAY_MINI_APP_STAFF_ID = 1000070;
        public static final long VENDING_MECHINE_STAFF_ID = 1000080;
        public static final long ATM_STAFF_ID = 1000090;
        public static final long WEBSITE_STAFF_ID = 1000100;
        public static final long NINGBO_NATIONAL_FITNESS_PLATFORM_STAFF_ID = 1000110;
        public static final long WALL_HANGING_STAFF_ID = 1000120;
    }

    public static class CouponForTradeExt {
        public static final String COUPON_IDS_SPLIT = ",";
        public static final String COUPON_IDNUM_SPLIT = ":";
        public static final String COUPON_IDS_KEY = "couponIdsKey";
    }

    public static class GiftResTradeExt {
        public static final String GIFTRES_SPLIT = ",";
        public static final String GIFTRES_IDNUM_SPLIT = ":";
        public static final String GIFTRES_KEY = "giftRes_key";
    }

    /**
     * 登录前缀
     */
    public static class LoginTypePrefix {
        // 手机登录
        public static final String MOBILE_LOGIN = "mobile_";
    }

    /**
     * 登录类型
     */
    public static class LoginType {
        // 普通登录
        public static final String COMMON_LOGIN = "1";
        // 手机登录
        public static final String MOBILE_LOGIN = "2";
    }

    /**
     * 价格项目类型 1-普通票价格 2-场地票价格
     */
    public static class PriceItemType {
        public static final String SIMPLE = "1";
        public static final String FIELD = "2";
    }

    //冻卡参数的key
    public static class FreezeCardParamKey {
        //冻卡列表key
        public static final String PARAM_FREEZE_CARD_LIST = "tradeFreezeCardList";
    }

    //常用参数的key
    public static class CommonParamKey {
        //参数ecardno的key
        public static final String PARAM_ECARD_NO = "ecardNo";
        public static final String PARAM_TRADE_ID = "tradeId";
        //参数员工对应的key
        public static final String PARAM_STAFF = "staff";
    }

    /**
     * 日期类型
     */
    public static class DayType {
        // 每周的星期几
        public static final String DAY_OF_WEEK = "day_of_week";
        // 每月的几号
        public static final String DAY_OF_MONTH = "day_of_month";
        //  每年的几月几号
        public static final String DAY_OF_YEAR = "day_of_year";
        // 固定日期
        public static final String DATE = "date";
        // 起止日期
        public static final String DATE_BETWEEN = "date_between";
    }

    /**
     * 短信模板类型
     */
    public static class SmsTempletType {
        //领取优惠劵
        public static final int COUPON_SELECT = 10;
        //会员绑定
        public static final int MEMBER_BIND = 11;
        //成功领取后通知
        public static final int COUPON_RECEIVE = 12;
        //绑定微信企业号
        public static final int CORP_MEMBER_BIND = 13;
        //生日祝福短信
        public static final int BIRTHDAY_WISHES = 14;
        //微信端修改客户手机号
        public static final int UPDATE_CUST_PHONE = 15;
        //售票/订场使用卡支付
        public static final int CARD_PAY = 16;
        //微信公众号申请手机登陆
        public static final int PHONE_LOGIN = 17;
        //协议占场一卡通充值提醒
        public static final int OCCUPY_RECHARGE = 20;
        //苏打app注册验证码
        public static final int SUDA_REGISTER = 21;
        //苏打app绑定一卡通
        public static final int SUDA_BING = 22;
        //苏打app修改密码
        public static final int SUDA_MODIFY_PASSWORD = 23;
        //苏打app解绑一卡通
        public static final int SUDA_NUBING = 24;
        //短信余额不足提醒
        public static final int SMS_WARNING = 25;
        //订场提醒给用户
        public static final int BOOK_NOTICE_CUST = 26;
        //订场提醒给员工
        public static final int BOOK_NOTICE_STAFF = 27;
        //查询培训课程信息
        public static final int QUERY_COURSE_INFO = 28;
        //上课提醒模板
        public static final int COURSE_REMIND = 29;
        //退订提醒给用户
        public static final int CANCEL_NOTICE_CUST = 30;
        //退订提醒给员工
        public static final int CANCEL_NOTICE_STAFF = 31;
        //私教签到提醒客户
        public static final int PRIVATE_SIGN = 32;
        //赛事活动队员报名
        public static final int PLAYER_ENROLL = 33;
        //培训课报名提醒
        public static final int TRAINING_ENROLLED = 34;
        //私教课报名提醒
        public static final int PRIVATE_COURSE_ENROLLED = 35;
        //小程序登录验证码
        public static final int WECHAT_APPLET_LOGIN = 38;
        //产品激活
        public static final int PRODUCT_ACTIVE = 40;
        //atm体测绑定手机号
        public static final int ATM_BM = 41;
        //开班发送短信
        public static final int OPEN_CLASS = 42;
        //赛事报名审核信息
        public static final int PUBLIC_CAMP_ENROLL_AUDIT = 43;
        //续课通知
        public static final int CLASS_CONTINUE_NOTICE = 45;
        //停课通知
        public static final int STOP_CLASS_NOTICE = 46;
        //退课提交通知
        public static final int REFUND_COURSE_SUBMIT = 47;
        //退课成功通知
        public static final int REFUND_COURSE_SUCCESS = 48;
        //修改一卡通支付密码
        public static final int MODIFY_ECARD_PASSWORD = 49;
        //小程序手机号登录
        public static final int MINI_APP_PHONE_LOGIN = 50;
        //小程序绑卡
        public static final int MINI_APP_MEMBER_BIND = 51;
        //签署安全协议
        public static final int SIGN_SECURITY_AGREEMENT = 52;
        //会员过期提醒
        public static final int CUST_OVER_TIME = 53;
        //票即将超时提醒
        public static final int TICKET_TIMEOUT_REMIND = 54;
        //约战支付提醒
        public static final int GAME_PAY_REMIND = 55;
        //约战撮合成功提醒
        public static final int GAME_SUCCESS_REMIND = 56;
        //约战取消提醒
        public static final int GAME_CANCEL_REMIND = 57;
        //约战取消保留场地提醒
        public static final int GAME_CANCEL_KEEP_FIELD_REMIND = 58;
        //租柜即将过期提醒
        public static final int CABINET_OVERDUE_REMIND = 59;
        //私教预约提醒
        public static final int PRIVATE_COURSE_RESV_REMIND = 60;
        //私教预约取消提醒
        public static final int PRIVATE_COURSE_RESV_CANCEL_REMIND = 61;
        // 学校课上课通知老师
        public static final int SCHOOL_COURSE_ATTN = 62;
        // 掌静脉绑定
        public static final int PALM_SET_BIND = 65;
        // 教练员上课通知
        public static final int COACH_CLASS_NOTICE = 64;
        //审核提醒
        public static final int AUDIT_REMIND = 66;
        //还柜提醒
        public static final int RETURN_LOCKER_REMIND = 67;
        //订场订单发送支付链接
        public static final int FIELD_TRADE_SEND_PAY_LINK = 68;
        public static final int COMPLAINT_VERIFY_CODE = 69; // 投诉验证码
        // 工单催办提醒
        public static final int WORK_ORDER_REMIND = 71;
        //健康承诺书临期提醒
        public static final int AGREEMENT_EXPIRE_REMIND = 69;
        //健康承诺书紧急联系人提醒
        public static final int AGREEMENT_EXPIRE_EMERGENCY_PERSON_REMIND = 70;
        //娱乐项目排队叫号提醒
        public static final int PROJECT_CALL_NUMBER_REMIND = 72;
        //积分变动提醒
        public static final int POINTS_CHANGE = 73;
        //积分清零提醒
        public static final int POINTS_CLEAR = 74;
        //娱乐项目排队到号提前提醒
        public static final int PROJECT_ADVANCE_CALL_NUMBER_REMIND = 75;
        //特殊人群审核成功通知
        public static final int SPECIAL_POPULATION_AUDIT_SUCCESS = 76;
        //特殊人群审核失败通知
        public static final int SPECIAL_POPULATION_AUDIT_FAIL = 77;
        //黑名单验证码
        public static final int CHECK_BLACK_LIST = 78;
        // 候场通知短信模板
        public static final int FIELD_WAIT_NOTICE = 79;
        // 购票信息通知短信模板
        public static final int TICKET_INFO_NOTICE = 80;
        //小程序实名认证
        public static final int MINI_APP_CERTIFICATION = 81;
        //培训机构申请-初审审核通过提醒
        public static final int INSTITUTIONAL_APPLY_AUDIT_ADOPT = 85;
        //赛事报名短信通知模板附带号码牌/装备
        public static final int CAMP_SIGN_TEMPLATE_WITH_PLAYER_NO_EQUIPMENT = 104;
        public static final int CAMP_SIGN_TEMPLATE_WITH_PLAYER_NO = 105;

        // 停车预约
        // 前边号码被占用了 离谱
        public static final int PARKING_RESV_NOTICE = 110;
    }

    /**
     * 邮件模板类型
     */
    public static class MailTempletType {
        //订单支付—订场
        public static final int PAY_OF_BOOKING = 1;
        //订单支付—购票
        public static final int PAY_OF_TICKET = 2;
        //订单支付—购课包
        public static final int PAY_OF_COURSE_ENROLL = 3;
        //订单支付—活动包场
        public static final int PAY_OF_ROOM_BOOKING = 4;
        //退票
        public static final int REFUND_TICKET = 5;
        //管理员退票
        public static final int ADMIN_REFUND_TICKET = 6;
        //领取分享票
        public static final int GET_SHARE_TICKET = 7;
        //取消分享票-分享账户
        public static final int CANCEL_SHARED_TICKET_FOR_OWNER = 8;
        //取消分享票-被取消账户
        public static final int CANCEL_SHARED_TICKET_FOR_RECIP = 9;
        //赠送优惠券
        public static final int GIFT_COUPON = 10;
        //协议占场
        public static final int AGREEMENT_OCCUPATION = 11;
        //协议占场取消
        public static final int AGREEMENT_OCCUPATION_CANCEL = 12;
        //协议包场
        public static final int ROOM_OCCUPY = 13;
        //退课
        public static final int REFUND_COURSE = 14;
        // 活动占场导致的协议占场退票邮件通知
        public static final int ACTIVE_POSSESSION_NOTICE = 15;
        // 教室占场导致的协议占场退票邮件通知
        public static final int JIAOSHI_POSSESSION_NOTICE = 16;
    }


    /**
     * 产品是否配置激活期间
     */
    public static class ProductActive {
        //不自动激活，产品使用时按使用时间激活
        public static final String NOT_AUTO_ACTIVE = "0";
        //自动激活，按配置的激活天数激活
        public static final String AUTO_ACTIVE = "1";
    }

    /**
     * 是否在场馆中
     */
    public static class EntryInfoState {
        //在馆未结算
        public static final String IN_ENTRY = "0";
        //离馆已结算
        public static final String OUT_ENTRY = "1";
        //结算中
        public static final String SETTLEMENTING = "2";
    }

    /**
     * 入馆属性表
     */
    public static class EntryInfoAttr {
        //用水量
        public static final String WATER_AMOUNT = "water_amount";
        //剩余水量
        public static final String WATER_REMAIN_AMOUNT = "water_remain_amount";
        //柜子信息
        public static final String CABINET_INFO = "cabinet_info";
        //手环归还标志，1-已归还，其他-未归还
        public static final String KEY_RETURN_TAG = "key_return_tag";
        public static final String HEALTH_AGREEMENT_LOG_ID = "health_agreement_log_id"; // 健康承诺书ID
        public static final String HEALTH_AGREEMENT_CUST_ID = "health_agreement_cust_id";
        public static final String BRACELET_ID = "bracelet_id";//手环id
        public static final String SERVICE_IDS = "service_ids"; //本次入馆记录适用于哪些服务类型
        public static final String ADVICE_STAFF = "advice_staff"; //咨询录入人
        public static final String ADVICE_TIME = "advice_time"; //咨询录入时间
        //手环性别
        public static final String BRACELET_GENDER = "bracelet_gender";
        // 西浦连续票入馆-记录连续票的ticketId
        public static final String SERIES_TICKET = "series_ticket";
        // 入馆票之绑定手环所对应的服务点id
        public static final String KEY_BELONG_SITE = "key_belong_site";
        public static final String OUT_TRADE_IDS = "out_trade_ids";
        // 分享次卡/直接用次卡账本入馆
        public static final String SHARE_DEPOSIT = "share_deposit";
        public static final String WX_PALMS_ID = "wx_palms_id";

    }

    /**
     * 外屏设备状态
     */
    public static class ExternalDisplayAdType {
        //外屏屏保广告
        public static final String SCREENSAVER = "1";
        //外屏侧边栏广告
        public static final String SIDEBAR = "2";
    }

    /**
     * 柜子租赁规则是否区分产品
     */
    public static class CabinetRentProTag {
        // 区分产品
        public static final String DISTINGUISH_PRO = "1";
        // 不区分产品
        public static final String UNDISTINGUISH_PRO = "0";
    }

    /**
     * 租柜价格配置区分远程或者本地标识
     */
    public static class IsLocalRemote {
        // 支持产品配置的情况,每个产品节点加上本地保存标识
        public static final String IS_LOCAL = "0";
        // 支持产品配置的情况,每个产品节点加上远程查询标识
        public static final String IS_REMOTE = "1";
    }

    /**
     * 专项卡是否激活
     */
    public static class ActiveState {
        // 已激活
        public static final String IS_ACTIVE = "1";
        // 未激活
        public static final String NOT_ACTIVE = "0";
    }

    /**
     * 一卡通折扣类型
     */
    public static class DiscountTag {
        // 没有优惠
        public static final String NONE = "0"; // 没有
        // 百分比
        public static final String PERCENT = "1";
        // 减免金额
        public static final String MONEY = "2";
    }

    /**
     * 一卡通入库日志查询方式
     */
    public static class SearchType {
        //起始卡号查询
        public static final String CARD_NUM = "0";
        //入库起始时间查询
        public static final String ENTER_TIME = "1";
    }

    /**
     * 搜索标记 1今日累计 2已结账 4未入场 5已验票 6场内剩余
     */
    public static class StatisticsSearchType {
        public static final String TOTAL = "1";
        public static final String SETTLED = "2";
        public static final String NOT_ADMITTED = "4";
        public static final String CHECKED = "5";
        public static final String REMAIN = "6";
    }

    /**
     * 微信订单状态
     */
    public static class OrderState {
        public static final int ALL = 0;//全部订单
        public static final int CANCEL = 1;//已经取消的
        public static final int UN_PAY = 2;//没有付款的
        public static final int PAY = 3;//付款的
        public static final int USED = 4;//已经使用的
        public static final int REFUND = 5;//已退款
        public static final int REFUND_AUDIT = 6;//退款审核
    }

    /**
     * 阿里体育定义的订单状态
     */
    public static class AliOrderState {
        public static final int UN_PAY = 1;//待付款
        public static final int CANCEL = 2;//已经取消的
        public static final int PAY = 3;//付款的
        public static final int OVERTIME = 4;//超时
        public static final int REFUND = 10;//已经退款
    }

    /**
     * 阿里体育验证码使用状态
     */
    public static class AliVerifyCodeState {
        public static final int UN_USE = 0;//未使用
        public static final int USED = 1;//已使用
        public static final int INVALID = 2;//已失效
    }

    /**
     * 阿里体育推送消息失败后,8次重新推送间隔
     */
    public static class AliPushDelayTime {
        public static final int FIRST_TIME = 100;//第一次,默认100ms
        public static final int SECOND_TIME = 2 * 60 * 1000;//第二次,2m
        public static final int THIRD_TIME = 10 * 60 * 1000;//第三次,10m
        public static final int FOURTH_TIME = 10 * 60 * 1000;//第四次,10m
        public static final int FIFTH_TIME = 3600 * 1000;//第五次,1h
        public static final int SIXTH_TIME = 2 * 3600 * 1000;//第六次,2h
        public static final int SEVENTH_TIME = 6 * 3600 * 1000;//第七次,6h
        public static final int EIGHTH_TIME = 15 * 3600 * 1000;//第八次,15h
    }

    /**
     * 员工是否离职标志
     */
    public static class DimissionTag {
        //在职
        public static final String NOT_DIMISSION = "0";
        //离职
        public static final String DIMISSION = "1";
    }

    /**
     * 微信企业号会员状态
     */
    public static class WechatCorpMemberState {
        //已经绑定
        public static final String BIND = "1";
        //没有绑定
        public static final String NU_BIND = "0";
    }

    /**
     * 是否是套票
     */
    public static class GroupTicket {
        // 不是
        public static final String NOT = "0";
        // 是
        public static final String YES = "1";
    }

    public static class Tag {
        public static final String YES = "1";
        public static final String NO = "0";
    }

    /**
     * 销售目标
     */
    public static class SalesTarget {
        // 会籍顾问目标ID
        public static final long CONSULTANT_TARGET = 10000001;
        // 教练目标ID
        public static final long COACH_TARGET = 10000002;
    }

    public static class KeyUseState {
        public static final String NOT_USED = "0";
        public static final String USED = "1";
        // 普通管理员钥匙
        public static final String MANAGER_KEY = "2";
        // 超级管理员钥匙
        public static final String SUPER_MANAGER_KEY = "3";
        //买票未付款绑定占用状态
        public static final String TEMP_OCCUPY = "9";
    }

    /**
     * 培训报名的单双期
     */
    public static class CourseGroupTag {
        // 双期报名
        public static final String YES = "0";
        // 单期报名
        public static final String NO = "1";
    }

    /**
     * 课程报名是否有效
     */
    public static class CourseState {
        // 无效
        public static final String NOT = "0";
        // 有效
        public static final String YES = "1";
        // 课程延期审核中
        public static final String DELAY_AUDIT = "2";
        // 课程退课审核中
        public static final String REFUND_AUDIT = "3";
        // 次课换课取消(冰雪课)
        public static final String CHANGE = "4";
        // 换课审核中(冰雪课)
        public static final String CHANGE_AUDIT = "5";
        // 变更审核中
        public static final String REPLACE_AUDIT = "6";
        // 课程转课审核中
        public static final String TRANSFER_AUDIT = "7";
        // 调整总课时审核中
        public static final String ADJUST_TOTAL_NUM_AUDIT = "8";
        // 调整剩余课时审核中
        public static final String ADJUST_REMAINING_NUM_AUDIT = "9";


        // 冻卡审核中
        public static final String FREEZE_AUDIT = "10";

        // 冻卡中
        public static final String FREEZE = "11";
    }

    /**
     * 课程报名分班状态
     */
    public static class CourseClassState {
        // 不需要
        public static final String NOT_NEED = "0";
        // 未分班
        public static final String NOT_DIVIDED = "1";
        // 已分班
        public static final String DIVIDED = "2";
    }

    /**
     * 课程报名类型
     */
    public static class CourseType {
        // 私教
        public static final String PRIVATE = "1";
        // 培训课
        public static final String COURSE = "2";
        // 课程组合
        public static final String GROUP = "3";
        // 滑冰课程
        public static final String SKATING_COURSE = "4";
    }

    /**
     * 课程报名类型
     */
    public static class CoursePromType {
        // 优惠价格
        public static final String PRICE = "1";
        // 赠品
        public static final String GIFT = "2";
        // 优惠券
        public static final String COUPON = "3";
        // 增资源
        public static final String LESSON = "4";
        // 赠课
        public static final String COURSE = "5";

        // 优惠价格(多课时)
        public static final String BAG_PRICE = "11";
        // 赠品(多课时)
        public static final String BAG_GIFT = "12";
        // 优惠券(多课时)
        public static final String BAG_COUPON = "13";
        // 增资源(多课时)
        public static final String BAG_LESSON = "14";
        // 赠课(多课时)
        public static final String BAG_COURSE = "15";

    }

    /**
     * 优惠券状态
     */
    public static class CouponEntityState {
        // 初始状态
        public static final String NORMAL = "0";
        // 领取未使用
        public static final String FETCH = "1";
        // 领取已使用
        public static final String USED = "2";
        // 禁用
        public static final String FORBIDDEN = "3";
        // 已取消
        public static final String CANCELED = "4";
        // 已退款
        public static final String REFUND = "5";
    }

    /**
     * 续课价格类型(training_course_price.type)
     */
    public static class CoursePriceType {
        // 基础价格
        public static final String BASE = "0";
        // 多买优惠
        public static final String LARGE = "1";
        // 第n件优惠
        public static final String CHOOSE = "2";
        // 续课价格
        public static final String RENEWAL = "3";
        // 冰雪单次课价格
        public static final String ICE_COURSE = "4";
        // 会员级别价格
        public static final String GRADE_PRICE = "5";
        // 课时费
        public static final String CLASS_FEE = "6";
        // 课时包
        public static final String SUBJECT_PRICE = "7";
    }

    /**
     * 培训报名方式
     */
    public static class CourseRegistrationType {
        // 单期
        public static final String SINGLE = "0";
        // 双期
        public static final String DOUBLE = "1";
        // 单期续双期
        public static final String RENEWAL_DOUBLE = "2";
    }

    /**
     * 预约记录状态
     */
    public static class PrivateBookingState {
        // 预约
        public static final String BOOK = "1";
        // 确定
        public static final String CONFIRMED = "2";
        // 取消
        public static final String CANCEL = "3";
        // 签到
        public static final String SIGN = "4";
    }

    /**
     * 场地票查询的天数便宜
     */
    public static class QueryTIcketDay {
        // 右边偏移数
        public static final int LEFT_DAY = -7;
        // 右边偏移数
        public static final int RIGHT_DAY = 6;
    }

    /**
     * 专项余额卡购票优惠类型
     */
    public static class DepositPromType {
        // 折扣百分比
        public static final String DISCOUNT_PERCENT = "1";
        // 折扣金额
        public static final String DISCOUNT_MONEY = "2";
        // 优惠价格
        public static final String PRICE = "3";
    }

    /**
     * 策略类型
     */
    public static class StrategyType {
        // 基础价格
        public static final String COMMON_PRICE = "0";
        // 节假日价格
        public static final String HOLIDAY_PRICE = "1";
        // 特殊定价
        public static final String SPECIAL_PRICE = "2";
        // 团购价
        public static final String GROUP_BUYING_PRICE = "3";
    }

    /**
     * 价格策略对应的价格类型
     */
    public static class PriceStrategyPriceType {
        // 按小时
        public static final String BY_HOUR = "1";
        // 按次
        public static final String BY_NUMBER = "2";
        // 计时
        public static final String BY_TIME = "3";
    }

    /**
     * 支付类型定义
     */
    public static class PayMode {
        //优惠劵
        public static final String COUPON = "10";
        //一卡通
        public static final String ECARD = "11";
        //专项卡
        public static final String SPECIAL_CARD = "12";
        //现金
        public static final String CASH = "13";
        //刷卡
        public static final String CREDIT_CARD = "14";
        //支票
        public static final String CHECK = "15";
        //减免
        public static final String MANUAL_REDUCTION = "16";
        //微信
        public static final String WECHAT = "17";
        //赠款
        public static final String GRANTS = "18";
        //口碑
        public static final String KOUBEI = "21";
        // 支付宝
        public static final String ALIPAY = "22";
        //天猫支付
        public static final String T_MALL = "24";
        // 通联微信
        public static final String POS_WECHAT = "27";
        // 通联支付宝
        public static final String POS_ALIPAY = "28";
        // 通联刷卡
        public static final String POS_CREDIT_CARD = "29";
        // 跨中心一卡通支付
        public static final String CROSS_CENTER_CARD = "25";
        // 挂帐
        public static final String ON_CREDIT = "30";
        // 兑换券
        public static final String EXCHANGE_COUPON = "31";
        // 产业中心
        public static final String INDUSTRY_CENTER = "32";
        //合作商补贴
        public static final String MERCHANT_ALLOWANCE = "37";
        //专项卡赠款
        public static final String SPECIAL_GRANT_CARD = "38";
        //社保
        public static final String SOCIETY = "41";
        //美团
        public static final String MEITUAN = "67";
        //订金
        public static final String DOWN_PAYMENT = "71";
        //携程
        public static final String CTRIP = "77";
        // 押金
        public static final String CASH_PLEDGE = "95";
        // 聚合支付
        public static final String AGGREGATE_PAY = "100";
        // 通联
        public static final String ALLINPAY = "150";
        //期间卡订场
        public static final String PERIOD_CARD = "periodCard";
        //计次卡订场
        public static final String TIMES_CARD = "timesCard";
        public static final String FOREGIFT = "foregift"; // 押金抵扣
        // 折扣券
        public static final String DISCOUNT_COUPON = "164";
        // 平台券
        public static final String PLATFROM_COUPON = "170";
        // 园秀通
        public static final String YUAN_XIU_PAY = "191";
        // 银联商务
        public static final String UNION_PAY = "198";
        // 银联商务-微信
        public static final String UNION_PAY_WECHAT = "199";
        // 银联商务-支付宝
        public static final String UNION_PAY_ALIPAY = "200";
        //建行二维码支付
        public static final String CCB_PAY_QR = "319";
        //建行微信支付(聚合支付时微信支付也使用该code)
        public static final String CCB_WECHAT_PAY = "320";
        // 建行聚合支付
        public static final String CCB_PAY = "334";
        // 建行聚合支付-支付宝支付
        public static final String CCB_ALI_PAY = "335";
        // 国信通积分
        public static final String GXT_POINTS = "400";
        // 云闪付
        public static final String YUN_SHAN_FU = "401";

        // 次卡扣次
        public static final String TIME_DEDUCTION = "500";
        // 微信非税支付
        public static final String WECHAT_NON_TAX = "506";
        // 校园卡
        public static final String SCHOOL_CARD = "507";

        // 国信通支付
        public static final String GXT = "402";

        // 建行数字货币支付
        public static final String CCB_PANDA = "508";
        //招行微信支付
        public static final String CMB_WECHAT_PAY = "621";
        //招行扫付款码码支付
        public static final String CMB_SWEPT_PAY = "621";
        //招行商户收款码支付
        public static final String CMB_SCAN_PAY = "621";
        //招行支付
        public static final String CMB_PAY = "621";
        // 江苏聚合支付
        public static final String SU_PAY = "623";
        // 常熟农商银行
        public static final String CHANG_SHU_BANK = "624";
        // 常熟积分支付（目前支持买次票）
        public static final String CHANG_SHU_POINT_PAY = "625";

        public static final String CASH_ALLIANCE = "cashAlliance";
        public static final String SWIFT_PASS = "678";
        public static final String GROUP_DEPOSIT = "686";

        public static final String ABC = "679";

        public static final String INTER_TRANSFER = "777";

    }

    /**
     * 优惠券种类
     */
    public static class CouponCat {
        //通用券
        public static final String GENERAL = "00";
        //票优惠券
        public static final String TIME_TICKET = "01";
        //场地优惠券
        public static final String FIELD_TICKET = "02";
        //卡优惠券
        public static final String CARD = "03";
        //租柜优惠券
        public static final String CABINAT = "04";
        //商品优惠券
        public static final String GOODS = "05";
    }

    /**
     * 优惠券类型
     */
    public static class CouponValueType {
        //代金券
        public static final String MONEY = "1";
        //体验券
        public static final String EXPERIENCE = "2";
        //兑换券
        public static final String EXCHANGE = "3";
        //停车券
        public static final String PARK = "4";
        //折扣券
        public static final String DISCOUNT = "5";
        //券包
        public static final String PACKAGE = "6";
        //体彩券
        public static final String SPORTS = "7";
    }

    /**
     * 上课-是否私教
     */
    public static class PrivateTag {
        // 否
        public static final String NOT = "0";
        // 是
        public static final String YES = "1";
    }

    /**
     * 上课-状态
     */
    public static class ClassTag {
        // 未上课
        public static final String NOT = "0";
        // 上课
        public static final String YES = "1";
        // 请假
        public static final String LEAVE = "2";
    }

    public static class RechargePresentType {
        public static final String MONEY = "1";
        public static final String PERCENT = "2";
    }

    /**
     * common_sale_campaign_item赠送类型
     */
    public static class CampaignPresentType {
        public static final String COUPON = "1"; //优惠券
        public static final String DISCOUNT = "2"; //折扣
    }

    /**
     * cust_access 表中的接入方式
     */
    public static class AccessType {
        public static final String ECARD = "0"; //卡号
        public static final String PHONE = "1"; //手机号
        public static final String EMAIL = "2"; //邮箱
        public static final String AD_ACCOUNT = "3"; // Ad帐号
    }

    /**
     * 新增校外导师和校外访客角色
     */
    public static class NetUserRole {
        public static final String TUTOR = "tutor"; //校外导师
        public static final String VISITOR = "visitor"; //校外访客
    }

    /**
     * 可操作权限
     */
    public static class OperateCode {
        // 返销
        public static final int CANCEL_TRADE = 1;
        // 修改订单
        public static final int UPDATE_TRADE = 2;
        // 补打发票
        public static final int PRINT_TRADE = 3;
        // 补签协议
        public static final int SIGN_AGREEMENT = 4;
        // 修改支付方式
        public static final int CHANGE_PAYMODE = 5;
        // 修改团购方式
        public static final int CHANGE_GROUP_MODE = 6;
        // 修改客户来源
        public static final int CHANGE_SOURCE_TYPE = 7;
        //下载办卡信息
        public static final int DOWNLOAD_BATCH_CARDS = 8;
        //修改订单的备注信息
        public static final int CHANGE_ORDER_REMARK = 9;
    }

    /**
     * 外屏日志类型
     */
    public static class ExternalDisplayLog {
        // 绑定
        public static final String BIND = "1";
        // 解绑
        public static final String UNBIND = "2";
        // 上线
        public static final String ONLINE = "3";
        // 下线
        public static final String OFFLINE = "4";
        public static final String WEB_LOG = "5";
    }

    /**
     * 场地是否支持全场
     */
    public static class FullTag {
        // 全场
        public static final String WHOLE_FIELD = "1";
        // 非全场
        public static final String NOT_WHOLE_FIELD = "0";
    }

    /**
     * 房间是否私密
     */
    public static class ChamberPrivateTag {
        //私密房间
        public static final String PRIVATE = "1";
        //非私密房间
        public static final String NOT_PRIVATE = "0";
    }

    /**
     * 约球支付方式
     */
    public static class ChamberPayType {
        //线上
        public static final String ONLINE = "1";
        //线下
        public static final String OFFLINE = "2";
    }

    /**
     * 约球属性
     */
    public static class ChamberOwnerTag {
        //我发起的
        public static final String INITIATE = "1";
        //我参与的
        public static final String PARTICIPATE = "0";
    }

    /**
     * 约球状态
     */
    public static class ChamberState {
        //已成团
        public static final String HAS_TEAMED = "1";
        //未成团
        public static final String NOT_TEAMED = "2";
        //已完成
        public static final String FINISHED = "3";
        //已取消
        public static final String CANCELED = "4";
    }

    /**
     * 冻结状态
     */
    public static class FreezeTag {
        public static final String UNFROZEN = "0";
        public static final String FROZEN = "1";
    }

    public static class CourseFreeTag {
        // 正常售卖 （实际表中没有这个值）
        public static final String NORMAL = "0";
        // 赠送
        public static final String FREE = "1";
        // 既能赠送也能售卖
        public static final String BOTH = "2";
    }

    /*
     * 营销活动限时优惠类型
     * 1-优惠价 2-赠送次 3-赠送金额 4-赠送天 5-赠送月 6-赠送年
     */
    public static class DiscountType {
        public static final String PRICE = "1";
        public static final String GIFTTIME = "2";
        public static final String GIFTMONEY = "3";
        public static final String GIFTDAY = "4";
        public static final String GIFTMONTH = "5";
        public static final String GIFTYEAR = "6";
    }

    /**
     * 限时优惠中的价格类型
     */
    public static class DiscountValueType{
        // 优惠价 老数据的空也是这个
        public static final String CASH = "1";
        // 折扣
        public static final String DISCOUNT = "2";
    }


    /**
     * 批量开卡状态
     */
    public static class BatchDoCardState {
        //批量未完工状态
        public static final int UNCOMPLETED = 0;
        //批量完工状态
        public static final int COMPLETED = 1;
        //失败状态
        public static final int FAILED = 2;
    }

    public static class ProdTag {
        //资源卡类型
        public static final String RESOURCE = "1";
        //专项卡
        public static final String CARD = "2";
    }

    /**
     * 普通状态 0-无效 1-有效（一般的状态都使用这个参数定义）
     */
    public static class Status {
        // 无效
        public static final String INVALID = "0";
        // 有效
        public static final String VALID = "1";
    }

    public static class VideoStatus {
        // 未发布
        public static final String INVALID = "0";
        // 已发布-有效
        public static final String VALID = "1";
        //已删除
        public static final String DELETED = "2";
    }

    /**
     * 预约单状态
     */
    public static class BmSiteAppointState {
        // 已预约
        public static final String APPOINTED = "1";
        // 检测中
        public static final String CHECKING = "2";
        //已检测
        public static final String CHECKED = "3";
        //已取消
        public static final String CANCELED = "4";
        //已过期
        public static final String EXPIRED = "5";
    }

    /**
     * 一卡通状态编码
     */
    public static class EmptyCardState {
        //空闲
        public static final String FREE = "0";
        //预占
        public static final String OCCUPIED = "1";
        //实占
        public static final String USED = "2";
    }

    public static class CabinetKeyState {
        //未领取
        public static final String UNRECEIVED = "0";
        //已领取
        public static final String RECEIVED = "1";
        //己损坏
        public static final String DESTORYED = "2";
        //己归还
        public static final String RETURNED = "3";
    }

    /**
     * 柜子租赁流水状态
     */
    public static class CabinetRentState {
        //未归还
        public static final String NOT_RETURNED = "0";
        //已归还
        public static final String RETURNED = "1";
        //审核中
        public static final String AUDIT = "2";
        // 冻柜审核中
        public static final String FREEZE_CABINET_AUDIT = "3";
        // 冻柜中
        public static final String FREEZE_CABINET = "4";
    }

    /**
     * 柜子使用状态
     */
    public static class LockerUseState {
        //空闲
        public static final String IDLE = "0";
        //占用
        public static final String OCCUPY = "1";
    }

    /**
     * 柜子租赁状态
     */
    public static class LockerRentState {
        //使用中
        public static final String USING = "1";
        //已结束
        public static final String END = "2";
    }

    public static class GoodsRentState {
        //未领取
        public static final String UNRECEIVED = "0";
        //已领取
        public static final String RECEIVED = "1";
        //己归还未结算
        public static final String RETURNED_NO_BALANCED = "2";
        //己结算
        public static final String BALANCED = "3";
        //已取消
        public static final String CANCELED = "4";
    }

    /*
     * 企业客户协议类型
     */
    public static class EntCustAgreementType {
        // 挂账协议
        public static final String ON_CREDIT = "1";
    }

    /**
     * 活动取消标志
     */
    public static class CampaignStatus {
        //未支付
        public static final String UNPAID = "0";
        //正常
        public static final String NORMAL = "1";
        //取消
        public static final String CANCEL = "2";
        //已签到
        public static final String SIGNED = "3";
    }

    /**
     * 领取装备状态
     */
    public static class CampaignEquipment {
        //未领取
        public static final String NO_GET = "0";
        //已领取
        public static final String GET = "1";
    }

    /**
     * 生成号码牌状态
     */
    public static class CampaignPlayerNoState {
        //不生成
        public static final String NO_GENERATE = "0";
        //生成
        public static final String GENERATE = "1";
    }

    public static class CampPlayerState {
        //无效
        public static final String INVALID = "0";
        //正常
        public static final String NORMAL = "1";
        //待队员确认
        public static final String CONFIRMING = "2";
        //队员申请中
        public static final String APPLYING = "3";
    }

    /**
     * 企业成员类型
     */
    public static class MemberType {
        //普通成员
        public static final String MEMBER = "0";
        //联系人
        public static final String CONTACTS = "1";
    }

    /**
     * 发布状态
     */
    public static class ReleaseStatus {
        // 未发布
        public static final String UN_RELEASE = "0";
        // 已发布
        public static final String RELEASE = "1";
        // 删除状态(不可用)
        public static final String DELETE = "2";
    }

    /**
     * 公开课课表的状态
     */
    public static class OpenCourseTableStatus {
        //未发布
        public static final String UNPUBLISHED = "0";
        //已发布
        public static final String PUBLISHED = "1";
        //已取消
        public static final String CANCELLED = "2";
    }

    /**
     * 已经发布的课程的状态
     */
    public static class OpenCourseInstanceStatus {
        //未上课
        public static final String NO_CLASS = "0";
        //已上课
        public static final String HAS_CLASS = "1";
        //已暂停
        public static final String PAUSED = "2";
    }

    /**
     * 调班换课的类型
     */
    public static class CourseChangeType {
        //更换上课时间
        public static final String TIME = "0";
        //更换班级
        public static final String CHANGE_CLASS = "1";
        //更换课时
        public static final String CHANGE_LESSON = "2";
        //课程延期
        public static final String COURSE_DELAY = "3";
        //销假
        public static final String CANCEL_LEAVE = "4";

    }

    /**
     * 审核类型
     */
    public static class AuditType {
        public static final String NORMAL = "1"; // 普通
        public static final String MANUAL_REDUCTION = "2"; // 支付减免
        public static final String ON_CREDIT = "3"; // 挂账
        public static final String PREMIUM = "4"; // 溢价
        public static final String PREMIUM_AMOUNT = "5"; // 普通-金额
    }

    /**
     * 优惠券活动类型
     */
    public static class CampaignType {
        //优惠券发放
        public static final String NATIONAL_FITNESS = "1";
        //购票优惠
        public static final String TICKET_SALE_CAMP = "2";
        //抽奖送券
        public static final String LOTTERY_COUPON = "3";
        //场地优惠
        public static final String FIELD_SALE_CAMP = "4";
        //充值优惠
        public static final String COMMON_SALE_CAMP = "5";
        //注册优惠券
        public static final String REGISTER_GRANT_COUPON_CAMP = "6";
        //蓄力活动
        public static final String ACCUMUL_CMAP = "7";
        //抢购活动
        public static final String SECKILL_CMAP = "8";
    }

    /**
     * 优惠券类型
     */
    public static class CouponType {
        //电子
        public static final String ELECTRIC = "1";
        //实体
        public static final String ENTITY = "2";
        //第三方券
        public static final String THIRD = "3";
    }

    /**
     * 优惠券活动有效状态
     */
    public static class SaleCampStatus {
        //有效
        public static final String VALID = "1";
        //无效
        public static final String INVALID = "0";
    }

    /**
     * 优惠券属性编码
     */
    public static class CouponAttrCode {
        // 协议id
        public static final String AGREEMENT_ID = "agreement_id";
        // 是否只有新用户可领
        public static final String NEW_CUST_ONLY = "new_cust_only";
        // 是否固定面值 1-固定面值, 0-非固定面值
        public static final String FIXED_VALUE_TAG = "fixed_value_tag";
        // 微信上是否可以使用, 1-可以使用, 0或不配-不可以
        public static final String WECHAT_USE_TAG = "wechat_use_tag";
        //是否用于大转盘抽奖
        public static final String WHEEL_LOTTERY_TAG = "wheel_lottery_tag";
        // 表示代金券使用限制，limit_tag = 1 可以和其他代金券一起支付，limit_tag = 0 不可以和其他代金券一起使用
        public static final String LIMIT_TAG = "limit_tag";
        public static final String LIMIT_USE_TIMES = "limit_use_times";//叠加使用次数限制
        // 身份证区域限制，格式{psptIdFormat:1307,description:张家口市民}
        public static final String PSPTID_AREA_LIMIT = "psptid_area_limit";
        // 外部券号
        public static final String OUTER_COUPON_ID = "outer_coupon_id";
        // 是否积分兑换
        public static final String POINTS_EXCHANGE_TAG = "points_exchange_tag";
        // 是否配置随机金额
        public static final String RANDOM_MONEY_TAG = "random_money_tag";
        // 随机金额-最小金额
        public static final String RANDOM_MONEY_MIN = "random_money_min";
        // 随机金额-最大金额
        public static final String RANDOM_MONEY_MAX = "random_money_max";
        //领取优惠券后多少天可以使用
        public static final String USE_DATE_LIMIT = "use_date_limit";
        public static final String COUPON_VALUE = "coupon_value"; // 券价值
        //券使用规则简介
        public static final String USE_RULE_DESC = "use_rule_desc";

        // 将trade_pay_log表中的支付方式记录为减免 1-是 0和没有记录是否
        public static final String RECORD_AS_MANUAL_REDUCTION = "record_as_manual_reduction";
    }

    /**
     * 优惠券属性编码值
     */
    public static class CustOnlyTag {
        // 只有新用户可领
        public static final String RECEIVE_ONLY = "1";
        // 不只有新用户可领
        public static final String NOT_RECEIVE_ONLY = "0";
    }

    /**
     * 优惠券是否微信可用
     */
    public static class WechatUseTag {
        // 可以使用
        public static final String CAN_USE = "1";
        // 不只有新用户可领
        public static final String CAN_NOT_USE = "0";
    }

    /**
     * add by jiaoyan 2016-03-21
     * 48时间制，场地使用状态
     */
    public static class FieldSegmentState {
        //未占用
        public static final String FREE = "0";
        //预占
        public static final String BOOKED = "1";
        //占用
        public static final String OCCUPY = "2";
        //活动占场已确认到场
        public static final String CHECKED_IN = "3";
    }

    /**
     * 关于活动占场冲突的处理结果标志
     */
    public static class HandleResultTag {
        //全部退款
        public static final String ALL_RETURN = "1";
        //保留不冲突部分
        public static final String REMAIN_LAVE = "2";
    }

    /**
     * 支付宝返回结果码
     */
    public static class AlipayResponseCode {
        //成功
        public static final String SUCCESS = "10000";
        //失败
        public static final String FAIL = "40004";
        //正在处理中
        public static final String PROCESSING = "10003";
        //未知错误
        public static final String UNKNOW_ERROR = "20000";
    }

    /**
     * 支付宝查询订单状态
     */
    public static class AlipayResponseTradeStatus {
        public static final String TRADE_SUCCESS = "TRADE_SUCCESS";
        public static final String WAIT_BUYER_PAY = "WAIT_BUYER_PAY";
    }

    /**
     * 支付宝退款状态
     */
    public static class AlipayRefundStatus {
        public static final String REFUND_SUCCESS = "REFUND_SUCCESS";
    }

    /**
     * 退课枚举
     */
    public static class RefundCourseParam {
        //报名id
        public static final String ENROLL_ID = "enroll_id";
        //手续费
        public static final String CHARGE_FEE = "charge_fee";
        //是否私教标识
        public static final String PRIVATE_TAG = "private_tag";
        //退课课程名称
        public static final String COURSE_NAME = "course_name";
        //学员名称
        public static final String STU_NAME = "stu_name";
        //学员id
        public static final String STU_ID = "stu_id";
        //学员电话
        public static final String PHONE = "phone";
    }

    /**
     * 赠课枚举
     */
    public static class HandselLessonParam {
        //赠课类型（1，私教；2，长训班）
        public static final String TRAINING_COURSE_TYPE = "training_course_type";
        //报名id
        public static final String ENROLL_ID = "enroll_id";
        //学员id
        public static final String STU_ID = "stu_id";
        //赠送原因
        public static final String REASON_CODE = "reason_code";
        //赠送课时
        public static final String PRESENT_NUM = "present_num";
        //介绍人电话
        public static final String PHONE = "phone";
    }

    /**
     * 主次标记
     */
    public static class PrimaryTag {
        public static final String PRIMARY = "1";
        public static final String SECONDARY = "0";
    }

    /**
     * 操作标志
     */
    public static class ActionFlag {
        public static final String ADD = "add";
        public static final String DEL = "del";
        public static final String UPDATE = "update";
    }

    /**
     * Grade会员状态
     */
    public static class GradeState {
        public static final String VALID = "1";
        public static final String INVALID = "0";
    }

    /**
     * 工作日或休息日的标志
     */
    public static class DayoffTag {
        //工作日
        public static final String WORKDAY = "0";
        //休息日
        public static final String HOLIDAYORWEEKDAY = "1";
    }

    /**
     * 课程扩展属性
     */
    public static class TrainingCourseAttr {
        //签到扣次
        public static final String SIGN_REDUCE = "sign_reduce";

        //选择上课时间choose_class_time
        public static final String CHOOSE_CLASS_TIME = "choose_class_time";

        //选择上课时间设置的课时数
        public static final String WEEK_LESSON_NUM = "week_lesson_num";

        //课程适用对象
        public static final String SUIT_PSERSON = "suit_person";

        //课程图片
        public static final String COURSE_PIC_URL = "course_pic_url";

        //长训班微信端开始时间的可选天数
        public static final String WECHAT_TRAINING_CHOOSE_DAYS = "wechat_training_choose_days";

        //课程原始价格
        public static final String ORIGINAL_PRICE = "original_price";

        //私教入馆次数限制
        public static final String ENTER_HALL_NUM = "enter_hall_num";

        //私教是否赠课
        public static final String FREE_TAG = "free_tag";

        //是否选择上课地点
        public static final String CHOOSE_CLASS_PLACE = "choose_class_place";

        //是否预约
        public static final String APPOINTMENT_TAG = "appointment_tag";

        //上课地点
        public static final String CLASS_PLACE = "class_place";

        //课程可预约天数
        public static final String COURSE_RESV_DAYS = "course_resv_days";

        //是否支持闸机入馆
        public static final String AUTO_GATE_TAG = "auto_gate_tag";

        //是否需要选择教练
        public static final String ENROLL_COACH_TAG = "enroll_coach_tag";

        //培训入馆是否先刷教练卡
        public static final String AUTO_GATE_COACH_TAG = "auto_gate_coach_tag";

        //课时费
        public static final String CLASS_FEE = "class_fee";

        //购买数量范围
        public static final String PURCHASE_NUM_LIMIT = "purchase_num_limit";

        //请假规则
        public static final String LEAVE_RULE = "leave_rule";

        //科目多价格
        public static final String SUBJECT_PRICE_TAG = "subject_price_tag";

        //课程需要校验健康承诺书
        public static final String CHECK_HEALTH_AGREEMENT_TAG = "check_health_agreement_tag";

        //私教一对多支持的人数
        public static final String COURSE_SUPPORT_PERSON_NUM = "course_support_person_num";

        //评价总数
        public static final String COMMENT_COUNT = "comment_count";

        //评价总分
        public static final String COMMENT_TOTAL_SCORE = "comment_total_score";

        //课程隐藏标识
        public static final String HIDE_TAG = "hide_tag";

        //分享海报二维码
        public static final String WX_QR_CODE = "wx_qr_code";

        //报名时间限制,在报名日期的基础上限制每天几点到几点能报名
        public static final String ENROLL_START_TIME = "enroll_start_time";
        public static final String ENROLL_END_TIME = "enroll_end_time";


        public static final String SELF_SUPPORT = "self_support";//是否自营 0-非自营 1-自营
        // 国信人力资源管理-培训课-课时费
        public static final String GX_LESSON_FEE = "gx_lesson_fee";//国信培训课课时费

        public static final String ACTIVATE_DAYS = "activate_days";//自动激活时间,单位：天
        // 课程多扣次入馆开关
        public static final String CHOOSE_NUM_TAG = "choose_num_tag";
        // 停车优惠
        public static final String PARKING_DISCOUNT_TAG = "parking_discount_tag";
        public static final String GIFT_SAME_LEVEL_LESSON = "gift_same_level_lesson";
        public static final String IS_APTITUDE_AUDIT = "is_aptitude_audit";//课程报名资质审核
        public static final String COURSE_ENROLL_AUDIT_TAG = "course_enroll_audit_tag";//是否开启课程报名审核 1-是 0- 否
        public static final String COURSE_ENROLL_AUDIT_STAFF_ID = "course_enroll_audit_staff_id";
        public static final String LEAVE_PROCESS_Id = "process_id"; //在课程中配置的审核流信息
        public static final String PROCESS_ID = "process_id"; // 家校通审核流

    }

    public static class ResvType {
        //上课预约
        public static final String RESV_TYPE_1 = "1";
        //体验预约
        public static final String RESV_TYPE_2 = "2";
    }

    /**
     * 签到类型
     */
    public static class AttendLogType {
        //签到
        public static final String SIGN = "1";
        //取消签到
        public static final String UN_SIGN = "2";
        //请假
        public static final String LEAVE = "3";
        //取消请假
        public static final String UN_LEAVE = "4";
    }

    /**
     * 物业费用变化标志
     */
    public static class PropertyPriceChangeTag {
        public static final String NO = "0";    //没变化
        public static final String PRICECHANGE = "1";   //租金变了
        public static final String PROPERTYCOSTSCHANGE = "2";   //物业费变了
        public static final String BOTHCHANGE = "3";    //租金和物业费都变了
    }


    public static class CouponUseTag {
        public static final String MULTI_TIMES = "1"; // 多次使用
        public static final String ONCE = "0"; // 使用一次
    }

    /**
     * 场馆活动管理的活动属性
     */
    public static class VenueCampAttr {
        public static final String CAR_NUM_INFO = "car_num_info";//关于车辆信息的属性
        public static final String PERSON_NUM_INFO = "person_num_info";//关于人信息的属性
        public static final String REMARK = "remark";//关于活动的备注信息
        public static final String UNDERTAKE_ENTERPRISE_ID = "undertake_enterprise_id";//承办企业id
        public static final String UNDERTAKE_CONTACT_PHONE = "undertake_contact_phone";//承办人联系电话
        public static final String UNDERTAKE_CONTACT_MAN = "undertake_contact_man";//承办人
        public static final String ACTIVITIES = "activities";//活动项目
        public static final String FIELD_TYPE = "field_type";//场地类型
        public static final String INVOICE_TITLE = "invoice_title";//发票抬头
        public static final String INVOICE_MONEY = "invoice_money";//发票金额
        public static final String INVOICE_NO = "invoice_no";//发票号码
        public static final String INVOICE_TIME = "invoice_time";//开票时间
        public static final String INVOICE_REMARK = "invoice_remark";//开票备注
        public static final String CONTRACT_CODE = "contract_code";//合同编码
    }

    /**
     * 合同任务类型
     */
    public static class TaskType {
        public static final String REVIEW = "1";//评审
        public static final String NOTICE_CAMP_SAFE = "3";//通知活动保障
        public static final String FIELD_ACCEPTANCE = "4";//场地验收
        public static final String CUSTOMER_REVIEW = "5";//场馆活动客户回访
        public static final String RECEDE_RENT = "6";  //退租--后改为物品状态
        public static final String RENT_CUSTOMER_REVIEW = "7";//物业租赁客户回访

    }

    /**
     * 表单说明
     */
    public static class FormType {
        public static final String CAMP_REVIEW = "1";//活动评审
        public static final String NOTICE_CAMP_SAFE = "3";//通知活动保障
        public static final String FIELD_ACCEPTANCE = "4";//场地验收
        public static final String CUSTOMER_REVIEW = "5";//场馆活动客户回访
        public static final String RECEDE_RENT = "6";
        public static final String RENT_CUSTOMER_REVIEW = "7";//物业租赁客户回访
    }

    /**
     * 关联类型
     */
    public static class RelatedType {
        public static final String CAMPAIGN = "1"; //活动
        public static final String RENT = "2";//租赁
        public static final String CAMPAIGN_AUDIT = "3"; //活动审核
        public static final String RENT_AUDIT = "4";//租赁审核
        public static final String RECEDE_RENT_AUDIT = "5";//退租审核

    }

    /**
     * 停车券关联类型
     */
    public static class CarParkingCouponRelatedType {
        public static final String TICKET = "1"; //票Id
        public static final String CLASS_ATTEND_LOG = "2";//上课记录Id
    }

    /**
     * 场馆活动的收费类型
     */
    public static class CampFeeType {
        public static final String DEPOSIT = "1";//押金
        public static final String PAYMENT_COLLECTION = "2";//回款
    }

    /**
     * 物业租赁的费用类型
     */
    public static class RentFeeType {
        public static final String DEPOSIT = "1";//押金
        public static final String RENT = "2";//租金
        public static final String PROPERTY = "3";//物业费
    }

    /**
     * 费用定义表中的费用类型
     */
    public static class FeeItem {
        public static final long CAMPAIGN_FEE = 10000010L;//活动费用
        public static final long RENTAL_FEE = 10000012L;//租赁费用
        public static final long PROPERTY_FEE = 10000013L;//物业费
        public static final long WATER_FEE = 10000014;//物业水费
        public static final long ELE_FEE = 10000015;//物业电费
        public static final long COMPENSATION = 10000016;//赔偿收费(如租赁损坏等)
        public static final long DEPOSIT_FEE = 10000017;//押金收费
        public static final long GAS_FEE = 10000018L;//物业燃气费
        public static final long CLEANING_FEE = 10000019L;//物业保洁费
        public static final long AIR_CONDITIONER_FEE = 10000020L;//物业空调制冷费
        public static final long HEATING_FEE = 10000021L;//物业采暖费
        public static final long RENOVATION_FEE = 10000022L;//装修押金
        public static final long WATER_ELECTRICITY_FEE = 10000023L;//水电费押金
        public static final long WATER_AND_ELE_LATE_FEE = 10000024L;//水电滞纳金
        public static final long RENTAL_LATE_FEE = 10000025L;//租金滞纳金
    }

    /**
     * SQL标签
     */
    public static class SqlLabel {
        // 产业中心下中心的数量
        public static final String SINGLE_VALUE_CENTER_NUM_FOR_MASTER = "single_value_center_num_for_master";
        // 中心会员数量
        public static final String SINGLE_VALUE_MEMBER_NUM_FOR_CENTER = "single_value_member_num_for_center";
        // 中心的年资金
        public static final String SINGLE_VALUE_YEAR_TOTAL_CASH_FOR_CENTER = "single_value_year_total_cash_for_center";
        // 中心的年收入
        public static final String SINGLE_VALUE_YEAR_TOTAL_INCOME_FOR_CENTER = "single_value_year_total_income_for_center";
        // 中心的年资金按月分布图
        public static final String YEAR_CASH_RANK_FOR_CENTER = "year_cash_rank_for_center";
        // 中心的年收入按月分布图
        public static final String YEAR_INCOME_RANK_FOR_CENTER = "year_income_rank_for_center";
        // 中心的日入馆数据统计
        public static final String TODAY_TOTAL_EXERCISE_FOR_CENTER = "today_total_exercise_for_center";
        // 多个中心的月资金集合数据
        public static final String MONTH_CASH_RANK_DAY_FOR_CENTER_LIST = "month_cash_rank_day_for_center_list";
        // 多个场馆的月资金集合数据
        public static final String MONTH_CASH_RANK_DAY_FOR_VENUE_LIST = "month_cash_rank_day_for_venue_list";
        // 多个中心的月收入集合数据
        public static final String MONTH_INCOME_RANK_DAY_FOR_CENTER_LIST = "month_income_rank_day_for_center_list";
        // 多个场馆的月收入集合数据
        public static final String MONTH_INCOME_RANK_DAY_FOR_VENUE_LIST = "month_income_rank_day_for_venue_list";
        // 多个中心的月入馆人次统计(按天排序)
        public static final String MONTH_EXERCISE_RANK_DAY_FOR_CENTER_LIST = "month_exercise_rank_day_for_center_list";
        // 多个场馆的月入馆人次统计(按天排序)
        public static final String MONTH_EXERCISE_RANK_DAY_FOR_VENUE_LIST = "month_exercise_rank_day_for_venue_list";
        // 获取管理中心月数据(具体是什么数据,依传入参数而定)
        public static final String MONTH_RANK_FOR_MASTER = "month_rank_for_master";
        // 获取管理中心日数据(具体是什么数据,依传入参数而定)
        public static final String DAY_RANK_FOR_MASTER = "day_rank_for_master";
        // 多个中心的天入馆人次统计(按小时排序)
        public static final String TODAY_EXERCISE_RANK_HOUR_FOR_CENTER_LIST = "today_exercise_rank_hour_for_center_list";
        // 多个场馆的天入馆人次统计(按小时排序)
        public static final String TODAY_EXERCISE_RANK_HOUR_FOR_VENUE_LIST = "today_exercise_rank_hour_for_venue_list";
        // 获得产业中心总览数据
        public static final String OVER_DATA_FOR_MASTER = "overview_data_for_master";
        // 获得某个中心的总览数据
        public static final String OVER_DATA_FOR_CENTER = "overview_data_for_center";
        // 获得该中心所有场馆的所有服务的繁忙度统计
        public static final String CUR_BUSY_DEGREE_FOR_CENTER = "cur_busy_degree_for_center";
        // 获取中心的今日资金
        public static final String TODAY_CASH_FOR_CENTER = "today_cash_for_center";
        // 获取中心的今日收入
        public static final String TODAY_INCOME_FOR_CENTER = "today_income_for_center";
        //中心员工工作台现金流月分布
        public static final String DASHBOARD_CENTER_MONTH_DISPLAY_CASH = "dashboard_center_month_display_cash";
        //中心员工工作台现金流日数据按业务分布
        public static final String DASHBOARD_CENTER_DAY_TRADE_CASH = "dashboard_center_day_trade_cash";
        //中心员工工作台收入月分布
        public static final String DASHBOARD_CENTER_MONTH_DISPLAY_INCOME = "dashboard_center_month_display_income";
        //中心员工工作台收入日数据按业务分布
        public static final String DASHBOARD_CENTER_DAY_TRADE_INCOME = "dashboard_center_day_trade_income";

    }

    /**
     * 中心类型
     */
    public static class CenterType {
        // 场馆中心
        public static final String CENTER = "1";
        // 产业中心
        public static final String MASTER_CENTER = "2";
    }

    /**
     * 微信返回的交易状态
     */
    public static class TradeState {
        //成功
        public static final String SUCCESS = "SUCCESS";
        //失败
        public static final String FAIL = "FAIL";
    }

    /**
     * 微信支付下单，腾讯平台订单生效时间，第一版为1440，单位是分钟
     */
    public static final int WECHAT_PAY_EFFECTIVE_MINUTE = 1440;
    /**
     * 微信扫码支付下单，腾讯平台订单生效时间，先设置成15分钟
     */
    public static final int WECHAT_QR_CODE_PAY_EFFECTIVE_MINUTE = 15;

    /**
     * 自定义费用收取移除状态
     */
    public static class CustomFeeitemRemoveTag {
        //正常使用
        public static final String NORMAL = "0";
        //已经移除
        public static final String REMOVED = "1";
    }

    /**
     * 自定义费用收取移除状态
     */
    public static class CustomFeeitemLevel {
        //类别
        public static final int PARENT_TYPE = 1;
        //收费项目
        public static final int CHILD_ITEM = 2;
    }

    /**
     * 提醒配置状态
     */
    public static class CustReminderStatus {
        //删除
        public static final String DELETE = "0";
        //正常
        public static final String NORMAL = "1";
        //禁用
        public static final String UNUSE = "2";
    }

    /**
     * 提醒类型
     */
    public static class ReminderType {
        //生日提醒
        public static final String BIRTHDAY = "01";
        //租柜到期提醒
        public static final String CABINET = "02";
        //协议占场
        public static final String FIELD_RESERVE = "03";
        //会员到期提醒
        public static final String CUST_OVER_TIME = "04";
        //续课通知
        public static final String CLASS_CONTINUE_NOTICE = "05";
        //计次卡余额不足提醒
        public static final String TIMES_CARD_REMIND = "06";
        //积分变动提醒
        public static final String POINTS_CHANGE = "07";
        //积分清零提醒
        public static final String POINTS_CLEAR = "08";
    }

    /**
     * 提醒内容关键字
     */
    public static class ReminderContent {
        public static final String SMS_SIGN = "sms_sign";
        public static final String SMS_CONTENT = "sms_content";
        public static final String SERVICES = "services";
        public static final String TIME_UNIT = "time_unit";
        public static final String TIME_AMOUNT = "time_amount";
        public static final String CABINET_AREA = "cabinet_area";
        public static final String THRESHOLD = "threshold";
        public static final String REMIND_NUM = "remind_num";
        public static final String LIMIT_TYPE = "limit_type";
        public static final String TIME_MINUTE = "time_minute";
    }

    /**
     * 产品属性
     */
    public static class ProductAttr {
        // 产品过期后是否可用，1:可用 0:不可用，余额专项卡属性
        public static final String USABLE_AFTER_EXPIRATION = "usable_after_expiration";
        // 产品是否支持自定义开始时间
        public static final String ALLOW_SELF_DEF_START = "allow_self_def_start";
        // 产品使用周“维度”
        public static final String USE_WEEKDAYS = "use_weekdays";
        // 产品微信售卖时的产品详情
        public static final String WECHAT_PRODUCT_DETAILS = "wechat_product_details";
        // 产品微信售卖时的使用说明
        public static final String WECHAT_USE_DETAILS = "wechat_use_details";
        // 产品超时时间
        public static final String PRODUCT_TIMEOUT = "product_timeout";
        // 产品超时金额 元/单位
        public static final String PRODUCT_TIMEOUT_MONEY = "product_timeout_money";
        // 产品超时单位
        public static final String PRODUCT_TIMEOUT_UNIT = "product_timeout_unit";
        public static final String AUTO_GATE_TAG = "auto_gate_tag";
        // 分时段的入馆次数规则，json格式 [{"startTime":"0800", "endTime":"1130", "times": 1}]
        public static final String ENTER_TIMES_RULE = "enter_times_rule";
        // 排序，数字越小越靠前
        public static final String SORT = "sort";
        // 使用说明
        public static final String INSTRUCTIONS = "instructions";
        // 产品详情
        public static final String PRODUCT_INFO = "product_info";
        // 自助取票标识
        public static final String ATM_FETCH_TICKET_TAG = "atm_fetch_ticket_tag";
        //每日购票次数限制
        public static final String BUY_TICKET_TIMES = "buy_ticket_times";
        //支持次票销售规则
        public static final String TIME_TICKET_SALE_RULE = "time_ticket_sale_rule";
        //支持次票销售规则标识
        public static final String TIME_TICKET_SALE_RULE_TAG = "time_ticket_sale_rule_tag";
        // 商品可销售的总数
        public static final String PRODUCTS_SALE_SUM = "products_sale_sum";
        // 是否取手环
        public static final String FETCH_BRACELET_TAG = "fetch_bracelet_tag";
        // 期间卡、计次卡超时限高
        public static final String PRODUCT_TIMEOUT_MONEY_LIMIT = "product_timeout_money_limit";//期间卡、计次卡超时限高费用
        //专项卡赠送卡营销费用
        public static final String COST_PRICE = "cost_price";//营销费用
        // 是否支持约球
        public static final String PLAYAA_TAG = "playAA_tag";
        // 产品配置的图片
        public static final String PRODUCT_PICTURE = "product_picture";
        // 是否支持分享
        public static final String SHARE_DEPOSIT_TAG = "share_deposit_tag";

        public static final String REDUCE_DAYS = "reduce_days";

        // 产品是否预约标志
        public static final String RESERVATION_TAG = "reservation_tag";
        // ----- 五台山团体卡
        // 套餐
        public static final String GROUP_CARD_FIXED_TAG = "group_card_fixed_tag";
        // 自定义
        public static final String GROUP_CARD_CUSTOM_TAG = "group_card_custom_tag";
        // 自定义套餐最低次数限制
        public static final String CUSTOM_MIN_LIMIT_TAG = "custom_min_limit_tag";
        // 自定义套餐最低次数
        public static final String CUSTOM_MIN_LIMIT = "custom_min_limit";
        // 是否刷身份证入馆 1-是
        public static final String ID_NUM_TAG = "id_num_tag";
        //每人每天入馆次数
        public static final String OO_ENTER_TIMES = "oo_enter_times";
        //停车优惠
        public static final String PARKING_DISCOUNT_TAG = "parking_discount_tag";
        // 闸机入馆只允许刷掌
        public static final String AUTO_GATE_ONLY_PALMS = "auto_gate_only_palms";



    }

    /**
     * 卡有效期类型
     */
    public static class CardValidityType {
        // 余额有效期
        public static final String BALANCE_VALIDITY = "1";
        // 折扣有效期
        public static final String DISCOUNT_VALIDITY = "2";
    }

    public static class TicketKind {
        // 普通票
        public static final String SIMPLE_TICKET = "1";
        // 培训票
        public static final String TRAINING_TICKET = "2";
        // 体验票
        public static final String COUPON_TICKET = "3";
        // 赛事活动票
        public static final String PERFORM_TICKET = "4";
        // 商家赞助票
        public static final String MERCHANT = "5";
        // 全民健身优惠票
        public static final String DISCOUNT_TICKET = "7";
        // 特色项目票
        public static final String FEATURED_PROJECT_TICKET = "8";

        public static final String PERFORM_NEW_TICKET = "9"; // 赛事活动新票
    }

    /**
     * 场馆静态参数定义表
     */
    public static class VenueStaticParam {
        public static final String OPEN_COURSE_TYPE = "open_course_type";//公开课类型
        public static final String VENUE_CAMP_ACTIVITIES = "venue_camp_activities";
        public static final String VENUE_CAMP_FIELD_TYPY = "venue_camp_field_type";
        public static final String STOCK_OUT_TYPE = "stock_out_type";//出库类型
        public static final String DELIVERY_COMPANY = "delivery_company";//出库单位
        public static final String STOCK_IN_TYPE = "stock_in_type";//入库类型
        public static final String PACKING_RATIO = "packing_ratio";//包装率
        public static final String GOODS_TYPE = "goods_type";//商品类型
        public static final String GOODS_BRAND = "goods_brand";//商品品牌
        public static final String TRAINING_TYPE = "training_type";//培训类型
        public static final String TRAINING_PERSON_AMOUNT = "training_person_amount";//培训一对几的价格配置
        public static final String TRAINING_SERVICES = "training_services";//微信上培训报名过滤的服务项目
        public static final String COUPON_SERVICES = "coupon_services";//微信上领优惠券过滤的服务项目
        public static final String COUPON_CLASS = "coupon_class";//微信上领优惠券过滤的券分类
        public static final String SKATING_TRAINING_SERVICES = "skating_training_services";//微信上冰雪课培训报名过滤的服务项目
        public static final String TRAINING_COACH_LEVEL = "training_coach_level";//冰雪课的培训教练级别
        public static final String CENTER_SERVICES = "center_services";//中心支持的服务
        public static final String PROJECT_TYPES = "project_types"; //赛事类型
        public static final String FIXED_OCCUPY_TYPE = "fixed_occupy_type";//场地占场类型
        public static final String CAMP_TYPE = "camp_type";//赛事类型
        public static final String WORK_GROUP_KIND = "work_group_kind"; //工作组类型
        public static final String VISITOR_SOURCE_ID = "visitor_source_id";//访客来源渠道
        public static final String INT_LEVEL = "int_level";//意向等级
        public static final String STAFF_CARD_TYPE = "staff_card_type";//卡类型列表
        public static final String GROUP_BUYING_TYPE = "group_buying_type";//团购类型
        public static final String CASH_PLEDGE_PAY_MODE = "cash_pledge_pay_mode";//押金支付方式
        public static final String POS_APPLY_CARD_PSPT_TYPE = "pos_apply_card_pspt_type"; // pos机办卡选择的证件类型
        public static final String OPERATING_CHARGE_TYPE = "operating_charge_type";  //场馆录入费用类别
        public static final String PLAY_PROJECT_LABEL = "play_project_label";  //娱乐项目标签
        public static final String CANCEL_GAME_REASON = "cancel_game_reason";  //取消约战原因
        public static final String BM_REPORT_ITEM_GROUP_CODE = "bm_report_item_group_code";  //体测报告项目分组编码
        public static final String COURSE_LEAVE_TYPE = "course_leave_type";  //课程请假类型
        public static final String PO_SOURCE_TYPE_ID = "po_source_type_id";  //积分来源编码
        public static final String PO_RULE_TYPE = "po_rule_type";  //积分类型
        public static final String NET_USER_ROLE_DEFINE = "net_user_role_define";// 西交角色与本库等级对应关系
        public static final String MAIL_CONFIG = "mail_config"; //邮箱配置信息
        public static final String WARNING_RECEIVERS = "warning_receivers"; //警报接收人
        public static final String OP_COMPLAINT_ITEM = "op_complaint_item"; // 运营投诉项目
        public static final String OP_COMPLAINT_ITEM_GROUP = "op_complaint_item_group"; // 运营投诉项目分组
        public static final String LESSON_ATTN_COMMENT_GRADE_TYPE = "lesson_attn_comment_grade_type"; //上课评分类型
        public static final String VENUE_CATEGORY = "venue_category"; // 场馆分类列表
        public static final String ROOM_OCCUPY_TYPE = "room_occupy_type"; // 教室占场类型
        public static final String TICKET_CHANNELS = "ticket_channels"; // 渠道列表
        public static final String MICROLECTURE_CATEGORY = "microlecture_category";//微课程类型
        public static final String FIELD_TICKET_SOURCE = "field_ticket_source"; //场地票售票渠道
        public static final String PROMOTION_CAMP = "promotion_camp"; //营销活动指定活动
        public static final String PROMOTION_CHANNELS = "promotion_channels"; // 营销活动渠道列表
        public static final String CENTER_SUIT_PSERSON = "center_suit_person"; //中心培训适用人群
        public static final String GOODS_CUSTOM_TYPE = "goods_custom_type"; // 商品自定义分类
        public static final String USER_ADDRESS_TAG_DEFAULT = "user_address_tag_default"; // 用户地址默认系统级标签
        public static final String VENUE_SHOP_SERVICES = "venue_shop_services"; //商家服务可选列表
        public static final String CITY_CODE = "city_code"; //默认配置的城市id
        public static final String COURSE_ENROLL_CHANNEL = "course_enroll_channel"; //课程报名渠道
        public static final String ANNUAL_REPORT_CODE = "annual_report_code";//年度报告
        public static final String GROUP_COURSE_COMMENT_GRADE_TYPE = "group_course_comment_grade_type"; //团课评分类型
        public static final String ENTER_PRIORITY = "enter_priority";

    }

    /**
     * 审核位置
     */
    public static class AuditPosition {
        //完工前
        public static final String BEFOR_FINISH = "2";
        //完工后
        public static final String AFTER_FINISH = "3";
        //支付前
        public static final String BEFOR_PAY = "1";
    }

    /**
     * 赛事场次属性
     */
    public static class PerformAttr {
        //折扣
        public static final String DISCOUNT = "discount";
        //折扣描述
        public static final String DISCOUNT_DESC = "discount_desc";
        public static final String CAN_REFUND_TICKET = "can_refund_ticket"; // 是否可以退票
    }

    public static class TradeTicketAttr {
        //区域票Id
        public static final String PERFORM_TICKET_ID = "perform_ticket_id";
        public static final String ATM_ID = "atm_id";//自助终端设备编号
        public static final String GUEST_PHONE = "guest_phone";//临时客户电话
        public static final String GUEST_NAME = "guest_name";//临时客户姓名
        public static final String GUEST_GENDER = "guest_gender";//临时客户性别
        public static final String GUEST_BIRTHDAY = "guest_birthday";//临时客户生日
        public static final String COUPON_NO = "coupon_no";//票领取的优惠券号
        public static final String TICKET_REMINDED_TAG = "ticket_reminded_tag";//票超时已提醒标志，1-已提醒，其他-未提醒
        public static final String PALM_PERSON_ID = "palm_person_id";// 掌静脉用户Id
        public static final String FACE_ID = "face_id";// 人脸用户Id
        public static final String PHONE = "phone";// 人脸用户手机号
        public static final String PHOTO_URL = "photo_url";// 人脸URL
        public static final String GROUP_BUYING_TICKET_ITEM = "group_buying_ticket_item";// 团购票项目节点
        public static final String GROUP_BUYING_TICKET_ID = "group_buying_ticket_id";// 携程团购票，票关系表Id
        public static final String ROOM_ID = "room_id";
        public static final String HEALTH_AGREEMENT_CUST_ID = "";
        public static final String USAGE = "usage"; // 使用须知
        public static final String INSTRUCTIONS = "instructions"; // 专用说明
        public static final String YACHT_DISCOUNT_MONEY = "yacht_discount_money";
        public static final String ROOM_BLOCK_NUM = "room_block_num"; // 连续包场数量
        public static final String CAR_NUMBER = "car_number";//绑定的车牌号
        public static final String ATM_PHONE_NUMBER = "atm_phone_number";//自助机售票新增输入手机号码
        public static final String BUY_DATE = "buy_date";// 购买日期
    }

    public static class WechatAccountAttr {
        //是否调用第三方注册接口
        public static final String REGISTER_SERVICE = "register_service";
        //对应的销售网店id
        public static final String SHOP_ID = "shop_id";
        //对应销售网点页面标题名
        public static final String SHOP_TITLE = "shop_title";
        //创建客户资料,0-不创建,1-创建
        public static final String CREATE_CUSTOMER = "create_customer";
        //微信公众号的图片
        public static final String ACCOUNT_PICTURE = "account_picture";
        public static final String SHARE_DOMAIN = "share_domain";
        //微信第三方平台时间验证Ticket
        public static final String COMPONENT_VERIFY_TICKET = "component_verify_ticket";
        // 非税支付配置
        public static final String NON_TAX_PROFILE = "non_tax_profile";
        // 非税支付-密钥
        public static final String NON_TAX_SECRET_KEY = "non_tax_secret_key";
    }

    public static class OffsetUnit {
        public static final String DAY = "0";//天
        public static final String MONTH = "1";//月
        public static final String QUARTER = "2";//季
        public static final String YEAR = "3";//年
        public static final String FIXED_DATE = "4";//固定日期
        public static final String FIXED_END_DATE = "5";
        public static final String FIXED_TIME = "6"; //固定时间-年月日
    }

    public static class MerchantStatus {
        public static final String NORMAL = "1";

        public static final String TERMINATED = "2";
    }

    /**
     * 商店属性
     */
    public static class ShopAttr {
        public static final String FETCH_ADDRESS = "fetch_address"; // 取快递地址
        public static final String DISCOUNT = "discount"; // 折扣
        public static final String EXPRESS_FEE = "express_fee"; // 快递费
        public static final String FETCH_TYPE = "fetch_type"; // 配送方式
        public static final String FEIE_DEVICE_IDS = "feie_device_ids"; // 飞鹅打印机Ids
        public static final String SHOP_SERVICE_QR_CODE = "shop_service_qr_code"; // 客服二维码
        public static final String SHOP_FETCH_CODE_VALID = "shop_fetch_code_valid"; // 商城自提有效期
        public static final String SHOP_SERVICES = "shop_services"; // 商家服务
        public static final String INVENTORY_THRESHOLD = "inventory_threshold"; //库存阈值
        public static final String SHOP_SELF_DELIVERY = "shop_self_delivery"; // 门店自提
        public static final String SHOP_SEND_GOODS = "shop_send_goods"; //商家发货
        public static final String SELF_DELIVERY_NOTICE = "self_delivery_notice"; //自提须知
        public static final String CUSTOMER_SERVICE_PHONE = "customer_service_phone"; // 客服联系电话
        public static final String CUSTOMER_SERVICE_REMARK = "customer_service_remark"; // 客服备注
        public static final String FACTORY_DELIVERY = "factory_delivery";//厂家发货标识
    }

    /**
     * 取商品方式
     */
    public static class FetchType {
        public static final String OWN = "1"; // 自取
        public static final String EXPRESS = "2"; // 快递
        public static final String OUT = "3"; // 外卖
    }

    /**
     * 商品领取状态
     */
    public static class GoodsFetchStatus {
        public static final String NOT_FETCH = "0"; // 未取货
        public static final String ALREADY_SHIPPED = "1"; // 已发货
        public static final String ALREADY_FETCHED = "2"; // 已自提
        public static final String ALREADY_SIGN = "3"; // 已签收
        public static final String RETURNED_GOODS = "8"; // 已退货
        public static final String NOT_PAY = "9"; // 未支付
    }

    /**
     * 商品领取状态
     */
    public static class GoodsTradeState {
        public static final int ALL = 0; // 全部订单
        public static final int ALREADY_SHIPPED = 1; // 已发货
        public static final int ALREADY_FETCHED = 2; // 已自提
        public static final int ALREADY_SIGN = 3; // 已签收
        public static final int CANCEL = 4; // 已取消
        public static final int PAY = 5; // 已支付
        public static final int RETURNED = 8; // 已退货
        public static final int NOT_PAY = 9; // 未支付
        public static final int RETURNED_PENDING = 10; // 退货待审核
        public static final int RETURNED_UNAPPROVED = 11; // 退货审核不通过
    }

    public static class GoodsSaleExpressStatus {
        public static final String INVALID = "0";
        public static final String VALID = "1";
    }

    /**
     * 短信充值订单支付状态
     */
    public static class SysTradePayState {
        public static final String UNPAID = "0";
        public static final String PAID = "1";
    }

    public static class SysTradeState {
        //未完工状态
        public static final String UNCOMPLETED = "0";
        //己完工
        public static final String COMPLETED = "1";
        //已取消
        public static final String CANCELED = "3";
    }

    /**
     * 短信充值支付方式
     */
    public static class SmsPayMode {
        public static final String WECHATPAY = "17";
        public static final String ALIPAY = "22";
    }

    /**
     * 支付宝返回状态
     */
    public static class AlipayState {
        public static final String SUCCESS = "10000";
    }

    public static class CallbackUrl {
        // 微信回调url
        public static final String WECHAT_CALLBACK_URL = "/smsRecharge/paymentCallback/";
        // 支付宝回调url
        public static final String ALIPAY_CALLBACK_URL = "/smsRecharge/alipayCallback/";
    }

    public static class RoleState {
        public static final String INVALID = "0"; // 无效
        public static final String VALID = "1"; // 有效
        public static final String DELETE = "2"; // 删除
    }

    public static class SmsAcctAttrCode {
        //短信签名
        public static final String SMS_SIGN = "sms_sign";
    }

    public static class SmsSendLogState {
        //待审核
        public static final String PENDING = "0";
        //审核通过
        public static final String APPROVED = "1";
        //审批不通过
        public static final String UNAPPROVED = "2";
        //已执行
        public static final String EXECUTE = "3";
        //删除
        public static final String DELETE = "4";
        //发送失败
        public static final String FAIL = "5";
    }

    public static class SmsAcctChangeType {
        //充值
        public static final String RECHARGE = "1";
        //消费(即发短息扣减)
        public static final String CONSUME = "2";
        //发送失败返还
        public static final String RETURN = "3";
        //审核不通过全部返还
        public static final String RETURN_ALL = "4";
    }

    public static class CommentState {
        public static final String PENDING = "0"; // 待审核
        public static final String APPROVAL = "1"; // 审核通过
        public static final String UNAPPROVED = "2"; // 审核不通过
        public static final String DELETED = "3"; // 已删除
    }

    public static class SmsReceivingState {
        //接收成功
        public static final String DELIVRD = "DELIVRD";
        //接收失败(信号不良,关停机,黑名单等异常情况)
        public static final String UNDELIV = "UNDELIV";
        //退回
        public static final String REJECT = "REJECT";
    }

    public static class UsbKeyTag {
        public static final String NONEED = "0";//不需要

        public static final String STAFF = "1";//员工

        public static final String VENUE = "2";//场馆

        public static final String CENTER = "3";//中心
    }

    public static class GoodsSpecType {
        public static final String GROUP = "1"; //规格组
        public static final String ITEM = "2"; //规格项
    }

    /**
     * 库存出入库类型
     */
    public static class GoodsStockLogType {
        public static final String MANUAL_STOCK = "1";//手工入库
        public static final String MANUAL_DELIVERY = "2";//手工出库
        public static final String TRANSFER_IN = "3";//调拨入库
        public static final String TRANSFER_OUT = "4";//调拨出库
        public static final String SALE_STOCK = "5";//销售出库
        public static final String CHECK_STOCK = "8"; //盘点调库
        public static final String RETURN_STOCK = "9";//退货入库
        public static final String USE_OUT = "6";//领用出库
        public static final String OLD_OUT = "7";//报废出库
        public static final String PURCHASE_CANCEL = "20";//返销入库
    }

    /**
     * 库存出入库状态
     */
    public static class GoodsStockLogState {
        public static final String NOT_APPROVE = "0";//未审核
        public static final String APPROVED = "1";//已审核
        public static final String DELETE = "2";//已删除
        public static final String NOT_APPROVED = "3";//审核不通过
    }

    /**
     * 调拨状态
     */
    public static class GoodsStockTransferState {
        public static final String NOT_TRANSFER = "0";//未调库

        public static final String TRANSFER = "1";//已调库

        public static final String DELETE = "2";//已删除
    }

    /**
     * 系统业务类型
     */
    public static class SysTradeType {
        public static final String SMS_RECHARGE = "10"; //短信充值
    }

    /**
     * 商品上下架状态
     */
    public static class GoodsReleaseState {
        public static final String USE = "1"; // 上架
        public static final String UNUSE = "0"; //下架
    }

    public static class GoodsPriceType {
        public static final String NORMAL = "1";
        public static final String PROMOTION = "2";
    }

    /**
     * 库存盘点状态
     */
    public static class StockCheckState {
        //未调库
        public static final String NOT_ADJUST = "0";
        //已调库
        public static final String HAVE_ADJUST = "1";
        //已删除
        public static final String DELETE = "2";
    }

    /**
     * 结算方式
     */
    public static class AccountType {
        //月结算
        public static final String MONTH = "2";
    }

    /**
     * 手牌挂账商品状态
     */
    public static class SingBillDetailState {
        public static final String SETTLED = "1"; //已结算
        public static final String SETTLEMENT = "2"; //结算中
        public static final String NOT_SETTLE = "0"; //未结算
        public static final String DELETE = "9"; //已取消
    }

    /**
     * 活动类型
     */
    public static class PublicCampType {
        public static final String BODY_MEASUREMENTS = "6"; //体质测试
        public static final String ORIENTATION = "5"; //定向赛
        public static final String ON_LINE_CAMP = "9527"; //线上赛
        public static final String ACTIVITY_CAMP = "952702"; //活动
    }

    /**
     * 线上赛活动类型
     */
    public class OnLineCampType {
        public static final String AI_CAMP = "1"; //AI动作识别
        public static final String DIRECTIONAL_CAMP = "2"; //定向赛
        public static final String WALK_CAMP = "3"; //健步走
        public static final String POKER_CAMP = "4"; //棋牌赛
    }

    /**
     * 活动属性表属性
     */
    public static class PublicCampAttr {
        public static final String ON_LINE_CAMP_TYPE = "on_line_camp_type";//线上赛事类型
        public static final String ITEM_PER_PERSON = "item_per_person"; //一个队伍可以报的活动项目数
        public static final String TICKET_TYPE_ID = "ticket_type_id"; //活动对应的票id
        public static final String CAMP_TYPE = "camp_type";   //赛事活动类型
        public static final String ENROLL_WAYS = "enroll_ways"; //报名方式  (1线上 2线下)
        public static final String IS_AUDIT = "is_audit"; ////是否需要审核(0不需要 1需要)
        public static final String NEED_LOT = "need_lot";  //是否需要抽签(0不需要 1需要)
        public static final String CAMP_CONTACT_PHONE = "camp_contact_phone";  //赛事联系电话
        public static final String CAMP_URL = "camp_url";  //赛事活动网址URL
        public static final String HAS_SCORE = "has_score";  //活动是否有比分
        public static final String IS_CHARITABLE = "is_charitable"; //是否公益性 0否 1是
        public static final String IS_STICK_TOP = "is_stick_top"; //是否首页置顶 0否 1是
        public static final String IS_NO_ITEM = "is_no_item";   //是否为无项目活动 0否 1是
        public static final String RACING_VALID_NUM = "racing_valid_num";   //至少多少个运动员跑完比赛成绩有效
        public static final String SIGN_VALID_DISTANCE = "sign_valid_distance"; //签到有效距离
        public static final String MARGIN = "margin"; //保证金
        public static final String COUPON_CAMPAIGN = "coupon_campaign"; //优惠券活动
        public static final String AGREEMENT_ID = "agreement_id"; //协议
        public static final String PUBLIC_CAMP_IMG = "public_camp_img"; //赛事活动图集
        public static final String DEFAULT_GROUP_TAG = "default_group_tag"; // 是否设置默认分组标识 0-否（可设置二级分组）1-是（仅一级分组）
        public static final String AS_MEMBER_ONLY_TAG = "as_member_only_tag"; //1:仅协会会员，0:全部用户
        public static final String IS_ACTIVITY_TAG = "is_activity_tag"; //1:是活动，0不是活动即赛事
        public static final String SPONSOR = "sponsor";//主办方
        public static final String NEED_SIGN_IN_TAG = "need_sign_in_tag";//是否需要签到
        public static final String SIGN_IN_RULES = "sign_in_rules";//签到的规则，活动的前后多少（分钟），距离多少（米）
        public static final String WX_QR_CODE = "wx_qr_code"; //分享海报二维码
        public static final String CAMP_LEVEL = "camp_level"; //赛事级别
        public static final String ACTIVITY_REVIEW = "activity_review";//活动回顾富文本
        public static final String CAMP_SMS_SETTING_CONTENT = "camp_sms_setting_content";//赛事短信配置
        public static final String CAMP_SMS_SETTING_SIGN = "camp_sms_setting_sign";//赛事短信配置签名
        public static final String CAMP_SMS_SETTING_TAG = "camp_sms_setting_tag";//赛事是否发送短信 1-发送 0-不发送
        public static final String CHESS_TYPE = "chess_type";//棋牌类型
    }

    /**
     * 赛事活动图集管理属性
     */
    public static class PublicCampMediaType {
        public static final String MEDIA_TYPE_IMG = "1"; //图片
        public static final String MEDIA_TYPE_VIDEO = "2"; //视频
    }

    /**
     * 活动项目属性表属性
     */
    public static class PublicCampItemAttr {
        public static final String MIN_MALE = "min_male"; //最少男性人数
        public static final String MIN_FEMALE = "min_female"; //最少女性人数
        public static final String AGE_LIMIT_TYPE = "age_limit_type";  //年龄限制类型，1-年龄，2-生日
        public static final String MIN_AGE = "min_age"; //报名要求最小年龄
        public static final String MAX_AGE = "max_age"; //报名要求最大年龄
        public static final String BIRTH_START_DATE = "birth_start_date"; //开始的出生日期
        public static final String BIRTH_END_DATE = "birth_end_date"; //结束的出生日期
        public static final String ENROLL_DEMAND = "enroll_demand"; //项目要求
        public static final String SUB_MATCHES = "sub_matches";//子比赛名称
        public static final String FINISH_CERTIFICATE_TEMPLATE_URL = "finish_certificate_template_url";//完赛证书模板路径
        public static final String IS_PUBLISH_ACHIEVEMENT = "is_publish_achievement";//是否发布成绩
        public static final String GENDER_LIMIT_TYPE = "gender_limit_type"; //性别限制
        public static final String PLAN_GAME_TIME = "plan_game_time"; //计划比赛时间
        public static final String VISIBLE_ONLY_TO_PLAYER_TAG = "visible_only_to_player_tag"; //仅对比赛成员可见
        public static final String REFUND_TAG = "refund_tag"; //报名后用户是否可取消，值为1时可退款（默认），值为0时不能退
    }

    /**
     * 年龄限制类型
     */
    public static class AgeLimitType {
        public static final String NO = "0"; //不限制
        public static final String AGE = "1"; //年龄
        public static final String BIRTH = "2"; //生日
    }

    /**
     * 性别限制类型
     */
    public static class GenderLimitType {
        public static final String MALE = "0"; //男
        public static final String FEMALE = "1"; //女
    }

    /**
     * 分组级别
     */
    public static class GroupingGrade {
        public static final String FIRST = "1"; //一级分组
        public static final String SECOND = "2"; //二级分组
    }

    /**
     * 赛事赛程状态
     */
    public static class MatchStageState {
        public static final String NOT_BEGIN = "0"; //未开始
        public static final String PROCESSING = "1"; //进行中
        public static final String END = "2"; //已结束
        public static final String DELETE = "3"; //已删除
    }

    /**
     * 次票延时队列处理失败后重新处理的时间间隔(毫秒)
     */
    public static final int TICKET_EXPIRED_RESEND = 5000;

    /**
     * 比赛团队标志
     */
    public static class TeamTag {
        public static final String PERSONAL = "0"; //个人赛
        public static final String TEAM = "1"; //团体赛
    }

    /**
     * 短信通知类型
     */
    public static class NoticeType {
        public static final String BOOKING = "11";//订场
        public static final String CANCEL_BOOKING = "12";//退订
        public static final String TRAINING = "13";//培训
        public static final String APPLY_CARD = "14";  //新用户办卡
        public static final String PRIVATE_COURSE = "15";//私教
        public static final String ENROLL_CAMPAIGN = "16";//活动报名
        public static final String SPECIAL_CARD_ACTIVATION = "17";//专项卡激活
        public static final String OPEN_TRAINING_CLASS = "18";// 开班前发送短信
        public static final String PUBLIC_CAMP_ENROLL_AUDIT_NOTICE = "19";  //赛事活动报名审核结果通知
        public static final String TC_LESSON_ATTN = "20";  //上课签到
        public static final String OVER_TIME_LESSON_ATTN = "21";//过期课程上课签到
    }

    /**
     * 短信通知扩展属性表
     */
    public static class SmsNoticeTypeAttr {
        public static final String STAFFID = "staff_id";
    }

    /**
     * 比赛状态
     */
    public static class MatchState {
        public static final String UNVAILD = "0";//无效
        public static final String UNSTART = "1";//未开始
        public static final String BEGINING = "2";//进行中
        public static final String END = "3";//已结束
    }


    /**
     * 活动报名类型
     */
    public static class EnrollType {
        public static final String NO_ITEM = "0";   //无项目
        public static final String PERSONAL = "1"; //个人
        public static final String TEAM = "2"; //团队
    }

    /**
     * 裁判类型 1-主裁判 2-副裁判
     */
    public static class JudgeType {
        public static final String MAIN = "1";
        public static final String NOT_MAIN = "2";
    }

    public static class TicketTypeAttr {
        //票场次
        public static final String TICKET_TIME = "ticket_time";
        //票使用场次
        public static final String TICKET_USED_TIME = "ticket_used_time";
        //票免费时间
        public static final String TICKET_TYPE_TIMEOUT = "ticket_type_timeout";
        //票超时金额 元/单位
        public static final String TICKET_TYPE_TIMEOUT_MONEY = "ticket_type_timeout_money";
        //票超时单位
        public static final String TICKET_TYPE_TIMEOUT_UNIT = "ticket_type_timeout_unit";
        //票超时价格
        public static final String TICKET_TYPE_TIMEOUT_PRICE = "ticket_type_timeout_price";
        //押金
        public static final String CASH_PLEDGE = "cash_pledge";
        //服务点
        public static final String SITES = "sites";
        //使用限制
        public static final String PLAY_PROJECT = "play_project";
        //缓冲时间
        public static final String BUFFER_TIME = "buffer_time";
        //是否显示前台销售
        public static final String SHOW_FRONT_SALE = "show_front_sale";
        public static final String POST_PAY_FREE_MINUTES = "post_pay_free_minutes"; // 后付费的免收费分钟
        public static final String POST_PAY_CAPPING_RULE = "post_pay_capping_rule"; // 后付费封顶规则
        public static final String POST_PAY_COST_RULE = "post_pay_cost_rule";  //后付费的计费规则简介
        public static final String POST_PAY_COST_RULE_DESC = "post_pay_cost_rule_desc";  //后付费的计费规则描述
        public static final String PRICE_UNIT = "price_unit"; // 计费单元
        public static final String ROOMS = "rooms"; // 教室
        public static final String CHECK_HEALTH_AGREEMENT_TAG = "check_health_agreement_tag"; // 是否校验健康承诺书 0-不校验 1-校验 默认校验
        public static final String POST_PAY_CAPPING_FEE = "post_pay_capping_fee"; // 后付费封顶价格
        public static final String CHANNEL_TOTAL_LIMIT = "channel_total_limit"; // 渠道总数量
        public static final String FIXED_START_DATE = "fixed_start_date"; // 固定开始日期
        public static final String FORBIDDEN_DATES = "forbidden_dates"; // 不可使用日期
        public static final String TICKET_TYPE_TIMEOUT_CEILING = "ticket_type_timeout_ceiling";//票超时封顶价格
        public static final String TICKET_TYPE_TIMEOUT_MONEY_LIMIT = "ticket_type_timeout_money_limit";//票超时限高费用
        public static final String TICKET_ADVICE_TYPE = "ticket_advice_type";//咨询类型
        public static final String FETCH_BRACELET_TAG = "fetch_bracelet_tag";//没有这个参数或者1的情况下都是能取手环，只有0才是不能取手环
        public static final String TICKET_TYPE_TIMEOUT_PAY_WITH_CASHPLEDGE = "ticket_type_timeout_pay_with_cashpledge";//次票超时费可以用押金抵扣 1-可以 0-不可以 默认不可以
        public static final String POST_BILLING_MODE = "post_billing_mode";//计时票类型 1-消费计时票 默认的计时票没有此属性
        public static final String POST_BILLING_STRATEGY = "post_billing_strategy";//计时票计费策略
        public static final String POST_DEDUCTION_ORDER = "post_deduction_order";//计时票超时费扣款顺序
        public static final String POST_COST_INCLUDE_FREE_FLAG = "post_cost_include_free_flag";//计时票超时后免费时长也收费开关，“1”-是，其他都是否
        public static final String ATM_SALE_WITH_TIME_TAG = "atm_sale_with_time_tag";//ATM是否按场地时间进行售票 1-是 其余不是
        public static final String ENTER_HALL_TIME_CAL_MODE = "enter_hall_time_cal_mode";//次票入馆时长计算 见TicketEnterHallCalMode 0-默认按实际入馆时间 1-按票对应场次的场次开始时间计算
        public static final String ATM_SALE_WITH_PHONE_TAG = "atm_sale_with_phone_tag";//ATM是否按场地时间进行售票 1-是 其余不是
        public static final String TICKET_TYPE_PICTURE = "ticket_type_picture";//票的图片

        public static final String TICKET_TYPE_CHANNEL_TIME_SALE_FLAG = "ticket_type_channel_time_sale_flag";//渠道的场次售卖开关 渠道存在说明开关打开

        public static final String WATER_CERTIFICATE_VALIDITY = "water_certificate_validity";//深水证有效期
        public static final String VALID_DAY_TYPE = "valid_day_type";//有效期类型，0是有效时段，1是当年有效
        public static final String USE_WEEK = "use_week"; // 可使用的星期
        public static final String DEFAULT_USE_WEEK = "1,2,3,4,5,6,7,8"; // 可使用的星期
    }

    public static class TicketEnterHallCalMode {
        public static final String CAL_BY_ENTER_TIME = "0";//根据实际入馆时间计算
        public static final String CAL_BY_TICKET_TYPE_TIME = "1";//更加次票对应场次开始时间计算
    }

    /**
     * 计时票计时类型
     */
    public static class PostBillingMode {
        // 消费计时票 计时规则按照时间段
        public static final String CONSUMPTION_TIME_TICKET = "1";
    }

    /**
     * 计时费闸机计算自动扣费方式
     */
    public static class PostBillingPayMode {
        /**
         * 余额卡
         */
        public static final String SPECIAL_CARD_MONEY = "1";
        /**
         * 次卡
         */
        public static final String SPECIAL_CARD_TIME = "2";
    }

    /**
     * 合作商类型
     */
    public static class CoopMerchantType {
        public static final String BANK = "1";
        public static final String SOCIAL_SECURUTY = "2";
    }

    /**
     * 合作商挂账类型
     */
    public static class CoopMerchantBillType {
        public static final String ALLOWANCE = "1";
    }

    /**
     * 微信首页模块类型
     */
    public static class WechatModuleType {
        //一个模块
        public static final String MODULE = "1";
        //一个模块分组
        public static final String GROUP = "2";
        //首页推荐菜单模块菜单
        public static final String RECOMMENDATION = "3";
    }

    /**
     * 合作商类型中文attr
     */
    public static class CoopMerchantAttr {
        public static final String BANK = "bank";
    }

    /**
     * 商品属性的编码
     */
    public static class GoodsAttrCode {
        //运动类型
        public static final String SPORTS_TYPE = "sports_type";
        //客制化类型
        public static final String CUSTOM_TYPE = "custom_type";
        //商品详情图片
        public static final String GOODS_PICTURE = "goods_picture";

        // 是否可以开具发票
        public static final String GOODS_INVOICE_TAG = "goods_invoice_tag";

        // 税收分类编码
        public static final String GOODS_TAX_CATEGORY = "goods_tax_category";
        // 税率
        public static final String GOODS_TAX_RATE = "goods_tax_rate";

    }

    /**
     * 商品上架属性的编码
     */
    public static class GoodsReleaseAttrCode {
        //商品销量
        public static final String SALES_VOLUME = "sales_volume";
        //商品回购量
        public static final String REPURCHASING_NUM = "repurchasing_num";
        //商品浏览量
        public static final String BROWSE_NUM = "browse_num";
        //商品上下架-厂家发货标识
        public static final String FACTORY_DELIVERY = "factory_delivery";
        // 门店自提
        public static final String SHOP_SELF_DELIVERY = "shop_self_delivery";
        //商家发货
        public static final String SHOP_SEND_GOODS = "shop_send_goods";
        //一级分组
        public static final String FIRST_GROUPING = "first_grouping";
        //二级分组
        public static final String SECOND_GROUPING = "second_grouping";
    }

    /**
     * 商品类型
     */
    public static class GoodsType {
        //专项卡
        public static final long SPECIAL_CARD = 1;
        //培训课
        public static final long TRAINING = 2;
    }

    /**
     * 商品实体类型
     */
    public static class GoodsEntityType {
        //对应goodsId
        public static final String GOODS = "1";
        //对应skuId
        public static final String SKU = "2";
    }

    /**
     * 学员本人信息
     */
    public static class StudentSelfTag {
        public static final String SELF = "1";
        public static final String OTHERS = "0";
    }

    /**
     * 活动报名元素类型
     */
    public static class CampElementType {
        //个人
        public static final String PERSONAL = "1";
        //团队
        public static final String TEAM = "2";
        //棋牌
        public static final String CHESS_CARD = "3";
    }

    /**
     * 日志类型
     */
    public static class JournalizedType {
        public static final String DEPOSIT = "deposit";
    }

    /**
     * app 类型
     */
    public static class AppType {
        public static final String OA = "1"; // 移动办公
        public static final String HAND_HELD = "2"; // 手持
        public static final String LARGE_DISPLAY = "3"; // 电视大屏
        public static final String ATM = "4"; //ATM自助终端
        public static final String CASH_REGISTER = "5"; //收银机
        public static final String MINI_APP = "6"; // 小程序
        public static final String WALL_HANGING = "7"; // 挂墙终端
        public static final String MINI_APP_MY_MENU = "8"; // 小程序之我的菜单
        public static final String MINI_APP_MY_CARD = "9"; // 小程序之我的卡包
        public static final String MINI_OA = "10";
        public static final String ATM_YUEDONG = "11"; // 悦动自助机（商米K1）
        public static final String MINI_APP_MY_SERVICE_MENU = "12"; // 小程序我的服务菜单
        public static final String MINI_APP_TOOL_MENU = "13"; // 小程序工具菜单
        public static final String DIVING = "20";//潜水官网
    }

    /**
     * 短信长度
     */
    public static class SmsLength {
        public static final int COMMON = 67;//普通长度
    }

    /**
     * 企业挂账结算状态
     */
    public static class OnCreditState {
        public static final String NO = "0"; // 未结算
        public static final String YES = "1"; // 已结算
        public static final String ING = "2"; // 结算中
    }

    /**
     * 支付宝授权类型
     */
    public static class AlipayGrantType {
        public static final String APP_AUTH_CODE = "authorization_code"; //通过app_auth_code获取token
        public static final String REFRESH_TOKEN = "refresh_token"; //通过refresh_token 换新的token
    }

    public static class InputType {
        //TEXT-文本输入 CHECKBOX-多选 RADIO-单选 TEXTAREA-多行 DATE-日期 IMAGE-图片
        public static final String TEXT = "TEXT";
        public static final String CHECKBOX = "CHECKBOX";
        public static final String RADIO = "RADIO";
        public static final String TEXTAREA = "TEXTAREA";
        public static final String DATE = "DATE";
        public static final String PICTURE = "PICTURE";
        public static final String NAME = "$NAME";
        public static final String PSPT_TYPE = "$PSPT_TYPE";
        public static final String PSPT_ID = "$PSPT_ID";
        public static final String PHONE = "$PHONE";
        public static final String GENDER = "$GENDER";
        public static final String TEAM_NAME = "$TEAM_NAME";

    }

    /**
     * 批量报名状态
     */
    public static class BatchEnrollState {
        //未执行
        public static final String START = "0";
        //处理成功
        public static final String SUCCESS = "1";
        //处理失败
        public static final String FAIL = "2";
        //已退课
        public static final String REFUND = "3";
    }

    /**
     * 性别
     */
    public static class Gender {
        //男
        public static final String MALE = "0";
        //女
        public static final String FEMALE = "1";
        // 设备用户
        public static final String ROBOT = "2";
    }

    /**
     * 图谱分析值
     */
    public static class AnalysisType {
        //0性别
        public static final String GENDER = "0";
        //1年龄
        public static final String AGERANGE = "1";
        //2次数
        public static final String VISIT_SUM = "2";
        //3停留时长
        public static final String DURATION = "3";
    }

    /**
     * 图谱入馆次数分析
     */
    public static class VisitSum {
        //0-2
        public static final String NEW = "0";
        //2-6
        public static final String TWO_SIX = "1";
        //>6
        public static final String SIX = "2";
    }

    /**
     * 图谱入馆时长分析
     */
    public static class Duration {
        //0-10分钟
        public static final String TEN = "0";
        //10-60分钟
        public static final String TEN_SIXTY = "1";
        //60-120分钟
        public static final String TWO_HOUR = "2";
        //>120分钟
        public static final String TWO_HOUR_MORE = "3";
    }

    /**
     * 图谱年龄
     */
    public static class AgeRange {
        //0-1
        public static final String ZERO_ONE = "0";
        //2-5
        public static final String TWO_FIVE = "1";
        //6-10
        public static final String SIX_TEN = "2";
        //11-15
        public static final String ELEVEN_FIFTEEN = "3";
        //16-20
        public static final String SIXTEEN_TWENTY = "4";
        //21-25
        public static final String TWENTYONE_TWENTYFIVE = "5";
        //26-30
        public static final String TWENTYSIX_THIRTY = "12";
        //31-40
        public static final String THIRTYONE_FORTY = "6";
        //41-50
        public static final String FORTYONE_FIFTY = "7";
        //51-60
        public static final String FIFTYONE_SIXTY = "8";
        //61-80
        public static final String SIXTYONE_EIGHTY = "9";
        //80以上
        public static final String EIGHTIES = "10";
        //难以辨别
        public static final String UNKOWN = "13";
    }

    /**
     * 区域等级
     */
    public static class AreaLevel {
        public static final String PROVINCE = "1";
        public static final String CITY = "2";
        public static final String DISTRICT = "3";
    }

    /**
     * 场馆属性表
     */
    public static class VenueAttrCode {
        public static final String DESC = "description";//描述
        public static final String SUM = "summary";//概要
        public static final String ADDITIONAL_SERVICE = "additional_service";//场馆支持的额外服务
        public static final String ADDITIONAL_SERVICE_CODE = "additional_service_code";//场馆支持的额外服务
        public static final String ENTER_HALL_FOREGIFT = "enter_hall_foregift";//验票入馆收取押金
        public static final String REMIND_INFO = "remind_info";//场馆友情提示（张家口要）
        //微信可查看实时客流的场馆属性
        public static final String STATISTISCS_VISUAL = "statistics_visual";
        public static final String VENUE_BUSINESS = "venue_business";//场馆业务
        public static final String VENUE_EMAIL = "venue_email";//场馆邮箱
        public static final String VENUE_CATEGORY = "venue_category";//场馆分类
        public static final String LIGHT_MODEL = "light_model";//灯控开关灯模式(阿里体育) 0-自动 1-手动
        public static final String ANNOUNCEMENT = "announcement";//公告
        public static final String ANNOUNCEMENT_TITLE = "announcement_title";//公告标题
        public static final String VR_LINK = "vr_link";//VR链接
        public static final String TRADE_SALES = "trade_sales";//场馆订单销量
        public static final String TRADE_COMMENT = "trade_comment";//场馆订单评分
        public static final String CLUB_CONTACT_PERSON = "club_contact_person";//俱乐部联系人
        public static final String CLUB_IMAGE = "club_image";//俱乐部广告图
        public static final String CLUB_LOGO = "club_logo";//俱乐部logo
        public static final String CLUB_USER_COUNT = "club_user_count";//俱乐部当前人数
        public static final String CLUB_BASIC_INTRODUCE = "club_basic_introduce";//俱乐部基本介绍
        public static final String IS_CLUB_TAG = "is_club_tag";//是否是俱乐部标签
        public static final String PERSON_SKILL_MERCHANT_TYPE = "person_skill_merchant_type";//一人一技筛选项-商户类型
        public static final String LICENSE_INFO = "license_info";//场馆证书信息（目前只有证书开始时间与结束时间）
        public static final String REMAIN_PARKING = "remain_parking";//剩余车位
        public static final String BUILDING_AREA = "building_area";
        public static final String FIELD_AREA = "field_area";
        public static final String VENUE_LEVEL = "venue_level";

        public static final String COUNTRY_VENUE_CODE = "country_venue_code";
        public static final String CORE_DISCOUNT_AREA = "core_discount_area";
        public static final String ZKJG_VENUE_TYPE = "zkjg_venue_type";
        public static final String INDOOR_FITNESS_AREA = "indoor_fitness_area";
        public static final String CORE_DISCOUNT_FLAG = "core_discount_flag";

    }

    /**
     * 场馆多媒体类型
     */
    public static class VenueResourceMediaType {
        public static final String PICTURE = "1";
        public static final String VIDEO = "2";
    }

    /**
     * 场馆业务类型
     */
    public static class VenueBusiness {
        //订场购票
        public static final String TICKETS = "1";
        //培训
        public static final String TRAINING = "2";
    }

    /**
     * 场馆多媒体用途
     */
    public static class VenueResourceUseType {
        public static final String VENUE_PICTURE = "1";
        public static final String ATM = "2";
    }

    /**
     * 活动占场状态
     */
    public static class FieldFixedStatus {
        public static final String UNVALID = "0";//无效
        public static final String VALID = "1";//有效
        public static final String AUDIT = "2";//审核中
        public static final String DELETE = "3";//已删除
    }

    /**
     * 微信会员卡默认状态
     */
    public static class CardDefaultTag {
        public static final String NOT_DEFAULT = "0";//非默认
        public static final String DEFAULT = "1";//默认
    }

    /**
     * 打印机类型
     */
    public static class PrinterType {
        public static final String NORMAL = "0";//普通打印机
        public static final String FEIE = "1";//飞鹅打印机
    }

    /**
     * 使用状态
     */
    public static class UseTag {
        public static final String USE = "1";
        public static final String UNUSE = "0";
    }

    /**
     * 教练培训报名状态
     */
    public static class CourseEnrollHourState {
        public static final String UNPAID = "0";    //未支付
        public static final String PAID = "1";    //已经支付
        public static final String CANCEL = "2";   //已经取消
    }

    /**
     * 签到状态(选择上课时间)
     */
    public static class EnrollDaysState {
        public static final String NO = "0";//未签到
        public static final String YES = "1";//已签到
        public static final String CANCEL = "2";//已取消
    }

    /**
     * 课程类别
     */
    public static class TrainingType {
        //分期课程
        public static final String CLASS_COURSE = "1";
        //长训班课程
        public static final String LONG_TRAINING = "2";
        //单次课程
        public static final String SINGLE_COURSE = "3";
        //长训期间课
        public static final String LONG_TERM = "4";
    }

    /**
     * 微信页面
     */
    public static class WechatPage {
        //培训首页
        public static final long TRAINING_INDEX = 20L;
    }

    /**
     * 报名证件类型
     */
    public static class PsptType {
        //身份证
        public static final String ID_CARD = "0";
        //军官证
        public static final String MILITARY_CERTIFICATE = "1";
        //护照
        public static final String PASSPORT = "2";
    }

    /**
     * product_channel 中的类型
     */
    public static class ObjectType {
        public static final String PRODUCT = "1";
        public static final String TRAINING = "2";
    }

    /**
     * 冰雪课独占标志
     */
    public static class ExclusiveTag {
        //非独占
        public static final String NOT_EXCLUSIVE = "0";
        //独占
        public static final String EXCLUSIVE = "1";
    }

    /**
     * 培训相关图片视频资源
     */
    public static class TrainingMediaUseType {
        //教练
        public static final String COACH = "1";
        //科目
        public static final String TC_SUBJECT = "2";
        //微课程
        public static final String MICROLECTURE = "3";
    }

    public static class CoachAttrCode {
        //教练教的课程中的最小价格
        public static final String MIN_COURSE_PRICE = "min_course_price";
        //教练平均评级
        public static final String REVIEW_AVERAGE_RATING = "review_average_rating";
        //教练评价人数
        public static final String REVIEW_NUM = "review_num";
        //报名教练的课时数
        public static final String ENROLLED_COURSE_HOUR = "enrolled_course_hour";
        //报名教练的学员数
        public static final String ENROLLED_STUDENT_NUM = "enrolled_student_num";
    }

    /**
     * app广告类型
     */
    public static class AppAdType {
        // 广告
        public static final String BANNER_AD = "1";
        //热门场馆
        public static final String HOT_VENUE = "2";
        //票务活动广告
        public static final String WECHAT_PROJECT_AD = "3";
        //培训广告
        public static final String TRAIN_AD = "4";
        //免费场馆
        public static final String FREE_VENUE = "5";
        //健身指导
        public static final String FITNESS_GUIDE = "6";
        //运动银行
        public static final String SPORTS_BANK = "7";
        //运动银行视频
        public static final String SPORTS_BANK_VIDEO = "8";
        //首页精品课程
        public static final String HOMEPAGE_HOT_COURSE = "9";
        //首页精品教练
        public static final String HOMEPAGE_HOT_COACH = "10";
        //首页体验项目
        public static final String EXPERIENCE_SERVICES = "11";
        //首页热门活动
        public static final String HOMEPAGE_HOT_CAMPAIGN = "12";
        //首页关于场馆
        public static final String HOMEPAGE_ABOUT_VENUE = "13";
        //首页横栏
        public static final String HOMEPAGE_BANNER = "14";
        //团建介绍
        public static final String TEAM_BUILDING = "15";
        //微课堂课件
        public static final String MICRO_CLASSROOM_COURSEWARE = "16";
        //优惠活动
        public static final String SALE_CAMPAIGN = "17";
        //首页公告
        public static final String HOMEPAGE_ANNOUNCEMENT = "18";
        //云课堂首页banner
        public static final String MICROLECTURE_AD = "19";
        //首页推荐模块
        public static final String HOMEPAGE_RECOMMEND_MODULE = "20";
        //体育设施服务
        public static final String SPORTS_FACILITIES_SERVICES = "21";
        //体育社会组织服务
        public static final String SPORTS_SOCIAL_ORGANIZATION_SERVICES = "22";
        //科学健身指导服务
        public static final String SCIENTIFIC_FITNESS_GUIDANCE_SERVICES = "23";
        //训练营
        public static final String TRAIN = "24";
        // 活动推广
        public static final String EVENT_PROMOTION = "25";
        // 赛事推广
        public static final String COMPETITION_PROMOTION = "26";
        // 演出现场
        public static final String PERFORMANCE = "27";
        //场馆咨询
        public static final String VENUE_CONSULT = "31";
        //体育资讯
        public static final String AT_CONSULT = "32";
        // 柜子屏保
        public static final String SCREENSAVER= "40";

        // 柜子内容推广
        public static final String CONTENT_PROMOTION= "41";
        //首页近期优惠
        public static final String HOMEPAGE_RECENT_DISCOUNT = "51";
        //首页近期资讯
        public static final String HOMEPAGE_RECENT_NEWS = "52";
    }

    /**
     * 大屏设备类型
     */
    public static class LargeDisplayDeviceType {
        //大屏内容，配置工号对应的大屏内容
        public static final String DISPLAY_CONTENT = "1";
        //大屏设备
        public static final String DISPLAY_DEVICE = "2";
        // 客流广告屏
        public static final String COUNTER_AD = "3";
        // 客流电视大屏
        public static final String COUNTER_TV = "4";
        // 集团大屏
        public static final String MASTER_CENTER_INFO = "5";
        // 中心数据大屏
        public static final String CENTER_INFO = "6";
    }

    /**
     * 队列路由的字典
     */
    public static class MQExchange {
        public static final String AUTO_GATE_EXCHANGE = "xports-autogate-exchange"; // 闸机显示数据
        public static final String COUNTER_EXCHANGE = "xports-camera-counter-exchange"; //摄像头计数
        public static final String SMS_SEND_EXCHANGE = "xports-sms-send-exchange";
        public static final String ALI_SMS_PUSH_EXCHANGE = "xports-ali-sms-push-exchange";//阿里短信推送信息

        public static final String DATA_SYNC_EXCHANGE = "xports-data-sync-exchange";//数据同步交换机

        public static final String ACTIVITY_EXCHANGE = "xports-activity-exchange";//互动活动交换机
        public static final String SKILL_COUPON_EXCHANGE = "xports-skill-coupon-exchange";//优惠券秒杀

        public static final String PERFORM_TICKET_EXCHANGE = "xports-perform-ticket-exchange";//赛事活动票
    }

    /**
     * 发票类型
     */
    public static class InvoiceType {
        public static final String COMMON_INVOICE = "1"; // 普通发票

        public static final String EMAIL_INVOICE = "2"; // 电子普通发票

        public static final String DIGITAL_INVOICE = "8"; // 数电普通发票
        public static final String SPECIAL_DIGITAL_INVOICE = "9"; // 数电专用发票



    }

    public static class InvoiceTitleType{
        public static final String PERSON = "0"; // 个人
        public static final String COMPANY = "1"; // 企业
    }

    /**
     * 开票状态
     */
    public static class InvoiceState {
        public static final String NO_INVOICED = "0"; // 未开票
        public static final String INVOICED = "1"; // 已开票
        public static final String APPLYING = "2"; //申请中

        public static final String CANCEL = "3"; //(水立方-取消申请)
        public static final String INVALID = "4"; //(水立方-已作废)
        public static final String INVOICING = "5"; //开票中
        public static final String FAILED = "6"; //开票失败
        public static final String AUTH_FAILED = "7"; //诺诺因为授权导致的开票失败  可能需要重试
    }

    /**
     * app开票状态
     */
    public static class AppInvoiceState {
        public static final String INVOICING = "1"; // 开票中
        public static final String INVOICED = "2"; // 已开票
        public static final String INVALID = "3"; //已作废
        public static final String ERROR = "4"; //开票出错
    }

    /**
     * 菜单编码
     */
    public static class MenuCode {
        public static final String ORDER_MANAGE = "order_manage"; //订单管理
        public static final String RESERVE_CANCEL = "reserve_cancel"; //订票取消
        public static final String VISITOR_MANAGEMENT = "visitor_management"; //潜在客户管理
        public static final String ENTER_HALL_NEW = "enter_hall_new"; //入馆(新)
        public static final String COURSE_SCHEDULE = "course_schedule"; //排课
        public static final String DOWNPAYMENT = "downPayment"; //订金管理
        public static final String AUDIT = "audit"; //审核
        public static final String FIELD_TICKET = "field_ticket"; //场地票销售
        public static final String ECARD_RECHARGE = "ecardRecharge"; //一卡通充值
        public static final String SPECIAL_CHARGE = "special_charge"; //费用收取
        public static final String RETURN_GOODS = "return_goods"; //退货
        public static final String OPERATE_TRAINING = "operate_training"; //培训变更
        public static final String ATTEND_QUERY = "attend_query"; //上课查询
        public static final String PO_RULE_MANAGE = "po_rule_manage"; //上课查询
        public static final String MEMBER_SEARCH = "memberSearch";//会员查询
        public static final String CUSTOMER_MANAGE = "customer_manage";//客户资料管理
        public static final String VENUE_CAMP = "venue_camp"; //活动管理
        public static final String HEALTH_ADVICE = "health_advice"; //健康咨询
        public static final String INVOICE_SUPPLEMENT = "invoice_supplement"; //发票补录
        public static final String WATER_FEE_MANAGE = "water_fee_manage"; //水量控制
        public static final String VISITOR_POOL_MANAGEMENT = "visitor_pool_management"; //访客管理池子
        public static final String HOUSE_PROPERTY_RENTAL = "house_property_rental"; //物业租赁
        public static final String COACH_VISITOR_POOL_MANAGEMENT = "coach_visitor_pool_management"; //访客管理池子
        public static final String BM_ARCHIVES = "bm_archives"; //体医档案
        public static final String CONSULTANT_MEMBER_POOL_MANAGEMENT = "consultant_member_pool_management"; //会籍会员池
        public static final String COACH_MEMBER_POOL_MANAGEMENT = "coach_member_pool_management"; //教练会员池
        public static final String SERVICE_RESV_MANAGEMENT = "service_resv_management"; // 潜客会员项目预约
        public static final String FIELD_RESOURCE_CONFIG = "field_resource_config"; // 场地配置
        public static final String PERFORMANCE_TARGET_PROGRESS = "performance_target_progress"; // 业绩目标进度

        public static final String COURSE_MANAGE_NEW = "course_manage_new"; // 课程管理（新）

        public static final String GROUP_LESSON_MANAGE = "group_lesson_manage"; // 团课排课
        public static final String HS_ENROLL = "hs_enroll"; // 五台山家校通
    }

    /**
     * 权限编码
     */
    public static class FunctionCode {
        public static final String VIEW_VENUE_DATA = "viewVenueData"; //场馆权限
        public static final String VEIW_CENTER_DATA = "viewCenterData"; //中心权限
        public static final String CHANGE_DEVELOPER = "changeDeveloper"; //修改发展人
        public static final String SPECIAL_REFUND = "specialRefund"; //特殊退票
        public static final String VIEW_VENUE_VISITOR = "viewVenueVisitor"; //显示场馆会籍顾问
        public static final String CHANGE_PAYMODE = "changePaymode"; //修改支付方式
        public static final String PRIVATE_REDUCE = "privateReduce"; //私教扣次
        public static final String CANCEL_TRADE = "cancelTrade"; //返销
        public static final String DEAL_COURSE_SCHEDULE = "dealCourseSchedule"; //排课操作权限
        public static final String ADVANCE_FIELD = "advanceField"; //锁场
        public static final String MANUAL_REDUCTION = "manualReduction"; //减免
        public static final String REFUND_CONFIRM = "refundConfirm"; //退款二次确认
        public static final String CHANGE_LESSON_NUM = "changeLessonNum"; //修改总/课时数量
        public static final String CHANGE_REMAIN_NUM = "changeRemainNum"; //修改剩余课时数量
        public static final String OCCUPY_COURSE_FIELD = "occupyCourseField"; //排课占场权限
        public static final String CHANGE_LESSON = "changeLesson"; //换课
        public static final String ACTIVATION_LESSON = "activationLesson"; //提前激活课程
        public static final String CHANGE_CLASS = "changeClass"; //更换班级
        public static final String REPLACE_COACH = "replaceCoach"; //更换教练
        public static final String VIEW_ONLINE = "viewOnline"; //场地全部查看
        public static final String CHANGE_COACH = "changeCoach"; //修改教练
        public static final String PO_RULE_EDIT = "poRuleEdit"; //修改教练
        public static final String EDIT_TRADE = "editTrade"; //订单编辑
        public static final String ADD_CAMP_ITEM = "addCampItem"; //新增活动项目
        public static final String ADD_CAMP_VENUE = "addCampVenue"; //新增活动使用场
        public static final String RETURN_ORDER = "returnOrder"; //订单退回
        public static final String CANCEL_ORDER = "cancelOrder"; //订单取消
        public static final String NOT_SUPPORT_PAY_ORDER = "notSupportPayOrder"; //不支持订单支付

        // 订金管理（downPayment）
        public static final String VIEW_VENUE_PAYMENT = "viewVenuePayment"; //场馆权限
        public static final String CHANGE_TRADE_TYPE = "changeTradeType"; //修改订金类型权限

        /*--------------------------------会员查询--------------------------------*/
        public static final String MEMBER_EDIT = "memberEdit";//编辑
        /*--------------------------------客户资料管理--------------------------------*/
        public static final String TAKE_PHOTO = "takePhoto";//拍照
        public static final String EDIT_CUSTOMER = "editCustomer";//编辑会员信息

        //咨询工作台
        public static final String ADVICE_EXPERT = "adviceExpert";// 咨询师-专家
        public static final String ADVICE_SPECIALIST = "adviceSpecialist";// 咨询师-专科
        //发票补录
        public static final String MAKE_OUT_INVOICE = "makeOutInvoice";// 开票
        public static final String MAKE_OUT_INVOICE_APPLICATION = "makeOutInvoiceApplication";// 开票申请
        public static final String MAKE_OUT_INVOICE_HISTORY = "makeOutInvoiceHistory";// 开票历史
        //水量控制
        public static final String WATER_FEE_EDIT = "waterFeeEdit"; //水量编辑
        // 国信新会籍 会籍顾问经理
        public static final String CONSULTANT_MANAGER = "consultantManager"; //会籍顾问经理

        // 国信新会籍 教练经理
        public static final String COACH_MANAGER = "coachManager"; //教练经理管理

        /*--------------------------------物业租赁--------------------------------*/
        public static final String ADD_RENTAL_CHARGE = "addRentalCharge";//新增租赁
        public static final String VIEW_RENTAL_CHARGE = "viewRentalCharge";//查看
        public static final String CANCEL_RENTAL_CHARGE = "cancelRentalCharge";//作废
        public static final String INPUT_RENTAL_CHARGE = "inputRentalCharge";//租赁费录入
        public static final String SETTLE_RENTAL_CHARGE = "settleRentalCharge";//租赁费结算
        public static final String INPUT_SERVICE_CHARGE = "inputServiceCharge";//服务费录入
        public static final String SETTLE_SERVICE_CHARGE = "settleServiceCharge";//服务费结算
        /*--------------------------------体医档案--------------------------------*/
        public static final String INPUT_BM_PRESCRIPTION = "inputBmPrescription";//录入运动处方
        /*--------------------------------会籍会员项目预约审核--------------------------------*/
        public static final String RESV_VERIFY = "resvVerify";//审核项目预约
        /*--------------------------------场地配置--------------------------------*/
        public static final String PACKAGE_CHANNEL_TIME = "packageChannelTime";//合并时段
        /*--------------------------------业绩目标进度--------------------------------*/
        public static final String QUERY_ALL_PROGRESS = "queryAllProgress";//查看全部员工进度

        public static final String COURSE_RELEASE_OFF = "courseReleaseOff";//课程发布下架


        public static final String GROUP_LESSON_ARRANGE = "groupLessonArrange";//团课排课权限

        public static final String GROUP_LESSON_PRICE = "groupLessonPrice";//团课价格权限

        public static final String GROUP_LESSON_RELEASE = "groupLessonRelease";//团课价格权限

        public static final String GROUP_LESSON_BOOK_RULE = "groupLessonBookRule";//团课预约规则


        public static final String ALL_HS_AUDIT = "allHsAudit"; // 家校通申请查询全部
    }

    /**
     * 短信通知级别
     */
    public static class SmsNoticeTypeLevel {
        public static final String CENTER = "1";
        public static final String VENUE = "2";
    }

    /**
     * 账本关联关系
     */
    public static class RelationType {
        public static final String GIFT = "1"; //赠送账本关联的主账本
    }

    /**
     * 入馆打票的状态
     */
    public static class PrintTicketState {
        public static final String CHECKED = "1"; //入馆打印票的状态为已验票

        public static final String UN_CHECKED = "2"; //入馆打印票的状态为未验票
    }

    /**
     * 位置类型
     */
    public static class LocationType {
        //场馆地址
        public static final String VENUE_LOCATION = "1";
        //票务演出地址
        public static final String PERFORM_LOCATION = "2";
    }

    /**
     * 票务活动属性编码定义
     */
    public static class ProjectAttrCode {
        public static final String PROJECT_BUY_MANUAL = "project_buy_manual"; //票务活动购票须知
    }

    /**
     * 票务购物车票状态
     */
    public static class PerformShoppingCartState {
        public static final String NORMAL = "1"; //未下单
        public static final String DELETE = "2"; //已删除
        public static final String ORDERED = "3"; //已下单
    }

    /**
     * 问券类型编码
     */
    public static class SalesTemplateListTypeCode {
        public static final String VISITOR = "visitor"; //访客扩展信息
    }

    /**
     * 预约类型
     */
    public static class ReserveTypeCode {
        public static final String SALES_RESERVE = "0"; //销售预约
        public static final String RETURN_RESERVE = "1"; //回访预约
        public static final String CALL_RESERVE = "2"; //来电预约
        public static final String SITE_RESERVE = "3"; //来店预约
    }

    /**
     * 预约对象类型
     */
    public static class ObjectTypeCode {
        public static final String VISITOR = "visitor"; //访客
        public static final String MEMBER = "member";   //会员
    }

    public static class CompanyMemberFollowState {
        public static final String APPROVAL_FINISHED = "0";//已审核
        public static final String APPROVAL_PROCESSING = "1";//审核处理中
    }

    /**
     * 负责人类型
     */
    public static class ResponsibleTypeCode {
        public static final String CONSULTANT = "consultant"; //会籍顾问
        public static final String COACH = "coach"; //教练
    }

    public static class ReserveStatus {
        public static final String PENDING = "0"; //待访
        public static final String VISITED = "1"; //到访
        public static final String EXIT = "2"; //离场
        public static final String CANCEL = "9"; //取消
    }

    /**
     * 登记时间标识
     */
    public static class JoinTag {
        public static final String THE_DAY = "0";
        public static final String THIS_WEEK = "1";
        public static final String THIS_MONTH = "2";
        public static final String THIS_YEAR = "3";
    }

    /**
     * 销售状态
     */
    public static class SalesStatus {
        public static final String TO_BE_ASSIGNED = "1"; //待分配
        public static final String ASSIGNED = "2"; //跟进中
        public static final String TO_JOIN = "3"; //入会
        public static final String SYSTEM_RECOVERY = "4"; //系统回收
        public static final String DELETE = "9"; //删除
    }

    /**
     * 日志操作类型
     */
    public static class OperationType {
        public static final String CARD_REGISTER = "card_register"; // 卡号登记
        public static final String HOUSE_PROPERTY_RENT = "house_property_rental"; // 物业租赁
        public static final String VENUE_CAMPAIGN = "venue_campaign"; // 场馆活动管理
        public static final String CHANGE_DEVELOPER = "change_developer"; // 修改发展人
        public static final String CHANGE_CUST_REMARK = "change_cust_remark"; // 修改客户备注
        public static final String CHANGE_TICKET_FIELD = "change_ticket_field"; // 更换场地;
        public static final String CHANGE_PAYMODE = "change_paymode"; // 修改支付方式
        public static final String CHANGE_COACH = "change_coach"; // 更换教练
        public static final String CHANGE_GROUP_TYPE = "change_group_type"; // 修改团购方式
        public static final String MAKEUP_CUSTOMER = "makeup_customer"; // 用户信息补录
        public static final String ALTER_PROMOTION = "alter_promotion"; // 营销活动修改
        public static final String CHANGE_LESSON_NUM = "change_lesson_num"; // 修改课时数量
        public static final String CHANGE_CLASS = "change_class"; // 修改班级
        public static final String CHANGE_REMAIN_NUM = "change_remain_num"; // 修改课时数量
        public static final String CHANGE_SOURCE_TYPE = "change_source_type"; // 修改客户来源
        public static final String DOWN_PAYMENT_CHANGE_TRADE_TYPE = "down_payment_change_trade_type"; // 订金修改类型
        public static final String CHANGE_SIGN_INFO = "change_sign_info"; // 修改报名信息
        public static final String UPDATE_STAFF = "update_staff";// 归属员工变更
        public static final String UPDATE_GRADE = "update_grade";// 级别调整
        public static final String LOGIN_LOG_INFO = "login_log_info"; // 登录日志信息
        public static final String LOGOUT_LOG_INFO = "logout_log_info"; // 用户退出日志信息
        public static final String PRODUCT_STOCK_CHANGE = "product_stock_change"; // 产品库存变更
    }

    /**
     * app广告内容类型
     * 内容类型 1-图文 2-外链 3-课程 4-活动 5-场馆 6-教练 7-视频  10-限时抢购
     */
    public static class AppAdContType {
        public static final String PICTURE_WORD = "1";
        public static final String OUTER_LINK = "2";
        public static final String COURSE = "3";
        public static final String CAMPAIGN = "4";
        public static final String VENUE = "5";
        public static final String COACH = "6";
        public static final String VIDEO = "7";
        public static final String DIVING_ANNOUNCEMENT = "9";
        public static final String LIMIT_SALE = "10";//限时折扣，对应sale_campaign表
        public static final String PUBLIC_CAMPAIGN = "11";//赛事活动，对应public_campaign
        public static final String MINI_APP = "21";//小程序
        public static final String MINI_LIMIT_SALE = "22";//小程序下限时抢购
        public static final String MINI_LOTTERY = "23";//小程序下大转盘
        public static final String MINI_GROUP_PURCHASE = "24";//小程序下拼团活动
        public static final String MINI_APPID = "25";//小程序下APPID
        public static final String MINI_OUTER_LINK = "26";//小程序下短链接
    }

    /**
     * 工作状态
     */
    public static class JobStatus {
        public static final String TO_BE_EXECUTED = "1"; //待执行
        public static final String IN_EXECUTION = "2"; //执行中
        public static final String FINISHED = "3"; //已完成
        public static final String CANCEL = "9"; //取消
    }

    /**
     * 任务客户类型
     */
    public static class SalesJobCutomerType {
        public static final String VISITOR = "1"; //访客
        public static final String MEMBER = "2";   //会员
    }

    /**
     * 任务来源
     */
    public static class SalesJobSourceType {
        public static final String MANUAL = "0";     //手工
        public static final String SYSTEM = "1";    //系统
    }

    /**
     * 任务类型
     */
    public static class SalesJobClassCode {
        public static final String SALES = "0";     //销售类型
        public static final String SERVICE = "1";   //服务类型
    }

    /**
     * 任务执行结果
     */
    public static class SalesJobExecResult {
        public static final String RESERVE = "0";   //预约
        public static final String NEXT_TIME_CONTACT = "1"; //下次联系
        public static final String NOT_DISTURB = "2"; //勿扰
        public static final String TC_LESSON_RESV = "3"; //预约体验
        public static final String NO_INTERSTING = "4"; //无意向
        public static final String SERVICE_RESV = "5"; //服务项预约
    }

    /**
     * 服务项预约状态
     */
    public static class ServiceResvState {
        public static final String PENDING = "0";   //待审核
        public static final String APPROVAL_UNVISIT = "1"; //审核通过待访
        public static final String REJECT = "2"; //拒绝
        public static final String VISITED = "3"; //到访
        public static final String LEAVE_VISIT = "4"; //离场
        public static final String CANCEL = "5"; //取消
    }

    /**
     * 服务项预约负责人类型
     */
    public static class ServiceResvResponsibleTypeCode {
        public static final String CONSULTANT_VISITOR = "0";   //会籍潜客预约
        public static final String COACH_VISITOR = "1"; //教练潜客预约
        public static final String CONSULTANT_MEMBER = "2";   //会籍会员预约
        public static final String COACH_MEMBER = "3"; //教练会员预约
    }

    /**
     * 访客属性表
     */
    public static class VisitorAttrCode {
        public static final String ATTR_CODE_INT_LEVEL = "int_level";   //意向等级
        public static final String ATTR_CODE_INT_SERVICE = "int_service";   //意向项目
        public static final String ATTR_CODE_INT_PLACE = "int_place";   //意向地点

    }

    /**
     * 跟进记录属性表
     */
    public static class SalesJobAttr {
        public static final String RESV_ID = "resv_id";   //预约体验表tc_lesson_resv主键
        public static final String SERVICE_RESV_ID = "service_resv_id";   //预约体验表service_resv主键
    }

    /**
     * 媒体类型
     */
    public static class MediaType {
        //图片
        public static final String IMAGE = "1";
        //视频
        public static final String MOVIE = "2";
    }

    /**
     * 场馆媒体资源使用类型
     */
    public static class VenueMediaUseType {
        public static final String VENUE = "1"; //1场馆图片
        public static final String ATM = "2";   //2自助终端图片
    }

    /**
     * 优惠券总数量类型
     */
    public static class CouponAmountType {
        public static final String TOTAL = "1"; //总计数量
        public static final String DAY = "2";   //每日总数量
        public static final String WEEK = "3";   //每周总数量
        public static final String MONTH = "4";   //每月总数量
    }

    /**
     * 优惠券类型限制类型
     */
    public static class CollectLimitType {
        public static final String TOTAL = "1"; //总计限制
        public static final String DAY = "2";   //每日限制
        public static final String WEEK = "3";   //每周限制
        public static final String MONTH = "4";   //每月限制
    }

    /**
     * 成绩状态
     */
    public static class ScoreState {
        public static final String UNCONFIRMED = "0";
        public static final String VALID = "1";
        public static final String INVALID = "2";
    }

    /**
     * 执行渠道编码
     */
    public static class ExecChannelCode {
        public static final String WEB = "1";
        public static final String APP = "2";
        public static final String WECHAT = "3";
        public static final String PHONE = "4";
        public static final String MESSAGE = "5";
        public static final String VISIT = "6";
    }

    /**
     * job的类型
     */
    public static class ClassCode {
        public static final String SALE = "0";
        public static final String SERVICE = "1";
    }

    /**
     * APP通知模板 动作编码
     */
    public static class ActionCode {
        public static final String CONSULTANT_ASSIGN_OUT = "consultant_assign_out"; //会籍顾问分配出
        public static final String CONSULTANT_ASSIGN_TO = "consultant_assign_to";  //分配进会籍顾问
        public static final String CONSULTANT_RESERVE = "consultant_reserve";  //预约会籍顾问
        public static final String CONSULTANT_VISIT = "consultant_visit";  //到访
        public static final String CONSULTANT_CANCEL = "consultant_cancel";  //取消
        public static final String TO_JOIN = "to_join";  //入会通知
        public static final String COACH_CLASS_NOTICE = "coach_class_notice";//教练员上课通知
        public static final String VISITOR_RECOVER_NOTICE = "visitor_recover_notice";//潜客回收通知
        public static final String VISITOR_RECOVER_SOON_REMIND = "visitor_recover_soon_remind";//潜客即将回收提醒
        public static final String VISITOR_NO_FOLLOW_REMIND = "visitor_no_follow_remind";//潜客未跟进提醒通知
        public static final String VISITOR_BIRTHDAY_REMIND = "visitor_birthday_remind";//潜客生日提醒通知
        public static final String MEMBER_BIRTHDAY_REMIND = "member_birthday_remind";//会员生日提醒通知
        public static final String MEMBER_RECOVER_NOTICE = "member_recover_notice";//会员回收通知
        public static final String MEMBER_RECOVER_SOON_REMIND = "member_recover_soon_remind";//会员即将回收提醒
        public static final String MEMBER_NO_FOLLOW_REMIND = "member_no_follow_remind";//会员未跟进/回访提醒通知
        public static final String MEMBER_PRODUCT_EXPIRE_REMIND = "member_product_expire_remind";//会员产品即将到期提醒通知

        public static final String STUDENT_RESV = "student_resv";//学员预约
        public static final String LEAVE_APPLY = "leave_apply";//学员请假
        public static final String LEAVE_REVOKE = "leave_revoke";//学员请假撤回
        public static final String STUDENT_ENROLL = "student_enroll";//学员报名
        public static final String ENROLL_FREEZE_APPLY = "freeze_apply";//学员请假
        public static final String ENROLL_FREEZE_REVOKE = "leave_revoke";//学员请假撤回


    }

    public static class NotificationAppType {
        public static final String OA = "1"; //悦动
        public static final String SUDA = "2"; //苏打
    }

    /**
     * 产品对象类型
     */
    public static class ProductObjectType {
        public static final String PRODUCT = "product";
        public static final String FIELD = "field";
        public static final String TICKET = "ticket";
        public static final String ECARD = "ecard";
    }

    /**
     * 人脸识别请求参数
     */
    public static class FaceRecognitionParamKey {
        public static final String API_KEY = "api_key";  //调用此API的API Key
        public static final String API_SECRET = "api_secret";    //调用此API的API Secret
        public static final String IMAGE_URL = "image_url";  //图片的URL。(下载图片需要时间,所以建议使用image_file或image_base64)   优先级3
        public static final String IMAGE_FILE = "image_file";    //一个图片，二进制文件，需要用post multipart/form-data的方式上传。 优先级1
        public static final String IMAGE_BASE64 = "image_base64";  //base64编码的二进制图片数据   优先级2
        public static final String RETURN_LANDMARK = "return_landmark";  //是否检测并返回人脸五官和轮廓的83个关键点。0:不检测 1:检测   ~默认0
        /*
         *  是否检测并返回根据人脸特征判断出的年龄，性别，微笑、人脸质量等属性，需要将需要检测的属性组织成一个用逗号分隔的字符串。
         *  目前支持：gender, age, smiling, headpose, facequality, blur, eyestatus, emotion, ethnicity
         *  属性的顺序没有要求。
         *  该字段的默认值为 none ，表示不检测属性。
         */
        public static final String RETURN_ATTRIBUTES = "return_attributes";
        public static final String DISPLAY_NAME = "display_name";   //人脸集合的名字，最长256个字符，不能包括字符^@,&=*'"
        public static final String OUTER_ID = "outer_id";   //账号下全局唯一的FaceSet自定义标识，可以用来管理FaceSet对象。最长255个字符，不能包括字符^@,&=*'"
        public static final String FACE_TOKENS = "face_tokens"; //人脸标识face_token，可以是一个或者多个，用逗号分隔。最多不超过5个face_token
        /*
         * 在传入outer_id的情况下，如果outer_id已经存在，是否将face_token加入已经存在的FaceSet中
         * 0：不将face_tokens加入已存在的FaceSet中，直接返回FACESET_EXIST错误
         * 1：将face_tokens加入已存在的FaceSet中
         * 默认值为0
         */
        public static final String FORCE_MERGE = "force_merge";
        public static final String FACE_TOKEN = "face_token";   //与Faceset中人脸比对的face_token
        public static final String RETURN_RESULT_COUNT = "return_result_count"; //返回比对置信度最高的n个结果，范围[1,5]。默认值为1
        public static final String IMAGE_BASE64_1 = "image_base64_1";   //用于两个图像对比，判断是否为同一人
        public static final String IMAGE_BASE64_2 = "image_base64_2";   //用于两个图像对比，判断是否为同一人
    }

    /**
     * 账本充值规则类型
     * 规则类型：1-普通充值 2-满赠 3-多买多送
     */
    public static class RechargeRuleType {
        public static final String NOMARL = "1";
        public static final String FULL_GRANT = "2";
        public static final String MORE_GRANT = "3";
    }

    /**
     * 角色类型
     */
    public static class RoleType {
        //web端角色
        public static final String WEB = "0";
        //移动oa端角色
        public static final String OA = "1";
        //手持设备 移动营业厅（悦动商家版）
        public static final String HANDHELD = "2";
        // 自助机
        public static final String ATM = "4";
        //后台管理
        public static final String ADMIN = "A";
    }

    /**
     * 赛事票项目状态
     * 0-删除，1-发布，2-未发布
     */
    public static class ProjectStatus {
        public static final String DELETE = "0";
        public static final String VALID = "1";
        public static final String NOT_VALID = "2";
    }

    /**
     * 体测预约类型
     */
    public static class BmApptType {
        //个人
        public static final String PERSONAL = "1";
        //团队
        public static final String GROUP = "2";
    }

    /**
     * 体测预约状态
     */
    public static class BmApptState {
        public static final String FAIL = "0";
        //已预约
        public static final String RESERVED = "1";
        //已取消
        public static final String CANCELED = "2";
        //已体测
        public static final String TESTED = "3";
        //已获取报告
        public static final String GETED = "4";
    }

    /**
     * 模块或报告类型
     */
    public static class AcmewayRptType {
        public static final String TEST_REPORT = "testReport";
        public static final String DIRECTION_REPORT = "directionReport";
        public static final String EXERCISE_RISK_RPT = "ExerciseRiskRpt";
        public static final String EXERCISE_CAPACITY_RPT = "ExerciseCapacityRpt";
    }

    /**
     * 模块名称编码
     */
    public static class AcmewayRptCode {
        public static final String SONOST2000 = "Sonost2000";
        public static final String MC180 = "MC180";
        public static final String MWT = "Test6MWT";
        public static final String EET = "ettload";
        public static final String INDIVIDUAL_ASSESS = "IndividualAssess";
        public static final String GMTZCS = "GMTZCS";
    }

    /**
     * 工作组状态
     */
    public static class WorkGroupStatus {
        public static final String INVALID = "0";   //禁用
        public static final String VALID = "1"; //启用
        public static final String DELETE = "3";    //删除
    }

    /*
     * 入馆类型
     */
    public static class EnterHallType {
        public static final String CARD = "0"; //卡(卡号)
        public static final String TICKET = "1"; //票(票号)
        public static final String COUPON = "2"; //券(券号、身份证号)
    }

    /**
     * 组类型编码
     */
    public static class GroupKindCode {
        public static final String EXERCISE_GUIDANCE = "1"; // 训练指导
    }

    /**
     * excel表头key
     */
    public static class ExcelCellKey {
        public static final String PHOTO = "photo";
    }

    public static class TrainingMediaType {
        public static final String IMAGE = "1";
        public static final String VIDEO = "2";
    }

    /**
     * pos机支付方式
     */
    public static class PosTradeType {
        public static final String PAY = "pay"; //支付
        public static final String REFUND = "refund"; //退款
    }

    /**
     * pos机支付状态
     */
    public static class PosTradeState {
        public static final String PAYING = "0"; //待支付
        public static final String PAID = "1"; //已支付
        public static final String FAIL = "2"; //支付失败
    }

    public static class RuleType {
        public static final String FIELD_TICKET_RULE = "1";//场地票规则
        public static final String SIMPLE_TICKET_RULE = "2";//次票规则
    }

    /**
     * 优惠券领取规则
     */
    public static class CouponDrawRuleType {
        public static final String PARKING_SCRIPT_RULE = "1";//停车券脚本规则
        public static final String PARKING_JSON_RULE = "2";//停车券json规则（青岛国信）
    }

    /**
     * 优惠券领取变量类型
     */
    public static class CouponDrawArgType {
        public static final String HOUR = "1";//按小时
        public static final String TIME = "2";//按次
    }

    public static class AutoEnterHall {
        public static final String NO = "0"; //不自动跳转
        public static final String OLD = "1"; //跳转到老入馆页面
        public static final String NEW = "2"; //跳转到新入馆页面
    }

    /**
     * 文件来源类型
     */
    public static class GeneraFileSourceType {
        public static final String VISITOR_BATCH_IMPORT = "1"; //潜在客户批量导入
        public static final String APP_BILL_UPLOAD = "2";//app账单上传
    }

    /**
     * 文件类型
     */
    public static class GeneraFileFileType {
        public static final String XLS = "xls"; //xls类型
        public static final String ZIP = "zip"; //xls类型
        public static final String CSV = "csv";
    }

    /**
     * 批量导入文件类型
     */
    public static class BatchImportTaskType {
        public static final String IMPORT_VISITORS = "import_visitors"; //导入潜在客户
    }

    /**
     * 批量导入数据扩展
     */
    public static class BatchImportTaskAttrCode {
        public static final String VENUE_ID = "venue_id";
        public static final String SOURCE_ID = "source_id";
        public static final String CAMP_ID = "camp_id";
        public static final String COLLECTOR_STAFF_ID = "collector_staff_id";
        public static final String REMARK = "remark";
    }

    /**
     * 收集方式
     */
    public static class CollectType {
        public static final String BATCH_IMPORT = "1"; // 批量导入
        public static final String SINGLE_INSERT = "2"; //单个录入
        public static final String CUSTOMER_SELF = "3"; //客户自助
    }

    /**
     * 课程预约状态
     */
    public static class LessonResvState {
        public static final String RESERVED = "0"; //已预约
        public static final String SIGNED = "1"; //已执行
        public static final String CANCELED = "2"; //已取消
        public static final String NOT_CONFIRMED = "9"; // 未确认的预约
    }

    /**
     * 课程预约类型
     */
    public static class LessonResvType {
        public static final String ENROLL = "1"; //上课预约
        public static final String VISIT = "2"; //体验预约
    }

    /**
     * 课程上课状态
     */
    public static class LessonAttnState {
        public static final String UN_ATTEND = "0"; //缺席
        public static final String ATTEND = "1"; //出席
        public static final String LEAVE = "2"; //请假
        public static final String CANCEL = "3"; //取消
    }

    /**
     * 课程签到取消状态
     */
    public static class LessonAttnCancelState {
        public static final String NOT = "0"; //未取消
        public static final String YES = "1"; //已取消
    }

    /**
     * 服务层级
     */
    public static class ServiceLevel {
        public static final String FIRST = "1"; //第一层级
        public static final String SECOND = "2"; //第二层级
    }

    /**
     * 课程类型
     */
    public static class TcLessonType {
        public static final String PUBLIC_LESSON = "1"; //公共课
        public static final String COACH_LESSON = "3"; //教练课
        public static final String PRIVATE_LESSON = "4"; //私教课
        public static final String SCHOOL_LESSON = "5"; //学校课
        public static final String MINI_GROUP_LESSON = "6"; //小团课
        public static final String GROUP_LESSON = "7"; //团课
    }

    /**
     * 一节课的状态
     */
    public static class TcLessonState {
        public static final String PREPARE_LESSON = "0"; //待上课
        public static final String FINISH_LESSON = "1"; //已上课
        public static final String STOP_LESSON = "2"; //停课
        public static final String DELETE_LESSON = "3"; //删除的课程
    }


    /**
     * 小团课的状态
     */
    public static class MiniTcLessonState {
        public static final String RESERVED = "0"; //已预约
        public static final String CAN_APPOINT = "1"; //可预约
        public static final String STOP_APPOINT = "2"; //已结束
        public static final String FULLED = "3";//已满员
    }

    /**
     * 员工级别
     */
    public static class StaffAdminTag {
        public static final String NORMAL = "0"; //0-非管理员
        public static final String SYS_MANAGER = "1"; //1-系统创建的管理员
        public static final String EMPOWER_MANAGER = "2"; //2-授权的管理员
    }

    /**
     * 员工资源类型
     * 资源类型 1中心 2场馆 3服务点 4服务项目
     */
    public static class StaffResourceType {
        public static final String CENTER = "1";
        public static final String VENUE = "2";
        public static final String SITE = "3";
        public static final String SERVICE = "4";
    }

    /**
     * 潜在客户的手机号码检查过滤编码
     */
    public static class VisitorPhoneFilterCode {
        public static final String BATCH_IMPORT_REPEAT_PHONE = "1000001"; //手机号批量导入时存在重复数据
        public static final String MEMBER_FILTER = "1000002"; //手机号批量导入时存在重复数据
        public static final String VISITOR_EXIST = "1000003"; //手机号批量导入时存在重复数据
        public static final String BLACKLIST_EXIST = "1000004"; //黑名单过滤
    }

    /**
     * 潜在客户的手机号码检查过滤项
     */
    public static class VisitorPhoneFilterItem {
        public static final String MEMBER_FILTER = "memberFilter"; //是否是正式会员判断：如果手机号码已经是正式会员，则不能录入系统，如果到期未续课，超过XX天，可以重新成为潜在客户。
        public static final String REPEAT_PHONE_FILTER = "repeatPhoneFilter"; //存在重复数据是否号码重复判断：判断该号码上次分配时间，如果上次分配时间超过30（可配置）天，且没有成单或没有客户意向等级
        public static final String BLACKLIST_FILTER = "blacklistFilter"; //手机号批量导入时存在重复数据
    }

    /**
     * 黑名单目标类型
     */
    public static class TargetType {
        public static final String VISITOR = "0";
        public static final String MEMBER = "1";
    }

    /**
     * 待办事项的事项类型
     */
    public static class TodoListTodoType {
        public static final String SALES_FOLLOW_UP = "1";
    }

    /**
     * 待办事项的来源类型
     */
    public static class TodoListSourceType {
        public static final String SALES_FOLLOW_UP = "1";
    }

    /**
     * 待办事项状态
     */
    public static class TodoListState {
        public static final String PENDING = "0";
        public static final String PROCESSED = "1";
    }

    /**
     * 待办事项扩展属性编码
     */
    public static class TodoListAttrCode {
        public static final String VISITOR = "visitor";
        public static final String MEMBER = "member";
    }

    /**
     * 客户提醒方法
     */
    public static class ReminderNoticeType {
        public static final String PAGE = "1"; //页面通知
        public static final String SMS = "2"; //短信
        public static final String WECHAT = "3"; //微信模板
        public static final String MINI = "4"; //小程序通知
    }

    /**
     * 产品限制类型
     */
    public static class ProductLimitTag {
        // 期间卡
        public static final String PERIOD = "0";
        // 计次卡
        public static final String TIMES = "1";
        // 余额卡
        public static final String MONEY = "2";
        // 组合卡
        public static final String GROUP = "3";
    }

    /**
     * 微信模板消息类型
     */
    public static class WechatMessageTemplateType {
        // 停课
        public static final String STOP_CLASS = "10001";
        // 课程到期提醒
        public static final String CLASS_DUE_REMIND = "10002";
        // 请假
        public static final String LEAVE_CLASS = "10003";
        // 取消请假
        public static final String CANCEL_LEAVE = "10004";
        // 积分变动
        public static final String POINTS_CHANGE = "10007";
        // 积分清零
        public static final String POINTS_CLEAR = "10008";
        // 课程取消通知
        public static final String CLASS_CANCEL = "10009";
        // 次票入馆提醒消息模版
        public static final String SIMPLE_TICKET_CHECKED = "10010";
        // 超时结算提醒消息模版
        public static final String PAYED_OVER_FEE = "10011";
        // 签到模板
        public static final String LESSON_SIGN = "10012";
        // 预约取消
        public static final String RESV_CANCEL = "10013";


        // 停车预约
        public static final String PARKING_RESV = "10014";


        public static final String GROUP_CARD_ENTER = "10016";

    }

    /**
     * 小程序模板消息类型
     */
    public static class MiniAppMessageTemplateType {
        // 排队叫号通知
        public static final String CALL_NUM = "20001";
        // 教练课程点评通知
        public static final String COACH_COMMENT_REMIND = "20002";
        // 课后作业提醒
        public static final String HOMEWORK_REMIND = "20003";
        // 候场通知提醒
        public static final String FIELD_WAIT_NOTICE = "20004";
        // 帆艇预定成功通知
        public static final String YACHT_ORDER_NOTICE = "20005";
        // 帆艇取消订单成功通知
        public static final String YACHT_CANCEL_NOTICE = "20006";
        // 充值成功通知
        public static final String YACHT_RECHARGE_NOTICE = "20007";

        // 五台山团课预约提示消息
        public static final String GC_LESSON_BOOK_WAITING_NOTICE = "20010";
        // 五台山团课签到提示消息
        public static final String GC_LESSON_SIGN_NOTICE = "20014";


        // 五台山团课预约提示消息
        public static final String GC_LESSON_CHANGE_NOTICE = "20011";
        // 直播购买成功提示消息
        public static final String PAY_BAIDU_LIVE = "20013";
        // 五台山团课取消预约提示消息
        public static final String GC_LESSON_BOOK_CANCEL_NOTICE = "20015";
        public static final String COURSE_ENROLL_AUDIT_NOTICE = "20016";//洪山课程报名审核结果通知


        // 家校通签到提醒
        public static final String LESSON_SIGN = "20020";

        // 家校通停课
        public static final String LESSON_STOP = "20021";

        // 请假成功
        public static final String LEAVE_SUCCESS = "20022";

        // 请假失败
        public static final String LEAVE_FAILED = "20023";
        // 预约上课提醒
        public static final String RESV_ATTEND_NOTICE = "20024";




    }

    /**
     * 阿里体育发卡用户类型
     */
    public static class AliOpenCardUserType {
        // 新用户
        public static final String NEW = "1";
        // 老用户
        public static final String OLD = "2";
    }

    /**
     * 卡号类型
     */
    public static class UserCardType {
        public static final String CUSTOMER_CARD = "1";  //客户
        public static final String STUDENT_CARD = "2";  //学员
    }

    /**
     * 卡号类型
     */
    public static class ResourceRange {
        public static final String PERSONAL_LEVEL = "0"; //个人权限
        public static final String ALL_LEVEL = "1"; //全部权限
    }

    /**
     * 期间卡顺延规则
     */
    public static class NewDepositTimeDelayRule {
        public static final String VENUE_DELAY_RULE = "1";  //与已有卡同场馆、同期间卡类型，则开卡时间进行顺延
        public static final String SERVICE_DELAY_RULE = "2";  //与已有卡同场馆、同期间卡、同服务项目，则开卡时间进行顺延
    }

    /**
     * 订金状态
     */
    public static class DownPaymentState {
        public static final String UNPAID = "0"; //未支付
        public static final String PAID = "1"; //已经支付
        public static final String CANCEL = "9"; //已经取消
        public static final String USED = "2";
        public static final String REFUND = "3"; //已退款
        public static final String REFUNDING = "4"; //已提交退款
        public static final String REFUND_SETTLE = "5"; //退款结算中
        public static final String DELETE = "8"; //已删除
    }

    /**
     * 预约状态
     */
    public static class ResvState {
        public static final String CAN_APPOINT = "1";  //可以预约
        public static final String STOP_APPOINT = "2";  //停止预约
    }

    public static class DXWSmsAPIClientType {
        public static final String SEND_TYPE = "pt";  //发送短信
        public static final String BALANCE_TYPE = "balance";  //查询余额
    }

    /**
     * 可否组合支付
     */
    public static class TradeTypeCompPayTag {
        public static final String CAN = "1";  //可以
        public static final String CAN_NOT = "0";  //不可以
    }

    public static class AutoGateKeyType {
        public static final String KEY = "key";
        public static final String MEMBER_CARD = "member_card";
        public static final String TICKET = "ticket";
        public static final String MANAGEMENT_CARD = "management_card";
        public static final String COACH_CARD = "coach_card";
        public static final String PALM_PASS = "palm_pass";
        public static final String FACE = "face";
        public static final String COUPON = "coupon";
        public static final String OTHER_QR_CODE = "other_qr_code";
        // 课程码，目前是西浦培训课（新模型）扫码入闸机
        public static final String COURSE_QR_CODE = "course_qr_code";
        public static final String COCIAL_SECURITY_CARD = "social_security_card";//电子社保卡
        public static final String ID_CARD = "id_card";//身份证
        public static final String COCIAL_SECURITY_CARD_ENTITY = "social_security_card_entity";//实体的电子社保卡（等价与身份证）
        // 账本id
        public static final String DEPOSIT_ID = "deposit_id";

        // 团课
        public static final String GROUP_LESSON = "group_lesson";

        // 团体卡
        public static final String GROUP_CARD = "group_card";
        // 到馆预约二维码
        public static final String RESERVATION_CODE = "reservation_code";

        // 微信掌纹
        public static final String WX_PALMS = "wx_palms";
    }

    /**
     * 订金来源类型
     */
    public static class DownPaymentSourceType {
        public static final String MANUAL_INPUT = "1";
        public static final String ORDER_TRANSFER = "2";
    }

    /**
     * 课程上课状态
     */
    public static class LessonInstanceStatus {
        public static final String NO = "0"; //未上课
        public static final String YES = "1"; //已上课、请假
        public static final String STOP = "2"; //已停课
    }

    /**
     * 减免类型
     */
    public static class ReductionMethod {
        public static final int MONEY = 1; // 金额
        public static final int DISCOUNT = 2; // 折扣
    }

    public static class AutoGateUseState {
        public static final String FREE = "1"; // 空闲状态
        public static final String COACH_OCCUPIED = "2"; // 教练占用状态
        public static final String CUST_OCCUPIED = "3"; // 客户占用状态
        public static final String TICKET_OCCUPIED = "4";// 票占用模式
        public static final String COUPON_OCCUPIED = "5"; // 优惠券占用
        public static final String FACE_OCCUPIED = "6"; // 人脸占用
    }

    /**
     * 签到方式
     */
    public static class SigninMode {
        public static final String SCAN_CODE = "1";  //教练扫码/刷卡签到
        public static final String SELF_HELP = "2";  //自助
        public static final String PLATFORM = "3"; // 前台
        public static final String AUTO = "4"; // 系统自动
        public static final String TEACHER = "5"; // 教师
        public static final String FACE_CHECKIN = "6"; // 人脸签到
        public static final String SIGN = "7"; // 学员签字
        public static final String OCM = "8"; // 一厘米签到
        public static final String AUTO_GATE = "9"; //闸机
        public static final String POS = "10"; //手持机签到
    }

    /**
     * 级别变动类型
     */
    public static class GradeChangeType {
        public static final String UPGRADE = "1"; // 升级
        public static final String DOWNGRADING = "2"; // 降级
        public static final String GRADEING = "3"; // 保级
    }

    public static class GradeType {
        public static final String CENTER = "1";//中心级别
        public static final String VENUE = "2";//场馆级别
    }

    /**
     * 级别变更方式
     */
    public static class GradeChangeMethod {
        public static final String MANUAL = "1"; //手动
        public static final String AUTO = "2";  //自动
    }

    /**
     * 期次学员分班状态
     * 0-无效 1-未分班 2-已分班
     */
    public static class TcTermStudentState {
        public static final String INVALID = "0";
        public static final String NOT_DIVIDED = "1";
        public static final String DIVIDED = "2";
    }

    /**
     * 小程序类型
     */
    public static class MiniAppType {
        // 微信
        public static final String WECHAT = "wechat";
        // 支付宝
        public static final String ALIPAY = "alipay";
    }

    public static class StudentAttrCode {
        public static final String REGISTERED_MEMBER_TAG = "registered_member_tag";//注册会员标志
        public static final String AD_ACCOUNT = "ad_account";//AD账号
        //学校
        public static final String SCHOOL = "school";
        //地址
        public static final String ADDRESS = "address";
        //家长姓名
        public static final String FAMILY_NAME = "family_name";
        //家长性别
        public static final String FAMILY_GENDER = "family_gender";
        //家长身份证号
        public static final String FAMILY_PSPT_ID = "family_pspt_id";
        //家长手机号
        public static final String FAMILY_PHONE = "family_phone";
        //员工oa号码
        public static final String OA_NUM = "oa_num";
    }

    public static class CommonPriceValueType {
        //1-单价
        public static final String PRICE = "1";
        //2-折扣
        public static final String DISCOUNT = "2";
    }

    /**
     * 退卡规则类型
     */
    public static class BackcardRuleType {
        public static final String FEE_RULE = "0"; //比例
        public static final String ENUM_RULE = "1"; //枚举
    }

    /**
     * 下载任务类型
     */
    public static class DownloadTaskType {
        public static final String PBULIC_CAMPAIGN = "1"; //赛事活动
    }

    /**
     * 下载任务状态
     */
    public static class DownloadTaskState {
        public static final String GENERATING = "0"; //正在生成
        public static final String SUCCESS = "1"; //生成成功
        public static final String FAILURE = "2"; //生成失败
    }

    /**
     * 退款方式
     */
    public static class ReturnType {
        public static final String CHARGE = "0"; //一卡通充值
        public static final String BACK = "1"; //原路退款
        public static final String CASH = "2"; //现金退款
        public static final String BANK = "3"; //银行卡退款
        public static final String INTER_TRANSFER = "7"; //内部转账
    }

    /**
     * 协议类型
     */
    public static class VenueAgreementType {
        public static final String COUPON = "1";
        public static final String PUBLIC_CAMP = "2";
    }

    /**
     * 票押金清退状态
     */
    public static class TicketForegiftRefundTag {
        public static final String NOT_REFUND = "0";
        public static final String REFUND = "1";
        public static final String TRANSFER = "2";
        public static final String TRANS_INCOME = "3";
    }

    /**
     * 限制名单适用类型
     */
    public static class LimitListAppropriateType {
        public static final String COLLECT_COUPON = "01"; //领取优惠券
        public static final String BUY_TICKET = "02"; //买票
        public static final String VENUE_RESERVATION = "03"; //场地预定短信验证
        public static final String ENTRY = "05"; //到馆预约

        public static final String GROUP_LESSON_BOOK = "06"; //团课预约
        public static final String FIELD_SANCTION = "07"; //订场降级
        public static final String COURSE_ENROLL = "08"; //培训报名
    }

    /**
     * 限制名单适用类型
     */
    public static class BlackListRuleTip {
        public static final String DEFAULT_TIP = "因违反馆内相关规定，暂无法提供服务"; //黑名单用户默认提示语
    }

    /**
     * 名单类型
     */
    public static class LimitListType {
        public static final String WHITE_LIST = "1"; //白名单
        public static final String BLACK_LIST = "2"; //黑名单
    }

    /**
     * 批量发放优惠券的状态
     */
    public static class TradeBatchCouponState {
        public static final String UNTREATED = "0";
        public static final String SUCCESS = "1";
        public static final String FAIL = "2";
    }

    /**
     * 定时任务执行日志状态
     */
    public static class ScheduleJobLogState {
        public static final String SUCCESS = "1";
        public static final String FAIL = "2";
    }

    public static class AuditOrderType {
        public static final String TRADE_AUDIT = "1"; // 订单审核
        public static final String CHANGE_CONSULTANT = "2"; // 会员分配审核
        public static final String MAKEUP_CUSTOMER = "3"; // 用户信息补录
        public static final String HEALTH_AGREEMENT_AUDIT = "4"; //健康档案审核
        public static final String DOC_NUR_APPLY_AUDIT = "5"; //医护申请审核

        public static final String GC_LESSON_EDIT_AUDIT = "8"; //团课编辑

        /**
         * 海运审核流程
         **/
        public static final String DIVING_COACH_ADD_AUDIT = "10"; //海运新增教练审核
        public static final String DIVING_LEARNING_APTITUDE_APPLY_AUDIT = "11"; //海运学习资质申请审核
        public static final String DIVING_CERTIFICATE_APPLY_AUDIT = "12"; //海运证书申请审核
        public static final String DIVING_COACH_INSPECTION_AUDIT = "13"; //海运教练年检审核
        public static final String DIVING_SHOP_INSPECTION_AUDIT = "14"; //海运潜店年检审核
        /**/
        public static final String HS_LEAVE = "20"; //家校通请假
        public static final String HS_ENROLL_FREEZE = "21"; //家校通冻课 gg还是走冻课的业务审核吧  这个不好走
        public static final String HS_TERM_CHANGE = "22"; //家校通换课
        public static final String STOP_LESSON = "23"; //家校通停课
    }

    /**
     * 培训可操作权限
     */
    public static class OperateTraining {
        // 更换教练
        public static final int CHANGE_COACH = 1;
        // 换课
        public static final int CHANGE_COURSE = 2;
        // 更换班级
        public static final int CHANGE_CLASS = 3;
        // 修改课时
        public static final int CHANGE_LESSON_NUM = 4;
        // 修改剩余课时
        public static final int CHANGE_REMAIN_NUM = 5;
        //提前激活课程
        public static final int CHANGE_LESSON_TIME = 6;
    }

    /**
     * 换课方式
     * 0-折算课时 1-补差价
     */
    public static class ReplaceChargeType {
        public static final String LESSON_NUM = "0";
        public static final String CHARGE = "1";
        // 产品新增培训可以自由调整节数和价格（平移换课和自由换课）
        public static final String FREE_CHARGE = "2";
    }

    /**
     * 会籍分配状态
     * 0-未处理 1-已处理 2-已拒绝
     */
    public static class BatchChangeConsultantState {
        public static final String NO_DEAL = "0";
        public static final String DEAL = "1";
        public static final String NO_APPROVE = "2";
    }

    public static class OperationLogDetailPropKey {
        //客户信息管理-紧急联系人关系
        public static final String EMERGENCY_CONTACT_RELATION = "emergencyContactRelation";
        //客户信息管理-性别
        public static final String GENDER = "gender";
        //客户信息管理-证件类型
        public static final String PSPTTYPE_ID = "psptTypeId";
        //客户信息管理-企业id
        public static final String ENTERPRISE_ID = "enterpriseId";
        //营销活动-活动类型
        public static final String PROM_TYPE = "promType";
        //营销活动-活动范围
        public static final String PRODUCT_ID = "productId";
        //营销活动-参与资格
        public static final String CONDITION_TYPE = "conditionType";
        //营销活动-指定活动
        public static final String CAMP_ID = "campId";
        //营销活动-渠道 ','分割
        public static final String CHANNEL_IDS = "channelIds";
        //更换场地-场地
        public static final String FIELD_ID = "fieldId";
        //更换场地-开始时间
        public static final String START_SEGMENT = "startSegment";
        //更换场地-结束时间
        public static final String END_SEGMENT = "endSegment";
        //团购类型
        public static final String GROUP_BUYING_TYPE = "groupBuyingType";
        //租赁周期
        public static final String HOUSE_RENT_TERM = "rentTerm";
        //租赁状态
        public static final String HOUSE_RENT_STATUS = "status";


    }

    /**
     * 账单类型
     */
    public static class BillType {
        public static final String TRADE = "1"; // 交易账单
    }

    /**
     * 场馆水电费类型
     */
    public static class ChargeTypeId {
        //电费
        public static final String ELECTRICITY = "1";
        //水费
        public static final String WATER = "2";
        //燃气费
        public static final String GAS = "3";
        //人员成本费
        public static final String PERSONNELS = "4";
        //工程维修
        public static final String ENGINEERING = "5";
        //市场推广
        public static final String MARKET = "6";
        //办公用品
        public static final String WORK = "7";
        //新旧摊销
        public static final String DEPRECIATION = "8";

    }

    /**
     * 费用标识
     */
    public static class CostType {
        //水电费类型
        public static final String EXTRA = "1";
        //其他类型
        public static final String OTHEREXTRA = "2";
    }

    /**
     * 记录时间点
     */
    public static class RecordingTime {
        //记录时间点为9点
        public static final String NINECLOCK = "9";
        //记录时间点为22点
        public static final String TENCLOCK = "22";
    }

    public static class AppAttrCode {
        public static final String ACCESS_BILL_TIME = "access_bill_time";//根据oss地址访问账单的有效时间
        public static final String BASE_URL = "base_url";//推送给第三方的地址
        public static final String SIGN_URL = "sign_url";
        // 授权访问的中心
        public static final String CENTER_ID = "center_id";
        // 授权访问的场馆
        public static final String VENUE_ID = "venue_id";
        // 授权使用的一卡通账号
        public static final String ECARD_NO = "card_no";
        // 查询次票的附加参数
        public static final String TICKET_QUERY_PARAM = "ticket_query_param";
        // 次票状态的变化通知
        public static final String TICKET_STATE_NOTIFY_URL = "ticket_state_notify_url";
        public static final String TICKET_PROPERTIES = "ticket_properties";
        public static final String TICKET_LIMIT = "ticket_limit";
    }

    public static class CustAttrCode {
        //老年证
        public static final String OLD_CARD = "old_card";
        //体检报告
        public static final String MEDICAL_REPORT = "medical_report";
        //安全协议
        public static final String SECURITY_PROTOCOLS = "security_protocols";
        // 常熟证件
        public static final String CHANG_SHU_CERTIFICATE = "chang_shu_cerificate";
        // 滁州这边上传的附件
        public static final String CHUZHOU_FILES = "chuzhou_files";
        public static final String EMERGENCY_CONTACT_PERSON = "emergency_contact_person"; //紧急联系人
        public static final String EMERGENCY_CONTACT_PHONE = "emergency_contact_phone"; //紧急联络电话
        public static final String GUO_XIN_CA_ATTACHMENT = "guo_xin_ca_attachment"; //国信ca附件
        public static final String GXT_ONE_ID = "gxt_one_id"; //国信通会员Id
        public static final String CARS = "cars";//绑定的车牌信息
        // 国信通会员unionId和oneId
        public static final String GXT_UNION_ID_BIND_TAG = "gxt_union_id_bind_tag";
        // 校园卡身份 0-学生；1-老师
        public static final String SCHOOL_IDENTITY = "school_identity";
        // 校园卡账号
        public static final String SCHOOL_CARD_NO = "school_card_no";
        // 特殊人群标志
        public static final String SPECIAL_PEOPLE_TAG = "special_people_tag";
        //绑定过人脸的手机号
        public static final String BINDING_FACE_PHONE = "binding_face_phone";    }

    /**
     * 网络用户属性
     */
    public static class NetUserAttrCode {
        //西交利物浦小程序用户角色
        public static final String NET_USER_ROLE = "net_user_role";
        public static final String REGISTER_VENUE_ID = "register_venue_id";
        public static final String REGISTER_GRADE_ID = "register_grade_id";//青岛海会员归属员工
        public static final String REGISTER_STEWARD_ID = "register_steward_id";//青岛海运会员级别
        public static final String OPERATOR_ID = "operator_id";//青岛海运会员操作人
        public static final String DIVING_ENGLISH_NAME = "diving_english_name";
        public static final String FAVORITE_SERVICE = "favorite_service";
        //重庆奥体微信用户购买停车优惠券数量 {"time":"2022-04-20","buyCouponsRecordNum":"1"}
        public static final String BUY_COUPONS_RECORD = "buy_coupons_record";
        public static final String ENTER_PRIORITY = "enter_priority";
        public static final String ENTER_PRIORITY_FLAG = "enter_priority_flag";
        //用户设置头像
        public static final String NET_USER_AVATAR = "net_user_avatar";
    }

    /**
     * 网络用户属性
     */
    public static class NetUserAccountAttrCode {
        //西交利物浦ad账号对应的工号，因为存在对方认证后没有工号的情况，需要手动记录
        public static final String EMPLOYEE_ID = "employee_id";
    }


    /**
     * 积分来源定义
     */
    public static class PoSourceType {
        public static final String CONSUME = "101"; // 消费
        public static final String AWARD = "102"; // 奖励
        public static final String ENTER_HALL = "103";// 入馆运动
    }

    /**
     * 积分兑换方式
     */
    public static class PoExchangeMode {
        public static final String PROPORTION = "1"; // 比例
        public static final String VALUE = "2"; //值
    }

    /**
     * 积分类型
     */
    public static class PointsType {
        public static final String CENTER_POINTS = "1"; // 中心积分（卡路里）
        public static final String SPORTS_BANK = "2"; //运动银行（卡币）
    }


    /**
     * 积分规则
     */
    public static class PointsRuleType {
        public static final String BASE_RULE = "1";//基本规则
        public static final String ADDITION_RULE = "2";//附加规则
    }

    /**
     * 积分规则内容元素
     */
    public static class PointRuleContent {
        public static final String HALF_HOUR_AMOUNT = "half_hour_amount"; // 半小时获取积分数量
        public static final String MIN_MINUTE = "min_minute"; // 获取积分最小在馆分钟数
        public static final String ROUND_TYPE = "round_type"; // 计算半小时取整方式 0-去尾 1-进1
    }

    /**
     * 运动娱乐项目增加积分方式
     */
    public static class PointsAddType {
        public static final String COEFFICIENT = "1"; // 系数方式
        public static final String ADDITION = "2";// 固定积分方式
    }

    /**
     * 积分异动变更类型
     */
    public static class PointsChangeType {
        public static final String GET_POINTS = "1"; // 获取积分
        public static final String RETURN_GET_POINTS = "2"; // 获取积分退还
        public static final String EXCHANGE_POINTS = "3"; // 积分兑换
        public static final String RETURN_EXCHANGE_POINTS = "4"; // 积分兑换退还
        public static final String EXPIRE = "5"; // 到期失效
        public static final String ISSURE = "6"; // 积分发放
        public static final String RECYCLE = "7"; // 积分回收
        public static final String TRANSFER = "8"; // 积分转账
        public static final String REWARD = "9"; // 奖励
    }

    /**
     * 运动娱乐项目属性
     */
    public static class PlayProjectAttr {
        public static final String POINTS_COEFFICIENT = "points_coefficient"; // 项目积分系数
        public static final String POINTS_ADDITION = "points_addition"; // 项目积分附加
        public static final String INTRODUCTION = "introduction"; // 简介
        public static final String DESCRIPTION = "description"; // 详细描述
        public static final String COVER_IMG = "cover_img"; // 封面图片
    }

    public static class CameraType {
        public static final String COUNTER = "1"; // 客流摄像头
        public static final String HIKVISION = "3"; // 海康摄像头
        public static final String HIKVISION_COUNTER = "5"; // 海康客流摄像头
        public static final String TUPU = "6"; // 图谱客流摄像头
    }

    public static class PassengerFlowType {
        public static final String Binocular = "1"; // 双目
        public static final String TUPU = "2"; // 图谱
    }

    /**
     * 海康统计组类型
     */
    public static class HikGroupType {
        public static final String ALL = "0"; // 全部
        public static final String BINOCULAR = "1"; // 双目客流
        public static final String DENSITY = "2"; // 客流密度
        public static final String AREA = "3"; // 区域客流
        public static final String CROWD = "4"; // 客群类型
        public static final String FACE = "5"; // 人脸客流类型
    }

    public static class WechatAccountType {
        public static final String PUBLIC_ACCOUNT = "1"; // 公众号
        public static final String SERVICE_MCH = "2"; // 服务商
        public static final String CHILD_MCH = "3"; // 子商户
        public static final String MINI_APP = "4"; // 小程序
        public static final String THIRD_PLATFORM = "5"; // 第三方平台，正常只会有一个

    }

    /**
     * 网络用户与阿里会员关系操作类型
     */
    public static class AliuidNetUserOperType {
        public static final String BIND = "0"; // 绑定
        public static final String UNBIND = "1"; // 解绑
    }

    /**
     * 对外接口日志目标系统
     */
    public static class OpenApiOutTargetSystem {
        public static final String ALI_SPORTS_BANK = "11"; // 阿里云从银行
    }

    /**
     * 对外接口日志接口类型
     */
    public static class OpenApiOutInterfaceType {
        public static final String CUST_BIND = "1101"; // 客户绑定
        public static final String ADD_POINTS = "1102"; // 发放卡币
        public static final String DEDUCT_POINTS = "1103"; // 扣减卡币
        public static final String QUERY_POINTS = "1104"; // 查询卡币
    }

    /**
     * 对外接口日志状态
     */
    public static class OpenApiOutLogState {
        public static final String FAILURE = "0"; // 失败
        public static final String SUCCESS = "1"; // 成功
        public static final String EXECUTING = "2"; // 执行中
    }

    /**
     * 积分兑换对象类型
     */
    public static class PoExchangeObjectType {
        public static final String VALUE = "1"; // 代金券
        public static final String EXCHANGE = "2"; //兑换券
    }

    /**
     * 积分获取类型
     */
    public static class PoNumType {
        public static final String MONEY = "1"; // 金额
        public static final String TIME = "2"; // 时长
        public static final String CALORIE = "3";// 卡路里
    }

    /**
     * 独墅湖支付配置
     */
    public static class DushuhuConfig {
        public static final String DUSHUHU_MCHID = "33333330";
        public static final String DUSHUHU_MCH_KEY = "33sdfsdfw323ooewdfdoi3ui2u298";
        public static final String DUSHUHU_WECHAT_REFUND_URL = "/api/wxpay/outrefund";
    }

    /**
     * venue_static_param.param_level
     * 1-公共参数 2-中心参数 3-场馆参数
     */
    public static class ParamLevel {
        public static final String PUBLIC = "1";
        public static final String CENTER = "2";
        public static final String VENUE = "3";
    }

    /**
     * 队长标志
     */
    public static class CaptainTag {
        public static final String CAPTAIN = "1"; //是队长
        public static final String NOT_CAPTAIN = "0"; //不是队长
    }

    /**
     * 约赛占场标志
     */
    public static class GameOccupyTag {
        public static final String NOT_OCCUPY = "0"; //不占场
        public static final String OCCUPY = "1"; //占场
    }

    /**
     * 约赛状态
     */
    public static class GameState {
        public static final String INITIAL = "0"; //初始发起状态
        public static final String SUCCESS = "1"; //约战成功
        public static final String CANCELD = "2"; //已取消
        public static final String UNPAID = "3"; //发起未支付
    }

    /**
     * 约赛取消方式
     */
    public static class GameCancelMode {
        public static final String WEB = "1"; //web取消
        public static final String MINI_APP = "2"; //小程序取消
        public static final String TRADE_EXPIRE = "3"; //订单失效自动取消
        public static final String SCHEDULE_JOB = "4"; //定时任务取消
    }

    /**
     * 约赛球队主客队标志
     */
    public static class GameTeamTag {
        public static final String HOST_TEAM = "H"; //主队
        public static final String VISITOR_TEAM = "V"; //客队
    }

    /**
     * 社区平台账号关联对象类型
     */
    public static class AssociateType {
        public static final String VENUE = "1"; //场馆
        public static final String STAFF = "2"; //工号
        public static final String NET_USER = "3"; //会员
    }

    /**
     * 发票录入状态
     */
    public static class InvoicePrintLogState {
        public static final String WAIT_FILL_ID = "0"; // 等待录入信息
        public static final String SUCCESS = "1"; // 成功
        public static final String FAIL = "2"; // 失败
    }

    /**
     * 开票地址
     */
    public static class InvoiceUrl {
        public static final String CREATE_BLUE = "/invoice/createBlueInvoice";
        public static final String BLUE = "/invoice/finishBlueInvoice";
        public static final String RED = "/invoice/finishRedInvoice";
    }

    /**
     * 阿里开票状态
     */
    public static class AliInvoiceState {
        public static final String CREATE = "CREATE"; // 创建
        public static final String SUCCESS = "INVOICE_SUCCESS"; // 成功
        public static final String FAILED = "INVOICE_FAILED"; // 失败
    }

    /**
     * 社区平台账号
     */
    public static class CommunityAccountType {
        public static final String NET_USER = "0"; // 会员
        public static final String OFFICIAL_ACCOUNT = "1"; // 官方账号
        public static final String STAFF = "2"; // 员工
        public static final String CONSULTANT = "3"; // 会籍顾问
    }

    /**
     * 课程帖子状态
     */
    public static class TcLessonPostState {
        public static final String DRAFT = "0"; // 草稿
        public static final String RELEASED = "1"; // 已发布
        public static final String DELETED = "2"; // 已作废
    }

    /**
     * 课程帖子类型
     */
    public static class TcLessonPostType {
        public static final String SUMMARY = "1"; // 课程总结帖
        public static final String HOMEWORK = "2"; // 布置作业贴
        public static final String HAND_IN_HOMEWORK = "3"; // 提交作业贴
    }

    /**
     * 人脸类型
     */
    public static class FaceType {
        public static final String FACE_ADD_ADD = "01"; // face++
        public static final String ALI = "02"; // 阿里体育
        public static final String SENSETIME = "03"; // 商汤科技
        public static final String HONG_RUAN = "04"; //虹软
        public static final String TENCENT = "05"; //腾讯人脸
    }

    /**
     * 社区平台推荐账号来源类型
     */
    public static class CoRecommendSourceId {
        public static final long VENUE = 1; // 场馆平台
    }

    /**
     * 入馆方式编码
     */
    public static class EnterMethodCode {
        public static final String MEMBER_CARD = "1"; // 会员卡
        public static final String KEY = "2"; // 手牌
        public static final String QR_CODE = "3"; //二维码
        public static final String FACE = "4"; // 人脸
        public static final String PALM = "5"; // 掌静脉
        public static final String ELE_CARD_CODE = "6";//电子卡二维码
        public static final String STAFF_CARD = "7"; // 员工卡
        public static final String SOCIAL_SECURITY_CARD = "9"; // 电子社保卡
        public static final String ID_CARD = "10"; // 身份证号
        public static final String SOCIAL_SECURITY_CARD_ENTITY = "11"; // 实体社保卡

        public static final String GROUP_LESSON = "12"; // 团课码

        public static final String GROUP_CARD = "13"; // 团体卡
        public static final String RESERVATION_CODE = "14"; // 场地预定二维码
        public static final String WX_PALMS = "15"; // 微信刷掌
    }

    /**
     * 入馆次数限制单位
     */
    public static class EnterTimesUnit {
        public static final String DAY = "0"; //日
        public static final String WEEK = "1"; //周
        public static final String MONTH = "2"; //月
    }

    /**
     * 服务点属性
     */
    public static class SiteAttrCode {
        public static final String DISABLED_PAY_MODES = "disabled_pay_modes";//禁用支付模式
        public static final String COMPLAINT_ITEM_GROUP = "complaint_item_group"; // 投诉项目分组
        public static final String BRACELET_BIND_PHONES = "bracelet_bind_phones"; // 手环机绑定的手机号
        // 青岛国信-国信通-门店编码-服务点参数
        public static final String GXT_STORE_CODE = "gxt_store_code";
    }

    /**
     * 付费方式表分组
     */
    public static class PayfeeModeGroupId {
        public static final String COMMON_PAY_MODE = "0";
    }

    /**
     * 请假状态
     */
    public static class TcLeaveState {
        public static final String NORMAL = "1"; //正常
        public static final String LEAVE = "2"; //销假
    }

    /**
     * 协议级别
     */
    public static class AgreementTypeLevel {
        // 中心
        public static final String CENTER = "1";
        // 场馆
        public static final String VENUE = "2";
    }

    /**
     * 场馆类型
     */
    public static class VenueType {
        // 服务场馆
        public static final String SERVICE_VENUE = "1";
        // 管理场馆
        public static final String MANAGEMENT_VENUE = "2";
    }

    /**
     * 掌静脉用户类型
     */
    public static class PalmPersonType {
        // 会员
        public static final String MEMBER = "1";
        // 散客
        public static final String GUEST = "2";
    }

    /**
     * 卡回收状态
     */
    public static class CardRecycleState {
        public static final String CANCEL = "0"; //取消
        public static final String NO_DEAL = "1"; //未处理
        public static final String SUCCESS = "2"; //成功
        public static final String FAIL = "3"; //失败
    }

    public static class OccupyType {
        public static final String CAMPAIGN = "0"; //活动占场
        public static final String TRAINING = "3"; //培训

        // 这三个仅用作占场冲突返回用的
        public static final String TICKET = "1"; //票
        public static final String AGREEMENT = "2"; //协议占场
        public static final String SCHOOL = "4"; //学校课包场
    }

    /**
     * 闸机入馆接口的Id类型
     */
    public static class AutoGateCheckInIdType {
        public static final String CARD = "0"; // 会员卡
        public static final String KEY = "2"; // 钥匙
        public static final String TICKET = "3"; // 票
        public static final String FACE = "6"; // 人脸识别
        public static final String PALM = "7"; // 掌静脉
    }

    /**
     * 小程序发布状态
     */
    public static class MiniAppReleaseStatus {
        // 待提交审核
        public static final String UN_AUDIT = "0";
        // 审核中
        public static final String AUDITING = "1";
        // 审核通过待发布
        public static final String UN_RELEASE = "2";
        // 审核不通过
        public static final String AUDIT_FAIL = "3";
        // 审核通过已发布
        public static final String RELEASED = "4";
        // 版本回退
        public static final String ROLLBACK = "5";
        // 审核撤回
        public static final String AUDIT_CANCEL = "6";
    }

    /**
     * 入馆规则
     */
    public static class EnterHallRule {
        public static final String FIELD_TICKET_CHECK_OUT_BUFFER_MINUTES = "fieldTicketCheckOutBufferMinutes"; // 场地票出馆缓冲时间
    }

    /**
     * 访客来源
     */
    public static class VisitorSourceId {
        public static final long MINI_APP_APPLY = 12;//小程序申请
    }

    /**
     * 一厘米用户类型
     */
    public static class OnecmUserType {
        // 会员
        public static final String MEMBER = "1";
        // 工作人员
        public static final String STAFF = "2";
    }

    /**
     * 物联网设备类型
     */
    public static class DeviceType {
        // 手环机
        public static final String WRISTBAND = "1";
        // 门禁
        public static final String DOOR_ACCESS = "2";
        // 团购自助取票机
        public static final String GROUP_BUYING_ATM = "3";
        // 私教签到设备
        public static final String PRIVATE_CHECKIN_DEVICE = "4";
        public static final String KOALA = "6";
    }

    /**
     * 开放平台Id
     */
    public static class OpenPlatform {
        // 口碑
        public static final long KOUBEI = 1L;
        // 美团点评
        public static final long MEITUAN_DIANPING = 2L;
        // 携程
        public static final long CTRIP = 3L;
    }

    /**
     * 团购类型
     */
    public static class GroupBuyingType {
        // 美团
        public static final String MEI_TUAN = "1";
        // 口碑
        public static final String KOU_BEI = "2";
        // 糯米
        public static final String NUO_MI = "3";
        // 携程
        public static final String XIE_CHENG = "4";
        // 云商
        public static final String YUN_SHANG = "5";
        // 亲子周末
        public static final String QIN_ZI_ZHOU_MO = "6";
    }

    /**
     * 团购取票类型
     */
    public static class GroupBuyFetchType {
        // 扫码取票
        public static final String SCAN_CODE = "1";
        // 自助取票
        public static final String ENTER_RECEIPT = "2";
        // 私教签到设备
        public static final String PRIVATE_CHECKIN_DEVICE = "4";
        // 自助售货机
        public static final String VENDING_MACHINE_DEVICE = "5";
    }

    /**
     * 设备子项目属性
     */
    public static class DeviceItemAttr {
        //货道类型
        public static final String GOODS_CHANNEL_TYPE = "goods_channel_type";
        //货道容量
        public static final String GOODS_CHANNEL_CAPACITY = "goods_channel_capacity";
    }

    /**
     * 期次课状态
     */
    public static class TermClassState {
        // 失效
        public static final String UNVALID = "0";
        // 有效
        public static final String VALID = "1";
        // 结课
        public static final String SETTLE = "2";
    }

    /**
     * 新的积分体系-积分来源
     */
    public static class PointsSourceTypeNew {
        public static final long CONSUME = 1L; //消费积分规则
        public static final long SPORTS = 2L; //运动积分规则
        public static final long REWARD = 3L; //奖励积分规则
    }

    /**
     * 物联网设备属性
     */
    public static class DeviceAttr {
        // 门禁对应控制场地，逗号分隔
        public static final String FIELD_IDS = "field_ids";
        // 门禁密码
        public static final String DOOR_CONTROL_PASSWORD = "door_control_password";
        //灯控的模式 0-自动 1-手动
        public static final String LIGHT_MODEL = "light_model";
        //灯控的状态 0-关 1-开
        public static final String LIGHT_STATE = "light_state";
        //设备对应的员工id
        public static final String STAFF_ID = "staff_id";
        //设备对应的人像服务点
        public static final String FACE_SET_ID = "face_set_id";
        // 手环机性别属性 1-男 2-女
        public static final String BRACELET_GENDER = "bracelet_gender";
        // 手环机密码
        public static final String BRACELET_PASSWORD = "bracelet_password";
        //设备服务时间
        public static final String SERVICE_TIME = "service_time";

    }

    /**
     * 新的积分体系-来源对象类型
     */
    public static class PointsObjectType {
        public static final String TRADE = "1"; //订单号
        public static final String TICKET = "2"; //票号
        public static final String REWARD = "3"; //奖励
    }

    /**
     * 新的积分体系-积分获取失效时扣除方式
     */
    public static class PointsReduceType {
        public static final String REDUCE_OLD_NUM = "0"; //扣除原数量
        public static final String REDUCE_TO_ZERO = "1"; //扣除至0
        public static final String NO_REDUCE = "2"; //保留
    }

    /**
     * 收入统计项目
     */
    public static class StatIncomeItem {
        public static final String ACTUAL_INCOME = "actual_income"; // 销售实收
        public static final String FOOD_INCOME = "food_income"; // 餐饮收入
        public static final String GOODS_INCOME = "goods_income"; // 装备收入
        public static final String GROUP_BUYING_INCOME = "group_buying_income"; // 团购售票
        public static final String LEAGUE_BUILDING_INCOME = "league_building_income"; // 团建收入
        public static final String OTHER_INCOME = "other_income"; // 其他收入
        public static final String PARTY_INCOME = "party_income"; // Party收入
        public static final String STORED_VALUE_INCOME = "stored_value_income"; // 储值收入
        public static final String TICKET_INCOME = "ticket_income"; // 普通售票
        public static final String TOTAL_INCOME = "total_income"; // 总收款
    }

    /**
     * 积分清除规则
     */
    public static class PointsClearType {
        public static final String EVERY_DAY = "0"; // 每天
        public static final String EVERY_MONTH = "1"; // 每月月底
        public static final String EVERY_YEAR = "2"; // 每年固定日期
    }

    public static class MenuType {
        public static final String WEB = "1";
        public static final String ADMIN = "2";
        public static final String COMMUNITY = "3";
        public static final String APP = "4";
    }

    /**
     * 第三方支付类型
     */
    public static class ThirdPayType {
        public static final String ALLINPAY = "allinpay"; //通联收银宝
        // 银联商务
        public static final String UNION_PAY = "unionPay";
        // 建行聚合支付
        public static final String CCB_PAY = "ccbPay";

        public static final String SWIFT_PASS_PAY = "swiftPassPay";
        // 常熟农商聚合支付
        public static final String CHANG_SHU_PAY = "changShuPay";
    }

    /**
     * 活动申报状态
     */
    public static class OpActivityType {
        public static final String NEW = "0"; //新增
        public static final String PASS = "1"; //通过
        public static final String NOT_PASS = "2"; //不通过
        public static final String INVALID = "4"; //无效
    }


    /*
     * 协议类型
     */
    public static class AgreementType {
        // 健康协议书类型
        public static final String HEALTH_AGREEMENT = "2001";
        //人脸录入使用协议类型
        public static final String FACE_AGREEMENT = "3001";
        //培训报名
        public static final String TRAINING_ENROLMENT_AGREEMENT = "59";
        //私教报名
        public static final String PRIVATE_ENROLMENT_AGREEMENT = "60";
        // 健康协议书类型
        public static final String YACHT_AGREEMENT = "2021";
        //约球协议类型
        public static final String CHAMBER_AGREEMENT = "2022";
    }

    /**
     * 公司属性
     */
    public static class CompanyAttr {
        // 运营公司类型
        public static final String OP_COMPANY_TYPE = "op_company_type"; //见OpCompanyType定义，不配默认运营公司"COMPANY_OPERATOR"
    }

    /**
     * 运营公司类型
     */
    public static class OpCompanyType {
        public static final String SUPERVISION = "COMPANY_SUPERVISION"; //监管公司
        public static final String OPERATOR = "COMPANY_OPERATOR"; //运营公司
    }

    public static class ProjectResourceType {
        public static final String CENTER = "1";
        public static final String VENUE = "2";
        public static final String SITE = "3";
    }

    public static class WorkOrderAttr {
        public static final String OP_PROJECT_ID = "op_project_id"; // 运营项目ID
        public static final String SITE_ID = "site_id"; // 服务点ID
        public static final String VENUE_ID = "venue_id"; // 场馆ID
        public static final String COMPLAINT_ITEM = "complaint_item"; // 投诉项ID
    }

    public static class WorkOrderProcessState {
        public static final String NEW = "0"; // 待受理
        public static final String ACCEPTED = "1"; // 已受理
        public static final String PROCESSED = "2"; // 已处理
        public static final String CLOSED = "3"; // 已关闭
        public static final String COMMENTED = "4"; // 已评价
    }

    /**
     * 工单受理结果
     */
    public static class WorkOrderAcceptResult {
        public static final String ACCEPTED = "1"; // 已受理
        public static final String REJECTED = "2"; // 不受理
    }

    /**
     * 文件容器类型
     */
    public static class ContainerType {
        public static final String WO_ORDER = "wo_order"; // 工单
        public static final String OP_REPORT = "op_report"; // 运营报告
    }

    /**
     * 已读类型
     */
    public static class ReadType {
        public static final String READ_TYPE = "1"; // 运营报告
    }

    /**
     * 超时收费类型
     */
    public static class TimeOutPriceType {
        //百分比票价
        public static final String PERCENT = "0";
        //固定金额
        public static final String FIX_AMOUNT = "1";
    }

    /**
     * 运营评分详情表类型
     */
    public static class OpScoreDetailType {
        public static final String COMPLAINT = "1"; // 投诉工单
        public static final String ACTIVITY = "2"; // 运营活动
    }

    /**
     * 运营评分科目属性
     */
    public static class OpScoreSubjectAttr {
        public static final String OP_ACTIVITY_TYPE = "op_activity_type"; //活动类型
    }

    /**
     * 活动类型
     */
    public static class ActivityType {
        public static final String OP_ACTIVITY_TYPE_FIELD = "1"; //场地服务
        public static final String OP_ACTIVITY_TYPE_SPORTS = "2"; //体育赛事
        public static final String OP_ACTIVITY_TYPE_CULTURE = "3"; //文化/会展
    }

    /**
     * 运营评分周期
     */
    public static class OpScoreCycle {
        public static final String YEAR = "1"; //年度
        public static final String SEASON = "2"; //季度
    }

    /**
     * 运营评分配置周期
     */
    public static class OpSubjectConfigScoreCycle {
        public static final String YEAR_AND_SEASON = "1,2"; //年度和季度
        public static final String YEAR = "1"; //年度
    }

    /**
     * 运营评分计算方式
     */
    public static class OpScoreCalculateMode {
        public static final String COMPLAINT = "1"; //投诉扣分
        public static final String ACTIVITY = "3"; //活动评分
        public static final String MATCH = "4"; //赛事评分
    }

    public static class CommentType {
        public static final String VENUE = "1"; //场馆评价
        public static final String CAMPAIGN = "2"; //活动评价
        public static final String TRAINING = "3"; //培训课程评价
        public static final String COURSE = "4"; //上课评价
        public static final String COACH = "5"; //教练评价
        public static final String MICROLECTURE = "6"; //微课程评价
        public static final String MICROLEC_LIKE = "7"; //微课程点赞
        public static final String VENUE_SERVICE = "8"; //场馆服务评价
        public static final String NEWS_LIKE = "9"; //新闻点赞
        public static final String ACTIVITY = "10"; //活动评价
        public static final String ACTIVITY_LIKE = "11"; //活动点赞
        public static final String YACHT = "12";//游艇评价
        public static final String COMMON_VIDEO_LIKE = "13";//视频点赞
        public static final String VIDEO = "15"; //通用视频点赞（彩票视频除外）
        public static final String GROUP_COURSE = "14"; // 团课评价
        public static final String BAIDU_LIVE = "20"; // 百度直播点赞
    }

    public static class CommentObjectType {
        public static final String LESSON_ATTN = "lesson_attn"; //上课签到评价
        public static final String MICROLECTURE = "microlecture"; //微课程
        public static final String VENUE_SERVICE = "venue_service"; //场馆服务
        public static final String ACTIVITY = "activity"; //活动评价
        public static final String ACTIVITY_LIKE = "activity_like"; //活动点赞
        public static final String NEWS = "news"; //新闻点赞
        public static final String YACHT = "yacht"; //游艇评价
        public static final String COMMON_VIDEO_RESOURCE = "common_video_resource"; //视频
        public static final String VIDEO = "video";///通用视频点赞（彩票视频除外）
        public static final String LESSON_BOOK = "book_attend"; //团课签到
        public static final String BAIDU_LIVE = "baidu_live"; //直播点赞
    }

    /**
     * 队伍类型
     */
    public static class QuQueueType {
        public static final String PLAY_PROJECT = "1"; //特色项目排队
    }

    /**
     * 队伍定义属性
     */
    public static class QuQueueDefAttr {
        public static final String PLAYER_NUM_PER_TIMES = "player_num_per_times"; //单次游玩人数
        public static final String PLAY_MINUTES_PER_TIMES = "play_minutes_per_times"; //单次游玩时间
        public static final String TOTAL_ENTER_PLAYER_NUM = "total_enter_player_num"; //每日入馆人数
        public static final String ONLINE_GET_NUMBER_TIME = "online_get_number_time"; //线上取号时间
    }

    /**
     * 排队号状态
     */
    public static class QuQueueNumState {
        public static final String INVALID = "0"; //无效
        public static final String VALID = "1"; //有效
        public static final String CANCELED = "2"; //已取消
        public static final String SIGNED = "3"; //已签到
        public static final String OVER = "4"; //已过号
    }

    /**
     * 是否随机金额配置标志
     */
    public static class RandomMoneyTag {
        public static final String RANDOM_MONEY_TAG_NO = "0"; //固定金额
        public static final String RANDOM_MONEY_TAG_YES = "1"; //随机金额
    }

    /**
     * app推荐类型
     */
    public static class AppRecommendType {
        public static final String MINI_APP_INDEX_RECOMMEND = "1"; //小程序首页推荐
    }

    /**
     * app推荐类型
     */
    public static class AppRecommendAssociatedType {
        public static final String TICKET = "ticket"; //门票
        public static final String PLAY_PROJECT_TICKET = "play_project_ticket"; //特色项目票
        public static final String SERVICE = "service"; //服务
    }

    /**
     * 队伍级别
     */
    public static class QuQueueLevel {
        public static final String CENTER = "1"; //中心
        public static final String VENUE = "2"; //场馆
        public static final String SITE = "3"; //服务点
    }

    /**
     * 营销活动属性
     */
    public static class SaleCampaignAttr {
        public static final String USE_CONDITION = "use_condition"; //使用条件
        public static final String RULE = "rule"; //规则
        public static final String EXCLUSIVE_CAMP = "exclusive_camp"; //互斥活动
        public static final String BACK_IMG = "back_img"; //背景图
        public static final String BG_COLOR = "bg_color"; //背景主色调
        public static final String INITIAL_NUM = "initial_num"; //初始数量
        public static final String SHARE_ICON = "share_icon"; //分享图标
        public static final String SHARE_TITLE = "share_title"; //分享标题
        public static final String SHARE_DESC = "share_desc"; //分享内容
        public static final String SHARE_REDIRECT = "shareRedirect"; //点赞活动结束后跳转
        public static final String ACTIVITY_GUIDELINES = "activity_guidelines"; //活动须知
        public static final String SECKILL_TYPE = "seckill_type"; //秒杀类型，1-爆款抢购，2-一元抢购
        public static final String CAMP_CONFIG_TAG = "camp_config_tag"; //蓄力活动类型 蓄力值增加类型标志 0-固定值 1-在两数之间随机生成
        public static final String CAMP_NUM = "camp_num"; //固定值
        public static final String CAMP_MIN_NUM = "camp_min_num"; //最小随机数
        public static final String CAMP_MAX_NUM = "camp_max_num"; //最大随机数
        public static final String ORDER_NUM = "order_num"; //总蓄力值
        public static final String SEND_ROLE = "send_role"; //发放对象角色，使用逗号隔开
        public static final String ACTIVITY_IMG = "activity_img"; //营销活动图片
        public static final String PRODUCT_IDS = "product_ids"; //产品列表
    }

    /**
     * 场地属性
     */
    public static class FieldAttr {
        public static final String LIGHT_RESOURCE_NO = "light_resource_no"; //灯控编号
        public static final String LIGHT_RESOURCE_NO_NEW = "light_resource_no_new"; //灯控配置，用于场地左右两个灯的情况（|场地|场地|场地|）
        public static final String LIGHT_RESOURCE_NO_CHANGSHU = "light_resource_no_changshu"; //灯控配置(常熟专用) 用于定时任务查询每个设备每天的能耗  (1-水2-电|gatewaySn|meterSn)
    }

    /**
     * 客流查询时间
     */
    public static class FlowReportTimeTag {
        public static final String DAY = "1"; // 日
        public static final String WEEK = "2"; // 周
        public static final String MONTH = "3"; // 月
        public static final String YEAR = "4"; // 年
    }

    /**
     * 销售线索属性
     */
    public static class SalesLeadsAttr {
        public static final String PER_CAPITA_BUDGET = "per_capita_budget"; //人均预算
        public static final String PARTICIPANTS_NUM = "participants_num"; //参与人数
        public static final String COMPANY_NAME = "company_name"; //公司名称
        public static final String APPLY_DATE = "apply_date"; //预约日期
        public static final String APPLY_START_TIME = "apply_start_time"; //预约开始时间
        public static final String APPLY_END_TIME = "apply_end_time"; //预约结束时间
        public static final String TOTAL_FEE = "total_fee"; //费用
    }

    /**
     * 销售线索类型
     */
    public static class SalesLeadsType {
        public static final String TEAM_BUILDING = "1"; //团建
    }

    public static class SalesLeadsState {
        public static final String HANDLE = "2"; //已经处理
        public static final String UNHANDLE = "1"; //未处理
        public static final String UNVALUE = "0"; //无效
    }

    /**
     * 赛事活动限制条件
     */
    public static class ActivityConditionType {
        public static final String ALL = "1"; //1 - 各项同时满足，否则都不得分
        public static final String ANY = "2"; //2 - 只需满足一项
        public static final String ANY_LIMITED = "3"; //3 - 其中一项不满足，该项不得分；其他项按规定场数得分，超过场次不得分
        public static final String LEVEL_LIITED = "4"; //4 - 高级别满足，低级别不满足，低级别按实际场次得分；高级别不满足，低级别满足，低级 别按规定场数得分，超过场次不得分
    }

    /**
     * 房间状态
     */
    public static class RoomOccupyState {
        public static final String FREE = "0"; //空闲
        public static final String OCCUPY = "1"; //占用
        public static final String TEMP_OCCUPY = "9"; //预占
    }

    /**
     * 黑名单products
     */
    public static class LimitListProducts {
        public static final String PRODUCTS_ALL = "ALL";
    }

    /**
     * 优惠券活动专项卡筛选
     */
    public static class CouponCampProducts {
        public static final String PRODUCTS_ALL = "ALL";
    }

    /**
     * 闸机属性
     */
    public static class AutoGateAttr {
        public static final String FIELD_IDS = "field_ids";//闸机对应的场地id
        public static final String IS_SECOND_GATE = "is_second_gate";//是不是第二道闸机 1-是

        public static final String WX_PALMS_WATER_CERT_CHECK = "wx_palms_water_cert_check";//掌纹校验深水票
    }

    public static class LightType {
        public static final String DING_RUI = "01"; //灯控类型-鼎瑞灯控
        public static final String ALI = "02"; //灯控类型-阿里体育
        public static final String XPORTS = "03"; //灯控类型-运享通
    }

    /**
     * 手环机在闸机内外参数
     */
    public static class BraceletGatePosition {
        // 手环机位于闸机里面
        public static final String INSIDE = "1";
        // 手环机位于闸机外面
        public static final String OUTSIDE = "2";
    }

    /**
     * 优惠券的属性
     */
    public static class CouponAttr {
        public static final String START_TIME = "start_time"; //优惠券可以领取开始时间 1000
        public static final String END_TIME = "end_time"; //优惠券可以领取结束时间 1400
    }

    /**
     * 培训课程状态 0-未发布 1-已发布 2-已下架
     */
    public static class TrainingCourseStatus {
        public static final String VALID = "1"; //已发布
    }

    /**
     * 批量发手环状态 0-已回收 1-使用中
     */
    public static class BatchGrantKeyState {
        public static final String RECYLED = "0";
        public static final String USED = "1";
    }

    /**
     * 校验 1-票 2-卡
     */
    public static class JudgeValidType {
        public static final String TICKET = "1"; //票
        public static final String CARD = "2"; //卡
        public static final String FACE_TOKEN = "3"; //人脸
    }

    /**
     * 元素类型
     */
    public static class FormElementType {
        public static final String PERSONAL = "1"; //个人项目
        public static final String TEAM = "2"; //团体项目
        public static final String COURSE = "3"; //课程
        public static final String COURSE_STUDENT = "4"; //课程学员信息
        public static final String COURSE_ENROLL = "5"; //课程报名信息
        public static final String PROMOTION = "7"; //营销活动
        public static final String RESERVATION_ACTIVITY = "8"; //预约活动报名信息
        public static final String ASSOCIATION_APPLY = "9"; //协会入会申请资料
        public static final String BM_FITNESS_TEST_ITEM = "10";//站点体测项目
        public static final String INSTITUTIONAL_AUDIT_REGISTRATION = "11"; //培训机构审核-登记申请信息
    }

    /**
     * 元素用途
     */
    public static class FormUseType {
        public static final String CAMP = "1"; //赛事
        public static final String COURSE = "2"; //培训
        public static final String PROMOTION = "4"; //营销
        public static final String RESERVATION_ACTIVITY = "5"; //预约活动
        public static final String ASSOCIATION_APPLY = "6"; //协会入会申请资料
        public static final String BM_SITE_TEST_ITEM = "7"; //站点体质测试
        public static final String INSTITUTIONAL_AUDIT_REGISTRATION = "8"; //培训机构审核-登记申请信息
    }

    /**
     * 元素用途
     */
    public static class FormCode {
        public static final String PROMOTION_PRESENT = "2"; //营销活动-赠送礼品
    }

    public static class FavoriteType {
        public static final String MICROLECTURE = "1"; //微课程收藏
    }

    public static class FavoriteObjectType {
        public static final String MICROLECTURE = "microlecture"; //微课程
    }

    /**
     * 预约活动时间-重复日期类型
     */
    public static class RepeatDateType {
        public static final String today = "1"; //当天
        public static final String everyday = "2"; //每天
        public static final String week = "3"; //每周
        public static final String month = "4"; //每月
    }

    /**
     * 包场类型
     */
    public static class RoomOccupyType {
        public static final String LESSON = "0"; //课时包场
        public static final String AGREEMENT = "1"; //协议包场
        public static final String CAMP = "2"; //活动包场
        public static final String SCHOOL_LESSON = "3"; //学校课包场
    }

    /**
     * 包场状态
     * 0-已取消 1-已完成 9-待支付
     */
    public static class TcRoomOccupyState {
        public static final String CANCELED = "0"; //已取消
        public static final String FINISHED = "1"; //已完成
        public static final String UNPAYED = "9"; //待支付
    }

    /**
     * 奖品类型
     */
    public static class AwardType {
        public static final String SPECIAL_CARD = "1"; //专项卡优惠
        public static final String COUPON = "2"; //优惠券

        public static final Integer TELECOM_TRAFFIC_COUPONS = 1;//实物奖品
        public static final Integer WECHAT_RED_ENVELOPE = 2;//三方奖品
        public static final Integer BLANK_AWARDS = 3;//空白奖项
        public static final Integer COUPON_AWARDS = 4;//奖品类型优惠券
    }

    /**
     * 优惠活动指定类型
     */
    public static class PromConditionType {
        public static final String NEW_USERS = "0"; // 新用户
        public static final String OLD_USERS = "1"; // 老用户
        public static final String CONDITION_CAMP = "2"; // 指定活动
    }

    public static class CheckCommentState {
        public static final String SHOW = "0"; // 展示
        public static final String NO_SHOW = "1"; // 不展示
        public static final String INVALID = "2"; // 无效
    }

    /**
     * 评论精选标识
     */
    public static class CommentSelectionTag {
        public static final String NO_SELECTION = "0"; // 非精选
        public static final String SELECTION = "1"; // 精选
    }

    /**
     * 限制新学员标识
     */
    public static class LimitNewStudentTag {
        public static final String NO_LIMIT = "0"; // 不限制
        public static final String LIMIT = "1"; // 限制
    }

    /**
     * 收费表费用类型
     */
    public static class ChargeFeeType {

        public static final String SHOWER_FEE = "01"; //淋浴费
    }

    /**
     * 秒杀产品类型
     */
    public static class SecKillProductType {
        //专项卡
        public static final String SPECIAL_CARD = "1";
        //培训课
        public static final String TRAINING_COURSE = "2";
        //优惠券
        public static final String COUPON = "3";
        //单次票
        public static final String SINGLE_TICKET = "4";
        //私教课
        public static final String PRIVATE_COURSE = "5";
    }

    /**
     * sale_unit_present 表的礼品类型
     */
    public static class UnitPresentType {
        public static final String PRODUCT = "1"; // 产品
        public static final String COUPON = "2"; // 优惠券
        public static final String RESOURSE = "3"; // 资源
    }

    /**
     * 营销商品属性
     */
    public static class SaleProductAttr {
        //单个商品每人最多购买多少
        public static final String PER_LIMIT_NUM = "per_limit_num";
        //次票秒杀产品对应的次票时段id
        public static final String TICKET_TIME_ID = "ticket_time_id";
    }

    /**
     * 营销活动状态
     */
    public static class PromotionState {
        public static final String NOT_BEGIN = "0"; //未开始
        public static final String PROCESSING = "1"; //进行中
        public static final String END = "2"; //已结束
        public static final String ARTIFICIAL_END = "3"; //手动终止
    }

    /**
     * 评价对象类型
     */
    public static class CommentDimensionObjectType {
        public static final String VENUE_SERVICE = "venue_service";
    }

    /**
     * 评价维度状态
     */
    public static class CommentDimensionState {
        public static final String INVALID = "0"; //无效
        public static final String USE = "1"; // 上架
        public static final String UNUSE = "2"; //下架
    }

    /**
     * 活动预约状态
     */
    public static class ReservationOrderState {
        public static final String INVALID = "0"; //无效
        public static final String NOT_CONFIRMED = "1"; //已预约，待确认（大连：预约ing）
        public static final String CONFIRMED = "2"; //已确认（大连：入馆ing）
        public static final String CHECKED = "3"; //已核销（大连：出馆ing）
        public static final String CANCELED = "4"; //已取消（大连：取消ing）

        public static final String ONE = "1"; //预约中

        public static final String TWO = "2"; //已核销

        public static final String THREE = "3"; //预约超时

        public static final String FOUR = "4"; //取消预约

    }

    public static class UserAddressTagType {
        public static final String SYSTEM = "1"; // 系统级标签
        public static final String USER = "2"; // 用户自定义标签
    }

    /**
     * 用户阅读类型
     */
    public static class UserReadType {
        public static final String MICROLECTURE = "2"; //微课堂
        public static final String WEB_NOTIFICATION = "3"; //Web消息通知
    }

    /**
     * 优惠券校验状态
     */
    public static class CouponCheckState {
        public static final String REMAIN_AMOUNT_NOT_ENOUGH = "1"; //剩余数量不足
    }

    //属性类型
    public class CommonAttrRelationType {
        public static final String COURSE = "0";//课程
        public static final String COACH = "1";//教练
        public static final String INST = "2";//机构
        public static final String VENUE = "3";//场馆
        public static final String PUBLIC_CAMPAIGN = "4";//赛事活动
    }

    /**
     * 区域编码
     */
    public class DistrictCode {
        public static final String CITY_LEVEL_AREA_CODE = "0";//区域编码为0代表是市等级
    }

    /**
     * 优惠券使用规则
     */
    public class CouponUseRule {
        public static final String DEFAULT_WEEK_DAYS = "1,2,3,4,5,6,7,8";
    }

    /**
     * 园秀通支付-请求地址常量类
     */
    public class YuanXiuPayURL {
        // 园秀通支付创建订单接口地址
        public static final String PING_YANG_CREATE_ORDER_URL = "https://api.pk.iparkshow.com/order/api/v1/order/createOrder";
        // 园秀通支付退款接口地址
        public static final String PING_YANG_REFUND_ORDER_URL = "https://api.pk.iparkshow.com/order/api/v1/refundOrder/refundForThird";
        // 园秀通支付关闭订单接口地址
        public static final String PING_YANG_CLOSE_ORDER_URL = "https://api.pk.iparkshow.com/order/api/v1/order/closeOrderForThird";
        // 园秀通支付-正向同步订单接口地址
        public static final String PING_YANG_POSITION_SYNC_ORDER_URL = "https://api.pk.iparkshow.com/order/api/v1/out-order/syncTradeOrderList";
        // 园秀通支付-逆向同步订单接口地址
        public static final String PING_YANG_REVERSE_SYNC_ORDER_URL = "https://api.pk.iparkshow.com/order/api/v1/out-order/syncRefundOrderList";
    }

    /**
     * 园秀通支付-约定的相关默认值
     */
    public class YuanXiuConstant {
        // 平阳全民健身中心的centerId
        public static final String PING_YANG_CENTER_ID = "33030001";
        // 约定的默认custId
        public static final String DEFAULT_CUSTOM_ID = "9999999999999999";
        // 约定的默认手机号
        public static final String DEFAULT_PHONE = "00012345678";
        // 获取一卡通账户操作记录，传入的业务类型标识，0代表每日分摊同步业务
        public static final String SHARE_OPERATION = "0";
        // 获取一卡通账户操作记录，传入的业务类型标识，1代表实时同步订单业务
        public static final String REAL_TIME_OPERATION = "1";
        // 动态SQL配置：园秀通分时分次业务同步--期间卡的拆分订单的标签（sql_label）
        public static final String SPECIAL_CARD_ORDER = "special_card_order";
        // 动态SQL配置：园秀通分时分次业务同步--次卡的拆分订单的标签（sql_label）
        public static final String TIMES_CARD_ORDER = "times_card_order";
        // 动态SQL配置：园秀通分时分次业务同步--课程消费的拆分订单的标签（sql_label）
        public static final String COURSE_CARD_ORDER = "course_card_order";

    }

    public class UnionPayConstant {
        public static final String CURRENCY_CODE = "156";
        public static final String PAY_MODE = "CODE_SCAN";
        public static final String MERCHANT_CODE = "merchantCode";
        public static final String TERMINAL_CODE = "terminalCode";
        public static final String UNION_PAY_ORDER_ID = "unionPayOrderId";
        // 小程序下单的标识
        public static final String MINI_ORDER_TYPE = "MINI";
        public static final String LONGITUDE = "longitude";
        public static final String LATITUDE = "latitude";
    }

    /**
     * 票过滤标签
     */
    public class TicketFilterTag {
        public static final String SALE = "1";//售出
        public static final String CHECKED = "2";//已验票
        public static final String OVERDUE = "3";//已过期
    }

    /**
     * 通知类型
     */
    public class MessageNotificationHistoryType {
        public static final String ACTIVITY_GUARANTEE_MESSAGE = "1";//活动保障通知
        public static final String PROPERTY_RENTAL_MESSAGE = "2";//物业租赁通知
        public static final String VISITOR_RECOVER_MESSAGE = "3";//潜客回收通知
        public static final String VISITOR_RECOVER_SOON_MESSAGE = "4";//潜客即将回收提醒通知
        public static final String VISITOR_NO_FOLLOW_MESSAGE = "5";//潜客未跟进提醒通知
        public static final String VISITOR_BIRTHDAY_MESSAGE = "6";//潜客生日提醒通知
        public static final String MEMBER_BIRTHDAY_MESSAGE = "7";//会员生日提醒通知
        public static final String MEMBER_RECOVER_MESSAGE = "8";//会员回收通知
        public static final String MEMBER_RECOVER_SOON_MESSAGE = "9";//会员即将回收提醒通知
        public static final String MEMBER_NO_FOLLOW_MESSAGE = "10";//会员未跟进提醒通知
        public static final String MEMBER_PRODUCT_EXPIRE_MESSAGE = "11";//会员产品过期提醒通知

        public static final String STUDENT_RESV = "20";//学员预约
        public static final String LEAVE_APPLY = "21";//学员请假
        public static final String LEAVE_REVOKE = "22";//学员请假撤回
        public static final String STUDENT_ENROLL = "23";//学员报名
        public static final String ENROLL_FREEZE_APPLY = "24";//学员请假
        public static final String ENROLL_FREEZE_REVOKE = "25";//学员请假撤回
    }

    public class VenueCampState {
        public static final String PENDING = "0"; //待审核
        public static final String APPROVED = "1";//已审核
        public static final String NOT_APPROVED = "2";//审核不通过
        public static final String TO_UPLOAD = "3";//待上传
    }

    /**
     * 物业租赁属性
     */
    public static class HousePropertyRentalAttr {
        public static final String CONTRACT_CODE = "contract_code";//合同编码
        public static final String FEE_UNIT = "fee_unit";//租金单位 1-月 2-年
        public static final String FEE_ALL_RENTFEE = "fee_all_rentFee";//总租金数
    }

    /**
     * 物业租赁状态
     */
    public static class HousePropertyRentalState {
        public static final String PENDING = "0"; //待审核
        public static final String APPROVED = "1";//已审核
        public static final String NOT_APPROVED = "2";//审核不通过
        public static final String RENT_CANCEL = "3";//已退租
        public static final String DELETE = "4";//删除
        public static final String TO_UPLOAD = "5";//待上传
    }

    /**
     * 租赁周期
     */
    public static class RentTerm {
        public static final String SEASON = "1"; //季度
        public static final String HALF_YEAR = "2";//半年
        public static final String YEAR = "3";//年
    }

    /**
     * 西浦订场购票选择的tab
     */
    public static class ChoiceType {
        public static final String FIELD = "1"; //订场
        public static final String TICKET = "2";//购票
        public static final String ACTIVITY = "3";//活动包场
    }

    /**
     * 场馆平台首页-租柜到期提醒的查询选修
     */
    public static class DeadlineType {
        // 到期查询
        public static final String EXPIRE = "0";
        // 过期查询
        public static final String OVERDUE = "1";
    }

    /**
     * 票咨询类型
     */
    public static class TicketAdviceType {
        // 咨询专家
        public static final String EXPERT = "1";
        // 咨询专科
        public static final String SPECIALIST = "2";
    }

    /**
     * 入馆意见类型
     */
    public static class EntryInfoAdviceType {
        public static final String HEALTH_ADVICE = "health_advice"; //咨询意见
        public static final String EXERCISE_PRESCRIPTION = "exercise_prescription"; //运动处方
    }

    /**
     * 潜水文件类型
     */
    public class DivingFileType {
        // word
        public static final String WORD = "1";
        // excel
        public static final String EXCEL = "2";
        //pdf
        public static final String PDF = "3";
        //图片
        public static final String PICTURE = "4";
        //
        public static final String VIDEO = "5";
    }

    /**
     * 潜水文件类型
     */
    public class DivingAnnualCheckEntityType {
        // divingShopId
        public static final String DIVINGSHOPID = "divingShopId";
        // divingCoachId
        public static final String DIVINGCOACHID = "divingCoachId";
    }

    /**
     * 黑名单规则类型
     */
    public class BlackListRuleType {
        // 订场不支付
        public static final String FIELD_UNPAY = "01";
        // 购票不核销
        public static final String TICKET_UNCHECK = "02";

        // 团课不核销
        public static final String GROUP_BOOK_UNCHECK = "04";
    }

    /**
     * 密码校验正则
     */
    public class RegPassword {
        //数字
        public static final String REG_NUMBER = ".*\\d+.*";
        //大写字母
        public static final String REG_UPPERCASE = ".*[A-Z]+.*";
        //小写字母
        public static final String REG_LOWERCASE = ".*[a-z]+.*";
        //特殊符号
        public static final String REG_SYMBOL = ".*[!@#$%&*]+.*";
    }

    /**
     * 教室占用类型
     */
    public class RoomStateType {
        public static final String LESSON = "0"; //课时包场
        public static final String AGREEMENT = "1"; //协议包场
        public static final String CAMP = "2"; //活动包场
        public static final String TIME_TICKET = "3"; //散客购票
        public static final String CAMP_TICKET = "4"; //活动包场票
        public static final String SCHOOL_LESSON = "5"; //学校课包场票
    }

    /**
     * 通用媒体资源对象类型
     */
    public class CommonMediaResourceObjectType {
        public static final String NEWS_MEDIA_RESOURCE = "news_media_resource";//新闻图片
        public static final String ASSOCIATION_MEDIA_RESOURCE = "association_media_resource";//协会图片
        public static final String CAMP_SPONSOR_MEDIA_RESOURCE = "camp_sponsor_media_resource";//赛事赞助商图片
        public static final String COMMENT = "comment";//评价
    }

    public class NewsType {
        public static final String APP_INDEX_NEWS = "1";//小程序首页资讯
        public static final String ANNOUNCEMENT = "2";//协会公告
    }

    /**
     * 通用媒体资源用途
     */
    public class CommonMediaResourceObjectUseType {
        public static final String DETAIL = "1";//1-详情展示
    }

    /**
     * 协会会员申请表状态
     */
    public class AsMemberApplyState {
        public static final String UN_PAY = "2";//未支付
    }

    /**
     * 协会会员申请表状态
     */
    public class AsMemberState {
        public static final String INVALID = "0";//无效
        public static final String VALID = "1";//待审核
        public static final String UN_AUDIT = "2";//待审核
        public static final String UN_PAY = "3";//未支付
    }

    /**
     * 协会会员有效时长类型
     */
    public class AsMemberExpireRuleType {
        public static final String FIXED = "1";//固定到期
        public static final String CYCLE = "2";//周期到期
    }

    /**
     * 协会属性编码
     */
    public class AssociationAttrCode {
        public static final String JOIN_NOTICE = "join_notice";//入会须知
        public static final String MEMBER_BENEFIT = "member_benefit";//会员权益
        public static final String RENEW_UPDATE_AS_MEMBER_TAG = "renew_update_as_member_tag";//入会更新会员资料
        public static final String ASSOCIATION_LEVEL = "association_level"; //协会A级
    }

    /**
     * 协会会员费用类型
     */
    public class AsMemberFeeType {
        public static final String FIXED = "1";//固定到期时间-固定收费
        public static final String DIVIDE = "2";//固定到期时间-分时段收费
        public static final String CYCLE_TIME = "3";//周期到期时间
    }

    /**
     * 专题类型
     */
    public class SubjectType {
        public static final String SUBJECT = "1";//专题类
    }

    /**
     * 专题项目类型
     */
    public class DetailType {
        public static final String OBJECT = "1";//对象类
        public static final String ATTR_ITEM = "2";//属性标签类
    }

    /**
     * 专题针对的对象类型
     */
    public class EntityType {
        public static final String COURSE = "0";//培训
        public static final String PRODUCT = "3";//产品
        public static final String GOODS = "4";//商品
        public static final String CAMPAIGN = "5";//赛事
        public static final String COUPON = "6";//优惠券
    }

    public class EnterHallTicketType {
        public static final String FIELD_TICKET = "1"; // 场地票
        public static final String SCATTERED_TICKET = "2"; // 散票（次票、包场票）
    }

    /**
     * 员工属性
     */
    public class StaffAttrCode {
        public static final String MEMBER_SEARCH_TAG = "member_search_tag"; // 会员查询标识
        // 平阳中心-管理员信息同步-标识
        public static final String PARK_SHOW_ADMIN_SYNC_TAG = "park_show_admin_sync_tag";
    }


    // 会员等级权益类型
    public class GradeBenefitType {
        // 提前订场
        public static final String ADVANCE_BOOK = "01";
    }


    // 会员权益的内容
    public class GradeBenefitContent {

        // 为订场天数
        public static final String UNRESERVEDAY = "unReserveDay";
        // 提前订场天数
        public static final String ADVANCEDAY = "advanceDay";
    }

    // 客户类型
    public static class TradeCustType {
        // 新会员
        public static final String NEW_MEMBER = "1";
        // 次新会员
        public static final String SECOND_NEW_MEMBER = "2";
        // 老会员
        public static final String MEMBER = "3";
    }

    // 客户类型
    public static class TradeCustObjectType {
        // 办卡
        public static final String PRODUCT = "1";
        // 课程
        public static final String COURSE = "2";
    }

    /**
     * 默认参数
     */
    public static class DefaultParam {
        // 青岛国信-单点登录-指定公司id
        public static final String QDGX_COMPANY_ID = "62";
        // 青岛国信-单点登录-指定部门id
        public static final String QDGX_DEPARTMENT_ID = "43";
        // 青岛国信-单点登录-用户注册时密码设置默认值
        public static final String QDGX_PASSWORD = "Gxwt@1234";
        // 青岛国信-国信通-c端用户注册时使用默认服务点
        public static final Long GXT_SITE = 1L;
    }

    /**
     * "天时同城"推送消息失败后，重推5次
     */
    public static class TnciPushDelayTime {
        public static final int FIRST_TIME = 3 * 60 * 1000;//第一次,3m
        public static final int SECOND_TIME = 3 * 60 * 1000;//第二次,3m
        public static final int THIRD_TIME = 20 * 60 * 1000;//第三次,20m
        public static final int FOURTH_TIME = 20 * 60 * 1000;//第四次,20m
        public static final int FIFTH_TIME = 20 * 60 * 1000;//第五次,20m
    }

    /**
     * 新闻内容类型
     */
    public class NewsContType {
        public static final String PICTURE_WORD = "1";//图文
    }

    /**
     * 健康跑活动属性表属性
     */
    public static class PubActivityAttr {
        public static final String ACTIVITY_TYPE = "activity_type";   //赛事活动类型
    }

    /**
     * 健康跑活动支付标志
     */
    public static class PubActivityPayStatus {
        //未支付
        public static final String UNPAID = "0";
        //正常
        public static final String NORMAL = "1";
        //取消
        public static final String CANCEL = "2";
        //已签到
        public static final String SIGNED = "3";
    }

    /**
     * 密码复杂度标识
     */
    public static class SysPasswordComplexityAttr {
        public static final String SYS_PASSWORD_COMPLEXITY_ATTR = "sys_password_complexity_attr";
    }

    /**
     * 密码复杂度标识
     */
    public static class SysPasswordComplexityTag {
        //默认4选4,8位以上且需同时包含大、小写字母、数字、符号（!@#$%&*）
        public static final String DEFAULT = "4";
        //4选2,密码为8-16位, 必须包含大小写字母、数字、符号（!@#$%&*）中至少2种（4选2）
        public static final String TWO_IN_FOUR = "2";
        //4选3,密码为8-16位, 必须包含大小写字母、数字、符号（!@#$%&*）中至少3种（4选3）
        public static final String THREE_IN_FOUR = "3";
        // 默认的4，以及不能存在四个相同的数字字母
        public static final String SAME = "5";

        // 5，以及不能连续四个连续的数字字母
        public static final String CONTINUOUS = "6";
    }

    /**
     * 一人一技-商户类型
     */
    public class PersonSkillMerchantType {
        public static final String CLUB = "6"; //俱乐部
    }

    /**
     * 订场提醒配置
     */
    public static class ServiceBookReminder {
        public static final String SERVICE_BOOKING_MANUAL = "reminderContent";
        public static final String WECHAT_MSG_SHOW_TAG = "showTag";
    }

    /**
     * 手环归还状态
     */
    public class KeyReturnTag {
        public static final String UNRETURNED = "0"; //未归还
        public static final String RETURNED = "1"; //已归还
    }

    /**
     * 国信通相关接口
     */
    public static class GxtApiConstant {
        //--公共参数
        public static final String COMMON_SETTINGS = "gxt_api_settings";
        //--会员接口
        // 会员验证接口
        public static final String MEMBER_CHECK = "gxt_member_check";
        // 会员注册接口
        public static final String MEMBER_REGISTER = "gxt_member_register";
        // OneID获取authCode接口
        public static final String GET_CODE_BY_ONE_ID = "gxt_get_code_by_one_id";
        // 会员详细信息查询接口
        public static final String QUERY_MEMBER_DETAIL = "gxt_query_member_detail";
        // authCode获取会员信息接口
        public static final String QUERY_MEMBER_BY_AUTH_CODE = "gxt_query_member_by_auth_code";
        // UnionId绑定OneId
        public static final String BINDING_ONE_ID_BY_UNION_ID = "gxt_binding_one_id_by_union_id";
        // UnionID获取会员信息接口
        public static final String GET_ONE_ID_BY_UNION_ID = "gxt_get_one_id_by_union_id";

        //--积分接口
        //积分余额查询
        public static final String ENQUIRY_ACCOUNT = "gxt_enquiry_account";
        //积分通积
        public static final String EARN_POINTS = "gxt_earn_points";
        //积分通积撤销
        public static final String REVERT_EARN_POINTS = "gxt_revert_earn_points";
        //积分通兑
        public static final String REDUCE_POINTS = "gxt_reduce_points";
        //积分通兑撤销
        public static final String REVERT_REDUCE_POINTS = "gxt_revert_reduce_points";
        //积分交易状态查询
        public static final String ENQUIRY_TRANS_STATUS = "gxt_enquiry_trans_status";

        //COMMON_SETTINGS中的参数
        public static final String PRIVATE_KEY = "privateKey";
        public static final String PUBLIC_KEY = "publicKey";
        public static final String REQUEST_PATH = "requestPath";
    }

    public static class DaHuaParkingLotUrl {
        //GET-获取accessToken
        public static final String ACCESS_TOKEN = "/ipms/subSystem/generate/token";
        //POST-停车预缴费-查询停车缴费信息
        public static final String PRE_CAR_CHARGE_INFO = "/ipms/integration/kingdo/payment/info";
        //POST-停车预缴费-缴费成功通知
        public static final String PRE_PAY_SUCCESS_NOTIFY = "/ipms/integration/kingdo/payment/success";
        //GET-会员查询
        public static final String MEMBER_QUERY = "/ipms/owner/find/";
        public static final String CAR_LIST = "/ipms/car/list";
        //        public static final String MEMBER_QUERY = "/ipms/owner/find/";
        //POST-出口缴费-获取出口车辆信息(根据道闸设备编码，获取当前出口车辆的停车收费信息)
        public static final String EXIT_DEVICE_CAR_CHARGE_INFO = "/ipms/payment/bydevice";
        //POST-出口缴费-收费成功通知(收费成功后通知停车场平台，停车场平台不会再判断收费金额是否大于等于停车费用，直接开闸放行，更新LED、余位信息等,基本等同于收费客户端的结算。)
        public static final String EXIT_DEVICE_PAY_SUCCESS_NOTIFY = "/ipms/payment/bydevice/success";
    }

    public static class JieShunParkingLotUrl {
        public static final String DATA = "DATA";//捷顺接口固定参数
        public static final String ACCESS_TOKEN = "/jsaims/login";//获取token
        public static final String SERVICE_URL = "/jsaims/as";//业务请求的路径
        public static final String JIE_SHUN_SIGN_KEY = "jie_shun_sign_key";//捷顺-请求的signKey

    }

    public static class GroupPurchaseConstants {
        //后台管理员发起拼团默认用户Id
        public static final Long ADMIN_WHITE_LIST_USER_ID = 10000L;
        //后台管理员发起拼团默认订单号
        public static final Long ADMIN_WHITE_LIST_TRADE_ID = 0L;
        //后台不限制参数
        public static final Integer NOT_LIMITED = -1;
        //初始参团人数添加延迟时间,单位毫秒
        public static final int DEFAULT_ROBOT_ADD_DELAY = 1800000;
    }

    public static class BargainConstants {
        //机器人默认用户Id
        public static final Long ADMIN_WHITE_LIST_USER_ID = 10000L;
        //发起拼团后距结束N分钟未砍价成功,添加机器人砍,默认30分钟
        public static final int DEFAULT_ROBOT_BARGAIN_DELAY = 1800000;
        //砍价随机范围默认值
        public static final int DEFAULT_RANDOM_PERCENT = 20;
        //自动砍完最大百分比
        public static final int MAX_AUTO_FINISH_PERCENT = 100;
    }

    public static class GroupPurchaseAttrCode {
        //分享标题
        public static final String SHARE_TITLE = "share_title";
        //简介
        public static final String SHARE_INFO = "share_info";
        //图片
        public static final String SHARE_PIC = "share_pic";
    }

    /**
     * 拼团商品类型
     */
    public static class GroupPurchaseRecordState {
        //拼团中
        public static final String GROUP_PURCHASING = "0";
        //已成团
        public static final String GROUP_PURCHASE_SUCCESS = "1";
        //成团失败
        public static final String GROUP_PURCHASE_FAIL = "9";
    }

    /**
     * 拼团商品类型
     */
    public static class GroupPurchaseItemType {
        //专项卡
        public static final String SPECIAL_CARD = "1";
        //课程
        public static final String COURSE = "2";
        //券
        public static final String COUPON = "3";
        //票
        public static final String TICKET = "4";

    }

    /**
     * 砍价活动状态
     */
    public class BargainActivityState {
        public static final String INVALID = "0"; //无效
        public static final String UNRELEASED = "1"; //未发布
        public static final String RELEASED = "2"; //已发布
    }

    /**
     * 砍价记录状态
     */
    public class BargainState {
        public static final String HAVE_IN_HAND = "2"; //进行中
        public static final String COMPLETED = "1"; //已完成
        public static final String EXPIRED = "0"; //已过期
    }

    /**
     * 砍价活动属性
     */
    public static class BargainActivityAttrCode {
        public static final String SHARE_TITLE = "share_title";
        public static final String SHARE_INFO = "share_info";
        public static final String SHARE_PIC = "share_pic";
    }

    public static class TermClassAttr {
        public static final String TERM_LESSON_PLAN_ID = "term_lesson_plan_id";
    }

    public static class CourseEnrollAttrCode {
        public static final String LESSON_PLAN_TIME_ID = "lesson_plan_time_id";
        public static final String LESSON_PLAN_WEEKDAY = "lesson_plan_weekday";
        public static final String ALREADY_SEND = "already_send";
    }

    /**
     * 潜在客户申请类型
     */
    public static class VisitorApplyType {
        //添加
        public static final String ADD = "1";
        // 移除
        public static final String REMOVE = "2";
        // 新增
        public static final String NEW = "3";

    }

    /**
     * 潜在客户申请审批状态
     */
    public static class VisitorApprovalState {
        //未审批
        public static final String NOT_APPROVAL = "0";
        // 通过
        public static final String APPROVALED = "1";
        // 不通过
        public static final String FAILED = "2";

    }


    /**
     * 视频来源
     */
    public static class CommonVideoResourceOrigin {
        public static final String ASSOCIATION_VIDEO = "1";
        public static final String FACILITY_VIDEO = "2";
        public static final String INSTRUCT_VIDEO = "3";
        public static final String FITNESS_VIDEO = "4";
        public static final String CAMP_PLACE_VIDEO = "5";//露营地视频
        public static final String INTELLIGENCE_GAMES_VIDEO = "6";//智运会视频
    }

    /**
     * 可上架平台业务类型
     */
    public class PlatformTradeType {
        public static final String ASSOCIATION_VIDEO = "1"; //协会视频
        public static final String TRAINING_COURSE = "2"; //培训课程
        public static final String PUB_CAMPAIGN = "3"; //赛事活动
        public static final String COUPON = "4"; //优惠券
    }


    /**
     * 产品发布对象服务
     */
    public class ProductPublishObjectType {
        public static final String ASSOCIATION_VIDEO = "1"; //协会视频
        public static final String TRAINING_COURSE = "2"; //培训课程
        public static final String PUB_CAMPAIGN = "3"; //赛事活动
        public static final String COUPON = "4"; //优惠券
    }

    public class productPublishState {
        public static final String INVALID = "0"; //无效
        public static final String VALID = "1"; //有效
        public static final String CANCEL = "2"; //已下架
        public static final String REFUSE = "3"; //已拒绝
    }

    public class CampConfigTag {
        public static final String FIXED = "0"; //固定值
        public static final String RANDOM = "1"; //随机值
    }

    /**
     * 申请信息的隐藏与显示
     */
    public class ShowHiddenItems {
        // 显示，一般是在web端显示
        public static final String SHOW = "1";
        // 隐藏，一般是在使用端隐藏
        public static final String HIDDEN = "0";
    }

    /**
     * 培训机构申请材料类型
     */
    public class InstitutionalDocType {
        public static final String APPLY_DOC = "1";//申请材料
        public static final String FILING_DOC = "2";//备案材料
        public static final String RECEIPT_DOC = "3";//备案回执材料
    }

    /**
     * 点赞-奖品配置-兑换条件-大于小于介于
     */
    public class CampAwardInfoNumType {
        public static final String LESS = "less"; //小于
        public static final String MORE = "more"; //大于
        public static final String BETWEEN = "between"; //介于
    }

    /**
     * 客户库配置-标签 0-潜客标签 1-会员标签
     */
    public class VisitorType {
        public static final String VISITOR = "0"; //潜客标签
        public static final String MEMBER = "1"; //访客标签
    }

    public static final class VenueDataMetaCollectionUrl {
        // 获取token
        public static final String GET_TOKEN = "https://mass-gym-service.sports.tyj.zj.gov.cn/sport/verification/getToken";
        // 到场核销
        public static final String DATA_META_COLLECTION = "https://mass-gym-service.sports.tyj.zj.gov.cn/api/add";
        // 场馆客流探头出入数据
        public static final String FLOW_DATA_COLLECTION = "https://mass-gym-service.sports.tyj.zj.gov.cn/api/add";
        // 场地数据
        public static final String FIELD_DATA_COLLECTION = "https://mass-gym-service.sports.tyj.zj.gov.cn/api/add";
    }

    /**
     * 国信产品和运享通产品的绑定关系
     */
    public class GxProductType {
        // 场地
        public static final String FIELD = "0";
        // 课程
        public static final String COURSE = "1";
        // 票
        public static final String TICKET = "2";
        // 产品
        public static final String PRODUCT = "3";
        // 赛事
        public static final String CAMPAIGN = "5";
    }

    /**
     * 赛事装备信息
     */
    public class Equipment{

        public static final String GENERATE_NUMBER = "generate_number";//是否生成号码牌
        public static final String NUMBER_RULE = "number_rule";//生成号码牌的规则
        public static final String GET_EQUIPMENT = "get_equipment";//是否领取装备
        public static final String GET_EQUIPMENT_START = "get_equipment_start";//领取装备开始时间
        public static final String GET_EQUIPMENT_END = "get_equipment_end";//领取装备结束时间
        public static final String GET_EQUIPMENT_ADDRESS = "get_equipment_address";//领取装备地点
    }

    /**
     * 通用比较
     */
    public class CommonCompare{

        public static final String NUMBER_ZERO = "0";
        public static final String NUMBER_ONE = "1";
        public static final String NUMBER_TWO = "2";
        public static final String NUMBER_THREE = "3";
        public static final String NUMBER_FOUR = "4";

        public static final String PSPT_TYPE = "$PSPT_TYPE";//证件类型
        public static final String GENDER = "$GENDER";//性别

        //赛事inputType类型
        public static final String NAME = "$NAME";//姓名
        public static final String PHONE = "$PHONE";//手机号
        public static final String PSPT_ID = "$PSPT_ID";//身份证号
        public static final String TEAM_NAME = "$TEAM_NAME";//队伍名称


    }

    /**
     * 教案管理功能明细
     */
    public class TcPlanType {
        public static final String COACH = "教练";
        public static final String VENUE = "场馆";
    }

    public class TcPlanQueryTag {
        public static final String VENUE = "1";
        public static final String COACH = "2";
    }

    /**
     * 砍价活动商品扩展属性
     */
    public static class BargainActivityItemAttr{
        // 帮砍人数
        public static final String BARGAIN_HELPER_NUM = "bargain_helper_num";
        // 自动砍完
        public static final String AUTO_FINISH = "auto_finish";
        // 自动砍完比例
        public static final String AUTO_FINISH_PERCENT = "auto_finish_percent";
        // 随机比例 默认20%
        public static final String RANDOM_PERCENT = "random_percent";
    }

    /**
     * 退票类型
     */
    public static class RefundTicketType {
        // 不可退
        public static final String CAN_NOT_REFUND = "0";
        // 固定金额
        public static final String FIXED_AMOUNT = "1";
        // 固定比例
        public static final String FIXED_RATIO = "2";
        // 阶梯收费
        public static final String LADDER_CHARGE = "3";
    }

    public static class PublicCampElementId {

        //9801	队伍名称
        public static final Long TEAM_NAME = 9801L;
        //9802	领队姓名
        public static final Long TEAM_LEADER_NAME = 9802L;
        //9803	联系电话
        public static final Long TEAM_PHONE = 9803L;
        //9804	证件类型
        public static final Long TEAM_PSPT_TYPE= 9804L;
        //9805	证件号码
        public static final Long TEAM_PSPT_ID = 9805L;
        //9806	性别
        public static final Long TEAM_GENDER = 9806L;

        //9901	参赛者姓名
        public static final Long PLAYER_NAME = 9101L;
        //9902	性别
        public static final Long PLAYER_GENDER = 9902L;
        //9903	手机号
        public static final Long PLAYER_PHONE = 9903L;
        //9904	证件类型
        public static final Long PLAYER_PSPT_TYPE = 9904L;

        //9905	证件号码
        public static final Long PLAYER_PSPT_ID = 9905L;

    }

    /**
     * 赠送课程状态
     */
    public static class FreeCourseState {
        // 无效 （还未兑换过）
        public static final String INVALID = "0";
        // 待使用
        public static final String TO_BE_USED = "1";
        // 已兑换
        public static final String USED = "2";
        // 取消了
        public static final String CANCEL = "3";
    }

    public static class TreadmillService {

        //设置二维码服务
        public static final String setQRCode = "/setQRCode";
        //设备解锁服务
        public static final String signIn = "/signIn";
        //设备退出登录服务
        public static final String signOut = "/signOut";
        //获得本次运动数据服务
        public static final String getSportData = "/getSportData";
        //获取历史运动数据服务
        public static final String getSportAllData = "/getSporAlltData";
        // 获取最后一条实时数据（拿坡度）
        public static final String GET_LAST_REAL_DATA = "/getLastRealDATA";
        //请求成功编码
        public static final String successfulCode = "10001";
        //请求失败编码
        public static final String unSuccessfulCode = "10002";
        //默认的设置二维码时间
        public static final String TREADMILL_QRCODE_TIME = "3";
        //通用的请求v2
        public static final String TREADMILL_SERVER_URL = "https://mes.ironmanapi.com/IronmanWebService/v2/ironman.asmx";
        //v1可以设置二维码（目前没用到）
        public static final String TREADMILL_SERVER_URL_v1 = "https://mes.ironmanapi.com/IronmanWebService/v1/ironman.asmx";

    }


    public static class HealthCode {
        //城市编码
        public static final String CITY_NO = "341100";
        //核验站点Id
        public static final String SITE_ID = "29004f92732e447183ed3477b2f246f5";
        //业务成功编码
        public static final String SUCCESS_CODE = "200";
        //中心参数  核验点检查人员姓名或核验点设备名称
        public static final String CHECK_USR_NAME = "check_usr_name";
        //中心参数  核验点检查人员身份证号码或核验点设备编码
        public static final String CHECK_USR_ID_CARD = "check_usr_card";
        public static final String ANHUI_PROVINCE_SERVER_ADDRESS = "anhui_province_server_address";
        public static final String ANHUI_PROVINCE_STADIUM_NO = "anhui_province_stadium_no";
        //滁州体育馆标识
        public static final String CHUZHOU_SPORTS_TAG = "chuzhou_sports_tag";
        public static final String ANKANG_CODE_TEST_TAG = "ankang_code_test_tag";
        //滁州全民日保存身份证
        public static final String CHUZHOU_HOLIDAY_ENTRY_PSPTID = "chuzhou_holiday_entry_psptId";

        // 异常码测试开关测试
        public static final String TEST_CODE_INFO_YELLOW = "test_code_info_yellow";

    }

    public static class SocialSecurityCards{

        //服务器地址
        public static final String BASE_URL = "http://*************:8888/prod-api";
        //获取token的服务项
        public static final String GET_TOKEN_SERVER = "/i/token/getTokenByUser";
        //获取用户详细信息服务
        public static final String GET_DETAIL_INFO = "/i/ecard/getDetailInfo";
        //登录用户名
        public static final String USER_NAME = "chuzhou_social_security_cards_user_name";
        //登录密码
        public static final String PASS_WORD = "chuzhou_social_security_cards_pass_word";
        //Rsa密钥ID
        public static final String KEY_ID = "chuzhou_social_security_cards_key_id";
        //公钥
        public static final String PUBLIC_KEY = "chuzhou_social_security_cards_public_key";
    }

    public static class SalaryCalcRecordState {
        // 计算中
        public static final String CALCING = "0";
        // 计算成功
        public static final String CALC_SUCCESS = "1";
        // 计算失败
        public static final String CALC_FAILED = "2";
        // 删除
        public static final String DELETED = "3";

    }

    /**
     * 日期范围
     */
    public static class DateRange {
        public static final String THIS_MONTH = "1"; // 本月
        public static final String LAST_MONTH = "2"; // 上月
        public static final String THIS_QUARTER = "3"; // 本季度
        public static final String LAST_QUARTER = "4"; // 上季度
    }

    /**
     * 机构申请审核进度
     */
    public static class InstitutionalApplyAuditType {
        public static final String TO_BE_SUBMITTED = "0"; // 待提交
        public static final String PENDING_PRELIMINARY_REVIEW = "1"; // 待初审
        public static final String TO_BE_REVIEWED_ON_SITE = "2"; // 待实地审核
        public static final String REJECTED = "3"; // 初审驳回
        public static final String TO_BE_FILED = "4"; // 待备案
        public static final String FILED = "5"; // 已备案
        public static final String FILING_REJECTED = "6"; // 备案驳回
    }

    /**
     * 机构申请审核结果
     */
    public static class AuditResult {
        public static final String APPROVAL = "1"; // 审核通过
        public static final String REJECT = "2"; // 审核不通过
    }

    /**
     * 重庆奥体智慧停车停车券服务
     */
    public static class AotiParkingCoupons {
        //商户号
        public static final String MERCHANT_NO = "aoti_merchant_no";
        //渠道号
        public static final String APP_ID = "aoti_app_id";
        //商户秘钥
        public static final String MERCHANT_KEY = "aoti_merchant_key";
        //（加密之后）车场标识
        public static final String AOTI_PARK_ID = "aoti_park_id";
        //（优免商户标识）加密之后
        public static final String AOTI_BUSINISS_ID = "aoti_business_id";

        // 订单保存的购买信息
        public static final String ORDER_COUPONS_INFO = "order_coupons_info";
        public static final String ORDER_COUPONS_WITH_USER = "order_coupons_with_user";

        //测试开关  控制请求服务地址
        public static final String AOTI_PARKING_COUPONS_TEST_TAG = "aoti_parking_coupons_test_tag";
        //服务地址
        public static final String SERVICE_ADDRES_TEST = "http://mapi.qa.etcp.cn";//测试地址
        public static final String SERVICE_ADDRES_PRD = "https://mapi.etcp.cn";//正式地址
        //登录服务
        public static final String USER_LOGIN = "/merchant/open/1.0.0/openapi/usersigin";
        // 商家优免券模板查询
        public static final String COUPONS_LIST = "/merchant/open/1.0.0/openapi/b/coupon/list";
        //商家优免券发放
        public static final String COUPONS_RECORD = "/merchant/open/1.0.0/openapi/b/coupon/record";

    }

    /**
     * 设备类型
     */
    public class OuterEquipmentType {
        public static final String BINOCULAR = "0";//双目
        public static final String FACE_RECOGNITION = "1";//人脸
        public static final String VERIFICATION = "2";//核验
    }

    /**
     * 设备类型
     */
    public class CustomerFlowEquipmentBrandId {
        public static final String WEN_ZHUN = "稳准";
        public static final String HIKVISION = "海康";
        public static final String TU_PU = "图谱";
        public static final String DA_HUA = "大华";
    }

    /**
     * 客流上传方式
     */
    public class UploadPlatform {
        public static final String JIANGSU = "0";//江苏省平台
        public static final String JS365 = "1";//国家平台
        public static final String HUBEI = "2";//湖北省平台
    }

    /**
     * treadmillAttr表的code
     */
    public static  class TreadmillAttrCode{
        public static final String FILE_ID = "file_id";
    }

    public static final class SalaryCalcExprParamType {
        public static final String CONSULTANT_SALE = "1";
        public static final String COACH_COURSE = "2";
        public static final String PERFORMANCE = "3";
        public static final String GENERIC_PERFORMANCE = "4";
    }

    public static final class SalaryTemplateCustomItemConstants {
        public static final String MONTH_BASED_PAY_ROLL = "0";//月薪制
        public static final String DAY_BASED_PAY_ROLL = "1";//日薪制
    }

    /**
     * 一卡通二维码缓存失效单位
     */
    public static class EcardQrcodeCacheUnit {
        public static final String SIXTY = "01";//缓存时长是1分钟
        public static final String FIFTEEN = "02"; // 缓存时长是15s
    }

    /**
     * 安徽省智慧体育平台
     */
    public static final class AnHuiProvince {
        // 通过安康码获取用户信息服务
        public static final String GET_USER_INFO = "/qrCode/transUserInfo";
        // 发送订单信息
        public static final String SEND_ORDER_INFO = "/order/saveCustomerOrderInfo";
        // 订单核销接口
        public static final String CHANGE_ORDER_STATUS = "/order/writeOffCustomerOrderInfo";
        // 客户入馆行为数据录入接口（入馆记录）
        public static final String CUSTOMER_ENTRY_INFO_UPLOAD = "/stadium/saveCustomerEnterGymInfo";
        // 客户消费行为数据录入接口（场馆消费记录）
        public static final String CUSTOMER_CONSUME_INFO_UPLOAD = "/stadium/saveCustomerOrderInfo";
        // 安徽省平台的tag标记
        public static final String ANHUI_PROVINCE_TAG = "anhui_province_tag";
        // 安徽省平台入馆行为录入已同步标志
        public static final String ALL_USER_ENTRY_INFO_UPLOAD_TAG = "all_user_entry_info_upload_tag";
        // 订单相关的网络用户id
        public static final String ANHUI_PROVINCE_TRADE_NET_USER_ID = "anhui_province_trade_net_user_id";
    }

    /**
     * 资源领取信息的状态
     */
    public static class ShareDepositDetailState {
        // 未使用
        public static final String NOT_USE = "0";
        // 已使用
        public static final String USED = "1";
        // 已二次分享
        public static final String SHARED = "2";
        // 已过期
        public static final String INVALID = "3";
    }

    public static final class SalaryCalcJobConstants {
        /*------------------- 任务状态 -------------------*/
        public static final String DISABLED = "0";//停用
        public static final String ENABLED = "1";//启用
        public static final String DELETED = "9";//删除
        /*------------------- 执行薪酬所属月 -------------------*/
        public static final String EXEC_CURRENT_MONTH = "0";//当前月
        public static final String EXEC_LAST_MONTH = "1";//上月
        public static final String EXEC_BEFORE_LAST_MONTH = "2";//上上月
    }
    public static class KeyStorageType {
        // 批量导入
        public static final String EXCEL = "0";
        // 手动刷入
        public static final String MANUAL = "1";
    }


    public static final class HumanResourceConstants{
        public static final String EXPR_PARAM_PREFIX_V2 = "a";// 参数公式的固定前缀
    }

    /**
     * 滁州特殊人群标志
     */
    public static final class specialPeopleTag{
        public static final String OPEN = "1";// 开启
        public static final String CLOSE = "0";// 关闭
    }

    /**
     * 西浦接口地址
     */
    public class xjtluUrl {
        public static final String STAFF_INFO_URL = "/staffs/STAFFID/basic"; //Staff认证，通过ad账号获取它是否状态正常和是否是校外导师。
        public static final String STUDENT_INFO_URL = "/students/STUDENTID/basic"; //Student认证，通过ad账号获取它是否状态正常和是否是校外导师。
        public static final String CARD_INFO_URL = "/campus/onecards/HEXID"; //根据卡信息查询账号信息
        public static final String STUDENT_PHOTO_URL = "/students/STUDENTID/profilephoto"; //查询学员账户图片
        public static final String STAFF_PHOTO_URL = "/staffs/STAFFID/profilephoto"; //查询员工账户图片
        public static final String MENTORS_INFO_URL = "/external/mentors/CITIZENSHIPID/information/to-sports-centre"; //获取校外导师基本信息

        // 西浦单点登录
        public static final String NET_SSO_USER_LOGIN_BASE_URL = "net_sso_user_login_base_url";
        public static final String NET_SSO_USER_LOGIN_PUBLIC_KEY = "net_sso_user_login_public_key";
        public static final String NET_SSO_USER_LOGIN_APP_ID = "net_sso_user_login_app_id";


    }




    /**
     * 签署附件类型
     */
    public static final class  AttachmentType{
        public static final String AGREEMENT = "1";// 协议
        public static final String Medical_Examination_Report = "2";// 体检报告
    }

    /**
     * 冻卡类型
     */
    public static final class FrozenCardType{
        public static final String NORMAL = "1"; // 普通冻卡
        public static final String SPECIAL = "2"; // 特殊冻卡
        public static final String REFREEZING = "3"; // 补冻卡
    }

    /**
     * 购票限制类型
     */
    public static final class RestrictionType{
        public static final String TICKET = "ticket"; // 次票限制
        public static final String FIELD = "field"; // 场地限制
    }

    /**
     * 排名类型
     */
    public static final class RankingType{
        public static final String ATTEND_CLASS = "1"; // 上课数量榜
        public static final String TASK_COMPLETE = "2"; // 作业完成榜
    }


    public class RoomAttr {
        public static final String SEAT_ROW = "seat_row";
        public static final String SEAT_COLUMN = "seat_column";


    }

    /**
     * 活动类型
     */
    public static class ChangeWeChatType {
        // 爆款
        public static final String SECKILL = "seckill";
        // 砍价
        public static final String PDD = "pdd";
        // 拼团
        public static final String WHOLESALE = "wholesale";
    }

    /**
     * 活动类型
     */
    public static class BaiduLiveState {
        // 待支付
        public static final String WAIT_PAY = "0";
        // 支付完成待开始
        public static final String PAID_TO_START = "1";
        // 直播中
        public static final String LIVE = "2";
        // 直播结束
        public static final String FINISH = "3";
        // 退款取消
        public static final String REFUND = "8";
        // 订单超时取消
        public static final String EXPIRED = "9";
    }

    /**
     * 行为验证码接口地址
     */
    public static class CaptchaUrl {
        public static final String GEN_RANDOM_URL = "genRandom"; //获取随机样式验证码
        public static final String GEN_URL = "gen"; //获取指定样式验证码
        public static final String CHECK_URL = "check"; //校验验证码
        public static final String CAPTCHA_TYPES = "captcha_types"; //验证码类型配置默认("0,1,2,3")
    }

    public static class ThirdPointType {
        public static final String CHANG_SHU = "1"; //常熟海棠铺子积分
    }

    public static class ChangShuAssociationState {
        // 正常
        public static final String VALID = "1";
        // 无效
        public static final String INVALID = "0";
        // 审核
        public static final String AUDIT = "2";
    }

    public static class BookingManualType {
        // 正常
        public static final String REMINDER = "1";
        // 订场提醒
        public static final String NOTIFY = "2";
    }

    /**
     * 水立方-验证识别类型
     */
    public static class WaterCertificateType {
        // 深水证单日
        public static final String WATER_CERTIFICATE_ONE_DAY = "1";
        // 深水证三年
        public static final String WATER_CERTIFICATE_THREE_YEAR = "2";
        // 结课考试
        public static final String END_CLASS = "3";
    }

    /**
     * 视频资源类型
     */
    public static class CommonVideoResourceType {
        public static final String VIDEO = "1";//视频
        public static final String IMAGE = "2";//图文
    }


    public static class EnrollFreezePersonType{
        // 客服
        public static final String STAFF = "1";
        // 用户
        public static final String USER = "2";
    }
}
