import $ from 'jquery'
import intercept from 'utils/intercept'

export function queryStaffResourceVenueList() {
   return $.get('/commonParam/queryStaffResourceVenueList').then(intercept)
}

export function removeSalesLeads(data) {
    return $.get('/salesLeadsManage/removeSalesLeads', data).then(intercept)
}

export function findSalesLeadsById(data) {
    return $.get('/salesLeadsManage/findSalesLeadsById', data).then(intercept)
}

export function updateSalesLeadsState(data) {
    return $.get('/salesLeadsManage/updateSalesLeadsState', data).then(intercept)
}

export function saveSalesLeads(data) {
    return $.post('/salesLeadsManage/saveSalesLeads', data).then(intercept)
}

export function findSalesLeads(data) {
    return $.ajax({
        type: 'get',
        url: '/salesLeadsManage/findSalesLeads',
        data,
        setHash: true,
    })
}