/**
 * Created by pei on 2015/5/25.
 */
import Vue from 'vue'
import 'frame'
import EcardSearch from 'business/EcardSearch.vue'
import FuzzyResult from 'business/FuzzyResult.vue'
import { VueRead } from 'utils/vueReadMixins.js'

const $ = require('jquery')

const batchCouponList = require('./components/batchCouponList.vue')
const settings = require('aisportsConfig');
const select = require('select');
const mAlertInstance = require('mAlert');

require('qselect');
require('textareaCount');
require('frame');
require('toolTip');

$(document).on('click', '.btnConfirm', () => {
    const userStatus = $('[name=userStatus]').val() || '';
    if (userStatus == '1') {
        mAlertInstance.showWarning('卡已挂失！');
        return;
    }
    $('.cover-box-spe').show();
});

// 确认发放优惠券弹出框的取消按钮
$(document).on('click', '.btn-cancel', function () {
    const _this = $(this);
    _this.parents('.cover-box').hide();
    location.reload();
});

// 确认发放优惠券弹出框的确认按钮
$(document).on('click', '.btn-confirm', function () {
    const _this = $(this);
    _this.parents('.cover-box').hide();
    const campaignId = $('.select-picker option:selected').val();
    const couponId = $('#couponName').val();
    let custName,
        contactPhone,
        ecardNo,
        psptId,
        psptType;
    const remark = $('#remark').val();
    if ($('#memberId').val() == 0) {
        custName = $('#name').html();
        contactPhone = $('#phone').html();
        ecardNo = $('#cardNo').html();
    } else {
        custName = $('#username').val();
        contactPhone = $('#normalphone').val();
        ecardNo = '';
    }
    psptId = $('#psptId').val() || undefined;
    const num = $('.quantity-form').find('.quantity-text').val();
    if (num == 0) {
        mAlertInstance.showWrong('请选择领取优惠券数');
        return;
    }
    if (couponId == '') {
        mAlertInstance.showWrong('请选择优惠券');
        return;
    }
    $.ajax({
        url: '/couponCampaign/getCoupon',
        type: 'post',
        data: {
            campaignId,
            couponId,
            psptId,
            psptType,
            ecardNo,
            custName,
            phoneCode: contactPhone,
            num,
            remark,
        },
        success(data) {
            if (data.error == '0') {
                mAlertInstance.showSuccess('优惠券发放成功!');
                location.reload();
            } else {
                mAlertInstance.showWrong(data.message);
            }
        },
        error() {
        },

    });

});

/* 绑定加减事件 */
function handleBuyTicket() {
    $(document).on('click', '.decrement', function (e) {
        e.preventDefault();
        let _ipt = $(this).closest('.quantity-form').find('.quantity-text'),
            value = +_ipt.val(),
            newVal = value > 0 ? value - 1 : 0;

        _ipt.val(newVal);
    });
    $(document).on('click', '.increment', function (e) {
        e.preventDefault();

        let _ipt = $(this).closest('.quantity-form').find('.quantity-text'),
            value = +_ipt.val(),
            newVal = value < 10000 ? value + 1 : 10000;

        _ipt.val(newVal);
    });
}

/**
     * 初始化tooltip
     */
function initToolTipForInputs() {
    $('#normalPsptId').tooltip({
        trigger: 'manual',
    });
    $('#normalphone').tooltip({
        trigger: 'manual',
    });
}


// 级联查询
window.changeCoupon = function () {
    const campaignId = $('.select-picker option:selected').val();
    $('#couponName').easyDropDown('destroy');
    const gglbType = $('#couponName');
    gglbType.find('option').remove();
    const opt = $('<option>').text('请选择电子券').val('');
    opt.appendTo(gglbType);
    $.ajax({
        url: '/couponCampaign/couponSelectByCompainId',
        type: 'post',
        data: { campaignId },
        success(data) {
            $.each(data.couponList, (k, v) => {
                if (v != null) {
                    const opt = $('<option>').text(v.couponName).val(v.couponId);
                    opt.appendTo(gglbType);
                }
            });
            $('#couponName').easyDropDown();
        },
        error() {
            settings.spinner.showWarning('级联错误');
        },

    });
};


// 套用已有的去查询用户信息，ecardNo
$(document).on('click', '#searchBtn', () => {
    queryCustInfo($('#ecardNo').val());
});

function query() {
    $('#searchBtn').trigger('click')
}

window.SEARCHBYID = function (cardno) {
    EcardSearchVue.cardNo = cardno
    $('#ecardNo').val(cardno)
    EcardSearchVue.enterCardNo()
}
// 根据选择的活动查询活动具体信息
window.selectCoupon = function () {
    const campaignId = $('.select-picker option:selected').val();
    const campaignName = $('.select-picker option:selected').text();
    const couponId = $('.selecter option:selected').val();
    const couponName = $('.selecter option:selected').text();
    if (campaignId == 0) {
        mAlertInstance.showWrong('请先选择活动');
        return;
    }
    if (couponId == 0) {
        mAlertInstance.showWrong('请选择电子券');
        return;
    }

    $('#couponName').easyDropDown('destroy');
    $.ajax({
        url: '/couponCampaign/selectCoupon',
        type: 'post',
        data: { couponId, campaignId },
        success(data) {
            if (data.coupon != null) {
                $('#memberQueryResultBox').show();
                $('#coupon-Name').html(data.coupon.couponName);
                // 券类型
                if (data.coupon.valueType != '' && data.coupon.valueType == '1') {
                    $('#valueType').html('代金券');
                } else if (data.coupon.valueType == '2') {
                    $('#valueType').html('次票');
                } else if (data.coupon.valueType == '3') {
                    $('#valueType').html('兑换券');
                }
                // 券价值
                if (data.coupon.value != '') {
                    if (data.coupon.valueType == '1') {
                        $('#value').html(`${data.coupon.value / 100}元`);
                    } else if (data.coupon.valueType == '2' || data.coupon.valueType == '3') {
                        $('#value').html(data.coupon.ticketValue);
                    }
                }
                // 有效期
                if (data.coupon.validDays > 0) {
                    $('#validDays').html(`${data.coupon.validDays}天`);
                }
                // 使用开始时段
                if (data.coupon.startSegment >= 0) {
                    $('#startSegment').html(`${data.coupon.startSegment}点`);
                }
                // 使用结束时段
                if (data.coupon.endSegment >= 0) {
                    $('#endSegment').html(`${data.coupon.endSegment}点`);
                }
                $('#weekDays').html(data.coupon.weekDays);
                // 节假日
                if (data.coupon.holidayTag != '') {
                    if (data.coupon.holidayTag == '1') {
                        $('#holidayTag').html('可以');
                    } else if (data.coupon.holidayTag == '0') {
                        $('#holidayTag').html('否');
                    }
                }
                $('#effectiveDate').html(data.coupon.effectiveDate);
                $('#expireDate').html(data.coupon.expireDate);
            }
            // 再次拼接电子券OPTION
            const gglbType = $('#couponName');
            gglbType.find('option').remove();
            $.each(data.couponList, (k, v) => {
                if (v != null) {
                    if (v.couponId == couponId) {
                        var opt = $("<option selected='selected'>").text(v.couponName).val(v.couponId);
                        opt.appendTo(gglbType);
                    } else {
                        var opt = $('<option>').text(v.couponName).val(v.couponId);
                        opt.appendTo(gglbType);
                    }

                }
            });
            $('#couponName').easyDropDown();
        },
        error() {
        },

    });
};

// 会员点击
$(document).on('click', '#memberRole', () => {
    location.href = `${settings.urlPrefix}/couponCampaign/coupon?memberId=0`;
});

// 非会员点击
$(document).on('click', '#notMemberRole', () => {
    location.href = `${settings.urlPrefix}/couponCampaign/coupon?memberId=1`;
});

// 批量
$(document).on('click', '#batchMember', () => {
    location.href = `${settings.urlPrefix}/couponCampaign/coupon?memberId=2`
})


// 非会员身份证校验
window.judgePsptId = function () {
    // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    const card = $('#psptId').val();
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (reg.test(card) === false) {
        $('#psptId').addClass('error');
        $('.btnConfirm').addClass('disabled');
        $('#psptId').tooltip('show');
    } else {
        $('#psptId').removeClass('error');
        $('#psptId').tooltip('hide');
        $('.btnConfirm').addClass('actived').removeClass('disabled');
    }
};

// 非会员手机号码校验
window.judgePhone = function () {
    // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
    const card = $('#normalphone').val();
    ecard = $('#normalphone');
    const reg = settings.regs.mobilphone;
    if (reg.test(card) === false) {
        $('#normalphone').addClass('error');
        $('#normalphone').tooltip('show');
        $('.btnConfirm').addClass('disabled');
        return false;
    }
    $('#normalphone').removeClass('error');
    $('#normalphone').tooltip('hide');
    $('.btnConfirm').addClass('actived').removeClass('disabled');

};

// text area剩余字数提示
function handleTextAreaIpt() {
    $('#remark').textareaCount();
}

/**
     * 查询用户信息
     */
function queryCustInfo() {
    settings.filterEcardNo(() => {
        $.get('/customer/queryCustInfo', { ecardNo: $('#ecardNo').val() }, (data) => {
            $('#custInfo').empty();
            $('#custInfo').html(data);
            if ($(data).find('#cardNo').html().length <= 0) {
                settings.spinner.showErrorResult('未查询到客户信息！');
                return;
            }
            const userStatus = $('[name=userStatus]').val() || '';
            if (userStatus == '1') {
                settings.spinner.showErrorResult('卡已挂失！');
            }
        });
    })
    
}


$(() => {
    handleBuyTicket();
    handleTextAreaIpt();
    $('.select-picker').easyDropDown();
    $('#couponName').easyDropDown();
    initToolTipForInputs();
});


let EcardSearchVue = new Vue({
    el: '#bach-send-coupon',
    components: {
        'batch-list': batchCouponList,
        EcardSearch,
        FuzzyResult,
    },
    data:{
        cardNo: ''
    },
    mixins:[VueRead],
    mounted () {
        this.getCardId('#advanced-query','#adInfoInput','#superSearchBox', false, {
            value:'#ecardNo',
            search: '#searchBtn'
        });
        this.queryCallback('cardNo', '#ecardNo', '#searchBtn', null, this)
    },
    methods:{
        isRoot() {
          const doms =   document.querySelectorAll('.small-tab .title-content')
          for(let i = 0; i <= doms.length - 1; i++) {
              const attrFlag =doms[i].getAttribute('class').includes('cur')
              const idName = doms[i].getAttribute('id')
            //   console.log(doms[i].getAttribute('class').includes('cur'),'s',doms[i].getAttribute('id'))
              if(attrFlag&&idName ==='notMemberRole') {
                  return true
              }
          }
          console.log(doms,'domes')
        },  
        enterCardNo: query,
    }
})

