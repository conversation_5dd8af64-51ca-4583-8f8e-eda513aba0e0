package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.annotation.IncludeRepeat;
import com.asiainfo.aisports.annotation.RepeatSubmit;
import com.asiainfo.aisports.domain.core.Perform;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.project.SeatImpDto;
import com.asiainfo.aisports.model.project.SeatImpResult;
import com.asiainfo.aisports.service.ProjectManageService;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.project.ProjectSeatService;
import com.asiainfo.aisports.tools.JacksonUtil;
import com.asiainfo.aisports.utils.ExcelParser;
import com.asiainfo.aisports.web.WebResult;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/3 17:15
 */
@RequestMapping("/projectSeatManage")
@Controller
public class ProjectSeatManageController {


    private static final Logger logger = LoggerFactory.getLogger(ProjectSeatManageController.class);

    @Autowired
    private ProjectSeatService projectSeatService;

    @Autowired
    private ProjectManageService projectManageService;


    @RequestMapping("/exportSeatTemplate")
    public ResponseEntity<byte[]> exportTemplate(MultipartFile file, LoginStaff staff, Long stockId) throws Exception {

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachment", new String("座位导入模板".getBytes("gb2312"), StandardCharsets.ISO_8859_1) + ".xls");
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        byte[] buff = null;
        try {
            String[] headList = new String[]{"区域名称","排数", "起始座位号", "结束座位号", "盲区座位号码", "小计"};
            String[] keyList = new String[]{"stock_name","row", "startSeat", "endSeat", "invalidSeat", "num"};

            buff = ExcelParser.dataToExcel(projectSeatService.getSeatTemplate(stockId), headList, keyList, null);
        } catch (Exception e) {
            logger.error("", e);
        }
        return new ResponseEntity<>(buff, httpHeaders, HttpStatus.OK);
    }


    @RequestMapping("/importSeatTemplate")
    public ResponseEntity<String> importSeatTemplate(HttpServletRequest request, HttpServletResponse response,
                                                     MultipartFile file, LoginStaff staff, Long stockId) throws Exception {


        request.setCharacterEncoding("utf-8");
        response.setHeader("X-Frame-Options", "SAMEORIGIN");
        WebResult result = new WebResult();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("Content-Type", "text/plain; charset=utf-8");
        List<SeatImpDto> rowList = null;
        try {
            rowList = ExcelParser.parseExcelFile(SeatImpDto.class, file, new String[]{"stockName","row", "startSeat", "endSeat", "invalidSeat", "num"});
        } catch (Exception e) {
            logger.error("stockId=[{}] parse excel error", stockId, e);
            result = new WebResult(1, "数据内格式错误");
            return new ResponseEntity<>(JacksonUtil.writeObj2Json(result), httpHeaders, HttpStatus.OK);

        }
        if (CollectionUtils.isEmpty(rowList)) {
            result = new WebResult(1, "未解析到数据");
            return new ResponseEntity<>(JacksonUtil.writeObj2Json(result), httpHeaders, HttpStatus.OK);
        }

        List<SeatImpResult> errorList = projectSeatService.importExcel(stockId, rowList, staff);
        if (!errorList.isEmpty()) {
            result.set("errorList", errorList);
        }


        return new ResponseEntity<>(JacksonUtil.writeObj2Json(result), httpHeaders, HttpStatus.OK);
    }


    @RequestMapping("/getImplList")
    @ResponseBody
    public WebResult getImplList(LoginStaff staff, Long stockId, Integer row, Integer seat,
                                 @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                 @RequestParam(value = "pageSize", defaultValue = "10") int pageSize) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectSeatService.getSeatImplInfo(stockId, row, seat, pageNum, pageSize);
        webResult.putAll(serviceResult);
        return webResult;
    }



    @RequestMapping("/getTakeLogPageList")
    @ResponseBody
    public WebResult getTakeLogPageList(String projectId, String performId, Long stockId, String state,
                                        @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) Date startDate,
                                        @DateTimeFormat(pattern = "yyyy-MM-dd") @RequestParam(required = false) Date endDate,
                                        @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                        @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                        LoginStaff staff) {

        List<DataMap> logList = projectSeatService.getTakeLogPageList(projectId, performId, stockId, state, startDate, endDate, pageNum, pageSize);
        return new WebResult().set("logList", logList);

    }


    @RequestMapping("/getCandidateSeatList")
    @ResponseBody
    public WebResult getCandidateSeatList(String performId, LoginStaff staff) {
        return new WebResult().set("seatList", projectSeatService.getCandidateSeatList(performId, staff));
    }


    @RequestMapping(value = "/takeSeat",method = RequestMethod.POST)
    @ResponseBody
    public WebResult takeSeat(@IncludeRepeat String performId, Long[] stockIds, LoginStaff staff) {
        if (stockIds == null) {
            return new WebResult(1, "请选择票区");
        }

        projectSeatService.takeSeat(performId, stockIds, staff);
        return new WebResult();

    }





}
