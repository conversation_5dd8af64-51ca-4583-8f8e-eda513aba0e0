package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.SalaryTemplateCustomItem;
import com.asiainfo.aisports.domain.core.SalaryTemplateItem;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.SalaryTemplateCustomItemInfo;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface SalaryTemplateCustomItemMapper {
    @Delete({
            "delete from salary_template_custom_item",
            "where item_id = #{itemId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long itemId);

    @Insert({
            "insert into salary_template_custom_item (item_id, salary_template_id, ",
            "item_name, start_index, ",
            "start_date, end_index, end_date, ",
            "pay_roll, expr_id, state, ",
            "update_time, update_staff_id)",
            "values (#{itemId,jdbcType=BIGINT}, #{salaryTemplateId,jdbcType=BIGINT}, ",
            "#{itemName,jdbcType=VARCHAR}, #{startIndex,jdbcType=CHAR}, ",
            "#{startDate,jdbcType=CHAR}, #{endIndex,jdbcType=CHAR}, #{endDate,jdbcType=CHAR}, ",
            "#{payRoll,jdbcType=CHAR}, #{exprId,jdbcType=BIGINT}, #{state,jdbcType=CHAR}, ",
            "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(SalaryTemplateCustomItem record);

    @Select({
            "select",
            "item_id, salary_template_id, item_name, start_index, start_date, end_index, ",
            "end_date, pay_roll, expr_id, state, update_time, update_staff_id",
            "from salary_template_custom_item",
            "where item_id = #{itemId,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="item_id", property="itemId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="salary_template_id", property="salaryTemplateId", jdbcType=JdbcType.BIGINT),
            @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_index", property="startIndex", jdbcType=JdbcType.CHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.CHAR),
            @Result(column="end_index", property="endIndex", jdbcType=JdbcType.CHAR),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.CHAR),
            @Result(column="pay_roll", property="payRoll", jdbcType=JdbcType.CHAR),
            @Result(column="expr_id", property="exprId", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    SalaryTemplateCustomItem selectByPrimaryKey(Long itemId);

    @Update({
            "update salary_template_custom_item",
            "set salary_template_id = #{salaryTemplateId,jdbcType=BIGINT},",
            "item_name = #{itemName,jdbcType=VARCHAR},",
            "start_index = #{startIndex,jdbcType=CHAR},",
            "start_date = #{startDate,jdbcType=CHAR},",
            "end_index = #{endIndex,jdbcType=CHAR},",
            "end_date = #{endDate,jdbcType=CHAR},",
            "pay_roll = #{payRoll,jdbcType=CHAR},",
            "expr_id = #{exprId,jdbcType=BIGINT},",
            "state = #{state,jdbcType=CHAR},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where item_id = #{itemId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(SalaryTemplateCustomItem record);

    @Select({
            "<script>",
            "select",
            "item_id, salary_template_id, item_name, start_index, start_date, end_index, end_date, pay_roll, expr_id, state, update_time, update_staff_id",
            "from salary_template_custom_item",
            "<where>",
            "<if test=\"itemId != null\">and item_id = #{itemId,jdbcType=BIGINT}</if>",
            "<if test=\"salaryTemplateId != null\">and salary_template_id = #{salaryTemplateId,jdbcType=BIGINT}</if>",
            "<if test=\"itemName != null\">and item_name = #{itemName,jdbcType=VARCHAR}</if>",
            "<if test=\"startIndex != null\">and start_index = #{startIndex,jdbcType=CHAR}</if>",
            "<if test=\"startDate != null\">and start_date = #{startDate,jdbcType=CHAR}</if>",
            "<if test=\"endIndex != null\">and end_index = #{endIndex,jdbcType=CHAR}</if>",
            "<if test=\"endDate != null\">and end_date = #{endDate,jdbcType=CHAR}</if>",
            "<if test=\"payRoll != null\">and pay_roll = #{payRoll,jdbcType=CHAR}</if>",
            "<if test=\"exprId != null\">and expr_id = #{exprId,jdbcType=BIGINT}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="item_id", property="itemId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="salary_template_id", property="salaryTemplateId", jdbcType=JdbcType.BIGINT),
            @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_index", property="startIndex", jdbcType=JdbcType.CHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.CHAR),
            @Result(column="end_index", property="endIndex", jdbcType=JdbcType.CHAR),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.CHAR),
            @Result(column="pay_roll", property="payRoll", jdbcType=JdbcType.CHAR),
            @Result(column="expr_id", property="exprId", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<SalaryTemplateCustomItem> selectByFields(SalaryTemplateCustomItem param);

    @Update({
            "<script>",
            "update salary_template_custom_item",
            "<set>",
            "<if test=\"salaryTemplateId != null\">salary_template_id = #{salaryTemplateId,jdbcType=BIGINT},</if>",
            "<if test=\"itemName != null\">item_name = #{itemName,jdbcType=VARCHAR},</if>",
            "<if test=\"startIndex != null\">start_index = #{startIndex,jdbcType=CHAR},</if>",
            "<if test=\"startDate != null\">start_date = #{startDate,jdbcType=CHAR},</if>",
            "<if test=\"endIndex != null\">end_index = #{endIndex,jdbcType=CHAR},</if>",
            "<if test=\"endDate != null\">end_date = #{endDate,jdbcType=CHAR},</if>",
            "<if test=\"payRoll != null\">pay_roll = #{payRoll,jdbcType=CHAR},</if>",
            "<if test=\"exprId != null\">expr_id = #{exprId,jdbcType=BIGINT},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where item_id = #{itemId,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(SalaryTemplateCustomItem record);

    @Select({
            "<script>",
            "select",
            "a.item_id, a.salary_template_id, a.item_name, a.start_index, a.start_date, a.end_index, a.end_date, a.pay_roll, a.expr_id, b.expr, b.expr_params, a.state, a.update_time, a.update_staff_id",
            "from salary_template_custom_item a left join salary_calc_expr b on a.expr_id = b.expr_id and b.state = '1'",
            "<where>",
            "a.salary_template_id = #{salaryTemplateId,jdbcType=BIGINT} and a.state = '1'",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="item_id", property="itemId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="salary_template_id", property="salaryTemplateId", jdbcType=JdbcType.BIGINT),
            @Result(column="item_name", property="itemName", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_index", property="startIndex", jdbcType=JdbcType.CHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.CHAR),
            @Result(column="end_index", property="endIndex", jdbcType=JdbcType.CHAR),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.CHAR),
            @Result(column="pay_roll", property="payRoll", jdbcType=JdbcType.CHAR),
            @Result(column="expr_id", property="exprId", jdbcType=JdbcType.BIGINT),
            @Result(column="expr", property="expr", jdbcType=JdbcType.VARCHAR),
            @Result(column="expr_params", property="exprParams", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<SalaryTemplateCustomItemInfo> selectBySalaryTemplateId(@Param("salaryTemplateId") Long salaryTemplateId);

    @Insert({
            "<script>",
            "insert into salary_template_custom_item (item_id, salary_template_id, item_name, start_index, start_date, end_index, end_date, pay_roll, expr_id, state, update_time, update_staff_id)",
            "values ",
            "<foreach collection=\"list\" item=\"item\" separator=\",\">",
            "(#{item.itemId,jdbcType=BIGINT}, #{item.salaryTemplateId,jdbcType=BIGINT}, #{item.itemName,jdbcType=VARCHAR}, ",
            "#{item.startIndex,jdbcType=CHAR}, #{item.startDate,jdbcType=CHAR}, ",
            "#{item.endIndex,jdbcType=CHAR}, #{item.endDate,jdbcType=CHAR}, #{item.payRoll,jdbcType=CHAR}, ",
            "#{item.exprId,jdbcType=BIGINT}, #{item.state,jdbcType=CHAR}, ",
            "#{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateStaffId,jdbcType=BIGINT})",
            "</foreach>",
            "</script>",
    })
    int batchInsert(List<SalaryTemplateCustomItemInfo> items);

    @Update({
            "<script>",
            "update salary_template_custom_item set state = '0' where salary_template_id = #{salaryTemplateId, jdbcType=BIGINT}",
            "and item_id not in",
            "<foreach collection=\"modifyItemIds\" item=\"modifyItemId\" open=\"(\" close=\")\" separator=\",\">",
            "#{modifyItemId,jdbcType=BIGINT}",
            "</foreach>",
            "</script>"
    })
    int removeItems(@Param("salaryTemplateId") Long salaryTemplateId,@Param("modifyItemIds") List<Long> modifyItemIds);
}