package com.asiainfo.aisports.service.training;

import com.asiainfo.aisports.domain.core.TradeEnrollChange;
import com.asiainfo.aisports.persistence.core.TradeEnrollChangeMapper;
import com.asiainfo.aisports.service.ServiceResult;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Service
public class TradeEnrollChangeService {
    @Autowired
    TradeEnrollChangeMapper tradeEnrollChangeMapper;

    @Transactional(readOnly = true)
    public ServiceResult checkEnrollChange(Long enrollId) {
        //判断报名是否存在进行中的操作
        Map<String, Object> tradeEnrollChange = tradeEnrollChangeMapper.selectByEnrollId(enrollId);

        if (tradeEnrollChange != null) {
            String tradeTypeName = MapUtils.getString(tradeEnrollChange, "tradeTypeName");
            String courseName = MapUtils.getString(tradeEnrollChange, "courseName");
            return new ServiceResult(1, "课程[" + courseName + "]有未完成的" + tradeTypeName + "订单，无法变更");
        }

        return new ServiceResult();
    }

    @Transactional
    public void insert(TradeEnrollChange tradeEnrollChange) {
        tradeEnrollChangeMapper.insert(tradeEnrollChange);
    }
}
