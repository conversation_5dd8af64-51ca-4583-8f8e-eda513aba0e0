package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.service.RoomOccupyService;
import com.asiainfo.aisports.service.VenueServService;
import com.asiainfo.aisports.web.WebResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;

/**
 * 教室查询
 */
@Controller
@RequestMapping("/roomQuery")
public class RoomQueryController {
    @Autowired
    private VenueServService venueServService;
    @Autowired
    private RoomOccupyService roomOccupyService;

    @RequestMapping("/init")
    public String init(LoginStaff staff, ModelMap modelMap) {
        return "roomQuery/roomQuery";
    }

    /**
     * 获取页面参数列表
     *
     * @param staff
     * @return
     */
    @RequestMapping("/queryParam")
    @ResponseBody
    public WebResult queryParam(LoginStaff staff) {
        if (staff.getVenueId() == null) {
            return new WebResult(1, "员工没有管理场馆");
        }
        // 获取服务列表
        return new WebResult().set("serviceList", venueServService.findByCenterIdAndVenueId(staff.getCenterId(), staff.getVenueId()));
    }

    /**
     * 查询占用教室信息
     *
     * @param serviceId
     * @param staff
     * @return
     */
    @RequestMapping("/findOccupyRoomList")
    @ResponseBody
    public WebResult findOccupyRoomList(@RequestParam(value = "serviceId", required = false) Long serviceId,
                                        @RequestParam(value = "date", required = false) Date date, LoginStaff staff) {
        if (staff.getVenueId() == null) {
            return new WebResult(1, "员工没有管理场馆");
        }
        WebResult webResult = new WebResult();
        webResult.putAll(roomOccupyService.queryOccupyRoomList(serviceId, date, staff));
        return webResult;
    }
}