package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.BargainActivityReward;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface BargainActivityRewardMapper {
    @Delete({
        "delete from bargain_activity_reward",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into bargain_activity_reward (id, activity_item_id, ",
        "type, type_value, state, ",
        "create_staff_id, create_time, ",
        "update_staff_id, update_time)",
        "values (#{id,jdbcType=BIGINT}, #{activityItemId,jdbcType=BIGINT}, ",
        "#{type,jdbcType=CHAR}, #{typeValue,jdbcType=VARCHAR}, #{state,jdbcType=CHAR}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})"
    })
    int insert(BargainActivityReward record);

    @Select({
        "select",
        "id, activity_item_id, type, type_value, state, create_staff_id, create_time, ",
        "update_staff_id, update_time",
        "from bargain_activity_reward",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="activity_item_id", property="activityItemId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="type_value", property="typeValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    BargainActivityReward selectByPrimaryKey(Long id);

    @Update({
        "update bargain_activity_reward",
        "set activity_item_id = #{activityItemId,jdbcType=BIGINT},",
          "type = #{type,jdbcType=CHAR},",
          "type_value = #{typeValue,jdbcType=VARCHAR},",
          "state = #{state,jdbcType=CHAR},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(BargainActivityReward record);

    @Select({
        "<script>",
        "select",
        "id, activity_item_id, type, type_value, state, create_staff_id, create_time, update_staff_id, update_time",
        "from bargain_activity_reward",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"activityItemId != null\">and activity_item_id = #{activityItemId,jdbcType=BIGINT}</if>",
        "<if test=\"type != null\">and type = #{type,jdbcType=CHAR}</if>",
        "<if test=\"typeValue != null\">and type_value = #{typeValue,jdbcType=VARCHAR}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="activity_item_id", property="activityItemId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="type_value", property="typeValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BargainActivityReward> selectByFields(BargainActivityReward param);

    @Update({
        "<script>",
        "update bargain_activity_reward",
        "<set>",
        "<if test=\"activityItemId != null\">activity_item_id = #{activityItemId,jdbcType=BIGINT},</if>",
        "<if test=\"type != null\">type = #{type,jdbcType=CHAR},</if>",
        "<if test=\"typeValue != null\">type_value = #{typeValue,jdbcType=VARCHAR},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(BargainActivityReward record);
}