import $ from 'jquery'
import intercept from 'utils/intercept'
// 范例，请修改或者删除
export function queryCommonParam() {
    return $.get('/protocalConfig/queryCommonParam').then(intercept)
}

export function queryServiceListByVenueId(data) {
    return $.get('/commonParam/queryServiceListByVenueId', data).then(intercept)
}

export function queryAgreementList(data) {
    return $.ajax({
        url: '/protocalConfig/queryAgreementList',
        setHash: true,
        data,
    }).then(intercept)
    // return $.get('/protocalConfig/queryAgreementList', data).then(intercept)
}

export function queryProtocal(data) {
    return $.get('/protocalConfig/queryProtocal', data).then(intercept)
}

export function dealProtocal(data) {
    return $.post('/protocalConfig/dealProtocal', data).then(intercept)
}

export function deleteProtocal(data) {
    return $.get('/protocalConfig/deleteProtocal', data).then(intercept)
}

export function querySealList(data) { 
    return $.get('/protocalConfig/seal/list', data).then(intercept)
}

export function getVenueParam() {
    return $.get('/commonParam/getVenueParam',{paramKeys:'guo_xin_ca_flag'}).then(intercept)
}
export function getAllVenueList() {
    return $.get('/electronicSealConfig/getAllVenueList').then(intercept)
}
export function getSealList(data) {
    return $.get('/electronicSealConfig/getSealList', data).then(intercept)
}

export function getSealInfo(data) {
    return $.get('/electronicSealConfig/getSealInfo', data).then(intercept)
}
export function deleteSeal(data) {
    return $.post('/electronicSealConfig/deleteSeal', data).then(intercept)
}

// /electronicSealConfig/addOrUpdateSeal
export function saveSeal(data) {
    return $.post('/electronicSealConfig/addOrUpdateSeal', data).then(intercept)
}
export function queryElectronicSealList(data) {
    return $.get('/protocalConfig/queryElectronicSealList', data).then(intercept)
}
