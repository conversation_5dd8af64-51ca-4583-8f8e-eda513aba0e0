<#include "header.ftl">
<div class="container-fluid">
    <div class="row">
    <#include "sidebar.ftl">
        <div class="col-sm-9 col-sm-offset-3 col-md-10 col-md-offset-2 main">
            <h1 class="sub-header">修改(Set)</h1>
        <#if message??>
            <div class="alert alert-success alert-dismissible fade in" role="alert">
                <button type="button" class="close" data-dismiss="alert"><span aria-hidden="true">×</span><span class="sr-only">Close</span></button>
            ${message}
            </div>
        </#if>
            <form class="form-horizontal" role="form" method="post" action="${webRoot}/memcached/set">
                <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label">键 (Key)*</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="key" name="key" placeholder="" required="true">
                    </div>
                </div>
                <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label">值 (Value)*</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="value" name="value" placeholder="" required="true">
                    </div>
                </div>
                <div class="form-group">
                    <label for="inputEmail3" class="col-sm-2 control-label">有效期 (Expire Time)*</label>
                    <div class="col-sm-10">
                        <input type="text" class="form-control" id="exp" name="exp" placeholder="" required="true">
                        <span class="help-block">设置过期时长，单位秒</span>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-sm-offset-2 col-sm-10">
                        <button type="reset" class="btn btn-default"> 取消 </button>
                        <button type="submit" id="query" class="btn btn-primary"> 修改 </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
                <h4 class="modal-title" id="exampleModalLabel">执行结果</h4>
            </div>
            <div class="modal-body">
                <div><span class="error-text"></span></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap stadium JavaScript
================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="/assets/js/jquery-2.1.1.min.js"></script>
<script src="/assets/js/bootstrap.min.js"></script>
<script src="/assets/js/sidebar.js"></script>
<script src="/assets/js/jquery.form.js"></script>

<script>
    $(function(){
        $('form').ajaxForm({
            success: function(data) {
                $('.error-text').html(data.message);
                if (data.error != 0) {
                    $('.error-text').addClass("text-danger");
                } else {
                    $('.error-text').removeClass("text-danger");
                }
                $('#myModal').modal();
            }
        });
    })
</script>
</body>
</html>
