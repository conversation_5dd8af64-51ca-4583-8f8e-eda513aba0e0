package com.asiainfo.aisports.controller;

import cn.hutool.core.collection.CollUtil;
import com.asiainfo.aisports.cache.RedisKeyEnum;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.ProjectConstants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ExcelModel;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.project.SeatImpDto;
import com.asiainfo.aisports.model.project.SeatImpResult;
import com.asiainfo.aisports.service.ProjectManageService;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.SiteManageService;
import com.asiainfo.aisports.service.VenueStaticParamService;
<<<<<<< HEAD
import com.asiainfo.aisports.service.project.ProjectSeatService;
import com.asiainfo.aisports.tools.JacksonUtil;
import com.asiainfo.aisports.utils.ExcelParser;
=======
import com.asiainfo.aisports.utils.ExcelPoiUtils;
>>>>>>> origin/master
import com.asiainfo.aisports.utils.RedisKeyGenerator;
import com.asiainfo.aisports.web.WebResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
<<<<<<< HEAD
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
=======
import com.google.common.collect.Lists;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
>>>>>>> origin/master
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

<<<<<<< HEAD
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
=======
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
>>>>>>> origin/master
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Created by wangdd on 2017/6/21.
 * 赛事配置(关于赛事票的)
 */
@Controller
@RequestMapping("/projectManage/new")
public class ProjectManageNewController {


    private static final Logger logger = LoggerFactory.getLogger(ProjectManageNewController.class);
    @Autowired
    ProjectManageService projectManageService;
    @Autowired
    VenueStaticParamService venueStaticParamService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RedisKeyGenerator redisKeyGenerator;
    @Autowired
    private SiteManageService siteManageService;

    @Autowired
    private ProjectSeatService projectSeatService;


    @RequestMapping("/init")
    public String initNew() {
        return "/projectManageNew/projectManageNew";
    }

    /**
     * 获取有效的赛事项目记录
     *
     * @param staff
     * @param projectName
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping("/getValidProjectList")
    @ResponseBody
    public WebResult getValidProjectList(LoginStaff staff,
                                         @RequestParam(value = "projectName", required = false) String projectName,
                                         @RequestParam(value = "pageNum", required = false) Integer pageNum,
                                         @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        WebResult webResult = new WebResult();
        List<Project> projectList = projectManageService.getValidNewProjectList(staff.getCenterId(), projectName, pageNum, pageSize);
        webResult.set("pageInfo", new PageInfo<Project>(projectList));
        return webResult;
    }

    /**
     * 更新赛事项目的发布状态
     *
     * @param staff
     * @param projectId
     * @param status    2:未发布 1:已发布
     * @return
     */
    @RequestMapping("/updateProjectReleaseStatus")
    @ResponseBody
    public WebResult updateProjectReleaseStatus(LoginStaff staff,
                                                @RequestParam(value = "projectId") String projectId,
                                                @RequestParam(value = "status") String status) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.updateProjectReleaseStatus(staff.getStaffId(), projectId, status);
        if (serviceResult.getError() == 0) {
            redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_LIST, staff.getCenterId()));
        }
        webResult.putAll(serviceResult);

        return webResult;
    }

    /**
     * 删除赛事项目
     *
     * @param staff
     * @param projectId
     * @return
     */
    @RequestMapping("/deleteProject")
    @ResponseBody
    public WebResult deleteProject(LoginStaff staff,
                                   @RequestParam(value = "projectId") String projectId) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.deleteProject(staff, projectId);
        webResult.putAll(serviceResult);

        return webResult;
    }

    /**
     * 获取项目类型集合
     *
     * @return
     */
    @RequestMapping("/getProjectTypeList")
    @ResponseBody
    public WebResult getProjectTypeList(LoginStaff staff) {
        WebResult webResult = new WebResult();
        List<VenueStaticParam> projectTypeList = venueStaticParamService.find(staff.getCenterId(),
                0L, Constants.VenueStaticParam.PROJECT_TYPES);
        webResult.set("projectTypeList", projectTypeList);
        return webResult;
    }

    /**
     * 获取赛事项目的基础信息
     *
     * @param projectId
     * @return
     */
    @RequestMapping("/getProjectInfo")
    @ResponseBody
    public WebResult getProjectInfo(@RequestParam(value = "projectId") String projectId) {
        WebResult webResult = new WebResult();
        Map<String, Object> projectInfo = projectManageService.getProjectById(projectId);
        webResult.set("projectInfo", projectInfo);
        return webResult;
    }

    /**
     * 新增或者更新赛事项目信息
     *
     * @param staff
     * @param projectId        项目ID
     * @param projectName      项目名称
     * @param projectTypeId    项目类型ID
     * @param image            图片
     * @param performStartDate 项目开始时间
     * @param performEndDate   项目结束时间
     * @param saleStartDate    销售开始时间
     * @param saleEndDate      销售结束时间
     * @param description      描述
     * @param projectBuyManual 购票须知
     * @param realFlag 是否实名
     * @param popFlag 详情页弹窗
     * @param popContent 弹窗详情
     * @return
     */
    @RequestMapping("/insertOrUpdateProject")
    @ResponseBody
    public WebResult insertOrUpdateProject(LoginStaff staff,
                                           @RequestParam(value = "projectId", required = false) String projectId,
                                           @RequestParam(value = "projectName") String projectName,
                                           @RequestParam(value = "projectTypeId") Long projectTypeId,
                                           @RequestParam(value = "image") String image,
                                           @RequestParam(value = "performStartDate") String performStartDate,
                                           @RequestParam(value = "performEndDate") String performEndDate,
                                           @RequestParam(value = "saleStartDate") String saleStartDate,
                                           @RequestParam(value = "saleEndDate") String saleEndDate,
                                           @RequestParam(value = "description") String description,
                                           @RequestParam(value = "sort", required = false) Integer sort,
                                           @RequestParam(value = "realFlag", required = false) String realFlag,
                                           @RequestParam(value = "popFlag", required = false) String popFlag,
                                           @RequestParam(value = "popContent", required = false) String popContent,
                                           @RequestParam(value = "projectBuyManual") String projectBuyManual) throws ParseException {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.insertOrUpdateProject(staff.getStaffId(), staff.getCenterId(),
                projectId, projectName, projectTypeId, image, performStartDate, performEndDate, saleStartDate, saleEndDate,
                description, projectBuyManual, sort, realFlag, popFlag, popContent, Constants.Tag.YES);

        webResult.putAll(serviceResult);
        return webResult;
    }

    /**
     * 获取有效的赛事项目场次记录
     *
     * @param projectId
     * @return
     */
    @RequestMapping("/getValidPerformList")
    @ResponseBody
    public WebResult getValidPerformList(@RequestParam(value = "projectId") String projectId) {
        WebResult webResult = new WebResult();
        List<Perform> performList = projectManageService.getValidPerformList(projectId);
        webResult.set("performList", performList);
        return webResult;
    }

    /**
     * 删除演出场次
     *
     * @param staff
     * @param performId
     * @return
     */
    @RequestMapping("/deletePerform")
    @ResponseBody
    public WebResult deletePerform(LoginStaff staff,
                                   @RequestParam(value = "performId") String performId) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.deletePerform(staff.getStaffId(), performId);
        if (serviceResult.getError() == 0) {
            redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_LIST, staff.getCenterId()));
        }
        webResult.putAll(serviceResult);
        return webResult;
    }

    /**
     * 获取场次的基础信息
     *
     * @param performId
     * @return
     */
    @RequestMapping("/getPerformBaseInfo")
    @ResponseBody
    public WebResult getPerformBaseInfo(@RequestParam(value = "performId") String performId) {
        WebResult webResult = new WebResult();
        Perform performBaseInfo = projectManageService.getPerformBaseInfo(performId);
        webResult.set("performBaseInfo", performBaseInfo);
        return webResult;
    }

    /**
     * 获取可选择的演出场次地址列表
     *
     * @param staff
     * @return
     */
    @RequestMapping("/getPerformLocationList")
    @ResponseBody
    public WebResult getPerformLocationList(LoginStaff staff) {
        WebResult webResult = new WebResult();
        List<VenueLocation> locationList = projectManageService.getPerformLocationList(staff.getCenterId());
        webResult.set("locationList", locationList);
        return webResult;
    }

    /**
     * 新增演出地址
     *
     * @param staff
     * @param name
     * @param latitude
     * @param longitude
     * @param address
     * @param cityCode
     * @param districtCode
     * @return
     */
    @RequestMapping("/addPerformLocation")
    @ResponseBody
    public WebResult addPerformLocation(LoginStaff staff,
                                        @RequestParam(value = "name") String name,
                                        @RequestParam(value = "latitude",required = false) String latitude,
                                        @RequestParam(value = "longitude",required = false) String longitude,
                                        @RequestParam(value = "address") String address,
                                        @RequestParam(value = "cityCode") String cityCode,
                                        @RequestParam(value = "districtCode") String districtCode) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.addPerformLocation(staff.getStaffId(), staff.getCenterId(),
                name, latitude, longitude, address, cityCode, districtCode);
        webResult.putAll(serviceResult);
        return webResult;
    }


    @RequestMapping("/getPerformTicketList")
    @ResponseBody
    public WebResult getPerformTicketListNew(@RequestParam(value = "performId") String performId) {
        WebResult webResult = new WebResult();
        List<Map<String, Object>> performTicketList = projectManageService.selectValidPerformTicketByPerformIdNew(performId);
        webResult.set("performTicketList", performTicketList);
        return webResult;
    }


    @RequestMapping("/getPerformTicketPageList")
    @ResponseBody
    public WebResult getPerformTicketPageList(@RequestParam(value = "performId") String performId,
                                              @RequestParam(value = "stockId", required = false) Long stockId,
                                              @RequestParam(value = "ticketName", required = false) String ticketName,
                                              @RequestParam(value = "ticketKind", required = false) String ticketKind,
                                              @RequestParam(defaultValue = "1") int pageNum,
                                              @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize
                                              ) {
        WebResult webResult = new WebResult();
        List<Map<String, Object>> performTicketList = projectManageService.selectValidPerformTicketPageList(performId, stockId, ticketKind, ticketName, pageNum, pageSize);
        webResult.set("pageInfo", new PageInfo<>(performTicketList));
        return webResult;
    }

    @RequestMapping("/getTeamPerformTicketPageList")
    @ResponseBody
    public WebResult getTeamPerformTicketPageList(@RequestParam(value = "performId") String performId,
                                              @RequestParam(value = "stockId", required = false) Long stockId,
                                              @RequestParam(value = "ticketName", required = false) String ticketName,
                                              @RequestParam(defaultValue = "1") int pageNum,
                                              @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize
    ) {
        WebResult webResult = new WebResult();
        List<Map<String, Object>> performTicketList = projectManageService.selectValidPerformTicketPageList(performId, stockId, ProjectConstants.TicketKind.TEAM, ticketName, pageNum, pageSize);
        webResult.set("pageInfo", new PageInfo<>(performTicketList));
        return webResult;
    }


    @RequestMapping("/getPerformTicketInfo")
    @ResponseBody
    public WebResult getPerformTicketInfo(String performTicketId) {
        PerformNewTicket ticketInfo = projectManageService.getPerformTicketInfo(performTicketId);
        WebResult webResult = new WebResult();
        webResult.set("ticketInfo", ticketInfo);
        return webResult;
    }

    /**
     * 插入或更新场次的基本信息
     *
     * @param staff
     * @param id
     * @param performName
     * @param description
     * @param projectId
     * @param performDate
     * @param address
     * @param locationId
     * @param startTime
     * @param endTime
     * @param buyLimit
     * @param remark
     * @param saleStartDate
     * @param saleEndDate
     * @return
     */
    @RequestMapping("/insertOrUpdatePerformBaseInfo")
    @ResponseBody
    public WebResult insertOrUpdatePerformBaseInfo(LoginStaff staff,
                                                   @RequestParam(value = "id", required = false) String id,
                                                   @RequestParam(value = "performName") String performName,
                                                   @RequestParam(value = "description") String description,
                                                   @RequestParam(value = "projectId") String projectId,
                                                   @RequestParam(value = "performDate") String performDate,
                                                   @RequestParam(value = "address") String address,
                                                   @RequestParam(value = "locationId") Long locationId,
                                                   @RequestParam(value = "startTime") String startTime,
                                                   @RequestParam(value = "endTime") String endTime,
                                                   @RequestParam(value = "buyLimit", required = false) Integer buyLimit,
                                                   @RequestParam(value = "remark") String remark,
                                                   @RequestParam(value = "saleStartDate") String saleStartDate,
                                                   @RequestParam(value = "saleEndDate") String saleEndDate) throws ParseException {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.insertOrUpdatePerformBaseInfo(staff, id,
                performName, description, projectId, performDate, address, locationId, startTime, endTime, buyLimit,
                remark, saleStartDate, saleEndDate);
        webResult.putAll(serviceResult);
        return webResult;
    }

    /**
     * 删除场次票
     *
     * @param staff
     * @param performTicketId
     * @return
     */
    @RequestMapping("/deletePerformTicket")
    @ResponseBody
    public WebResult deletePerformTicket(LoginStaff staff,
                                         @RequestParam(value = "performTicketId") String performTicketId) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.deletePerformNewTicket(staff, performTicketId);

        webResult.putAll(serviceResult);
        return webResult;
    }




    /**
     * 新增或者更新场次票
     *
     * @param staff
     * @param performId
     * @param performTicketId
     * @param performTicketName
     * @param price
     * @param totalAmount
     * @return
     */
//    @RequestMapping("/insertOrUpdatePerformTicket")
//    @ResponseBody
    public WebResult insertOrUpdatePerformTicket(LoginStaff staff,
                                                 @RequestParam(value = "performId") String performId,
                                                 @RequestParam(value = "performTicketId", required = false) String performTicketId,
                                                 @RequestParam(value = "performTicketName") String performTicketName,
                                                 @RequestParam(value = "price") Integer price,
                                                 @RequestParam(value = "totalAmount") Integer totalAmount) throws ParseException {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.insertOrUpdatePerformTicket(staff.getStaffId(), staff.getCenterId(),
                performId, performTicketId, performTicketName, price, totalAmount);
        webResult.putAll(serviceResult);
        return webResult;
    }



    @RequestMapping("/saveOrUpdateStock")
    @ResponseBody
    public WebResult saveOrUpdateStock(@RequestParam(value = "stockId", required = false) Long stockId,
                                       String name,
                                       String performId,
                                       Integer num,
                                       String location,
                                       LoginStaff staff) {

        ServiceResult result = projectManageService.saveOrUpdateStock(stockId, name, performId, num, location, staff);
        WebResult webResult = new WebResult();
        webResult.putAll(result);
        return webResult;
    }


    @RequestMapping("/deleteStock")
    @ResponseBody
    public WebResult deleteStock(@RequestParam(value = "stockId") Long stockId,
                                 LoginStaff staff) {
        ServiceResult result = projectManageService.deleteStock(stockId, staff);
        WebResult webResult = new WebResult();
        webResult.putAll(result);
        return webResult;
    }


    @RequestMapping("/getStockList")
    @ResponseBody
    public WebResult getStockList(@RequestParam(value = "performId") String performId) {
        WebResult webResult = new WebResult();
        List<PerformStock> stockList = projectManageService.getStockList(performId);
        webResult.set("stockList", stockList);
        return webResult;
    }


    @RequestMapping("/insertOrUpdatePerformTicket")
    @ResponseBody
    public WebResult insertOrUpdatePerformTicketNew(LoginStaff staff,
                                                    @RequestParam(value = "performId") String performId,
                                                    @RequestParam(value = "performTicketId", required = false) String performTicketId,
                                                    @RequestParam(value = "performTicketName") String performTicketName,
                                                    @RequestParam(value = "price") Integer price,
                                                    Long stockId,
                                                    String ticketKind,
                                                    Integer adultNum,
                                                    Integer minorNum,
                                                    String rule,
                                                    Long siteId,
                                                    String rightIds,
                                                    String teamName) throws ParseException {

        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.insertOrUpdatePerformTicketNew(staff.getStaffId(), staff.getCenterId(),
                performId, performTicketId, performTicketName, price, stockId, ticketKind, adultNum, minorNum, rule, siteId, rightIds, teamName);
        webResult.putAll(serviceResult);
        return webResult;
    }


    @RequestMapping("/getPmList")
    @ResponseBody
    public WebResult getPmList(LoginStaff staff) {
        WebResult webResult = new WebResult();
        List<ActivityMerchant> pmList = projectManageService.getPmList(staff.getCenterId());
        webResult.set("pmList", pmList);
        return webResult;
    }

    @RequestMapping("/getRightsList")
    @ResponseBody
    public WebResult getRightsList(LoginStaff staff,
                                   @RequestParam(value = "performId") String performId) {

        WebResult webResult = new WebResult();
        List<Map<String, Object>> rightsList = projectManageService.getRightsList(performId);

        webResult.set("rightsList", rightsList);
        return webResult;
    }


    @RequestMapping("/getRightsPageList")
    @ResponseBody
    public WebResult getRightsPageList(LoginStaff staff,
                                       @RequestParam(value = "performId") String performId,
                                       @RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize) {

        WebResult webResult = new WebResult();
        List<DataMap> rightsList = projectManageService.getRightsPageList(performId, pageNum, pageSize);

        webResult.set("pageInfo", new PageInfo<>(rightsList));
        return webResult;
    }



    @RequestMapping("/getRightsById")
    @ResponseBody
    public WebResult getRightsById(LoginStaff staff,
                                   @RequestParam(value = "rightId") Long rightId) {
        WebResult webResult = new WebResult();
        Map<String, Object> rights = projectManageService.getRightsById(rightId);
        webResult.set("rights", rights);
        return webResult;
    }


    @RequestMapping("/saveRights")
    @ResponseBody
    public WebResult saveRights(LoginStaff staff,
                                @RequestParam(value = "rightId", required = false) Long rightId,
                                @RequestParam(value = "performId") String performId,
                                @RequestParam(value = "pmId") Long pmId,
                                String name,
                                String content,
                                String validType,
                                @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
                                @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endDate) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.saveRights(staff, rightId, performId, pmId, name, content, validType, startDate, endDate);
        webResult.putAll(serviceResult);
        return webResult;
    }


    @RequestMapping("/delRights")
    @ResponseBody
    public WebResult delRights(LoginStaff staff,
                                @RequestParam(value = "rightId") Long rightId) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.delRights(staff, rightId);
        webResult.putAll(serviceResult);
        return webResult;
    }



    /**
     * 获取省份集合
     *
     * @return
     */
    @RequestMapping("/getProvinceList")
    @ResponseBody
    public WebResult getProvinceList() {
        WebResult webResult = new WebResult();
        List<Area> provinceList = projectManageService.getProvinceList();
        webResult.set("provinceList", provinceList);
        return webResult;
    }



    @RequestMapping("/getSiteList")
    @ResponseBody
    public WebResult getSiteList(LoginStaff staff) {
        WebResult webResult = new WebResult();
        List<Site> siteList = siteManageService.findSitesByVenueId(staff.getCenterId(), null);
        webResult.set("siteList", siteList);
        return webResult;
    }

    /**
     * 获取城市集合
     *
     * @param parentCode
     * @return
     */
    @RequestMapping("/getCityList")
    @ResponseBody
    public WebResult getCityList(@RequestParam(value = "parentCode") String parentCode) {
        WebResult webResult = new WebResult();
        List<Area> cityList = projectManageService.getAreaListByParentCodeAndLevel(parentCode, Constants.AreaLevel.CITY);
        webResult.set("cityList", cityList);
        return webResult;
    }

    /**
     * 获取区域集合
     *
     * @param parentCode
     * @return
     */
    @RequestMapping("/getDistrictList")
    @ResponseBody
    public WebResult getDistrictList(@RequestParam(value = "parentCode") String parentCode) {
        WebResult webResult = new WebResult();
        List<Area> districtList = projectManageService.getAreaListByParentCodeAndLevel(parentCode, Constants.AreaLevel.DISTRICT);
        webResult.set("districtList", districtList);
        return webResult;
    }


    @RequestMapping("/loadCache")
    @ResponseBody
    public WebResult loadCache(LoginStaff staff) {

        projectManageService.loadProjectCache(staff.getCenterId());

        return new WebResult();

    }

    @RequestMapping("/getProjectIndexInfo")
    @ResponseBody
    public WebResult getProjectIndexInfo(LoginStaff staff, @RequestParam(value = "projectId") String projectId) {
        WebResult webResult = new WebResult();
        Map<String, Object> projectInfo = projectManageService.getProjectById(projectId);
        String projectTypeName = venueStaticParamService.queryValueName(staff.getCenterId(),
                0L, Constants.VenueStaticParam.PROJECT_TYPES, projectInfo.get("projectTypeId").toString());
        projectInfo.put("projectTypeName", projectTypeName);
        webResult.set("projectInfo", projectInfo);
        return webResult;
    }

    @RequestMapping("/getPerformList")
    @ResponseBody
    public WebResult getPerformList(LoginStaff staff, @RequestParam(value = "projectId") String projectId) {
        WebResult webResult = new WebResult();
        List<Perform> performList = projectManageService.getValidPerformList(projectId);
        //List<Perform> performList转为List<DataMap> performs
        performList.forEach(perform -> {
            List<Map<String, Object>> performTicketList = projectManageService.selectValidPerformTicketByPerformIdNew(perform.getId());
            perform.setPerformTicketList(performTicketList);
        });
        webResult.set("performList", performList);
        return webResult;
    }

    @RequestMapping("/getPerformTickets")
    @ResponseBody
    public WebResult getPerformTickets(LoginStaff staff,
                                       @RequestParam(value = "projectId") String projectId,
                                       String performId,
                                       Long stockId,
                                       @RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize) {
        WebResult webResult = new WebResult();
        webResult.set("ticketStatistics", projectManageService.ticketStatistics(staff, projectId, performId, stockId));
        webResult.set("performTickets", new PageInfo<>(projectManageService.getPerformTickets(staff, projectId, performId, stockId, pageNum, pageSize)));
        return webResult;
    }

    @RequestMapping("/getPerformTicketRights")
    @ResponseBody
    public WebResult getPerformTicketRights(LoginStaff staff,
                                            @RequestParam(value = "projectId") String projectId,
                                            String performId,
                                            String rightId,
                                            @RequestParam(defaultValue = "1") int pageNum,
                                            @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize) {
        WebResult webResult = new WebResult();
        webResult.set("rightTicketStatistics", projectManageService.rightTicketStatistics(staff, projectId, performId, rightId));
        webResult.set("performTicketRights", new PageInfo<>(projectManageService.getPerformTicketRights(staff, projectId, performId, rightId, pageNum, pageSize)));
        return webResult;
    }


    @RequestMapping("/reloadMessage")
    @ResponseBody
    public WebResult reloadMessage(Long id, LoginStaff staff) {

        projectManageService.reloadmessage(id, staff.getCenterId());

        return new WebResult();

    }

    @RequestMapping("/sendTeamTicket")
    @ResponseBody
    public WebResult sendTeamTicket(@RequestParam(value = "performTicketId") String performTicketId,
                                    @RequestParam(value = "sendNum") Integer sendNum,
                                    @RequestParam(value = "stockId") Long stockId,
                                    @RequestParam(value = "price") Long price,
                                    LoginStaff staff){
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = projectManageService.sendTeamTicket(performTicketId, sendNum, stockId, price, staff);
        webResult.putAll(serviceResult);
        return webResult;
    }

    @RequestMapping("/getTeamTicketSendRecordList")
    @ResponseBody
    public WebResult getTeamTicketSendRecordList(LoginStaff staff,
                                                 @RequestParam(value = "performId", required = false) String performId,
                                                 @RequestParam(value = "stockId", required = false) Long stockId,
                                                 @RequestParam(value = "keyword", required = false) String keyword,
                                                 Date startDate,
                                                 Date endDate,
                                                 @RequestParam(defaultValue = "1") int pageNum,
                                                 @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize) {
        WebResult webResult = new WebResult();
        webResult.set("teamTicketSendRecordList", new PageInfo<>(projectManageService.getTeamTicketSendRecordList(staff, performId, stockId, keyword, startDate, endDate, pageNum, pageSize)));
        return webResult;
    }

    @RequestMapping("/getTeamTicketList")
    @ResponseBody
    public WebResult getTeamTicketList(LoginStaff staff,
                                     @RequestParam(value = "recordId") String recordId,
                                     String keyword,
                                     String state,
                                     @RequestParam(defaultValue = "1") int pageNum,
                                     @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize) {
        WebResult webResult = new WebResult();
        webResult.set("teamTicketSendRecord", projectManageService.getTeamTicketSendRecord(recordId));
        webResult.set("teamTicketList", new PageInfo<>(projectManageService.getTeamTicketList(staff, recordId, keyword, state, pageNum, pageSize)));
        return webResult;
    }

    @RequestMapping("/setValidPeriod")
    @ResponseBody
    public WebResult setValidPeriod(String period, LoginStaff staff) {

        projectManageService.setValidPeriod(period, staff.getCenterId());

        return new WebResult();

    }

<<<<<<< HEAD

=======
    @RequestMapping("/downloadQrcodes")
    public void downloadQrcodes(HttpServletResponse response,
                                                  HttpServletRequest request,
                                                  @RequestParam(value = "recordId") String recordId,
                                                  String keyword,
                                                  String state,
                                                  String ticketIds) throws Exception {
        projectManageService.downloadQrcodes(response, request, recordId, keyword, state, ticketIds);
    }

    @RequestMapping("/downloadQrcodesExcel")
    public ResponseEntity<byte[]> downloadQrcodesExcel(@RequestParam(value = "recordId") String recordId,
                                                  String keyword,
                                                  String state,
                                                  String ticketIds,
                                                  LoginStaff staff) throws Exception {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentDispositionFormData("attachment", new String("团体票二维码导出".getBytes("gb2312"), StandardCharsets.ISO_8859_1) + ".xlsx");
        httpHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ExcelModel excelModel = new ExcelModel();
        List<Map<String, Object>> dataList = Lists.newArrayList();
        dataList = projectManageService.queryQrcodes(recordId, keyword, state, ticketIds);
        excelModel.setBodyList(dataList);
        excelModel.setHeaders(new String[]{"票号", "场次", "票区", "票价", "票名称", "核销区域", "二维码"});
        excelModel.setCellKeys(new String[]{"ticketNo", "performName", "stockName", "price", "ticketName", "siteName", "qrcode"});
        excelModel.setHeadersWidths(new Integer[]{4000, 4000, 4000, 4000, 4000, 4000, 6000});
        excelModel.setFileName("团体票二维码导出.xlsx");
        File file = ExcelPoiUtils.excelExport(excelModel);
        return new ResponseEntity<>(FileUtils.readFileToByteArray(file), httpHeaders, HttpStatus.OK);
    }
>>>>>>> origin/master

}
