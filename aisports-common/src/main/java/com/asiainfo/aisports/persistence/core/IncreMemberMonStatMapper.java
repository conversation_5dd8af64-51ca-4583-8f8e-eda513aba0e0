package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.IncreMemberMonStat;
import com.asiainfo.aisports.domain.core.IncreMemberMonStatKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

public interface IncreMemberMonStatMapper {
    @Delete({
        "delete from incre_member_mon_stat",
        "where stat_month = #{statMonth,jdbcType=VARCHAR}",
          "and staff_id = #{staffId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(IncreMemberMonStatKey key);

    @Insert({
        "insert into incre_member_mon_stat (stat_month, staff_id, ",
        "center_id, venue_id, ",
        "venue_name, increase_member, ",
        "staff_name, update_time)",
        "values (#{statMonth,jdbcType=VARCHAR}, #{staffId,jdbcType=BIGINT}, ",
        "#{centerId,jdbcType=BIGINT}, #{venueId,jdbcType=BIGINT}, ",
        "#{venueName,jdbcType=VARCHAR}, #{increaseMember,jdbcType=INTEGER}, ",
        "#{staffName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})"
    })
    int insert(IncreMemberMonStat record);

    @Insert({
        "<script>",
        "insert into incre_member_mon_stat",
        "select ",
        "	date_format(now(),'%Y%m') stat_month,",
        "	v.center_id,",
        "	v.venue_id,",
        "	v.venue_name,",
        "	count(m.cust_id) increase_member,",
        "  cl.consultant_id staff_id,",
        "	cl.consultant_name staff_name,",
        "	now() update_time",
        "from member m,consultant cl,venue v",
        "where m.consultant_id=cl.consultant_id ",
        "and m.venue_id=v.venue_id",
        "and date_format(m.create_time,'%Y%m')=date_format(now(),'%Y%m')",
        "<if test=\"centerId != null\">and v.center_id = #{centerId,jdbcType=BIGINT}</if>",
        "group by cl.consultant_id",
        "</script>"
    })
    int insertFromTable(@Param("centerId") Long centerId);

    @Select({
        "select",
        "stat_month, staff_id, center_id, venue_id, venue_name, increase_member, staff_name, ",
        "update_time",
        "from incre_member_mon_stat",
        "where stat_month = #{statMonth,jdbcType=VARCHAR}",
          "and staff_id = #{staffId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="stat_month", property="statMonth", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_name", property="venueName", jdbcType=JdbcType.VARCHAR),
        @Result(column="increase_member", property="increaseMember", jdbcType=JdbcType.INTEGER),
        @Result(column="staff_name", property="staffName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    IncreMemberMonStat selectByPrimaryKey(IncreMemberMonStatKey key);

    @Update({
        "update incre_member_mon_stat",
        "set center_id = #{centerId,jdbcType=BIGINT},",
          "venue_id = #{venueId,jdbcType=BIGINT},",
          "venue_name = #{venueName,jdbcType=VARCHAR},",
          "increase_member = #{increaseMember,jdbcType=INTEGER},",
          "staff_name = #{staffName,jdbcType=VARCHAR},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
        "where stat_month = #{statMonth,jdbcType=VARCHAR}",
          "and staff_id = #{staffId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(IncreMemberMonStat record);

    @Select({
        "<script>",
        "select",
        "stat_month, staff_id, center_id, venue_id, venue_name, increase_member, staff_name, update_time",
        "from incre_member_mon_stat",
        "<where>",
        "<if test=\"statMonth != null\">and stat_month = #{statMonth,jdbcType=VARCHAR}</if>",
        "<if test=\"staffId != null\">and staff_id = #{staffId,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"venueName != null\">and venue_name = #{venueName,jdbcType=VARCHAR}</if>",
        "<if test=\"increaseMember != null\">and increase_member = #{increaseMember,jdbcType=INTEGER}</if>",
        "<if test=\"staffName != null\">and staff_name = #{staffName,jdbcType=VARCHAR}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="stat_month", property="statMonth", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_name", property="venueName", jdbcType=JdbcType.VARCHAR),
        @Result(column="increase_member", property="increaseMember", jdbcType=JdbcType.INTEGER),
        @Result(column="staff_name", property="staffName", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<IncreMemberMonStat> selectByFields(IncreMemberMonStat param);

    @Update({
        "<script>",
        "update incre_member_mon_stat",
        "<set>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "<if test=\"venueName != null\">venue_name = #{venueName,jdbcType=VARCHAR},</if>",
        "<if test=\"increaseMember != null\">increase_member = #{increaseMember,jdbcType=INTEGER},</if>",
        "<if test=\"staffName != null\">staff_name = #{staffName,jdbcType=VARCHAR},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where stat_month = #{statMonth,jdbcType=VARCHAR}",
          "and staff_id = #{staffId,jdbcType=BIGINT}",
        "</script>"
    })

    int updateByPrimaryKeySelective(IncreMemberMonStat record);


    @Select({
            "<script>",
            "select",
            "stat_month, sum(increase_member) increase_member",
            "from incre_member_mon_stat",
            "<where>",
            "<if test=\"staffId != null\">and staff_id = #{staffId,jdbcType=BIGINT}</if>",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"startDate != null\">and stat_month &gt;= date_format(#{startDate,jdbcType=DATE},'%Y%m')</if>",
            "<if test=\"endDate != null\">and stat_month &lt;= date_format(#{endDate,jdbcType=DATE},'%Y%m')</if>",
            "</where>",
            "group by stat_month",
            "order by stat_month",
            "</script>"
    })
    @Results({
            @Result(column="stat_month", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="increase_member", property="value", jdbcType=JdbcType.INTEGER)
    })
    List<Map<String,Object>> getAddMember(Map<String,Object> param);
}