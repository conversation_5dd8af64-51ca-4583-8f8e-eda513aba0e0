package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.CarParkingCoupon;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface CarParkingCouponMapper {
    @Delete({
        "delete from car_parking_coupon",
        "where coupon_no = #{couponNo,jdbcType=VARCHAR}"
    })
    int deleteByPrimaryKey(String couponNo);

    @Insert({
        "insert into car_parking_coupon (coupon_no, relate_type, ",
        "relate_object_id, car_license_num, ",
        "state, create_time, ",
        "update_time)",
        "values (#{couponNo,jdbcType=VARCHAR}, #{relateType,jdbcType=CHAR}, ",
        "#{relateObjectId,jdbcType=BIGINT}, #{carLicenseNum,jdbcType=VARCHAR}, ",
        "#{state,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{updateTime,jdbcType=TIMESTAMP})"
    })
    int insert(CarParkingCoupon record);

    @Select({
        "select",
        "coupon_no, relate_type, relate_object_id, car_license_num, state, create_time, ",
        "update_time",
        "from car_parking_coupon",
        "where coupon_no = #{couponNo,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="coupon_no", property="couponNo", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="relate_type", property="relateType", jdbcType=JdbcType.CHAR),
        @Result(column="relate_object_id", property="relateObjectId", jdbcType=JdbcType.BIGINT),
        @Result(column="car_license_num", property="carLicenseNum", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    CarParkingCoupon selectByPrimaryKey(String couponNo);

    @Update({
        "update car_parking_coupon",
        "set relate_type = #{relateType,jdbcType=CHAR},",
          "relate_object_id = #{relateObjectId,jdbcType=BIGINT},",
          "car_license_num = #{carLicenseNum,jdbcType=VARCHAR},",
          "state = #{state,jdbcType=CHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
        "where coupon_no = #{couponNo,jdbcType=VARCHAR}"
    })
    int updateByPrimaryKey(CarParkingCoupon record);

    @Select({
        "<script>",
        "select",
        "coupon_no, relate_type, relate_object_id, car_license_num, state, create_time, update_time",
        "from car_parking_coupon",
        "<where>",
        "<if test=\"couponNo != null\">and coupon_no = #{couponNo,jdbcType=VARCHAR}</if>",
        "<if test=\"relateType != null\">and relate_type = #{relateType,jdbcType=CHAR}</if>",
        "<if test=\"relateObjectId != null\">and relate_object_id = #{relateObjectId,jdbcType=BIGINT}</if>",
        "<if test=\"carLicenseNum != null\">and car_license_num = #{carLicenseNum,jdbcType=VARCHAR}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="coupon_no", property="couponNo", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="relate_type", property="relateType", jdbcType=JdbcType.CHAR),
        @Result(column="relate_object_id", property="relateObjectId", jdbcType=JdbcType.BIGINT),
        @Result(column="car_license_num", property="carLicenseNum", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<CarParkingCoupon> selectByFields(CarParkingCoupon param);

    @Update({
        "<script>",
        "update car_parking_coupon",
        "<set>",
        "<if test=\"relateType != null\">relate_type = #{relateType,jdbcType=CHAR},</if>",
        "<if test=\"relateObjectId != null\">relate_object_id = #{relateObjectId,jdbcType=BIGINT},</if>",
        "<if test=\"carLicenseNum != null\">car_license_num = #{carLicenseNum,jdbcType=VARCHAR},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where coupon_no = #{couponNo,jdbcType=VARCHAR}",
        "</script>"
    })
    int updateByPrimaryKeySelective(CarParkingCoupon record);

    @Insert({
            "<script>",
            "insert into car_parking_coupon (coupon_no, relate_type, ",
            "relate_object_id, car_license_num, ",
            "state, create_time, ",
            "update_time)",
            "values ",
            "<foreach collection=\"carParkingCoupons\" item=\"item\" index=\"index\" separator=\",\">",
            "(#{item.couponNo,jdbcType=VARCHAR}, #{item.relateType,jdbcType=CHAR}, ",
            "#{item.relateObjectId,jdbcType=BIGINT}, #{item.carLicenseNum,jdbcType=VARCHAR}, ",
            "#{item.state,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP}, ",
            "#{item.updateTime,jdbcType=TIMESTAMP})",
            "</foreach>",
            "</script>"
    })
    int insertBatch(@Param("carParkingCoupons") List<CarParkingCoupon> carParkingCoupons);
}
