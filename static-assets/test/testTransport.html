<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html;charset=UTF-8">
    <title>test seajs for datepicker</title>
    <script type="text/javascript" src="../sea_modules/seajs/2.3.0/dist/sea.js"></script>
    <link rel="stylesheet" href="../sea_modules/static/testDatePicker/main.css"/>
</head>
<body>
<input id="datepick" type="text"/>
<script type="text/javascript">
    seajs.config({
        base:"../sea_modules/"
    });

    if(location.href.indexOf("?deve")!=-1){
        seajs.use("../static/testTransport/src/a");
    }
    else{
        seajs.use("static/testTransport/a");
    }
</script>
</body>
</html>