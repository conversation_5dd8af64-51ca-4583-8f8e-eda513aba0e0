// projectName	String	否	项目名称
// pageNum	Integer	否	页数
// pageSize	Integer	否	每页条数
export function getValidProjectList(data, cb) {
    $.get('/projectManage/new/getValidProjectList', data, cb)
}

// projectId	String	是	项目ID
// status	String	是	0-未发布 1-已发布
export function updateProjectReleaseStatus(data, cb) {
    $.get('/projectManage/new/updateProjectReleaseStatus', data, cb)
}

export function delProject(projectId, cb) {
    $.post('/projectManage/new/deleteProject', { projectId }, cb)
}

export function getProjectInfo(projectId, cb) {
    $.get('/projectManage/new/getProjectInfo', { projectId }, cb)
}

export function getProjectTypeList(cb) {
    $.get('/projectManage/new/getProjectTypeList', cb)
}

export function updateProjectInfo(data, cb) {
    $.post('/projectManage/new/insertOrUpdateProject', data, cb)
}

export function getPerformList(projectId, cb) {
    $.get('/projectManage/new/getValidPerformList', { projectId }, cb)
}
export function getPerformList2(projectId, cb) {
    $.get('/projectManage/new/getPerformList', { projectId }, cb)
}
export function deletePerformItem(performId, cb) {
    $.get('/projectManage/new/deletePerform', { performId }, cb)
}

export function getPerformItemInfo(performId, cb) {
    $.get('/projectManage/new/getPerformBaseInfo', { performId }, cb)
}
export function getPerformLocationList(cb) {
    $.get('/projectManage/new/getPerformLocationList', cb)
}

export function getPerformTickets(performId, cb) {
    $.get('/projectManage/new/getPerformTicketList', { performId }, cb)
}

export function deletePerformTicket(performTicketId, cb) {
    $.post('/projectManage/new/deletePerformTicket', { performTicketId }, cb)
}

export function updatePerformTicket(data, cb) {
    $.post('/projectManage/new/insertOrUpdatePerformTicket', data, cb)
}


export function getProvinceList(cb) {
    $.get('/publicCamp/getProvinceList', cb)
}

export function getCityList(parentCode, cb) {
    $.get('/publicCamp/getCityList', { parentCode }, cb)
}

export function getDistrictList(parentCode, cb) {
    $.get('/publicCamp/getDistrictList', { parentCode }, cb)
}
export function addPerformLocation(data, cb) {
    $.post('/projectManage/new/addPerformLocation', data, cb)
}

export function updatePerformInfo(data, cb) {
    $.post('/projectManage/new/insertOrUpdatePerformBaseInfo', data, cb)
}
/**
 * 新增或修改票务信息
 * @param data 票务数据
 * @param cb 回调函数
 */
export function insertOrUpdatePerformTicket(data, cb) {
    $.post('/projectManage/new/insertOrUpdatePerformTicket', data, cb);
}

/**
 * 保存或更新库存信息
 * @param data 库存数据
 * @param cb 回调函数
 */
export function saveOrUpdateStock(data, cb) {
    $.post('/projectManage/new/saveOrUpdateStock', data, cb);
}
// 删除库存
export function deleteStock(stockId, cb) {
    $.post('/projectManage/new/deleteStock', { stockId }, cb);
}

/**
 * 获取库存列表
 * @param performTicketId 票务ID
 * @param cb 回调函数
 */
export function getStockList(performId, cb) {
    $.get('/projectManage/new/getStockList', { performId }, cb);
}

/**
 * 获取合作商家列表
 * @param cb 回调函数
 */
export function getPmList(cb) {
    $.get('/projectManage/new/getPmList', cb);
}

/**
 * 保存权益信息
 * @param data 权益数据
 * @param cb 回调函数
 */
export function saveRights(data, cb) {
    $.post('/projectManage/new/saveRights', data, cb);
}

/**
 * 查询权益详情
 * @param rightId 权益ID
 * @param cb 回调函数
 */
export function getRightsById(rightId, cb) {
    $.get('/projectManage/new/getRightsById', { rightId }, cb);
}

/**
 * 查询权益列表（用于票选择）
 * @param cb 回调函数
 */
export function getRightsList(data, cb) {
    $.get('/projectManage/new/getRightsList', data, cb);
}

/**
 * 查询权益列表（用于票选择）
 * @param cb 回调函数
 */
export function getRightsPageList(data, cb) {
    $.get('/projectManage/new/getRightsPageList', data, cb);
}
/**
 * 删除权益
 * @param rightId 权益ID
 * @param cb 回调函数
 */
export function delRights(rightId, cb) {
    $.post('/projectManage/new/delRights', { rightId }, cb);
}

/**
 * 查询站点列表
 * @param {*} data 
 * @param {*} cb 
 */
export function getSiteList(data, cb) {
    $.get('/projectManage/new/getSiteList', data, cb);
}
/**
 * 获取场次票务对应的权益信息
 * @param {String} data 场次票务ID
 * @param {Function} cb 回调函数
 */
export function getPerformTicketRights(data, cb) {
    $.get('/projectManage/new/getPerformTicketRights', data, cb);
}
// /projectManage/new/getPerformTicketPageList
export function getPerformTicketPageList(data, cb) {
    $.get('/projectManage/new/getPerformTicketPageList', data, cb);
}
/**
 * 获取某个场次下的所有票务信息
 * @param {String} data 场次ID
 * @param {Function} cb 回调函数
 */
export function getPerformTickets2(data, cb) {
    $.get('/projectManage/new/getPerformTickets', data, cb);
}

// 新增团体票
// /projectManage/new/insertOrUpdatePerformTicket

// 团体票分页列表
// /projectManage/new/getTeamPerformTicketPageList?stockId=&ticketName=&performId=315&pageNum=1&pageSize=10
export function getTeamPerformTicketPageList(data, cb) {
    $.get('/projectManage/new/getTeamPerformTicketPageList', data, cb);
}

// 团体票发放记录列表
// /projectManage/new/getTeamTicketSendRecordList?performId=315&stockId=&keyword=&startDate=&endDate=&pageNum=&pageSize=
export function getTeamTicketSendRecordList(data, cb) {
    $.get('/projectManage/new/getTeamTicketSendRecordList', data, cb);
}

// 团体票发放
// /projectManage/new/sendTeamTicket
export function sendTeamTicket(data, cb) {
    $.post('/projectManage/new/sendTeamTicket', data, cb);
}
// 生成的团体票列表
// /projectManage/new/getTeamTicketList?recordId=349&pageNum=1&pageSize=10
export function getTeamTicketList(data, cb) {
    $.get('/projectManage/new/getTeamTicketList', data, cb);
}
// 批量下载二维码
// /projectManage/new/downloadQrcode
export function downloadQrcode(data, cb) {
    $.post('/projectManage/new/downloadQrcodes', data, cb);
}


