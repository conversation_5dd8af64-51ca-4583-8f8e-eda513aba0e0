package com.asiainfo.aisports.service.oauth2;

import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.domain.core.OauthCode;
import com.asiainfo.aisports.domain.core.OauthCodeHist;
import com.asiainfo.aisports.persistence.core.OauthCodeHistMapper;
import com.asiainfo.aisports.persistence.core.OauthCodeMapper;
import com.asiainfo.aisports.service.SequenceWrapper;
import com.asiainfo.aisports.tools.DateCalUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-05-31 16:51
 */
@Service
public class OauthCodeService {

    @Autowired
    private OauthCodeMapper oauthCodeMapper;


    @Autowired
    private OauthCodeHistMapper oauthCodeHistMapper;


    @Autowired
    private SequenceWrapper sequenceWrapper;


    @Transactional
    @TargetDataSource(name = "center")
    public void save(String code, String clientId, String scope, Long userId, String userType) {

        Date date = new Date();
        OauthCode oauthCode = new OauthCode();
        oauthCode.setCode(code);
        oauthCode.setClientId(clientId);
        oauthCode.setScope(scope);
        oauthCode.setUserId(userId);
        oauthCode.setUserType(userType);
        oauthCode.setCreateTime(new Date());
        oauthCode.setExpireTime(DateCalUtil.getDateWithOffset(date, "10m"));
        oauthCodeMapper.insert(oauthCode);

    }


    @TargetDataSource(name = "center")
    public OauthCode getById(String code) {
        return oauthCodeMapper.selectByPrimaryKey(code);
    }

    @TargetDataSource(name = "center")
    public int remove(OauthCode code) {
        oauthCodeMapper.deleteByPrimaryKey(code.getCode());
        OauthCodeHist hist = OauthCodeHist.from(code);
        hist.setId(sequenceWrapper.oauthCodeHistId());
        return oauthCodeHistMapper.insert(hist);

    }
}
