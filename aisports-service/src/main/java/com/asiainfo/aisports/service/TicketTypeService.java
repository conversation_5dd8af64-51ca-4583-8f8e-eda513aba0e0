package com.asiainfo.aisports.service;

import com.asiainfo.aisports.cache.CacheKeyConst;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.TicketConstants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.helper.Errors;
import com.asiainfo.aisports.helper.ResultCode;
import com.asiainfo.aisports.model.*;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.wechat.WechatOrderService;
import com.asiainfo.aisports.tools.CheckUtils;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.JSONObjectUtils;
import com.asiainfo.aisports.tools.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by michael on 15/3/9.
 */
@Service
public class TicketTypeService {

    private static final Logger LOG = LoggerFactory.getLogger(TicketTypeService.class);

    @Autowired
    private TicketTypeMapper ticketTypeMapper;
    @Autowired
    private TicketTypePromService ticketTypePromService;
    @Autowired
    private HolidayService holidayService;
    @Autowired
    private TicketTypeAttrMapper ticketTypeAttrMapper;
    @Autowired
    private PlayProjectAttrMapper playProjectAttrMapper;
    @Autowired
    private VenueCloseDayService venueCloseDayService;
    @Autowired
    private VenueService venueService;
    @Value("${oss.url}")
    private String ossUrl;
    @Autowired
    private ReserveCancelService reserveCancelService;
    @Autowired
    private TicketTypeAttrService ticketTypeAttrService;
    @Autowired
    private TcRoomService tcRoomService;
    @Autowired
    private WechatOrderService wechatOrderService;
    @Autowired
    private ShopService shopService;
    @Autowired
    private ProductService productService;
    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private TicketChannelSaleMapper ticketChannelSaleMapper;

    @Autowired
    private AppAttrService appAttrService;

    @Autowired
    private TicketChannelMapper ticketChannelMapper;

    @Autowired
    private SimpleTicketService simpleTicketService;

    @Autowired
    private TradeTicketAttrMapper tradeTicketAttrMapper;

    @Autowired
    private  TicketTypeTimeMapper ticketTypeTimeMapper;

    /**
     * 根据场馆Id和服务Id获得对应的次票类型和价格
     *
     * @param paramMap 必须含serviceId、venueId、weekday weekday1-8，8表示节假日
     * @return
     */
    @Transactional
    public List<TicketTypePrice> getValidTicketTypePrice(Map<String, Object> paramMap) {
        List<TicketTypePrice> returnList = Lists.newArrayList();

        List<TicketTypePrice> ticketTypePriceList;
        if (Strings.isNullOrEmpty(MapUtils.getString(paramMap, "groupType"))) {
            // 非团购票
            ticketTypePriceList = ticketTypeMapper.selectValidTicketTypePrice(paramMap);
        } else {
            // 团购票
            ticketTypePriceList = ticketTypeMapper.selectValidTicketTypeGroupPrice(paramMap);
        }

        for (final TicketTypePrice ticketTypePrice : ticketTypePriceList) {
            // 优惠活动票
            if (ticketTypePrice.getPromId() != null) {
                // 如果活动已过期，不允许销售
                if (ticketTypePrice.getPromLimitAmount() == null) {
                    continue;
                }

                // 获取剩余票数
                TicketTypePromItem ticketTypePromItem = ticketTypePromService.getPromItemAndRemainNum(ticketTypePrice.getPromId(), ticketTypePrice.getTicketTypeId());
                ticketTypePrice.setDayRemain(ticketTypePromItem.getDayRemain());
                ticketTypePrice.setDayAmount(ticketTypePromItem.getDayAmount());
                ticketTypePrice.setLimitAmount(ticketTypePromItem.getLimitAmount());
            }

            ticketTypePrice.setItemTicketNum(1);

            // 如果是套票，查询出套票下一共有几张有效的子票
            if (Constants.Tag.YES.equals(ticketTypePrice.getGroupTag())) {
                Optional<Integer> optional = ticketTypeMapper.selectGroupTickets(ticketTypePrice.getTicketTypeId()).stream().map(TicketTypePrice::getTicketNum).reduce(Integer::sum);
                optional.ifPresent(ticketTypePrice::setItemTicketNum);

                //套票的子票信息
                ticketTypePrice.setMemberTicketInfos(ticketTypeMapper.getGroupTicketAttr(ticketTypePrice.getTicketTypeId()));
            }

            //通过参数控制是否走新的支付提示页面
            ticketTypePrice.setGroupTicketBindCustId(venueParamConfig.getParam(ticketTypePrice.getCenterId(),Constants.VenueParam.GROUP_TICKET_BIND_CUST_ID,"0"));

            //增加web端售票情况展示
            int total = 0;
            TicketChannelSale param = new TicketChannelSale();
            param.setTicketTypeId(ticketTypePrice.getTicketTypeId());
            param.setSaleDate(new Date());
            List<TicketChannelSale> ticketChannelSaleList = ticketChannelSaleMapper.selectByFields(param);
            for (TicketChannelSale sale : ticketChannelSaleList) {
                if (sale.getChannelId().equals(Constants.Channel.ALL)) {
                    continue;
                }
                int num = sale.getTotalAmount() - sale.getRemainAmount();
                total += num;
            }
            ticketTypePrice.setDailySaleAmount(total);


            // 查询游泳票的属性
            String attrValue = ticketTypeAttrService.getAttrValue(ticketTypePrice.getTicketTypeId(), Constants.TicketTypeAttr.WATER_CERTIFICATE_VALIDITY);
            if (null != attrValue) {// xx d/w/m/y
                String unit = attrValue.substring(attrValue.length() - 1);
                String amount = attrValue.substring(0, attrValue.length() - 1);

                // 转换下
                switch(unit){
                    case "y":
                        unit = "4";
                        break;
                    case "m":
                        unit = "3";
                        break;
                    case "w":
                        unit = "2";
                        break;
                    case "d":
                        unit = "1";
                        break;
                }

                // 我这边设定的是[{"value":"1","name":"深水考试（单日）","amount":1,"unit":"1"},{"value":"2","name":"深水考试（三年）","amount":3,"unit":"4"},{"value":"3","name":"结课考试"}]
                // amount-值  unit-单位（1.天 2.周 3.月 4.年）
                //查询设置的证书设置
                String waterTypeParam = venueParamConfig.getParam(MapUtils.getLong(paramMap,"centerId"), Constants.VenueParam.WATER_CERTIFICATE_TYPE, null);
                if (null != waterTypeParam) {
                    JSONArray jsonArray = JSONObjectUtils.parseArray(waterTypeParam);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        // 匹配下类型、
                        if (jsonObject.containsKey("unit")){
                            if (unit.equals(jsonObject.getString("unit")) && amount.equals(jsonObject.getString("amount"))) {
                                ticketTypePrice.setWaterTicketTag(jsonObject.getString("value"));
                                ticketTypePrice.setWaterTicketTypeName(jsonObject.getString("name"));
                            }
                        }
                    }
                }
            }

            returnList.add(ticketTypePrice);
        }

        return returnList;
    }

    /**
     * 根据场馆Id和服务Id获得对应的次票类型
     *
     * @param venueId
     * @param serviceId
     * @return
     */
    public List<TicketType> getValidTicketType(Long venueId, Long serviceId) {
        return ticketTypeMapper.selectValidTicketType(venueId, serviceId);
    }

    /**
     * 查询团购票类型
     *
     * @param venueId
     * @param serviceId
     * @return
     */
    public List<TicketType> selectGroupBuyingTicketType(Long venueId, Long serviceId) {
        return ticketTypeMapper.selectGroupBuyingTicketType(venueId, serviceId);
    }

    /**
     * 根据Id获得对应的场次
     *
     * @param ticketTypeId
     * @return
     */
    public List<DataMap> getTicketTypeTimes(Long ticketTypeId) {
        return ticketTypeMapper.selectTicketTypeTimesById(ticketTypeId);
    }

    @Cacheable(CacheKeyConst.TICKET_TYPE)
    @Transactional(readOnly = true)
    public TicketType findById(Long ticketTypeId) {
        return ticketTypeMapper.selectByPrimaryKey(ticketTypeId);
    }

    /**
     * 查询场馆下卖的次票
     *
     * @param centerId 中心id
     * @param venueId  场馆id
     * @return
     */
    public List<Map> getTicketsAndServices(Long centerId, Long venueId) {
        return ticketTypeMapper.getTicketsAndServices(centerId, venueId);
    }

    /**
     * 根据主键查票类型
     *
     * @param ticketTypeId
     * @return
     */
    public TicketType selectByPrimaryKey(Long ticketTypeId) {
        return ticketTypeMapper.selectByPrimaryKey(ticketTypeId);
    }

    /**
     * 根据主键查票类型(website)
     *
     * @param ticketTypeId
     * @return
     */
    public TicketType selectByKeyLang(Long ticketTypeId, String lang) {
        return ticketTypeMapper.selectByKeyLang(ticketTypeId, lang);
    }


    /**
     * 查询售票的票信息
     *
     * @param centerId
     * @param venueId
     * @param serviceId
     * @param channelId
     * @param date
     * @param segment
     * @param playProject 特色项目，0表示查询全部，null则查询非特色项目票
     * @return
     */
    @Transactional(readOnly = true)
    public List<TicketTypePrice> getTicketList(Long centerId, Long venueId, Long serviceId, Long channelId, Date date, Short segment, String playProject) {
        List<TicketTypePrice> ticketTypeList = ticketTypeMapper.selectTicketTypeByChannel(centerId, venueId, serviceId,
                channelId, date, String.valueOf(holidayService.getHoliday(centerId, date, true)), segment);
        //特色项目票过滤
        if (!Strings.isNullOrEmpty(playProject)) {
            List<TicketTypePrice> resultList = Lists.newArrayList();
            for (TicketTypePrice ticketTypePrice : ticketTypeList) {
                if (Constants.TicketKind.FEATURED_PROJECT_TICKET.equals(ticketTypePrice.getTicketKind())) {
                    Set<String> projectIds = getProjectSet(ticketTypePrice.getPlayProject());
                    if ("0".equals(playProject) || projectIds.contains(playProject)) { // playProject = 0表示查询全部特色项目票
                        ticketTypePrice.setProjImgList(playProjectAttrMapper.selectProjImgList(new ArrayList<>(projectIds)));
                        resultList.add(ticketTypePrice);
                    }
                }
            }
            return resultList;
        } else { // 排除特色项目票
            return ticketTypeList.stream().filter(a -> !Constants.TicketKind.FEATURED_PROJECT_TICKET.equals(a.getTicketKind())).collect(Collectors.toList());
        }
    }

    /**
     * 查询售票的票信息
     *
     * @param centerId
     * @param venueId
     * @param serviceId
     * @param channelId
     * @param date
     * @param segment
     * @return
     */
    @Transactional(readOnly = true)
    public List<TicketTypePrice> getTicketList(Long centerId, Long venueId, Long serviceId, Long channelId, Date date, Short segment) {
        return ticketTypeMapper.selectTicketTypeByChannel(centerId, venueId, serviceId,
                channelId, date, String.valueOf(holidayService.getHoliday(centerId, date, true)), segment);
    }

    /**
     * 查询售票的票信息
     *
     * @param venueId
     * @param serviceId
     * @param date
     * @param channelId
     * @return
     * @Param centerId
     */
    @Transactional(readOnly = true)
    public List<TicketTypePrice> getTicketListLang(Long centerId, Long venueId, Long serviceId, Long channelId, Date date, Short segment, String lang) {
        return ticketTypeMapper.selectTicketTypeByChannelLang(centerId, venueId, serviceId, channelId, date, String.valueOf(holidayService.getHoliday(centerId, date, true)), segment, lang);
    }

    /**
     * 分页查询售票的票信息
     *
     * @param centerId
     * @param venueId
     * @param serviceId
     * @param channelId
     * @param date
     * @param segment
     * @param rowBounds
     * @return
     */
    @Transactional(readOnly = true)
    public List<TicketTypePrice> getTicketList(Long centerId, Long venueId, Long serviceId, Long channelId, Date date, Short segment, RowBounds rowBounds) {
        return ticketTypeMapper.selectTicketTypeByRowBounds(centerId, venueId, serviceId,
                channelId, date, String.valueOf(holidayService.getHoliday(centerId, date, true)), segment, rowBounds);
    }


    /**
     * @param ticketTypeId
     * @param weekDay
     * @return
     */
    @Transactional(readOnly = true)
    public TicketTypePrice queryTicketTypePrice(Long ticketTypeId, Integer weekDay) {
        return ticketTypeMapper.selectValidPrice(ticketTypeId, weekDay);
    }

    /**
     * 根据 ticketTypeId 和当前周几获取有效价格集
     *
     * @param ticketTypeId
     * @param weekDay
     * @return
     */
    @Transactional(readOnly = true)
    public List<TicketTypePrice> queryTicketTypePriceList(Long ticketTypeId, Integer weekDay) {
        return ticketTypeMapper.selectValidPriceList(ticketTypeId, weekDay);
    }

    /**
     * 根据服务点和venueId 查询票类型
     *
     * @param venueId
     * @param siteId
     * @return
     */
    @Transactional(readOnly = true)
    public List<TicketType> getTicketTypeBySiteId(Long venueId, Long siteId) {
        return ticketTypeMapper.queryTicketTypeBySiteId(venueId, siteId);
    }

    /**
     * 根据主键查询票类型属性
     *
     * @param ticketTypeId
     * @param attrCode
     * @return
     */
    @Transactional(readOnly = true)
    public TicketTypeAttr queryTicketTypeAttrByKey(Long ticketTypeId, String attrCode) {
        TicketTypeAttrKey key = new TicketTypeAttrKey();
        key.setTicketTypeId(ticketTypeId);
        key.setAttrCode(attrCode);
        return ticketTypeAttrMapper.selectByPrimaryKey(key);
    }

    /**
     * 根据场馆Id和服务Id获得对应的次票类型
     *
     * @param venueId
     * @param serviceIds
     * @return
     */
    public List<TicketType> getTicketTypesByServiceIds(Long venueId, List serviceIds) {
        return ticketTypeMapper.selectTicketTypesByServiceIds(venueId, serviceIds);
    }


    /**
     * 获取票属性中所有的projectId集合
     *
     * @param playProjectAttr
     * @return
     */
    private Set<String> getProjectSet(String playProjectAttr) {
        Set<String> playProjectIds = Sets.newHashSet();
        List<Map<String, Object>> projectLists = (List<Map<String, Object>>) JSONObjectUtils.parseArray(playProjectAttr);
        if (projectLists != null) {
            projectLists.forEach(a -> playProjectIds.add((String) a.get("id")));
        }
        return playProjectIds;
    }

    /**
     * 查询API渠道售票
     *
     * @param centerId
     * @param channelId
     * @param venueId
     * @param serviceId
     * @param date
     * @param playProject
     * @param lang
     * @return
     */
    @Transactional
    public ServiceResult findApiTicketList(Long centerId, Long channelId, Long venueId, Long serviceId, Date date, String playProject, String lang, String typeId) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        if (date == null) {
            date = now;
        }
        //判断订票时间是否过期
        if (DateCalUtil.daysBetween(date, now) > 0) {
            return new ServiceResult(Errors.OVER_TIME, ResultCode.OVER_TIME);
        }
        //判断场馆当天服务是否闭馆
        if (!venueCloseDayService.isWorking(venueId, serviceId, date, null)) {
            VenueCloseDay venueCloseDay = venueCloseDayService.findRecentCloseDay(venueId, serviceId);
            return new ServiceResult(Errors.CLOSE_VENUE.getError(), venueCloseDay == null || Strings.isNullOrEmpty(venueCloseDay.getRemark()) ? "场馆闭馆中" : venueCloseDay.getRemark(), ResultCode.VENUE_CLOSED);
        }

        //获取场馆图片map
        Map<Long, String> venueImageMap = Maps.newHashMap();
        List<VenueMediaResource> venueImageList = venueService.findVenueImageList(centerId, venueId);
        for (VenueMediaResource venueMediaResource : venueImageList) {
            Long theVenueId = venueMediaResource.getVenueId();
            String path = venueMediaResource.getPath();
            if (!venueImageMap.containsKey(theVenueId) && !Strings.isNullOrEmpty(path)) {
                venueImageMap.put(theVenueId, path.startsWith("http") ? path : ossUrl + path);
            }
        }
        Map<Long, String> ticketImageMap = Maps.newHashMap();
        if (typeId != null) {
            //获取该场馆下次票图片map
            List<DataMap> ticketImageList = tradeTicketAttrMapper.findTicketImageList(venueId);
            ticketImageList.forEach(dataMap -> {
                Long ticketTypeId = dataMap.getLong("ticketTypeId");
                String image = dataMap.getString("image");
                if (!ticketImageMap.containsKey(ticketTypeId) && !Strings.isNullOrEmpty(image)) {
                    ticketImageMap.put(ticketTypeId, image.startsWith("http") ? image : ossUrl + image);
                }
            });
        }
        //根据渠道查询可销售的票
        List<TicketTypePrice> ticketTypePriceList;
        //校验票销售时间
        String openTime = venueParamConfig.getParam(venueId, Constants.VenueParam.TICKET_SALE_OPEN_TIME);
        if (StringUtils.isNotBlank(openTime) && !venueCloseDayService.judgeOpenTime(openTime)) {
            String[] timeArr = openTime.split("-");
            String time = DateCalUtil.convertTime(timeArr[0], timeArr[1]);
            return new ServiceResult(1, "抱歉，当前未到售票服务时间！售票时间：" + time);
        }
        if (lang != null) {
            ticketTypePriceList = this.getTicketListLang(centerId, venueId, serviceId, channelId, date, DateCalUtil.getSegmentByDate(Calendar.getInstance()).shortValue(), lang);
        } else {
            //渠道非自助终端 次票按场次结束时段过滤，自助终端根据参数配置是否展示全场次 选择否或未配置是否按场次销售参数，只校验销售时间即可
            ticketTypePriceList = this.getTicketList(centerId, venueId, serviceId, channelId, date, DateCalUtil.getSegmentByDate(Calendar.getInstance()).shortValue(), playProject);
        }
        // 如果被配置成闭馆，则从可用票List中删去
        Date nowTrim = DateCalUtil.trim(date);
        ticketTypePriceList.removeIf(ticketTypePrice -> !venueCloseDayService.isWorkingByTicketType(venueId, ticketTypePrice.getServiceId(), nowTrim, ticketTypePrice.getTicketTypeId()));
        // 筛选提前结束售卖的票
        ticketTypePriceList.removeIf(ticketTypePrice -> !simpleTicketService.checkTicketBefroeEndSale(ticketTypePrice.getTicketTypeId(), channelId, ticketTypePrice.getTimeId(), nowTrim));
        if (ticketTypePriceList.isEmpty()) {
            return new ServiceResult(Errors.NO_TICKET_SALES, ResultCode.NO_TICKET_SALES);
        }
        // 平阳全民健身中心不显示计时票
        String isPingYangCenter = venueParamConfig.getParam(centerId, Constants.VenueParam.IS_PINGYANG_CENTER);
        if (Constants.Tag.YES.equals(isPingYangCenter)) {
            ticketTypePriceList = ticketTypePriceList.stream().filter(a -> !TicketConstants.ChargeMode.POST_PAY.equals(a.getChargeMode())).collect(Collectors.toList());
        }
        //获取每种票当天还剩余多少张,单次最大购买数量,以及相应的退票规则
        Map<Long, List<TicketRefundRuleDetail>> refundDetails = findTicketRefundDetails(ticketTypePriceList);
        int lowestPrice = Integer.MAX_VALUE;
        for (TicketTypePrice ticketTypePrice : ticketTypePriceList) {
            Long ticketVenueId = ticketTypePrice.getVenueId();
            Long ticketTypeId = ticketTypePrice.getTicketTypeId();
            ticketTypePrice.setDayRemain(wechatOrderService.getRemainTickets(date, ticketTypePrice.getTicketTypeId(), channelId, ticketTypePrice.getTimeId()).getRemainAmount());
            ticketTypePrice.setRefundRuleDetails(refundDetails.get(ticketTypePrice.getTicketTypeId()));
            //添加票图片
            if (venueImageMap.containsKey(ticketVenueId)) {
                ticketTypePrice.setTicketImageUrl(venueImageMap.get(ticketVenueId));
            }
            if (typeId != null) {
                ticketTypePrice.setTicketImageUrl(ticketImageMap.get(ticketTypeId));
            }
            //展示套票下子票信息
            if (Constants.Tag.YES.equals(ticketTypePrice.getGroupTag())) {
                ticketTypePrice.setMemberTicketInfos(ticketTypeMapper.getGroupTicketAttr(ticketTypePrice.getTicketTypeId()));
            }
            try {
                //判断次票是否指定使用教室，若指定了则判断某场次教室是否被团课或者包场占用
                if (isRoomInUse(ticketTypePrice, date)) {
                    ticketTypePrice.setDayRemain(0);
                }
            } catch (Exception e) {
                LOG.info("查询房间状况异常:{}", e.toString());
                throw new ServiceException("系统繁忙，请稍后重试");
            }
            //获取票的最低价格
            if (ticketTypePrice.getPrice() != null && ticketTypePrice.getPrice() < lowestPrice) {
                lowestPrice = ticketTypePrice.getPrice();
            }
            //获取票的销量
//            if (typeId != null) {
//                ticketTypePrice.setTotalSaleCount(tradeTicketMapper.selectTicketSalesCount(ticketVenueId, ticketTypePrice.getTicketTypeId()));
//            }
        }
//        if (typeId != null) {
//            ticketTypePriceList = ticketTypePriceList.stream().sorted(Comparator.comparing(TicketTypePrice::getTotalSaleCount).reversed()).collect(Collectors.toList());
//        }
        serviceResult.set("ticketTypeList", ticketTypePriceList).set("lowestPrice", lowestPrice);

        /*Shop shop = shopService.getShopByChannel(venueId, centerId, Constants.Channel.WECHAT);
        if (shop != null) {
            //推荐销售卡
            List<DataMap> recommendCards = productService.getRecommendCards(shop.getShopId(), null, Constants.GoodsType.SPECIAL_CARD, 1);
            if (!recommendCards.isEmpty()) {
                serviceResult.set("recommendCardInfo", recommendCards.get(0));
            }
        }*/

        return serviceResult;
    }

    public Map<Long, List<TicketRefundRuleDetail>> findTicketRefundDetails(List<TicketTypePrice> ticketTypes) {
        Map<Long, List<TicketRefundRuleDetail>> map = Maps.newHashMap();
        ticketTypes.forEach(p -> {
            if (!map.containsKey(p.getTicketTypeId())) {
                map.put(p.getTicketTypeId(), reserveCancelService.getTicketRefundRuleDetailList(p.getTicketTypeId()));
            }
        });
        return map;
    }

    /**
     * 设置票信息
     *
     * @param ticketInfo
     * @param groupBuyingTicket
     * @param ticketType
     */
    public void setGroupBuyTicketInfo(Map<String, Object> ticketInfo, GroupBuyingTicket groupBuyingTicket, TicketType ticketType, String groupBuyingType) {
        ticketInfo.put("ticketTypeId", groupBuyingTicket.getTicketTypeId()); //票类型Id
        ticketInfo.put("timeId", groupBuyingTicket.getTimeId()); //场次Id

        // 押金
        TicketTypeAttr cashPledge = ticketTypeAttrService.findByKey(groupBuyingTicket.getTicketTypeId(), Constants.TicketTypeAttr.CASH_PLEDGE);
        if (cashPledge != null && !Strings.isNullOrEmpty(cashPledge.getAttrValue())) {
            ticketInfo.put("cashPledge", cashPledge.getAttrValue());
        }

        // 票数量
        ticketInfo.put("itemTicketNum", 1);

        // 服务项
        long serviceId = 0L;
        if (Constants.Tag.YES.equals(ticketType.getGroupTag())) {
            int itemTicketNum = 0;

            // 套票取子票的serviceId
            List<TicketTypePrice> ticketTypePrices = ticketTypeMapper.selectGroupTickets(ticketType.getTicketTypeId());
            for (TicketTypePrice ticketTypePrice : ticketTypePrices) {
                itemTicketNum += ticketTypePrice.getTicketNum();
                if (ticketTypePrice.getServiceId() != null) {
                    serviceId = ticketTypePrice.getServiceId();
                }
            }

            ticketInfo.put("itemTicketNum", itemTicketNum);
        } else {
            serviceId = ticketType.getServiceId();
        }
        ticketInfo.put("serviceId", serviceId);

        // 价格
        TicketTypePrice ticketTypePrice = ticketTypeMapper.selectTicketTypeGroupPrice(ticketType.getTicketTypeId(), groupBuyingTicket.getTimeId(), groupBuyingType);
        if (ticketTypePrice == null) {
            ticketTypePrice = ticketTypeMapper.selectTicketTypePrice(ticketType.getTicketTypeId(), holidayService.getHoliday(ticketType.getCenterId(), new Date(), true), groupBuyingTicket.getTimeId(), Constants.StrategyType.GROUP_BUYING_PRICE);
        }
        if (ticketTypePrice != null) {
            ticketInfo.put("groupBuyTicketPrice", ticketTypePrice.getPrice());
        }
    }

    /**
     * 判断次票是否占用房间
     *
     * @return
     */
    private boolean isRoomInUse(TicketTypePrice ticketTypePrice, Date date) {
        TicketTypeAttr ticketTypeAttr = ticketTypeAttrService.findByKey(ticketTypePrice.getTicketTypeId(), Constants.TicketTypeAttr.ROOMS);
        if (ticketTypeAttr != null && ticketTypeAttr.getAttrValue().split(",").length == 1) {
            Long roomId = Long.parseLong(ticketTypeAttr.getAttrValue());
            DataMap useDetail = tcRoomService.getRoomUseDetailByTimeId(roomId, date, ticketTypePrice.getTimeId());
            //限制使用 人数
            if (useDetail.getInteger("remain") == 0) {
                return true;
            }
            // 1003601 添加定时任务没有占用教室的补丁逻辑
            if (useDetail.containsKey("lessonCount") && useDetail.getInteger("lessonCount") > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据场馆id和服务项查询票类型
     *
     * @param venueId
     * @param serviceIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<TicketType> findListByServiceIds(Long venueId, List<String> serviceIds) {
        return ticketTypeMapper.findListByServiceIds(venueId, serviceIds);
    }

    /**
     * 分页查询售票信息
     *
     * @param centerId
     * @param channelId
     * @param venueId
     * @param serviceId
     * @param date
     * @return
     */
    @Transactional
    public ServiceResult findTicketListByRowBounds(Long centerId, Long channelId, Long venueId, Long serviceId, Date date, RowBounds rowBounds) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        if (date == null) {
            date = now;
        }
        //判断订票时间是否过期
        if (DateCalUtil.daysBetween(date, now) > 0) {
            return new ServiceResult(Errors.OVER_TIME, ResultCode.OVER_TIME);
        }
        //判断场馆当天服务是否闭馆
        if (!venueCloseDayService.isWorking(venueId, serviceId, date, null)) {
            VenueCloseDay venueCloseDay = venueCloseDayService.findRecentCloseDay(venueId, serviceId);
            return new ServiceResult(Errors.CLOSE_VENUE.getError(), venueCloseDay == null || Strings.isNullOrEmpty(venueCloseDay.getRemark()) ? "场馆闭馆中" : venueCloseDay.getRemark(), ResultCode.VENUE_CLOSED);
        }

        //根据渠道查询可销售的票
        List<TicketTypePrice> ticketTypePriceList;
        //校验票销售时间
        String openTime = venueParamConfig.getParam(venueId, Constants.VenueParam.TICKET_SALE_OPEN_TIME);
        if (StringUtils.isNotBlank(openTime) && !venueCloseDayService.judgeOpenTime(openTime)) {
            String[] timeArr = openTime.split("-");
            String time = DateCalUtil.convertTime(timeArr[0], timeArr[1]);
            return new ServiceResult(1, "抱歉，当前未到售票服务时间！售票时间：" + time);
        }
        ticketTypePriceList = this.getTicketList(centerId, venueId, serviceId, channelId, date, DateCalUtil.getSegmentByDate(Calendar.getInstance()).shortValue(), rowBounds);
        // 如果被配置成闭馆，则从可用票List中删去
        Date nowTrim = DateCalUtil.trim(date);
        ticketTypePriceList.removeIf(ticketTypePrice -> !venueCloseDayService.isWorkingByTicketType(venueId, ticketTypePrice.getServiceId(), nowTrim, ticketTypePrice.getTicketTypeId()));
        if (ticketTypePriceList.isEmpty()) {
            return new ServiceResult(Errors.NO_TICKET_SALES, ResultCode.NO_TICKET_SALES);
        }

        //获取每种票当天还剩余多少张,单次最大购买数量,以及相应的退票规则
        Map<Long, List<TicketRefundRuleDetail>> refundDetails = findTicketRefundDetails(ticketTypePriceList);
        int lowestPrice = Integer.MAX_VALUE;
        for (TicketTypePrice ticketTypePrice : ticketTypePriceList) {
            TicketChannelSale ticketChannelSale = wechatOrderService.getRemainTickets(date, ticketTypePrice.getTicketTypeId(), channelId, ticketTypePrice.getTimeId());
            ticketTypePrice.setLimitAmount(ticketChannelSale.getTotalAmount());
            ticketTypePrice.setDayRemain(ticketChannelSale.getRemainAmount());
            ticketTypePrice.setRefundRuleDetails(refundDetails.get(ticketTypePrice.getTicketTypeId()));
            try {
                //判断次票是否指定使用教室，若指定了则判断某场次教室是否被团课或者包场占用
                if (isRoomInUse(ticketTypePrice, date)) {
                    ticketTypePrice.setDayRemain(0);
                }
            } catch (Exception e) {
                LOG.info("查询房间状况异常:{}", e.toString());
                throw new ServiceException("系统繁忙，请稍后重试");
            }
            //获取票的最低价格
            if (ticketTypePrice.getPrice() != null && ticketTypePrice.getPrice() < lowestPrice) {
                lowestPrice = ticketTypePrice.getPrice();
            }
        }
        serviceResult.set("ticketTypeList", new PageInfo<>(ticketTypePriceList)).set("lowestPrice", lowestPrice);
        return serviceResult;
    }


    /**
     * 查询API渠道售票
     *
     * @param centerId
     * @param channelId
     * @param venueId
     * @param serviceId
     * @param date
     * @return
     */
    @Transactional
    public ServiceResult findAppTicketList(Long centerId, Long channelId, Long venueId, Long serviceId, Date date, Long appId) {

        ServiceResult serviceResult = new ServiceResult();

        Date now = new Date();
        if (date == null) {
            date = now;
        }
        //判断订票时间是否过期
        if (DateCalUtil.daysBetween(date, now) > 0) {
            return new ServiceResult(Errors.OVER_TIME, ResultCode.OVER_TIME);
        }
        //判断场馆当天服务是否闭馆
        if (!venueCloseDayService.isWorking(venueId, serviceId, date, null)) {
            VenueCloseDay venueCloseDay = venueCloseDayService.findRecentCloseDay(venueId, serviceId);
            return new ServiceResult(Errors.CLOSE_VENUE.getError(), venueCloseDay == null || Strings.isNullOrEmpty(venueCloseDay.getRemark()) ? "场馆闭馆中" : venueCloseDay.getRemark(), ResultCode.VENUE_CLOSED);
        }

        //获取场馆图片map
        Map<Long, String> venueImageMap = Maps.newHashMap();
        List<VenueMediaResource> venueImageList = venueService.findVenueImageList(centerId, venueId);
        for (VenueMediaResource venueMediaResource : venueImageList) {
            Long theVenueId = venueMediaResource.getVenueId();
            String path = venueMediaResource.getPath();
            if (!venueImageMap.containsKey(theVenueId) && !Strings.isNullOrEmpty(path)) {
                venueImageMap.put(theVenueId, path.startsWith("http") ? path : ossUrl + path);
            }
        }

        //校验票销售时间
        String openTime = venueParamConfig.getParam(venueId, Constants.VenueParam.TICKET_SALE_OPEN_TIME);
        if (StringUtils.isNotBlank(openTime) && !venueCloseDayService.judgeOpenTime(openTime)) {
            String[] timeArr = openTime.split("-");
            String time = DateCalUtil.convertTime(timeArr[0], timeArr[1]);
            return new ServiceResult(1, "抱歉，当前未到售票服务时间！售票时间：" + time);
        }

        //根据渠道查询可销售的票
        Map<String, Object> param = formatTicketQueryParam(centerId, venueId, serviceId, channelId, date, appId);

        //渠道非自助终端 次票按场次结束时段过滤，自助终端根据参数配置是否展示全场次 选择否或未配置是否按场次销售参数，按票场次结束时间过滤
        List<TicketTypePrice> ticketTypePriceList = ticketTypeMapper.selectAppTicketTypeByChannel(param);

        // 如果被配置成闭馆，则从可用票List中删去
        Date nowTrim = DateCalUtil.trim(date);
        ticketTypePriceList.removeIf(ticketTypePrice -> !venueCloseDayService.isWorkingByTicketType(venueId, ticketTypePrice.getServiceId(), nowTrim, ticketTypePrice.getTicketTypeId()));
        if (ticketTypePriceList.isEmpty()) {
            return new ServiceResult(Errors.NO_TICKET_SALES, ResultCode.NO_TICKET_SALES);
        }
        for (TicketTypePrice ticketTypePrice : ticketTypePriceList) {
            ticketTypePrice.setDayRemain(wechatOrderService.getRemainTickets(date, ticketTypePrice.getTicketTypeId(), channelId, ticketTypePrice.getTicketTimeId()).getRemainAmount());

            try {
                //判断次票是否指定使用教室，若指定了则判断某场次教室是否被团课或者包场占用

                if (isRoomInUse(ticketTypePrice, date)) {
                    ticketTypePrice.setDayRemain(0);
                }
            } catch (Exception e) {
                LOG.error("findAppTicketList 查询房间状况异常", e);
                throw new ServiceException("系统繁忙，请稍后重试");
            }
        }

        List<Object> dataList = filterProperties(ticketTypePriceList, appId);

        serviceResult.set("data", dataList);

        return serviceResult;
    }

    private List<Object> filterProperties(List<TicketTypePrice> ticketTypePriceList, Long appId) {
        String propertiesAttr = appAttrService.queryAppAttrValue(appId, Constants.AppAttrCode.TICKET_PROPERTIES);
        List<Object> dataList = Lists.newArrayList();
        if (StringUtils.isBlank(propertiesAttr)) {
            TicketAppPrice ticketAppPrice;
            for (TicketTypePrice ticketTypePrice : ticketTypePriceList) {
                ticketAppPrice = new TicketAppPrice();
                BeanUtils.copyProperties(ticketTypePrice, ticketAppPrice);
                dataList.add(ticketAppPrice);
            }
        } else {
            String[] properties = StringUtils.split(propertiesAttr);
            Map<String, Object> data;
            for (TicketTypePrice ticketTypePrice : ticketTypePriceList) {
                data = new HashMap<>();
                for (String property : properties) {
                    data.put(property, getPropertyValue(ticketTypePrice, property));

                }
                dataList.add(data);

            }

        }


        return dataList;
    }

    private Object getPropertyValue(TicketTypePrice ticketTypePrice, String property) {
        PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(ticketTypePrice.getClass(), property);
        if (propertyDescriptor == null) {
            return null;
        }
        Method readMethod = propertyDescriptor.getReadMethod();
        if (readMethod == null) {
            return null;
        }
        readMethod.setAccessible(true);
        try {
            return readMethod.invoke(ticketTypePrice);
        } catch (Exception e) {
            LOG.error("getPropertyValue ,readValue for [" + property + "] error", e);
            throw new ServiceException("系统错误[" + e.getMessage() + "]，请联系管理员");
        }
    }

    private Map<String, Object> formatTicketQueryParam(Long centerId, Long venueId, Long serviceId, Long channelId, Date date, Long appId) {
        Map<String, Object> param = new HashMap<>();
        param.put("centerId", centerId);
        param.put("venueId", venueId);
        param.put("serviceId", serviceId);
        param.put("channelId", channelId);
        param.put("date", date);
        param.put("segment", DateCalUtil.getSegmentByDate(Calendar.getInstance()).shortValue());
        param.put("weekday", String.valueOf(holidayService.getHoliday(centerId, date, true)));

        String additionalParamStr = appAttrService.queryAppAttrValue(appId, Constants.AppAttrCode.TICKET_QUERY_PARAM);
        DataMap additionalParamMap = JacksonUtil.readJson2Object(additionalParamStr, DataMap.class);
        if (additionalParamMap != null) {
            param.putAll(additionalParamMap);
        }

        return param;
    }

    /**
     * 判断下第三方app的权限
     * @param appId
     */
    private boolean checkAppAccessPermission(Long appId, String attrCode, Object target, boolean notNull) {
        if (target == null) {
            return !notNull;
        }
        String str = target.toString();
        if (StringUtils.isBlank(str)) {
            return !notNull;
        }
        return appAttrService.checkValueExist(appId, attrCode, str);
    }




    public List<TicketBuyInfo> checkAppTicketInfo(Long appId, Long channelId, String ticketInfo, LoginStaff loginStaff) {
        CheckUtils.check(StringUtils.isNotBlank(ticketInfo), "请填写购票信息");

        List<TicketBuyInfo> ticketBuyInfos = JacksonUtil.readJson2Object(ticketInfo, new TypeReference<List<TicketBuyInfo>>() {
        });
        CheckUtils.check(CollectionUtils.isNotEmpty(ticketBuyInfos), "请填写购票信息");

        //Long venueId = null;
        Long serviceId = null;

        Date date = new Date();

        int totalNum = 0;
        for (TicketBuyInfo ticketBuyInfo : ticketBuyInfos) {
            Long ticketTypeId = ticketBuyInfo.getTicketTypeId();

            if (ticketTypeId == null || ticketBuyInfo.getTicketTimeId() == null || ticketBuyInfo.getNum() < 1) {
                throw new ServiceException("购票参数不全");

            }
            CheckUtils.check(ticketTypeId != null && ticketBuyInfo.getTicketTimeId() != null && ticketBuyInfo.getNum() > 1, "购票信息填写有误");
            Integer weekDay = holidayService.getHoliday(loginStaff.getCenterId(), date, true);
            TicketTypePrice ticketTypePrice = ticketTypeMapper.selectTicketTypePrice(ticketTypeId, weekDay, ticketBuyInfo.getTicketTimeId(), null);
            CheckUtils.check(ticketTypePrice != null, "未查询到ticketTypeId=[%s]的价格信息", ticketTypeId);
            CheckUtils.check(ticketTypePrice.getCashPledge() == null || ticketTypePrice.getCashPledge() == 0L, "暂不支持押金票购买");

            //if(venueId == null ){
            //    venueId = ticketTypePrice.getVenueId();
            //}

            if(serviceId == null ){
                serviceId = ticketTypePrice.getServiceId();
            }
            //CheckUtils.check(venueId.equals(ticketTypePrice.getVenueId()), "一次购票只能是一个场馆");
            CheckUtils.check(serviceId.equals(ticketTypePrice.getServiceId()), "一次购票只能是一个项目");
            //CheckUtils.check(loginStaff.getVenueId().equals(venueId), "票所属场馆和目标场馆不一致");
            CheckUtils.check(ticketTypePrice.getPrice() != null, "所选票的价格不正确，不能购买");
            ticketBuyInfo.setPrice(ticketTypePrice.getPrice() * ticketBuyInfo.getNum());
            totalNum += ticketBuyInfo.getNum();

            TicketChannelKey ticketChannelParam = new TicketChannelKey();
            ticketChannelParam.setTicketTypeId(ticketTypeId);
            ticketChannelParam.setChannelId(channelId);
            TicketChannel ticketChannel = ticketChannelMapper.selectByPrimaryKey(ticketChannelParam);
            CheckUtils.check(ticketChannel != null, "未查询到ticketTypeId=[%s]的销售信息", ticketTypeId);
            if (ticketChannel.getBuyLimit() != null && ticketChannel.getBuyLimit() < ticketBuyInfo.getNum()) {
                throw new ServiceException("ticketTypeId=[" + ticketTypeId + "]单次购买数量为：" + ticketChannel.getBuyLimit());
            }
            Integer remainAmount = wechatOrderService.getRemainTickets(date, ticketTypePrice.getTicketTypeId(), channelId, ticketTypePrice.getTicketTimeId()).getRemainAmount();
            if (remainAmount < ticketBuyInfo.getNum()) {
                throw new ServiceException("ticketTypeId=[" + ticketTypeId + "]当日剩余可售数量为[" + remainAmount + "]");
            }
        }

        String limit = appAttrService.queryAppAttrValue(appId, Constants.AppAttrCode.TICKET_LIMIT);
        if (StringUtils.isNotBlank(limit) && StringUtils.isNumeric(limit) && Integer.parseInt(limit) < totalNum) {
            throw new ServiceException("单次购票数量[" + Integer.parseInt(limit) + "]");

        }

        return ticketBuyInfos;


    }

    @Transactional(readOnly = true)
    public List<TicketTypeTime> getTicketTypeTime(String attrValue) {
        if(StringUtils.isNotEmpty(attrValue)){
            String[] ids = attrValue.split(",");
            List<TicketTypeTime> typeTimes = ticketTypeTimeMapper.selectByIds(ids);
            return typeTimes;
        }
        return null;
    }
}
