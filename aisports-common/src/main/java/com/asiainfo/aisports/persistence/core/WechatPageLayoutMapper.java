package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.WechatPageLayout;
import com.asiainfo.aisports.domain.core.WechatPageLayoutKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface WechatPageLayoutMapper {
    @Delete({
        "delete from wechat_page_layout",
        "where page_id = #{pageId,jdbcType=BIGINT}",
          "and element_id = #{elementId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(WechatPageLayoutKey key);

    @Insert({
        "insert into wechat_page_layout (page_id, element_id, ",
        "display_order)",
        "values (#{pageId,jdbcType=BIGINT}, #{elementId,jdbcType=BIGINT}, ",
        "#{displayOrder,jdbcType=INTEGER})"
    })
    int insert(WechatPageLayout record);

    @Select({
        "select",
        "page_id, element_id, display_order",
        "from wechat_page_layout",
        "where page_id = #{pageId,jdbcType=BIGINT}",
          "and element_id = #{elementId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="page_id", property="pageId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="element_id", property="elementId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="display_order", property="displayOrder", jdbcType=JdbcType.INTEGER)
    })
    WechatPageLayout selectByPrimaryKey(WechatPageLayoutKey key);

    @Update({
        "update wechat_page_layout",
        "set display_order = #{displayOrder,jdbcType=INTEGER}",
        "where page_id = #{pageId,jdbcType=BIGINT}",
          "and element_id = #{elementId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(WechatPageLayout record);

    @Select({
        "<script>",
        "select",
        "page_id, element_id, display_order",
        "from wechat_page_layout",
        "<where>",
        "<if test=\"pageId != null\">and page_id = #{pageId,jdbcType=BIGINT}</if>",
        "<if test=\"elementId != null\">and element_id = #{elementId,jdbcType=BIGINT}</if>",
        "<if test=\"displayOrder != null\">and display_order = #{displayOrder,jdbcType=INTEGER}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="page_id", property="pageId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="element_id", property="elementId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="display_order", property="displayOrder", jdbcType=JdbcType.INTEGER)
    })
    List<WechatPageLayout> selectByFields(WechatPageLayout param);

    @Update({
        "<script>",
        "update wechat_page_layout",
        "<set>",
        "<if test=\"displayOrder != null\">display_order = #{displayOrder,jdbcType=INTEGER},</if>",
        "</set>",
        "where page_id = #{pageId,jdbcType=BIGINT}",
          "and element_id = #{elementId,jdbcType=BIGINT}",
        "</script>"
    })

    int updateByPrimaryKeySelective(WechatPageLayout record);
}