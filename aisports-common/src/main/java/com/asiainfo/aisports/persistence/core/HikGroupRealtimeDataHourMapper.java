package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.HikGroupRealtimeDataHour;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;

public interface HikGroupRealtimeDataHourMapper {
    @Delete({
        "delete from hik_group_realtime_data_hour",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into hik_group_realtime_data_hour (id, group_id, ",
        "region_id, center_id, ",
        "venue_id, equipment_id, ",
        "group_time, group_date, count_hour,",
        "enter_num, exit_num, enter_count, exit_count, pass, ",
        "hold_value, all_enter, ",
        "all_exit, create_time, ",
        "update_time)",
        "values (#{id,jdbcType=BIGINT}, #{groupId,jdbcType=VARCHAR}, ",
        "#{regionId,jdbcType=VARCHAR}, #{centerId,jdbcType=BIGINT}, ",
        "#{venueId,jdbcType=BIGINT}, #{equipmentId,jdbcType=BIGINT}, ",
        "#{groupTime,jdbcType=TIMESTAMP}, #{groupDate,jdbcType=DATE}, #{groupDate,jdbcType=VARCHAR}, ",
        "#{enterNum,jdbcType=BIGINT}, #{exitNum,jdbcType=BIGINT},",
        " #{enterCount,jdbcType=BIGINT}, #{exitCount,jdbcType=BIGINT}, #{pass,jdbcType=BIGINT}, ",
        "#{holdValue,jdbcType=BIGINT}, #{allEnter,jdbcType=BIGINT}, ",
        "#{allExit,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{updateTime,jdbcType=TIMESTAMP})"
    })
    int insert(HikGroupRealtimeDataHour record);

    @Select({
        "select",
        "id, group_id, region_id, center_id, venue_id, equipment_id, group_time, group_date, ",
        "enter_num, exit_num, pass, hold_value, all_enter, all_exit, create_time, update_time",
        "from hik_group_realtime_data_hour",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="group_id", property="groupId", jdbcType=JdbcType.VARCHAR),
        @Result(column="region_id", property="regionId", jdbcType=JdbcType.VARCHAR),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="equipment_id", property="equipmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="group_time", property="groupTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="group_date", property="groupDate", jdbcType=JdbcType.DATE),
        @Result(column="enter_num", property="enterNum", jdbcType=JdbcType.BIGINT),
        @Result(column="exit_num", property="exitNum", jdbcType=JdbcType.BIGINT),
        @Result(column="pass", property="pass", jdbcType=JdbcType.BIGINT),
        @Result(column="hold_value", property="holdValue", jdbcType=JdbcType.BIGINT),
        @Result(column="all_enter", property="allEnter", jdbcType=JdbcType.BIGINT),
        @Result(column="all_exit", property="allExit", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    HikGroupRealtimeDataHour selectByPrimaryKey(Long id);

    @Update({
        "update hik_group_realtime_data_hour",
        "set group_id = #{groupId,jdbcType=VARCHAR},",
          "region_id = #{regionId,jdbcType=VARCHAR},",
          "center_id = #{centerId,jdbcType=BIGINT},",
          "venue_id = #{venueId,jdbcType=BIGINT},",
          "equipment_id = #{equipmentId,jdbcType=BIGINT},",
          "group_time = #{groupTime,jdbcType=TIMESTAMP},",
          "group_date = #{groupDate,jdbcType=DATE},",
          "enter_num = #{enterNum,jdbcType=BIGINT},",
          "exit_num = #{exitNum,jdbcType=BIGINT},",
          "pass = #{pass,jdbcType=BIGINT},",
          "hold_value = #{holdValue,jdbcType=BIGINT},",
          "all_enter = #{allEnter,jdbcType=BIGINT},",
          "all_exit = #{allExit,jdbcType=BIGINT},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(HikGroupRealtimeDataHour record);

    @Select({
        "<script>",
        "select",
        "id, group_id, region_id, center_id, venue_id, equipment_id, group_time, group_date, enter_num, exit_num, pass, hold_value, all_enter, all_exit, create_time, update_time",
        "from hik_group_realtime_data_hour",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"groupId != null\">and group_id = #{groupId,jdbcType=VARCHAR}</if>",
        "<if test=\"regionId != null\">and region_id = #{regionId,jdbcType=VARCHAR}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"equipmentId != null\">and equipment_id = #{equipmentId,jdbcType=BIGINT}</if>",
        "<if test=\"groupTime != null\">and group_time = #{groupTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"groupDate != null\">and group_date = #{groupDate,jdbcType=DATE}</if>",
        "<if test=\"enterNum != null\">and enter_num = #{enterNum,jdbcType=BIGINT}</if>",
        "<if test=\"exitNum != null\">and exit_num = #{exitNum,jdbcType=BIGINT}</if>",
        "<if test=\"pass != null\">and pass = #{pass,jdbcType=BIGINT}</if>",
        "<if test=\"holdValue != null\">and hold_value = #{holdValue,jdbcType=BIGINT}</if>",
        "<if test=\"allEnter != null\">and all_enter = #{allEnter,jdbcType=BIGINT}</if>",
        "<if test=\"allExit != null\">and all_exit = #{allExit,jdbcType=BIGINT}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="group_id", property="groupId", jdbcType=JdbcType.VARCHAR),
        @Result(column="region_id", property="regionId", jdbcType=JdbcType.VARCHAR),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="equipment_id", property="equipmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="group_time", property="groupTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="group_date", property="groupDate", jdbcType=JdbcType.DATE),
        @Result(column="enter_num", property="enterNum", jdbcType=JdbcType.BIGINT),
        @Result(column="exit_num", property="exitNum", jdbcType=JdbcType.BIGINT),
        @Result(column="pass", property="pass", jdbcType=JdbcType.BIGINT),
        @Result(column="hold_value", property="holdValue", jdbcType=JdbcType.BIGINT),
        @Result(column="all_enter", property="allEnter", jdbcType=JdbcType.BIGINT),
        @Result(column="all_exit", property="allExit", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<HikGroupRealtimeDataHour> selectByFields(HikGroupRealtimeDataHour param);

    @Update({
        "<script>",
        "update hik_group_realtime_data_hour",
        "<set>",
        "<if test=\"groupId != null\">group_id = #{groupId,jdbcType=VARCHAR},</if>",
        "<if test=\"regionId != null\">region_id = #{regionId,jdbcType=VARCHAR},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "<if test=\"equipmentId != null\">equipment_id = #{equipmentId,jdbcType=BIGINT},</if>",
        "<if test=\"groupTime != null\">group_time = #{groupTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"groupDate != null\">group_date = #{groupDate,jdbcType=DATE},</if>",
        "<if test=\"enterNum != null\">enter_num = #{enterNum,jdbcType=BIGINT},</if>",
        "<if test=\"exitNum != null\">exit_num = #{exitNum,jdbcType=BIGINT},</if>",
        "<if test=\"pass != null\">pass = #{pass,jdbcType=BIGINT},</if>",
        "<if test=\"holdValue != null\">hold_value = #{holdValue,jdbcType=BIGINT},</if>",
        "<if test=\"allEnter != null\">all_enter = #{allEnter,jdbcType=BIGINT},</if>",
        "<if test=\"allExit != null\">all_exit = #{allExit,jdbcType=BIGINT},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(HikGroupRealtimeDataHour record);

    @Insert({
        "<script>",
        "insert into hik_group_realtime_data_hour (id, group_id, ",
        "region_id, center_id, ",
        "venue_id, equipment_id, ",
        "group_time, group_date, count_hour,",
        "enter_num, exit_num, enter_count, exit_count, pass, ",
        "hold_value, all_enter, ",
        "all_exit, create_time, ",
        "update_time)",
        "values",
        "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
        "(#{item.id,jdbcType=BIGINT}, #{item.groupId,jdbcType=VARCHAR}, ",
        "#{item.regionId,jdbcType=VARCHAR}, #{item.centerId,jdbcType=BIGINT}, ",
        "#{item.venueId,jdbcType=BIGINT}, #{item.equipmentId,jdbcType=BIGINT}, ",
        "#{item.groupTime,jdbcType=TIMESTAMP}, #{item.groupDate,jdbcType=DATE}, #{item.countHour,jdbcType=VARCHAR},",
        "#{item.enterNum,jdbcType=BIGINT}, #{item.exitNum,jdbcType=BIGINT},",
        "#{item.enterCount,jdbcType=BIGINT}, #{item.exitCount,jdbcType=BIGINT}, #{item.pass,jdbcType=BIGINT}, ",
        "#{item.holdValue,jdbcType=BIGINT}, #{item.allEnter,jdbcType=BIGINT}, ",
        "#{item.allExit,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},",
        "#{item.updateTime,jdbcType=TIMESTAMP})",
        "</foreach>",
        "</script>"
    })
    void batchSave(List<HikGroupRealtimeDataHour> realTimeInfos);

    @Select({
            "<script>",
            "select",
            "a.center_id, a.venue_id, a.equipment_id, e.device_id, a.group_time, a.group_date, IFNULL(SUM(a.enter_count),0) as all_enter, IFNULL(SUM(a.exit_count),0) as all_exit",
            "from hik_group_realtime_data_hour a left join venue_center.customer_flow_equipment e on a.equipment_id = e.equipment_id and a.venue_id = e.venue_id",
            "<where>",
            "<if test=\"centerId != null\">and a.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"equipmentId != null\">and a.equipment_id = #{equipmentId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTime != null\">and a.group_time = #{groupTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"countHour != null\">and a.count_hour = #{countHour,jdbcType=TIMESTAMP}</if>",
            "<if test=\"groupStartTime != null\">and a.group_time &gt;= #{groupStartTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"groupEndTime != null\">and a.group_time &lt;= #{groupEndTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"groupDate != null\">and a.group_date = #{groupDate,jdbcType=DATE}</if>",
            "<if test=\"startTime != null\">and a.group_date &gt;= #{startTime,jdbcType=DATE}</if>",
            "<if test=\"endTime != null\">and a.group_date &lt;= #{endTime,jdbcType=DATE}</if>",
            "<if test=\"equipmentIds != null\">and a.equipment_id in ",
            "<foreach collection=\"equipmentIds\" item=\"id\" open=\"(\" separator=\",\" close=\")\">#{id, jdbcType=BIGINT}</foreach>",
            "</if>",
            "<if test=\"outerEquipmentType != null\">and e.outer_equipment_type = #{outerEquipmentType,jdbcType=VARCHAR} and e.area_id is not null</if>",
            "</where>",
            "group by a.group_date,a.equipment_id",
            "</script>",
    })
    @Results({
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
            @Result(column="equipment_id", property="equipmentId", jdbcType=JdbcType.BIGINT),
            @Result(column="device_id", property="deviceId", jdbcType=JdbcType.VARCHAR),
            @Result(column="group_time", property="groupTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="group_date", property="groupDate", jdbcType=JdbcType.DATE),
            @Result(column="all_enter", property="allEnter", jdbcType=JdbcType.BIGINT),
            @Result(column="all_exit", property="allExit", jdbcType=JdbcType.BIGINT),
    })
    List<DataMap> selectHikGroupRealtimeDataHour(ParamMap param);

    @Select({
        "select",
        "id, group_id, region_id, center_id, venue_id, equipment_id, group_time, group_date, count_hour,",
        "enter_num, exit_num, pass, hold_value, all_enter, all_exit, create_time, update_time",
        "from hik_group_realtime_data_hour",
        "where group_id = #{groupId,jdbcType=VARCHAR} and group_date = #{now,jdbcType=DATE}",
        "order by create_time desc limit 1"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="group_id", property="groupId", jdbcType=JdbcType.VARCHAR),
        @Result(column="region_id", property="regionId", jdbcType=JdbcType.VARCHAR),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="equipment_id", property="equipmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="group_time", property="groupTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="group_date", property="groupDate", jdbcType=JdbcType.DATE),
        @Result(column="count_hour", property="countHour", jdbcType=JdbcType.VARCHAR),
        @Result(column="enter_num", property="enterNum", jdbcType=JdbcType.BIGINT),
        @Result(column="exit_num", property="exitNum", jdbcType=JdbcType.BIGINT),
        @Result(column="pass", property="pass", jdbcType=JdbcType.BIGINT),
        @Result(column="hold_value", property="holdValue", jdbcType=JdbcType.BIGINT),
        @Result(column="all_enter", property="allEnter", jdbcType=JdbcType.BIGINT),
        @Result(column="all_exit", property="allExit", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    HikGroupRealtimeDataHour selectNewestByGroupId(@Param("groupId") String groupId, @Param("now") Date now);

    @Select({
        "<script>",
        "select center_id,venue_id,equipment_id,group_date,",
        "ifnull(sum(enter_count),0) as all_enter,ifnull(sum(exit_count),0) as all_exit",
        "from hik_group_realtime_data_hour",
        "<where>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"startDate != null\">and group_date &gt;= #{startDate,jdbcType=DATE}</if>",
        "<if test=\"endDate != null\">and group_date &lt;= #{endDate,jdbcType=DATE}</if>",
        "</where>",
        "group by center_id,venue_id,equipment_id,group_date",
        "</script>"
    })
    @Results({
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="equipment_id", property="equipmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="group_date", property="groupDate", jdbcType=JdbcType.DATE),
        @Result(column="all_enter", property="allEnter", jdbcType=JdbcType.BIGINT),
        @Result(column="all_exit", property="allExit", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> selectHikGroupRealtimeDataWeekAndMonth(ParamMap param);

    @Select({
            "<script>",
            "select group_date ,center_id, sum(enter_count) enter_count",
            "from",
            "(select",
            "group_date ,center_id, sum(enter_count) enter_count",
            "from hik_group_realtime_data_hour",
            "where",
            "DATE_FORMAT(group_date , '%Y%m' )  = DATE_FORMAT( CURDATE( ) , '%Y%m')",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "group by group_date",
            "union",
            "select data_time group_date,center_id, sum(num) enter_count",
            "from (select data_time ,center_id ,max(today_in_num) num",
            "from dq_customer_flow",
            "where",
            "DATE_FORMAT(data_time , '%Y%m' )  = DATE_FORMAT( CURDATE( ) , '%Y%m')",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "group by data_time,code) a",
            "group by data_time) aa",
            "group by group_date",
            "</script>"
    })
    @Results({
            @Result(column="group_date", property="groupDate", jdbcType= JdbcType.DATE),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="enter_count", property="enterCount", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> queryCurMonthData(ParamMap param);

    @Select({
            "<script>",
            "select group_date, count_hour, sum(enter_count) enter_count",
            "from",
            "(select",
            "group_date, count_hour, SUM(enter_count) enter_count",
            "from hik_group_realtime_data_hour",
            "<where>",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"groupDate != null\">and group_date = #{groupDate,jdbcType=DATE}</if>",
            "</where>",
            "GROUP BY group_date,count_hour",
            "union",
            "select",
            "data_time group_date, count_hour, sum(max_num) enter_count",
            "from dq_customer_flow",
            "<where>",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"groupDate != null\">and data_time = #{groupDate,jdbcType=DATE}</if>",
            "</where>",
            "GROUP BY data_time,count_hour) a",
            "group by group_date,count_hour",
            "</script>"
    })
    @Results({
        @Result(column="group_date", property="groupDate", jdbcType= JdbcType.DATE),
        @Result(column="count_hour", property="countHour", jdbcType=JdbcType.CHAR),
        @Result(column="enter_count", property="enterCount", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> queryTodayData(ParamMap param);

    @Select({
            "<script>",
            "select group_date ,center_id, sum(enter_count) enter_count",
            "from",
            "(select",
            "group_date ,center_id, sum(enter_count) enter_count",
            "from hik_group_realtime_data_hour",
            "where 1 = 1",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"startDate != null\">and group_date &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and group_date &lt;= #{endDate,jdbcType=DATE}</if>",
            "group by group_date",
            "union",
            "select data_time group_date,center_id, sum(num) enter_count",
            "from (select data_time ,center_id ,max(today_in_num) num",
            "from dq_customer_flow",
            "where 1 = 1",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"startDate != null\">and data_time &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and data_time &lt;= #{endDate,jdbcType=DATE}</if>",
            "group by data_time,code) a",
            "group by data_time) aa",
            "group by group_date",
            "</script>"
    })
    @Results({
        @Result(column="group_date", property="groupDate", jdbcType= JdbcType.DATE),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="enter_count", property="enterCount", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> queryDayData(ParamMap param);

    @Select({
            "<script>",
            "select",
            "(select venue_name from venue where venue_id = a.venue_id) venue_name,",
            "a.venue_id, sum(a.enter_count) enter_count ",
            "from hik_group_realtime_data_hour a",
            "where 1=1",
            "<if test=\"centerId != null\">and a.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"startDate != null\">and a.group_date &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and a.group_date &lt;= #{endDate,jdbcType=DATE}</if>",
            "group by a.venue_id",
            "union",
            "select (select venue_name from venue where venue_id = bb.venue_id) venue_name,",
            "bb.venue_id ,sum(num) enter_count",
            "from",
            "(select b.venue_id ,max(today_in_num) num",
            "from dq_customer_flow b",
            "where 1=1",
            "<if test=\"centerId != null\">and b.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"startDate != null\">and b.data_time &gt;= #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and b.data_time &lt;= #{endDate,jdbcType=DATE}</if>",
            "group by b.venue_id ,b.data_time ,b.code) bb having enter_count > 0",
            "</script>"
    })
    @Results({
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_Name", property="venueName", jdbcType=JdbcType.VARCHAR),
        @Result(column="enter_count", property="enterCount", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> queryVenueData(ParamMap param);


    @Select({
            "<script>",
            "select",
            "YEAR(a.group_date) AS year,",
            "IFNULL(SUM(enter_count) ,0) AS total_enter_amount,",
            "IFNULL(SUM(exit_count) ,0)AS total_exit_amount",
            "from hik_group_realtime_data_hour a left join venue_center.customer_flow_equipment e on a.equipment_id = e.equipment_id and a.venue_id = e.venue_id",
            "<where>",
            "<if test=\"centerId != null\">and a.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "</where>",
            "group by YEAR(a.group_date),a.equipment_id",
            "</script>",
    })
    @Results({
            @Result(column = "year", property = "year", jdbcType = JdbcType.VARCHAR),
            @Result(column = "total_enter_amount", property = "totalEnterAmount", jdbcType = JdbcType.BIGINT),
            @Result(column = "total_exit_amount", property = "totalExitAmount", jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> queryCountYearInfo(@Param("centerId") Long centerId,
                                     @Param("venueId") Long venueId);


    @Select({
            "<script>",
            "select",
            "CONCAT( group_date, ' ', LPAD( count_hour, 2, '0' ), ':00:00' ) AS formatted_time,",
            "SUM(enter_count) as enter_amount,",
            "SUM(exit_count) as exit_amount",
            "from hik_group_realtime_data_hour a left join venue_center.customer_flow_equipment e on a.equipment_id = e.equipment_id and a.venue_id = e.venue_id",
            "<where>",
            "DATE_FORMAT( a.group_date, '%Y%m%d' ) = DATE_FORMAT( #{queryDate,jdbcType=VARCHAR} , '%Y%m%d' )",
            "<if test=\"centerId != null\">and a.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "</where>",
            "group by a.count_hour",
            "</script>",
    })
    @Results({
            @Result(column = "formatted_time", property = "formattedTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enter_amount", property = "enterAmount", jdbcType = JdbcType.BIGINT),
            @Result(column = "exit_amount", property = "exitAmount", jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> queryCountHourInfo(@Param("centerId") Long centerId,
                                     @Param("venueId") Long venueId,
                                     @Param("queryDate") String queryDate);

    @Select({
            "<script>",
            "select",
            "IFNULL(SUM(enter_count) ,0) AS total_enter_amount,",
            "IFNULL(SUM(exit_count) ,0)AS total_exit_amount",
            "from hik_group_realtime_data_hour a left join venue_center.customer_flow_equipment e on a.equipment_id = e.equipment_id and a.venue_id = e.venue_id",
            "<where>",
            "<if test=\"centerId != null\">and a.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>",
    })
    @Results({
            @Result(column = "total_enter_amount", property = "allCountEnter", jdbcType = JdbcType.BIGINT),
            @Result(column = "total_exit_amount", property = "allCountExit", jdbcType = JdbcType.BIGINT),
    })
    DataMap queryCountAllInfo(@Param("centerId") Long centerId,
                              @Param("venueId") Long venueId);

    @Select({
            "<script>",
            "select",
            "IFNULL(SUM(enter_count) ,0) AS total_enter_amount,",
            "IFNULL(SUM(exit_count) ,0)AS total_exit_amount",
            "from hik_group_realtime_data_hour a left join venue_center.customer_flow_equipment e on a.equipment_id = e.equipment_id and a.venue_id = e.venue_id",
            "<where>",
            "DATE_FORMAT( a.group_date, '%Y%m%d' ) = DATE_FORMAT( #{queryDate,jdbcType=VARCHAR} , '%Y%m%d' )",
            "<if test=\"centerId != null\">and a.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>",
    })
    @Results({
            @Result(column = "total_enter_amount", property = "allCountEnter", jdbcType = JdbcType.BIGINT),
            @Result(column = "total_exit_amount", property = "allCountExit", jdbcType = JdbcType.BIGINT),
    })
    DataMap queryCountDayInfo(@Param("centerId") Long centerId,
                              @Param("venueId") Long venueId,
                              @Param("queryDate") String queryDate);

    @Select({
            "<script>",
            "select",
            "id, group_id, region_id, center_id, venue_id, equipment_id, group_time, group_date, count_hour, enter_num, exit_num," ,
            "       enter_count, exit_count, pass, hold_value, all_enter, all_exit, create_time, update_time",
            "from hik_group_realtime_data_hour",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"groupId != null\">and group_id = #{groupId,jdbcType=VARCHAR}</if>",
            "<if test=\"regionId != null\">and region_id = #{regionId,jdbcType=VARCHAR}</if>",
            "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"equipmentId != null\">and equipment_id = #{equipmentId,jdbcType=BIGINT}</if>",
            "<if test=\"groupTime != null\">and group_time = #{groupTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"groupDate != null\">and group_date = #{groupDate,jdbcType=DATE}</if>",
            "<if test=\"countHour != null\">and count_hour = #{countHour,jdbcType=CHAR}</if>",
            "<if test=\"enterNum != null\">and enter_num = #{enterNum,jdbcType=BIGINT}</if>",
            "<if test=\"exitNum != null\">and exit_num = #{exitNum,jdbcType=BIGINT}</if>",
            "<if test=\"pass != null\">and pass = #{pass,jdbcType=BIGINT}</if>",
            "<if test=\"holdValue != null\">and hold_value = #{holdValue,jdbcType=BIGINT}</if>",
            "<if test=\"allEnter != null\">and all_enter = #{allEnter,jdbcType=BIGINT}</if>",
            "<if test=\"allExit != null\">and all_exit = #{allExit,jdbcType=BIGINT}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="group_id", property="groupId", jdbcType=JdbcType.VARCHAR),
            @Result(column="region_id", property="regionId", jdbcType=JdbcType.VARCHAR),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
            @Result(column="equipment_id", property="equipmentId", jdbcType=JdbcType.BIGINT),
            @Result(column="group_time", property="groupTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="group_date", property="groupDate", jdbcType=JdbcType.DATE),
            @Result(column="count_hour", property="countHour", jdbcType=JdbcType.CHAR),
            @Result(column="enter_num", property="enterNum", jdbcType=JdbcType.BIGINT),
            @Result(column="exit_num", property="exitNum", jdbcType=JdbcType.BIGINT),
            @Result(column="pass", property="pass", jdbcType=JdbcType.BIGINT),
            @Result(column="hold_value", property="holdValue", jdbcType=JdbcType.BIGINT),
            @Result(column="all_enter", property="allEnter", jdbcType=JdbcType.BIGINT),
            @Result(column="all_exit", property="allExit", jdbcType=JdbcType.BIGINT),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<HikGroupRealtimeDataHour> selectVenueFlowInfo(HikGroupRealtimeDataHour param);

}
