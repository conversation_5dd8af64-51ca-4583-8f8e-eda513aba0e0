package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class TrainScheduleDetail extends TrainScheduleDetailKey {
    private Long itemId;

    private String itemName;

    private String detailItemName;

    private Long itemType;

    private Long attrId1;

    private Long attrValueId1;

    private String attrValueName1;

    private Long attrId2;

    private Long attrValueId2;

    private String attrValueName2;

    private Long attrId3;

    private Long attrValueId3;

    private String attrValueName3;

    private Integer showOrder;

    private Date updateTime;

    private Long attrId4;

    private Long attrValueId4;

    private String attrValueName4;

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName == null ? null : itemName.trim();
    }

    public String getDetailItemName() {
        return detailItemName;
    }

    public void setDetailItemName(String detailItemName) {
        this.detailItemName = detailItemName == null ? null : detailItemName.trim();
    }

    public Long getItemType() {
        return itemType;
    }

    public void setItemType(Long itemType) {
        this.itemType = itemType;
    }

    public Long getAttrId1() {
        return attrId1;
    }

    public void setAttrId1(Long attrId1) {
        this.attrId1 = attrId1;
    }

    public Long getAttrValueId1() {
        return attrValueId1;
    }

    public void setAttrValueId1(Long attrValueId1) {
        this.attrValueId1 = attrValueId1;
    }

    public String getAttrValueName1() {
        return attrValueName1;
    }

    public void setAttrValueName1(String attrValueName1) {
        this.attrValueName1 = attrValueName1 == null ? null : attrValueName1.trim();
    }

    public Long getAttrId2() {
        return attrId2;
    }

    public void setAttrId2(Long attrId2) {
        this.attrId2 = attrId2;
    }

    public Long getAttrValueId2() {
        return attrValueId2;
    }

    public void setAttrValueId2(Long attrValueId2) {
        this.attrValueId2 = attrValueId2;
    }

    public String getAttrValueName2() {
        return attrValueName2;
    }

    public void setAttrValueName2(String attrValueName2) {
        this.attrValueName2 = attrValueName2 == null ? null : attrValueName2.trim();
    }

    public Long getAttrId3() {
        return attrId3;
    }

    public void setAttrId3(Long attrId3) {
        this.attrId3 = attrId3;
    }

    public Long getAttrValueId3() {
        return attrValueId3;
    }

    public void setAttrValueId3(Long attrValueId3) {
        this.attrValueId3 = attrValueId3;
    }

    public String getAttrValueName3() {
        return attrValueName3;
    }

    public void setAttrValueName3(String attrValueName3) {
        this.attrValueName3 = attrValueName3 == null ? null : attrValueName3.trim();
    }

    public Integer getShowOrder() {
        return showOrder;
    }

    public void setShowOrder(Integer showOrder) {
        this.showOrder = showOrder;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getAttrId4() {
        return attrId4;
    }

    public void setAttrId4(Long attrId4) {
        this.attrId4 = attrId4;
    }

    public Long getAttrValueId4() {
        return attrValueId4;
    }

    public void setAttrValueId4(Long attrValueId4) {
        this.attrValueId4 = attrValueId4;
    }

    public String getAttrValueName4() {
        return attrValueName4;
    }

    public void setAttrValueName4(String attrValueName4) {
        this.attrValueName4 = attrValueName4 == null ? null : attrValueName4.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", itemId=").append(itemId);
        sb.append(", itemName=").append(itemName);
        sb.append(", detailItemName=").append(detailItemName);
        sb.append(", itemType=").append(itemType);
        sb.append(", attrId1=").append(attrId1);
        sb.append(", attrValueId1=").append(attrValueId1);
        sb.append(", attrValueName1=").append(attrValueName1);
        sb.append(", attrId2=").append(attrId2);
        sb.append(", attrValueId2=").append(attrValueId2);
        sb.append(", attrValueName2=").append(attrValueName2);
        sb.append(", attrId3=").append(attrId3);
        sb.append(", attrValueId3=").append(attrValueId3);
        sb.append(", attrValueName3=").append(attrValueName3);
        sb.append(", showOrder=").append(showOrder);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", attrId4=").append(attrId4);
        sb.append(", attrValueId4=").append(attrValueId4);
        sb.append(", attrValueName4=").append(attrValueName4);
        sb.append("]");
        return sb.toString();
    }
}