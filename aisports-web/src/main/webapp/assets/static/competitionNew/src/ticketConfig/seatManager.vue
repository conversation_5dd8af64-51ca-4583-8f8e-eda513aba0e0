<template>
  <div class="team-ticket-container">
    <section class="header-area">
      <div class="panel-heading">
        <span>落座管理</span>
        <span class="btn-return" title="后退" @click="goBack">
          <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
        </span>
      </div>
    </section>

    <section class="search-area">
      <el-select v-model="performId" placeholder="场次" clearable filterable>
        <el-option v-for="perform in performList" :key="perform.id" :label="perform.name" :value="perform.id">
        </el-option>
      </el-select>
      <el-select v-model="stockId" placeholder="票区" clearable filterable>
        <el-option v-for="stock in stockList" :key="stock.id" :label="stock.name" :value="stock.id"> </el-option>
      </el-select>

      <el-select v-model="state" placeholder="分配状态" clearable filterable>
        <el-option v-for="stock in importStateList" :key="stock.id" :label="stock.name" :value="stock.id"> </el-option>
      </el-select>


      <el-date-picker class="date-long" v-model="salesDateRange" format="yyyy-MM-dd" type="daterange"
        placeholder="选择日期时间"></el-date-picker>
      <el-button icon="el-icon-search" type="primary" @click="selectTableData">查询</el-button>
    </section>

    <section class="action-area">
      <el-button icon="el-icon-plus" @click="createdTeamTicket">新增落座</el-button>
    </section>

    <section class="table-area">
      <el-table :data="teamTicketSendRecordList" header-cell-class-name="camp-table-header" style="width: 100%">

        <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column align="center" prop="stockName" label="票区"></el-table-column>
        <el-table-column align="center" prop="totalNum" label="总座位"></el-table-column>
        <el-table-column align="center" prop="soldNum" label="已占座位"></el-table-column>
        <el-table-column align="center" label="剩余座位">
          <template slot-scope="scope">
            {{ scope.row.totalNum - scope.row.soldNum }}
          </template>
        </el-table-column>


        <el-table-column align="center" prop="validNum" label="可落座位数"></el-table-column>
        <el-table-column align="center" prop="finishNum" label="落座完成"></el-table-column>
        <el-table-column align="center" prop="createTime" label="执行时间"></el-table-column>
        <el-table-column align="center" label="分配状态">
          <template slot-scope="scope">
            <span v-if="scope.row.state == 0">分配失败</span>
            <span v-if="scope.row.state == 1">分配中</span>
            <span v-if="scope.row.state == 2">分配完成</span>
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button @click="handleview(scope.$index, scope.row)" type="text" size="small">查看</el-button>
          </template>
        </el-table-column> -->

      </el-table>
      <div class="page_footer">
        <el-pagination layout="prev, pager, next" background :total="teamPageInfo.total"
          @current-change="handleTeamPageChange"> </el-pagination>
      </div>
    </section>

    <section class="pagination-area">
      <el-drawer title="新增落座" :visible.sync="drawer" size="50%" :before-close="handleClose">
        <!-- 比赛场次选择 -->
        <el-form label-width="100px" :rules="rules" :model="form">
          <el-form-item label="比赛场次:" prop="performId">
            <el-select v-model="form.performId" @change="changeMatchId" placeholder="请选择场次">
              <el-option v-for="perform in performList" :key="perform.id" :label="perform.name" :value="perform.id">
              </el-option>
            </el-select>
          </el-form-item>
          <div style="padding: 0 30px;">
            <el-table :data="seatList" header-cell-class-name="camp-table-header" style="width: 100%" height="700"
              @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" :selectable="selectableRow"></el-table-column>
              <el-table-column align="center" prop="name" label="票区"></el-table-column>
              <el-table-column align="center" prop="totalNum" label="总座位"></el-table-column>
              <el-table-column align="center" prop="soldNum" label="已占座位"></el-table-column>
              <el-table-column align="center" prop="teamName" label="剩余座位">
                <template slot-scope="scope">
                  {{ scope.row.totalNum - scope.row.soldNum }}
                </template>
              </el-table-column>
              <el-table-column align="center" prop="validNum" label="可落座位数"></el-table-column>
            </el-table>
          </div>

          <el-form-item>
            <div class="foot-seat-btn">
              <el-button>取消</el-button>
              <el-button type="primary" @click="submitForm">确定匹配座位号</el-button>
            </div>
          </el-form-item>
        </el-form>
      </el-drawer>
    </section>
  </div>
</template>

<script>
import * as service from "./service";
import { showSuccess, showWrong } from "mAlert";
import moment from "moment";

export default {
  name: "TeamTicket",
  data() {
    return {
      // 数据属性
      projectId: "",
      performId: "",
      stockId: "",
      stockList: [],
      keyword: "",
      salesDateRange: [],
      performList: [],
      startDate: "",
      endDate: "",
      state: '',
      teamPageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      seatList: [],
      teamTicketSendRecordList: [],
      teamTicketList: [],
      teamTicketSelected: {
        remainAmount: 100,
        price: 0,
      },
      drawer: false,
      form: {
        sendNum: 1,
        performId: "",
        performTicketId: "",
      },
      importStateList: [
        { id: '0', name: '分配失败' },
        { id: '1', name: '分配中' },
        { id: '2', name: '分配成功' },
      ],
      stockIds: [],
      rightsList2: [],
      rules: {
        performId: [
          { required: true, message: "请选择场次", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    m2front,
    showSuccess,
    showWrong,
    // 方法定义
    goBack() {
      this.$router.go(-1);
    },
    handleSelectionChange(selection) {

      this.stockIds = selection.map(item => item.id);

    },
    selectableRow(row, index) {
      console.log(row, index)
      return row.state != 1
    },

    handleview(index, row) {
      console.log(index, row, "查看");
      this.$router.push({
        path: "/teamTicketDetail",
        query: {
          projectId: this.projectId,
          recordId: row.id,
        },
      });
    },
    changeMatchId(id) {
      this.getCandidateSeatList(id);
    },
    getCandidateSeatList(id) {
      service.getCandidateSeatList({ performId: id }, (res) => {
        this.seatList = res.seatList
      });
    },

    submitForm() {

      if (this.stockIds.length == 0) {
        this.showWrong('请选择分配落座的区域');
        return false
      }
      let params = {
        performId: this.form.performId,
        stockIds: this.stockIds.join(","),
      };

      service.takeSeat(params, (res) => {
        if (res.error == 0) {
          this.drawer = false;
          this.showSuccess("提交分配成功");
          this.teamPageInfo.pageNum = 1;
          this.getTeamTicketSendRecordList()
        } else {
          this.showWrong(res.message);
        }
      });
    },
    initFormData() {
      this.form = {
        sendNum: 1,
        performId: "",
        performTicketId: "",
      };
    },
    createdTeamTicket() {
      if (this.performList.length > 0) {
        debugger
        this.form.performId = this.performList[0].id;
        this.getCandidateSeatList(this.performList[0].id)
      }
      this.drawer = true;
    },
    getTakeLogPageList() {
      service.getTakeLogPageList(
        {
          performId: this.form.performId,
          stockId: "",
          ticketName: "",
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        },
        (res) => {
          this.teamTicketList = res.pageInfo.list.map((ticket) => {
            return ticket;
          });
        }
      );
    },
    handleClose(done) {
      this.drawer = false;
    },
    cancelForm() {
      this.drawer = false;
    },
    getStockList() {
      service.getStockList(this.performId, (res) => {
        // this.performTickets = res.performTickets
        this.stockList = res.stockList;
        this.filterStocklist = this.stockList.map((item) => {
          return {
            ...item,
          };
        });
      });
    },
    getPerformList() {
      service.getPerformList(this.projectId, (res) => {
        this.performList = res.performList.map((item) => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      });
    },
    handleTeamPageChange(pageNum) {
      this.teamPageInfo.pageNum = pageNum;
      this.getTeamTicketSendRecordList();
    },

    selectTableData() {

      if (this.salesDateRange && this.salesDateRange.length == 2) {
        this.startDate = moment(this.salesDateRange[0]).format("YYYY-MM-DD");
        this.endDate = moment(this.salesDateRange[1]).format("YYYY-MM-DD");
      } else {
        this.startDate = '';
        this.endDate = ''

      }
      this.getTeamTicketSendRecordList();
    },
    getTeamTicketSendRecordList() {

      let params = {
        performId: this.performId,
        projectId: this.projectId,
        stockId: this.stockId,
        startDate: this.startDate,
        state: this.state,
        endDate: this.endDate,
        pageNum: this.teamPageInfo.pageNum,
        pageSize: 10,
      };
      service.getTakeLogPageList(params, (res) => {
        this.teamTicketSendRecordList = res.pageInfo.list;
        this.teamPageInfo.total = res.pageInfo.total;
      });
    },
  },
  mounted() {
    this.projectId = this.$route.query.projectId;
    this.performId = this.$route.query.performId;
    this.getStockList();
    this.getPerformList();
    this.getTeamTicketSendRecordList();
    // this.getRightsList();
    // 生命周期钩子
  },
};
</script>

<style lang="scss" scoped>
.team-ticket-container {
  .drawer-content {
    padding: 20px;
    width: 500px;
  }

  .box-card {
    margin-bottom: 20px;
  }

  .text.item p {
    margin: 5px 0;
  }

  .warning-message {
    color: #f56c6c;
    margin-top: 20px;
  }

  .panel-heading {
    background-color: #fafafa;
    height: 60px;
    font-size: 20px;
  }

  .search-area {
    padding: 20px;
    display: flex;
    gap: 20px;
  }

  .action-area {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
  }

  .table-area {
    padding: 20px;
  }

  .ticket-area-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); // 两列等宽
    gap: 10px;
    width: 472px;
    height: 165px;
    background-color: #f8f8f8;
    padding: 10px;
    font-size: 14px;
    box-sizing: border-box;
    margin-bottom: 20px;
  }

  .ticket-item {
    display: flex;
    padding: 5px;
  }

  .ticket-label {
    color: #666;
  }

  .ticket-value {
    color: #333;
  }

  .text-red {
    color: #fc5f44;
  }

  .text-blue {
    color: #1fa2f5;
  }

  .demo-drawer__footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 40px;
  }

  .el-button {
    border-radius: 8px;
    width: auto;
    height: 36px;
    padding: 0 15px;
  }
}
</style>
