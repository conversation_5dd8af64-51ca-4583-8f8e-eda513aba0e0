package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.CabinetRentFee;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

public interface CabinetRentFeeMapper {
    @Delete({
        "delete from cabinet_rent_fee",
        "where fee_id = #{feeId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long feeId);

    @Insert({
        "insert into cabinet_rent_fee (fee_id, fee_name, ",
        "deposit_fee, price_type, ",
        "rent_unit, rent_tag, delay_unit, ",
        "delay_fee, start_date, ",
        "end_date, venue_id, retail_tag, ",
        "retail_fee, state, overdue_limit, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{feeId,jdbcType=BIGINT}, #{feeName,jdbcType=VARCHAR}, ",
        "#{depositFee,jdbcType=INTEGER}, #{priceType,jdbcType=CHAR}, ",
        "#{rentUnit,jdbcType=CHAR}, #{rentTag,jdbcType=CHAR}, #{delayUnit,jdbcType=CHAR}, ",
        "#{delayFee,jdbcType=INTEGER}, #{startDate,jdbcType=DATE}, ",
        "#{endDate,jdbcType=DATE}, #{venueId,jdbcType=BIGINT}, #{retailTag,jdbcType=CHAR}, ",
        "#{retailFee,jdbcType=INTEGER}, #{state,jdbcType=CHAR}, #{overdueLimit,jdbcType=CHAR}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(CabinetRentFee record);

    @Select({
        "select",
        "fee_id, fee_name, deposit_fee, price_type, rent_unit, rent_tag, delay_unit, ",
        "delay_fee, start_date, end_date, venue_id, retail_tag, retail_fee, state, overdue_limit, ",
        "create_time, create_staff_id, update_time, update_staff_id",
        "from cabinet_rent_fee",
        "where fee_id = #{feeId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="fee_id", property="feeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="fee_name", property="feeName", jdbcType=JdbcType.VARCHAR),
        @Result(column="deposit_fee", property="depositFee", jdbcType=JdbcType.INTEGER),
        @Result(column="price_type", property="priceType", jdbcType=JdbcType.CHAR),
        @Result(column="rent_unit", property="rentUnit", jdbcType=JdbcType.CHAR),
        @Result(column="rent_tag", property="rentTag", jdbcType=JdbcType.CHAR),
        @Result(column="delay_unit", property="delayUnit", jdbcType=JdbcType.CHAR),
        @Result(column="delay_fee", property="delayFee", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.DATE),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="retail_tag", property="retailTag", jdbcType=JdbcType.CHAR),
        @Result(column="retail_fee", property="retailFee", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="overdue_limit", property="overdueLimit", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    CabinetRentFee selectByPrimaryKey(Long feeId);

    @Update({
        "update cabinet_rent_fee",
        "set fee_name = #{feeName,jdbcType=VARCHAR},",
          "deposit_fee = #{depositFee,jdbcType=INTEGER},",
          "price_type = #{priceType,jdbcType=CHAR},",
          "rent_unit = #{rentUnit,jdbcType=CHAR},",
          "rent_tag = #{rentTag,jdbcType=CHAR},",
          "delay_unit = #{delayUnit,jdbcType=CHAR},",
          "delay_fee = #{delayFee,jdbcType=INTEGER},",
          "start_date = #{startDate,jdbcType=DATE},",
          "end_date = #{endDate,jdbcType=DATE},",
          "venue_id = #{venueId,jdbcType=BIGINT},",
          "retail_tag = #{retailTag,jdbcType=CHAR},",
          "retail_fee = #{retailFee,jdbcType=INTEGER},",
          "state = #{state,jdbcType=CHAR},",
          "overdue_limit = #{overdueLimit,jdbcType=CHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where fee_id = #{feeId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(CabinetRentFee record);

    @Select({
        "<script>",
        "select",
        "fee_id, fee_name, deposit_fee, price_type, rent_unit, rent_tag, delay_unit, delay_fee, start_date, end_date, venue_id, retail_tag, retail_fee, state, overdue_limit, create_time, create_staff_id, update_time, update_staff_id",
        "from cabinet_rent_fee",
        "<where>",
        "<if test=\"feeId != null\">and fee_id = #{feeId,jdbcType=BIGINT}</if>",
        "<if test=\"feeName != null\">and fee_name = #{feeName,jdbcType=VARCHAR}</if>",
        "<if test=\"depositFee != null\">and deposit_fee = #{depositFee,jdbcType=INTEGER}</if>",
        "<if test=\"priceType != null\">and price_type = #{priceType,jdbcType=CHAR}</if>",
        "<if test=\"rentUnit != null\">and rent_unit = #{rentUnit,jdbcType=CHAR}</if>",
        "<if test=\"rentTag != null\">and rent_tag = #{rentTag,jdbcType=CHAR}</if>",
        "<if test=\"delayUnit != null\">and delay_unit = #{delayUnit,jdbcType=CHAR}</if>",
        "<if test=\"delayFee != null\">and delay_fee = #{delayFee,jdbcType=INTEGER}</if>",
        "<if test=\"startDate != null\">and start_date = #{startDate,jdbcType=DATE}</if>",
        "<if test=\"endDate != null\">and end_date = #{endDate,jdbcType=DATE}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"retailTag != null\">and retail_tag = #{retailTag,jdbcType=CHAR}</if>",
        "<if test=\"retailFee != null\">and retail_fee = #{retailFee,jdbcType=INTEGER}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"overdueLimit != null\">and overdue_limit = #{overdueLimit,jdbcType=CHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="fee_id", property="feeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="fee_name", property="feeName", jdbcType=JdbcType.VARCHAR),
        @Result(column="deposit_fee", property="depositFee", jdbcType=JdbcType.INTEGER),
        @Result(column="price_type", property="priceType", jdbcType=JdbcType.CHAR),
        @Result(column="rent_unit", property="rentUnit", jdbcType=JdbcType.CHAR),
        @Result(column="rent_tag", property="rentTag", jdbcType=JdbcType.CHAR),
        @Result(column="delay_unit", property="delayUnit", jdbcType=JdbcType.CHAR),
        @Result(column="delay_fee", property="delayFee", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.DATE),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="retail_tag", property="retailTag", jdbcType=JdbcType.CHAR),
        @Result(column="retail_fee", property="retailFee", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="overdue_limit", property="overdueLimit", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<CabinetRentFee> selectByFields(CabinetRentFee param);

    @Update({
        "<script>",
        "update cabinet_rent_fee",
        "<set>",
        "<if test=\"feeName != null\">fee_name = #{feeName,jdbcType=VARCHAR},</if>",
        "<if test=\"depositFee != null\">deposit_fee = #{depositFee,jdbcType=INTEGER},</if>",
        "<if test=\"priceType != null\">price_type = #{priceType,jdbcType=CHAR},</if>",
        "<if test=\"rentUnit != null\">rent_unit = #{rentUnit,jdbcType=CHAR},</if>",
        "<if test=\"rentTag != null\">rent_tag = #{rentTag,jdbcType=CHAR},</if>",
        "<if test=\"delayUnit != null\">delay_unit = #{delayUnit,jdbcType=CHAR},</if>",
        "<if test=\"delayFee != null\">delay_fee = #{delayFee,jdbcType=INTEGER},</if>",
        "<if test=\"startDate != null\">start_date = #{startDate,jdbcType=DATE},</if>",
        "<if test=\"endDate != null\">end_date = #{endDate,jdbcType=DATE},</if>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "<if test=\"retailTag != null\">retail_tag = #{retailTag,jdbcType=CHAR},</if>",
        "<if test=\"retailFee != null\">retail_fee = #{retailFee,jdbcType=INTEGER},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"overdueLimit != null\">overdue_limit = #{overdueLimit,jdbcType=CHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where fee_id = #{feeId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(CabinetRentFee record);

    @Select({
        "<script>",
        "select",
        "fee_id, fee_name, deposit_fee, price_type, rent_unit, rent_tag, delay_unit, delay_fee, start_date, end_date, venue_id, retail_tag, retail_fee, state, overdue_limit, create_time, create_staff_id",
        "from cabinet_rent_fee",
        "<where>",
        "<if test=\"feeName != null\">and fee_name like concat('%', #{feeName,jdbcType=VARCHAR}, '%')</if>",
        "and venue_id = #{venueId,jdbcType=BIGINT}",
        "and state = '1'",
        "</where>",
        "order by create_time desc",
        "</script>"
    })
    @Results({
        @Result(column="fee_id", property="feeId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="fee_name", property="feeName", jdbcType=JdbcType.VARCHAR),
        @Result(column="deposit_fee", property="depositFee", jdbcType=JdbcType.INTEGER),
        @Result(column="price_type", property="priceType", jdbcType=JdbcType.CHAR),
        @Result(column="rent_unit", property="rentUnit", jdbcType=JdbcType.CHAR),
        @Result(column="rent_tag", property="rentTag", jdbcType=JdbcType.CHAR),
        @Result(column="delay_unit", property="delayUnit", jdbcType=JdbcType.CHAR),
        @Result(column="delay_fee", property="delayFee", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.DATE),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="retail_tag", property="retailTag", jdbcType=JdbcType.CHAR),
        @Result(column="retail_fee", property="retailFee", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="overdue_limit", property="overdueLimit", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<Map<String, Object>> selectFeeList(Map<String, Object> param);
}