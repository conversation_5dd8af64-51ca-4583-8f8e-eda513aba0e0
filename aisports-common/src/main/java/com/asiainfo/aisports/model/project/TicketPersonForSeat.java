package com.asiainfo.aisports.model.project;

/**
 * <AUTHOR>
 * @date 2025/7/3 20:47
 */
public class TicketPersonForSeat {

    private Long id;

    private Long ticketId;

    private Long tradeId;

    private String type;

    private Long tradeIdB;

    private Integer ticketNum;

    private Long seatId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTicketId() {
        return ticketId;
    }

    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId;
    }

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getTradeIdB() {
        return tradeIdB;
    }

    public void setTradeIdB(Long tradeIdB) {
        this.tradeIdB = tradeIdB;
    }

    public Integer getTicketNum() {
        return ticketNum;
    }

    public void setTicketNum(Integer ticketNum) {
        this.ticketNum = ticketNum;
    }

    public Long getSeatId() {
        return seatId;
    }

    public void setSeatId(Long seatId) {
        this.seatId = seatId;
    }
}
