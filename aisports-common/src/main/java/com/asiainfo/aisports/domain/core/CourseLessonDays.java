package com.asiainfo.aisports.domain.core;

public class CourseLessonDays {
    private Long id;

    private Long lessonId;

    private String weekDay;

    private String lessonStart;

    private String lessonEnd;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public String getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(String weekDay) {
        this.weekDay = weekDay == null ? null : weekDay.trim();
    }

    public String getLessonStart() {
        return lessonStart;
    }

    public void setLessonStart(String lessonStart) {
        this.lessonStart = lessonStart == null ? null : lessonStart.trim();
    }

    public String getLessonEnd() {
        return lessonEnd;
    }

    public void setLessonEnd(String lessonEnd) {
        this.lessonEnd = lessonEnd == null ? null : lessonEnd.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", lessonId=").append(lessonId);
        sb.append(", weekDay=").append(weekDay);
        sb.append(", lessonStart=").append(lessonStart);
        sb.append(", lessonEnd=").append(lessonEnd);
        sb.append("]");
        return sb.toString();
    }
}