;define(function(require){
    var $=require('jquery'),
        keyUtil = require('keyUtil');
    require('./ui');
    require('select');
    var settings=require('aisportsConfig');
    var slimScroll=require('slimScroll');   //滚动条插件
    var spinner = settings.spinner;

    var visitorId='';//全区变量，用于返回data.guestInfo.visitorId
    var guestName='';//全区变量，用于返回data.guestInfo.visitorName
    var getCardId =  require('readCardNative')
    getCardId('#eCardNo', '#searchByCardNo', '.advance-search' )
    $('.text-area').slimscroll({
            height:'245px',
            size:'7px',
            alwaysVisible:true,
            railVisible:true,
            railColor:'#e6dec5',
            distance:'5px',
            color: '#cccccc',
            opacity:1
    });

    function makeSelects() {
        $('#serviceType')
            .filter('.dropped')
            .removeClass('dropped')
            .easyDropDown('destroy');
        $('#serviceType').addClass('dropped').easyDropDown();
    }

    window.handleBraceletNum = function(keyId) {
        enterKey(keyId);
    };

    function enterKey(keyId) {
        if ($('#key').is(':hidden')) {
            return;
        }
        if ($('#eCardNo').val() == "") {
            return;
        }

        // 刷入钥匙
        keyUtil.getInKey(keyId, function(data) {
            if (data.error !== 0) {
                settings.spinner.showErrorResult(data.message);
            } else {
                settings.spinner.showSuccessResult(data.message);
                addTicketKey(keyId);
            }
        });
    }

    function addTicketKey(keyId) {
        //验证是否刷入票
        keyUtil.getInKey(keyId, function(data) {
            if (data.error !== 0) {
                settings.spinner.showErrorResult(data.message);
            } else {
                settings.spinner.showSuccessResult(data.message);
                $('#ticket-key-item')
                    .addClass('key-added')
                    .attr('keyId', data.keyId)
                    .attr('keyNo', data.keyNo)
                    .attr('title', data.keyNo);
            }
        });
    }

    //输入卡号的确定按钮   
    function searchByCardNo(){
        $(document).on('click','#searchByCardNo',handleSearch);
    }

    function handleSearch(cardNo){
        var cardNo = typeof cardNo === 'string' ? cardNo : $('#eCardNo').val();  //获取到了input框中的卡号
        if(cardNo.length<=0){
            spinner.showErrorResult('请输入身份证号码！');
        }
        else{
            var _data={psptId : cardNo};
            $.ajax({
                url:'/guest/SelectCardInfo',
                data: _data,
                success:function(data){
                    if (data.error != 0) {
                        spinner.showErrorResult(data.message);
                        appendErrorCardInfo();
                    }
                    else {
                        appendCardInfo(data);
                    }
                }
            })
        }
    }

    window.SEARCHBYID = function(cardNo){
        handleSearch(cardNo);
    };

    function showKey() {
        $.ajax({
            type: 'POST',
            url: '/guest/checkKey',
            data: {},
            success: function(data) {
                if (data.list) {
                    if (data.list.length > 0) {
                        $('#serviceChoose').show();
                        var serviceType = $('#serviceType');
                        serviceType.find('option').remove();
                        $.each(data.list, function (k, v) {
                            var opt = $('<option>').text(v.serviceName).val(v.serviceId).attr('isKey', v.isKey);
                            opt.appendTo(serviceType);
                        });
                        var value = $('#serviceType').find('option:selected').attr('isKey');
                        if (value == 1) {
                            $('#key').show();
                        } else {
                            $('#key').hide();
                        }
                        makeSelects();
                    }
                    else{
                        $('#serviceChoose').hide();
                        $('#key').hide();
                    }
                }
            }
        });
    }

    window.checkService = function() {
        var value = $('#serviceType').find('option:selected').attr('isKey');
        if (value == 1) {
            $('#key').show();
        } else {
            $('#key').hide();
        }
        makeSelects();
    }

    /*添加身份信息*/
    function appendErrorCardInfo(){
        var _visitorName='',
            _phoneNo='',
            _psptId='',
            _entryNum='';
        var visitorInfoHtml='<table class="table table-striped table-th-border"> <th>姓名</th><th>场馆</th><th>入馆时间</th>';

        $('.visitor-name').text(_visitorName);
        $('.phoneNo').text(_phoneNo);
        $('.psptId').text(_psptId);
        $('.entry-num').text(_entryNum);
        $('#visitorInfo').html(visitorInfoHtml);
    }

    /*添加身份信息*/
    function appendCardInfo(data){
        var _guestInfo,
            _visitorName,
            _phoneNo,
            _psptId,
            _entryNum,
            _visitorInfo;

        //获取身份信息
        if(data.guestInfo){
            _guestInfo = data.guestInfo;
            _visitorName = _guestInfo.visitorName;
            _phoneNo = _guestInfo.phoneNumber;
            _psptId = _guestInfo.psptId;
            visitorId = _guestInfo.visitorId;
            guestName = _guestInfo.visitorName;
        }
        else
        {
            _guestInfo = '';
            _visitorName = '';
            _phoneNo = '';
            _psptId = '';
            visitorId = '';
            guestName = '';
        }

        if(data.entryNumber){
            _entryNum=data.entryNumber;
        }
        else
        {
            _entryNum=0;
        }

        if(data.visitorInfo){
            var visitorInfoHtml='<table class="table table-striped table-th-border"> <th>姓名</th><th>场馆</th><th>入馆时间</th>';
            $.each(data.visitorInfo,function(index,ele) {
                visitorInfoHtml += '<tr>' +
                '<td>' + ele.visitorName + '</td>' +
                '<td>' + ele.venueName + '</td>' +
                '<td>' + ele.enterInTime + '</td>' +
                '</tr>'
            });
            visitorInfoHtml+='</table>';
            //console.log(visitorInfoHtml);
            $('#visitorInfo').html(visitorInfoHtml);
        }

        $('.visitor-name').text(_visitorName);
        $('.phoneNo').text(_phoneNo);
        $('.psptId').text(_psptId);
        $('.entry-num').text(_entryNum);

        showKey();
    }

    //确认入馆按钮
    function confirmEnter(){
        $(document).on('click','#confirmEnter',function(){
            //alert('确认入馆');
            var _visitorId = visitorId;//访客id
            var _visitorName = guestName;//访客姓名
            var _keyId = $('#ticket-key-item').attr('keyId');//钥匙扣编码

            if ($('#key').is(':hidden')) {
                _keyId = "";
            }
            else if( !_keyId){
                settings.spinner.showErrorResult('请先绑定钥匙扣！');
                return false;
            }
            var _data = {visitorId:_visitorId,
                visitorName:_visitorName,
                keyId:_keyId};
            console.log(_data);
            spinner.showWaitCover();
            $.ajax({
                method: 'post',
                url:'/guest/CommitEntryInfoRecord',
                data: _data,
                success:function(data){
                    if (data.error != 0) {
                        spinner.showErrorResult(data.message);
                    }
                    else
                    {
                        spinner.showSuccessResult('操作成功');
                        window.setTimeout(function () {
                            spinner.showWaitCover();
                            window.location.href = settings.urlPrefix + data.redirect;
                        }, 2000);
                    }
                }
            });
        })
    }


    //钥匙扣图形的点击
    /**function keyClick(){
        $(document).on('click', '.my-key', function(){
            var $this=$(this);
            $this
                .addClass('key-select')
              .parent()
              .find('.keyNo-update')
                .show()
              .find('input[type="text"]')
                .focus();
        })
    }**/

    //钥匙绑定确定
    /**function cardKeyConfirm(){
        function handleTicketKeyConfirm() {
            var $this = $('#ticket-key'),
                key_no = $this.parent().find('input').val(),
                _data;

            if (key_no === '') {
                settings.spinner.showErrorResult('请先输入钥匙号！');
                return;
            }

            _data = {
                keyId: $('#key-input').val()
            };

            $.ajax({
                url: '/enterHall/judgeIfKeyValid',
                data: _data,
                success: function(data) {
                    $this.parent().hide();
                    $('#key-input').val('');
                    if (data.error != 0) {
                        //失败则不绑定
                        $item.removeClass('key-select');
                        settings.spinner.showErrorResult(data.message);
                    } else {
                        $item.attr('keyNo', key_no);
                        settings.spinner.showSuccessResult(data.message);
                    }
                }
            });
        }

        $(document).on('click', '#ticket-key', handleTicketKeyConfirm);
        $(document).on('keydown', '#key-input', function(e) {
            // enter输入框
            if (e.keyCode === 13) {
                handleTicketKeyConfirm.call(this);
            }
        });
    }**/

    $(function(){
        searchByCardNo();
        confirmEnter();
        //keyClick();
        //cardKeyConfirm();
        $('#serviceChoose').hide();
    });
    
});