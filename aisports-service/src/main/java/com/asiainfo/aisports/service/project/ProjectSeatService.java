package com.asiainfo.aisports.service.project;

import com.asiainfo.aisports.annotation.RoutingKey;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.cache.RedisKeyEnum;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.ProjectConstants;
import com.asiainfo.aisports.domain.core.PerformStock;
import com.asiainfo.aisports.domain.core.StockSeat;
import com.asiainfo.aisports.domain.core.StockSeatImp;
import com.asiainfo.aisports.domain.core.TakeSeatLog;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.model.project.SeatImpDto;
import com.asiainfo.aisports.model.project.SeatImpResult;
import com.asiainfo.aisports.model.project.TicketPersonForSeat;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.ProjectManageService;
import com.asiainfo.aisports.service.RedisCommonService;
import com.asiainfo.aisports.service.SequenceWrapper;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.job.JobEnum;
import com.asiainfo.aisports.service.mq.JobMessageService;
import com.asiainfo.aisports.tools.BeanUtils;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.MapUtil;
import com.asiainfo.aisports.utils.RedisKeyGenerator;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/3 14:18
 */
@Service
public class ProjectSeatService {


    private static final Logger log = LoggerFactory.getLogger(ProjectSeatService.class);


    private static Pattern NUM_PATTERN = Pattern.compile("([1-9]\\d*)");
    @Autowired
    private StockSeatImpMapper stockSeatImpMapper;


    @Autowired
    private StockSeatMapper stockSeatMapper;

    @Autowired
    private SequenceWrapper sequenceWrapper;

    @Autowired
    private PerformStockMapper performStockMapper;



    @Autowired
    private TakeSeatLogMapper takeSeatLogMapper;

    @Autowired
    private ProjectManageService projectManageService;

    @Autowired
    private RedisCommonService redisCommonService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private JobMessageService jobMessageService;

    @Autowired
    private RedisKeyGenerator redisKeyGenerator;

    @Autowired
    private TradeTicketPersonMapper tradeTicketPersonMapper;


    public List<StockSeatImp> getSeatTemplate(Long stockId) {


        StockSeatImp param = new StockSeatImp();
        param.setStockId(stockId);
        param.setState(Constants.Status.VALID);
        List<StockSeatImp> stockSeatImps = stockSeatImpMapper.selectByFields(param);
        return stockSeatImps;


    }


    @Transactional
    public List<SeatImpResult> importExcel(Long stockId, List<SeatImpDto> rowList, LoginStaff staff) {

        PerformStock performStock = performStockMapper.selectByPrimaryKey(stockId);
        if(Constants.Status.VALID.equals(performStock.getAssignState())) {
            throw new ServiceException("座位已经分配，不可以修改");
        }


        stockSeatImpMapper.invalidateByStockId(stockId, staff.getStaffId());

        stockSeatMapper.invalidateByStockId(stockId, staff.getStaffId());

        List<SeatImpResult> resultList = new ArrayList<>();

        List<StockSeatImp> stockSeatImps = new ArrayList<>();

        List<StockSeat> seatList = new ArrayList<>();

        AtomicInteger index = new AtomicInteger(0);

        for (SeatImpDto seatImpDto : rowList) {
            try {
                checkData(seatImpDto, stockSeatImps, seatList, index);
            } catch (Exception e) {
                log.error("importExcel seatImpDto=[{}] error ", seatImpDto, e);
                resultList.add(new SeatImpResult(seatImpDto, e.getMessage()));
            }

        }

        if(!resultList.isEmpty()) {
            return resultList;
        }

        if (seatList.size() < performStock.getTotalAmount() - performStock.getRemainAmount()) {
            throw new ServiceException("导入的座位数量小于已分配的座位数量");
        }

        for (StockSeatImp stockSeatImp : stockSeatImps) {
            stockSeatImp.setStockId(stockId);
            stockSeatImp.setState(Constants.Status.VALID);
            stockSeatImp.setCreateStaffId(staff.getStaffId());
            stockSeatImp.setCreateTime(new Date());
            stockSeatImp.setUpdateStaffId(staff.getStaffId());
            stockSeatImp.setUpdateTime(new Date());
        }

        Long[] ids = sequenceWrapper.stockSeatId(seatList.size());

        for (int i = 0; i < seatList.size(); i++) {
            StockSeat ss = seatList.get(i);
            ss.setId(ids[i]);
            ss.setStockId(stockId);
            ss.setStockName(performStock.getName());
            ss.setState(ProjectConstants.SeatState.VALID);
            ss.setCreateStaffId(staff.getStaffId());
            ss.setCreateTime(new Date());
            ss.setUpdateStaffId(staff.getStaffId());
            ss.setUpdateTime(new Date());
        }



        List<String> distinctSeat = seatList.stream().map(s -> s.getRow() + "_" + s.getSeat()).distinct().collect(Collectors.toList());
        if (distinctSeat.size() != seatList.size()) {
            throw new ServiceException("存在重复的座位");
        }


        stockSeatImpMapper.batchInsert(stockSeatImps);

        stockSeatMapper.batchInsert(seatList);





        return resultList;

    }

    private void checkData(SeatImpDto seatImpDto, List<StockSeatImp> stockSeatImps, List<StockSeat> totalSeatList, AtomicInteger index) {

        String row = seatImpDto.getRow();
        String startSeat = seatImpDto.getStartSeat();
        String endSeat = seatImpDto.getEndSeat();
        String num = seatImpDto.getNum();
        String distType = seatImpDto.getDistType();
        if (StringUtils.isBlank(distType)) {
            distType = ProjectConstants.SeatDisType.CONTINUE;
        }
        String invalidSeat = seatImpDto.getInvalidSeat();

        if (StringUtils.isAnyBlank(row, startSeat, endSeat, num)) {
            throw new ServiceException("存在未填写的数据");
        }
        Integer rowNum = extratRowNum(row);
        if (!StringUtils.isNumeric(startSeat) || !StringUtils.isNumeric(endSeat) || !StringUtils.isNumeric(num)) {
            throw new ServiceException("座位号码或者数量设置不正确");
        }
        int start = Integer.parseInt(startSeat);
        int end = Integer.parseInt(endSeat);
        int numInt = Integer.parseInt(num);
        if (start < end) {
            throw new ServiceException("结束座位不可大于开始座位");
        }


        Long batchId = sequenceWrapper.stockSeatId();


        StockSeatImp imp = new StockSeatImp();
        imp.setId(sequenceWrapper.stockSeatId());
        imp.setBatchId(batchId);
        imp.setStockName(seatImpDto.getStockName());
        imp.setRow(rowNum);
        imp.setStartSeat(start);
        imp.setEndSeat(end);
        imp.setNum(numInt);
        imp.setDistType(distType);
        imp.setInvalidSeat(invalidSeat);
        stockSeatImps.add(imp);

        List<Integer> invalidList = com.asiainfo.aisports.tools.StringUtils.convert2IntegerList(invalidSeat);
        Integer group = index.getAndIncrement();


        List<StockSeat> seatList = new ArrayList<>();
        for(int i = start; i <= end; i++) {

            if (invalidList.contains(start)) {
                group = index.incrementAndGet();
                continue;
            }
            StockSeat seat = new StockSeat();
            seat.setImpId(imp.getId());
            seat.setStockName(seatImpDto.getStockName());
            seat.setRow(rowNum);
            seat.setSeat(i);
            seat.setSubRow(group);
            seatList.add(seat);


        }

        if (seatList.isEmpty() || seatList.size() != numInt) {
            throw new ServiceException("小计和实际数量不符");
        }

        totalSeatList.addAll(seatList);



    }

    private Integer extratRowNum(String row) {

        if (StringUtils.isNumeric(row)) {
            return Integer.parseInt(row);
        }

        Matcher matcher = NUM_PATTERN.matcher(row);
        if (!matcher.find()) {
            throw new ServiceException("行数设置不正确");
        }
        return Integer.valueOf(matcher.group(1));
    }

    public ServiceResult getSeatImplInfo(Long stockId, Integer row, Integer seat, int pageNum, int pageSize) {
        StockSeatImp param = new StockSeatImp();
        param.setState(ProjectConstants.SeatState.VALID);
        param.setStockId(stockId);
        param.setRow(row);
        List<DataMap> stockSeatImps = stockSeatImpMapper.selectByPageList(param, new RowBounds(pageNum, pageSize));
        if (seat != null) {
            stockSeatImps = stockSeatImps.stream().filter(si -> si.getInteger("startSeat") <= seat && si.getInteger("endSeat") >= seat).collect(Collectors.toList());

        }

        List<Map<String, Object>> dataList = new ArrayList<>();
        for (DataMap stockSeatImp : stockSeatImps) {
            Map<String, Object> dm = BeanUtils.beanToMap(stockSeatImp);
            StockSeat sp = new StockSeat();
            sp.setImpId(stockSeatImp.getLong("id"));
            sp.setState(ProjectConstants.SeatState.VALID);
            dm.put("seatList", stockSeatMapper.selectByFields(sp));
            dataList.add(dm);

        }
        return new ServiceResult().set("pageInfo", new PageInfo<>(dataList));

    }

    public List<DataMap> getTakeLogPageList(String projectId, String performId, Long stockId, String state, Date startDate, Date endDate, int pageNum, int pageSize) {

        ParamMap param = new ParamMap();
        param.set("projectId", projectId);
        param.set("performId", Strings.emptyToNull(performId));
        param.set("stockId", stockId);
        param.set("state", Strings.emptyToNull(state));
        param.set("startDate", startDate);
        param.set("endDate", endDate);
        return takeSeatLogMapper.selectPageList(param, new RowBounds(pageNum, pageSize));
    }

    public List<DataMap> getCandidateSeatList(String performId, LoginStaff staff) {

        List<PerformStock> stockList = projectManageService.getStockList(performId);
        if (stockList.isEmpty()) {
            return Lists.newArrayList();
        }
        List<DataMap> dataList = new ArrayList<>();
        for (PerformStock performStock : stockList) {
            DataMap map = new DataMap();
            map.put("id", performStock.getId());
            map.put("name", performStock.getName());
            map.put("state", performStock.getAssignState());
            map.put("soldNum", performStock.getTotalAmount() - performStock.getRemainAmount());

            StockSeat param = new StockSeat();
            param.setStockId(performStock.getId());
            param.setState(ProjectConstants.SeatState.VALID);
            List<StockSeat> validSeatList = stockSeatMapper.selectByFields(param);
            param.setState(ProjectConstants.SeatState.OCCUPY);
            List<StockSeat> occupySeatList = stockSeatMapper.selectByFields(param);
            map.put("validNum", validSeatList.size());
            map.put("occupyNum", occupySeatList.size());
            map.put("totalNum", validSeatList.size() + occupySeatList.size());

            dataList.add(map);

        }
        return dataList;
    }

    public void takeSeat(String performId, Long[] stockIds, LoginStaff staff) {
        List<DataMap> candidateSeatList = getCandidateSeatList(performId, staff);
        if (candidateSeatList.isEmpty()) {
            throw new ServiceException("没有可选的票区");
        }


        List<TakeSeatLog> takeSeatLogs = new ArrayList<>();

        for (Long stockId : stockIds) {

            PerformStock performStock = performStockMapper.selectByPrimaryKey(stockId);
            if (performStock == null) {
                throw new ServiceException("票区不存在");
            }
            if (!ProjectConstants.SeatState.OCCUPY.equals(performStock.getAssignState())) {
                throw new ServiceException("票区已经分配");
            }

            StockSeat param = new StockSeat();
            param.setStockId(performStock.getId());
            param.setState(ProjectConstants.SeatState.VALID);
            List<StockSeat> validSeatList = stockSeatMapper.selectByFields(param);
            if (validSeatList.size() < performStock.getTotalAmount() - performStock.getRemainAmount()) {
                throw new ServiceException("可分配座位不足");
            }

            param.setState(ProjectConstants.SeatState.OCCUPY);
            List<StockSeat> occupySeatList = stockSeatMapper.selectByFields(param);

            Long impId = validSeatList.get(0).getImpId();
            StockSeatImp imp = stockSeatImpMapper.selectByPrimaryKey(impId);

            TakeSeatLog takeSeatLog = new TakeSeatLog();
            takeSeatLog.setId(sequenceWrapper.takeSeatLogId());
            takeSeatLog.setCenterId(staff.getCenterId());
            takeSeatLog.setStockId(stockId);
            takeSeatLog.setPerformId(performId);
            takeSeatLog.setState(ProjectConstants.TaskSeatState.PROCESS);
            takeSeatLog.setCreateTime(new Date());
            takeSeatLog.setCreateStaffId(staff.getStaffId());
            takeSeatLog.setTotalNum(validSeatList.size() + occupySeatList.size());
            takeSeatLog.setSoldNum(performStock.getTotalAmount() - performStock.getRemainAmount());
            takeSeatLog.setValidNum(validSeatList.size());
            takeSeatLog.setFinishNum(occupySeatList.size());
            takeSeatLog.setBatchId(imp.getBatchId());
            takeSeatLogs.add(takeSeatLog);
        }

        boolean lock = redisCommonService.lock(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_TAKE_SEAT_LOCK, performId), DateCalUtil.date2String(new Date(), Constants.DEFAULT_TIME_FORMAT), -1);
        if (!lock) {
            throw new ServiceException("该场次正在落座中,请稍后再试");
        }


        takeSeatLogMapper.batchInsert(takeSeatLogs);

        jobMessageService.sendMessage(staff.getCenterId(), JobEnum.PROJECT_TAKE_SEAT_JOB,
                MapUtil.build("performId", performId,
                        "logIds", takeSeatLogs.stream().map(l -> l.getId().toString()).collect(Collectors.joining(","))
                )
        );





    }


    @Transactional
    public void takeSeat(TakeSeatLog takeSeatLog) {
        Long id = takeSeatLog.getId();
        TakeSeatLog log = takeSeatLogMapper.selectByPrimaryKey(id);
        if (log == null) {
            throw new ServiceException("任务不存在");
        }
        if (!ProjectConstants.TaskSeatState.PROCESS.equals(log.getState())) {
            throw new ServiceException("任务状态不正常");
        }

        Long stockId = takeSeatLog.getStockId();

        StockSeat param = new StockSeat();
        param.setStockId(stockId);
        param.setState(ProjectConstants.SeatState.VALID);
        List<StockSeat> validSeatList = stockSeatMapper.selectByFields(param);


        List<TicketPersonForSeat> persons = tradeTicketPersonMapper.selectSeatCandidateList(stockId);
        if (persons.size() > validSeatList.size()) {
            throw new ServiceException("可分配座位不足");
        }

        Map<Boolean, List<TicketPersonForSeat>> gmap = persons.stream().collect(Collectors.groupingBy(p -> ProjectConstants.TicketKind.TEAM.equals(p.getType())));
        List<TicketPersonForSeat> teamPersons = gmap.getOrDefault(true, new ArrayList<>());
        List<TicketPersonForSeat> noTeamPersons = gmap.getOrDefault(false,new ArrayList<>());

        List<List<TicketPersonForSeat>> teamList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(teamPersons)) {
            Map<Long, List<TicketPersonForSeat>> collect = teamPersons.stream().collect(Collectors.groupingBy(TicketPersonForSeat::getTradeIdB));
            teamList.addAll(collect.values());

        }
        List<List<TicketPersonForSeat>> noTeamList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(noTeamPersons)) {
            noTeamList = noTeamPersons
                    .stream()
                    .collect(Collectors.groupingBy(TicketPersonForSeat::getTicketId))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(List::size, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

        }

        // Group seats by subRow, each subRow represents a consecutive seating area
        Map<Integer, List<StockSeat>> subRowSeatMap = validSeatList
                .stream()
                .collect(Collectors.groupingBy(StockSeat::getSubRow));

        // Sort consecutive seating areas by seat count in descending order, prioritize larger areas
        List<List<StockSeat>> sortedSubRowSeats = subRowSeatMap.values()
                .stream()
                .sorted(Comparator.comparing(List::size, Comparator.reverseOrder()))
                .collect(Collectors.toList());

        // Sort seats within each consecutive area by seat number
        for (List<StockSeat> subRowSeats : sortedSubRowSeats) {
            subRowSeats.sort(Comparator.comparing(StockSeat::getSeat));
        }

        // Execute seat assignment
        List<StockSeat> assignedSeats = new ArrayList<>();

        // 1. Assign team tickets first
        assignedSeats.addAll(assignTeamTickets(teamList, sortedSubRowSeats));

        // 2. Then assign group tickets (sorted by size in descending order)
        assignedSeats.addAll(assignGroupTickets(noTeamList, sortedSubRowSeats));

        // Update seat status to assigned
        updateSeatAssignments(assignedSeats, stockId);


        takeSeatLog.setState(ProjectConstants.TaskSeatState.SUCCESS);
        takeSeatLog.setFinishTime(new Date());



    }

    /**
     * Assign team ticket seats - start consecutive assignment from smallest row and seat number
     * @param teamList List of team tickets
     * @param sortedSubRowSeats Consecutive seating areas sorted by seat count in descending order
     * @return List of assigned seats
     */
    private List<StockSeat> assignTeamTickets(List<List<TicketPersonForSeat>> teamList,
                                              List<List<StockSeat>> sortedSubRowSeats) {
        List<StockSeat> assignedSeats = new ArrayList<>();

        // Get all available seats and sort by row number and seat number
        List<StockSeat> allAvailableSeats = new ArrayList<>();
        for (List<StockSeat> subRowSeats : sortedSubRowSeats) {
            allAvailableSeats.addAll(subRowSeats.stream()
                    .filter(seat -> seat.getPersonId() == null)
                    .collect(Collectors.toList()));
        }

        // Sort by row and seat number in ascending order to ensure assignment starts from the smallest
        allAvailableSeats.sort(Comparator.comparing(StockSeat::getRow)
                .thenComparing(StockSeat::getSeat));

        for (List<TicketPersonForSeat> team : teamList) {
            int teamSize = team.size();

            if (allAvailableSeats.size() < teamSize) {
                log.warn("Team ticket assignment failed,team tradeId:【{}】, team size: 【{}】, remaining available seats: 【{}】", team.get(0).getTradeIdB(), teamSize, allAvailableSeats.size());
                throw new ServiceException("团体票分配失败，座位不够");
            }

            // Start consecutive assignment from smallest row and seat number
            List<StockSeat> teamSeats = allAvailableSeats.subList(0, teamSize);

            // Assign seats to team members
            for (int i = 0; i < teamSize; i++) {
                StockSeat seat = teamSeats.get(i);
                seat.setPersonId(team.get(i).getId());
                assignedSeats.add(seat);
            }

            // Remove assigned seats from available seat list
            allAvailableSeats.removeAll(teamSeats);

            log.info("Team ticket assignment successful,team tradeId:【{}】，team size: 【{}】, assigned seats: 【{}】",team.get(0).getTradeIdB(), teamSize,
                    teamSeats.stream()
                            .map(s -> s.getRow() + "排" + s.getSeat() + "座")
                            .collect(Collectors.joining(", ")));
        }

        return assignedSeats;
    }

    /**
     * Assign group ticket seats
     * @param groupList List of group tickets (already sorted by size in descending order)
     * @param sortedSubRowSeats Consecutive seating areas sorted by seat count in descending order
     * @return List of assigned seats
     */
    private List<StockSeat> assignGroupTickets(List<List<TicketPersonForSeat>> groupList,
                                               List<List<StockSeat>> sortedSubRowSeats) {
        List<StockSeat> assignedSeats = new ArrayList<>();

        for (List<TicketPersonForSeat> group : groupList) {
            int groupSize = group.size();
            List<StockSeat> allocatedSeats = findBestSeatsForGroup(groupSize, sortedSubRowSeats);

            if (allocatedSeats.size() >= groupSize) {
                // Assign seats to group members
                for (int i = 0; i < groupSize; i++) {
                    StockSeat seat = allocatedSeats.get(i);
                    seat.setPersonId(group.get(i).getId());
                    assignedSeats.add(seat);
                }
                log.info("Group ticket assignment successful,ticketId=【{}】 group size: 【{}】, assigned seats: 【{}】", group.get(0).getTicketId(), groupSize,
                        allocatedSeats.subList(0, groupSize).stream()
                                .map(s -> s.getRow() + "排" + s.getSeat() + "座")
                                .collect(Collectors.joining(", ")));
            } else {
                log.warn("Group ticket assignment failed, group size: 【{}】,ticketId:【{}】, insufficient consecutive seats available", groupSize, group.get(0).getTicketId());
                throw new ServiceException("家庭票或者个人座位分配失败，座位不够");
            }
        }

        return assignedSeats;
    }

    /**
     * Find best seats for specified group/team size
     * @param groupSize Size of group/team
     * @param sortedSubRowSeats Consecutive seating areas sorted by seat count in descending order
     * @return List of assigned seats
     */
    private List<StockSeat> findBestSeatsForGroup(int groupSize, List<List<StockSeat>> sortedSubRowSeats) {

        // Strategy 1: Prioritize assignment within a single consecutive seating area
        for (List<StockSeat> subRowSeats : sortedSubRowSeats) {
            List<StockSeat> availableSeats = subRowSeats.stream()
                    .filter(seat -> seat.getPersonId() == null)
                    .collect(Collectors.toList());

            if (availableSeats.size() >= groupSize) {
                // Find consecutive seats within this seating area
                // same subRow is consecutive
                return availableSeats.subList(0, groupSize);
            }
        }

        // Strategy 2: If single seating area cannot satisfy, try cross-area assignment (prioritize same row)
        List<StockSeat> result = new ArrayList<>(findSeatsAcrossSubRows(groupSize, sortedSubRowSeats, true));
        if (result.size() >= groupSize) {
            return result.subList(0, groupSize);
        }

        // Strategy 3: Finally try cross-row assignment
        result.clear();
        result.addAll(findSeatsAcrossSubRows(groupSize, sortedSubRowSeats, false));

        return result;
    }

    /**
     * Find consecutive seats in the seat list
     * @param seats Available seat list (already sorted by seat number)
     * @param requiredCount Required number of seats
     * @return List of consecutive seats
     */
    private List<StockSeat> findConsecutiveSeats(List<StockSeat> seats, int requiredCount) {
        if (seats.size() < requiredCount) {
            return new ArrayList<>();
        }

        for (int i = 0; i <= seats.size() - requiredCount; i++) {
            List<StockSeat> consecutive = new ArrayList<>();
            consecutive.add(seats.get(i));

            for (int j = i + 1; j < seats.size() && consecutive.size() < requiredCount; j++) {
                StockSeat currentSeat = seats.get(j);
                StockSeat lastSeat = consecutive.get(consecutive.size() - 1);

                // Check if consecutive (same row and consecutive seat numbers)
                if (currentSeat.getRow().equals(lastSeat.getRow()) &&
                    currentSeat.getSeat().equals(lastSeat.getSeat() + 1)) {
                    consecutive.add(currentSeat);
                } else {
                    break;
                }
            }

            if (consecutive.size() >= requiredCount) {
                return consecutive;
            }
        }

        return new ArrayList<>();
    }

    /**
     * Assign seats across consecutive seating areas
     * @param groupSize Required number of seats
     * @param sortedSubRowSeats List of consecutive seating areas
     * @param sameRowOnly Whether to assign only within the same row
     * @return List of assigned seats
     */
    private List<StockSeat> findSeatsAcrossSubRows(int groupSize, List<List<StockSeat>> sortedSubRowSeats,
                                                   boolean sameRowOnly) {
        List<StockSeat> result = new ArrayList<>();

        if (sameRowOnly) {
            // Group by row number
            Map<Integer, List<StockSeat>> rowMap = new HashMap<>();
            for (List<StockSeat> subRowSeats : sortedSubRowSeats) {
                for (StockSeat seat : subRowSeats) {
                    if (seat.getPersonId() == null) {
                        rowMap.computeIfAbsent(seat.getRow(), k -> new ArrayList<>()).add(seat);
                    }
                }
            }

            // Try to assign within each row
            for (List<StockSeat> rowSeats : rowMap.values()) {
                rowSeats.sort(Comparator.comparing(StockSeat::getSeat));
                if (rowSeats.size() >= groupSize) {
                    List<StockSeat> consecutiveSeats = findConsecutiveSeats(rowSeats, groupSize);
                    if (consecutiveSeats.size() >= groupSize) {
                        return consecutiveSeats.subList(0, groupSize);
                    }
                }
            }
        } else {
            // Cross-row assignment, try to select nearby seats
            List<StockSeat> allAvailableSeats = new ArrayList<>();
            for (List<StockSeat> subRowSeats : sortedSubRowSeats) {
                allAvailableSeats.addAll(subRowSeats.stream()
                        .filter(seat -> seat.getPersonId() == null)
                        .collect(Collectors.toList()));
            }

            // Sort by row number and seat number
            allAvailableSeats.sort(Comparator.comparing(StockSeat::getRow)
                    .thenComparing(StockSeat::getSeat));

            if (allAvailableSeats.size() >= groupSize) {
                return allAvailableSeats.subList(0, groupSize);
            }
        }

        return result;
    }

    /**
     * Update seat assignment status
     * @param assignedSeats List of assigned seats
     * @param stockId Stock ID
     */
    private void updateSeatAssignments(List<StockSeat> assignedSeats, Long stockId) {
        for (StockSeat seat : assignedSeats) {
            seat.setState(ProjectConstants.SeatState.OCCUPY);
            seat.setUpdateTime(new Date());
            stockSeatMapper.updateByPrimaryKeySelective(seat);
        }

        log.info("Seat assignment completed, stock ID: {}, assigned seats count: {}", stockId, assignedSeats.size());
    }


    @Transactional
    @TargetDataSource(name = "{}")
    public void updateLog(TakeSeatLog takeSeatLog, @RoutingKey Long centerId) {
        takeSeatLogMapper.updateByPrimaryKeySelective(takeSeatLog);

    }
}
