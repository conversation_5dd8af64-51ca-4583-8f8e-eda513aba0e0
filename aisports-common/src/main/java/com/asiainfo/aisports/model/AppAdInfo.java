package com.asiainfo.aisports.model;

import com.asiainfo.aisports.domain.core.AppAd;
import org.springframework.beans.BeanUtils;

/**
 * Created by john.
 * Date: 2019/4/18
 * Time: 11:35
 */
public class AppAdInfo extends AppAd {

    private String venueName;

    private String typeName;

    private String contTypeName;

    private String imageType;

    private String listImageType;

    private String contInfo;

    private String associatedEntityName;

    public AppAdInfo() {
    }

    public AppAdInfo(AppAd appAd) {
        super();
        BeanUtils.copyProperties(appAd, this);
    }

    public String getVenueName() {
        return venueName;
    }

    public void setVenueName(String venueName) {
        this.venueName = venueName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getContTypeName() {
        return contTypeName;
    }

    public void setContTypeName(String contTypeName) {
        this.contTypeName = contTypeName;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getListImageType() {
        return listImageType;
    }

    public void setListImageType(String listImageType) {
        this.listImageType = listImageType;
    }

    public String getContInfo() {
        return contInfo;
    }

    public void setContInfo(String contInfo) {
        this.contInfo = contInfo;
    }

    public String getAssociatedEntityName() {
        return associatedEntityName;
    }

    public void setAssociatedEntityName(String associatedEntityName) {
        this.associatedEntityName = associatedEntityName;
    }
}
