<!--
 * @Descripttion: 
 * @version: 
 * @Author: wh
 * @Date: 2022-03-10 12:34:48
 * @LastEditTime: 2022-03-11 18:38:43
-->
<template>
  <div>
    <div class="custom-title" v-if="isTitle">
      <div class="l-title">
        {{ titleInfo.name }}
      </div>
      <div class="r-date-search">
        <el-date-picker
          v-if="pickerIsMonth"
          v-model="charts.monthVal"
          format="yyyy-MM"
          value-format="yyyy-MM"
          type="monthrange"
          range-separator="至"
          :picker-options="pickerBeginDateBefore"
          start-placeholder="开始月份"
          end-placeholder="结束月份"
          @change="changeMonth"
        >
        </el-date-picker>
        <el-date-picker
          v-model="charts.dateVal"
          v-else
          type="daterange"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          :popper-class="popperClass"
          @change="changeDate"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        >
        </el-date-picker>
      </div>
    </div>
    <div class="custom-card" v-if="isCard">
      <div class="c-left">
        <div class="divide icon-desc" :style="{ backgroundColor: cardInfo.bg }">
          <i
            class="iconfont"
            :class="cardInfo.icon"
            :style="{ color: cardInfo.iconBg }"
          ></i>
        </div>
        <div class="divide c-l-content">
          <span>{{ cardInfo.name }}</span>
          <span>{{ cardInfo.value }}</span>
        </div>
      </div>
      <div class="c-right" v-if="true">
        <div>
          <div class="compare-title">同比去年同月</div>
          <span :class="true ? 'shangsheng-class' : 'xiajiang-class'"
            >+19
            <i
              class="iconfont"
              :class="true ? 'icon-shangsheng' : 'icon-icon-test'"
            ></i
          ></span>
        </div>
        <div>
          <div class="compare-title">环比上月</div>
          <span :class="false ? 'shangsheng-class' : 'xiajiang-class'"
            >-20
            <i
              class="iconfont"
              :class="false ? 'icon-shangsheng' : 'icon-icon-test'"
            ></i
          ></span>
        </div>
      </div>
      <div class="c-right" v-else>
        <div class="compare-title">环比上月</div>
        <span :class="false ? 'shangsheng-class' : 'xiajiang-class'"
          >-20
          <i
            class="iconfont"
            :class="false ? 'icon-shangsheng' : 'icon-icon-test'"
          ></i
        ></span>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    isTitle: {
      type: Boolean,
      default: () => false,
    },
    popperClass: {
      type: String,
      default: () => "",
    },
    isCard: {
      type: Boolean,
      default: () => false,
    },
    titleInfo: {
      type: Object,
      default: () => {},
    },
    cardInfo: {
      type: Object,
      default: () => {},
    },
    pickerIsMonth: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      pickerBeginDateBefore: {
        disabledDate: (time) => {
          var _ = this;
          return time.getTime() > new Date(_.getLastMonth()).getTime(); //如果现在是12月，则getLastMonth（）为 2020-11，那么十二月不能选，之后年的月份都不可选
        },
      },
      pickerOptions: {
        disabledDate(v) {
          return v.getTime() > new Date().getTime() - 86400000; //  - 86400000是否包括当天
        },
      },
      charts: {
        dateVal: [],
        monthVal: "",
      },
    };
  },
  methods: {
    changeMonth(val) {
      this.$emit("monthChange", val);
    },
    getLastMonth() {
      //获取上个月日期 格式 2020-12
      var date = new Date();
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      if (month == 0) {
        year = year - 1;
        month = 12;
      }
      month = month < 10 ? `0${month}` : month;
      return year + "-" + month;
    },
    changeDate(val) {
      this.$emit("changeDate", val && val.length ? val : []);
    },
    assign(val) {
      this.charts.dateVal = val;
    },
  },
};
</script>

<style></style>
