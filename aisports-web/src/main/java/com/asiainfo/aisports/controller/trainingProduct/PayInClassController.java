package com.asiainfo.aisports.controller.trainingProduct;


import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.TcTime;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.TcLessonPriceInfo;
import com.asiainfo.aisports.model.TcLessonResvInfo;
import com.asiainfo.aisports.service.PayInClassService;
import com.asiainfo.aisports.service.VenueService;
import com.asiainfo.aisports.service.training.TcTimeService;
import com.asiainfo.aisports.web.WebResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * User: 齐彦洲
 * Date: 2018/1/9
 * Time: 10:01
 */
@Controller
@RequestMapping("/payInClass")
public class PayInClassController {

    @Autowired
    PayInClassService payInClassService;
    @Autowired
    VenueService venueService;
    @Autowired
    TcTimeService tcTimeService;
    Logger logger = LoggerFactory.getLogger(PayInClassController.class);

    @RequestMapping("")
    public String payInClass() {
        return "/payInClass/payInClass";
    }

    /**
     * 根据卡号获取预约信息
     *
     * @param staff
     * @param custId
     * @return
     */
    @RequestMapping(value = "/findTcLessonResvList")
    @ResponseBody
    public WebResult findTcLessonResvList(LoginStaff staff,
                                          @RequestParam("custId") Long custId) {

        if(custId == null){
            return new WebResult(1,"custId不可以为空");
        }
        return new WebResult().set("tcLessonResvInfoList", payInClassService.findTcLessonResvList(custId, staff, "0"));//状态 0-已预约 1-已执行 2-已取消
    }

    /**
     * 根据卡号查询学员列表
     *
     * @param custId
     * @param staff
     * @return
     */
    @RequestMapping(value = "/findStudentList")
    @ResponseBody
    public WebResult findStudentList(Long custId, LoginStaff staff) {
        return new WebResult().set("studentList", payInClassService.findStudentList(custId, staff));
    }

    /**
     * 获取折扣列表
     *
     * @param staff
     * @return
     */
    @RequestMapping(value = "/findDiscountListAndOnClassNum")
    @ResponseBody
    public WebResult findDiscountListAndOnClassNum(LoginStaff staff) {
        return new WebResult().set("discount", payInClassService.getDiscount(staff)).set("classNum", payInClassService.findClassNumByVenueId(staff.getVenueId()));
    }

    /**
     * 价格和交费历史列表
     *
     * @param staff
     * @param custId
     * @param resvId
     * @param lessonType
     * @param serviceId
     * @return
     */
    @RequestMapping(value = "/findPayAndPriceList")
    @ResponseBody
    public WebResult findPayAndPriceList(LoginStaff staff,
                                         @RequestParam("custId") Long custId,
                                         @RequestParam(value = "resvId", required = false) Long resvId,
                                         @RequestParam(value = "lessonType", required = false) String lessonType,
                                         @RequestParam(value = "serviceId", required = false) Long serviceId) {

        if(custId == null){
            return new WebResult(1,"custId不可以为空");
        }
        List<TcLessonResvInfo> tcLessonResvList = payInClassService.findTcLessonResvList(custId, staff, "1");
        WebResult webResult = new WebResult();

        if (resvId != null) {
            List<TcLessonPriceInfo> tcLessonPriceInfoList = payInClassService.findPayList(staff, resvId, lessonType, serviceId);
            if (tcLessonPriceInfoList.isEmpty() && Constants.TcLessonType.COACH_LESSON.equals(lessonType)) {
                webResult.put("message", "该私教课教练没有设置级别，无法查询价格");
            }
            webResult.set("tcLessonPriceInfoList", tcLessonPriceInfoList);
        }
        webResult.set("TcLessonResvInfo", tcLessonResvList);
        return webResult;
    }

    /**
     * 查询场馆内 所有服务项的教练
     *
     * @param staff
     * @return
     */
    @RequestMapping(value = "/findCoachList")
    @ResponseBody
    public WebResult findCoachList(LoginStaff staff, String lessonType) {
        return new WebResult().set("serviceMapList", payInClassService.findCoachList(staff, lessonType));
    }

    /**
     * 查询场馆内 所有服务项
     *
     * @param staff
     * @return
     */
    @RequestMapping(value = "/findServiceList")
    @ResponseBody
    public WebResult findServiceList(LoginStaff staff) {
        return new WebResult().set("serviceList", venueService.selectVenueServiceList(staff.getVenueId()));
    }

    /**
     * 查询场馆内 今日上课情况
     *
     * @param staff
     * @param serviceId
     * @param timeId
     * @param ecardNo
     * @param coachId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/findClassDetailsByServiceId")
    @ResponseBody
    public WebResult findClassDetailsByServiceId(LoginStaff staff,
                                                 @RequestParam(value = "serviceId") Long serviceId, Long timeId, String ecardNo,
                                                 Long coachId, Integer pageNo, Integer pageSize, Long payFlag) {
        // 传空默认为null
        if (Strings.isNullOrEmpty(ecardNo)) {
            ecardNo = null;
        }
        List<Map<String, Object>> classDetails = payInClassService.findClassDetailsByServiceId(serviceId, timeId, ecardNo, coachId, pageNo, pageSize, payFlag, staff.getVenueId());
        List<Long> timeIdList = Lists.newArrayList();
        List<TcTime> tcTimeList = Lists.newArrayList();
        classDetails.forEach(classDetail -> {
            Long timeIdParam = (Long) classDetail.get("timeId");
            if (!timeIdList.contains(timeIdParam)) {
                timeIdList.add(timeIdParam);
                tcTimeList.add(tcTimeService.findDetailsByPrimaryKey(timeIdParam));
            }
        });
        return new WebResult().set("classDetails", new PageInfo<>(classDetails)).set("timeIdList", tcTimeList).set("coachMapList", payInClassService.findCoachList(serviceId))
                .set("onClassNumMap", payInClassService.findNumByVenueId(staff.getVenueId(), serviceId));
    }

    /**
     * 结账
     *
     * @param staff
     * @param resvId
     * @param ecardNo
     * @param payFee
     * @param priceItem //tc_lesson_price主键
     * @param discount
     * @return
     */
    @RequestMapping(value = "/submitResv")
    @ResponseBody
    public WebResult submitResv(LoginStaff staff, @RequestParam(value = "resvId") Long resvId,
                                @RequestParam(value = "ecardNo") String ecardNo,
                                @RequestParam(value = "payFee") Long payFee,
                                @RequestParam(value = "priceItem") String priceItem,
                                @RequestParam(value = "discount", required = false) String discount) {
        WebResult webResult = new WebResult();
        webResult.putAll(payInClassService.submitResv(staff, resvId, ecardNo, payFee, priceItem, discount));
        return webResult;
    }

    /**
     * 重新结账
     *
     * @param staff
     * @param resvId
     * @return
     */
    @RequestMapping(value = "/refundPay")
    @ResponseBody
    public WebResult refundPay(LoginStaff staff, @RequestParam(value = "resvId") Long resvId) {
        String refundInfo;
        try {
            refundInfo = payInClassService.refundPay(staff, resvId);
        } catch (Exception e) {
            logger.info("refundPay", e);
            return new WebResult(1, "保存失败");
        }
        return new WebResult(0, refundInfo).set("resvId", resvId);
    }

    /**
     * 取消预约
     *
     * @param staff
     * @param resvId
     * @return
     */
    @RequestMapping(value = "/cancelResv")
    @ResponseBody
    public WebResult cancelResv(LoginStaff staff, @RequestParam(value = "resvId") Long resvId) {
        payInClassService.cancelResv(staff, resvId);
        return new WebResult(0, "保存成功");
    }

    /**
     * 新增预约
     *
     * @param staff
     * @param lessonId
     * @param studentId
     * @return
     */
    @RequestMapping(value = "/addTcLessonResv")
    @ResponseBody
    public WebResult addTcLessonResv(LoginStaff staff,
                                     @RequestParam(value = "lessonId") Long lessonId,
                                     @RequestParam(value = "studentId") Long studentId) {

        Long resvId;
        try {
            resvId = payInClassService.addTcLessonResv(staff, lessonId, studentId);
        } catch (Exception e) {
            logger.info("addTcLessonResv", e);
            return new WebResult(1, "保存失败");
        }
        return new WebResult(0, "保存成功").set("resvId", resvId);
    }

    /**
     * 新增预约
     *
     * @param lessonId
     * @param resvId
     * @return
     */
    @RequestMapping(value = "/saveTcLessonResv")
    @ResponseBody
    public WebResult saveTcLessonResv(@RequestParam(value = "lessonId") Long lessonId,
                                      @RequestParam(value = "resvId") Long resvId) {

        try {
            payInClassService.saveTcLessonResv(lessonId, resvId);
        } catch (Exception e) {
            logger.info("saveTcLessonResv", e);
            return new WebResult(1, "保存失败");
        }
        return new WebResult(0, "保存成功").set("resvId", resvId);
    }
}
