package com.asiainfo.aisports.api.service.socket;

import com.asiainfo.aisports.tools.StringUtils;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.Set;
import java.util.UUID;

public class NioTcpServer {
    private static final Logger logger = LoggerFactory.getLogger(NioTcpServer.class);
    private Selector selector;
    private ByteBuffer buffer = ByteBuffer.allocate(1024);

    public NioTcpServer(int port) throws IOException {
        selector = Selector.open();
        ServerSocketChannel serverChannel = ServerSocketChannel.open();
        serverChannel.bind(new InetSocketAddress(port));
        serverChannel.configureBlocking(false);
        serverChannel.register(selector, SelectionKey.OP_ACCEPT);
    }

    public void start() throws IOException {
        while (true) {
            int readyChannels = selector.select();
            if (readyChannels == 0) {
                continue;
            }

            Set<SelectionKey> selectedKeys = selector.selectedKeys();
            Iterator<SelectionKey> keyIterator = selectedKeys.iterator();

            while (keyIterator.hasNext()) {
                SelectionKey key = keyIterator.next();
                if (key.isAcceptable()) {
                    acceptConnection(key);
                } else if (key.isReadable()) {
                    readData(key);
                } else if (key.isWritable()) {
                    writeData(key);
                }
                keyIterator.remove();
            }
        }
    }

    private void acceptConnection(SelectionKey key) throws IOException {
        ServerSocketChannel serverChannel = (ServerSocketChannel) key.channel();
        SocketChannel clientChannel = serverChannel.accept();
        clientChannel.configureBlocking(false);
        clientChannel.register(selector, SelectionKey.OP_READ | SelectionKey.OP_WRITE);
    }

    private void readData(SelectionKey key) throws IOException {
        SocketChannel clientChannel = (SocketChannel) key.channel();
        buffer.clear();
        int numBytesRead = clientChannel.read(buffer);
        if (numBytesRead == -1) {
            disconnectClient(key);
            return;
        }
        String msg = new String(buffer.array(), 0, numBytesRead, StandardCharsets.UTF_8);
        key.interestOps(SelectionKey.OP_WRITE);
        key.attach(msg);
    }

    private void writeData(SelectionKey key) throws IOException {
        SocketChannel clientChannel = (SocketChannel) key.channel();
        String msg = (String) key.attachment();
        if (StringUtils.isEmpty(msg)) {
            return;
        }
        key.attach(null);
        ByteBuffer buf = ByteBuffer.wrap(msg.getBytes());
        clientChannel.write(buf);
        key.interestOps(SelectionKey.OP_READ);
    }

    private void disconnectClient(SelectionKey key) throws IOException {
        SocketChannel clientChannel = (SocketChannel) key.channel();
        key.cancel();
        clientChannel.close();
    }
}