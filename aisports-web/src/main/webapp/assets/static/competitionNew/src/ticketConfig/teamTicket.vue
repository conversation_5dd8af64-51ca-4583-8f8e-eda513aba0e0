<template>
  <div class="team-ticket-container">
    <section class="header-area">
      <div class="panel-heading">
        <span>发放团体票</span>
        <span class="btn-return" title="后退" @click="goBack">
          <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
        </span>
      </div>
    </section>

    <section class="search-area">
      <el-select v-model="performId" placeholder="场次" clearable filterable>
        <el-option v-for="perform in performList" :key="perform.id" :label="perform.name" :value="perform.id"> </el-option>
      </el-select>
      <el-select v-model="stockId" placeholder="票区" clearable filterable>
        <el-option v-for="stock in stockList" :key="stock.id" :label="stock.name" :value="stock.id"> </el-option>
      </el-select>
      <input v-model="keyword" class="normal" required placeholder="票名称/团体名称" />

      <el-date-picker class="date-long" v-model="salesDateRange" format="yyyy-MM-dd HH:mm" type="datetimerange" placeholder="选择日期时间"></el-date-picker>
      <el-button icon="el-icon-search" type="primary" plain @click="selectTableData">查询</el-button>
    </section>

    <section class="action-area">
      <el-button icon="el-icon-plus" @click="createdTeamTicket">新增团体票生成</el-button>
    </section>

    <section class="table-area">
      <el-table :data="teamTicketSendRecordList" header-cell-class-name="camp-table-header" style="width: 100%">
        <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
        <el-table-column align="center" prop="tradeId" label="业务流水号"></el-table-column>
        <el-table-column align="center" prop="performName" label="场次"></el-table-column>
        <el-table-column align="center" prop="stockName" label="票区"></el-table-column>
        <el-table-column align="center" prop="teamName" label="团体名称"></el-table-column>
        <el-table-column align="center" prop="performTicketName" label="票名称"></el-table-column>
        <el-table-column align="center" prop="staffName" label="办理人"></el-table-column>
        <el-table-column align="center" prop="createTime" label="办理时间"></el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button @click="handleview(scope.$index, scope.row)" type="text" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="page_footer">
        <el-pagination layout="prev, pager, next" background :total="teamPageInfo.total" @current-change="handleTeamPageChange"> </el-pagination>
      </div>
    </section>

    <section class="pagination-area">
      <el-drawer title="新增团体票生成" :visible.sync="drawer" size="520" :before-close="handleClose">
        <div class="drawer-content">
          <!-- 比赛场次选择 -->
          <el-form label-width="100px" :rules="rules" :model="form">
            <el-form-item label="比赛场次:">
              <el-select v-model="form.performId" @change="changeMatchId" placeholder="请选择场次">
                <el-option v-for="perform in performList" :key="perform.id" :label="perform.name" :value="perform.id"> </el-option>
              </el-select>
            </el-form-item>

            <!-- 票名称选择 -->
            <el-form-item label="票名称:">
              <el-select v-model="form.performTicketId" placeholder="请选择票" @change="changePerformTicketId" filterable>
                <el-option v-for="item in teamTicketList" :key="item.performTicketId" :label="item.performTicketName" :value="item.performTicketId"></el-option>
              </el-select>
            </el-form-item>

            <!-- 票区信息展示 -->
            <div class="ticket-area-grid" v-if="teamTicketSelected.performTicketId">
              <div class="ticket-item">
                <div class="ticket-label">票区：</div>
                <div class="ticket-value">{{ teamTicketSelected.stockName }}</div>
              </div>

              <div class="ticket-item">
                <div class="ticket-label">票价：</div>
                <div class="ticket-value">{{ m2front(teamTicketSelected.price) }}</div>
              </div>

              <div class="ticket-item">
                <div class="ticket-label">票名称：</div>
                <div class="ticket-value">{{ teamTicketSelected.performTicketName }}</div>
              </div>

              <div class="ticket-item">
                <div class="ticket-label">总座位数：</div>
                <div class="ticket-value">{{ teamTicketSelected.totalAmount }}</div>
              </div>

              <div class="ticket-item">
                <div class="ticket-label">已占座位：</div>
                <div class="ticket-value text-red">{{ teamTicketSelected.totalAmount - teamTicketSelected.remainAmount }}</div>
              </div>

              <div class="ticket-item">
                <div class="ticket-label">剩余票数：</div>
                <div class="ticket-value">{{ teamTicketSelected.remainAmount }}</div>
              </div>

              <div class="ticket-item">
                <div class="ticket-label">核销区域：</div>
                <div class="ticket-value">{{ teamTicketSelected.siteName }}</div>
              </div>

              <div class="ticket-item">
                <div class="ticket-label">权益：</div>
                <div class="ticket-value text-blue">{{ teamTicketSelected.rightsName }}</div>
              </div>
            </div>

            <!-- 发放数量输入 -->
            <el-form-item label="发放数量:" prop="sendNum">
              <el-input-number v-model="form.sendNum" :min="1" :max="teamTicketSelected.remainAmount"></el-input-number>
            </el-form-item>

            <!-- 提示信息 -->
            <div class="warning-message"><i class="el-icon-warning"></i> 生成后不支持撤销、删除，请确认操作!</div>
            <div class="demo-drawer__footer">
              <el-button @click="cancelForm">取 消</el-button>
              <el-button type="primary" @click="submitForm">确定</el-button>
            </div>
          </el-form>
        </div>
      </el-drawer>
    </section>
    <!-- 页面内容 -->
    <!-- 可以在这里添加更多组件或功能 -->
  </div>
</template>

<script>
import * as service from "./service";
import { showSuccess, showWrong } from "mAlert";
import moment from "moment";

export default {
  name: "TeamTicket",
  data() {
    return {
      // 数据属性
      projectId: "",
      performId: "",
      stockId: "",
      stockList: [],
      keyword: "",
      salesDateRange: [],
      performList: [],
      startDate: "",
      endDate: "",
      teamPageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      teamTicketSendRecordList: [],
      teamTicketList: [],
      teamTicketSelected: {
        remainAmount: 100,
        price: 0,
      },
      drawer: false,
      form: {
        sendNum: 1,
        performId: "",
        performTicketId: "",
      },
      rightsList2: [],
      rules: {
        sendNum: [
          { required: true, message: "发放数量不能为空", trigger: "blur" },
          { type: "number", min: 1, message: "发放数量必须大于0", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (!this.teamTicketSelected || !this.teamTicketSelected.remainAmount) {
                callback(this.showWrong("请先选择票信息"));
              } else if (value > this.teamTicketSelected.remainAmount) {
                callback(this.showWrong("发放数量不能超过剩余票数"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    m2front,
    showSuccess,
    showWrong,
    // 方法定义
    goBack() {
      this.$router.go(-1);
    },
    handleview(index, row) {
      console.log(index, row, "查看");
      this.$router.push({
        path: "/teamTicketDetail",
        query: {
          projectId: this.projectId,
          recordId: row.id,
        },
      });
    },
    changeMatchId() {
      this.getTeamPerformTicketPageList();
    },
    changePerformTicketId(id) {
      this.teamTicketList.forEach((ticket) => {
        if (ticket.performTicketId == this.form.performTicketId) {
          this.teamTicketSelected = ticket;
          if (ticket.rightsIds) {
            let rightsIdsList = ticket.rightsIds.split(",");
            ticket.rightsName = rightsIdsList
              .map((rightId) => {
                let rightitem = this.rightsList2[rightId];
                return rightitem;
              })
              .join(",");
          } else {
            ticket.rightsName = "";
          }
        }
      });
    },

    submitForm() {
      let params = {
        performTicketId: this.teamTicketSelected.performTicketId,
        stockId: this.teamTicketSelected.stockId,
        price: this.teamTicketSelected.price,
        sendNum: this.form.sendNum,
      };

      service.sendTeamTicket(params, (res) => {
        if (res.error == 0) {
          this.drawer = false;
          this.showSuccess("新建成功");
          this.teamPageInfo.pageNum = 1;
          this.getTeamTicketSendRecordList();
          this.initFormData();
        } else {
          this.showWrong(res.message);
        }
      });
    },
    initFormData() {
      this.form = {
        sendNum: 1,
        performId: "",
        performTicketId: "",
      };
    },
    createdTeamTicket() {
      this.drawer = true;
    },
    getTeamPerformTicketPageList() {
      service.getTeamPerformTicketPageList(
        {
          performId: this.form.performId,
          stockId: "",
          ticketName: "",
          pageNum: 1,
          pageSize: 100,
        },
        (res) => {
          this.teamTicketList = res.pageInfo.list.map((ticket) => {
            return ticket;
          });
        }
      );
    },
    handleClose(done) {
      this.drawer = false;
    },
    cancelForm() {
      // this.$refs.form.resetFields();
      this.drawer = false;
    },
    getStockList() {
      service.getStockList(this.performId, (res) => {
        // this.performTickets = res.performTickets
        this.stockList = res.stockList;
        this.filterStocklist = this.stockList.map((item) => {
          return {
            ...item,
          };
        });
      });
    },
    getPerformList() {
      service.getPerformList(this.projectId, (res) => {
        this.performList = res.performList.map((item) => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      });
    },
    handleTeamPageChange(pageNum) {
      this.teamPageInfo.pageNum = pageNum;
      this.getTeamTicketSendRecordList();
    },
    getRightsList() {
      service.getRightsList({ performId: this.performId }, (res) => {
        console.log(res);
        this.rightsList2 = res.rightsList.reduce((map, type) => {
          map[type.id] = type.name;
          return map;
        });
      });
    },
    selectTableData() {
      console.log(this.salesDateRange, "salesDateRange");
      if (this.salesDateRange.length == 2) {
        this.startDate = moment(this.salesDateRange[0]).format("YYYY-MM-DD HH:mm:ss");
        this.endDate = moment(this.salesDateRange[1]).format("YYYY-MM-DD HH:mm:ss");
      }
      this.getTeamTicketSendRecordList();
    },
    getTeamTicketSendRecordList() {
      console.log(this.salesDateRange, "salesDateRange");
      let params = {
        performId: this.performId,
        stockId: this.stockId,
        startDate: this.startDate,
        keyword: this.keyword,
        endDate: this.endDate,
        pageNum: this.teamPageInfo.pageNum,
        pageSize: 10,
      };
      service.getTeamTicketSendRecordList(params, (res) => {
        this.teamTicketSendRecordList = res.teamTicketSendRecordList.list;
        this.teamPageInfo.total = res.teamTicketSendRecordList.total;
      });
    },
  },
  mounted() {
    this.projectId = this.$route.query.projectId;
    this.performId = this.$route.query.performId;
    this.getStockList();
    this.getPerformList();
    this.getTeamTicketSendRecordList();
    this.getRightsList();
    // 生命周期钩子
  },
};
</script>

<style lang="scss" scoped>
.team-ticket-container {
  .drawer-content {
    padding: 20px;
    width: 500px;
  }
  .box-card {
    margin-bottom: 20px;
  }

  .text.item p {
    margin: 5px 0;
  }

  .warning-message {
    color: #f56c6c;
    margin-top: 20px;
  }

  .panel-heading {
    background-color: #fafafa;
    height: 60px;
    font-size: 20px;
  }
  .search-area {
    padding: 20px;
    display: flex;
    gap: 20px;
  }
  .action-area {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
  }
  .table-area {
    padding: 20px;
  }
  .ticket-area-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr); // 两列等宽
    gap: 10px;
    width: 472px;
    height: 165px;
    background-color: #f8f8f8;
    padding: 10px;
    font-size: 14px;
    box-sizing: border-box;
    margin-bottom: 20px;
  }

  .ticket-item {
    display: flex;
    padding: 5px;
  }

  .ticket-label {
    color: #666;
  }

  .ticket-value {
    color: #333;
  }
  .text-red {
    color: #fc5f44;
  }
  .text-blue {
    color: #1fa2f5;
  }
  .demo-drawer__footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    gap: 40px;
  }
  .el-button {
    border-radius: 8px;
    width: auto;
    height: 36px;
    padding: 0 15px;
  }
}
</style>
