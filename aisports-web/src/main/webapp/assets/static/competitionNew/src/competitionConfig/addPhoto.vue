<template>
    <div class="title-box">
        <div class="sub-box-photo">
            <div class="operation-line">
                <div class="tips" style="color:#999;">上传的图片尺寸不限，大小不超过5M，支持jpg,png,bmp,gif</div>
                <file-uploader
                    :data="uploadData"
                    :on-success="handleUploadSuccess"
                    :on-error="handleError"
                    :multiple="true"
                    url="/upload/file"
                >
                    <button class="btn btn-md btn-default">上传图片</button>
                </file-uploader>
                <button class="btn btn-md btn-default" @click="deleteCampPictureBatchPre">批量删除</button>
            </div>
            <div class="img-box" v-for="(item, index) in pictureList">
                <img :src="item.path">
                <div class="iconfont icon-gou"
                     @click="toggleItem(item)"
                     :class="{'checked': item.checked}"></div>
                <div class="img-operation">
                    <div class="iconfont icon-fangdaiconx" @click="scanImgs(index)"></div>
                    <div class="iconfont icon-shanchu" @click="deleteCampPictureBatchItem(item.id)"></div>
                </div>
            </div>
        </div>
        <photo-swipe
                ref = "photoSwiper"
                :items = "scanImgList"
                :index = "scanIndex"
        ></photo-swipe>
    </div>
</template>

<script>
import FileUploader from 'vue-components/FileUploader.vue'
import 'utils/confirm'
import mAlert from 'mAlert'
import PhotoSwipe from 'libs/photoSwipe/PhotoSwipe.vue'
    
export default{
    components:{
        'file-uploader': FileUploader,
        PhotoSwipe,
    },
    data(){
        return{
            campId: '',
            uploadData: {},
            pageNum: 1,
            pageSize: 10,
            total: 0,
            pictureList: [],
            fileIds: '',
            scanImgList: [],
            scanIndex: 0,
        }
    },
    created(){
        if(this.$route.query.campId){
            this.campId = this.$route.query.campId || ''
            this.uploadData = {
                path: 'venueImage',
            }
            this.getCampPictureInit()
        }
    },
    methods:{
        scanImgs(index){
            this.scanIndex = index
            this.$nextTick(() => {
                this.$refs.photoSwiper.$emit('fireslide')
            })
        },
        handleUploadSuccess(data){
            console.log('img',data);
            this.manageCampPictureInit(data.url)
        },
        goprev(){
            window.history.back()
        },
        getCampPictureInit(){
            $.get('/publicCamp/getSponsorMediaResources', {
                campId: this.campId,
            }, (res) => {
                console.log(res);
                let pictureList = res || {}
                this.total = pictureList.total
                this.pictureList = pictureList.list || []
                this.scanImgList = []
                this.pictureList.forEach((item) => {
                    this.scanImgList.push({
                        src: item.path
                    })
                })
            })
        },
        manageCampPictureInit(url){
            $.post('/publicCamp/saveCampSponsorMediaResource', {
                imagePath: url,
                campId: this.campId,
            }, (res) => {
                console.log(res);
                this.getCampPictureInit();
            })
        },
        toggleItem(item){
            this.$set(item, 'checked', !item.checked)
        },
        deleteCampPictureBatchItem(id){
            this.fileIds = id
            this.deleteCampPictureBatch()
        },
        deleteCampPictureBatchPre(){
            let fileIds = []
            this.pictureList.forEach((item) => {
                if(item.checked){
                    fileIds.push(item.id)
                }
            })
            if(!fileIds.length){
                mAlert.showWarning('请选择要删除的图片')
                return
            }
            this.fileIds = fileIds.toString()
            this.deleteCampPictureBatch()
        },
        deleteCampPictureBatch(){
            this.$confirm('是否确认删除图片？').then(() => {
                $.post('/publicCamp/deleteSponsorMedia', {
                    resourceIds: this.fileIds
                }, () => {
                    mAlert.showSuccess('删除成功')
                    this.getCampPictureInit()
                })
            })
        },
        handleError(msg){
            mAlert.showWarning(msg.message)
        }
    }
}
</script>