<template>
    <div class="basic-info">
        <div class="form-row">
            <label class="form-label required">选择服务</label>
            <div class="form-field">
                <el-select
                    v-validate="'required'"
                    v-model="serviceId"
                    name="courseName"
                    placeholder="课程分类/课程名称"
                    size="medium"
                    filterable
                    @change="clearCourseInfo">
                    <el-option
                        v-for="item in serviceList"
                        :key="item.serviceId"
                        :label="item.serviceName"
                        :value="item.serviceId">
                    </el-option>
                </el-select>
            </div>
        </div>
        <div class="form-row">
            <label class="form-label required">所属课程</label>
            <div class="form-field">
                <el-select
                    v-validate="'required'"
                    v-model="courseId"
                    name="courseName"
                    placeholder="课程分类/课程名称"
                    size="medium"
                    filterable
                    multiple>
                    <el-option
                        v-for="item in courseList"
                        :key="item.courseId"
                        :label="item.courseName"
                        :value="item.courseId">
                    </el-option>
                </el-select>
            </div>
        </div>
        <div class="form-row">
            <label class="form-label required">期次名称</label>
            <div class="form-field">
                <input
                    v-validate="'required'"
                    v-model="termName"
                    name="termName"
                    type="text"
                    class="normal"
                    placeholder="请输入期次名称">
            </div>
        </div>
        <div class="form-row">
            <label class="form-label required">有效期</label>
            <div class="form-field">
                <el-date-picker
                    v-validate="'required'"
                    v-model="effectDate"
                    name="effectDate"
                    type="daterange"
                    format="yyyy/MM/dd"
                    size="medium"
                    range-separator="至"
                    placeholder="选择出生年月">
                </el-date-picker>
            </div>
        </div>
        <div class="form-row" v-if="this.$store.state.chooseLessonPlan">
            <label class="form-label required">上课计划</label>
            <div class="form-field">
                <div class="class-plan">
                    <div class="date-list">
                        <div class="date-info" v-for="(item,index) in classPlans"  :key="index">
                                <el-select
                                    v-validate="'required'"
                                    v-model="item.timeId"
                                    placeholder="请选择上课时段"
                                    filterable
                                    :disabled="!item.edit"
                                    clearable
                                    @change="changeInfo(item)" 
                                    >
                                    <el-option
                                        v-for="(tcItem) in tcTimeList"
                                        :key="tcItem.timeId"
                                        :label="tcItem.startTime + '-' + tcItem.endTime "
                                        :value="tcItem.timeId">
                                    </el-option>
                                </el-select>
                                <el-select
                                    v-model="item.weekday"
                                    placeholder="请选择"
                                    multiple
                                    collapse-tags
                                    :disabled="!item.edit"
                                    clearable 
                                    @change="getSelectDay(item)">
                                    <el-checkbox
                                        :style="selfstyle"
                                        v-model="item.checkedThing"
                                        @change="selectAllDay(item)">全选
                                    </el-checkbox>
                                    <el-option
                                        v-for="(week, i) in weekdays"
                                        :label="'周' + week"
                                        :value="i + 1"
                                        :key="i">
                                    </el-option>
                                </el-select>
                            <span class="iconfont icon-shanchu date-shanchu" :class="!item.edit?'disable-icon':''" @click="!item.edit?'':delClassPlan(item,index)"></span>
                        </div>
                    </div>
                    <div class="form-bottom" :class="classPlans.length == 0? 'not-border-top':''">
                        <span @click="addClassPlan">新增上课计划</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-row">
            <label class="form-label">报名级别</label>
            <div class="form-field">
                <el-select
                    v-model="levelId"
                    placeholder="请选择报名级别"
                    size="medium"
                    filterable>
                    <el-option
                        v-for="item in tcLevelList"
                        :key="item.levelId"
                        :label="item.levelName"
                        :value="item.levelId">
                    </el-option>
                </el-select>
            </div>
        </div>
        <div class="form-row">
            <label class="form-label">上架/下架</label>
            <div class="form-field form-field-switch">
                <el-switch
                    v-model="status">
                </el-switch>
            </div>
        </div>
        <div class="form-row-btn">
            <div class="form-field">
                <button
                    class="btn btn-primary"
                    @click="dealTermMsg">保存</button>
            </div>
        </div>
    </div>
</template>

<script>
import * as API from './API'
import { Select, Option, DatePicker, Switch, TimePicker, Checkbox } from 'element-ui'
import moment from 'moment'
import { vValidate } from 'directives/vValidate'
import { map } from 'lodash'
import mAlert from 'mAlert'


export default{
    components: {
        'el-select': Select,
        'el-option': Option,
        'el-date-picker': DatePicker,
        'el-switch': Switch,
        'el-time-picker': TimePicker,
        'el-checkbox': Checkbox
    },
    mixins: [vValidate],
    data() {
        return {
            venueId: '',
            courseList: [],
            courseId: [],
            termName: '',
            effectDate: [],
            tcLevelList: [],
            levelId: '',
            status: true,
            startDate: '',
            endDate: '',
            termId: '',
            serviceId: '',
            serviceList: [],
            classPlans: [],  // '上课计划'
            timesList:[],
            selfstyle: {
                textAlign: 'right',
                width: '100%',
                paddingRight: '10px',
            },
            weekdays: ['一', '二', '三', '四', '五', '六', '日'],
            tcTimeList:[],
            delClassPlanList:[] // 记录删除的ID
        }
    },
    watch: {
        effectDate() {
            if (this.effectDate && this.effectDate.length) {
                this.startDate = moment(this.effectDate[0]).format('YYYY-MM-DD')
                this.endDate = moment(this.effectDate[1]).format('YYYY-MM-DD')
            } else {
                this.startDate = ''
                this.endDate = ''
            }
        },
        serviceId() {
            this.queryLevelList()
            this.queryCourseList()
            this.queryTcTimeList(this.serviceId)
        },
    },
    mounted() {
        this.$store.dispatch('getClassInfo', {termName:this.termName,effectDate: this.effectDate});
        if (this.$route.query.venueId) {
            this.venueId = this.$route.query.venueId
        }
        this.queryServiceList()
        if (this.$route.query.courseId) {
            this.courseId = [+this.$route.query.courseId]
        }
        if (this.$route.query.serviceId) {
            this.serviceId = +this.$route.query.serviceId;
        }
        if (this.$route.query.termId) {
            this.termId = this.$route.query.termId
            this.queryTermMsg()
        }
        this.$store.dispatch('getServiceId', {serviceId:this.serviceId});
    },
    methods: {
        // 变化
        changeInfo(item) {
            if(item.termLessonPlanId) {
                item.state = '1'
            }
        },
        // 选取所有天数
        selectAllDay(item) {
            item.weekday = []
            if (item.checkedThing) {
                this.weekdays.map((day, index) => {
                    item.weekday.push(index + 1)
                })
            } else {
                item.weekday = []
            }
        },
        getSelectDay(item) {
            if(item.termLessonPlanId) {
                item.state = '1'
            }
            if (item.weekday.length === this.weekdays.length) {
                item.checkedThing = true
            } else {
                item.checkedThing = false
            }
        },
        // 删除上课计划
        delClassPlan(item,index) {
                this.classPlans.splice(index,1)
                this.delClassPlanList.push({
                    termLessonPlanId:item.termLessonPlanId,
                    state: '0'
                })
        },
        // 查询上课时间段
        queryTcTimeList(serviceId) {
            API.queryTcTimeList({
                serviceId,
                publicCourseTag:'1',
            },res=>{
                res.tcTimeList.forEach(item=>{
                    item.startTime = item.startTime.substr(0,2) + ':' + item.startTime.substr(2)
                    item.endTime = item.endTime.substr(0,2) + ':' + item.endTime.substr(2)
                })
                this.tcTimeList = res.tcTimeList
            })
        },
        // 新增上课计划
        addClassPlan() {
            this.classPlans.push({
                timeId: '',
                weekday: [],
                state: '1',
                edit: true,
                checkedThing: false,
            })
             this.$nextTick(()=>{
                var message = document.querySelector('.date-list');
                message.scrollTop = message.scrollHeight;
             })
        },
        queryServiceList() {
            API.queryServiceListByVenueId({
                venueId: this.venueId ? this.venueId : '',
            }, (res) => {
                this.serviceList = res.serviceList;
                this.$store.dispatch('getServiceList', {serviceList:this.serviceList});
            })
        },
        queryCourseList() {
            API.queryCourseList({
                venueId: this.venueId ? this.venueId : undefined,
                serviceId: this.serviceId,
            }, (res) => {
                this.courseList = res.courseList
            })
        },
        queryLevelList() {
            API.queryLevelList({
                serviceId: this.serviceId,
            }, (res) => {
                this.tcLevelList = res.tcLevelList
            })
        },
        clearCourseInfo() {
            this.levelId = ''
            this.courseId = [];
            this.$store.dispatch('getServiceId', {serviceId:this.serviceId})
        },
        dealTermMsg() {
            let lessonPlan = []
            lessonPlan = this.classPlans.map(item=>{
                let params = {}
                if(item.weekday) {
                    params.weekday = item.weekday.join(',')
                }
                if(item.termLessonPlanId) {
                    params.termLessonPlanId = item.termLessonPlanId
                }
                if(item.state) {
                    params.state = item.state
                }
                if(item.timeId) {
                    params.timeId = item.timeId
                }
                return params
            })
            lessonPlan.push(...this.delClassPlanList)
            this.$validator.validateAll().then((result) => {
                if (!result) {
                    return
                }
                API.dealTermMsg({
                    termId: this.termId ? this.termId : undefined,
                    courseIds: this.courseId.join(','),
                    termName: this.termName,
                    startDate: this.startDate,
                    endDate: this.endDate,
                    levelId: this.levelId,
                    status: this.status ? 1 : 0,
                    lessonPlan: JSON.stringify(lessonPlan)
                }, (res) => {
                    setTimeout(() => {
                        this.$router.replace({
                            name: 'term',
                            query: {
                                termId: res.termId,
                                venueId: this.venueId ? this.venueId : undefined,
                                showTermName: this.$route.query.showTermName
                            },
                        })
                        this.$emit('statechange')
                    }, 1500)
                this.$store.dispatch('getClassInfo', {termName:this.termName,effectDate: this.effectDate});
                })
            })
        },
        queryTermMsg() {
            // const lessonPlan = [...]
            API.queryTermMsg({
                termId: this.termId,
            }, (res) => {
                this.courseId = res.tcTerm.courseId
                this.effectDate = [moment(res.tcTerm.startDate).format('YYYY-MM-DD'), moment(res.tcTerm.endDate).format('YYYY-MM-DD')]
                this.termName = res.tcTerm.termName
                this.serviceId = res.tcTerm.serviceId;
                this.$store.dispatch('getServiceId', {serviceId:this.serviceId})
                this.courseId = map(res.tcTermCourseList, m => m.courseId)
                this.levelId = res.tcTerm.levelId
                if(this.$store.state.chooseLessonPlan) {
                    this.classPlans = res.termLessonPlans.map(item=>{
                    return {...item,weekday: item.weekday.split(',').map(wItem=>Number(wItem)),times: String(item.timeId),checkedThing:item.weekday.split(',').length==this.weekdays.length?true:false}
                })
                console.log(this.classPlans,'this.classPlans')
                }
                if (res.tcTerm.state === '1') {
                    this.status = true
                } else {
                    this.status = false
                }
                this.$store.dispatch('getClassInfo', {termName:this.termName,effectDate: this.effectDate});
            })
        },
    },
}
</script>
