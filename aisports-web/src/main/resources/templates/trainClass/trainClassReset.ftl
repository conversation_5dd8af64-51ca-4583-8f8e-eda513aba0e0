<#include "../public/macros.ftl">
<!DOCTYPE html>
<html>
<head>
    <@head_declares />
    <title>培训-课程调整</title>
    <#--<link rel="stylesheet" href="../static/trainClass/dist/trainClass-debug.css"/>-->
    <link rel="stylesheet" href="/assets/release/static/trainClass/trainClass.css"/>
    <@head_includes />
</head>
<body>
<div class="wrapper">
    <!--header-->
<#include "../public/header.ftl">
    <!--left menu-->
<#include "../public/aside.ftl">

    <!--main content-->
    <!--pd 和 margin-bottom后期可以删除-->
    <div class="main" ${bodyStyle!}>
        <div class="trainClass">
            <div class="top-info clearfix">
                <div class="breadcrumb-nav left">
                    <a href="#">工作台</a><span class="op">/</span><span>课程调整</span>
                </div>
            </div>
            <div class="title-box">
                <h1>课程调整</h1>
                <div class="sub-box" id="class-reset" style="display: block;">
                    <dl class="f-line">
                        <dt class="f-label">查询条件：</dt>
                        <dd class="f-field">
                            <div class="search-select-input" id="search-select-input-coach">
                                <select name="" id="search-input-coach" data-live-search="true">
                                    <option value="">请选择教练</option>
                                    <#if coachList??>
                                        <#list coachList as c>
                                            <option value="#{c.coachId}">${c.coachName}</option>
                                        </#list>
                                    </#if>
                                </select>
                            </div>

                            <div class="search-select-input" id="search-select-input-class">
                                <select name="" id="search-input-class" data-live-search="true">
                                    <option value="">请选择课程</option>
                                    <#if courseList??>
                                        <#list courseList as course>
                                            <option value="#{course.courseId}">${course.courseName}</option>
                                        </#list>
                                    </#if>
                                </select>
                            </div>

                            <button type="button" id="findButton" class="btn btn-md btn-56b854 s-btn">查询</button>
                        </dd>
                    </dl>
                    <dl class="f-line">
                        <dt class="f-label">&nbsp;</dt>
                        <dd class="f-field reset-classes-bg-field">
                            <div id="reset-classes-bg" class="reset-classes-bg">
                                <table class="table table-striped table-th-border reset-classes-table" id="class-list-table">
                                    <thead style="display:none;">
                                    <tr>
                                        <th width="50">&nbsp;</th>
                                        <th>班级名称</th>
                                        <th>教练</th>
                                        <th>上课时间</th>
                                    </tr>
                                    </thead>
                                    <tbody id="courseClassList">

                                    </tbody>
                                </table>
                            </div>
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/template" id="course-list-temp">
    <tr>
        <td><i class="iconfont icon-zhengque"></i></td>
        <td><%= className %></td>
        <td><%= coachName %></td>
        <td><%= trainingDate %></td>
        <td class=" class-id hidden"><%= classId %></td>
        <td class="course-id hidden"><%= courseId %></td>
    </tr>
</script>

<script type="text/template" id="showCourseClassInfoTemp">
    <tr class="operations" id="operations">
        <td colspan="4">
            <dl class="f-line">
                <dt class="f-label">上课时间：</dt>
                <dd class="f-field">
                    <div class="bootstrap-timepicker">
                        <input type="text" class="normal timepicker" id="startTime" placeholder="请选择开始时间" />
                    </div>
                    <span>-</span>
                    <div class="bootstrap-timepicker">
                        <input type="text" class="normal timepicker" id="endTime" placeholder="请选择结束时间" />
                    </div>
                </dd>
            </dl>
            <dl class="f-line">
                <dt class="f-label">教练：</dt>
                <dd class="f-field">
                    <select name="" id="coaches" class="select-picker show-menu-arrow">
                        <option value="0">请选择教练</option>
                    </select>
                </dd>
            </dl>
            <dl class="f-line">
                <dt class="f-label">上课日期：</dt>
                <dd class="f-field">
                    <div class="date-picker" id="class-dates">
                    </div>
                    <div  class="selected-days">
                        <h3>已选中上课天数</h3>
                        <p><em id="selected-days-num">22</em>天</p>
                    </div>
                </dd>
            </dl>
            <div class="operations-save" id="operations-save">
                <button class="btn btn-56b854 btn-lg" id="cancelUpdate">取消</button>
                <button class="btn btn-1fa2f5 btn-lg" id="submitUpdate">确认</button>
            </div>
        </td>
    </tr>
</script>


<script type="text/javascript">

    if(location.href.indexOf("?deve") != -1){
        seajs.use("/assets/static/trainClass/src/trainReset");
    }
    else{
        seajs.use("/assets/static/trainClass/src/trainReset");
    }
</script>
</body>
</html>