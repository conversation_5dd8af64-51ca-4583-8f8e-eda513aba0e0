package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class Training<PERSON>romComp extends Training<PERSON>romCompKey {
    private Integer elementNum;

    private Integer elementSum;

    private Integer remainNum;

    private Date createTime;

    private Date updateTime;

    private Long updateStaffId;

    public Integer getElementNum() {
        return elementNum;
    }

    public void setElementNum(Integer elementNum) {
        this.elementNum = elementNum;
    }

    public Integer getElementSum() {
        return elementSum;
    }

    public void setElementSum(Integer elementSum) {
        this.elementSum = elementSum;
    }

    public Integer getRemainNum() {
        return remainNum;
    }

    public void setRemainNum(Integer remainNum) {
        this.remainNum = remainNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", elementNum=").append(elementNum);
        sb.append(", elementSum=").append(elementSum);
        sb.append(", remainNum=").append(remainNum);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append("]");
        return sb.toString();
    }
}