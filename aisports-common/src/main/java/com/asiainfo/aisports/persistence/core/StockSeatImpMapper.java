package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.StockSeatImp;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface StockSeatImpMapper {
    @Delete({
            "delete from stock_seat_imp",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
            "insert into stock_seat_imp (id, batch_id, ",
            "stock_id, stock_name, ",
            "row, start_seat, ",
            "end_seat, invalid_seat, ",
            "num, dist_type, state, ",
            "create_time, create_staff_id, ",
            "update_time, update_staff_id)",
            "values (#{id,jdbcType=BIGINT}, #{batchId,jdbcType=BIGINT}, ",
            "#{stockId,jdbcType=BIGINT}, #{stockName,jdbcType=VARCHAR}, ",
            "#{row,jdbcType=INTEGER}, #{startSeat,jdbcType=INTEGER}, ",
            "#{endSeat,jdbcType=INTEGER}, #{invalidSeat,jdbcType=VARCHAR}, ",
            "#{num,jdbcType=INTEGER}, #{distType,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, ",
            "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
            "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(StockSeatImp record);

    @Select({
            "select",
            "id, batch_id, stock_id, stock_name, row, start_seat, end_seat, invalid_seat, ",
            "num, dist_type, state, create_time, create_staff_id, update_time, update_staff_id",
            "from stock_seat_imp",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="batch_id", property="batchId", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="row", property="row", jdbcType=JdbcType.INTEGER),
            @Result(column="start_seat", property="startSeat", jdbcType=JdbcType.INTEGER),
            @Result(column="end_seat", property="endSeat", jdbcType=JdbcType.INTEGER),
            @Result(column="invalid_seat", property="invalidSeat", jdbcType=JdbcType.VARCHAR),
            @Result(column="num", property="num", jdbcType=JdbcType.INTEGER),
            @Result(column="dist_type", property="distType", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    StockSeatImp selectByPrimaryKey(Long id);

    @Update({
            "update stock_seat_imp",
            "set batch_id = #{batchId,jdbcType=BIGINT},",
            "stock_id = #{stockId,jdbcType=BIGINT},",
            "stock_name = #{stockName,jdbcType=VARCHAR},",
            "row = #{row,jdbcType=INTEGER},",
            "start_seat = #{startSeat,jdbcType=INTEGER},",
            "end_seat = #{endSeat,jdbcType=INTEGER},",
            "invalid_seat = #{invalidSeat,jdbcType=VARCHAR},",
            "num = #{num,jdbcType=INTEGER},",
            "dist_type = #{distType,jdbcType=VARCHAR},",
            "state = #{state,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(StockSeatImp record);

    @Select({
            "<script>",
            "select",
            "id, batch_id, stock_id, stock_name, row, start_seat, end_seat, invalid_seat, num, dist_type, state, create_time, create_staff_id, update_time, update_staff_id",
            "from stock_seat_imp",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"batchId != null\">and batch_id = #{batchId,jdbcType=BIGINT}</if>",
            "<if test=\"stockId != null\">and stock_id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"stockName != null\">and stock_name = #{stockName,jdbcType=VARCHAR}</if>",
            "<if test=\"row != null\">and row = #{row,jdbcType=INTEGER}</if>",
            "<if test=\"startSeat != null\">and start_seat = #{startSeat,jdbcType=INTEGER}</if>",
            "<if test=\"endSeat != null\">and end_seat = #{endSeat,jdbcType=INTEGER}</if>",
            "<if test=\"invalidSeat != null\">and invalid_seat = #{invalidSeat,jdbcType=VARCHAR}</if>",
            "<if test=\"num != null\">and num = #{num,jdbcType=INTEGER}</if>",
            "<if test=\"distType != null\">and dist_type = #{distType,jdbcType=VARCHAR}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="batch_id", property="batchId", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="row", property="row", jdbcType=JdbcType.INTEGER),
            @Result(column="start_seat", property="startSeat", jdbcType=JdbcType.INTEGER),
            @Result(column="end_seat", property="endSeat", jdbcType=JdbcType.INTEGER),
            @Result(column="invalid_seat", property="invalidSeat", jdbcType=JdbcType.VARCHAR),
            @Result(column="num", property="num", jdbcType=JdbcType.INTEGER),
            @Result(column="dist_type", property="distType", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<StockSeatImp> selectByFields(StockSeatImp param);

    @Update({
            "<script>",
            "update stock_seat_imp",
            "<set>",
            "<if test=\"batchId != null\">batch_id = #{batchId,jdbcType=BIGINT},</if>",
            "<if test=\"stockId != null\">stock_id = #{stockId,jdbcType=BIGINT},</if>",
            "<if test=\"stockName != null\">stock_name = #{stockName,jdbcType=VARCHAR},</if>",
            "<if test=\"row != null\">row = #{row,jdbcType=INTEGER},</if>",
            "<if test=\"startSeat != null\">start_seat = #{startSeat,jdbcType=INTEGER},</if>",
            "<if test=\"endSeat != null\">end_seat = #{endSeat,jdbcType=INTEGER},</if>",
            "<if test=\"invalidSeat != null\">invalid_seat = #{invalidSeat,jdbcType=VARCHAR},</if>",
            "<if test=\"num != null\">num = #{num,jdbcType=INTEGER},</if>",
            "<if test=\"distType != null\">dist_type = #{distType,jdbcType=VARCHAR},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where id = #{id,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(StockSeatImp record);

    @Update({
            "update stock_seat_imp set state='0',updateTime=now(),updateStaffId=#{staffId} where stock_id = #{stockId} and state = '1'"
    })
    int invalidateByStockId(@Param("stockId") Long stockId, @Param("staffId") Long staffId);



    @Insert({
            "<script>",
            "insert into stock_seat_imp (id, batch_id, ",
            "stock_id, row, start_seat, ",
            "end_seat, invalid_seat, ",
            "num, dist_type, state, ",
            "create_time, create_staff_id, ",
            "update_time, update_staff_id)",
            "values",
            "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
            " (#{item.id,jdbcType=BIGINT}, #{item.batchId,jdbcType=BIGINT}, ",
            "#{item.stockId,jdbcType=BIGINT}, #{item.row,jdbcType=INTEGER}, #{item.startSeat,jdbcType=INTEGER}, ",
            "#{item.endSeat,jdbcType=INTEGER}, #{item.invalidSeat,jdbcType=VARCHAR}, ",
            "#{item.num,jdbcType=INTEGER}, #{item.distType,jdbcType=VARCHAR}, #{item.state,jdbcType=VARCHAR}, ",
            "#{item.createTime,jdbcType=TIMESTAMP}, #{item.createStaffId,jdbcType=BIGINT}, ",
            "#{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateStaffId,jdbcType=BIGINT})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(List<StockSeatImp> stockSeatImps);


    @Select({
            "<script>",
            "select",
            "id, batch_id, stock_id, row, start_seat, end_seat, invalid_seat, num, dist_type, state, create_time, create_staff_id, update_time, update_staff_id",
            "from stock_seat_imp",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"batchId != null\">and batch_id = #{batchId,jdbcType=BIGINT}</if>",
            "<if test=\"stockId != null\">and stock_id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"row != null\">and row = #{row,jdbcType=INTEGER}</if>",
            "<if test=\"startSeat != null\">and start_seat = #{startSeat,jdbcType=INTEGER}</if>",
            "<if test=\"endSeat != null\">and end_seat = #{endSeat,jdbcType=INTEGER}</if>",
            "<if test=\"invalidSeat != null\">and invalid_seat = #{invalidSeat,jdbcType=VARCHAR}</if>",
            "<if test=\"num != null\">and num = #{num,jdbcType=INTEGER}</if>",
            "<if test=\"distType != null\">and dist_type = #{distType,jdbcType=VARCHAR}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "batch_id", property = "batchId", jdbcType = JdbcType.BIGINT),
            @Result(column = "stock_id", property = "stockId", jdbcType = JdbcType.BIGINT),
            @Result(column = "row", property = "row", jdbcType = JdbcType.INTEGER),
            @Result(column = "start_seat", property = "startSeat", jdbcType = JdbcType.INTEGER),
            @Result(column = "end_seat", property = "endSeat", jdbcType = JdbcType.INTEGER),
            @Result(column = "invalid_seat", property = "invalidSeat", jdbcType = JdbcType.VARCHAR),
            @Result(column = "num", property = "num", jdbcType = JdbcType.INTEGER),
            @Result(column = "dist_type", property = "distType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<DataMap> selectByPageList(StockSeatImp param, RowBounds rowBounds);

}
