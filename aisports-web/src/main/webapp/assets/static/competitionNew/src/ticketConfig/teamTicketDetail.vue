<template>
  <div class="team-ticket-detail-container">
    <section class="header-area">
      <div class="panel-heading">
        <span>团体票发放详情</span>
        <span class="btn-return" title="后退" @click="goBack">
          <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
        </span>
      </div>
    </section>
    <section class="ticketinfo-area">
      <div class="ticket-info">
        <div class="ticket-item" v-for="teamTicket in ticketInfo" :key="teamTicket.value">
          <div class="ticket-label">{{ teamTicket.label + "：" }}</div>
          <div :class="['ticket-value', teamTicket.class]">{{ teamTicket.value }}</div>
        </div>
      </div>
    </section>
    <section class="search-area">
      <input v-model="keyword" class="normal" required placeholder="手机号/身份证/用户名" />
      <el-select v-model="state" placeholder="状态" clearable filterable>
        <el-option v-for="stock in stateList" :key="stock.value" :label="stock.lable" :value="stock.value"> </el-option>
      </el-select>

      <el-button icon="el-icon-search" type="primary" plain @click="selectTableData">查询</el-button>
    </section>
    <section class="action-area">
      <el-button icon="el-icon-download" @click="downloadTable">批量导出二维码</el-button>
    </section>
    <section class="table-area">
      <el-table :data="teamTicketList" header-cell-class-name="camp-table-header" style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="55"></el-table-column>
        <el-table-column align="center" prop="ticketNo" label="票号"></el-table-column>
        <el-table-column align="center" prop="name" label="姓名"></el-table-column>
        <el-table-column align="center" prop="mobileNum" label="手机号码"></el-table-column>
        <el-table-column align="center" prop="psptId" label="证件号码"></el-table-column>
        <el-table-column align="center" prop="ticketName" label="票名称"></el-table-column>
        <el-table-column align="center" prop="siteName" label="核销区域"></el-table-column>
        <el-table-column align="center" prop="createTime" label="购票时间"></el-table-column>
        <el-table-column align="center" prop="payTfee" label="费用(元)"></el-table-column>
        <el-table-column align="center" prop="createTime" label="核销时间"></el-table-column>
        <el-table-column align="center" prop="state" label="状态">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.state" :type="statusType[scope.row.state]">
              {{ statusMap[scope.row.state] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button @click="generateQRCode(scope.row)" type="text" size="small">二维码</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="page_footer">
        <el-pagination layout="prev, pager, next" background :total="pageInfo.total" @current-change="changePageInfo" :pageSize="pageInfo.pageSize"> </el-pagination>
      </div>
    </section>

    <section class="footer-area">
      <div class="modal-mask" v-show="qrCodeDialogVisible" @click.self="qrCodeDialogVisible = false">
        <div class="dialog-qrcode">
          <div class="dialog-header">
            <span>二维码</span>
            <span class="iconfont icon-cuowu1" @click="qrCodeDialogVisible = false"></span>
          </div>
          <div class="dialog-tickets-info">
            <div class="ticket-item" v-for="teamTicket in ticketInfo2" :key="teamTicket.value">
              <div class="ticket-label">{{ teamTicket.label + "：" }}</div>
              <div :class="['ticket-value', teamTicket.class]">{{ teamTicket.value }}</div>
            </div>
          </div>
          <div class="qrcode-container">
            <img :src="qrcodeImage" id="myQrCode" class="qrcode-img"/>
          </div>
          <div class="ticket-no">票号:{{ ticketNo }}</div>
          <div class="ticket-no">
            <el-button type="text" @click="downloadSignleQrcode"> 下载二维码 </el-button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import * as service from "./service";
import settings from "aisportsConfig";
import { down } from "inquirer/lib/utils/readline";
import QRCode from "qrcode";
import mAlert from "mAlert";

export default {
  name: "TeamTicketDetail",
  data() {
    return {
      // 数据模型定义
      recordId: "",
      rightsList2: [],
      ticketId: "",
      performId: "",
      ticketInfo: [],
      teamTicketList: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      qrcodeImage:'',
      ticketNo: "",
      qrCodeDialogVisible: false,
      ticketInfo2: [],
      multipleSelection: [],
      stateList: [
        { value: 1, lable: "已取消" },
        { value: 2, lable: "已支付" },
        { value: 3, lable: "已核销" },
        { value: 4, lable: "未绑定" },
        { value: 9, lable: "未支付" },
      ],
      keyword:'',
      state: "",
      statusMap: {
        1: "已取消",
        2: "已支付",
        3: "已核销",
        4: "未绑定",
        9: "未支付",
      },
      statusType: {
        1: "info",
        2: "",
        3: "success",
        4: "info",
        9: "danger",
      },
    };
  },
  methods: {
    m2front,
    // 方法定义
    goBack() {
      this.$router.go(-1);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log(val, "val");
    },
    changePageInfo(num) {
      this.pageInfo.pageNum = num;
      this.getTeamTicketList();
    },
    selectTableData(){
       this.pageInfo.pageNum = 1;
      this.getTeamTicketList();
    },
    downloadSignleQrcode() {
      settings.util.simulationForm("/projectManage/new/downloadQrcodes", { ticketIds: this.ticketId });
    },
    downloadTable() {
      // if (this.multipleSelection.length == 0) {
      //   mAlert.showWrong("请选择要导出的数据");
      //   return;
      // }
      let ticketIds = this.multipleSelection.map((item) => item.ticketId).join(",");
      let params = { 
        ticketIds: ticketIds,
        keyword:this.keyword,
        state:this.state,
        recordId: this.recordId,
       }
      settings.util.simulationForm("/projectManage/new/downloadQrcodes", params);
      // service.downloadQrcode({ticketIds: row.ticketId },(res)=>{
      //     console.log(res)
      // })
    },
    testError(num) {
      try {
        this.generateQRCode(num);
      } catch (error) {
        console.log(error);
      }
    },
    generateQRCode(row) {
      let number = row.ticketId;
      this.ticketNo = row.ticketNo;
      this.ticketId = row.ticketId;
      this.qrcodeImage = row.qrcode
      this.qrCodeDialogVisible = true;
      // const container = document.getElementById("myQrCode");
      // if (!container) {
      //   console.error("找不到二维码容器");
      //   return;
      // }

      // // 如果已有二维码实例，先清空
      // container.innerHTML = "";
      // console.log(number, "number");
      // let codeText = number.toString();
      // let configParams = {
      //   text: codeText,
      //   width: 120,
      //   height: 120,
      //   colorDark: "#000000",
      //   colorLight: "#ffffff",
      //   correctLevel: QRCode.CorrectLevel.H,
      // };
      // try {
      //   new QRCode(document.getElementById("myQrCode"), configParams);
      // } catch (error) {
      //   console.log(error);
      // }
      // 创建二维码

      //   this.qrCodeDialogVisible = true;
    },
    maskIdNumber(idNumber) {
      if (!idNumber) return "";
      const prefix = idNumber.slice(0, 6);
      const suffix = idNumber.slice(-4);
      return `${prefix}***********${suffix}`;
    },
    getTeamTicketList() {
      let params = {
        keyword:this.keyword,
        state:this.state,
        recordId: this.recordId,
        pageNum: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize,
      };
      service.getTeamTicketList(params, (res) => {
        let { tradeId, stockName, siteName, sendNum, rightsIds, price, performTicketName, performName, rightsList } = res.teamTicketSendRecord;
        let rightIdsName = "";
        if (rightsList.length) {
          rightIdsName = rightsList
            .map((item) => {
              return item.name;
            })
            .join("、");
        }
        price = this.m2front(price)
        this.pageInfo.total = res.teamTicketList.total;
        this.ticketInfo = [
          { class: "", value: tradeId, label: "业务流水号" },
          { class: "", value: performName, label: "场次" },
          { class: "", value: stockName, label: "票区" },
          { class: "", value: price, label: "票价" },
          { class: "", value: performTicketName, label: "票名称" },
          { class: "", value: siteName, label: "核销区域" },
          { class: "text-blue", value: rightIdsName, label: "权益" },
          { class: "", value: sendNum, label: "发放数量" },
        ];
        this.ticketInfo2 = [
          { class: "", value: performName, label: "场次" },
          { class: "", value: stockName, label: "票区" },
          { class: "", value: price, label: "票价" },
          { class: "", value: performTicketName, label: "票名称" },
          { class: "", value: siteName, label: "核销区域" },
          { class: "text-blue", value: rightIdsName, label: "权益" },
        ];
        this.teamTicketList = res.teamTicketList.list.map((item) => {
          if (item.person.length == 0) {
            item.payTfee = this.m2front(item.payTfee);
            return item;
          } else {
            item.person[0].psptId = this.maskIdNumber(item.person[0].psptId);
            item.payTfee = this.m2front(item.payTfee);
            return {
              ...item,
              ...item.person[0],
            };
          }
        });

        console.log(res);
      });
    },
  },
  mounted() {
    // 初始化逻辑
    this.recordId = this.$route.query.recordId;
    if (this.recordId) {
      this.getTeamTicketList();
    }
  },
};
</script>

<style lang="scss" scoped>
.team-ticket-detail-container {
  .header-area {
    .panel-heading {
      background-color: #fafafa;
      height: 60px;
      font-size: 20px;
    }
  }
  .ticketinfo-area {
    padding: 20px;
    width: 100%;
  }
  .ticket-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr); // 两列等宽
    gap: 10px;
    width: 100%;
    height: 122px;
    background-color: #f8f8f8;
    padding: 10px;
    font-size: 14px;
    box-sizing: border-box;
    margin-bottom: 20px;
    .ticket-item {
      display: flex;
      padding: 5px;
    }

    .ticket-label {
      // font-weight: bold;
      color: #666;
    }

    .ticket-value {
      color: #333;
    }
    .text-red {
      color: #fc5f44;
    }
    .text-blue {
      color: #1fa2f5;
    }
  }
  .table-area {
    padding: 20px;
  }
  .search-area{
    padding-left: 20px;
    display: flex;
    gap:20px;
  }
  .action-area {
    display: flex;
    justify-content: flex-end;
    padding-right: 20px;
  }
  .el-button {
    border-radius: 8px;
    width: auto;
    height: 36px;
    padding: 0 15px;
  }
  .qrcode-img {
    width: 120px;
    height: 120px;
  }
  .dialog-qrcode {
    width: 528px;
    height: 412px;
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  .modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
    .dialog-header {
      height: 62px;
      line-height: 62px;
      display: flex;
      font-size: 18px;
      color: #000;
      justify-content: space-between;
      align-self: center;
    }
    .dialog-qrcode {
      width: 528px;
      padding: 0 24px;
    }
    .qrcode-container {
      display: flex;
      justify-content: center;
    }
    .ticket-no {
      text-align: center;
      margin-top: 10px;
    }
    .dialog-tickets-info {
      display: grid;
      grid-template-columns: repeat(2, 1fr); // 两列等宽
      gap: 10px;
      width: 100%;
      height: 122px;
      background-color: #f8f8f8;
      padding: 10px;
      font-size: 14px;
      box-sizing: border-box;
      margin-bottom: 20px;
      .ticket-item {
        display: flex;
        padding: 5px;
      }

      .ticket-label {
        // font-weight: bold;
        color: #666;
      }

      .ticket-value {
        color: #333;
      }
      .text-red {
        color: #fc5f44;
      }
      .text-blue {
        color: #1fa2f5;
      }
    }
  }
}
</style>
