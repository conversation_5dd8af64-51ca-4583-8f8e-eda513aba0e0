<#include "../public/macros.ftl">
<!DOCTYPE html>
<html>
<head>
    <@head_declares />
    <title>批量业务状态查询</title>
    <!--<link rel="stylesheet" href="../static/review/dist/reviewDetails-debug.css"/>-->
    <link rel="stylesheet" href="/assets/release/static/batchServiceStatesQuery/batchServiceStatesQuery.css"/>
    <@head_includes />
</head>
<body>
<div class="wrapper">
    <!--header-->
<#include "../public/header.ftl">
    <!--left menu-->
<#include "../public/aside.ftl">
    <div class="main" ${bodyStyle!}>
        <div class="review">
            <div class="top-info clearfix">
                <div class="breadcrumb-nav left">
                    <span href="#">一级菜单</span><span class="op">/</span><span href="#">批量业务状态查询</span><span class="op">/</span>
                </div>
                <div class="title-box">
                    <div class="sub-box">
                        <form>
                            <fieldset>
                                <!-- 业务信息 -->
                                <div class="f-line f-line-top">
                                    <span class="bold">${tradeDtl.acceptDate!''}</span>
                                    <span>业务流水号：${tradeDtl.tradeId?c}</span>
                                    <span>提交人：${tradeDtl.tradeStaffName!''}</span>
                                </div>
                                <!-- 主要内容 -->
                                <div class="content">
                                <#if tradeDtl.tradeTypeCode == 18>
                                    <#include "tradeStates/tradeStates18.ftl">
                                </#if>
                                <#if tradeDtl.tradeTypeCode == 27>
                                    <#include "tradeStates/tradeStates27.ftl">
                                </#if>
                                <#if tradeDtl.tradeTypeCode == 37>
                                    <#include "tradeStates/tradeStates37.ftl">
                                </#if>
                                </div>
                            </fieldset>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    if(location.href.indexOf("?deve")!=-1){
        seajs.use("/assets/static/batchServiceStatesQuery/src/batchServiceStatesQuery");
    }
    else{
        seajs.use("/assets/static/batchServiceStatesQuery/src/batchServiceStatesQuery");
    }
</script>
</body>
</html>