<template>
    <div>
        <div class="left">
            <div class="f-line middle-top">
                <input
                    id="inputEcardNo"
                    ref="ipt"
                    v-model="cardno"
                    :readonly="notKeyboardInputCard"
                    type="text"
                    placeholder="请输入卡号"
                    class="normal md inputCardNo"
                    @keyup.enter="queryCardInfo" >
                <button
                    id="searchForCard"
                    class="btn btn-md btn-primary ticket-btn"
                    @click="queryCardInfo">查询</button>
            </div>
            <div class="f-line">
                <div class="card-info-box">
                    <div class="border-box ">
                        <div
                            v-if="!cardInfo"
                            class="border-content-placeholder">
                            <h3></h3>
                        </div>
                        <ul
                            v-else
                            class="list-with-bg query-res">
                            <template v-if="cardType==='card'">
                                <li class="full subtitle">
                                    <span>
                                        {{ cardInfo.productName }}
                                        <span
                                            :class="['card-type-'+['orange','purple','green'][cardInfo.limitType]]"
                                            class="card-type">
                                            {{ ['期','次','储'][cardInfo.limitType] }}
                                        </span>
                                    </span>
                                </li>
                                <li class="empty"></li>
                                <li>
                                    卡状态：
                                    {{
                                        ['正常', '冻结', '退专项卡', '转卡', '卡延期', '冻结审核', '返销'][cardInfo.depositStatus]
                                    }}
                                </li>
                                <li>
                                    <span>卡有效期：{{ cardInfo.startSDate.replace(/-/g,'/') }} - {{ cardInfo.endSDate.replace(/-/g,'/') }}</span>
                                </li>

                                <li v-if="cardInfo.limitType==='0'">
                                    <span>使用时限：
                                    已用<i class="fz24 red-text">{{
                                    cardInfo.activeTag === '0' ? 0 : cardInfo.totalDays - cardInfo.leftDays }}</i>天／
                                    余<i class="fz24 green-text">{{ cardInfo.leftDays }}</i>天
                                    </span>
                                </li>
                                <li v-if="cardInfo.limitType==='1'">
                                    <span>使用情况：
                                    已用<i class="fz24 red-text">{{ cardInfo.resCnt - cardInfo.balance }}</i>次／
                                    余<i class="fz24 green-text">{{ cardInfo.balance }}</i>次
                                    </span>
                                </li>
                                <li v-if="cardInfo.limitType==='2'">
                                    <span>使用情况：
                                    已用<i class="fz24 red-text">{{
                                        cardInfo.resCnt - cardInfo.balance > 0 ?
                                            ((cardInfo.resCnt - cardInfo.balance)/100).toFixed(2) :
                                    0 }}</i>元／
                                    余<i class="fz24 green-text">{{ (cardInfo.balance/100).toFixed(2) }}</i>元
                                    </span>
                                </li>
                                <li>
                                    <span>当日入馆：已用<i class="red-text fz24">{{ cardInfo.usedNum }}</i>次</span>
                                </li>
                                <li>
                                </li>
                                <li
                                    v-if="cardInfo.remark"
                                    class="full">
                                    <span>使用说明：{{ cardInfo.remark }}</span>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>

                <div
                    v-if="cardInfo"
                    class="card-operation-wrap">
                    <dl
                        v-if="cardInfo.limitType==='2'"
                        class="ticket-type-wrap">
                        <dt class="sub-title">选择票类型：</dt>
                        <dd class="ticket-type-list">
                            <div
                                v-for="ticketType in filterTicket(cardInfo.ticketTypePriceList)"
                                :class="{on: curTicket===ticketType}"
                                class="ticket-type"
                                @click="curTicket=ticketType">
                                <div class="ticket-type-name">{{ ticketType.ticketTypeName }}</div>
                                <div class="ticket-type-price">{{ ticketType.price/100 }}元/张</div>
                                <i class="iconfont icon-gou"></i>
                            </div>
                        </dd>
                    </dl>

                    <key-manager
                        ref="keyManager"
                        :need-key-number="cardInfo.needKeyNumber"
                        @enterhall="submit"
                        @amountchanged="updateKey"></key-manager>
                </div>

                <div
                    v-if="cardInfo"
                    class="card-submit-btn">
                    <span
                        v-if="cardInfo.limitType==='2'"
                        class="charge-fee">
                        本次应收：
                        <i class="red-text fz24">&yen;
                            {{ ((enterAmount||1)*(curTicket.price||0)/100).toFixed(2) }}
                        </i>
                    </span>

                    <button
                        id="card-submit-btn"
                        ref="submit"
                        type="button"
                        class="btn btn-lg btn-primary"
                        @click="submit">确认入馆</button>
                </div>
            </div>
        </div>

        <select-card
            ref="cardSelect"
            @selectcard="selectCard"></select-card>

        <confirm
            v-show="showPasswdBox"
            ref="passwdBox">
            <input
                ref="passwd"
                v-model="passwd"
                type="password"
                need-second
                class="activePSW normal"
                placeholder="请提醒用户输入密码"
                @keyupsubmit="savePasswd">
        </confirm>

        <div
            id="custInfo"
            class="right">
            <customer-info
                ref="cinfo"
                :cardno="cardno"></customer-info>
        </div>
    </div>
</template>

<script>
import CustomerInfo from './customerInfo.vue'
import { showSuccess, showWarning, showWrong } from 'mAlert'
import KeyManager from './keyManager.vue'
import SelectCard from './selectCard.vue'
import 'utils/confirm'
import { trySendMsg } from 'agreement'
import createEvent from 'utils/createEvent'
import settings, { post } from 'aisportsConfig'

import Confirm from 'vue-components/confirm.vue'
import showBeautifulConfirmBox from 'beautifulConfirmBox'
import printTicket from './printTicket'

export default {
    components: {
        CustomerInfo,
        KeyManager,
        SelectCard,
        Confirm,
    },
    data() {
        return {
            custInfo: {},
            cardno: '',

            cardInfo: false,
            cardType: '',
            notKeyboardInputCard: window.notKeyboardInputCard,
            showPasswdBox: false,
            requirePasswd: '',
            passwd: '',
            curTicket: '',
            enterAmount: 0,
            needKeyServiceIds: $('#needKeyServiceIds').val().split(',').map(id => +id),
        }
    },
    watch: {
        cardInfo() {
            this.$nextTick(() => {
                this.$refs.keyManager.focusInput()
            })
        },
    },
    methods: {
        queryCardInfo() {
            if (this.$refs.cardSelect.showSelectCard) return
            this.$refs.cinfo.getCustomerInfo()

            $.get('/enterHall/checkInputCard', {
                ecardNo: this.cardno,
            }, (res) => {
                this.custInfo = res.custInfo
                this.requirePasswd = res.requirePassword

                this.$refs.cardSelect.setEntries(res)
                $(this.$refs.ipt).blur()
            })
        },
        selectCard(cardInfo, cardType) {
            cardInfo.needKeyNumber = this.needKeyServiceIds.indexOf(cardInfo.serviceId) >= 0

            this.cardInfo = cardInfo
            this.cardType = cardType

            if (this.cardInfo.ticketTypePriceList) {
                this.curTicket =
						this.filterTicket(this.cardInfo.ticketTypePriceList)[0]
            }
        },
        filterTicket(ticketTypeList) {
            return ticketTypeList.filter(ticketType => ticketType.ticketKind !== '5' && ticketType.ticketTimeId !== null)
        },
		    resetInfo() {
		    	this.cardInfo = false
		    	this.cardno = ''
		    	this.curTicket = ''
		    	this.$refs.cinfo.reset()
		    	this.enterAmount = 0
		    },
		    addKey(keyInfo) {
            this.$refs.keyManager.addKey(keyInfo)
		    },
        getKeyInfo() {
            if (this.cardInfo.needKeyNumber) {
                if (this.$refs.keyManager.keys.length === 0) {
                    return false
                }

                return {
                    keyIds: this.$refs.keyManager.keys.map(key => key.keyId).join(','),
                    inputNum: this.$refs.keyManager.keys.length || 1,
                }
            }
            return {
                inputNum: this.enterAmount || 1,
            }

        },
        savePasswd() {
            createEvent($('.popPasswdBox').find('.confirm')[0], 'click')
        },
        updateKey(amt) {
            this.enterAmount = amt
        },
		    submit() {

            if (this.cardInfo.limitType === '2' && this.curTicket === '') {
                showWrong('请选择票种类')
                return
            }

            if (!this.getKeyInfo()) {
		    		showWrong('请刷入钥匙')
                return
		    	}

		    	if (this.requirePasswd.indexOf(this.cardInfo.limitType) !== -1) {
                this.promptPassWd()
            } else {
                this.submitInfo()
            }
		    },
		    printTicket,
		    promptPassWd() {
		    	trySendMsg('showPswBoard', '', false)

            this.$refs.passwdBox.show()
            this.$refs.passwdBox.title = '确认入馆'
            this.$refs.passwdBox.className = 'popPasswdBox'
            this.$refs.passwdBox.destroy = false
            this.passwd = ''

            this.$nextTick(() => {
                $(this.$refs.passwd).focus()
            })

            return this.$refs.passwdBox.deferred
                .then(this.submitInfo)
                .fail(this.resetSingleKeys)
		    },
		    resetSingleKeys() {
            if (this.$refs.keyManager.enterTimes === 'single') {
                this.$refs.keyManager.reset()
            }
		    },
		    promptReenterPasswd() {
            return this.$confirm({
                content: '密码输入错误',
                title: '确认入馆',
                confirmText: '重新输入',
                className: 'popPasswdError',
            })
                .then(this.promptPassWd)
                .fail(this.resetSingleKeys)
		    },
		    submitInfo() {
		    	if (this.cardInfo.activeTag === '0') {
		    		showBeautifulConfirmBox(() => {
	                    this.postInfo()
	                }, {
	                    content: '该卡尚未激活，是否需要激活并且使用？',
	                })
		    	} else {
		    		this.postInfo()
		    	}
		    },

		    postInfo() {
		    	const vm = this

		    	if (this.curTicket.promId) {
		    		if ((this.enterAmount + this.curTicket.dayAmount)
		    				> this.curTicket.dayRemain) {
		    			const buyInfoStr = `每天最多购买${this.curTicket.dayAmount}张${this.curTicket.ticketTypeName}`
		    			const promptStr = this.curTicket.dayRemain === this.curTicket.dayAmount ? '' : `，今天之前已经购买过${this.curTicket.dayAmount - this.curTicket.dayRemain}张`

                    showWrong(buyInfoStr + promptStr)
                    return
		    		}
		    	}

                // 本次入馆人数
                const num = this.enterAmount || 1
                if (this.cardInfo.limitType === '0') { // 期间卡
                    if (this.cardInfo.enterTimes - this.cardInfo.usedNum < 1) {
                        showWrong(`当天最多入馆${this.cardInfo.enterTimes}次, 今天更早时候已经入馆${this.cardInfo.usedNum}次`);
                        return;
                    }
                    if (this.cardInfo.personNum && num > this.cardInfo.personNum) {
                        showWrong(`每次最多可入馆${this.cardInfo.personNum}人`);
                        return;
                    }
                } else if (this.cardInfo.limitType === '1') { // 计次卡
                    if (this.cardInfo.enterTimes - this.cardInfo.usedNum < num) {
                        showWrong(`当天最多入馆${this.cardInfo.enterTimes}次, 今天更早时候已经入馆${this.cardInfo.usedNum}次`);
                        return;
                    }
                }

		    	const tickets = [{
		    		ticketTypeId: this.curTicket.ticketTypeId,
		    		ticketTimeId: this.curTicket.ticketTimeId,
		    		num,
		    	}]

		    	post('/enterHall/submitEcard', {
                ...this.getKeyInfo(),
                depositId: this.cardInfo.depositId,
                ecardNo: this.custInfo.ecardNo,
                custId: this.custInfo.custId,
                passWord: this.requirePasswd.indexOf(this.cardInfo.limitType) !== -1 ? this.passwd : undefined,

                serviceId: this.cardInfo.limitType === '2' ? this.curTicket.serviceId : this.cardInfo.serviceId,
                tickets: this.cardInfo.limitType === '2' ? JSON.stringify(tickets) : undefined,
            }, (data) => {
                if (data.error === 5008) {
                    vm.promptReenterPasswd()
                } else if (data.error === 2) {
                    settings.spinner.showErrorResult('输入卡号后请先执行查询，再提交入馆操作！')
                } else if (data.error !== 0 && data.startTime && data.endTime && data.noCancelFreeze !== '1') {
                    vm.$confirm(`该卡已被冻结，冻结时间为 ${
                        data.startTime} 至 ${data.endTime
                    }，是否需要解冻？`)
                        .then(() => {
                            const needData = {
                                eCardNo: vm.custInfo.ecardNo,
                                remark: '',
                                depositIds: `[${vm.cardInfo.depositId}]`,
                                cancelFreezeTag: 0,
                            }
                            $.ajax({
                                type: 'json',
                                method: 'post',
                                url: '/cancelFrozenCard/submitCancelFrozen',
                                data: needData,
                                success(data) {
                                    showSuccess(data.message)
                                    vm.postInfo()
                                },
                            })
                        })
                } else if (data.error !== 0) {
                    settings.spinner.showErrorResult(data.message);
                } else {
                    showSuccess('入馆成功')
                    vm.resetInfo()
                    vm.cardno = ''

                    vm.printTicket(data)
                    window.userListInst.updateUserList()
                }
            }, this.$refs.submit)
		    },
    },
}
</script>
