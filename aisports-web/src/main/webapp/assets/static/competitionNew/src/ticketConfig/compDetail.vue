<template>
  <div class="competition-detail">
    <!-- 顶部信息区 -->
    <div class="top-info clearfix">
      <div class="panel-heading">
        <span>赛事活动详情</span>
        <span class="btn-return" title="后退" @click="goBack">
          <i class="iconfont icon-zuojiantou"></i>
        </span>
      </div>
      <div class="info-content">
        <img :src="eventImage" alt="Event Image" class="event-image" />
        <div>
          <div class="event-details">
            <h3>{{ eventName }}</h3>
            <p style="margin-bottom: 5px">销售时间：{{ saleTime }}</p>
            <p>比赛时间：{{ matchTime }}</p>
          </div>
          <div class="action-buttons">
            <el-button icon="el-icon-edit" @click="editEvent">编辑赛事</el-button>
            <el-button icon="el-icon-share" @click="publishEvent" v-if="!isPublish">发布赛事</el-button>
            <el-button icon="el-icon-delete" @click="deleteEvent">删除赛事</el-button>
            <el-button icon="el-icon-user" @click="sendTeamTicket">发放团体票</el-button>
            <!-- <el-button icon="el-icon-delete" @click="manageSignup" >管理报名</el-button> -->
          </div>
        </div>
      </div>
    </div>
    <div class="main-content">
      <!-- 标签页导航 -->
      <el-tabs v-model="activeTab" @tab-click="tabClick">
        <el-tab-pane label="基本信息" name="basicInfo"></el-tab-pane>
        <el-tab-pane label="场次信息" name="matchInfo"></el-tab-pane>
        <el-tab-pane label="售票信息" name="ticketInfo"></el-tab-pane>
        <el-tab-pane label="渠道合作" name="channelCoop"></el-tab-pane>
      </el-tabs>
      <!-- 基本信息表单 -->
      <div id="basicInfo" class="basic-info-box" v-show="activeTab === 'basicInfo'">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>项目名称：</label>
              <span>{{ projectInfo.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>项目类型：</label>
              <span>{{ projectInfo.projectName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>实名认证：</label>
              <span>{{ projectInfo.realFlag ? "是" : "否" }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>比赛时间：</label>
              <span>{{ matchTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>销售时间：</label>
              <span>{{ saleTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label>销售优先级：</label>
              <span>{{ projectInfo.sort }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="info-item">
              <label>是否详情页弹框：</label>
              <span>{{ projectInfo.popFlag == "1" ? "是" : "否" }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="projectInfo.popFlag == '1'">
          <el-col :span="24">
            <div class="info-item info-item-long">
              <label>详情页弹窗：</label>
              <div v-html="projectInfo.popContent" class="bg-purple-light"></div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item info-item-long">
              <label>项目详情：</label>
              <div v-html="projectInfo.description" class="bg-purple-light"></div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item info-item-long">
              <label>购票须知：</label>
              <div v-html="projectInfo.projectBuyManual" class="bg-purple-light"></div>
            </div>
          </el-col>
        </el-row>
      </div>
      <!-- 场次信息表格 -->

      <div v-show="activeTab === 'matchInfo'" class="tab-content">
        <el-table :data="performList" header-cell-class-name="camp-table-header" style="width: 100%">
          <el-table-column type="expand">
            <template slot-scope="props">
              <el-table :data="props.row.performTickets" header-cell-class-name="camp-table-header" style="width: 100%">
                <el-table-column align="center" prop="performTicketName" label="票名称"></el-table-column>
                <el-table-column align="center" prop="price" label="票价(元)"></el-table-column>
                <el-table-column align="center" prop="stockName" label="票区名称"></el-table-column>
                <el-table-column align="center" prop="totalAmount" label="总票数(张)"></el-table-column>
                <el-table-column align="center" prop="remainAmount" label="剩余票数(张)"></el-table-column>
                <el-table-column align="center" prop="siteName" label="票核销区域"></el-table-column>
              </el-table>
              <!-- 票价信息表格 -->
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" prop="index" label="序号" width="50"></el-table-column> -->
          <el-table-column align="center" prop="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="name" label="场次信息"></el-table-column>
          <el-table-column align="center" prop="address" label="比赛地点"></el-table-column>
          <el-table-column align="center" prop="buyLimit" label="单次限购(张)"></el-table-column>
          <el-table-column align="center" prop="performTime" label="比赛时间"></el-table-column>
          <el-table-column align="center" prop="saleTime" label="销售日期"></el-table-column>
        </el-table>
      </div>

      <!-- 售票信息 -->
      <div class="ticket-info" v-show="activeTab == 'ticketInfo'">
        <!-- 顶部筛选区 -->
        <div class="filter-section">
          <div class="filter-group">
            <div type="primary" class="match-item active">全部场次</div>
            <div v-for="match in performList" :class="['match-item', match.id == activeMatch ? 'active' : '']" :key="match.id" @click="selectMatch(match)">{{ match.name }}</div>
          </div>
          <div class="filter-group">
            <div type="primary" class="area-item active" @click="selectArea('all')">全部区域</div>
            <div v-for="area in stockList" class="area-item" :class="['match-item', area.id == activeArea ? 'active' : '']" :key="area.id" @click="selectArea(area)">{{ area.name }}</div>
          </div>
          <div class="inventory-info">
            <div>
              <span class="info-lable">总库存</span> <span class="info-num">{{ ticketStatistics.totalNum || 0 }}</span>
            </div>
            <div>
              <span class="info-lable">已售出</span> <span class="info-num">{{ ticketStatistics.saleNum || 0 }}</span>
            </div>
            <div>
              <span class="info-lable">已核销</span> <span class="info-num">{{ ticketStatistics.verifiedOffNum || 0 }}</span>
            </div>
          </div>
        </div>
        <!-- 表格展示区 -->
        <el-table :data="rightsPageInfo.list" header-cell-class-name="camp-table-header" style="width: 100%">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="name" label="姓名"></el-table-column>
          <el-table-column align="center" prop="mobileNum" label="手机号"></el-table-column>
          <el-table-column align="center" prop="psptId" label="证件号"></el-table-column>
          <el-table-column align="center" prop="ticketName" label="票名称"></el-table-column>
          <el-table-column align="center" prop="siteName" label="核销区域"></el-table-column>
          <el-table-column align="center" prop="createTime" label="购票时间"></el-table-column>
          <el-table-column align="center" prop="payTfee" label="费用(元)"></el-table-column>
          <el-table-column align="center" prop="checkTicketTime" label="核销时间"></el-table-column>
          <el-table-column align="center" prop="status" label="状态">
            <template slot-scope="scope">
              <el-tag :type="statusType[scope.row.state]">
                {{ statusMap[scope.row.state] }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div class="page_footer">
          <el-pagination layout="prev, pager, next" background :total="stockPageInfo.total" @current-change="handleStockPageChange"> </el-pagination>
        </div>
      </div>
      <!-- 渠道合作 -->
      <div class="ticket-info" v-show="activeTab == 'channelCoop'">
        <!-- 顶部筛选区 -->
        <div class="filter-section">
          <div class="filter-group">
            <div type="primary" class="match-item active">全部场次</div>
            <div v-for="match in performList" :class="['match-item', match.id == activeMatch ? 'active' : '']" :key="match.id" @click="selectMatch(match)">{{ match.name }}</div>
          </div>
          <div class="filter-group">
            <div type="primary" :class="['area-item', 'all' == activeRight ? 'active' : '']" @click="selectRight('all')">全部合作</div>
            <div v-for="area in rightsList" class="area-item" :class="['match-item', area.id == activeRight ? 'active' : '']" :key="area.id" @click="selectRight(area)">{{ area.name }}</div>
          </div>
          <div class="inventory-info">
            <!-- <div>
              <span class="info-lable">总库存</span> <span class="info-num">{{ rightTicketStatistics.totalNum }}</span>
            </div> -->
            <div>
              <span class="info-lable">已售出</span> <span class="info-num">{{ rightTicketStatistics.totalNum }}</span>
            </div>
            <div>
              <span class="info-lable">已核销</span> <span class="info-num">{{ rightTicketStatistics.verifiedOffNum }}</span>
            </div>
          </div>
        </div>
        <!-- 表格展示区 -->
        <el-table :data="rightsPageInfo.list" header-cell-class-name="camp-table-header"  :span-method="objectSpanMethod" style="width: 100%">
          <el-table-column align="center" type="index" label="序号" width="50"></el-table-column>
          <el-table-column align="center" prop="name" label="姓名"></el-table-column>
          <el-table-column align="center" prop="mobileNum" label="手机号"></el-table-column>
          <el-table-column align="center" prop="psptId" label="证件号"></el-table-column>
          <el-table-column align="center" prop="ticketName" label="票名称"></el-table-column>
          <el-table-column align="center" prop="content" label="核销内容"></el-table-column>
          <el-table-column align="center" prop="createTime" label="购票时间"></el-table-column>
          <el-table-column align="center" prop="checkTicketTime" label="核销时间"></el-table-column>
          <el-table-column align="center" prop="status" label="状态">
            <template slot-scope="scope">
              <el-tag :type="statusType[scope.row.state]">
                {{ statusMap[scope.row.state] }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
        <div class="page_footer">
          <el-pagination layout="prev, pager, next" background :total="rightsPageInfo.total" @current-change="handleRightsPageChange"> </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as service from "./service";
import showConfirm from "helpers/showConfirm";
import mAlert from "mAlert";
export default {
  data() {
    return {
      projectInfo: {},
      projectId: "",
      eventName: "",
      eventImage: "http://xports-test.oss-cn-hangzhou.aliyuncs.com/dev/center/10000000/temp/b34c620d64f740709a183c8dca369115.jpg",
      saleTime: "",
      matchTime: "",
      activeTab: "basicInfo",
      activeMatch: "all",
      activeArea: "all",
      activeRight: "all",
      ticketTypes: {
        1: "普通票",
        2: "套票",
        3: "客场球迷票",
        4: "外地成人票",
        5: "外地套票",
        6: "儿童票",
        7: "外地儿童票",
      },
      statusMap: {
        1: "已取消",
        2: "已支付",
        3: "已核销",
        9: "未支付",
      },
      statusType:{
        1: "info",
        2: "",
        3: "success",
        9: "danger",
      },
      rightsPageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        totalPage: 0,
        list: [],
      },
      ticketStatistics: {
        saleNum: 0,
        totalNum: 0,
        verifiedOffNum: 0,
      },
      rightTicketStatistics: { verifiedOffNum: 0, totalNum: 0 },
      stockPageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        totalPage: 0,
        list: [],
      },
      rightsList: [],
      stockList: [],
      areaName: "",
      currentPage: 1,
      pageSize: 10,
      totalTicketCount: 100,
      matchList: [

      ],
      ticketList: [

      ],
      performList: [],
      matches: [],
      areas: [],
      totalInventory: 100,
      soldOut: 72,
      verified: 4,
      ticketSales: [
      ],
      spanlist:[]
    };
  },
  mounted() {
    this.projectId = this.$route.query.projectId;
    this.getList();
    this.getPerformList();
  },
  computed: {
    isPublish() {
      return this.projectInfo.status == "1";
    },
  },
  methods: {
    m2front,
    sendTeamTicket(){
      this.$router.push({
        path:'/teamTicket',
        query:{
          projectId:this.projectId,
          performId:this.performList[0].id
        }
      })
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }){
      if (columnIndex >= 4) {
        console.log(row.contactNum,row.needContact,row,'row');
          if (row.needContact ) {
            console.log('row.contactNum',row.contactNum);
            return {
              rowspan: row.contactNum,
              colspan: 1
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0
            };
          }
        }

    },
    getList() {
      service.getProjectInfo(this.projectId, (res) => {
        res.projectInfo.projectName = "";
        this.projectInfo = res.projectInfo;
        //  把字符串中的- 换为.
        this.projectInfo.saleStartDate = this.projectInfo.saleStartDate.replace(/-/g, ".");
        this.projectInfo.saleEndDate = this.projectInfo.saleEndDate.replace(/-/g, ".");
        this.projectInfo.performStartDate = this.projectInfo.performStartDate.replace(/-/g, ".");
        this.projectInfo.performEndDate = this.projectInfo.performEndDate.replace(/-/g, ".");
        this.saleTime = this.projectInfo.saleStartDate.slice(0, 16) + " - " + this.projectInfo.saleEndDate.slice(5, 16);
        this.matchTime = this.projectInfo.performStartDate.slice(0, 16) + " - " + this.projectInfo.performEndDate.slice(10, 16);
        this.eventName = this.projectInfo.name;
        this.eventImage = this.projectInfo.image;
        this.getProjectTypeList();
      });
    },
    getProjectTypeList() {
      service.getProjectTypeList((data) => {
        data.projectTypeList.forEach((item) => {
          if (this.projectInfo.projectTypeId == item.paramValue) {
            console.log("执行了");
            this.projectInfo.projectName = item.valueName;

            console.log(item.valueName);
          }
          console.log(this.projectInfo.projectName);
        });
      });
    },
    getStockList() {
      service.getStockList(this.performId, (res) => {
        // this.performTickets = res.performTickets
        this.stockList = res.stockList;
      });
    },

    getRightsList() {
      service.getRightsList({ performId: this.performId }, (res) => {
        this.rightsList = res.rightsList;
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    manageSignup() {
      // this.$router.push({
      //   name: "manageSignup",
      //   query: {
      //     performId: this.performId,
      //     performName: this.performName,
      //   },
      // });
    },
    tabClick(info) {
      console.log(info.name, "12345");
      if (info.name == "basicInfo") {
        this.activeTab = "basicInfo";
      } else if (info.name == "matchInfo") {
        this.activeTab = "matchInfo";
      } else if (info.name == "ticketInfo") {
        console.log("ticketInfo");
        this.activeTab = "ticketInfo";
        // this.getRightsList()
        this.selectMatch();
        // this.getStockList()
      } else if (info.name == "channelCoop") {
        console.log("ticketSales");
        this.selectMatch();
        this.activeTab = "channelCoop";
      }
    },
    // 选择场次
    selectMatch(match) {
      if (this.performList.length == 0) {
        return;
      }
      if (match) {
        this.matchId = match.id;
        this.performId = match.id;
        this.activeMatch = match.id;
      } else {
        this.matchId = this.performList[0].id;
        this.performId = this.matchId;
        this.activeMatch = this.matchId;
      }
      console.log(match);
      // 可以在这里添加根据选中场次更新数据的逻辑，例如请求对应的票务信息
      if (this.activeTab === "channelCoop") {
        this.getRightsList();
        this.getPerformTicketRights();
      } else {
        this.getStockList();
        this.getPerformTickets2();
      }
      console.log("Selected Match:", match);
    },
    // 选择区域
    selectArea(area) {
      if (area == "all") {
        this.activeArea = "all";
      } else {
        this.activeArea = area.id;
        this.areaName = area.name;
      }
      // 设置当前选中的区域ID
      // 可以在这里添加根据选中区域更新数据的逻辑
      console.log("Selected Area:", area);
      this.getPerformTickets2();
    },
    selectRight(right) {
      if (right == "all") {
        this.activeRight = "all";
      } else {
        this.activeRight = right.id;
      }
      this.getPerformTicketRights();
    },
    getPerformList() {
      service.getPerformList2(this.projectId, (res) => {
        this.performList = res.performList.map((perform, index) => {
          perform.performTime = this.getPerformTime(perform);
          perform.index = index + 1;
          perform.saleTime = `${perform.saleStartDate}~${perform.saleEndDate}`;
          perform.performTickets = perform.performTicketList.map((ticket) => {
            ticket.price = this.m2front(ticket.price);
            ticket.ticketTypeName = this.ticketTypes[ticket.ticketKind];
            return ticket;
          });
          return perform;
        });
      });
    },
    maskIdNumber(idNumber) {
      if (!idNumber) return "";
      const prefix = idNumber.slice(0, 6);
      const suffix = idNumber.slice(-4);
      return `${prefix}***********${suffix}`;
    },

    getPerformTickets2() {
      const params = {
        projectId: this.projectId,
        performId: this.activeMatch === "all" ? "" : this.activeMatch,
        stockId: this.activeArea === "all" ? "" : this.activeArea,
        pageNum: this.stockPageInfo.pageNum,
        pageSize: this.stockPageInfo.pageSize,
      };
      this.spanlist = [];
      service.getPerformTickets2(params, (res) => {
        console.log("票务数据:", res);
        // this.rightsPageInfo.list = res.performTickets.list
        this.stockPageInfo.total = res.performTickets.total;
        this.stockPageInfo.pageNum = res.performTickets.pageNum;
        this.stockPageInfo.pageSize = res.performTickets.pageSize;
        // this.stockPageInfo.total = res.stockTickets.total;
        this.rightsPageInfo.list = [];
        this.ticketStatistics.saleNum = res.ticketStatistics.saleNum;
        this.ticketStatistics.totalNum = res.ticketStatistics.totalNum;
        this.ticketStatistics.verifiedOffNum = res.ticketStatistics.verifiedOffNum;
        res.performTickets.list.forEach((ticket) => {
          ticket.payTfee = this.m2front(ticket.payTfee);
          ticket.person.forEach((person) => {
            person.psptId = this.maskIdNumber(person.psptId);
            this.rightsPageInfo.list.push({
              ...ticket,
              ...person,
            });
          });
        });
        console.log("this.rightsPageInfo.list", this.rightsPageInfo.list);
      });
    },
    // 加载票务权益数据
    getPerformTicketRights() {
      const params = {
        projectId: this.projectId,
        performId: this.activeMatch === "all" ? "" : this.activeMatch,
        rightId: this.activeRight === "all" ? "" : this.activeRight,
        pageNum: this.rightsPageInfo.pageNum,
        pageSize: this.rightsPageInfo.pageSize,
      };

      service.getPerformTicketRights(params, (res) => {
        console.log("票务权益数据:", res);
        // this.ticketSales = res.list || [];
        // this.rightsPageInfo.list = res.performTickets.list
        this.rightsPageInfo.total = res.performTicketRights.total;
        this.rightsPageInfo.pageNum = res.performTicketRights.pageNum;
        this.rightsPageInfo.pageSize = res.performTicketRights.pageSize;
        // this.stockPageInfo.total = res.stockTickets.total;
        this.rightsPageInfo.list = [];
        // this.rightTicketStatistics.saleNum = res.ticketStatistics.saleNum;
        this.rightTicketStatistics.totalNum = res.rightTicketStatistics.totalNum;
        this.rightTicketStatistics.verifiedOffNum = res.rightTicketStatistics.verifiedOffNum;
        res.performTicketRights.list.forEach((ticket) => {
          ticket.payTfee = this.m2front(ticket.payTfee);
          ticket.person.forEach((person,personindex) => {
            person.psptId = this.maskIdNumber(person.psptId);
              let contact = {
              needContact:false,
              contactNum:1,
            }
            if(personindex == 0){
              contact.needContact = true;
              contact.contactNum = ticket.person.length;
            }
            this.rightsPageInfo.list.push({
              ...ticket,
              ...person,
              ...contact
            });
          });
        });
      });
    },
    handleRightsPageChange(pageNum) {
      console.log("pageNum", pageNum);
      this.rightsPageInfo.pageNum = pageNum;
      this.getPerformTicketRights();
    },
    handleStockPageChange(pageNum) {
      this.stockPageInfo.pageNum = pageNum;
      this.getPerformTickets2();
    },
    getTickets(performId, index) {
      service.getPerformTickets(performId, (res) => {
        let performTickets = res.performTicketList.map((ticket) => {
          console.log(this.rightsList, " this.rightsList");
          ticket.price = ticket.price / 100;
          ticket.ticketTypeName = this.ticketTypes[ticket.ticketKind];
          return ticket;
        });
        this.performList[index].performTickets = performTickets;
      });
    },
    getPerformTime(perform) {
      return `${(perform.performDate || "").substr(0, 10)} ${this.addColumn(perform.startTime)} ~ ${this.addColumn(perform.endTime)}`;
    },
    addColumn(time) {
      return time ? `${time.substr(0, 2)}:${time.substr(2, 2)}` : "";
    },
    delComp() {
      showConfirm({
        title: "提示",
        content: "确定删除？",
        onConfirm: () => {
          service.delProject(this.projectId, () => {
            mAlert.showSuccess("删除成功！");
          });
        },
      });
    },
    editEvent() {
      console.log(this.$route.query, "Edit Event");
      //   vueRouter.go(
      //     {
      //         path: 'detail',
      //             query: {
      //                 projectId: this.$route.query.projectId,
      //             },
      //         }
      //   )
      this.$router.push({
        path: "comp",
        query: {
          projectId: this.$route.query.projectId,
        },
      });
    },
    publishEvent() {
      console.log("Publish Event");
      service.updateProjectReleaseStatus(
        {
          projectId: this.projectId,
          status: 1,
        },
        () => {
          mAlert.showSuccess("更新成功！");
          this.getList()
        }
      );
    },
    deleteEvent() {
      console.log("Delete Event");
      showConfirm({
        title: "提示",
        content: "确定删除？",
        onConfirm: () => {
          service.delProject(this.projectId, () => {
            mAlert.showSuccess("删除成功！");
            this.goBack();
          });
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.competition-detail {
  /* padding: 20px; */
  background-color: #f1f2f7;
}
.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.basic-info-box {
  padding: 20px;
}
.info-item-long {
  display: flex;
}
.info-item label {
  color: #666;
}
.bg-purple-light {
  background: #f8f8f8;
  border-radius: 8px;
  padding: 10px;
  flex: 1;
}
.main-content {
  border-radius: 8px;
  margin-top: 20px;
  padding: 20px 0;
  background-color: #fff;
}
.ticket-info {
  padding: 20px;
}

.top-info {
  margin-bottom: 20px;
  border-radius: 8px;
  background-color: #fff;
}

.event-image {
  width: 126px;
  height: 168px;
  margin-right: 20px;
}

.info-content {
  display: flex;
  padding: 20px;
  align-items: center;
}
.page_footer {
  padding: 20px;
  // display: flex;
  text-align: right;
}

.event-details {
  flex: 1;
}

.action-buttons {
  margin-top: 20px;
  text-align: left;
}
.header-competition {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.filter-section {
  margin-bottom: 20px;
}

.panel-heading {
  background-color: #fafafa;
  height: 60px;
}
.el-button {
  border-radius: 8px;
  width: auto;
  height: 36px;
  padding: 0 15px;
}
.tab-content {
  padding: 20px;
}
.filter-section {
  display: flex;
  flex-direction: column;
  gap: 12px; // 各个 filter group 之间的间距
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  .filter-group {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .match-item,
    .area-item {
      padding: 10px 15px;
      height: 36px;
      line-height: 16px;

      &:hover {
        // color: #66b1ff;
        cursor: pointer;
      }
    }

    > button {
      margin-right: 12px;
      font-size: 14px;
    }
    > div {
      margin-right: 12px;
      font-size: 14px;
      color: #333;
    }
    .active {
      background-color: #409eff;
      color: #fff;
      border-radius: 8px;
    }
  }

  .inventory-info {
    margin-top: 10px;
    display: flex;
    border-radius: 4px;
    width: 100%;
    height: 48px;
    align-items: center;

    background-color: #fafafa;
    padding: 8px 12px;
    border-radius: 6px;
    margin-top: 6px;
    .info-lable {
      color: #999999;
    }
    .info-num {
      font-weight: bold;
    }
    div {
      padding: 0 20px;
      border-right: 1px solid #eaeaea;
      text-align: center;
      &:last-child {
        border: none;
      }
    }
  }
}
</style>
