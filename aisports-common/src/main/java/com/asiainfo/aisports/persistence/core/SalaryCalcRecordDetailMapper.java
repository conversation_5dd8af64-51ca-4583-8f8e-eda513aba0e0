package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.SalaryCalcRecordDetail;
import com.asiainfo.aisports.domain.core.SalaryCalcRecordDetailKey;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface SalaryCalcRecordDetailMapper {
    @Delete({
            "delete from salary_calc_record_detail",
            "where salary_calc_record_id = #{salaryCalcRecordId,jdbcType=BIGINT}",
            "and staff_id = #{staffId,jdbcType=BIGINT}",
            "and salary_item_code = #{salaryItemCode,jdbcType=CHAR}"
    })
    int deleteByPrimaryKey(SalaryCalcRecordDetailKey key);

    @Insert({
            "insert into salary_calc_record_detail (salary_calc_record_id, staff_id, ",
            "salary_item_code, salary_item_value, ",
            "state, remark, create_time, ",
            "create_staff_id)",
            "values (#{salaryCalcRecordId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, ",
            "#{salaryItemCode,jdbcType=CHAR}, #{salaryItemValue,jdbcType=BIGINT}, ",
            "#{state,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
            "#{createStaffId,jdbcType=BIGINT})"
    })
    int insert(SalaryCalcRecordDetail record);

    @Select({
            "select",
            "salary_calc_record_id, staff_id, salary_item_code, salary_item_value, state, ",
            "remark, create_time, create_staff_id",
            "from salary_calc_record_detail",
            "where salary_calc_record_id = #{salaryCalcRecordId,jdbcType=BIGINT}",
            "and staff_id = #{staffId,jdbcType=BIGINT}",
            "and salary_item_code = #{salaryItemCode,jdbcType=CHAR}"
    })
    @Results({
            @Result(column="salary_calc_record_id", property="salaryCalcRecordId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="salary_item_code", property="salaryItemCode", jdbcType=JdbcType.CHAR, id=true),
            @Result(column="salary_item_value", property="salaryItemValue", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT)
    })
    SalaryCalcRecordDetail selectByPrimaryKey(SalaryCalcRecordDetailKey key);

    @Update({
            "update salary_calc_record_detail",
            "set salary_item_value = #{salaryItemValue,jdbcType=BIGINT},",
            "state = #{state,jdbcType=CHAR},",
            "remark = #{remark,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT}",
            "where salary_calc_record_id = #{salaryCalcRecordId,jdbcType=BIGINT}",
            "and staff_id = #{staffId,jdbcType=BIGINT}",
            "and salary_item_code = #{salaryItemCode,jdbcType=CHAR}"
    })
    int updateByPrimaryKey(SalaryCalcRecordDetail record);

    @Select({
            "<script>",
            "select",
            "salary_calc_record_id, staff_id, salary_item_code, salary_item_value, state, remark, create_time, create_staff_id",
            "from salary_calc_record_detail",
            "<where>",
            "<if test=\"salaryCalcRecordId != null\">and salary_calc_record_id = #{salaryCalcRecordId,jdbcType=BIGINT}</if>",
            "<if test=\"staffId != null\">and staff_id = #{staffId,jdbcType=BIGINT}</if>",
            "<if test=\"salaryItemCode != null\">and salary_item_code = #{salaryItemCode,jdbcType=CHAR}</if>",
            "<if test=\"salaryItemValue != null\">and salary_item_value = #{salaryItemValue,jdbcType=BIGINT}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
            "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="salary_calc_record_id", property="salaryCalcRecordId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="salary_item_code", property="salaryItemCode", jdbcType=JdbcType.CHAR, id=true),
            @Result(column="salary_item_value", property="salaryItemValue", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<SalaryCalcRecordDetail> selectByFields(SalaryCalcRecordDetail param);

    @Update({
            "<script>",
            "update salary_calc_record_detail",
            "<set>",
            "<if test=\"salaryItemValue != null\">salary_item_value = #{salaryItemValue,jdbcType=BIGINT},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
            "<if test=\"remark != null\">remark = #{remark,jdbcType=VARCHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where salary_calc_record_id = #{salaryCalcRecordId,jdbcType=BIGINT}",
            "and staff_id = #{staffId,jdbcType=BIGINT}",
            "and salary_item_code = #{salaryItemCode,jdbcType=CHAR}",
            "</script>"
    })
    int updateByPrimaryKeySelective(SalaryCalcRecordDetail record);

    @Insert({
            "<script>",
            "insert into salary_calc_record_detail (salary_calc_record_id, staff_id, salary_item_code, salary_item_value, state, remark, create_time, create_staff_id)",
            "values",
            "<foreach collection=\"list\" item=\"item\" separator=\",\">",
            "(#{item.salaryCalcRecordId,jdbcType=BIGINT}, #{item.staffId,jdbcType=BIGINT},#{item.salaryItemCode,jdbcType=CHAR}, #{item.salaryItemValue,jdbcType=BIGINT}, #{item.state,jdbcType=CHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},#{item.createStaffId,jdbcType=BIGINT} )",
            "</foreach>",
            "</script>",
    })
    int batchInsert(List<SalaryCalcRecordDetail> list);

    @Select({
            "select",
            "a.staff_id, s.staff_name, IF(s.gender = '0', '男' , IF(s.gender = '1', '女', '-')) gender, a.salary_item_code, a.salary_item_value, a.state, a.remark, a.create_time, a.create_staff_id",
            "from salary_calc_record_detail a left join staff s on a.staff_id = s.staff_id",
            "where a.salary_calc_record_id = #{salaryRecordId, jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
            @Result(column="staff_name", property="staffName", jdbcType=JdbcType.VARCHAR),
            @Result(column="gender", property="gender", jdbcType=JdbcType.VARCHAR),
            @Result(column="salary_item_code", property="salaryItemCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="salary_item_value", property="salaryItemValue", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> calcDetail(@Param("salaryRecordId") Long salaryRecordId);
}