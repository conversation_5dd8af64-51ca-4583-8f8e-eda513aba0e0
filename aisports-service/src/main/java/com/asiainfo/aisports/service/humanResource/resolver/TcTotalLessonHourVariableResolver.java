package com.asiainfo.aisports.service.humanResource.resolver;

import com.asiainfo.aisports.persistence.core.TcLessonAttnMapper;
import com.asiainfo.aisports.service.humanResource.Variable;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

/**
 * @auther: zhouwei
 * @date: 2022/3/14 18:19
 * @see Variable#tcTotalLessonHour
 */
@Component
public class TcTotalLessonHourVariableResolver extends LessonNumVariableResolver {

    @Autowired
    TcLessonAttnMapper tcLessonAttnMapper;

    @Override
    public Object resolve(Map<String, Object> vars) {
        checkVars(vars);
        Long staffId = MapUtils.getLong(vars, "staffId");
        Date salaryStartDate = (Date) MapUtils.getObject(vars, "salaryStartDate");
        Date salaryEndDate = (Date) MapUtils.getObject(vars, "salaryEndDate");
        Long centerId = MapUtils.getLong(vars, "centerId");


        Long cTotalLessonHour;
        if (vars.containsKey(Variable.tcTotalLessonHour.toString())) {
            cTotalLessonHour = (Long) vars.get(Variable.tcTotalLessonHour.toString());
        } else {
            cTotalLessonHour = tcLessonAttnMapper.cTotalLessonHour(staffId, salaryStartDate, salaryEndDate, centerId);
            vars.put(Variable.tcTotalLessonHour.toString(), cTotalLessonHour);
        }
        return cTotalLessonHour;
    }
}