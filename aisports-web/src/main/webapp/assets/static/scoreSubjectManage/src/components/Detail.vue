<template>
    <div class="title-box">
        <h1>
            <span>{{ id ? '编辑' : '新增' }}考核科目</span>
            <a
                href=""
                title="后退"
                class="btn-return"
                @click.prevent="goBack">
                <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
            </a>
        </h1>
        <div class="sub-box">
            <side-menu
                :tab-list = 'tabList'
                :on-selected = 'onTabSelected'
            ></side-menu>
            <div class="op-con">
                <router-view></router-view>
            </div>
        </div>
    </div>
</template>

<script>
import SideMenu from 'vue-components/SideMenu.vue'

export default{
    data() {
        return{
            tabList: [{
                tabText: '基本信息',
                name: 'basic',
            }],
            id: '',
            level: ''
        }
        
    },
    created(){
        this.setActive()
    },
    watch: {
        '$route': 'setActive'
    },
    components : {
        SideMenu
    },
    methods: {
        setActive(){
            if(this.$route.query.id){
                this.id = this.$route.query.id
            }
            if(this.$route.query.id){
                this.level = this.$route.query.level
                if(this.level == '2'){
                    this.tabList = [{
                        tabText: '基本信息',
                        name: 'basic',
                    },
                    {
                        tabText: '评分规则',
                        name: 'score',
                    }]
                }else{
                    this.tabList = [{
                        tabText: '基本信息',
                        name: 'basic',
                    }]
                }
            }
            this.tabList.forEach((item) => {
                if(this.id){
                    if(this.$route.name === item.name){
                        item.active = true
                    }
                }else{
                    this.tabList[0].active = true
                }
            })
            
        },
        onTabSelected(tab){
            this.tabList.forEach((item) => {
                item.active = false
            })
            tab.active = true
            this.$router.replace({
                name: tab.name,
                query: {
                    id: this.id,
                    level: this.level
                }
            })
        },
        goBack() {
            window.history.back()
        },
    },
}
</script>