package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.SalesMktCamp;
import com.asiainfo.aisports.domain.core.SalesMktCampStaff;
import com.asiainfo.aisports.domain.core.Staff;
import com.asiainfo.aisports.domain.core.Venue;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.param.StaticParamConfig;
import com.asiainfo.aisports.persistence.core.CmCustCollectMapper;
import com.asiainfo.aisports.persistence.core.SalesMktCampExecMapper;
import com.asiainfo.aisports.persistence.core.SalesMktCampMapper;
import com.asiainfo.aisports.persistence.core.SalesMktCampStaffMapper;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhuyu on 2017/11/10.
 */
@Service
public class MktCampManageService {
    @Autowired
    SalesMktCampMapper salesMktCampMapper;
    @Autowired
    SalesMktCampExecMapper salesMktCampExecMapper;
    @Autowired
    SalesMktCampStaffMapper salesMktCampStaffMapper;
    @Autowired
    SequenceWrapper sequenceWrapper;
    @Autowired
    CmCustCollectMapper cmCustCollectMapper;
    @Autowired
    private StaffResourceService staffResourceService;
    @Autowired
    private StaticParamConfig staticParamConfig;

    /**
     * 查询营销活动列表
     *
     * @param params
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> queryMktCampList(ParamMap params, Staff staff, int pageNum, int pageSize) {
        List<Long> venues = staffResourceService.findVenuesByStaff(staff).stream().map(Venue::getVenueId).collect(Collectors.toList());
        if (venues.isEmpty()) {
            return Lists.newArrayList();
        }
        params.set("venues", venues)
                .set("centerId", staff.getCenterId());
        List<DataMap> list = salesMktCampMapper.selectMktCampList(params, new RowBounds(pageNum, pageSize));
        list.forEach(p -> {
            p.set("campTypeName", staticParamConfig.getName(Constants.StaticParam.SALES_MKT_CAMP_TYPE, p.getString("campType")));
            p.set("stateName", staticParamConfig.getName(Constants.StaticParam.SALES_MKT_CAMP_STATE, p.getString("state")));
        });
        return list;
    }

    /**
     * 根据主键查询营销活动
     *
     * @param campId
     * @return
     */
    @Transactional(readOnly = true)
    public SalesMktCamp queryByCampId(Long campId) {
        return salesMktCampMapper.selectByPrimaryKey(campId);
    }

    /**
     * 根据campId查询活动信息
     *
     * @param campId
     * @return
     */
    @Transactional(readOnly = true)
    public Map queryCampInfoByCampId(Long campId) {
        return salesMktCampMapper.selectByCampId(campId);
    }

    /**
     * 根据campId查询有效的营销活动推广人员
     *
     * @param campId
     * @return
     */
    @Transactional(readOnly = true)
    public List<SalesMktCampStaff> queryCampStaffList(Long campId) {
        SalesMktCampStaff param = new SalesMktCampStaff();
        param.setCampaignId(campId);
        param.setState(Constants.Status.VALID);
        return salesMktCampStaffMapper.selectByFields(param);
    }

    /**
     * 新增或者修改营销活动
     *
     * @param staff
     * @param salesMktCamp
     * @param campStaffIds
     * @return
     */
    @Transactional
    public ServiceResult saveCamp(SalesMktCamp salesMktCamp, String campStaffIds, Staff staff) {
        Date now = new Date();
        if (salesMktCamp.getEndDate() != null) {
            salesMktCamp.setEndDate(DateCalUtil.endTime(salesMktCamp.getEndDate()));
        }

        if (salesMktCamp.getCampaignId() == null) {
            salesMktCamp.setCampaignId(sequenceWrapper.campaignSequence());
            if (salesMktCamp.getCreateStaffId() == null) {
                salesMktCamp.setCreateStaffId(staff.getStaffId());
            }
            salesMktCamp.setUpdateStaffId(staff.getStaffId());
            salesMktCamp.setCreateTime(now);
            salesMktCamp.setUpdateTime(now);
            salesMktCamp.setCenterId(staff.getCenterId());
            salesMktCamp.setState(Strings.isNullOrEmpty(salesMktCamp.getState()) ? "0" : salesMktCamp.getState());
            salesMktCampMapper.insert(salesMktCamp);
        } else {
            salesMktCamp.setUpdateStaffId(staff.getStaffId());
            salesMktCamp.setUpdateTime(now);
            salesMktCampMapper.updateByPrimaryKeySelective(salesMktCamp);
            salesMktCampStaffMapper.deleteByCampId(salesMktCamp.getCampaignId());
        }
        // 处理销售人员
        List<SalesMktCampStaff> addCampStaffList = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(campStaffIds)) {
            String[] campStaffIdArray = campStaffIds.split(",");
            for (String campStaffIdStr : campStaffIdArray) {
                SalesMktCampStaff tempStaff = new SalesMktCampStaff();
                tempStaff.setCampaignId(salesMktCamp.getCampaignId());
                tempStaff.setState(Constants.Status.VALID);
                tempStaff.setAttendStaffId(Long.parseLong(campStaffIdStr));
                tempStaff.setCreateStaffId(staff.getStaffId());
                tempStaff.setCreateTime(now);
                tempStaff.setUpdateStaffId(staff.getStaffId());
                tempStaff.setUpdateTime(now);
                addCampStaffList.add(tempStaff);
            }
        }
        if (!addCampStaffList.isEmpty()) {
            salesMktCampStaffMapper.batchInsert(addCampStaffList);
        }
        return new ServiceResult().set("salesMktCamp", salesMktCamp);
    }

    /**
     * 根据campId 查询执行日志
     *
     * @param campId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map> queryCampExec(Long campId, String execType, Date startDate, Date endDate) {
        Map params = Maps.newHashMap();
        params.put("campId", campId);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("execType", execType);
        return salesMktCampExecMapper.selectByCampId(params);
    }

    /**
     * 查询执行结果
     *
     * @param campId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map> queryCmCustInfoByCampId(Long campId, String collectType, Date startDate, Date endDate) {
        Map param = Maps.newHashMap();
        param.put("campId", campId);
        param.put("collectType", collectType);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        List<Map> list = cmCustCollectMapper.selectCmCustInfoByCampId(param);
        if (list.isEmpty()) {
            return list;
        }
        List<Map> resultList = Lists.newArrayList();
        Object tempSaleStaffId = list.get(0).get("saleStaffId");
        Integer totalNum = 0; // 总量
        Integer repeatNum = 0;// 重复数量
        Integer allotNum = 0; // 分配数量
        Integer followNum = 0;//跟进数量
        Integer completeNum = 0;
        String staffName = list.get(0).get("staffName") == null ? "" : MapUtils.getString(list.get(0), "staffName");

        for (Map map : list) {
            if ((tempSaleStaffId == null && map.get("saleStaffId") != null) || (tempSaleStaffId != null && !tempSaleStaffId.equals(map.get("saleStaffId")))) {
                Map result = Maps.newHashMap();
                result.put("totalNum", totalNum);
                result.put("repeatNum", repeatNum);
                result.put("allotNum", allotNum);
                result.put("followNum", followNum);
                result.put("completeNum", completeNum);
                result.put("staffName", staffName);
                result.put("saleStaffId", tempSaleStaffId);

                resultList.add(result);

                tempSaleStaffId = map.get("saleStaffId");
                staffName = map.get("staffName") == null ? "" : MapUtils.getString(map, "staffName");
                totalNum = 0; // 总量
                repeatNum = 0;// 重复数量
                allotNum = 0; // 分配数量
                followNum = 0;//跟进数量
                completeNum = 0;
            }
            totalNum += 1;
            if (Constants.Tag.NO.equals(map.get("state"))) {
                repeatNum += 1;
            }
            if (map.get("consultantId") != null) {
                allotNum += 1;
            }
            if (map.get("saleFollowupTime") != null) {
                followNum += 1;
            }
            if (Constants.SalesStatus.TO_JOIN.equals(map.get("saleStatus"))) {
                completeNum += 1;
            }
        }
        Map result = Maps.newHashMap();
        result.put("totalNum", totalNum);
        result.put("repeatNum", repeatNum);
        result.put("allotNum", allotNum);
        result.put("followNum", followNum);
        result.put("completeNum", completeNum);
        result.put("staffName", staffName);
        result.put("saleStaffId", tempSaleStaffId);
        resultList.add(result);
        return resultList;
    }

    /**
     * 修改营销活动
     *
     * @param param
     * @return
     */
    @Transactional
    public int updateMktCamp(SalesMktCamp param) {
        return salesMktCampMapper.updateByPrimaryKeySelective(param);
    }
}
