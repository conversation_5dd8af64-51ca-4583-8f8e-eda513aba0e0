package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class BmSiteAppoint {
    private Long id;

    private Long bmSiteId;

    private Long appointDateId;

    private Long appointTimeId;

    private Long appointUserId;

    private Date appointDate;

    private String state;

    private Long netUserId;

    private Date createTime;

    private Date updateTime;

    private String reportImage;

    private Date reportTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBmSiteId() {
        return bmSiteId;
    }

    public void setBmSiteId(Long bmSiteId) {
        this.bmSiteId = bmSiteId;
    }

    public Long getAppointDateId() {
        return appointDateId;
    }

    public void setAppointDateId(Long appointDateId) {
        this.appointDateId = appointDateId;
    }

    public Long getAppointTimeId() {
        return appointTimeId;
    }

    public void setAppointTimeId(Long appointTimeId) {
        this.appointTimeId = appointTimeId;
    }

    public Long getAppointUserId() {
        return appointUserId;
    }

    public void setAppointUserId(Long appointUserId) {
        this.appointUserId = appointUserId;
    }

    public Date getAppointDate() {
        return appointDate;
    }

    public void setAppointDate(Date appointDate) {
        this.appointDate = appointDate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public Long getNetUserId() {
        return netUserId;
    }

    public void setNetUserId(Long netUserId) {
        this.netUserId = netUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getReportImage() {
        return reportImage;
    }

    public void setReportImage(String reportImage) {
        this.reportImage = reportImage == null ? null : reportImage.trim();
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bmSiteId=").append(bmSiteId);
        sb.append(", appointDateId=").append(appointDateId);
        sb.append(", appointTimeId=").append(appointTimeId);
        sb.append(", appointUserId=").append(appointUserId);
        sb.append(", appointDate=").append(appointDate);
        sb.append(", state=").append(state);
        sb.append(", netUserId=").append(netUserId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", reportImage=").append(reportImage);
        sb.append(", reportTime=").append(reportTime);
        sb.append("]");
        return sb.toString();
    }
}