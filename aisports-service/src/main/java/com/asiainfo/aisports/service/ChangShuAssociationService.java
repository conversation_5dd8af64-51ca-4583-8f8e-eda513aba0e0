package com.asiainfo.aisports.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.ChangShuAssociation;
import com.asiainfo.aisports.domain.core.ChangShuAssociationInfo;
import com.asiainfo.aisports.domain.core.GradeBenefits;
import com.asiainfo.aisports.domain.core.NetUserCards;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.ChangShuAssociationMapper;
import com.asiainfo.aisports.persistence.core.GradeBenefitsMapper;
import com.asiainfo.aisports.persistence.core.NetUserCardsMapper;
import com.asiainfo.aisports.tools.JSONObjectUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2023年11月16日 16:13:54
 * @describe
 */
@Service
public class ChangShuAssociationService {


    @Autowired
    private ChangShuAssociationMapper changShuAssociationMapper;
    @Autowired
    private SequenceWrapper sequenceWrapper;
    @Autowired
    private NetUserCardsMapper netUserCardsMapper;
    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private GradeBenefitsMapper gradeBenefitsMapper;
    @Value("${oss.url}")
    String ossUrl;


    @Transactional
    public void insert(ChangShuAssociation changShuAssociation) {
        DateTime date = DateUtil.date();
        changShuAssociation.setId(sequenceWrapper.changShuAssociationIdSequence());
        changShuAssociation.setCreateTime(date);
        changShuAssociation.setUpdateTime(date);
        changShuAssociationMapper.insert(changShuAssociation);
    }

    @Transactional
    public void update(ChangShuAssociation changShuAssociation) {
        DateTime date = DateUtil.date();
        changShuAssociation.setUpdateTime(date);
        changShuAssociationMapper.updateByPrimaryKeySelective(changShuAssociation);
    }

    @Transactional
    public List<ChangShuAssociation> query(ChangShuAssociation changShuAssociation) {
        List<ChangShuAssociation> changShuAssociations = changShuAssociationMapper.selectByFields(changShuAssociation);
        return changShuAssociations;
    }

    @Transactional
    public List<ChangShuAssociationInfo> queryList(ChangShuAssociation changShuAssociation, RowBounds rowBounds) {
        List<ChangShuAssociationInfo> changShuAssociations = changShuAssociationMapper.queryList(changShuAssociation, rowBounds);

        // 添加服务器地址
        for (ChangShuAssociationInfo data : changShuAssociations) {
            StringBuilder allMaterial = new StringBuilder();
            String[] split = data.getMaterials().split(",");
            for (String path : split) {
                if (!data.getMaterials().startsWith("http")) {
                    allMaterial.append(ossUrl);
                }

                allMaterial.append(path).append(",");
            }
            if (allMaterial.toString().endsWith(",")){
                allMaterial = new StringBuilder(allMaterial.substring(0, allMaterial.length() - 1));
            }
            data.setMaterials(allMaterial.toString());
        }
        return changShuAssociations;
    }


    /**
     * 查询是否存在权益
     *
     * @param netUserId
     * @return
     */
    public boolean checkHasLevel(Long netUserId) {

        boolean result = false;

        // 查询主卡
        List<NetUserCards> netUserCardsList = netUserCardsMapper.selectUserCards(netUserId);

        if (netUserCardsList.isEmpty()) {
            return false;
        }
        // 取第一个卡
        NetUserCards netUserCards = netUserCardsList.get(0);
        if (netUserCards != null) {
            // 查询工会员工权益
            ChangShuAssociation query = new ChangShuAssociation();
            query.setEcardNo(netUserCards.getEcardNo());
            query.setCustId(Long.valueOf(netUserCards.getVenueCustId()));
            query.setState(Constants.ChangShuAssociationState.VALID);
            List<ChangShuAssociation> list = this.query(query);
            if (!list.isEmpty()) {
                String gradeId = list.get(0).getLevel();

                // 查询可以提前预定的天数
                GradeBenefits param = new GradeBenefits();
                param.setGradeId(gradeId);
                List<GradeBenefits> gradeBenefits = gradeBenefitsMapper.selectByFields(param);
                if (!gradeBenefits.isEmpty()) {
                    result = true;
                }
            }
        }
        return result;
    }


    /**
     * 查询是否存在权益
     *
     * @param netUserId
     * @return
     */
    public Integer getReserveNum(Long netUserId) {

        Integer result = 0;

        // 查询主卡
        List<NetUserCards> netUserCardsList = netUserCardsMapper.selectUserCards(netUserId);

        if (netUserCardsList.isEmpty()) {
            return result;
        }
        // 取第一个卡
        NetUserCards netUserCards = netUserCardsList.get(0);

        if (netUserCards != null) {
            // 查询工会员工权益
            ChangShuAssociation query = new ChangShuAssociation();
            query.setEcardNo(netUserCards.getEcardNo());
            query.setCustId(Long.valueOf(netUserCards.getVenueCustId()));
            query.setState(Constants.ChangShuAssociationState.VALID);
            List<ChangShuAssociation> list = this.query(query);
            if (!list.isEmpty()) {
                String gradeId = list.get(0).getLevel();

                // 查询可以提前预定的天数
                GradeBenefits param = new GradeBenefits();
                param.setGradeId(gradeId);
                List<GradeBenefits> gradeBenefits = gradeBenefitsMapper.selectByFields(param);
                if (!gradeBenefits.isEmpty()) {
                    result = JSONObjectUtils.parseObject(gradeBenefits.get(0).getContent()).getInt("advanceDay");
                }
            }
        }
        return result;
    }


}
