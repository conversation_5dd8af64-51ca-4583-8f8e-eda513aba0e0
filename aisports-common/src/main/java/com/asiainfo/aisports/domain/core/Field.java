package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class Field {
    private Long fieldId;

    private String fieldName;

    private Integer fieldNo;

    private Long centerId;

    private Long venueId;

    private Long serviceId;

    private Date startDate;

    private Date endDate;

    private String fieldLoc;

    private String remark;

    private Long fieldType;

    private String fullTag;

    private Long fullFieldId;

    private String liveDeviceId;

    private String status;

    private Date createTime;

    private Date updateTime;

    private Long updateStaffId;

    private Long priceItem;

    public Long getFieldId() {
        return fieldId;
    }

    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName == null ? null : fieldName.trim();
    }

    public Integer getFieldNo() {
        return fieldNo;
    }

    public void setFieldNo(Integer fieldNo) {
        this.fieldNo = fieldNo;
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public Long getVenueId() {
        return venueId;
    }

    public void setVenueId(Long venueId) {
        this.venueId = venueId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getFieldLoc() {
        return fieldLoc;
    }

    public void setFieldLoc(String fieldLoc) {
        this.fieldLoc = fieldLoc == null ? null : fieldLoc.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Long getFieldType() {
        return fieldType;
    }

    public void setFieldType(Long fieldType) {
        this.fieldType = fieldType;
    }

    public String getFullTag() {
        return fullTag;
    }

    public void setFullTag(String fullTag) {
        this.fullTag = fullTag == null ? null : fullTag.trim();
    }

    public Long getFullFieldId() {
        return fullFieldId;
    }

    public void setFullFieldId(Long fullFieldId) {
        this.fullFieldId = fullFieldId;
    }

    public String getLiveDeviceId() {
        return liveDeviceId;
    }

    public void setLiveDeviceId(String liveDeviceId) {
        this.liveDeviceId = liveDeviceId == null ? null : liveDeviceId.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    public void setPriceItem(Long priceItem) {
        this.priceItem = priceItem;
    }

    public Long getPriceItem() {
        return priceItem;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fieldId=").append(fieldId);
        sb.append(", fieldName=").append(fieldName);
        sb.append(", fieldNo=").append(fieldNo);
        sb.append(", centerId=").append(centerId);
        sb.append(", venueId=").append(venueId);
        sb.append(", serviceId=").append(serviceId);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", fieldLoc=").append(fieldLoc);
        sb.append(", remark=").append(remark);
        sb.append(", fieldType=").append(fieldType);
        sb.append(", fullTag=").append(fullTag);
        sb.append(", fullFieldId=").append(fullFieldId);
        sb.append(", liveDeviceId=").append(liveDeviceId);
        sb.append(", status=").append(status);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append(", priceItem=").append(priceItem);
        sb.append("]");
        return sb.toString();
    }
}