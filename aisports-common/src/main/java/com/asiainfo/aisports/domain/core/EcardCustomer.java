package com.asiainfo.aisports.domain.core;

public class EcardCustomer {
    private Long custId;

    private String custName;

    private String custState;

    private String psptTypeId;

    private String psptId;

    private String contactPhone;

    private String photo;

    private  String ecardNo;

    private  String status;

    private Integer balance;

    private Long ecardCustId;

    private Integer depositBalance;

    private String adAccount;
    //是否录入人脸 1-是 其他否
    private String faceState;

    private Long consultantId;

    private String consultantName;

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName == null ? null : custName.trim();
    }

    public String getCustState() {
        return custState;
    }

    public void setCustState(String custState) {
        this.custState = custState == null ? null : custState.trim();
    }

    public String getPsptTypeId() {
        return psptTypeId;
    }

    public void setPsptTypeId(String psptTypeId) {
        this.psptTypeId = psptTypeId == null ? null : psptTypeId.trim();
    }

    public String getPsptId() {
        return psptId;
    }

    public void setPsptId(String psptId) {
        this.psptId = psptId == null ? null : psptId.trim();
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone == null ? null : contactPhone.trim();
    }

    public String getEcardNo() {
        return ecardNo;
    }

    public void setEcardNo(String ecardNo) {
        this.ecardNo = ecardNo == null ? null : ecardNo.trim();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public Integer getBalance() {
        return balance;
    }

    public void setBalance(Integer balance) {
        this.balance = balance;
    }

    public Long getEcardCustId() {
        return ecardCustId;
    }

    public void setEcardCustId(Long ecardCustId) {
        this.ecardCustId = ecardCustId;
    }

    public Integer getDepositBalance() {
        return depositBalance;
    }

    public void setDepositBalance(Integer depositBalance) {
        this.depositBalance = depositBalance;
    }

    public String getAdAccount() {
        return adAccount;
    }

    public void setAdAccount(String adAccount) {
        this.adAccount = adAccount;
    }

    public String getFaceState() {
        return faceState;
    }

    public void setFaceState(String faceState) {
        this.faceState = faceState;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public Long getConsultantId() {
        return consultantId;
    }

    public void setConsultantId(Long consultantId) {
        this.consultantId = consultantId;
    }

    public String getConsultantName() {
        return consultantName;
    }

    public void setConsultantName(String consultantName) {
        this.consultantName = consultantName;
    }

    @Override
    public String toString() {
        return "EcardCustomer{" +
                "custId=" + custId +
                ", custName='" + custName + '\'' +
                ", custState='" + custState + '\'' +
                ", psptTypeId='" + psptTypeId + '\'' +
                ", psptId='" + psptId + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", photo='" + photo + '\'' +
                ", ecardNo='" + ecardNo + '\'' +
                ", status='" + status + '\'' +
                ", balance=" + balance +
                ", ecardCustId=" + ecardCustId +
                ", depositBalance=" + depositBalance +
                ", adAccount='" + adAccount + '\'' +
                ", faceState='" + faceState + '\'' +
                ", consultantId=" + consultantId +
                ", consultantName='" + consultantName + '\'' +
                '}';
    }
}