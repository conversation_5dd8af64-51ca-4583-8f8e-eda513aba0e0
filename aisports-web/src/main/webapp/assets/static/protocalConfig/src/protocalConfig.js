/**
 * Created by xuzeng on 2015/2/3.
 */
define((require) => {
    const $ = require('jquery');
    const settings = require('aisportsConfig');
    const umEditorStyle = require('umEditorStyle');

    require('frame');
    require('umEditorConfig');
    require('umEditor');

    let spinner = settings.spinner,
        ue = null;
    require('xValidate');
    const mAlertInstance = require('mAlert');

    let requiredParam = {};
    let readOnly = true;
    let agreementType = {};

    // init UE
    function initUE() {
        if (ue !== null) return;
        ue = UM.getEditor('container');
        ue.setOpt({
            iframeCssUrl: '',
            initialFrameWidth: '100%',
            initialFrameHeight: '325px',
            autoHeightEnabled: false,
        });
        ueReady();
    }

    // init ready
    function ueReady() {
        ue.addListener('ready', () => {
            ue.setDisabled(); // 编辑器家在完成后，让编辑器拿到焦点
            ue.setHeight('325');
            $('#protocal-edit').removeClass('opacity-hide').addClass('hide');
        });
    }

    // 点击场馆切换
    const handleVenueChange = function () {
        $(document).on('click', '.tags-pattern li', function () {
            $(this).addClass('selected').siblings().removeClass('selected');
            $(this).parent().siblings().filter('div')
                .addClass('hide');
            const venueId = $(this).attr('venueid');
            $(`div[data-venue='${venueId}']`).removeClass('hide');
            initPage();
        });
    };

    // 点击服务切换
    const handleServiceChange = function () {
        $(document).on('click', '.service-info li', function () {
            $(this).addClass('active').siblings().removeClass('active');
        });
    };

    function initPage() {
        agreementType = undefined;
        requiredParam = undefined;
        setAgreement(agreementType);
        setElemState(elemState.readOnly);
    }

    function addOptionEvent() {
        // 区分service的切换service
        $(document).on('change', 'select', function () {
            const $this = $(this);
            requiredParam = {};
            let serviceId;
            if ($this.val() != 'disabled') {
                serviceId = $this.val();
            } else {
                serviceId = '0';
            }
            const venueId = $this.parents('[data-venueid]').attr('data-venue');
            const tradeTypeCode = $this.parents('li').attr('tradeTypeCode');
            const level = $this.parents('li').attr('level');
            requiredParam.serviceId = serviceId;
            requiredParam.venueId = venueId;
            requiredParam.tradeTypeCode = tradeTypeCode;
            if (!tradeTypeCode || tradeTypeCode == '' || !serviceId || serviceId == '' || !venueId || venueId == '') {
                settings.spinner.showErrorResult('参数读取有误，请刷新界面后重试');
                return;
            }
            if (level == '1') {
                requiredParam.venueId = null;
                readOnly = true;
            } else {
                readOnly = false;
            }
            findAgreement();
        });
    }

    function addListEvent() {
        $(document).on('click', "ul[class='service-info']>li", function (e) {
            let $this = $(this),
                tempOptions = $this.find('select');
            if (tempOptions.length > 0) return;
            const tradeTypeCode = $this.attr('tradeTypeCode');
            const venueId = $this.parents('[data-venueid]').attr('data-venue');
            const level = $this.attr('level');
            requiredParam = {};
            requiredParam.serviceId = 0;
            requiredParam.venueId = venueId;
            requiredParam.tradeTypeCode = tradeTypeCode;
            if (level == '1') {
                requiredParam.venueId = null;
                readOnly = true;
            } else {
                readOnly = false;
            }
            findAgreement();
        });
    }

    function findAgreement() {
        const url = '/protocalConfig/findProtocal';
        $.post(url, requiredParam, (data) => {
            if (data.error == 1) {
                agreementType = undefined;
                settings.spinner.showErrorResult(data.message);
            } else if (data.error == 0) {
                agreementType = data.agreementType;
                if (readOnlyForEditElems()) {
                    setElemState(elemState.readOnly);
                } else {
                    setElemState(elemState.init);
                }

            } else if (data.error == 2) {
                agreementType = {};
                if (isCenter() || !readOnly) {
                    setElemState(elemState.add);
                } else {
                    setElemState(elemState.readOnly);
                }
            }
            setAgreement(agreementType);
        });
    }

    function setAgreement(aType) {
        let _agreementName = aType && aType.agreementName ? aType.agreementName : '',
            _agreementCont = aType && aType.agreementCont ? aType.agreementCont : '';
        // 赋值编辑框
        $('#protocalTitle').val(_agreementName);
        ue.setContent(_agreementCont, false);
        // 赋值展示
        $('#protocal-show h1').html(_agreementName);
        addContentToIframe('protocal-content', umEditorStyle.getStyle('all') + _agreementCont);
    }

    // 编辑组件的状态
    var elemState = {
        init: 'init',
        edit: 'edit',
        add: 'add',
        cancel: 'cancelEdit',
        readOnly: 'readOnly',
    };

    function setElemState(state) {
        switch (state) {
        case elemState.init:
            {
                $('#protocalTitle').attr('disabled', 'disabled');
                ue.setDisabled();
                hideElem('button[data-cancel]');
                hideElem('#protocal-show', false);
                hideElem('#protocal-edit');
                hideElem('button[data-edit]', false);
                hideElem('#submitBtn');
            }
            break;
        case elemState.edit:
            {
                $('#protocalTitle').removeAttr('disabled');
                ue.setEnabled();
                hideElem('button[data-cancel]', false);
                hideElem('#protocal-show');
                hideElem('#protocal-edit', false);
                hideElem('button[data-edit]', false);
                hideElem('#submitBtn', false);
            }
            break;
        case elemState.add:
            {
                $('#protocalTitle').removeAttr('disabled');
                ue.setEnabled();
                hideElem('button[data-cancel]');
                hideElem('#protocal-show');
                hideElem('#protocal-edit', false);
                hideElem('button[data-edit]', false);
                hideElem('#submitBtn', false);
            }
            break;
        case elemState.readOnly:
            {
                $('#protocalTitle').attr('disabled', 'disabled');
                ue.setDisabled();
                hideElem('button[data-cancel]');
                hideElem('#protocal-show', false);
                hideElem('#protocal-edit');
                hideElem('button[data-edit]');
                hideElem('#submitBtn');
            }
            break;
        default:
            break;

        }
    }

    function hideElem(el, hide) {
        hide == undefined || hide == true ? $(el).addClass('hide') : $(el).removeClass('hide');
    }

    function isCenter() {
        const isCenter = $('#isCenter').val();
        if (isCenter && isCenter == 'yes') {
            return true;
        }
        return false;
    }

    /**
     * 判断是否是只读权限
     * @returns {boolean}
     */
    function readOnlyForEditElems() {
        return readOnly && !isCenter();
    }

    function addEditEvent() {
        $(document).on('click', 'button[data-edit]', () => {
            initUE();
            if (!readOnlyForEditElems()) {
                setElemState(elemState.edit);
            }
        });
    }

    function addCancelEidtEvent() {
        $(document).on('click', 'button[data-cancel]', () => {
            setAgreement(agreementType);
            setElemState(elemState.init);
        });
    }

    function addSubmitEvent() {
        $(document).on('click', '#submitBtn', () => {
            if (!requiredParam || !requiredParam.tradeTypeCode) {
                settings.spinner.showErrorResult('请先选择一项业务');
                return;
            }
            if ($('div.protocal-title').validate()) {
                if (readOnlyForEditElems()) {
                    settings.spinner.showErrorResult('您只能读取本协议');
                    return;
                }
                agreementType.agreementName = $.trim($('#protocalTitle').val());
                if (!ue.hasContents()) {
                    settings.showErrorResult('协议内容不能为空')
                    return;
                }
                agreementType.agreementCont = ue.getContent();
                $.extend(requiredParam, agreementType);
                $.post('/protocalConfig/saveProtocal', {
                    agreementTypeStr: JSON.stringify(requiredParam),
                }, (data) => {
                    if (data.error == 1) {
                        settings.spinner.showErrorResult(data.message);
                    } else {
                        mAlertInstance.showSuccess(data.message);
                        setAgreement(agreementType);
                        readOnly = false;
                        setElemState(elemState.init);
                    }
                });
            }
        });
    }

    function addContentToIframe(iframe, html) {
        const doc = document.getElementById(iframe).contentWindow.document;
        doc.open();
        doc.write(html);
        doc.close();
    }

    $(() => {
        handleVenueChange();
        handleServiceChange();
        initUE();
        addOptionEvent();
        addListEvent();
        addEditEvent();
        addCancelEidtEvent();
        addSubmitEvent();
    });
});
