<template>
  <div
    v-loading="isLoading"
    :id="id"
    :style="{ width: width, height: height }"
  ></div>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader'

let map = null
export default {
  name: 'MapCom',
  props: {
    center: {
      type: Object,
      default: () => {
        return {
          lat: 38.492688,
          lng: 106.239933,
        }
      },
    },
    width: {
      type: String,
      default: '315',
    },
    height: {
      type: String,
      default: '150',
    },
    id: {
      type: String,
      default: 'map-container',
    },
  },
  data() {
    return {
      AMap: null,
      isLoading: true,
    }
  },
  computed: {
    locationInfo() {
      return this.$store.state.locationInfo
    },
  },
  watch: {
    'center': {
      handler(val) {
        this.center = val;
        this.initMap()
      },
      deep: true,
    },
  },

  mounted() {
    if (!this.noMapRender) {
      this.initMap()
    }
  },
  beforeDestroy() {
    map.destroy()
  },
  methods: {
    // 初始化地图
    async initMap() {
      const { lat, lng } = this.center
      const AMap = await AMapLoader.load({
        key: '899d510f3a3e283be18068424a1e9527', // 申请好的Web端开发者Key，首次调用 load 时必填
        // 899d510f3a3e283be18068424a1e9527
        // 15ad44fddcf2ad8421a8092fc3a10658
        version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [''], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
      })

      this.AMap = AMap
      map = new AMap.Map(this.id, {
        zoom: 15, // 初始化地图级别
        center: [lng, lat], // 初始化地图中心点位置
        resizeEnable: true, // 是否监控地图容器尺寸变化
      })
      map.on('complete', () => {
        this.isLoading = false
        this.addMarker()
      })
      map.on('click', (e) => {
        map.clearMap()
        this.addMarker(e.lnglat)
        this.$emit('change', e.lnglat)
      })
    },
    // 添加标记
    addMarker(latlng = this.center) {
      const { lat, lng } = latlng
      const marker = new this.AMap.Marker({
        position: [lng, lat],
      })
      marker.setMap(map)
    },
  },
}
</script>

<style lang="scss" scoped>
#map-container {
  // width: 315px;
  // height: 150px;
  margin: 0 auto;
  border-radius: 4px;
}
.action_icons {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 50%;
  right: 30px;
  transform: translate(0, -50%);
  z-index: 22222;
  .icon_item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 19px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.08) 8px 8px 8px;
    margin-bottom: 40px;
    img {
      width: 34px;
      height: 34px;
    }
  }
}
</style>
