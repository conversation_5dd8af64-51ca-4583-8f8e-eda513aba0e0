package com.asiainfo.aisports.api.controller.api;

import com.asiainfo.aisports.helper.Errors;
import com.asiainfo.aisports.api.web.WebResult;
import com.asiainfo.aisports.domain.core.GeneralProduct;
import com.asiainfo.aisports.service.api.GeneralProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by zhuyu on 2017/7/24.
 */
@RestController
@RequestMapping("/api/generalProduct")
public class GeneralProductController {
    @Autowired
    GeneralProductService generalProductService;

    /**
     * 查询开放的object信息
     *
     * @param id
     * @param objectType
     * @param channelId
     * @param venueId
     * @return
     */
    @RequestMapping("/queryOpenObject")
    public WebResult queryOpenObject(@RequestParam("id") String id,
                                     @RequestParam("objectType") String objectType,
                                     @RequestParam("channelId") Long channelId,
                                     @RequestParam("centerId") Long centerId,
                                     @RequestParam(value = "venueId", required = false) Long venueId) {
        GeneralProduct generalProduct = generalProductService.queryOpenObject(id, objectType, channelId, centerId, venueId);
        if (generalProduct == null) {
            return new WebResult(Errors.DATA_NOT_FOUND);
        }
        return new WebResult().set("generalProduct", generalProduct);
    }
}
