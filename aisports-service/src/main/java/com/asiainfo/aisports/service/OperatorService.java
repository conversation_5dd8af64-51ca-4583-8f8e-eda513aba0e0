package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.Operator;
import com.asiainfo.aisports.domain.core.OperatorBenefitItem;
import com.asiainfo.aisports.domain.core.OperatorFeeItem;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.persistence.core.OperatorBenefitItemMapper;
import com.asiainfo.aisports.persistence.core.OperatorFeeItemMapper;
import com.asiainfo.aisports.persistence.core.OperatorMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OperatorService {
    @Autowired
    private OperatorMapper operatorMapper;
    @Autowired
    private OperatorFeeItemMapper operatorFeeItemMapper;
    @Autowired
    private SequenceWrapper sequenceWrapper;
    @Autowired
    private OperatorBenefitItemMapper benefitItemMapper;


    /**
     * 获取运营商列表
     *
     * @param pageNum
     * @param pageSize
     * @param operator
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> getOperatorPageList(int pageNum, int pageSize, Operator operator) {
        List<DataMap> operatorList = operatorMapper.selectOperatorPageList(new RowBounds(pageNum, pageSize), operator);
        for (DataMap map : operatorList) {
            //业务费率信息
            OperatorFeeItem feeItem = new OperatorFeeItem();
            feeItem.setOperatorId(MapUtils.getLong(map, "operatorId"));
            List<DataMap> operatorFeeItemList = operatorFeeItemMapper.selectOperatorFeeItemList(feeItem);
            map.put("operatorFeeItemList", operatorFeeItemList);
        }
        return operatorList;
    }

    /**
     * 新增或者编辑运营商
     *
     * @param operatorId
     * @param operatorName
     * @param operatorContact
     * @param operatorPhone
     * @param chargePerson
     * @param chargePhone
     * @param venueId
     * @param incomeType    收入确认方式1-权责发生制 2-收付实现制
     * @param feeItemList
     * @param benefitItemList 分润周期信息
     * @param staff
     * @return
     */
    @Transactional
    public ServiceResult insertOrUpdateOperator(Long operatorId, String operatorName, String operatorContact, String operatorPhone, String chargePerson,
                                                String chargePhone, Long venueId, String incomeType, JSONArray feeItemList, JSONArray benefitItemList, LoginStaff staff) {
        ServiceResult serviceResult = new ServiceResult();
        // 校验：一个场馆只能绑定一个运营商benefit
        Operator checkParam = new Operator();
        checkParam.setVenueId(venueId);
        checkParam.setState(Constants.Status.VALID);
        List<Operator> operators = operatorMapper.selectByFields(checkParam);
        if (CollectionUtils.isNotEmpty(operators)) {
            if (Objects.isNull(operatorId)) {
                // 新增
                return new ServiceResult(1, "当前场馆已绑定运营商");
            } else {
                // 更新
                List<Long> operatorIds = operators.stream().map(Operator::getOperatorId).collect(Collectors.toList());
                if (!operatorIds.contains(operatorId)) {
                    return new ServiceResult(1, "当前场馆已绑定运营商");
                }
            }
        }

        // 数据落库——运营商
        Operator operator = new Operator();
        operator.setOperatorName(operatorName);
        operator.setOperatorContact(operatorContact);
        operator.setOperatorPhone(operatorPhone);
        operator.setChargePerson(chargePerson);
        operator.setChargePhone(chargePhone);
        operator.setVenueId(venueId);
        operator.setIncomeType(incomeType);
        // 2022年7月29日18:52:25 运动项目字段删除，暂时使用0做标记（库字段暂时保留）
        operator.setServiceId(0L);
        operator.setState(Constants.Status.VALID);
        operator.setUpdateTime(new Date());
        operator.setUpdateStaffId(staff.getStaffId());

        //保存运营商主信息
        int saveOrUpdateRows = 0;
        if (operatorId == null) {
            operator.setOperatorId(sequenceWrapper.operatorSequence());
            operator.setCreateTime(new Date());
            operator.setCreateStaffId(staff.getStaffId());
            saveOrUpdateRows = operatorMapper.insert(operator);
        } else {
            operator.setOperatorId(operatorId);
            saveOrUpdateRows = operatorMapper.updateByPrimaryKeySelective(operator);
        }
        if (saveOrUpdateRows <= 0) {
            return new ServiceResult(1, "新增或者编辑运营商失败");
        }

        //费率信息处理
        int feeItemSize = feeItemList.size();
        if (Objects.isNull(operatorId)) {
            // 新增
            // 2022年7月26日17:51:24 注销删除操作，新增时没有必要做删除操作
            //先删除旧的费率信息
//            operatorFeeItemMapper.deleteByOperatorId(operator.getOperatorId());

            //新增费率信息
            for (int i = 0; i < feeItemSize; i++) {
                JSONObject object = (JSONObject) feeItemList.get(i);
                OperatorFeeItem feeItem = new OperatorFeeItem();
                feeItem.setOperatorId(operator.getOperatorId());
                feeItem.setServerClass(object.getString("serverClass"));
                feeItem.setServerType(object.getString("serverType"));
                feeItem.setSettleRate(object.getString("settleRate"));
                // 2022年7月26日18:01:26 取消此处的周期字段，改为分润周期表数据
//                feeItem.setSettleCycle(object.getString("settleCycle"));
                feeItem.setState(Constants.Status.VALID);
                feeItem.setUpdateTime(new Date());
                feeItem.setUpdateStaffId(staff.getStaffId());
                feeItem.setCreateTime(new Date());
                feeItem.setCreateStaffId(staff.getStaffId());
                if (Strings.isNullOrEmpty(object.getString("settleRate"))) {
                    throw new ServiceException("业务费率或周期不能为空！");
                }
                operatorFeeItemMapper.insert(feeItem);
            }
        } else {
            //判断是否有重复
            for (int i = 0; i < feeItemSize; i++) {
                JSONObject object1 = (JSONObject) feeItemList.get(i);
                for (int j = i + 1; j < feeItemList.size(); j++) {
                    JSONObject object2 = (JSONObject) feeItemList.get(j);
                    if (object1.getString("serverClass").equals(object2.getString("serverClass")) &&
                            object1.getString("serverType").equals(object2.getString("serverType"))) {
                        throw new ServiceException("不可重复添加业务费率！");
                    }
                }
            }

            //原费率信息
            OperatorFeeItem param = new OperatorFeeItem();
            param.setOperatorId(operator.getOperatorId());
            List<OperatorFeeItem> operatorFeeItemList = operatorFeeItemMapper.selectByFields(param);
            if (feeItemSize > operatorFeeItemList.size()) {
                for (int i = 0; i < feeItemSize; i++) {
                    JSONObject object = (JSONObject) feeItemList.get(i);
                    OperatorFeeItem feeItem = new OperatorFeeItem();
                    feeItem.setOperatorId(operator.getOperatorId());
                    feeItem.setServerClass(object.getString("serverClass"));
                    feeItem.setServerType(object.getString("serverType"));

                    if (Strings.isNullOrEmpty(object.getString("settleRate"))) {
                        throw new ServiceException("业务费率或周期不能为空！");
                    }

                    OperatorFeeItem oldFeeItem = operatorFeeItemMapper.selectByPrimaryKey(feeItem);
                    if (oldFeeItem != null && (!oldFeeItem.getSettleRate().equals(object.getString("settleRate")))) {
                        feeItem.setSettleRate(object.getString("settleRate"));
//                        feeItem.setSettleCycle(object.getString("settleCycle"));
                        feeItem.setUpdateTime(new Date());
                        feeItem.setUpdateStaffId(staff.getStaffId());

                        operatorFeeItemMapper.updateByPrimaryKeySelective(feeItem);
                    }

                    if (oldFeeItem == null) {
                        feeItem.setSettleRate(object.getString("settleRate"));
//                        feeItem.setSettleCycle(object.getString("settleCycle"));
                        feeItem.setState(Constants.Status.VALID);
                        feeItem.setUpdateTime(new Date());
                        feeItem.setUpdateStaffId(staff.getStaffId());
                        feeItem.setCreateTime(new Date());
                        feeItem.setCreateStaffId(staff.getStaffId());

                        operatorFeeItemMapper.insert(feeItem);
                    }
                }
            } else if (feeItemSize < operatorFeeItemList.size()) {
                //查询原有的数据
                OperatorFeeItem operatorFeeItem = new OperatorFeeItem();
                operatorFeeItem.setOperatorId(operator.getOperatorId());
                List<OperatorFeeItem> operatorFeeItems = operatorFeeItemMapper.selectByFields(operatorFeeItem);
                List<OperatorFeeItem> classAndTypeList = Lists.newArrayList();
                //更新费率信息
                for (int i = 0; i < feeItemSize; i++) {
                    JSONObject object = (JSONObject) feeItemList.get(i);
                    OperatorFeeItem feeItem = new OperatorFeeItem();
                    feeItem.setOperatorId(operator.getOperatorId());
                    feeItem.setServerClass(object.getString("serverClass"));
                    feeItem.setServerType(object.getString("serverType"));
                    feeItem.setSettleRate(object.getString("settleRate"));
//                    feeItem.setSettleCycle(object.getString("settleCycle"));
                    feeItem.setUpdateTime(new Date());
                    feeItem.setUpdateStaffId(staff.getStaffId());

                    if (Strings.isNullOrEmpty(object.getString("settleRate"))) {
                        throw new ServiceException("业务费率或周期不能为空！");
                    }

                    OperatorFeeItem oldFeeItem = operatorFeeItemMapper.selectByPrimaryKey(feeItem);
                    if (oldFeeItem != null && (!oldFeeItem.getSettleRate().equals(object.getString("settleRate")))) {
                        operatorFeeItemMapper.updateByPrimaryKeySelective(feeItem);
                    }

                    classAndTypeList.add(feeItem);
                }

                //删除业务费率
                operatorFeeItems.addAll(classAndTypeList);
                // 分组统计
                Map<String, Long> countMap = operatorFeeItems.stream().collect(Collectors.groupingBy(o -> o.getServerClass() + "_" + o.getServerType(), Collectors.counting()));
                List<DataMap> countRecords = countMap.keySet().stream().map(key -> {
                    String[] temp = key.split("_");
                    String serverClass = temp[0];
                    String serverType = temp[1];

                    DataMap record = new DataMap();
                    record.set("serverClass", serverClass);
                    record.set("serverType", serverType);
                    record.set("count", countMap.get(key).intValue());
                    return record;
                }).collect(Collectors.toList());
                for (DataMap map : countRecords) {
                    if (map.getInteger("count") != 2) {
                        OperatorFeeItem feeItem = new OperatorFeeItem();
                        feeItem.setOperatorId(operator.getOperatorId());
                        feeItem.setServerClass(map.getString("serverClass"));
                        feeItem.setServerType(map.getString("serverType"));
                        operatorFeeItemMapper.deleteByPrimaryKey(feeItem);
                    }
                }
            } else if (feeItemSize == operatorFeeItemList.size()) {
                //更新费率信息
                for (int i = 0; i < feeItemSize; i++) {
                    JSONObject object = (JSONObject) feeItemList.get(i);
                    OperatorFeeItem feeItem = new OperatorFeeItem();
                    feeItem.setOperatorId(operator.getOperatorId());
                    feeItem.setServerClass(object.getString("serverClass"));
                    feeItem.setServerType(object.getString("serverType"));

                    if (Strings.isNullOrEmpty(object.getString("settleRate"))) {
                        throw new ServiceException("业务费率或周期不能为空！");
                    }

                    OperatorFeeItem oldFeeItem = operatorFeeItemMapper.selectByPrimaryKey(feeItem);
                    if (oldFeeItem != null && (!oldFeeItem.getSettleRate().equals(object.getString("settleRate")))) {
                        feeItem.setSettleRate(object.getString("settleRate"));
//                        feeItem.setSettleCycle(object.getString("settleCycle"));
                        feeItem.setUpdateTime(new Date());
                        feeItem.setUpdateStaffId(staff.getStaffId());

                        operatorFeeItemMapper.updateByPrimaryKeySelective(feeItem);
                    }
                }
            }
        }

        // 处理分润周期数据（ benefitItemList 不可为空）
        if (Objects.isNull(operatorId)) {
            // 批量新增
            batchSaveBenefitItems(benefitItemList, staff, operator);
        } else {
            // 更新
            // 1、获取已有的数据
            OperatorBenefitItem query = new OperatorBenefitItem();
            query.setOperatorId(operatorId);
            query.setState(Constants.Status.VALID);
            List<OperatorBenefitItem> preList = benefitItemMapper.selectByFields(query);
            if (CollectionUtils.isEmpty(preList)) {
                // 批量新增
                batchSaveBenefitItems(benefitItemList, staff, operator);
                return serviceResult.set("operatorId", operatorId);
            }

            // 存在原始数据，对比修改
            // 新增与更新
            for (Object object : benefitItemList) {
                JSONObject jsonObject = (JSONObject) object;
                if (Objects.isNull(jsonObject.get("benefitItemId"))) {
                    // 新增
                    saveBenefitItem(staff, operator, jsonObject);
                    continue;
                }
                // 更新数据
                for (OperatorBenefitItem benefitItem : preList) {
                    if (Objects.equals(benefitItem.getBenefitItemId(), jsonObject.getLong("benefitItemId"))) {
                        updateBenefitItem(benefitItem, staff, jsonObject, operator);
                        break;
                    }
                }
            }
            // 删除
             List<OperatorBenefitItem> deletedList = Lists.newArrayList();
            for (OperatorBenefitItem benefitItem : preList) {
                boolean isNeedDeleted = Boolean.TRUE;
                for (Object object : benefitItemList) {
                    JSONObject jsonObject = (JSONObject) object;
                    if (!Objects.isNull(jsonObject.get("benefitItemId")) &&
                            Objects.equals(benefitItem.getBenefitItemId(), jsonObject.getLong("benefitItemId"))) {
                        isNeedDeleted = Boolean.FALSE;
                        break;
                    }
                }
                if (isNeedDeleted) {
                    deletedList.add(benefitItem);
                }
            }
            if (CollectionUtils.isNotEmpty(deletedList)) {
                // 删除
                for (OperatorBenefitItem benefitItem : deletedList) {
                    logicDeleteBenefitItem(benefitItem, staff);
                }
            }
        }

        return serviceResult.set("operatorId", operatorId);
    }


    /**
     * 修改状态
     *
     * @param operatorId
     * @param state
     * @param staff
     * @return
     */
    @Transactional
    public ServiceResult updateOperatorState(Long operatorId, String state, LoginStaff staff) {
        ServiceResult serviceResult = new ServiceResult();
        Operator operator = new Operator();
        operator.setOperatorId(operatorId);
        operator.setState(state);
        operator.setUpdateTime(new Date());
        operator.setUpdateStaffId(staff.getStaffId());

        int num = operatorMapper.updateByPrimaryKeySelective(operator);
        if (num == 0) {
            return new ServiceResult(1, "修改状态失败");
        }

        return serviceResult;
    }

    /**
     * 获取全部已绑定了运营商的场馆id
     *
     * @return List<Long>
     */
    public List<Long> getAllOperatorVenueIds() {
        return operatorMapper.getAllOperatorVenueIds(Constants.Status.VALID);
    }


    /**
     * 根据id获取当前运营商场馆的分润信息
     *
     * @param operatorId 运营商id
     * @param venueId    场馆id
     * @return WebResult
     */
    public ServiceResult getBenefitItemList(Long operatorId, Long venueId) {
        ServiceResult serviceResult = new ServiceResult();
        List<OperatorBenefitItem> list = benefitItemMapper.getBenefitItemList(operatorId, venueId, Constants.Status.VALID);
        serviceResult.put("data", list);
        return serviceResult;
    }


    /**
     * 批量新增分润周期数据
     *
     * @param benefitItemList 分润周期数组
     * @param staff           登录用户
     * @param operator        场馆信息
     */
    private void batchSaveBenefitItems(JSONArray benefitItemList, LoginStaff staff, Operator operator) {
        for (int i = 0; i < benefitItemList.size(); i++) {
            JSONObject object = (JSONObject) benefitItemList.get(i);
            saveBenefitItem(staff, operator, object);
        }
    }


    /**
     * 新增分润数据
     *
     * @param staff    登录用户
     * @param operator 场馆信息
     * @param object   分润周期
     */
    private void saveBenefitItem(LoginStaff staff, Operator operator, JSONObject object) {
        OperatorBenefitItem benefitItem = generateBasicBenefitItem(staff);
        // 主键
        benefitItem.setBenefitItemId(sequenceWrapper.benefitItemSequence());
        benefitItem.setOperatorId(operator.getOperatorId());
        benefitItem.setVenueId(operator.getVenueId());
        benefitItem.setSettleRate(object.getString("settleRate"));
        benefitItem.setSettleCycle(object.getString("settleCycle"));
        benefitItemMapper.insert(benefitItem);
    }


    /**
     * 更新分润数据
     *
     * @param benefitItem 原分润周期
     * @param staff       登录用户信息
     * @param jsonObject  分润周期
     * @param operator    运营商信息
     */
    private void updateBenefitItem(OperatorBenefitItem benefitItem, LoginStaff staff, JSONObject jsonObject, Operator operator) {
        benefitItem.setSettleRate(jsonObject.getString("settleRate"));
        benefitItem.setSettleCycle(jsonObject.getString("settleCycle"));
        benefitItem.setVenueId(operator.getVenueId());
        benefitItem.setUpdateStaffId(staff.getStaffId());
        benefitItem.setUpdateTime(new Date());
        benefitItem.setVenueId(operator.getVenueId());
        benefitItemMapper.update(benefitItem);
    }


    /**
     * 逻辑删除分润数据
     *
     * @param benefitItem 分润数据
     * @param staff       员工信息
     */
    private void logicDeleteBenefitItem(OperatorBenefitItem benefitItem, LoginStaff staff) {
        benefitItem.setState(Constants.Status.INVALID);
        benefitItem.setUpdateTime(new Date());
        benefitItem.setUpdateStaffId(staff.getStaffId());
        benefitItemMapper.logicDeleteBenefitItem(benefitItem);
    }


    /**
     * 构建基础的 OperatorBenefitItem
     *
     * @param staff    登录用户信息
     * @return OperatorBenefitItem
     */
    private OperatorBenefitItem generateBasicBenefitItem(LoginStaff staff) {
        OperatorBenefitItem benefitItem = new OperatorBenefitItem();
        benefitItem.setState(Constants.Status.VALID);
        benefitItem.setUpdateTime(new Date());
        benefitItem.setUpdateStaffId(staff.getStaffId());
        benefitItem.setCreateTime(new Date());
        benefitItem.setCreateStaffId(staff.getStaffId());
        return benefitItem;
    }


}
