function intercept(res) {
    if (res.error !== 0) {
        throw new Error(res.message)
    }
    return res
}


export function queryInitInfo() {
    return $.get('/gameManage/queryInitInfo').then(intercept)
}

export function queryGameList(data) {
    return $.get('/gameManage/queryGameList', data).then(intercept)
}

export function queryGameDetails(gameId) {
    return $.get('/gameManage/queryGameDetails', {
        gameId,
    }).then(intercept)
}

export function cancelGame(data) {
    return $.post('/gameManage/cancelGame', data).then(intercept)
}
