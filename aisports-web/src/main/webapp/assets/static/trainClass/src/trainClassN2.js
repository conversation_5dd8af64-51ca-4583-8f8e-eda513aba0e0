// trainClassN2.js
define(function (require) {
    var $ = require('jquery'),
        ui = require('./ui'),
        _ = require('underscore'),
        moment = require('moment'),
        Backbone = require('backbone');

    var vent = require('./utils/vent');

    require('tabs');
    require('xValidate');
    require('qselect');

    var settings = require('aisportsConfig');
    var spinner = settings.spinner;

    var ClassListView = require('./views/classListView');
    var StudentListView = require('./views/studentListView');


    var SpecifySeparateView = require('./views/specifySeparateView');
    var QuickSeparateView = require('./views/quickSeparateView');

    var SeparatedClassListView = require('./views/separatedClassListView');

    // 创建班级列表， 班级成员， 被选中班级成员独立的view， 之间通过事件
    // 中心 'utils/vent' 进行通信
    var classList, studentList, separatedClassList, specifySeparateView, quickSeparateView;

    function initViews() {
        classList = new ClassListView();
        studentList = new StudentListView();

        specifySeparateView = new SpecifySeparateView();
        quickSeparateView = new QuickSeparateView();

        separatedClassList = new SeparatedClassListView();
    }

    //服务选择的样式
    $(document).on("click", '#myService li', function () {
        $(this).parent().find('li').removeClass('on');
        $(this).addClass('on');
    });

    /* 滑块事件初始化 */
    function switchInit() {
        // switch toggle
        $(document).on("click", '.switch-slider', function () {
            $(this).toggleClass('on').toggleClass('off');
            $(this).trigger("change");
        });
    }

    /* 快速分班和个性分班 tab切换 */
    function switchTabs() {
        var container = $("#class-content");
        $(document).on("change", "#class-switch", function (e) {
            var _this = $(this);
            var tabName = _this.hasClass("on") ? "quick" : "specify";

            if (tabName === 'quick') {
                quickSeparateView.render();
                specifySeparateView.unrender();
                vent.trigger('changeRenderType', 'quick');
            } else {
                specifySeparateView.render();
                quickSeparateView.unrender();
                vent.trigger('changeRenderType', 'specify');
            }
        });
    }


    // 搜索class
    function handleSearchClass() {
        $(document).on('input', '#q', function () {
            // classList.trigger('search');
            classList.searchItems(this.value);
        });
    }

    function handleSidePanelToogle() {
        $(document).on('click', '[data-click="toggle-side-panel"]', function () {
            $(this).closest('.class-seperated-box').toggleClass('closed');
        });

        $(document).on('click', function (e) {
            var target = e.target;
            var container = $('.class-seperated-box')[0];
            if (!$.contains(container, target) && !(target === container)) {
                $('.class-seperated-box').addClass('closed');
            }
        });
    }


    //获取班级集合
    function handleClassListFetch() {
        $(document).on('click', '.class-cat li', function () {
            var serviceId = $(this).attr('value');
            vent.trigger('fetchCourses', serviceId);
        });
        $('.class-cat li:first').click();
    }

    //获取学生集合
    function handleStudentListFetch() {
        $(document).on('click', '.lessons li>.lesson-name', function () {
            var $el = $(this).closest('li');
            var courseId = $el.attr('courseid');
            var termId = $el.attr('termid');
            var lessonId = $el.attr('lessonid');
            vent.trigger('fetchStudent', courseId, termId, lessonId);
        });
    }

    // 班级列表和学生列表添加滚动条
    function handlePanelScroll() {
        $('#search-res-scroller').slimscroll({
            height: '100%',
            size: "8px",
            railColor: "#f2f3f6",
            distance: "4px"
        });
        $('#student-list-wrap-scroller').slimscroll({
            height: '100%',
            size: "8px",
            railColor: "#f2f3f6",
            distance: "4px"
        });
    }

    function handleSeparateSubmit() {
        $(document).on('click', '#main-submit', function () {
            var separatedClasses = separatedClassList.collections.toJSON();
            separatedClasses.forEach(function (sc) {
                sc.students = sc.students.map(function (s) {
                    return s.toJSON();
                });
            });
            console.log(separatedClasses);
            $.ajax({
                url: '/divideClass/separatedClassSubmit',
                method: 'POST',
                data: {
                    separatedClasses: JSON.stringify(separatedClasses)
                },
                dataType: 'json',
                success: function (res) {
                    if (res.classChangNum > 0 && res.studentChangeNum > 0 && res.studentChangeNum == res.enrollChangeNum) {
                        settings.spinner.showSuccessResult("成功创建" + res.classChangNum + "个班级,分配" + res.studentChangeNum + "名学员!");
                        window.setTimeout(function () {
                            settings.spinner.showWaitCover();
                            window.location.href = settings.urlPrefix + res.redirect;
                        }, 2000);
                    } else {
                        settings.spinner.showErrorResult("分班失败!");
                    }
                }
            });

        });
    }

    // 点击lesson时修改title
    function handleClassTitleChange() {
        vent.on('openLesson', function (lesson) {
            var courseId = lesson.get('courseId');
            var termId = lesson.get('termId');

            var termTime = lesson.get('time');
            var studentCount = lesson.get('stuCount');

            var courseModel = _.findWhere(classList.collections.models, {courseId: courseId});
            var courseName = courseModel['name'];

            var termName = _.findWhere(courseModel.terms, {termId: termId})['termName'];

            // 暑期游泳班2015年第一期8：00-10：00(10人)
            var studentListTitle = courseName + termName + termTime + '(' + studentCount + '人)';
            // 暑期游泳班2015年第一期8：00-10：00
            var sepClassTitle = courseName + termName + termTime;


            $('#class-name').html(studentListTitle).attr('title', studentListTitle);
            $('.class-seperated-hd em').html(sepClassTitle);
        });
    }

    function makeSelects() {
        var selectpicker = $(".selecter").data("selectpicker");
        if(typeof selectpicker == 'undefined'){
            $('.selecter').selectpicker({
                searchkey: 'data-sp'
            });
        }
    }

    // 学生详情
    function handleStudentDetail(){
        var $wrap = $('#student-list-wrap');
        // 详情
        var studentDetailBoxTmpl = _.template($('#studentDetailBoxTmpl').html());
        $(document).on('click', '[data-click="renderNameDetail"]', function(e){
            e.preventDefault();
            $wrap.find('.pop-box').remove();

            var $this = $(this);
            var data = {
                name: $this.data('name'),
                ecard: $this.data('ecard'),
                phone: $this.data('phone')
            };

            var $box = $(studentDetailBoxTmpl(data));
            $wrap.append($box);

            var pos = getRelativePosition($wrap, $this);

            $box.css({
                left: pos.left + 'px',
                top: pos.top + 'px'
            });

        });

        // 备注
        var studentRemarkBoxTmpl = _.template($('#studentRemarkBoxTmpl').html());
        $(document).on('click', '[data-click="renderRemarkDetail"]', function(e){
            e.preventDefault();
            $wrap.find('.pop-box').remove();

            var $this = $(this);
            var data = {
                remark: $this.data('remark')
            };

            var $box = $(studentRemarkBoxTmpl(data));
            $wrap.append($box);

            var pos = getRelativePosition($wrap, $this);

            $box.css({
                left: pos.left + 'px',
                top: pos.top + 'px'
            })
        });

        // cleaning up
        $(document).on('click', function(e){
            $wrap.find('.pop-box').each(function(){
                if(!$.contains(this, e.target) &&
                    !$(e.target).hasClass('name-in') &&
                    !$(e.target).hasClass('student-remark-in-text')){
                    $(this).remove();
                }
            })
        });

        $(document).on('click', '[data-click="remove-pop-info"]', function(){
            $(this).closest('.pop-box').remove();
        })
    }

    function getRelativePosition($wrap, $elem){
        var wrapPos = $wrap.offset();
        var elemPos = $elem.offset();

        return {
            top: elemPos.top - wrapPos.top + 35,
            left: elemPos.left - wrapPos.left - 110
        }
    }

    $(function () {
        // ui的main函数
        handleSidePanelToogle();

        initViews();

        handleSearchClass();
        switchInit();
        switchTabs();
        handleClassListFetch();
        handleStudentListFetch();
        handlePanelScroll();
        handleSeparateSubmit();
        handleClassTitleChange();
        makeSelects();
        handleStudentDetail();
    });
});