package com.asiainfo.aisports.service;

import com.asiainfo.aisports.domain.core.CouponAttr;
import com.asiainfo.aisports.persistence.core.CouponAttrMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by xzeng on 18/9/4.
 */
@Service
public class CouponAttrService {
    @Autowired
    CouponAttrMapper couponAttrMapper;

    /**
     * 根据attrCode查询coupon的attr
     *
     * @param couponId
     * @param attrCode
     * @return
     */
    @Transactional(readOnly = true)
    public CouponAttr queryAttrByCode(Long couponId, String attrCode) {
        return couponAttrMapper.getCouponValue(couponId, attrCode);
    }
}
