<template>
  <div class="title-box add-Site-config">
    <el-form
      :model="campForm"
      ref="campForm"
      :rules="addValidateForm"
      label-width="120px"
      class="demo-ruleForm"
      :disabled="(type =='edit' && status == 2 || status == 3) || type =='check'"
    >
      <el-form-item label="活动名称" prop="actName">
        <el-input
          v-model="campForm.actName"
          placeholder="请输入活动名称"
          maxlength="20"
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="活动类型" prop="actType"  >
        <el-select v-model="campForm.actType" placeholder="请选择" @change="handleChangeType" :disabled="type =='edit'|| type =='check'">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="活动时间" prop="startDate">
        <el-date-picker
          v-model="activityDate"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleChangeDate"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="小程序背景图" prop="miniScreen" class="addOnline">
        <el-upload
          :class="['avatar-uploader',campForm.actType == 2&&'wx-upload']"
          :action="serverUrl"
          :show-file-list="false"
          :on-success="
            (response, file, fileList) =>
              handlePhonoSuccess(response, file, fileList, 'wx')
          "
          :before-upload="(file) => beforeAvatarUpload(file, 'img', 10)"
          :on-error="handleUploadError"
          :on-exceed="handleExceed"
          :limit="1"
        >
          <img
            v-if="campForm.miniScreen"
            :src="campForm.miniScreen"
            :class="['avatar',campForm.actType == 2&&'wx-avatar']"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <p style="color: #999">
          用于小程序活动主图展示，建议上传的图片{{campForm.actType == 1? '750*530': '750*1624'}}像素，大小不超过10M，支持jpg,png,bmp
        </p>
        <div class="template-cover-box">
          <div
            v-for="item in coverList"
            :key="item.paramKey"
            class="cover-item"
            @click="downloadIamge(item.paramValue)"
          >
            <img class="cover" :src="item.paramValue" alt="" />
            <i class="iconfont icon-xiazai1 download"></i>
          </div>
          <!-- <div class="cover-item more-cover">更多模板敬起期待</div> -->
        </div>
      </el-form-item>
      <el-form-item label="大屏背景图" prop="largeScreen" class="addOnline">
        <el-upload
          class="avatar-uploader bg-upload"
          :action="serverUrl"
          :show-file-list="false"
          :on-success="
            (response, file, fileList) =>
              handlePhonoSuccess(response, file, fileList, 'web')
          "
          :before-upload="(file) => beforeAvatarUpload(file, 'img', 10)"
          :on-error="handleUploadError"
          :on-exceed="handleExceed"
          :limit="1"
        >
          <img
            v-if="campForm.largeScreen"
            :src="campForm.largeScreen"
            class="bg-avatar"
          />
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <p style="color: #999">
          用于大屏活动主图展示，建议上传的图片1152*2048像素，大小不超过10M，支持jpg,png,bmp
        </p>
        <div class="template-cover-box">
          <div
            v-for="item in webList"
            :key="item.src"
            class="cover-item"
            @click="downloadIamge(item.src)"
          >
            <img class="cover" :src="item.src" alt="" />
            <i class="iconfont icon-xiazai1 download"></i>
          </div>
          <!-- <div class="cover-item more-cover">更多模板敬起期待</div> -->
        </div>
      </el-form-item>
      <el-form-item label="背景音乐" prop="backMusic">
        <el-upload
          class="upload-demo"
          :action="music_url"
          :on-success="
            (response, file, fileList) =>
              handlePhonoSuccess(response, file, fileList, 'music')
          "
          :before-upload="(file) => beforeAvatarUpload(file, 'music', size)"
          :on-change="handleChange"
          :on-error="handleUploadError"
          :on-remove="handleRemove"
          :file-list="musicList"
          :limit="1"
        >
          <el-button :disabled="musicList.length > 0" size="small" type="primary">点击上传</el-button>
          <!-- <div slot="tip" class="el-upload__tip">
            格式支持mp3、m4a， 单个文件大小不超过300M
          </div> -->
        </el-upload>
      </el-form-item>
      <el-form-item class="bottom-fr" v-if="type != 'check'">
        <el-button @click="goBack()">取消</el-button>
        <el-button type="primary" @click="submitForm('campForm')"
          >确认</el-button
        >
      </el-form-item>
    </el-form>
    <el-dialog title="提示" :visible.sync="dialogVisibleTitle" width="30%">
      <i class="el-icon-warning i-color"></i>
      <span>确认保存基本信息吗</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleTitle = false">取 消</el-button>
        <el-button type="primary" @click="submitManageForm">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
var validatorNum = function (rule, value, callback) {
  if (value === "") {
    callback(new Error("不能为空"));
  } else if (!/^([1-9]\d*|[0]{1,1})$/.test(value)) {
    callback(new Error("格式错误"));
  } else {
    callback();
  }
};
import settings from "aisportsConfig";
import { API } from "../utils/api";
import mAlert from "mAlert";
export default {
  data() {
    return {
      activityDate: '',
      options: [
        {
          label: "签到抽奖",
          value: "1",
        },
        {
          label: "活动上墙",
          value: "2",
        },
      ],
      webList: [],
      musicList: [],
      coverList: [],
      campTitle: "",
      activityId: "",
      avatarUrl: "",
      dialogVisibleTitle: false,
      ossUrl: $("#ossUrl").val(),
      imageUrl: "",
      upload_url: "",
      dialogVisible: false,
      campForm: {
        actName: "",
        actType: "1",
        startDate: "",
        endDate: "",
        largeScreen: "",
        miniScreen: "",
        backMusic: "",
      },
      addValidateForm: {
        actName: [
          { required: true, message: "请输入运动会名称", trigger: "blur" },
        ],
        actType: [{ required: true, message: "请选择", trigger: "change" }],
        startDate: [{ required: true, message: "请选择", trigger: "change" }],
        largeScreen: [{ required: true, message: "请选择", trigger: "change" }],
        miniScreen: [{ required: true,  message: "请选择", trigger: "blur" }],
        backMusic: [{ required: true, message: "请上传背景音乐", trigger: "blur" }],
      },
    };
  },
  props: {
    type: {
      type: String,
      default: "",
    },
    status: {
      type: [String, Number],
      default: "",
    },
  },
  computed: {
    music_url() {
      return settings.urlPrefix + "/upload/storeFile";
    },
    serverUrl() {
            return `${settings.urlPrefix}/upload/temp`
        },
  },
  mounted() {
    console.log(this.$route);
    if (this.$route.query.id) {
      console.log(33);
      this.activityId = this.$route.query.id;
      this.getDetail();
    }else{
      this.getDefaultBg();
    }
  },
  methods: {
    handleChangeType(val){
      this.getDefaultBg();
      this.$emit('changeType',val)
    },
    handleChangeDate(val){
      if(val){
        this.activityDate = val;
        this.campForm.startDate = val[0];
        this.campForm.endDate = val[1];
      }else{
        this.activityDate = '';
        this.campForm.startDate = '';
        this.campForm.endDate = '';
      }
    },
    getDefaultBg(){
      API.getDefaultBg({actType: this.campForm.actType}).then(res=>{
        this.campForm.largeScreen = this.ossUrl +res.defaultLargeScreen;
        this.campForm.miniScreen = this.ossUrl +res.defaultMiniScreen;
        if(res.defaultMp3 != ''){
          const backMusic = this.ossUrl +res.defaultMp3;
          this.musicList = [{
            name: '背景音乐',
            url: backMusic
          }];
          this.campForm.backMusic = backMusic;
        }
      })
    },
    handleExceed(file, fileList){
      this.$message.error('只能上传一张背景图');
    },
    handleRemove(file, fileList){
      this.campForm.backMusic = '';
      console.log(file, fileList,'remove');
      this.musicList = fileList;
    },
    handleChange(file, fileList){
      console.log(file, fileList,'change')
      this.musicList = fileList;
    },
    downloadIamge(imgsrc, name) {
      let image = new Image();
      // 解决跨域 Canvas 污染问题
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        let context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        let url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        let a = document.createElement("a"); // 生成一个a元素
        let event = new MouseEvent("click"); // 创建一个单击事件
        a.download = name || "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = imgsrc;
    },
    getCover() {
      API.getSportsOriginalCover().then((res) => {
        this.coverList = res.result;
      });
    },
    getImgUrl(src) {
      var imgRoot = $("#ossUrl").val();
      if (/^http/.test(src)) {
        return src;
      }
      return imgRoot + src;
    },
    handlePhonoSuccess(res, file, fileList, type) {
      if(type =='music'){
        this.campForm.backMusic = res.url;
      }
      if(type == 'web'){
        this.campForm.largeScreen = res.url;
      }
      if(type == 'wx'){
        this.campForm.miniScreen = res.url;
      }
      console.log("success", res);
      // this.avatarUrl = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file, type, size) {
      if (type == "music") {
        const isJPG = file.type === "audio/mp3" || file.type === "audio/mpeg";
        const isLt2M = file.size / 1024 / 1024 < 10;
        if (!isJPG) {
          this.$message.warning("上传音乐只能是 audio/mpeg 格式!");
          return isJPG;
        }
        if (!isLt2M) {
          this.$message.warning("上传音乐大小不能超过 10MB!");
          return isLt2M;
        }
      return isJPG && isLt2M;
      } else {
        const isJPG =
          file.type == "image/jpeg" ||
          file.type == "image/png" ||
          file.type == "image/jpg";
        const isLt2M = file.size / 1024 / 1024 < size;
        if (!isJPG) {
          this.$message.warning("上传图片只能是 JPG/PNG/JPEG格式!");
          return isJPG;
        }
        if (!isLt2M) {
          this.$message.warning(`上传图片大小不能超过 ${size}MB!`);
          return isLt2M;
        }
      return isJPG && isLt2M;
      }
    },
    dataURLtoFile(dataurl, filename) { //将base64转换为文件
        var arr = dataurl.split(','),
            mime = arr[0].match(/:(.*?);/)[1],
            bstr = atob(arr[1]),
            n = bstr.length,
            u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new File([u8arr], filename, {
            type: mime
        });
    },
    uploadSectionAvatar(params) {
      let reader = new FileReader();
      reader.readAsDataURL(params.file);
      reader.onload = () => {
        let img_base64 = reader.result;
        let BASE64 = img_base64.split(",");
        const URL = window.URL || window.webkitURL;
        let img;
        img = URL.createObjectURL(params.file);
        let photoParams = {
          base64: BASE64[1],
          imageType: "png",
          path: "image",
        };
        API.uploadFile(photoParams).then((res) => {
          let imgInfo = { path: this.ossUrl + res.fileName };
          console.log(imgInfo);
          // this.campForm.cover = imgInfo.path;
          params.onSuccess(res);
        });
      };
      // .catch(({err}) => {
      //   params.onError(err);
      // });
    },
    handleUploadError(err, file, fileList) {
      //文件上传失败后的操作

      console.log("err", err);
    },
    async getDetail() {
      try {
        let result = await API.getActivityInfo({activityId:this.activityId});
        // this.campTitle = result.camp.title;
        // this.$emit("showCamptitle", this.campTitle);
        this.campForm = Object.assign(this.campForm, result.activityInfo);
        this.campForm.largeScreen = this.ossUrl + result.activityInfo.largeScreen;
        this.campForm.miniScreen = this.ossUrl + result.activityInfo.miniScreen;
        this.$emit('changeType',this.campForm.actType)
        this.musicList = [
          {
            name: "背景音乐",
            url: this.ossUrl + result.activityInfo.backMusic,
          },
        ];
        this.activityDate = [
          result.activityInfo.startDate,
          result.activityInfo.endDate,
        ];
      } catch (err) {}
    },
    getCampBaseInfo(id) {
      return API.getBasicSportsInformation({ activityId: id });
    },
    goBack() {
      if (this.activityId) {
        this.$router.go(-1);
      } else {
        this.$confirm("此时离开将丢失已编辑的内容, 是否离开?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$router.go(-1);
          })
          .catch(() => {
            // this.$message({
            //   type: 'info',
            //   message: '已取消删除'
            // });
          });
      }
    },

    submitManageForm() {
      let params = {
        actName: this.campForm.actName,
        actType: this.campForm.actType,
        startDate: this.campForm.startDate,
        endDate: this.campForm.endDate,
        largeScreen: this.formatOssUrl(this.campForm.largeScreen),
        miniScreen: this.formatOssUrl(this.campForm.miniScreen),
        backMusic: this.formatOssUrl(this.campForm.backMusic),
      };
      if (this.campForm.id){
        params.id = this.campForm.id;
      }
      console.log(params);
      API.saveActivity(params).then((res) => {
        console.log(res);
        if (res.error == "0") {
          this.dialogVisibleTitle = false;
          mAlert.showSuccess(params.id ? "编辑成功" : "新增成功");
          if(this.campForm.actType ==1){
            this.$emit("showActivityId", res.activityId);
          }
          // this.$router.go(-1);
          // this.getDetail();
        } else {
          this.dialogVisibleTitle = false;
        }
      });
    },
    formatOssUrl(url){
      if (/^http/.test(url)) {
        return url.split(this.ossUrl)[1];
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log(this.campForm);
          this.dialogVisibleTitle = true;
        } else {
          console.log(112, this.campForm);
        }
      });
    },
    handleAvatarSuccess(res, file) {
      console.log("success", res);
      this.imageUrl = URL.createObjectURL(file.raw);
    },
  },
};
</script>
<style lang="scss" scoped>
.add-Site-config {
  padding-top: 40px;
  .el-upload--picture-card {
    input[type="file"] {
      display: none;
    }
  }
  .info-input .el-input {
    width: 292px;
  }

  .el-input {
    width: 292px;
  }
  .info-input .el-input {
    width: 292px;
  }
  .el-textarea {
    width: 292px;
  }
  .el-date-editor.el-input,
  .el-select {
    width: 292px;
  }
  .el-input {
    width: 292px;
  }
  .template-cover-box {
    display: flex;
    .more-cover {
      border: 2px dashed rgba(224, 224, 224, 1);
      background: #f7f7f7;
      text-align: center;
      line-height: 54px;
      color: #999;
      opacity: 0.5;
    }
    .cover-item {
      width: 162px;
      height: 54px;
      border-radius: 4px;
      position: relative;
      margin-right: 12px;
      .cover {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
      .download {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 14px;
        height: 14px;
        border-radius: 4px 0 4px 0;
        background: rgba(0, 0, 0, 0.3);
        font-size: 12px;
        text-align: center;
        color: #fff;
        display: inline-block;
        line-height: 14px;
        // .iconfont{
        // }
      }
    }
  }
}
.quillHeight {
  width: 580px;
  // height: 400px;
}

.quill-editor {
  width: 580px;
  height: 310px;
}
</style>
<style lang="scss">
.add-Site-config {
  padding-top: 40px;
  .upload-demo {
    input[type="file"] {
      display: none !important;
    }
  }
}
.height460 {
  height: 460px;
  .el-form-item__content {
    height: 460px;
  }
}
.addOnline {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    input[type="file"] {
      display: none !important;
    }
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 225px;
    height: 159px;
    line-height: 159px;
    text-align: center;
  }
  .avatar {
    width: 225px;
    height: 159px;
    display: block;
  }
  .wx-upload{
    width: 100px !important;
    height: 216px !important;
    line-height: 216px !important;
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 100px !important;
      height: 216px !important;
      line-height: 216px !important;
      text-align: center;
    }
    .wx-avatar {
      width: 100px !important;
      height: 216px !important;
      display: block;
    }
  }
  .bg-upload {
    width: 256px !important;
    height: 144px !important;
    line-height: 144px !important;
    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 256px;
      height: 144px;
      line-height: 144px;
      text-align: center;
    }
    .bg-avatar {
      width: 256px;
      height: 144px;
      display: block;
    }
  }
}
.el-input__inner {
  .el-range-input {
    width: 150px !important;
  }
  .el-range-separator {
    line-height: 32px !important;
  }
}
.el-range-editor {
  width: 360px !important;
}
.map-thumb {
  margin-left: 100px;
  width: 360px;
  height: 180px;
  margin-bottom: 10px;
}
#changeMap {
  width: 765px;
  height: 500px;
}
</style>