package com.asiainfo.aisports.service.cancel;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.CustInfo;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.pay.client.PayClient;
import com.asiainfo.aisports.pay.client.Result;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.*;
import com.asiainfo.aisports.service.audit.AuditService;
import com.asiainfo.aisports.service.gxt.GxtPointsService;
import com.asiainfo.aisports.service.product.DepositDetailService;
import com.asiainfo.aisports.service.thirdPay.ThirdPayService;
import com.asiainfo.aisports.service.thirdPay.ThirdPayServiceFactory;
import com.asiainfo.aisports.service.unionPay.UnionPayService;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.JSONObjectUtils;
import com.asiainfo.aisports.workflow.engine.WorkflowEngineFacets;
import com.asiainfo.aisports.service.wechat.WechatNonTaxPayService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.snaker.engine.SnakerEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by liuchangyuan on 16/6/28.
 */
@Service
public class CancelService {
    @Autowired
    TradeMapper tradeMapper;
    @Autowired
    ApplicationContext applicationContext;
    @Autowired
    TradePayLogMapper tradePayLogMapper;
    @Autowired
    SequenceWrapper sequenceWrapper;
    @Autowired
    TradeService tradeService;
    @Autowired
    TradeFeeMapper tradeFeeMapper;
    @Autowired
    EcardChangeMapper ecardChangeMapper;
    @Autowired
    EcardTradeService ecardTradeService;
    @Autowired
    PayClient payClient;
    @Autowired
    CustQueryService custQueryService;
    @Autowired
    DepositMapper depositMapper;
    @Autowired
    ResDepositChangeMapper resDepositChangeMapper;
    @Autowired
    CouponService couponService;
    @Autowired
    AuditOrderMapper auditOrderMappers;
    @Autowired
    WorkflowEngineFacets facets;
    @Autowired
    AuditService auditService;
    @Autowired
    JournalService journalService;
    @Autowired
    MemberMapper memberMapper;
    @Autowired
    TradePayOncreditMapper tradePayOncreditMapper;
    @Autowired
    DownPaymentChangeMapper downPaymentChangeMapper;
    @Autowired
    DownPaymentMapper downPaymentMapper;
    @Autowired
    MemberGradeLogMapper memberGradeLogMapper;
    @Autowired
    CouponEntityService couponEntityService;
    @Autowired
    DepositDetailService depositDetailService;
    @Autowired
    private UnionPayService unionPayService;
    @Autowired
    private GxtPointsService gxtPointsService;
    @Autowired
    private WechatNonTaxPayService wechatNonTaxPayService;
    @Autowired
    VenueParamConfig venueParamConfig;

    /**
     * 执行返销操作
     *
     * @param tradeId 流水号
     * @param staff   员工
     * @param remark  备注
     * @return
     */
    @Transactional
    public ServiceResult cancelTrade(Long tradeId, LoginStaff staff, String remark) {
        Trade trade = tradeMapper.selectByPrimaryKey(tradeId);
        if (trade == null) {
            return new ServiceResult(1, "订单不存在");
        }

        TradeCancelService tradeCancelService = TradeCancelServiceFactory.createTradeCancelService(
                applicationContext, trade.getTradeTypeCode());
        if (tradeCancelService == null) {
            return new ServiceResult(1, "该业务暂时未提供返销功能");
        }

        ServiceResult checkResult = tradeCancelService.check(trade);
        if (checkResult.getError() != 0) {
            return checkResult;
        }

        // 校验订单可返销的时间
        checkCancelTradeLimitTime(trade);

        Long cancelTradeId = sequenceWrapper.tradeSequence();

        CustInfo custInfo = custQueryService.findByCustId(trade.getCustId());

        Date now = new Date();
        // 修改 trade 的状态
        trade.setCancelTag(Constants.CancelTag.CANCELLED);
        trade.setCancelStaffId(staff.getStaffId());
        trade.setCancelTime(now);
        tradeMapper.updateByPrimaryKeySelective(trade);

        // 新增返销业务流水 trade 记录, trade_id_b 记录被返销的 trade_id
        Trade cancelTrade = new Trade();
        cancelTrade.setTradeId(cancelTradeId);
        cancelTrade.setCustId(trade.getCustId());
        if (trade.getAcctId() != null) {
            cancelTrade.setAcctId(trade.getAcctId());
        }
        if (trade.getUserId() != null) {
            cancelTrade.setUserId(trade.getUserId());
        }
        cancelTrade.setCustName(trade.getCustName());
        cancelTrade.setEcardNo(trade.getEcardNo());
        cancelTrade.setTradeTypeCode(tradeCancelService.getCancelTradeTypeCode());
        cancelTrade.setSubscribeState(Constants.SubscribeState.COMPLETED);
        cancelTrade.setAcceptDate(now);
        cancelTrade.setFinishDate(now);
        cancelTrade.setPayTfee(-trade.getPayTfee());
        cancelTrade.setCancelTag("2");
        cancelTrade.setRelationSubscribeId(trade.getSubscribeId());
        cancelTrade.setTradeIdB(tradeId);
        cancelTrade.setRemark(remark);
        cancelTrade.setPayState(Constants.PayState.PAID);
        cancelTrade.setPayTime(now);
        cancelTrade.setBusinessType(trade.getBusinessType());
        cancelTrade.setVenueId(trade.getVenueId());
        tradeService.saveTrade(cancelTrade, staff);

        // 如果存在 trade_pay_log 记录, 则增加负的 trade_pay_log 的记录
        ServiceResult cancelResult = cancelPayLog(trade.getTradeId(), cancelTrade, custInfo, staff);
        if (cancelResult.getError() != 0) {
            throw new ServiceException(cancelResult.getMessage());
        }

        // 如果存在未审核的工单, 则修改工单状态
        AuditOrder param = new AuditOrder();
        param.setTradeId(tradeId);
        param.setAuditState(Constants.TradeAuditState.UN_AUDIT);
        List<AuditOrder> auditOrders = auditOrderMappers.selectByFields(param);
        if (!auditOrders.isEmpty()) {
            for (AuditOrder auditOrder : auditOrders) {
                // 中止审核
                facets.getEngine().order().terminate(auditOrder.getWfOrderId(), SnakerEngine.ADMIN);
                // 更新审核表 audit_order
                auditService.terminateAuditOrder(auditOrder.getAuditId(), staff.getStaffId(), "订单已返销");
            }
        }

        // 返销参加的 sale_campaign
        if (checkResult.containsKey("saleCamp")) {
            cancelSaleCampaign(trade.getTradeId(), (JSONArray) checkResult.get("saleCamp"));
        }

        // 返销优惠券
        couponEntityService.cancelCoupons(trade.getTradeId(), cancelTradeId, staff.getStaffId());

        // 返销会员信息
        cancelMemberAndGrade(cancelTrade);

        // 执行业务自己的返销方法
        ServiceResult serviceResult = tradeCancelService.cancel(trade, cancelTradeId, staff);
        if (serviceResult.getError() == 0) {
            // 最后调用一卡通平台的返销接口
            Result result = payClient.cancelTrade(trade.getCenterId(), trade.getTradeId(), cancelTradeId);
            if (result.getCode() != 0) {
                throw new ServiceException("一卡通平台返销接口错误[" + result.getMessage() + "]");
            }
        }
        return serviceResult;
    }

    /**
     * 校验订单可返销的时间
     *
     * @param trade
     */
    private void checkCancelTradeLimitTime(Trade trade) {
        String cancelLimitDay = venueParamConfig.getParam(trade.getCenterId(), Constants.VenueParam.CANCEL_TRADE_LIMIT_DAY);
        if (!Strings.isNullOrEmpty(cancelLimitDay) && JSONObjectUtils.parseArray(cancelLimitDay) != null){
            JSONArray jsonArray = JSONObjectUtils.parseArray(cancelLimitDay);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject data = jsonArray.getJSONObject(i);
                if (data.getInt("tradeTypeCode") == trade.getTradeTypeCode().longValue()){
                    DateTime day = DateUtil.offsetDay(trade.getAcceptDate(), data.getInt("day"));
                    if (DateUtil.date().after(day)){
                        throw new ServiceException("当前业务可返销截止时间为"+day.toString());
                    }
                    //匹配到后直接返回
                    break;
                }
            }
        }
    }

    /**
     * 执行拼团失败后返销操作 不需要新建返销类
     *
     * @param trade 流水号
     * @param staff   员工
     * @param remark  备注
     * @return
     */
    @Transactional
    public ServiceResult groupPurchaseFailCancelTrade(Trade trade, LoginStaff staff, Long cancelTradeTypeCode, String remark) {
        if (trade == null) {
            throw new ServiceException("订单不存在");
        }
        Long cancelTradeId = sequenceWrapper.tradeSequence();

        CustInfo custInfo = custQueryService.findByCustId(trade.getCustId());
        Date now = new Date();
        // 修改 trade 的状态
        trade.setSubscribeState(Constants.SubscribeState.COMPLETED);
        trade.setCancelTag(Constants.CancelTag.CANCELLED);
        trade.setCancelStaffId(staff.getStaffId());
        trade.setCancelTime(now);
        tradeMapper.updateByPrimaryKeySelective(trade);

        // 新增返销业务流水 trade 记录, trade_id_b 记录被返销的 trade_id
        Trade cancelTrade = new Trade();
        cancelTrade.setTradeId(cancelTradeId);
        cancelTrade.setCustId(trade.getCustId());
        if (trade.getAcctId() != null) {
            cancelTrade.setAcctId(trade.getAcctId());
        }
        if (trade.getUserId() != null) {
            cancelTrade.setUserId(trade.getUserId());
        }
        cancelTrade.setCustName(trade.getCustName());
        cancelTrade.setEcardNo(trade.getEcardNo());
        cancelTrade.setTradeTypeCode(cancelTradeTypeCode);
        cancelTrade.setSubscribeState(Constants.SubscribeState.COMPLETED);
        cancelTrade.setAcceptDate(now);
        cancelTrade.setFinishDate(now);
        cancelTrade.setPayTfee(-trade.getPayTfee());
        cancelTrade.setCancelTag("2");
        cancelTrade.setRelationSubscribeId(trade.getSubscribeId());
        cancelTrade.setTradeIdB(trade.getTradeId());
        cancelTrade.setRemark(remark);
        cancelTrade.setPayState(Constants.PayState.PAID);
        cancelTrade.setPayTime(now);
        cancelTrade.setBusinessType(trade.getBusinessType());
        cancelTrade.setVenueId(trade.getVenueId());
        cancelTrade.setCenterId(trade.getCenterId());
        tradeService.saveTrade(cancelTrade, staff);

        // 如果存在 trade_pay_log 记录, 则增加负的 trade_pay_log 的记录
        ServiceResult cancelResult = cancelPayLog(trade.getTradeId(), cancelTrade, custInfo, staff);
        if (cancelResult.getError() != 0) {
            throw new ServiceException(cancelResult.getMessage());
        }
        // 最后调用一卡通平台的返销接口
        Result result = payClient.cancelTrade(trade.getCenterId(), trade.getTradeId(), cancelTradeId);
        if (result.getCode() != 0) {
            throw new ServiceException("一卡通平台返销接口错误[" + result.getMessage() + "]");
        }
        return new ServiceResult();
    }

    /**
     * 返销销售优惠活动
     *
     * @param tradeId
     * @param camp
     */
    private void cancelSaleCampaign(Long tradeId, JSONArray camp) {
        for (Object object : camp) {
            JSONObject jsonObject = (JSONObject) object;
            String type = jsonObject.getString("type"); // 赠送类型 1-优惠券
            if ("1".equals(type)) { // 优惠券
                continue;
            }
        }
    }

    /**
     * 返销支付日志
     *
     * @param tradeId
     * @param cancelTrade
     * @param custInfo
     * @param staff
     * @return
     */
    private ServiceResult cancelPayLog(Long tradeId, Trade cancelTrade, CustInfo custInfo, LoginStaff staff) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        Long cancelTradeId = cancelTrade.getTradeId();

        // 插入trade_fee
        TradeFee tradeFee = tradeFeeMapper.selectByPrimaryKey(tradeId);
        if (tradeFee != null) {
            tradeFee.setTradeId(cancelTradeId);
            tradeFee.setShouldPay(-tradeFee.getShouldPay());
            tradeFee.setRealPay(-tradeFee.getRealPay());
            tradeFee.setRemark("");
            tradeFee.setManualReduction(null);
            tradeFee.setTradeStaffId(staff.getStaffId());
            tradeFee.setTradeDate(now);
            tradeFee.setTradeVenueId(staff.getVenueId());
            tradeFeeMapper.insert(tradeFee);
        }

        // 支付日志
        List<TradePayLog> payLogList = tradePayLogMapper.selectByTradeId(tradeId);

        List<TradePayLog> cancelPayLogList = Lists.newArrayList();
        List<ResDepositChange> resDepositChanges = Lists.newArrayList();
        for (TradePayLog payLog : payLogList) {
            // 将原来的修改退款金额
            payLog.setDrawbackMoney(payLog.getRealPay());
            tradePayLogMapper.updateByPrimaryKeySelective(payLog);

            payLog.setPayLogId(sequenceWrapper.tradeSequence());
            payLog.setTradeId(cancelTradeId);
            payLog.setShouldPay(-payLog.getShouldPay());
            payLog.setRealPay(-payLog.getRealPay());
            payLog.setTradeStaffId(staff.getStaffId());
            payLog.setTradeDate(now);
            payLog.setTradeVenueId(staff.getVenueId());
            payLog.setRemark("业务返销冲正");
            payLog.setDrawbackMoney(0);
            cancelPayLogList.add(payLog);

            String payModeCode = payLog.getPayModeCode();

            // 判断挂账
            if (Constants.PayMode.ON_CREDIT.equals(payModeCode)) {
                TradePayOncredit tradePayOncredit = tradePayOncreditMapper.selectByPrimaryKey(tradeId);
                if (tradePayOncredit != null) {
                    if (tradePayOncredit.getRealPay() != null && tradePayOncredit.getRealPay() > 0) {
                        throw new ServiceException("该笔交易已经结算部分金额,不可以返销.");
                    }
                    tradePayOncredit.setShouldPay(0);
                    tradePayOncredit.setUpdateStaffId(staff.getStaffId());
                    tradePayOncredit.setPayState(Constants.OnCreditState.YES);
                    tradePayOncredit.setUpdateTime(now);
                    tradePayOncreditMapper.updateByPrimaryKeySelective(tradePayOncredit);
                }
            }

            // 支付退款
            if (Constants.PayMode.ECARD.equals(payModeCode)) { // 一卡通
                // 获得一卡通余额
                EcardChange latestOne = ecardChangeMapper.selectLatestOne(custInfo.getCustId());
                Long ecardBalance = latestOne.getBalance() - payLog.getRealPay().longValue();

                ecardTradeService.addEcardChange(cancelTrade, custInfo, -payLog.getRealPay().longValue(), ecardBalance);
            } else if (Constants.PayMode.SPECIAL_CARD.equals(payModeCode) || Constants.PayMode.SPECIAL_GRANT_CARD.equals(payModeCode)) { // 专项卡
                // 修改 deposit
                depositMapper.updateByDepositId(payLog.getDepositId(), -payLog.getRealPay());
                Deposit deposit = depositMapper.selectByPrimaryKey(payLog.getDepositId());
                // 专项卡变更日志
                ResDepositChange resDepositChange = new ResDepositChange();
                resDepositChange.setChangeLogId(payLog.getPayLogId());
                resDepositChange.setTradeId(cancelTradeId);
                resDepositChange.setDepositId(payLog.getDepositId());
                resDepositChange.setResChangeCnt(-payLog.getRealPay());
                resDepositChange.setPayableAmount(payLog.getShouldPay());
                resDepositChange.setResType(Constants.ProductResType.MONEY);
                resDepositChange.setUpdateVenueId(staff.getVenueId());
                resDepositChange.setUpdateStaffId(staff.getStaffId());
                resDepositChange.setUpdateTime(now);
                resDepositChange.setBalance(deposit.getBalance()); // 交易后余额
                resDepositChanges.add(resDepositChange);

                ServiceResult result = depositDetailService.depositRefundMoney(cancelTradeId, tradeId, payLog.getDepositId(), Long.valueOf(-payLog.getRealPay()), payLog.getPayLogId(), staff.getStaffId());
                if (result.getError() != 0) {
                    throw new ServiceException(serviceResult.getMessage());
                }
            } else if (Constants.PayMode.COUPON.equals(payModeCode)) { // 代金券
                couponService.returnCoupon(payLog.getCouponNo(), -payLog.getRealPay(), cancelTradeId, staff.getStaffId());
            } else if (Constants.PayMode.EXCHANGE_COUPON.equals(payModeCode)) { // 兑换券
                couponService.returnCoupon(payLog.getCouponNo(), 1, cancelTradeId, staff.getStaffId());
            } else if (Constants.PayMode.DISCOUNT_COUPON.equals(payModeCode)) { // 折扣券
                couponService.returnCoupon(payLog.getCouponNo(), 1, cancelTradeId, staff.getStaffId());
            } else if (Constants.PayMode.ALIPAY.equals(payModeCode)
                    || Constants.PayMode.WECHAT.equals(payModeCode)
                    || Constants.PayMode.ALLINPAY.equals(payModeCode)
                    || Constants.PayMode.YUAN_XIU_PAY.equals(payModeCode)
                    || Constants.PayMode.CCB_PAY_QR.equals(payModeCode)
                    || Constants.PayMode.CCB_ALI_PAY.equals(payModeCode)
                    || Constants.PayMode.CCB_WECHAT_PAY.equals(payModeCode)
                    || Constants.PayMode.CMB_WECHAT_PAY.equals(payModeCode)
                    || Constants.PayMode.CMB_SWEPT_PAY.equals(payModeCode)
                    || Constants.PayMode.CMB_SCAN_PAY.equals(payModeCode)
                    || Constants.PayMode.GXT_POINTS.equals(payModeCode)
                    || Constants.PayMode.SWIFT_PASS.equals(payModeCode)
                    || Constants.PayMode.CHANG_SHU_BANK.equals(payModeCode)
                    || Constants.PayMode.CHANG_SHU_POINT_PAY.equals(payModeCode)
                    || Constants.PayMode.ABC.equals(payModeCode)
                    || Constants.PayMode.SU_PAY.equals(payModeCode)) { //支付宝,微信,通联,园秀通,建行二维码, 建行微信支付
                ThirdPayService thirdPayService =
                        ThirdPayServiceFactory.createThirdPayService(applicationContext, payModeCode);

                JSONObject rsp = thirdPayService.refundOrder(staff, tradeId, cancelTradeId, -payLog.getRealPay(), false);
                serviceResult.putAll(rsp);
            } else if (Constants.PayMode.UNION_PAY_ALIPAY.equals(payModeCode)
                    || Constants.PayMode.UNION_PAY_WECHAT.equals(payModeCode)) {
                // 支付记录如果是银联商务-支付宝、银联商务-微信等渠道支付的，退款途径选择“银联商务”
                JSONObject rsp = unionPayService.refundOrder(staff, tradeId, cancelTradeId, -payLog.getRealPay(), false);
                serviceResult.putAll(rsp);
            } else if (payModeCode.equals(Constants.PayMode.WECHAT_NON_TAX)) {
                // 支持微信非税支付
                Map refundResult = wechatNonTaxPayService.refundOrder(staff, tradeId, cancelTradeId, -payLog.getRealPay(), false);
                if (refundResult.get("error") != null && !refundResult.get("error").toString().equals("0")) {
                    throw new ServiceException(refundResult.get("message").toString());
                }
            } else if (payModeCode.equals(Constants.PayMode.DOWN_PAYMENT)) {
                // 查询订单
                DownPaymentChange downPaymentChange = downPaymentChangeMapper.selectUseChaneByTradeId(tradeId);
                if (downPaymentChange == null) {
                    throw new ServiceException("未查询到可退订金流水");
                }

                DownPayment downPayment = downPaymentMapper.selectByPrimaryKey(downPaymentChange.getPaymentId());
                if (downPayment == null
                        || Constants.DownPaymentState.REFUNDING.equals(downPayment.getState())
                        || Constants.DownPaymentState.REFUND_SETTLE.equals(downPayment.getState())) {
                    throw new ServiceException("订金无法使用");
                }

                // 计算应该剩余的金额，这个realPay是负的
                int leftBalance = downPayment.getBalance() - payLog.getRealPay();

                // 进行余额更新
                downPayment.setBalance(leftBalance);
                downPayment.setState(Constants.DownPaymentState.PAID);
                downPayment.setUpdateTime(now);
                downPayment.setUpdateStaffId(staff.getStaffId());
                downPaymentMapper.updateByPrimaryKeySelective(downPayment);

                // 更新原异动表
                downPaymentChange.setDrawbackMoney(downPaymentChange.getDrawbackMoney() - payLog.getRealPay());
                downPaymentChange.setUpdateTime(now);
                downPaymentChange.setUpdateStaffId(staff.getStaffId());
                downPaymentChange.setUpdateVenueId(staff.getVenueId());
                downPaymentChangeMapper.updateByPrimaryKeySelective(downPaymentChange);

                // 进行异动表插入
                DownPaymentChange downPaymentChangeNew = new DownPaymentChange();
                downPaymentChangeNew.setId(sequenceWrapper.downPaymentSequence());
                downPaymentChangeNew.setTradeId(cancelTradeId);
                downPaymentChangeNew.setPaymentId(downPayment.getPaymentId());
                downPaymentChangeNew.setSchargeMoney(payLog.getRealPay());
                downPaymentChangeNew.setBalance(leftBalance);
                downPaymentChangeNew.setDrawbackMoney(0);
                downPaymentChangeNew.setUpdateTime(now);
                downPaymentChangeNew.setUpdateStaffId(staff.getStaffId());
                downPaymentChangeNew.setUpdateVenueId(staff.getVenueId());
                downPaymentChangeMapper.insert(downPaymentChangeNew);
            }
        }
        if (!cancelPayLogList.isEmpty()) {
            tradePayLogMapper.insertBatch(cancelPayLogList);
        }
        if (!resDepositChanges.isEmpty()) {
            resDepositChangeMapper.insertBatch(resDepositChanges);
        }
        return serviceResult;
    }

    /**
     * 返销会员级别
     *
     * @param cancelTrade
     */
    private void cancelMemberAndGrade(Trade cancelTrade) {
        Journals journals = journalService.findByRelatedId(cancelTrade.getTradeIdB(), "member");
        if (journals != null) {
            List<JournalDetails> detailsList = journalService.findDetailsByJournalId(journals.getId());
            if (!detailsList.isEmpty()) {
                Member member = memberMapper.selectByPrimaryKey(journals.getJournalizedId());
                if (member == null) {
                    throw new ServiceException("未找到会员信息，不能返销该订单");
                }

                // 恢复会员原信息
                ServiceResult result = revertMember(member, detailsList, cancelTrade);
                if (result.getError() != 0) {
                    throw new ServiceException(result.getMessage());
                }
            }
        }
    }

    private ServiceResult revertMember(Member member, List<JournalDetails> detailsList, Trade cancelTrade) {
        Member oldMember = new Member();
        oldMember.setMemberId(member.getMemberId());

        boolean isNewMember = true; // 是否是新增会员
        for (JournalDetails details : detailsList) {
            if ("endDate".equals(details.getPropKey())) {
                Date oldEndDate = DateCalUtil.convertDate(details.getOldValue(), Constants.DEFAULT_DATE_FORMAT);
                Date newEndDate = DateCalUtil.convertDate(details.getValue(), Constants.DEFAULT_DATE_FORMAT);
                if (!isEqual(member.getEndDate(), newEndDate)) {
                    return new ServiceResult(1, "会员有效期已经发生变化，不能返销该订单");
                }
                if (oldEndDate != null) { // 原有效期不为空
                    isNewMember = false;
                }
                member.setEndDate(oldEndDate);
            } else if ("gradeId".equals(details.getPropKey())) {
                String oldGradeId = details.getOldValue();
                String newGradeId = details.getValue();
                if (!isEqual(member.getGradeId(), newGradeId)) {
                    return new ServiceResult(1, "会员级别已经发生变化，不能返销该订单");
                }
                if (oldGradeId != null) { // 原级别不为空
                    isNewMember = false;
                }
                member.setGradeId(oldGradeId);

                MemberGradeLog oldMemberGradeLog = memberGradeLogMapper.selectByTradeId(cancelTrade.getTradeIdB());
                if (oldMemberGradeLog != null) {
                    member.setGradeStartDate(oldMemberGradeLog.getOldGradeStartDate());
                    member.setGradeEndDate(oldMemberGradeLog.getOldGradeEndDate());

                    MemberGradeLog memberGradeLog = new MemberGradeLog();
                    memberGradeLog.setLogId(sequenceWrapper.memberGradeLogSequence());
                    memberGradeLog.setMemberId(member.getMemberId());
                    memberGradeLog.setVenueId(cancelTrade.getVenueId());
                    memberGradeLog.setTradeId(cancelTrade.getTradeId());
                    memberGradeLog.setOldGradeId(oldMemberGradeLog.getGradeId());
                    memberGradeLog.setOldGradeStartDate(oldMemberGradeLog.getGradeStartDate());
                    memberGradeLog.setOldGradeEndDate(oldMemberGradeLog.getGradeEndDate());
                    memberGradeLog.setGradeId(oldMemberGradeLog.getOldGradeId());
                    memberGradeLog.setGradeStartDate(oldMemberGradeLog.getOldGradeStartDate());
                    memberGradeLog.setGradeEndDate(oldMemberGradeLog.getOldGradeEndDate());
                    memberGradeLog.setGradeChangeType(Constants.GradeChangeType.DOWNGRADING);
                    memberGradeLog.setGradeChangeMethod(Constants.GradeChangeMethod.AUTO);
                    memberGradeLog.setUpdateTime(new Date());
                    memberGradeLog.setCenterId(cancelTrade.getCenterId());
                    memberGradeLog.setCustId(oldMemberGradeLog.getCustId());
                    memberGradeLog.setUpdateStaffId(cancelTrade.getTradeStaffId());
                    memberGradeLogMapper.insert(memberGradeLog);
                }
            }
        }

        if (isNewMember) { // 新增会员，删除会员信息
            memberMapper.deleteByPrimaryKey(member.getMemberId());
        } else { // 老会员，更新会员信息
            memberMapper.updateByPrimaryKey(member);
        }
        return new ServiceResult();
    }

    private static boolean isEqual(Object object1, Object object2) {
        if (object1 != null) {
            return object1.equals(object2);
        } else {
            return object2 == null;
        }
    }
}
