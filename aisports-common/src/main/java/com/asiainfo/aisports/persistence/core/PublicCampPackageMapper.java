package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.PublicCampPackage;
import java.util.List;

import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

public interface PublicCampPackageMapper {
    @Delete({
        "delete from public_camp_package",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into public_camp_package (id, camp_id, ",
        "name, description, ",
        "price, state, camp_items, ",
        "create_time, create_staff_id, ",
        "update_time, update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{campId,jdbcType=BIGINT}, ",
        "#{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, ",
        "#{price,jdbcType=INTEGER}, #{state,jdbcType=CHAR}, #{campItems,jdbcType=VARCHAR}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(PublicCampPackage record);

    @Select({
        "select",
        "id, camp_id, name, description, price, state, camp_items, create_time, create_staff_id, ",
        "update_time, update_staff_id",
        "from public_camp_package",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="camp_id", property="campId", jdbcType=JdbcType.BIGINT),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
        @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="camp_items", property="campItems", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    PublicCampPackage selectByPrimaryKey(Long id);

    @Update({
        "update public_camp_package",
        "set camp_id = #{campId,jdbcType=BIGINT},",
        "name = #{name,jdbcType=VARCHAR},",
        "description = #{description,jdbcType=VARCHAR},",
        "price = #{price,jdbcType=INTEGER},",
        "state = #{state,jdbcType=CHAR},",
        "camp_items = #{campItems,jdbcType=VARCHAR},",
        "create_time = #{createTime,jdbcType=TIMESTAMP},",
        "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
        "update_time = #{updateTime,jdbcType=TIMESTAMP},",
        "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(PublicCampPackage record);

    @Select({
        "<script>",
        "select",
        "id, camp_id, name, description, price, state, camp_items, create_time, create_staff_id, update_time, update_staff_id",
        "from public_camp_package",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"campId != null\">and camp_id = #{campId,jdbcType=BIGINT}</if>",
        "<if test=\"name != null\">and name = #{name,jdbcType=VARCHAR}</if>",
        "<if test=\"description != null\">and description = #{description,jdbcType=VARCHAR}</if>",
        "<if test=\"price != null\">and price = #{price,jdbcType=INTEGER}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"campItems != null\">and camp_items = #{campItems,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="camp_id", property="campId", jdbcType=JdbcType.BIGINT),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
        @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="camp_items", property="campItems", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PublicCampPackage> selectByFields(PublicCampPackage param);

    @Update({
        "<script>",
        "update public_camp_package",
        "<set>",
        "<if test=\"campId != null\">camp_id = #{campId,jdbcType=BIGINT},</if>",
        "<if test=\"name != null\">name = #{name,jdbcType=VARCHAR},</if>",
        "<if test=\"description != null\">description = #{description,jdbcType=VARCHAR},</if>",
        "<if test=\"price != null\">price = #{price,jdbcType=INTEGER},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"campItems != null\">camp_items = #{campItems,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(PublicCampPackage record);

    @Select({
            "<script>",
            "select",
            "id, camp_id, name, description, price, state, camp_items, create_time, create_staff_id, update_time, update_staff_id",
            "from public_camp_package",
            "where camp_id = #{campId,jdbcType=BIGINT}",
            "and state = '1'",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="camp_id", property="campId", jdbcType=JdbcType.BIGINT),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="camp_items",property="campItems", jdbcType = JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PublicCampPackage> getListByCampId(@Param("campId") Long campId);

    /**
     * 根据campId来获取套餐集合(未删除)
     * @param campId
     * @return
     */
    @Select({
            "<script>",
            "select",
            "id, camp_id, name, description, price, state, camp_items, create_time, create_staff_id, update_time, update_staff_id",
            "from public_camp_package",
            "<where>",
            "camp_id = #{campId,jdbcType=BIGINT}",
            "and state != '2'",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="camp_id", property="campId", jdbcType=JdbcType.BIGINT),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="camp_items", property="campItems", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PublicCampPackage> getByCampId(@Param("campId") Long campId);

    @Select({
            "<script>",
            "select",
            "id, camp_id, name, description, price, state, camp_items, create_time, create_staff_id, update_time, update_staff_id",
            "from public_camp_package",
            "<where>",
            "camp_id = #{campId,jdbcType=BIGINT}",
            "and find_in_set(#{campItemId,jdbcType=BIGINT}, camp_items)",
            "and state != '2'",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="camp_id", property="campId", jdbcType=JdbcType.BIGINT),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="camp_items", property="campItems", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PublicCampPackage> selectByCampItemId(@Param("campId") Long campId, @Param("campItemId") Long campItemId);
}