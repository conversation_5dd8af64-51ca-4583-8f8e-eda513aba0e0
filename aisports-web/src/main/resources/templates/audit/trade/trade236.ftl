<#include "./trade.ftl">
<#if pageData.tradeFreezeCardList??>
<#list pageData.tradeFreezeCardList as tradeFreezeCard>
    <div class="cards">
        <div class="card-name">
            <span class="des">产品名称</span><span class="l-content">${tradeFreezeCard.productName!''}</span>
        </div>
        <div class="card-refund">
            <span class="des">冻卡月数</span><span class="l-content">${tradeFreezeCard.frozeMonth!''}个月</span>
        </div>
        <#if tradeFreezeCard.frozeDays?? && tradeFreezeCard.frozeDays != '0'>
            <div class="card-refund">
                <span class="des">冻卡天数</span><span class="l-content">${tradeFreezeCard.frozeDays!''}天</span>
            </div>
        </#if>
        <div class="card-refund">
            <span class="des">冻卡时间</span><span class="l-content">${tradeFreezeCard.startDate!""} 至 ${tradeFreezeCard.endDate!""}</span>
        </div>
        <div class="card-refund">
            <span class="des">冻卡前效期</span><span class="l-content">${tradeFreezeCard.cardStartDate!""} 至 ${tradeFreezeCard.cardEndDate!""}</span>
        </div>
        <div class="card-refund">
            <span class="des">冻卡规则</span><span class="l-content">${tradeFreezeCard.ruleName!""}</span>
        </div>
        <div class="card-refund">
            <span class="des">手续费</span><span class="l-content">${pageData.auditTaskInfo.fee!"0"}元</span>
        </div>

<#--        <div class="checkinfo">-->
<#--            <a class="my-check" data-value="${ossUrl}${(tradeFreezeCard.imgUrl)!''}">查看证明文件</a>-->
<#--        </div>-->
        <#if pageData.originalOrderPayMode?? && pageData.originalOrderPayMode != ''>
            <div class="card-name">
                <span class="des">支付信息</span><span class="l-content">${pageData.originalOrderPayMode!''}</span>
            </div>
        </#if>
    </div>
</#list>
</#if>
