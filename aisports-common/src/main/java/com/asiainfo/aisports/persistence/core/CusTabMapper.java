package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.CusAutoParam;
import com.asiainfo.aisports.domain.core.CusAutoParamDetail;
import com.asiainfo.aisports.domain.core.CusEcardTab;
import com.asiainfo.aisports.domain.core.CusTab;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

public interface CusTabMapper {
    @Delete({
        "delete from cus_tab",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into cus_tab (id, tab_name, ",
        "state,tag_type, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id, use_num, ",
        "color, sort, venue_id)",
        "values (#{id,jdbcType=BIGINT}, #{tabName,jdbcType=VARCHAR}, ",
        "#{state,jdbcType=CHAR},#{tagType,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT}, #{useNum,jdbcType=INTEGER}, ",
        "#{color,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{venueId,jdbcType=BIGINT})"
    })
    int insert(CusTab record);

    @Insert({
            "insert into cus_auto_param (id, tab_name, ",
            "reg_start_time,reg_end_time,",
            "join_start_time, join_end_time, ",
            "due_start_time, due_end_time, ",
            "mem_status_id,mem_status_name)",
            "values (#{id,jdbcType=BIGINT}, #{tabName,jdbcType=VARCHAR}, ",
            "#{regStartTime,jdbcType=TIMESTAMP},#{regEndTime,jdbcType=TIMESTAMP}, ",
            "#{joinStartTime,jdbcType=TIMESTAMP},#{joinEndTime,jdbcType=TIMESTAMP}, ",
            "#{dueStartTime,jdbcType=TIMESTAMP},#{dueEndTime,jdbcType=TIMESTAMP}, ",
            "#{memStatusId,jdbcType=INTEGER}, #{memStatusName,jdbcType=VARCHAR})"
    })
    int insertAutoParam(CusAutoParam record);

    @Insert({
            "insert into cus_auto_param_detail (detail_id,title_id, title_name, ",
            "service_id,service_name,",
            "type_id, type_name, ",
            "start_time, end_time, ",
            "min_num,max_num)",
            "values (#{detailId,jdbcType=BIGINT},#{titleId,jdbcType=INTEGER}, #{titleName,jdbcType=VARCHAR}, ",
            "#{serviceId,jdbcType=INTEGER},#{serviceName,jdbcType=VARCHAR}, ",
            "#{typeId,jdbcType=INTEGER},#{typeName,jdbcType=VARCHAR}, ",
            "#{startTime,jdbcType=TIMESTAMP},#{endTime,jdbcType=TIMESTAMP}, ",
            "#{minNum,jdbcType=INTEGER},#{maxNum,jdbcType=INTEGER})"
    })
    int insertAutoParamDetail(CusAutoParamDetail record);

    @Select({
        "select",
        "id, tab_name, state, tag_type ,create_time, create_staff_id, update_time, update_staff_id, ",
        "use_num, color, sort, venue_id",
        "from cus_tab",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="tab_name", property="tabName", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="tag_type", property="tagType", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="use_num", property="useNum", jdbcType=JdbcType.INTEGER),
        @Result(column="color", property="color", jdbcType=JdbcType.VARCHAR),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT)
    })
    CusTab selectByPrimaryKey(Long id);

    @Select({
            "select",
            "id,reg_start_time,reg_end_time,join_start_time,join_end_time,",
            "due_start_time,due_end_time,mem_status_id,mem_status_name",
            "from cus_auto_param",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="reg_start_time", property="regStartTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="reg_end_time", property="regEndTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="join_start_time", property="joinStartTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="join_end_time", property="joinEndTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="due_start_time", property="dueStartTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="due_end_time", property="dueEndTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="mem_status_id", property="memStatusId", jdbcType=JdbcType.BIGINT),
            @Result(column="mem_status_name", property="memStatusName", jdbcType=JdbcType.VARCHAR),
    })
    CusAutoParam selectParamByPrimaryKey(Long id);

    @Select({
            "select",
            "detail_id, title_id, title_name, service_id, service_name, type_id, type_name, start_time, end_time, min_num,max_num",
            "from cus_auto_param_detail",
            "where detail_id=#{detailId,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="detail_id", property="detailId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="title_id", property="titleId", jdbcType=JdbcType.BIGINT),
            @Result(column="title_name", property="titleName", jdbcType=JdbcType.VARCHAR),
            @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
            @Result(column="service_name", property="serviceName", jdbcType=JdbcType.VARCHAR),
            @Result(column="type_id", property="typeId", jdbcType=JdbcType.BIGINT),
            @Result(column="type_name", property="typeName", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_time", property="startTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_time", property="endTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="min_num", property="minNum", jdbcType=JdbcType.BIGINT),
            @Result(column="max_num", property="maxNum", jdbcType=JdbcType.BIGINT),
    })
    List<CusAutoParamDetail> selectParamMoreByPrimaryKey(Long detailId);

    @Update({
        "update cus_tab",
        "set tab_name = #{tabName,jdbcType=VARCHAR},",
          "state = #{state,jdbcType=CHAR},",
          "tag_type = #{tagType,jdbcType=CHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT},",
          "use_num = #{useNum,jdbcType=INTEGER},",
          "color = #{color,jdbcType=VARCHAR},",
          "sort = #{sort,jdbcType=INTEGER},",
          "venue_id = #{venueId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(CusTab record);

    @Select({
        "<script>",
        "select",
        "id, tab_name, state, tag_type , create_time, create_staff_id, update_time, update_staff_id, use_num, color, sort, venue_id",
        "from cus_tab",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"tabName != null\">and tab_name = #{tabName,jdbcType=VARCHAR}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"tagType != null\">tag_type = #{tagType,jdbcType=CHAR},</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"useNum != null\">and use_num = #{useNum,jdbcType=INTEGER}</if>",
        "<if test=\"color != null\">and color = #{color,jdbcType=VARCHAR}</if>",
        "<if test=\"sort != null\">and sort = #{sort,jdbcType=INTEGER}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="tab_name", property="tabName", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="tag_type", property="tagType", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="use_num", property="useNum", jdbcType=JdbcType.INTEGER),
        @Result(column="color", property="color", jdbcType=JdbcType.VARCHAR),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT)
    })
    List<CusTab> selectByFields(CusTab param);

    @Update({
        "<script>",
        "update cus_tab",
        "<set>",
        "<if test=\"tabName != null\">tab_name = #{tabName,jdbcType=VARCHAR},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"tagType != null\">tag_type = #{tagType,jdbcType=CHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"useNum != null\">use_num = #{useNum,jdbcType=INTEGER},</if>",
        "<if test=\"color != null\">color = #{color,jdbcType=VARCHAR},</if>",
        "<if test=\"sort != null\">sort = #{sort,jdbcType=INTEGER},</if>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(CusTab record);

    @Select({
            "<script>",
            "select id, tab_name, state, tag_type ,create_time, create_staff_id, update_time, update_staff_id, ",
            "use_num, color, sort, venue_id",
            "from cus_tab",
            "<where>",
            "venue_id = #{venueId}",
            "<if test=\"startDate != null\">and create_time &gt;= #{startDate}</if>",
            "<if test=\"endDate != null\">and create_time &lt;= #{endDate}</if>",
            "<if test=\"tabName != null\">and tab_name like concat('%', #{tabName}, '%')</if>" ,
            "<if test=\"state != null\">and state = #{state}</if>" ,
            "<if test=\"tagType != null\">and tag_type = #{tagType}</if>" ,
            "</where>",
            "order by case",
            "WHEN `sort` IS NOT NULL THEN 0 ELSE 1 END, `sort` ASC, `create_time` DESC",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="tab_name", property="tabName", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="tag_type", property="tagType", jdbcType=JdbcType.CHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="use_num", property="useNum", jdbcType=JdbcType.INTEGER),
            @Result(column="color", property="color", jdbcType=JdbcType.VARCHAR),
            @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
            @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT)
    })
    List<CusTab> list(Map<String, Object> param, RowBounds rowBounds);

    @Select({
            "<script>",
            "select id, tab_name, state,tag_type, create_time, create_staff_id, update_time, update_staff_id, ",
            "use_num, color, sort, venue_id",
            "from cus_tab",
            "<where>",
            "state = '1' and tag_type = '0' and venue_id = #{centerId}",
            "<if test=\"tabName != null\">and tab_name like concat('%', #{tabName}, '%')</if>" ,
            "</where>",
            "order by case",
            "WHEN `sort` IS NOT NULL THEN 0 ELSE 1 END, `sort` ASC, `create_time` DESC",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="tab_name", property="tabName", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="tag_type", property="tagType", jdbcType=JdbcType.CHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="use_num", property="useNum", jdbcType=JdbcType.INTEGER),
            @Result(column="color", property="color", jdbcType=JdbcType.VARCHAR),
            @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
            @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT)
    })
    List<CusTab> listNoPage(@Param("tabName")String tabName, @Param("centerId") Long centerId);

    @Update({
            "UPDATE `cus_tab`  \n" +
                    "SET `use_num` = `use_num` + 1,  \n" +
                    "    `update_time` = now()  \n" +
                    "WHERE `id` = #{cusTabId}"
    })
    void updateCusTabUseNum(@Param("cusTabId") String cusTabId);

    @Select({
            "<script>",
            "select tab_name",
            "from cus_tab",
            "<where>",
            "id in",
            "<foreach item=\"serviceId\" collection=\"list\" separator=\",\" open=\"(\" close=\")\" index=\"\">" +
                    " #{serviceId}" +
                    "</foreach>" +
            "</where>",
            "</script>"
    })
    List<String> selectByIds(@Param("list")List<String> cusTabIds);
}