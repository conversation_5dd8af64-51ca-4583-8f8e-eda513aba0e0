<div class="block-info">
    <h2 class = "block-title">订单详情</h2>
    <div class="block-conent-list">
        <ul>
            <li>
                <div class="title">课程名称</div>
                <div class="content">
                    <p>${result.privateEnrollInfo.courseName!""}</p>
                </div>
            </li>
            <li>
                <div class="title">教练</div>
                <div class="content">
                   <p>${result.privateEnrollInfo.coachName!""}</p>
                </div>
            </li>
            <li>
                <div class="title">有效期</div>
                <div class="content">
                    <p>${result.privateEnrollInfo.startDate?string("yyyy/MM/dd")!""} - ${result.privateEnrollInfo.endDate?string("yyyy/MM/dd")!""}</p>
                </div>
            </li>
            <li>
                <div class="title">参加活动</div>
                <div class="content">
                    <p>${result.promName!""}</p>
                </div>
            </li>
            <li>
                <div class="title">支付详情</div>
                <div class="content">
                    <p>金额：¥${result.tradeInfo.shouldPay/100!""}</p>
                    <p>折扣：¥${(result.tradeInfo.shouldPay - result.tradeInfo.realPay)/100!""}</p>
                    <p>实付：¥${result.tradeInfo.realPay/100!""}</p>
                </div>
            </li>
            <li>
                <div class="title">支付方式</div>
                <div class="content">
                <#if result.tradePayLogList??>
                    <#list result.tradePayLogList as tradePayLog>
                        <p>${staticParam.getName("pay_mode_code",tradePayLog.payModeCode)}：¥${tradePayLog.realPay/100!""}</p>
                    </#list>
                </#if>
                </div>
            </li>
            <li>
                <div class="title">备注</div>
                <div class="content">
                    <p>${result.tradeInfo.remark!""}</p>
                </div>
            </li>
        </ul>
    </div>
</div>