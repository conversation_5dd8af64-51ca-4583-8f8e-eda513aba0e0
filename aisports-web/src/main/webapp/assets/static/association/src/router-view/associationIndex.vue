<template>
  <div class="title-box associationIndex">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClickTab">
      <el-tab-pane label="入会申请审核" name="first">
        <div class="search-association">
          <el-input
            v-model="mobileNum"
            placeholder="请输入申请用户手机号码"
          ></el-input>
          <el-button @click="searchMember" type="primary">查询</el-button>
          <div class="association-audit fr">
            <el-button v-if="audit" @click="batchReview">批量审核</el-button>
            <div class="association-audit" v-if="!audit">
              <el-button @click="handlePass" style="color: #54b755"
                >通过</el-button
              >
              <el-button @click="handleNopass" style="color: #f15232"
                >不通过</el-button
              >
              <el-button @click="batchReview">取消</el-button>
            </div>
          </div>
        </div>
        <div class="categoryList">
          <div>
            <el-table
              ref="multipleTable"
              :data="asMemberList"
              style="width: 100%"
              :header-cell-style="{ background: '#FAFAFA', color: '#333' }"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column type="index" label="序号"></el-table-column>
              <!-- <el-table-column prop="nickName" label="昵称"></el-table-column> -->
              <el-table-column
                prop="mobileNum"
                label="手机号码"
              ></el-table-column>
              <el-table-column prop="payTfee" label="支付会费(元)">
                <template slot-scope="scope">
                  {{ scope.row.payTfee | payFormat }}
                </template>
              </el-table-column>
              <el-table-column
                prop="createTime"
                sortable
                label="申请时间"
              ></el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <el-button @click="jumpDetail(scope.row, 2)" type="text"
                    >详情</el-button
                  >
                  <el-button @click="handleClick(scope.row)" type="text"
                    >通过</el-button
                  >
                  <el-button @click="delClick(scope.row)" type="text"
                    >不通过</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="会员管理" name="second">
        <div class="search-association">
          <el-input
            v-model="mobileNum"
            placeholder="请输入申请用户手机号码"
          ></el-input>
          <el-button @click="searchMember" type="primary">查询</el-button>
          <div class="association-audit fr">
            <el-button @click="batchExport">批量导出</el-button>
            <el-button @click="batchEdit" icon="el-icon-folder-add"
              >批量编辑</el-button
            >
            <!-- <el-button @click="bulkImport" icon="el-icon-download">批量导入</el-button> -->
            <el-button @click="addMember" icon="el-icon-plus"
              >新增会员</el-button
            >
            <!-- <div class="association-audit" v-if="!audit">
              <el-button @click="dialogVisible = true" style="color:#54B755;">通过</el-button>
              <el-button style="color:#F15232;">不通过</el-button>
              <el-button @click="batchReview">取消</el-button>
            </div>-->
          </div>
        </div>
        <div class="categoryList">
          <!-- 会员管理 -->
          <div>
            <p class="memberCount">当前会员数量:{{ total }}</p>
            <el-table
              ref="multipleTable1"
              :data="asAllMemberList"
              style="width: 100%"
              :header-cell-style="{ background: '#FAFAFA', color: '#333' }"
              @selection-change="handleMemberSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column type="index" label="序号"></el-table-column>
              <el-table-column prop="name" label="昵称"></el-table-column>
              <el-table-column
                prop="mobileNum"
                label="手机号码"
              ></el-table-column>
              <el-table-column
                prop="joinTime"
                label="加入协会时间"
              ></el-table-column>
              <el-table-column
                prop="payTime"
                sortable
                label="最近缴纳会费"
              ></el-table-column>
              <el-table-column
                prop="endDate"
                label="会员到期时间"
              ></el-table-column>
              <el-table-column prop="state" label="会员状态">
                <template slot-scope="scope">
                  <span
                    class="TextBg"
                    :class="[
                      scope.row.state == 1
                        ? 'normal'
                        : scope.row.state == 2
                        ? 'overdue'
                        : 'freeze',
                    ]"
                    >{{ scope.row.state | stateFormat }}</span
                  >
                </template>
              </el-table-column>
              <!-- //  0-无效 1-有效 2-待审核 3-未支付 -->
              <el-table-column label="操作" width="160">
                <template slot-scope="scope">
                  <el-button @click="jumpDetail(scope.row, 1)" type="text"
                    >详情</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <div class="page-bottom">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNo"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisiblePass"
      width="30%"
      :before-close="handleClose"
    >
      <span>确认通过审核吗？通过后将加入协会成为会员。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisiblePass = false">取 消</el-button>
        <el-button type="primary" @click="submitPass">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="确认拒绝?"
      :visible.sync="dialogVisibleNoPass"
      width="30%"
      :before-close="handleClosePass"
    >
      <el-form
        :model="noPassForm"
        ref="noPassForm"
        label-width="100px"
        :rules="noPassValidateForm"
        class="demo-ruleForm"
      >
        <el-form-item label="原因" prop="auditOpinion">
          <el-input
            type="text"
            v-model="noPassForm.auditOpinion"
            autocomplete="off"
            placeholder="请输入未通过原因"
          ></el-input>
        </el-form-item>
        <el-form-item class="bottom-fr">
          <el-button @click="resetNoPassForm('noPassForm')">重置</el-button>
          <el-button type="primary" @click="submitNoPassForm('noPassForm')"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 批量编辑 -->
    <el-dialog title="批量编辑" :visible.sync="dialogVisibleEdit" width="35%">
      <edit-form
        :selectlist="selectList"
        v-if="dialogVisibleEdit"
        @submitNoPassForm="showDialog"
      ></edit-form>
      <!-- <el-form
        :model="betchForm"
        ref="numberValidateForm"
        :rules="betchValidateForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="证件类型" prop="region">
          <el-select v-model="betchForm.region" placeholder="下拉选项">
            <el-option
      v-for="item in regionList"
      :key="item.value"
      :label="item.label"
      :value="item.value">
    </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="年龄"
          prop="age"
          :rules="[
            { required: true, message: '年龄不能为空' },
            { type: 'number', message: '年龄必须为数字值' },
          ]"
        >
          <el-input
            type="age"
            v-model.number="betchForm.age"
            autocomplete="off"
            placeholder="请输入年龄"
          ></el-input>
        </el-form-item>
        <el-form-item class="bottom-fr">
          <el-button @click="resetEditNoPassForm('betchForm')">取消</el-button>
          <el-button type="primary" @click="submitEditForm('betchForm')">提交</el-button>
        </el-form-item>
      </el-form>-->
    </el-dialog>
  </div>
  <!-- <el-input v-model="activeName" placeholder=""></el-input> -->
</template>
<script>
import { associationApi } from "../utils/api";
import EditForm from "./editForm.vue";
import { urlPrefix } from "aisportsConfig";

export default {
  data() {
    return {
      dialogVisiblePass: false,
      dialogVisibleEdit: false,
      mobileNum: "",
      pageNo: 1,
      pageSize: 10,
      total: 1,
      dialogVisibleNoPass: false,
      noPassForm: {
        auditOpinion: "",
        auditAgreeTag: 0,
        asMemberIdList: "",
      },
      audit: true,
      regionList: [],
      activeName: "first",
      Name: "",
      asMemberList: [],
      asMemberIdList: "",
      asAllMemberList: [],
      multipleSelection: [],
      memberSelect: [],
      selectList: [],
      betchValidateForm: {
        region: [{ required: true, message: "请选择", trigger: "change" }],
        age: [{ required: true, message: "请输入年龄", trigger: "blur" }],
      },
      noPassValidateForm: {
        auditOpinion: [
          { required: true, message: "请输入原因", trigger: "blur" },
        ],
      },
      numberValidateForm: {
        age: "",
      },
      betchForm: {
        region: "",
        age: "",
      },
    };
  },
  components: { EditForm },
  mounted() {
    this.getMemberList();
    this.getgetAssociationElementList();
  },
  filters: {
    payFormat(a) {
      if (a == undefined) {
        return "";
      } else {
        var num = Number(a);
        if (!num) {
          //等于0
          return num + ".00";
        } else {
          //不等于0
          num = Math.round(num * 100) / 10000;
          num = num.toFixed(2);
          num += ""; //转成字符串
          var reg =
            num.indexOf(".") > -1
              ? /(\d{1,3})(?=(?:\d{3})+\.)/g
              : /(\d{1,3})(?=(?:\d{3})+$)/g; //千分符的正则
          // console.log(num.indexOf('.')>-1)
          return num.replace(reg, "$1,"); //千分位格式化
        }
      }
    },
    stateFormat(state) {
      //  0-无效 1-有效 2-待审核 3-未支付
      switch (state) {
        case "0":
          state = "无效";
          break;
        case "1":
          state = "有效";
          break;
        case "2":
          state = "待审核";
          break;
        case "3":
          state = "未支付";
          break;
      }
      return state;
    },
  },
  methods: {
    showDialog(item) {
      console.log(item);
      this.dialogVisibleEdit = item;
    },
    //下拉框
    getgetAssociationElementList() {
      associationApi.getAssociationElementList().then((res) => {
        if (res.message == "ok") {
          this.regionList = res.associationElementList;
        }
      });
    },
    //批量导入
    bulkImport() {
      this.$router.push({ name: "bulkImport" });
    },
    //会员新增
    addMember() {
      this.$router.push({ name: "associationAdd" });
    },
    //详情跳转
    jumpDetail(row, type) {
      row = JSON.stringify(row);
      this.$router.push({
        name: "associationDetail",
        query: { row: row, type: type },
      });
    },
    //批量编辑
    batchEdit() {
      if (this.memberSelect.length > 0) {
        this.selectList = this.memberSelect;
        this.dialogVisibleEdit = true;
      } else {
        this.$message.error("请先选择至少一条数据!");
      }
    },
    //通过
    handlePass() {
      if (this.multipleSelection.length > 0) {
        let list = [];
        this.multipleSelection.forEach((item, index) => {
          list.push(item.id);
        });
        this.asMemberIdList = list.toString();
        this.dialogVisiblePass = true;
      } else {
        this.$message.error("请先选择至少一条数据!");
      }
    },
    //不通过
    handleNopass() {
      if (this.multipleSelection.length > 0) {
        this.dialogVisibleNoPass = true;
      } else {
        this.$message.error("请先选择至少一条数据!");
      }
    },
    submitPass() {
      let params = {
        auditAgreeTag: 1,
        asMemberIdList: this.asMemberIdList,
      };
      associationApi.auditAsMember(params).then((res) => {
        console.log(res);
        if (res.message == "ok") {
          this.dialogVisiblePass = false;
          this.searchMember();
        } else {
          this.$message.error("审核失败!");
        }
      });
    },
    handleClosePass() {
      this.$refs.noPassForm.resetFields();
      this.dialogVisibleNoPass = false;
    },
    //通过
    handleClick(row) {
      this.dialogVisiblePass = true;
      this.asMemberIdList = row.id;
      this.$refs.multipleTable.clearSelection();
      console.log(row);
    },
    //不通过
    delClick(row) {
      this.dialogVisibleNoPass = true;
      this.noPassForm.asMemberIdList = row.id;
      this.$refs.multipleTable.clearSelection();
    },
    //入会审核多选
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log(val);
    },
    //会员多选
    handleMemberSelectionChange(val) {
      this.memberSelect = val;
      console.log("会员多选", val);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      this.pageSize = val;
      if (this.activeName == "first") {
        this.getMemberList();
      } else {
        this.getAllMemberList();
      }
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      if (this.activeName == "first") {
        this.getMemberList();
      } else {
        this.getAllMemberList();
      }
      console.log(`当前页: ${val}`);
    },
    //获取入会申请审核列表
    getMemberList() {
      let params = {
        mobileNum: this.mobileNum,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        state: 2,
      };
      associationApi.queryAsMemberList(params).then((res) => {
        console.log(res);
        this.asMemberList = res.asMemberList.list;
        this.total = res.asMemberList.total;
      });
    },
    batchExport() {
      window.open(
        `${urlPrefix}/association/exportAsMemberList?mobileNum=${this.mobileNum}&pageNo=${this.pageNo}&pageSize=${this.pageSize}`
      );
    },
    //获取会员管理列表
    getAllMemberList() {
      let params = {
        mobileNum: this.mobileNum,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
      };
      associationApi.queryAsMemberList(params).then((res) => {
        console.log(res);
        this.asAllMemberList = res.asMemberList.list;
        this.total = res.asMemberList.total;
      });
    },
    //tab切换
    handleClickTab(tab, event) {
      if (tab.name == "second") {
        this.getAllMemberList();
      } else {
        this.getMemberList();
      }
    },
    //批量审核切换显示
    batchReview() {
      this.audit = !this.audit;
    },
    //查询
    searchMember() {
      this.pageNo = 1;
      if (this.activeName == "first") {
        this.getMemberList();
      } else {
        this.getAllMemberList();
      }
    },
    handleClose() {},
    //不通过提交
    submitNoPassForm(formName) {
      if (this.multipleSelection.length > 0) {
        let list = [];
        this.multipleSelection.forEach((item, index) => {
          list.push(item.id);
        });
        this.noPassForm.asMemberIdList = list.toString();
      }
      // console.log(this.multipleSelection, list, list.toString());
      this.$refs[formName].validate((valid) => {
        if (valid) {
          associationApi.auditAsMember(this.noPassForm).then((res) => {
            console.log(res);
            if (res.message == "ok") {
              this.dialogVisibleNoPass = false;
              this.$message.success("审核不通过成功!");
              this.$refs.noPassForm.resetFields();
              this.searchMember();
            } else {
              this.$message.error("审核失败!");
            }
          });
        } else {
          return false;
        }
      });
    },
    //批量编辑提交
    submitEditForm(formName) {
      console.log(this.memberSelect);
      let list = [];
      this.memberSelect.forEach((item, index) => {
        list.push({
          applyId: item.applyId,
          elementId: this.betchForm.region,
          value: this.betchForm.age,
        });
      });
      console.log(list);
      // this.$refs[formName].validate((valid) => {
      //   if (valid) {
      //     associationApi.editMemberInfo(this.betchForm).then((res) => {
      //       console.log(res);
      //       if (res.message == "ok") {
      //         this.dialogVisibleNoPass = false;
      //         this.searchMember();
      //       } else {
      //         this.$message.error("审核失败!");
      //       }
      //     });
      //   } else {
      //     return false;
      //   }
      // });
    },
    //不通过重置
    resetNoPassForm() {
      this.$refs.noPassForm.resetFields();
      this.dialogVisibleNoPass = false;
    },
    resetEditNoPassForm() {
      this.dialogVisibleEdit = false;
    },
  },
};
</script>