package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.PerformTeamTicketRecord;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;

/**
 * CREATE TABLE `perform_team_ticket_record` (
 *   `id` varchar(20) NOT NULL,
 *   `perform_ticket_id` varchar(20) NOT NULL,
 *   `send_num` int(11) NOT NULL COMMENT '发放数量',
 *   `state` varchar(1) DEFAULT NULL COMMENT '0-无效 1-有效',
 *   `create_time` datetime NOT NULL,
 *   `create_staff_id` bigint(20) NOT NULL,
 *   `update_time` datetime DEFAULT NULL,
 *   `update_staff_id` bigint(20) DEFAULT NULL,
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团体票发放信息';
 */
public interface PerformTeamTicketRecordMapper {

    @Delete({
        "delete from perform_team_ticket_record",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into perform_team_ticket_record (id, perform_ticket_id, send_num, ",
        "state, create_time, create_staff_id, ",
        "update_time, update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{performTicketId,jdbcType=VARCHAR}, #{sendNum,jdbcType=INTEGER}, ",
        "#{state,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(PerformTeamTicketRecord record);

    @Select({
            "select",
            "id, perform_ticket_id, send_num, state, create_time, create_staff_id, update_time, ",
            "update_staff_id",
            "from perform_team_ticket_record",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType= JdbcType.BIGINT, id=true),
            @Result(column="perform_ticket_id", property="performTicketId", jdbcType=JdbcType.VARCHAR),
            @Result(column="send_num", property="sendNum", jdbcType=JdbcType.INTEGER),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
    })
    PerformTeamTicketRecord selectByPrimaryKey(Long id);

    @Update({
            "update perform_team_ticket_record",
            "set perform_ticket_id = #{performTicketId,jdbcType=VARCHAR},",
            "send_num = #{sendNum,jdbcType=INTEGER},",
            "state = #{state,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(PerformTeamTicketRecord record);

    @Select({
            "<script>",
            "select",
            "id, perform_ticket_id, send_num, state, create_time, create_staff_id, update_time, ",
            "update_staff_id",
            "from perform_team_ticket_record",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"performTicketId != null\">and perform_ticket_id = #{performTicketId,jdbcType=VARCHAR}</if>",
            "<if test=\"sendNum != null\">and send_num = #{sendNum,jdbcType=INTEGER}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    List<PerformTeamTicketRecord> selectByFields(PerformTeamTicketRecord param);

    @Update({
            "<script>",
            "update perform_team_ticket_record",
            "<set>",
            "<if test=\"performTicketId != null\">perform_ticket_id = #{performTicketId,jdbcType=VARCHAR},</if>",
            "<if test=\"sendNum != null\">send_num = #{sendNum,jdbcType=INTEGER},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</set>",
            "where id = #{id,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(PerformTeamTicketRecord record);

    @Select({
            "<script>",
            "select",
            "a.id, a.perform_ticket_id, a.send_num, a.create_time, b.name perform_ticket_name, b.team_name, c.name stock_name, d.staff_name, e.trade_id, f.name perform_name",
            "from perform_team_ticket_record a",
            "left join perform_new_ticket b on a.perform_ticket_id = b.id",
            "left join perform_stock c on b.stock_id = c.id",
            "left join perform f on b.perform_id = f.id and f.id = #{performId,jdbcType=VARCHAR}",
            "left join staff d on a.create_staff_id = d.staff_id",
            "left join trade_extra g on g.extra_key = 'perform_team_ticket_send_id' and g.value = a.id",
            "left join trade e on e.trade_id = g.trade_id",
            "<where>",
            "<if test=\"stockId != null\">and c.id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"keyword != null and keyword != ''\">and (b.name like #{keyword,jdbcType=VARCHAR} or b.team_name like #{keyword,jdbcType=VARCHAR})</if>",
            "<if test=\"startDate != null\">and a.create_time &gt;= #{startDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"endDate != null\">and a.create_time &lt;= #{endDate,jdbcType=TIMESTAMP}</if>",
            "</where>",
            "order by a.create_time desc",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="perform_ticket_id", property="performTicketId", jdbcType=JdbcType.VARCHAR),
            @Result(column="send_num", property="sendNum", jdbcType=JdbcType.INTEGER),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="perform_ticket_name", property="performTicketName", jdbcType=JdbcType.VARCHAR),
            @Result(column="team_name", property="teamName", jdbcType=JdbcType.VARCHAR),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="staff_name", property="staffName", jdbcType=JdbcType.VARCHAR),
            @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
            @Result(column="perform_name", property="performName", jdbcType=JdbcType.VARCHAR)
    })
    List<DataMap> selectPageList(@Param("performId") String performId,
                                 @Param("stockId") Long stockId,
                                 @Param("keyword") String keyword,
                                 @Param("startDate") Date startDate,
                                 @Param("endDate") Date endDate,
                                 RowBounds rowBounds);

    @Select({
            "<script>",
            "select",
            "a.id, a.perform_ticket_id, a.send_num, a.create_time, b.name perform_ticket_name, ",
            "b.team_name, c.name stock_name, e.trade_id, f.name perform_name, g.name site_name, e.pay_tfee/a.send_num price,",
            "ifnull((select group_concat(ptr.rights_id) from perform_ticket_rights ptr where ptr.perform_ticket_id = b.id and now() between ptr.start_date and ptr.end_date), '') as rights_ids",
            "from perform_team_ticket_record a",
            "left join perform_new_ticket b on a.perform_ticket_id = b.id",
            "left join perform_stock c on b.stock_id = c.id",
            "left join perform f on b.perform_id = f.id",
            "left join site g on b.site_id = g.id",
            "left join trade_extra h on h.extra_key = 'perform_team_ticket_send_id' and h.value = a.id",
            "left join trade e on e.trade_id = h.trade_id",
            "where a.id = #{recordId,jdbcType=BIGINT}",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="perform_ticket_id", property="performTicketId", jdbcType=JdbcType.VARCHAR),
            @Result(column="send_num", property="sendNum", jdbcType=JdbcType.INTEGER),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="perform_ticket_name", property="performTicketName", jdbcType=JdbcType.VARCHAR),
            @Result(column="team_name", property="teamName", jdbcType=JdbcType.VARCHAR),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
            @Result(column="perform_name", property="performName", jdbcType=JdbcType.VARCHAR),
            @Result(column="site_name", property="siteName", jdbcType=JdbcType.VARCHAR),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="rights_ids", property="rightsIds", jdbcType=JdbcType.VARCHAR)
    })
    DataMap selectById(@Param("recordId") String recordId);
}
