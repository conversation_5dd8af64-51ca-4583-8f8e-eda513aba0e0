<template>
    <basic-modal
        :visible="value"
        :on-request-close="handleModalClose"
        :on-confirm="handleModalConfirm"
        title="预约取消"
    >
        <div
            ref="form"
            class="form-wrap">
            <div class="cline">
                <label>取消原因：</label>
                <g-select
                    v-model="modalData.cancelCode"
                    :options="cancelVisitList"></g-select>
            </div>
            <div class="cline-s">
                <label>备注：</label>
                <textarea
                    v-model="modalData.reason"
                    required
                    placeholder="请输入说明信息"></textarea>
            </div>
        </div>
    </basic-modal>
</template>

<script>
import Modal from 'vue-components/Modal.vue'
import BasicModal from 'vue-components/BasicModal'
import GSelect from 'vue-components/GSelect.vue'
import Checkbox from 'vue-components/Checkbox.vue'
import _ from 'lodash'
import 'xValidate'
import * as service from './service'

// events: submit, input
export default {
    components: {
        'basic-modal': BasicModal,
        'g-select': GSelect,
        checkbox: Checkbox,
    },
    // use value for modal show
    props: ['initialData', 'value', 'siteOptions'],
    data() {
        return {
            cancelVisitList: [],
            modalData: {
                reserveId: '',
                cancelCode: '',
                reason: '',
            },
        }
    },
    mounted() {
        this.modalData = _.assign({}, this.modalData, this.initialData)
        service.getInitData((data) => {
            this.cancelVisitList = data.cancelVisitList.map(d => ({
                value: d.paramKey,
                name: d.paramValue,
            }))
        })
    },
    methods: {
        handleModalClose() {
            this.$emit('input', false)
        },
        handleModalConfirm() {
            if (!$(this.$refs.form).validate()) {
                return false
            }
            this.$emit('submit', this.modalData)
        },
    },
}
</script>
