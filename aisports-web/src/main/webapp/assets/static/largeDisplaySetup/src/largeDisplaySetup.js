const showBeautifulConfirmBox = require('beautifulConfirmBox');
const $ = require('jquery');
const _ = require('underscore');
const mAlert = require('mAlert');
const settings = require('aisportsConfig');
const Vue = require('vue')
const FileUploader = require('vue-components/FileUploader.vue')
var photoSwipe = require('libs/photoSwipe/photoSwipeForSea');
import { Drawer,Table,TableColumn,Dialog,Button,Input} from 'element-ui-2.15.5'
Vue.use(Drawer);
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(Dialog);
Vue.use(Button);
Vue.use(Input);

require('textareaCount');
require('xValidate');
require('frame');
require('ajaxUpload');

const plainTextFormLineTmpl = _.template($('#plaintextFormLineTmpl').html());
function renderPlainTextFormLine(name, type, content, key, required) {
    if (type === 'images' || type === 'mp4') {
        content = content || [];
    }
    const $line = $(plainTextFormLineTmpl({
        name,
        type,
        content,
        key,
        required,
    }));
    if (type === 'textarea') {
        $line.find('.remark').textareaCount({
            remainder: '.lyishu',
            total: '.lsheng',
        });
    }
    return $line;
}

const poolManage = new Vue({
    el: '#vue-content-add',
    data: {
        drawer: false,
        tableData:[],
        dialogVisible:false,
        inputName:'',
        subVenueList:{
            subVenueName:'',
            venueld:'',
            childVenueSub:'',
        },
        activeId:'',
        subVenueId:'',
        isEdit:false,
        sort:'',
        editsubVenueId:'',
    },
    components: {
    },
    computed: {
        title(){
            return this.isEdit?'修改泳池':'添加泳池'
        }
    },
    methods: {
        handleClose() {
            this.drawer = false;

        },
        showDrawer() {
            this.drawer = true;
            console.log(this.drawer,'vue-content-add')
        },
        showDialog(){
            this.dialogVisible = true;
            this.inputName = ''
            this.sort = ''
            console.log(this.dialogVisible,'vue-content-add')

        },
        cancleDialog(){
            this.dialogVisible = false
            this.isEdit = false

        },
        deletePool(item){
            console.log('删除')
            this.drawer = false

            showBeautifulConfirmBox(()=> {
                $.get('/largeDisplaySetUp/delete', {
                    subVenueId:item.subVenueId
                }, (res) => {
                    if (res.error === 0) {
                        fetchTabItem($('#page-tabs').find('li.cur'));
                    }
                });
            }, {
                content: `确认删除${item.subVenueName}？删除后将不可恢复`,
            }
            )
            // $.get('/largeDisplaySetUp/delete', {
            //     subVenueId:item.subVenueId
            // }, (res) => {
            //     if (res.error === 0) {
            //         fetchTabItem($('#page-tabs').find('li.cur'));
            //     }
            // });
        },
        editPool(row){
            this.isEdit = true
            this.dialogVisible = true;
            this.inputName = row.subVenueName
            this.editsubVenueId = row.subVenueId
            this.sort = row.sort
        },
        getData(subVenueId){
            this.subVenueId = subVenueId
            $.get('/largeDisplaySetUp/getDisplayInfo', {
                subVenueId,
            }, (res) => {
                try {
                    const $qtWrap = $('#setup-qts');
                    $qtWrap.html('');
                if (res.error == 0) {
                    $qtWrap.append(renderTabItem(res));
                } else {
                    mAlert.showWrong('该场馆没有配置信息!');
                }
                } catch (error) {
                    console.log(error,'errror')
                }
                
            });
            $('.wait-cover').hide()
        },
        submit(){
            let tabItem = $('#page-tabs').find('li.cur')
            const subVenueId = tabItem.attr('sub-venue-id');
            let param ={
                subVenueId,
                subVenueName: this.inputName,
                sort:this.sort || ''
            }
            console.log(this.isEdit,'isEdit')
            if(this.isEdit){
                param.subVenueId = this.editsubVenueId
                param.type = '1'
                this.isEdit = false
            }
            $.post('/largeDisplaySetUp/saveOrUpdateSubVenue', param, (res) => {
                if (res.error === 0) {
                    this.dialogVisible = false;
                    this.drawer = false;
                    fetchTabItem($('#page-tabs').find('li.cur'));
                }});
            
        }
    },
});
// ajaxFileUpload的iframe上传方法在ie11玄学跨域，改用vue
// 每次renderPlainTextFormLine都要mountVueOnInput
function mountVueOnInput() {
    const imgTmpl = _.template($('#imageDisplayTmpl').html());
    let vm = new Vue({
        el: '.sub-box-photo',
        components: {
            'file-uploader': FileUploader,
        },
        methods: {
            scanImgs() {
            },
            batchDeleteImg() {
                let filePathList = []
                $('#image-upload-container li').each(function () {
                    let checkEl = $(this).find('.icon-gou');
                    if (checkEl && checkEl.hasClass('checked')) {
                        filePathList.push($(this).data('src'))
                    }
                });
                if(!filePathList.length){
                    mAlert.showWarning('请选中图片');
                    return
                }
                showBeautifulConfirmBox(()=> {
                    $.post('/largeDisplaySetUp/removeFiles', {
                        filePaths: filePathList.join(',')
                    }, (res) => {
                        if (res.error === 0) {
                            $('#image-upload-container li').each(function () {
                                let checkEl = $(this).find('.icon-gou');
                                if (checkEl && checkEl.hasClass('checked')) {
                                    $(this).remove()
                                }
                            });
                            mAlert.showSuccess('删除成功');
                            saveResult();
                        } else {
                            mAlert.showWarning(res.message);
                        }
                    });

                }, {
                    content: '确认删除选中的图片吗？删除后将不可恢复',
                });
            },
            handleSuccess(res) {
                if (res.error == 0) {
                    $('#image-upload-container').append(imgTmpl({
                        images: res.filePaths,
                        ossUrl: $('#baseUrl').val(),
                    }));
                } else {
                    settings.spinner.showErrorResult(res.message);
                }
            },
            handleError() {
            }
        }
    })
}

function savePlainText($container, content, value) {
    const preview = $container.find('.setup-item-plain-text').data('preview');
    let html;
    console.log($container, content, value, preview)
    if (!preview) {
        html = value;
    } else if (preview === 'images') {
        html = `<img src="${$('#baseUrl').val()}${content[preview][0]}" width="80" height="44">`;
    } else if (preview === 'videos' || preview === 'mp4') {
        html = `<video src="${$('#baseUrl').val()}${content[preview][0]}" width="80" height="44">`;
    } else {
        html = content[preview];
    }
    $container
        .find('.setup-item-plain-text')
        .data('value', content)
        .html(html);
}

function handleEditPlainText() {
    $(document).on('click', '[data-click="edit-plain-text"]', function () {
        const $container = $(this).closest('.setup-item');

        const extra = $container.find('.setup-item-plain-text').data('extra');
        const content = $container.find('.setup-item-plain-text').data('value');

        let plaineTextForm;
        if ($.isArray(extra)) {
            plaineTextForm = extra.map(extraItem => renderPlainTextFormLine(extraItem.name, extraItem.type, content[extraItem.code], extraItem.code, extraItem.required));
        } else {
            plaineTextForm = renderPlainTextFormLine(extra.name, extra.type, content, extra.key, extra.required);
        }

        $('#setup-items-page').hide();
        $('#setup-plaintext-form').find('.form-line').remove();
        $('#setup-plaintext-form')
            .data('elem', $container)
            .find('.setup-plaintext-save')
            .before(plaineTextForm)
            .end()
            .show();
        mountVueOnInput()
    });

    $(document).on('click', '[data-click="setup-plaintext-return"]', () => {
        $('#setup-plaintext-form').hide();
        $('#setup-items-page').show();
        fetchTabItem($('#page-tabs').find('li.cur'));
    });

    $(document).on('click', '[data-click="setup-plaintext-save"]', () => {
        saveResult('save');
    });
}

function getLineVal($line) {
    if ($line.attr('type') === 'images') {
        return $line.find('.image-container:not(.image-upload-container)').map(function () {
            return $(this).data('src');
        }).toArray()
    }
    if ($line.attr('type') === 'mp4') {
        return $line.find('.image-container:not(.image-upload-container)').map(function () {
            return $(this).data('src');
        }).toArray()
    }

    return $line.find('.field').val()
}

function saveResult(tag) {
    if (!$('#setup-plaintext-form').validate()) {
        return;
    }
    const $container = $('#setup-plaintext-form').data('elem');
    const subVenueId = $container.data('sub-venue-id');
    const contCode = $container.data('contcode');
    let content = {};
    const value = getLineVal($('#setup-plaintext-form').find('.form-line').first());
    if ($('#setup-plaintext-form').find('.form-line').length === 1) {
        content = getLineVal($('#setup-plaintext-form').find('.form-line'));
    } else {
        $('#setup-plaintext-form').find('.form-line').each(function () {
            const $this = $(this);

            content[$this.data('key')] = getLineVal($this);
        });
    }
    if (contCode.indexOf('custom_video') >= 0) {
        const videoCon = { videos: '', mp4: '' }
        videoCon.videos = content
        videoCon.mp4 = content
        content = videoCon
    }
    $.post('/largeDisplaySetUp/updateValue', {
        subVenueId,
        contCode,
        value: typeof content === 'string' ? content : JSON.stringify(content),
    }, (res) => {
        if(res.error == 0){
            mAlert.showSuccess('保存成功');
            setTimeout(() => {
                if (tag === 'save') {
                    $('#setup-plaintext-form').hide();
                    $('#setup-items-page').show();
                }
                savePlainText($container, content, value);
            }, 1000);
        }else{
            mAlert.showWrong(res.message);
        }
    });
}

function handleEditUnitText() {
    // 切换到编辑
    $(document).on('click', '[data-click="edit-setup-item"]', function () {
        const $container = $(this).closest('.setup-item');

        const unit = $container.find('.setup-item-unit-text').data('unit');
        const value = $container.find('.setup-item-unit-text').data('value');
        const required = $container.find('.setup-item-unit-text').data('required');

        const editTmpl = _.template($('#toEditUnitContentTmpl').html());

        let $input;

        $container.find('.edit-btns').html($('#saveEditUnitBtnsTmpl').html());
        $container.find('.setup-item-unit-text').html(editTmpl({
            unit,
            value,
            required,
        }));

        $input = $container.find('input')

        $input.focus();
        $input[0].select();
    });

    function renderEditSave($container, unit, value) {
        const elementTmpl = _.template($('#saveEditUnitContentTmpl').html());

        $container.find('.edit-btns').html($('#toEditUnitBtnsTmpl').html());
        $container.find('.setup-item-unit-text').html(elementTmpl({
            unit,
            value,
        })).data('value', value);
    }

    $(document).on('click', '[data-click="edit-setup-cancel"]', function () {
        const $container = $(this).closest('.setup-item');

        const unit = $container.find('.setup-item-unit-text').data('unit');
        const value = $container.find('.setup-item-unit-text').data('value');

        renderEditSave($container, unit, value);
    });

    $(document).on('click', '[data-click="edit-setup-save"]', function () {
        const $container = $(this).closest('.setup-item');
        if (!$container.validate()) {
            return;
        }

        const subVenueId = $container.data('sub-venue-id');
        const contCode = $container.data('contcode');

        const unit = $container.find('.setup-item-unit-text').data('unit');
        const value = $container.find('input').val();

        $.post('/largeDisplaySetUp/updateValue', {
            subVenueId,
            contCode,
            value,
        }, () => {
            renderEditSave($container, unit, value);
        });
    });
}

const qtTmpl = _.template($('#setupQtTmpl').html());
const itemTmpl = _.template($('#setupItemTmpl').html());
function renderTabItem(data) {
    const qts = data.resultList.map((qt) => {
        const $html = $(qtTmpl(qt));

        const $bd = $html.find('.setup-qtbd');
        qt.data.forEach((item) => {
            const extraInfo = JSON.parse(item.extra);
            item.extraInfo = extraInfo;
            item.preview = item.preview || '';
            if ($.isArray(extraInfo)) {
                item.type = 'textarea';
                item.contentType = 'array';
                item.value = item.value || '{}';
                item.contentObject = JSON.parse(item.value);
                item.content = item.preview ? item.contentObject[item.preview] : item.contentObject[extraInfo[0].code];
                if (item.preview === 'images') {
                    item.content = item.content || [];
                }
                if (item.preview === 'mp4' || item.preview === 'videos') {
                    item.content = item.content || [];
                }
            } else if (extraInfo.type === 'textarea') {
                item.type = 'textarea';
                item.contentType = 'text';
                item.value = item.value || '';
                item.content = item.value;
                item.contentObject = item.value;
                extraInfo.key = item.contCode;
            } else {
                item.type = 'text';
                item.contentType = 'text';
                item.value = item.value || '';
                item.content = item.value;
                item.unit = extraInfo.unit || '';
            }

            const itemHtml = itemTmpl(item);
            $bd.append(itemHtml);
        });
        return $html;
    });

    return qts;
}

function fetchTabItem($tabItem) {
    const $qtWrap = $('#setup-qts');
    const subVenueId = $tabItem.attr('sub-venue-id');

    $.get('/largeDisplaySetUp/initData', {
        subVenueId,
    }, (res) => {
        if(res.subVenueList && res.subVenueList.length > 0){
            let  subVenueId =  res.subVenueList[0].subVenueId
            poolManage.subVenueId = subVenueId;
            poolManage.tableData = res.subVenueList;
            $.get('/largeDisplaySetUp/getDisplayInfo', {
                    subVenueId,
                }, (res) => {
                    $qtWrap.html('');
                    if (res.error == 0) {
                        $qtWrap.append(renderTabItem(res));
                    } else {
                        mAlert.showWrong('该场馆没有配置信息!');
                    }
                });
        }else{
            $.get('/largeDisplaySetUp/getDisplayInfo', {
                subVenueId,
            }, (res) => {
                $qtWrap.html('');
                if (res.error == 0) {
                    $qtWrap.append(renderTabItem(res));
                } else {
                    mAlert.showWrong('该场馆没有配置信息!');
                }
            });
        }
    });
    
}

function handleRenderTabItem() {
    $('#page-tabs').on('click', 'li', function () {
        const $this = $(this);
        $this
            .addClass('cur')
            .siblings('.cur')
            .removeClass('cur');

        fetchTabItem($this);
    });
    $('#page-tabs').on('click', '>li a', (e) => {
        e.preventDefault();
    });
    fetchTabItem($('#page-tabs').find('li.cur'));
}

// 上传视频
function handleFileUpload() {
    const imgTmpl = _.template($('#imageDisplayTmpl').html());
    const videoTmpl = _.template($('#videoDisplayTmpl').html());
    $(document).on('change', '.video-upload', () => {
        const uploadedFile = document.getElementById('video-uploader-field')
        const fileSize = (uploadedFile.files[0] || {}).size
        const fileName = (uploadedFile.files[0] || {}).name
        const fileFormat = fileName.slice(fileName.indexOf('.') + 1)
        const videoFormat = ['mp4', 'flv', 'MP4', 'FLV']
        if (videoFormat.indexOf(fileFormat) < 0) {
            mAlert.showWrong('视频仅支持mp4、flv格式')
            return
        }
        if (fileSize > 500 * 1024 * 1024) {
            mAlert.showWrong('视频超出500M限制！')
            return
        }
        $.ajaxFileUpload({
            url: '/largeDisplaySetUp/uploadFile',
            type: 'POST',
            dataType: 'json',
            fileElementId: 'video-uploader-field',
            timeout: 1000 * 60 * 30,
            success(data) {
                if (data.error == 0) {
                    $('#image-upload-container').before(videoTmpl({
                        video: data.filePath,
                        baseUrl: $('#baseUrl').val(),
                    }));
                } else {
                    settings.spinner.showErrorResult(data.message);
                }
            },
        });
        document.getElementById('media-form').reset()
    });
}

// 删除,选中批量删除图片
function handleDeleteImage() {
    $(document).on('click', '[data-click="select-image"]', function () {
        const _this = $(this)
        if(_this.hasClass('checked')){
            _this.removeClass('checked')
        }else{
            $(this).addClass('checked')
        }
    });
    $(document).on('click', '[data-click="remove-image"]', function () {
        const _this = $(this)
        showBeautifulConfirmBox(() => {
            const $container = _this.closest('.image-container');
            const filePath = $container.data('src');

            $.post('/largeDisplaySetUp/removeFile', {
                filePath,
            }, (res) => {
                if (res.error === 0) {
                    $container.remove();
                    mAlert.showSuccess('删除成功');
                    saveResult();
                } else {
                    mAlert.showWarning(res.message);
                }

            });
        }, {
            content: '删除后将立即保存，是否删除？',
        });
    });
}
// function handleSelectImage() {
//     $(document).on('click', '[data-click="select-image"]', function () {

//     });
// }

function imgChange() {
    let $srcImgDiv = null;
    $(document).on('dragstart', '.img-div img', function () {
        $srcImgDiv = $(this).parent();
    });
    $(document).on('dragover', '.img-div img', (event) => {
        event.preventDefault();
    });
    $(document).on('drop', '.img-div img', function (event) {
        event.preventDefault();
        if ($srcImgDiv[0] != $(this).parent()[0]) {
            if ($('.img-div').index($srcImgDiv) > $('.img-div').index($(this).parent())) {
                $(this).parent().before($srcImgDiv);
            } else {
                $(this).parent().after($srcImgDiv);
            }
        }
    });
}
function viewImage() {
    $(document).on('click', '.setup-item-image img', function () {
        const $this = $(this);
        let imgUrl = $this.attr('src');
        photoSwipe.initSwiper([{src: imgUrl}], 0)
    })
    $('.main').delegate('.img-div.image-container', 'click', function () {
        let index = $(this).index()
        const parent = $('#image-upload-container');
        let arr = []
        parent.find('li').each(function () {
            arr.push({
                src: $('#baseUrl').val() + $(this).attr('data-src')
            })
        })
        photoSwipe.initSwiper(arr, index)
    })
}

$(() => {
    viewImage();
    handleEditPlainText();
    handleEditUnitText();
    handleRenderTabItem();
    handleFileUpload();
    handleDeleteImage();
    imgChange();
});
