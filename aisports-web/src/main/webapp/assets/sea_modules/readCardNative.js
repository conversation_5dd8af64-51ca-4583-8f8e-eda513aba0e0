define(function (require,exports,module) {
  var $ = require('jquery')
  require('utils/cardReader')
  const moment = require('moment')

  // 可通过身份证查询卡号
  function getCardId(iptDom, searchDom, watchDom, normalFlag, callback, obj) {
    //赋值读卡器读取的卡号
    $('#idCardlist').change(function () {
      // if (flag) {
      //   $(clickDom).click()
      // }
      let newCardNo = ''
      let type = JSON.parse($('#idCardlist').val()).type
      if (type == '9') {
        if (
          ($(watchDom) &&
            $(watchDom).prop('style') &&
            $(watchDom).prop('style').display !== 'none') ||
          normalFlag
        ) {
          newCardNo = JSON.parse($('#idCardlist').val()).idNum
        } else {
          const isArea = [{
              id: 'userName',
              card: 'name'
            },
            {
              id: 'sex',
              card: 'gender'
            },
            {
              id: 'birthday',
              card: 'birthDay'
            },
            {
              id: 'address',
              card: 'Address'
            },
            {
              id: 'psptId',
              card: 'idNum'
            },
            {
              id: 'validTime',
              card: 'expireEnd'
            },
            {
              id: 'picture',
              card: 'idCardsrc'
            }
          ]

          const list = JSON.parse($('#idCardlist').val())
          if (list.type == '9') {
            isArea.forEach(function(item) {
              if (item.card === 'gender') {
                const sexValue = list[item.card] === '男' ? 1 : 0
                $('#' + item.id).prop('value', sexValue)
              } else if (item.card === 'idCardsrc') {
                $('#' + item.id).prop('src', list[item.card])
              } else {
                $('#' + item.id).prop('value', list[item.card])
              }

              if (item.card === 'expireEnd' || item.card === 'birthDay') {
                $('#' + item.id).prop(
                  'value',
                  new moment(list[item.card]).format('YYYY-MM-DD')
                )
              }
            })
          }
          callback && callback()
        }
      } else if (type == '0' && $(watchDom).prop('style') && $(watchDom).prop('style').display === 'none') {
        newCardNo = JSON.parse($('#idCardlist').val()).eCardNo
        $(obj.value).prop('value', newCardNo)
        $(obj.search).trigger('click')
        return
      } else if (type == '1' && $(watchDom).prop('style') && $(watchDom).prop('style').display === 'none') {
        newCardNo = JSON.parse($('#idCardlist').val()).serialNumber
        $(obj.value).prop('value', newCardNo)
        $(obj.search).trigger('click')
        return
      } else if (type == '2') {
        newCardNo = JSON.parse($('#idCardlist').val()).KeyCodeValue
      }
      // if (newCardNo == EcardSearchVue.cardNo) {
      //     return
      // }

      if (newCardNo.length !== 0) {
        //自动查询
        $(iptDom).prop('value', newCardNo) //js attr赋值取不到value  改用prop
        $(searchDom).trigger('click')
      }
    })
    this.getCardId = getCardId
  }

  module.exports= new getCardId();
})
