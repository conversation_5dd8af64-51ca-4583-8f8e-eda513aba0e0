package com.asiainfo.aisports.service.qrcode;

import com.google.common.base.Strings;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR> niithub
 * @description :
 * @date : 2022/6/6
 */
public class QrcodeCacheServiceFactory {

    public QrcodeCacheServiceFactory() {
    }

    public static QrcodeCacheService createQrcodeCacheService(ApplicationContext applicationContext, String type) {
        if (Strings.isNullOrEmpty(type)) {
            return null;
        }
        if (applicationContext.containsBean("qrcodeCache" + type + "Service")) {
            return (QrcodeCacheService) applicationContext.getBean("qrcodeCache" + type + "Service");
        } else {
            return null;
        }
    }
}
