package com.asiainfo.aisports.scheduler.service.custRemind;

import com.alibaba.fastjson.JSONArray;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.SequenceWrapper;
import com.asiainfo.aisports.service.sms.SmsSendService;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.DateJsonValueProcessor;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xzeng on 17/11/13.
 * 会员到期提醒
 */
@Service
public class CustRemind04Service implements CustRemindService {
    @Autowired
    SequenceWrapper sequenceWrapper;
    @Autowired
    CustReminderGenLogMapper custReminderGenLogMapper;
    @Autowired
    DepositMapper depositMapper;
    @Autowired
    CustReminderMapper custReminderMapper;
    @Autowired
    CustReminderLogMapper custReminderLogMapper;
    @Autowired
    SmsSendService smsSendService;

    @Autowired
    MiniReminderNoticeMapper miniReminderNoticeMapper;

    /**
     * 生成客户提醒
     *
     * @param custReminderRule
     */
    @Override
    public void generateCustRemind(CustReminderRule custReminderRule) {
        Date now = new Date();
        Date nowDate = DateCalUtil.trim(now);

        //1.如果通知类型没有配置，则不进行提醒
        String noticeTypeString = custReminderRule.getNoticeType();
        if (Strings.isNullOrEmpty(noticeTypeString)) {
            return;
        }
        List<String> noticeTypeList = Arrays.asList(noticeTypeString.split(","));

        //2.如果提醒配置的有问题，就不要提醒了
        JSONObject contentObject = JSONObject.fromObject(custReminderRule.getContent());
        if (!contentObject.containsKey(Constants.ReminderContent.LIMIT_TYPE) || !contentObject.containsKey(Constants.ReminderContent.TIME_UNIT) || !contentObject.containsKey(Constants.ReminderContent.TIME_AMOUNT)) {
            return;
        }

        //客户提醒生成日志
        CustReminderGenLog custReminderGenLog = new CustReminderGenLog();
        custReminderGenLog.setGenLogId(sequenceWrapper.custReminderSequence().toString());
        custReminderGenLog.setReminderRuleId(custReminderRule.getRuleId());
        custReminderGenLog.setStatus(Constants.Tag.YES);
        custReminderGenLog.setCreateTime(now);
        custReminderGenLogMapper.insert(custReminderGenLog);

        //获取提醒日期
        Date remindDate = DateCalUtil.getDateWithOffset(DateCalUtil.getDateWithOffset(nowDate, contentObject.getString(Constants.ReminderContent.TIME_AMOUNT) + contentObject.getString(Constants.ReminderContent.TIME_UNIT)), "1d");

        List<CustReminder> custReminderList = Lists.newArrayList();
        List<CustReminderLog> custReminderLogList = Lists.newArrayList();
        List<MiniReminderNotice> miniReminderNoticeList = Lists.newArrayList();
        List<Map> depositList = Lists.newArrayList();

        //提醒类型
        Set<String> limitTypes = Sets.newHashSet(Arrays.asList(contentObject.getString(Constants.ReminderContent.LIMIT_TYPE).split(",")));

        //查询需要提醒的期间卡
        if (limitTypes.contains(Constants.DepositLimitType.PERIOD)) {
            depositList.addAll(depositMapper.queryRemindPeriodDeposit(custReminderRule.getVenueId(), remindDate));
            limitTypes.remove(Constants.DepositLimitType.PERIOD);
        }

        //查询需要提醒的计次、卡余额卡
        if (!limitTypes.isEmpty()) {
            depositList.addAll(depositMapper.queryRemindDeposit(custReminderRule.getVenueId(), remindDate, limitTypes.stream().collect(Collectors.joining(","))));
        }

        JsonConfig jsonConfig = new JsonConfig();
        // 发现保存的时候，日期被格式化的结果特长
        // 类似{"date":10,"hours":11,"seconds":51,"month":7,"nanos":0,"timezoneOffset":-480,"year":117,"minutes":9,"time":1502334591000,"day":4}
        // 导致extra字段超出长度报错
        jsonConfig.registerJsonValueProcessor(Date.class, new DateJsonValueProcessor("yyyy-MM-dd HH:mm:ss"));
        jsonConfig.registerJsonValueProcessor(Timestamp.class, new DateJsonValueProcessor("yyyy-MM-dd HH:mm:ss"));



        for (Map deposit : depositList) {
            Long centerId = MapUtils.getLong(deposit, "centerId");
            noticeTypeList.forEach(noticeType -> {
                if (Constants.ReminderNoticeType.PAGE.equals(noticeType)) {
                    // 提醒表
                    CustReminder custReminder = new CustReminder();
                    custReminder.setReminderId(sequenceWrapper.custReminderSequence().toString());
                    custReminder.setCustId(MapUtils.getLong(deposit, "custId"));
                    custReminder.setVenueId(MapUtils.getLong(deposit, "venueId"));
                    custReminder.setCenterId(centerId);
                    custReminder.setReminderType(custReminderRule.getReminderType());
                    custReminder.setContent("尊敬的会员您好，您的" + MapUtils.getString(deposit, "productName") + "将于" + DateCalUtil.date2String((Date) deposit.get("endDate"), Constants.DEFAULT_DATE_FORMAT) + "到期,为了不影响您使用，请尽快办理相关续卡");
                    custReminder.setStatus(Constants.Tag.NO);
                    custReminder.setReminderRuleId(custReminderRule.getRuleId());
                    custReminder.setGenLogId(custReminderGenLog.getGenLogId());
                    custReminder.setCreateTime(now);
                    custReminder.setUpdateTime(now);
                    if (Constants.DepositLimitType.PERIOD.equals(MapUtils.getString(deposit, "limitType"))) {
                        custReminder.setExtra(JSONObject.fromObject(deposit, jsonConfig).toString());
                    } else {
                        custReminder.setExtra(MapUtils.getString(deposit, "depositId"));
                    }
                    custReminderList.add(custReminder);

                    // 提醒日志表
                    CustReminderLog custReminderLog = new CustReminderLog();
                    custReminderLog.setLogId(sequenceWrapper.custReminderSequence().toString());
                    custReminderLog.setReminderId(custReminder.getReminderId());
                    custReminderLog.setNoticeType(Constants.ReminderNoticeType.PAGE);
                    custReminderLog.setStatus(Constants.Tag.YES);
                    custReminderLog.setLogTime(now);
                    custReminderLogList.add(custReminderLog);
                }

                if (Constants.ReminderNoticeType.SMS.equals(noticeType) && !Strings.isNullOrEmpty(MapUtils.getString(deposit, "contactPhone"))) {
                    // 获取模板
                    SmsTemplate smsTemplate = smsSendService.getSmsTemplate(centerId, Constants.SmsTempletType.CUST_OVER_TIME);
                    if (smsTemplate != null) {
                        // 封装参数
                        Map smsParams = Maps.newHashMap();
                        smsParams.put("productName", MapUtils.getString(deposit, "productName"));
                        smsParams.put("endDate", DateCalUtil.date2String((Date) deposit.get("endDate"), Constants.DEFAULT_DATE_FORMAT));
                        // 发送短信
                        smsSendService.sendMessagesByPlanTime(centerId, MapUtils.getLong(deposit, "venueId"), MapUtils.getString(deposit, "contactPhone"), Constants.SmsTempletType.CUST_OVER_TIME, smsParams, null);

                        CustReminder custReminder = new CustReminder();
                        custReminder.setReminderId(sequenceWrapper.custReminderSequence().toString());
                        custReminder.setCustId(MapUtils.getLong(deposit, "custId"));
                        custReminder.setVenueId(MapUtils.getLong(deposit, "venueId"));
                        custReminder.setCenterId(centerId);
                        custReminder.setReminderType(custReminderRule.getReminderType());
                        custReminder.setContent("尊敬的会员您好，您的" + MapUtils.getString(deposit, "productName") + "将于" + DateCalUtil.date2String((Date) deposit.get("endDate"), Constants.DEFAULT_DATE_FORMAT) + "到期,为了不影响您使用，请尽快办理相关续卡");
                        custReminder.setStatus(Constants.Tag.YES);
                        custReminder.setReminderRuleId(custReminderRule.getRuleId());
                        custReminder.setGenLogId(custReminderGenLog.getGenLogId());
                        custReminder.setCreateTime(now);
                        custReminder.setUpdateTime(now);
                        if (Constants.DepositLimitType.PERIOD.equals(MapUtils.getString(deposit, "limitType"))) {
                            custReminder.setExtra(JSONObject.fromObject(deposit, jsonConfig).toString());
                        } else {
                            custReminder.setExtra(MapUtils.getString(deposit, "depositId"));
                        }
                        custReminderList.add(custReminder);

                        CustReminderLog custReminderLog = new CustReminderLog();
                        custReminderLog.setLogId(sequenceWrapper.custReminderSequence().toString());
                        custReminderLog.setReminderId(custReminder.getReminderId());
                        custReminderLog.setNoticeType(Constants.ReminderNoticeType.SMS);
                        custReminderLog.setStatus(Constants.Tag.YES);
                        custReminderLog.setLogTime(now);
                        custReminderLogList.add(custReminderLog);
                    }
                }

                if (Constants.ReminderNoticeType.MINI.equals(noticeType)) {
                    // 插入小程序表
                    MiniReminderNotice miniReminderNotice = new MiniReminderNotice();
                    miniReminderNotice.setCustId(MapUtils.getLong(deposit, "custId"));
                    miniReminderNotice.setCenterId(centerId);
                    miniReminderNotice.setId(sequenceWrapper.miniReminderNoticeId());
                    miniReminderNotice.setCreateTime(new Date());
                    miniReminderNotice.setState(Constants.Tag.YES);
                    List<String> contentList = Lists.newArrayList(
                            "尊敬的用户，您办理的“" + MapUtils.getString(deposit, "productName") + "”，即将到期，请续费",
                            "到期时间：" + DateCalUtil.date2String((Date) deposit.get("endDate"), "yyyy/MM/dd"),
                            "剩余天数：" + DateCalUtil.daysBetween(nowDate, (Date) deposit.get("endDate")) + "天");
                    miniReminderNotice.setContent(JSONArray.toJSONString(contentList));
                    miniReminderNotice.setReminderType(custReminderRule.getReminderType());
                    miniReminderNoticeList.add(miniReminderNotice);
                    // 提醒表
                    CustReminder custReminder = new CustReminder();
                    custReminder.setReminderId(sequenceWrapper.custReminderSequence().toString());
                    custReminder.setCustId(MapUtils.getLong(deposit, "custId"));
                    custReminder.setVenueId(MapUtils.getLong(deposit, "venueId"));
                    custReminder.setCenterId(centerId);
                    custReminder.setReminderType(custReminderRule.getReminderType());
                    custReminder.setContent(miniReminderNotice.getContent());
                    custReminder.setStatus(Constants.Tag.YES);
                    custReminder.setReminderRuleId(custReminderRule.getRuleId());
                    custReminder.setGenLogId(custReminderGenLog.getGenLogId());
                    custReminder.setCreateTime(now);
                    custReminder.setUpdateTime(now);
                    if (Constants.DepositLimitType.PERIOD.equals(MapUtils.getString(deposit, "limitType"))) {
                        custReminder.setExtra(JSONObject.fromObject(deposit, jsonConfig).toString());
                    } else {
                        custReminder.setExtra(MapUtils.getString(deposit, "depositId"));
                    }
                    custReminderList.add(custReminder);

                    // 提醒日志表
                    CustReminderLog custReminderLog = new CustReminderLog();
                    custReminderLog.setLogId(sequenceWrapper.custReminderSequence().toString());
                    custReminderLog.setReminderId(custReminder.getReminderId());
                    custReminderLog.setNoticeType(Constants.ReminderNoticeType.MINI);
                    custReminderLog.setStatus(Constants.Tag.YES);
                    custReminderLog.setLogTime(now);
                    custReminderLogList.add(custReminderLog);



                }
            });
        }

        //批量插入提醒
        if (!custReminderList.isEmpty()) {
            custReminderMapper.batchInsert(custReminderList);
        }

        //批量取消已经办理续卡的提醒
        List<CustReminder> cancelReminderList = custReminderMapper.queryCancelReminderList(custReminderRule.getVenueId(), remindDate);
        List<String> reminderIdList = Lists.newArrayList();
        for (CustReminder custReminder : cancelReminderList) {
            reminderIdList.add(custReminder.getReminderId());
        }
        if (!reminderIdList.isEmpty()) {
            custReminderMapper.batchUpdateStatus(reminderIdList);
        }

        if (!custReminderLogList.isEmpty()) {
            custReminderLogMapper.batchInsert(custReminderLogList);
        }

        if (!miniReminderNoticeList.isEmpty()) {
            miniReminderNoticeMapper.batchInsert(miniReminderNoticeList);
        }
    }
}
