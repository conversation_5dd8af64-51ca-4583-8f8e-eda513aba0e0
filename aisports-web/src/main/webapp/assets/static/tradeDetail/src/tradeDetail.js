/**
 * Created by nightost on 16/7/6.
 * 订单详情,带有返销功能
 */
define(function (require) {
    var $ = require('jquery');
    var settings = require('aisportsConfig');
    var mAlert = require('mAlert');
    var tipCoverIns = require('handleTipCover');
    var confirmTipIns;
    require('frame');
    /**
     * detail events
     */
    function detailEvents(){
        $('#cancelTrade').on('click' , showTipCover);
    }

    /**
     * create tip cover
     */
    function showTipCover(){
        var options = {
            tipText : '确认返销',
            tipContent : '请确认是否返销当前订单',
            confirmText : '确定',
            cancelText : '取消'
        };
        if(confirmTipIns == undefined){
            confirmTipIns = tipCoverIns.createTipCover(options);
            confirmTipIns.addClickHandle(confirmPayHandle);
            confirmTipIns.addCloseHandle(cancelPayHandle);
        }
        confirmTipIns.showTipCover();
    }

    /**
     * confirmPayHandle
     */
    function confirmPayHandle(data){
        var $el = $(data.el);
        if($el.hasClass('confirm')){
            cancelTrade();
        }
        data.$container.hide();
    }

    /**
     * cancelPayHandle
     */
    function cancelPayHandle(data){
        data.$container.hide();
    }


    /**
     * cancel trade
     */
    function cancelTrade(){
        var tradeId = $('#tradeId').val();
        $.ajax({
            url : '/orderManage/submitCancelTrade',
            data : {
                tradeId : tradeId
            },
            success : cancelTradeHandler
        });
    }

    /**
     * cancel trade callback handler
     */
    function cancelTradeHandler(backData){
        if(backData.error != 0){
            mAlert.showWrong(backData.message);
            return;
        }
        mAlert.showSuccess('返销成功!');
        backToList(backData);
    }

    /**
     * back to list page
     */
    function backToList(backData){
       window.location.href = settings.urlPrefix + backData.url;
    }

    /**
     * dom loaded
     */
    $(function () {
        detailEvents();
    });
});