<template>
    <div class="colx-4">
        <div class="rowx">
            <div class="colx-10">
                <chart height="367">
                    <text-chart
                        :number="summaries.centerNum"
                        title="场馆公司数量"
                        unit="个"></text-chart>
                    <text-chart
                        :number="summaries.memberNum"
                        title="会员总数"
                        unit="人"></text-chart>
                    <text-chart
                        :number="summaries.newMemberNum"
                        title="新增会员数"
                        unit="人"></text-chart>
                    <text-chart
                        :number="summaries.receiveNum"
                        title="总接待人次"
                        unit="人"></text-chart>
                    <text-chart
                        :number="summaries.yearIncome"
                        title="年累计现金"
                        unit="元"></text-chart>
                    <text-chart
                        :number="summaries.yearConsume"
                        title="年累计收入"
                        unit="元"></text-chart>
                </chart>
            </div>
            <div class="colx-10">
                <chart height="175">
                    <text-chart
                        :number="summaries.dayIncome"
                        title="当日现金"
                        unit="元"></text-chart>
                    <text-chart
                        :number="summaries.dayConsume"
                        title="当日收入"
                        unit="元"></text-chart>
                    <text-chart
                        :number="summaries.dayReceive"
                        title="当日接待人次"
                        unit="人"></text-chart>
                </chart>
            </div>
        </div>
    </div>
</template>

<script>
import fetchData from '../common/fetchData'

export default {
    mixins: [fetchData],
    data() {
        return {
            summaries: {},
        }
    },
    methods: {
        paintChart(res) {
            res.masterCenterInfo.forEach((mc, i) => {
                this.$set(this.summaries, mc.key, mc.value || 0)
            })

            return []
        },
    },
}
</script>
