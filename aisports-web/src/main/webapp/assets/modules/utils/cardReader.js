define(function(require) {

let websocket = ''//websocket实例
let lockReconnect = false//避免重复连接
let mAlert = require('mAlert')
let $ = require('jquery')
// var wsUrl = "ws://127.0.0.1:10086/scan";
let idcardList={} //卡数据
let wsUrl = "ws://localhost:50000/scan"
const czCard = {
    ID_CARD: {
        name: '',
        gender: '',
        folk: '',
        birthDay: '',
        Address: '',
        idNum:'',
        agency: '',
        expireStart: '',
        expireEnd: ''
    }
}

function createWebSocket(url) {
    try {
        websocket = new WebSocket(url);
        //websocket.send("hello");
        initEventHandle();
    } catch (e) {
        reconnect(url);
    }
}
function initEventHandle() {
    websocket.onclose = function () {
        console.log("WebSocket连接已经关闭");
        reconnect(wsUrl);
    };
    websocket.onerror = function () {
        console.log("WebSocket连接发生错误");
        reconnect(wsUrl);
    };
    websocket.onopen = function () {
        console.log("WebSocket连接已经成功");
        //心跳检测重置
        heartCheck.start();
    };
    websocket.onmessage = function (event) {
        if (event.data == "2") {
            heartCheck.reset().start();
            return
        } else {
            var obj = JSON.parse(event.data);
            if (obj["Type"] == 9) { //身份证json串
                idcardList={}
                idcardList.name = obj["Idmsg"].Name;
                idcardList.idCardsrc = "data:image/png;base64," + obj["Idmsg"].PicBase64Code;
                idcardList.gender = obj["Idmsg"].Gender;
                idcardList.folk = obj["Idmsg"].Folk;
                idcardList.birthDay = obj["Idmsg"].BirthDay;
                idcardList.Address = obj["Idmsg"].Address;
                idcardList.idNum = obj["Idmsg"].IdNum;
                idcardList.agency = obj["Idmsg"].Agency;
                idcardList.expireStart = obj["Idmsg"].ExpireStart;
                idcardList.expireEnd = obj["Idmsg"].ExpireEnd;
                idcardList.picBase64Code = obj["Idmsg"].PicBase64Code;
                if(window.ChromeCardReader) {
                    window.ReadIDCARDHANDLE && window.ReadIDCARDHANDLE(idcardList)
                }
            }
            else if (obj["Type"] == 0) {
                idcardList={}
                idcardList.eCardNo =  obj["Number"];//16位卡号
            } else if (obj["Type"] == 1) {
                idcardList={}
                idcardList.serialNumber =  obj["Number"];//8位16进制序列号
                if(window.ChromeCardReader) {
                    window.SEARCHBYID && window.SEARCHBYID(obj["Number"]);
                }
            } else if (obj["Type"] == 2) {
                idcardList.KeyCodeValue =  obj["Number"];//钥匙扣
                if(window.ChromeCardReader) {
                    window.handleBraceletNum && window.handleBraceletNum(obj["Number"]);
               }
            }  else if(obj['Type'] == 11) {
                idcardList.idNum = obj.CZmsg.split('|')[1]
                idcardList.name = obj.CZmsg.split('|')[4]
            }else if(obj['Type'] == 12) {
                // 这是滁州一卡通的身份证扫描
                var idCardInfo = obj['CZmsg'].split('|')
                Object.keys(czCard.ID_CARD).forEach((card, cardIndex) => {
                    if(card == 'birthDay') {
                        czCard.ID_CARD[card] = idCardInfo[cardIndex].split('.').join('-')
                    }else {
                        czCard.ID_CARD[card] = idCardInfo[cardIndex]
                    }
                })
                idcardList = czCard.ID_CARD
            }
            idcardList.type=obj["Type"]
        }
        //如果获取到消息，心跳检测重置
        //拿到任何消息都说明当前连接是正常的
        heartCheck.reset().start();
        $('#idCardlist').attr("value",JSON.stringify(idcardList))
        $("#idCardlist").change();
    }
}
//重新连接webSocket
function reconnect(url) {
    if (lockReconnect) return;
    lockReconnect = true;
    //没连接上会一直重连，设置延迟避免请求过多
    //createWebSocket(url);
    setTimeout(function () {
        createWebSocket(url);
        lockReconnect = false;
    }, 2000);
}

//心跳检测
let heartCheck = {
    timeout: 5000,//50秒
    timeoutObj: null,
    serverTimeoutObj: null,
    reset: function () {
        clearTimeout(this.timeoutObj);
        clearTimeout(this.serverTimeoutObj);
        return this;
    },
    start: function () {
        var self = this;
        this.timeoutObj = setTimeout(function () {
            //这里发送一个心跳，后端收到后，返回一个心跳消息，
            //onmessage拿到返回的心跳就说明连接正常
            websocket.send("1");
            self.serverTimeoutObj = setTimeout(function () {//如果超过一定时间还没重置，说明后端主动断开了
                websocket.close();//如果onclose会执行reconnect，我们执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
            }, self.timeout)
        }, this.timeout)
    }
}
//判断当前浏览器是否支持WebSocket
   function ifWebSocket(){
        if ('WebSocket' in window) {
            //初始化连接webSocket
            createWebSocket(wsUrl);
        }
        else {
            mAlert.showWarning('当前浏览器 Not support websocket')
        }
    }

    $(function(){
        ifWebSocket()
    });

})
