<template>
    <div class="title-box edit-advertise">
        <h1 style="display: block;">
            <span>{{ flag ? '编辑' : '新增' }}{{ adTypeName }}</span>
            <a href="#/" class="btn-return" title="后退" @click.prevent="goprev">
                <i class="iconfont icon-zuojiantou"></i>
            </a>
        </h1>
        <div class="sub-box">
            <div class="details">
                <div class="form-line">
                    <div class="form-label">
                        <span class="star">*</span>
                        <span>封面图</span>
                    </div>
                    <div class="form-field">
                        <div class="upload-area">
                            <div class="upload-area-bc">
                                <i v-if="!editInfo.cover" class="iconfont icon-jiahao2"></i>
                                <form id="file-form">
                                    <input type="file" @change="fileChange">
                                </form>
                                <img v-if="editInfo.cover" :src="editInfo.cover">
                            </div>
                        </div>
                        <div class="tip">＊建议图片尺寸{{ currentSize.sizeDesc || '750*380' }}px,支持jpeg、png、bmp</div>
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-label">归属场馆</div>
                    <div class="form-field">
                        <el-cascader v-if="staffResource.length > 0" :options="staffResource" v-model="venueSelect"
                            placeholder="请选择中心/场馆" clearable siz="medium">
                        </el-cascader>
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-label">显示顺序</div>
                    <div class="form-field">
                        <el-input v-model="editInfo.showOrder" type="number" placeholder="数字越小越靠前">
                        </el-input>
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-label">有效期</div>
                    <div class="form-field">
                        <el-date-picker v-model="dateRange" type="daterange" range-separator="至"
                            start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-label">
                        <span class="star">*</span>
                        广告类型
                    </div>
                    <div class="form-field">
                        <el-select v-if="contentTypes.length > 0" v-model="editInfo.contType" size="medium"
                            placeholder="请选择类型">
                            <el-option v-for="item in contentTypes" :key="item.key" :value="item.key"
                                :label="item.value">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="editInfo.contType === '10'" class="form-line">
                    <div class="form-label">限时抢购</div>
                    <div class="form-field">
                        <el-select v-if="timeLimitList.length > 0" v-model="editInfo.contInfo" size="medium" clearable
                            filterable>
                            <el-option v-for="item in timeLimitList" :key="item.campId" :value="item.campId"
                                :label="item.campName">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="editInfo.contType === '11'" class="form-line">
                    <div class="form-label">赛事活动</div>
                    <div class="form-field">
                        <el-select v-if="competitionList.length > 0" v-model="editInfo.contInfo" size="medium" clearable
                            filterable>
                            <el-option v-for="item in competitionList" :key="item.campId" :value="item.campId"
                                :label="item.title">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="editInfo.contType === '6'" class="form-line">
                    <div class="form-label">教练</div>
                    <div class="form-field">
                        <el-select v-if="coachList.length > 0" v-model="editInfo.contInfo" size="medium" clearable>
                            <el-option v-for="item in coachList" :key="item.coachId" :value="item.coachId"
                                :label="item.coachName">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="editInfo.contType === '3'" class="form-line">
                    <div class="form-label">课程</div>
                    <div class="form-field">
                        <el-select v-if="courseList.length > 0" v-model="editInfo.contInfo" size="medium" clearable
                            filterable>
                            <el-option v-for="item in courseList" :key="item.courseId" :value="item.courseId"
                                :label="item.courseName">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="editInfo.contType === '4'" class="form-line">
                    <div class="form-label">活动</div>
                    <div class="form-field">
                        <el-select v-if="campList.length > 0" v-model="editInfo.contInfo" size="medium" clearable
                            filterable>
                            <el-option v-for="item in campList" :key="item.campId" :value="item.campId"
                                :label="item.title">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="editInfo.contType === '2'" class="form-line">
                    <div class="form-label">外链地址</div>
                    <div class="form-field">
                        <el-input v-model="editInfo.contInfo" type="text" placeholder="http://">
                        </el-input>
                    </div>
                </div>
                <div v-if="editInfo.contType === '2'"  class="form-line">
                    <div class="form-label">标题</div>
                    <div class="form-field">
                        <el-input v-model="editInfo.title" type="text" class="normal" placeholder="请输入标题">
                        </el-input>
                    </div>
                </div>
                <div v-if="editInfo.contType === '1'" class="form-line">
                    <div class="form-label">图文标题</div>
                    <div class="form-field">
                        <el-input v-model="editInfo.title" type="text" class="normal" placeholder="请输入标题">
                        </el-input>
                    </div>
                </div>
                <div v-show="editInfo.contType === '1'" class="form-line">
                    <div class="form-label">内容</div>
                    <div class="form-field">
                        <script id="description" type="text/plain"></script>
                    </div>
                </div>
                <div v-if="editInfo.contType === '5'" class="form-line">
                    <div class="form-label">场馆</div>
                    <div class="form-field">
                        <el-cascader v-if="staffResource.length > 0" :options="staffResource"
                            v-model="contInfoSelect" placeholder="请选择中心/场馆" clearable siz="medium">
                        </el-cascader>
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-label">是否发布</div>
                    <div class="form-field">
                        <el-switch v-model="editInfo.state">
                        </el-switch>
                    </div>
                </div>
                <div class="form-line">
                    <div class="form-field">
                        <button class="btn btn-md btn-primary" @click="save">保存</button>
                        <button class="btn btn-md btn-cancel" @click="goprev">取消</button>
                    </div>
                </div>
            </div>
        </div>
        <clip-img :display="showClipImg" :src="editInfo.cover" :size="currentSize.size"
            :initial-size="currentSize.initialSize" :height="currentSize.height" :width="currentSize.width"
            :result-img-height="adType === '10' ? '130px' : ''" @hide="hideClipCover" @confirm="handleImgConfirm">
        </clip-img>
    </div>
</template>
<script>
    import getData from './getData'
    import {
        showWarning,
        showSuccess
    } from 'mAlert'
    import {
        findCoachList,
        findCourseList,
        findCampaignList,
        findCompetitionList,
        findTimeLimitList,
        saveAd,
        getDetail,
    } from '../utils/api'
    import settings from 'aisportsConfig'
    import {
        filter
    } from 'lodash'

    export default {
        mixins: [getData],
        data() {
            return {
                adType: '',
                editInfo: {
                    title: '', // 图文标题
                    cover: '',
                    showOrder: '', // 显示顺序,
                    contType: '', // 广告类型(1: 图文，2：外链，3：课程，4：活动，5：场馆，6：教练，7：视频,10:赛事活动，11:限时抢购)
                    dateRange: [],
                    content: '', // 内容
                    state: false, // 是否发布
                    contInfo: '',
                },
                coachList: [],
                courseList: [],
                campList: [],
                competitionList: [], //赛事活动
                timeLimitList: [], //限时抢购
                venueId: '', // 选的热门场馆
                showClipImg: false,
                flag: false,
            }
        },
        watch: {
            "editInfo.contType": {
                deep: true,
                handler(val) {
                    if (this.editInfo.contType === '6' && !this.coachList.length) {
                        findCoachList().then(({
                            coachList
                        }) => {
                            this.coachList = coachList
                            this.courseList = []
                            this.campList = []
                            this.competitionList = []
                            this.timeLimitList = []
                        })
                    }
                    if (this.editInfo.contType === '3' && !this.courseList.length) {
                        findCourseList().then(({
                            courseList
                        }) => {
                            this.courseList = courseList
                            this.coachList = []
                            this.campList = []
                            this.competitionList = []
                            this.timeLimitList = []
                        })
                    }
                    if (this.editInfo.contType === '4' && !this.campList.length) {
                        findCampaignList().then(({
                            campList
                        }) => {
                            this.campList = campList
                            this.coachList = []
                            this.courseList = []
                            this.competitionList = []
                            this.timeLimitList = []
                        })
                    }
                    if (this.editInfo.contType === '11' && !this.competitionList.length) {
                        findCompetitionList().then(({
                            resultList
                        }) => {
                            this.competitionList = resultList
                            this.coachList = []
                            this.courseList = []
                            this.campList = []
                            this.timeLimitList = []
                        })
                    }
                    if (this.editInfo.contType === '10' && !this.timeLimitList.length) {
                        findTimeLimitList().then(({
                            campList
                        }) => {
                            this.timeLimitList = campList
                            this.competitionList = []
                            this.coachList = []
                            this.courseList = []
                            this.campList = []
                            // debugger
                            // this.editInfo.contInfo = campList[0].campName
                            //不想动脑子处理了，就这样吧  反正没bug了  你们凑合着看
                        })
                    }
                    if (this.editInfo.contType === '2') {
                        this.editInfo.contInfo = this.urls
                    }

                    if (this.editInfo.content) {
                        this.ue.setContent(this.editInfo.content)
                    }

                },
            },
        },
        mounted() {
            this.adType = this.$route.query.adType
            this.flag = this.$route.query.flag
            this.ue = UM.getEditor('description', {
                initialFrameHeight: 300,
            })
            this.ue.ready(() => {
                this.ue.setContent(this.editInfo.content || '')
            })
        },
        beforeDestroy() {
            this.ue.destroy()
        },
        methods: {
            goprev() {
                vueRouter.go(-1)
            },
            save() {
                if (!this.editInfo.contType) {
                    showWarning('请选择广告类型！')
                    return
                }
                if (!this.editInfo.cover) {
                    showWarning('请选择封面！')
                    return
                }
                if (this.editInfo.contType === '2' && this.contInfo && !settings.regs.url.test(this.contInfo)) {
                    showWarning('请输入正确的外链地址')
                    return
                }
                this.valiedateInfo()
                if (this.editInfo.contType === '5') {
                    // console.log(this.editInfo.contInfo,'this.editInfo.contInfo')
                    this.editInfo.contInfo = this.contInfoSelect[this.contInfoSelect.length - 1]
                }
                console.log(this.editInfo)
                const data = {
                    ...this.editInfo,
                    cover: this.editInfo.cover,
                    appId: this.appId,
                    type: this.adType,
                }
                if (data.cover) {
                    if (data.cover.slice(0, 4) === 'data') {
                        data.cover = data.cover.replace('data:image/png;base64,', '')
                        data.imageType = 'jpg'
                    }
                    if (data.cover.slice(0, 4) === 'http') {
                        data.cover = data.cover.replace(this.ossUrl, '')
                    }
                }
                if (data.contType === '1') {
                    data.content = this.ue.getContent()
                }
                data.state = data.state ? 1 : 0
                saveAd(data).then(() => {
                    showSuccess('保存成功')
                    this.goprev()
                })
            },
        },
    }
</script>