<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aisports</artifactId>
        <groupId>com.asiainfo.aisports</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../aisports-aggregator/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>aisports-tools</artifactId>

    <properties>
        <dao.target.dir>src/main/java</dao.target.dir>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.5.7</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.2</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <!--<executions>-->
                    <!--<execution>-->
                        <!--<id>Generate MyBatis Artifacts</id>-->
                        <!--<goals>-->
                            <!--<goal>generate</goal>-->
                        <!--</goals>-->
                    <!--</execution>-->
                <!--</executions>-->
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generatorConfig.xml</configurationFile>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                    <contexts>venue,center,pay,community,community-platform,competition</contexts>
                    <tableNames>
                        stock_seat_imp,stock_seat,perform_stock,take_seat_log,pre_allocate_seat
                    </tableNames>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>5.1.32</version>
                    </dependency>
                    <dependency>
                        <groupId>org.mybatis.generator</groupId>
                        <artifactId>mybatis-generator-core</artifactId>
                        <version>1.3.2</version>
                    </dependency>
                    <dependency>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                        <version>3.5.7</version>
                    </dependency>
                    <dependency>
                        <groupId>cn.xports.mybatis</groupId>
                        <artifactId>xports-mybatis-plugin</artifactId>
                        <version>1.0</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
