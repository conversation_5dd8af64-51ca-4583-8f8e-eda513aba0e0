package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.ProductStockConstants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.persistence.core.ProductSaleLogMapper;
import com.asiainfo.aisports.persistence.core.ProductStockChangeLogMapper;
import com.asiainfo.aisports.persistence.core.ProductStockMapper;
import com.google.common.collect.ImmutableList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/3 15:10
 */
@Service
public class ProductStockService {

    private static final Logger logger = LoggerFactory.getLogger(ProductStockService.class);


    @Autowired
    private ProductStockMapper productStockMapper;

    @Autowired
    private OperationLogService operationLogService;
    @Autowired
    private SequenceWrapper sequenceWrapper;

    @Autowired
    private ProductStockChangeLogMapper productStockChangeLogMapper;

    @Autowired
    private ProductSaleLogMapper productSaleLogMapper;


    @Transactional
    public void updateStock(Long productId, long stock, LoginStaff staff) {
        ProductStock ps = productStockMapper.selectByPrimaryKey(productId);
        if (ps == null) {
            ps = new ProductStock();
            ps.setProductId(productId);
            ps.setTotalNum(stock);
            ps.setSaleNum(0L);
            ps.setRemainNum(stock);
            ps.setCreateStaffId(staff.getStaffId());
            ps.setCreateTime(new Date());
            ps.setVersion(0L);
            productStockMapper.insert(ps);
            return;
        }

        Long ot = ps.getTotalNum();
        if (ot == stock) {
            return;
        }

        Long orz = ps.getRemainNum();
        long changeNum = stock - ot;

        ps.setTotalNum(stock);
        ps.setRemainNum(orz + changeNum);
        ps.setUpdateTime(new Date());
        ps.setUpdateStaffId(staff.getStaffId());
        int num = productStockMapper.updateByVersion(ps);
        if (num != 1) {
            throw new ServiceException("库存状态变化 修改失败");
        }
        //


        ProductStockChangeLog pscl = saveChangeLog(productId, stock, (int) changeNum, orz, ps.getRemainNum(), ot, 0L, ProductStockConstants.ChangeType.CONFIGURATION);

        operationLogService.addLog(Constants.OperationType.PRODUCT_STOCK_CHANGE,
                "chang_id", pscl.getId().toString(),
                ImmutableList.of(new OperationLogDetail("total_num", stock, ot)), staff);

    }

    private ProductStockChangeLog saveChangeLog(Long productId, long stock, int changeNum, Long oldRemainNum, Long remainNum, Long tradeId, Long saleLogId, String changeType) {
        ProductStockChangeLog pscl = new ProductStockChangeLog();

        pscl.setId(sequenceWrapper.productStockId());
        pscl.setProductId(productId);
        pscl.setChangeNum(changeNum);
        pscl.setOldRemainNum(oldRemainNum);
        pscl.setRemainNum(remainNum);
        pscl.setTotalNum(stock);
        pscl.setTradeId(tradeId);
        pscl.setSaleLogId(saleLogId);
        pscl.setCreateTime(new Date());
        pscl.setChangeType(changeType);
        productStockChangeLogMapper.insert(pscl);
        return pscl;
    }


    @Transactional
    public void deduct(Long productId, Trade trade, int saleNum) {
        ProductStock ps = productStockMapper.selectByPrimaryKey(productId);
        if (ps == null) {
            return;
        }
        Long remainNum = ps.getRemainNum();
        if (remainNum < saleNum) {
            throw new ServiceException("产品库存不足");
        }

        int num = productStockMapper.updateByBottom(productId, saleNum);
        if (num != 1) {
            throw new ServiceException("商品已经被抢光啦");
        }

        ProductSaleLog psl = new ProductSaleLog();
        psl.setId(sequenceWrapper.productStockId());
        psl.setProductId(productId);
        psl.setSaleNum(saleNum);
        psl.setTradeId(trade.getTradeId());
        psl.setCreateTime(new Date());
        psl.setUpdateTime(new Date());
        productSaleLogMapper.insert(psl);


        saveChangeLog(productId, ps.getTotalNum(), -saleNum, remainNum, remainNum - saleNum, trade.getTradeId(), psl.getId(), ProductStockConstants.ChangeType.ORDER);


    }

    @Transactional
    public void returnStock(Long tradeId) {
        ProductSaleLog param = new ProductSaleLog();
        param.setTradeId(tradeId);
        List<ProductSaleLog> productSaleLogs = productSaleLogMapper.selectByFields(param);
        if (productSaleLogs.isEmpty()) {
            return;
        }
        ProductSaleLog log = productSaleLogs.get(0);
        Integer saleNum = log.getSaleNum();

        log.setUpdateTime(new Date());
        productSaleLogMapper.updateByPrimaryKeySelective(log);

        ProductStock ps = productStockMapper.selectByPrimaryKey(log.getProductId());
        Long remainNum = ps.getRemainNum();
        productStockMapper.updateStock(log.getProductId(), saleNum);

        saveChangeLog(ps.getProductId(), ps.getTotalNum(), saleNum, remainNum, remainNum + saleNum, 0L, log.getId(), ProductStockConstants.ChangeType.NOT_PAID);

    }


    @Transactional
    public void returnStock(Long tradeId, Long refundId, Integer num, String changeType) {

        ProductSaleLog param = new ProductSaleLog();
        param.setTradeId(tradeId);
        List<ProductSaleLog> productSaleLogs = productSaleLogMapper.selectByFields(param);
        if (productSaleLogs.isEmpty()) {
            return;
        }
        ProductSaleLog log = productSaleLogs.get(0);
        if (num == null) {
            num = log.getSaleNum();
        }

        log.setUpdateTime(new Date());
        productSaleLogMapper.updateByPrimaryKeySelective(log);

        ProductStock ps = productStockMapper.selectByPrimaryKey(log.getProductId());
        Long remainNum = ps.getRemainNum();
        productStockMapper.updateStock(log.getProductId(), num);

        saveChangeLog(ps.getProductId(), ps.getTotalNum(), num, remainNum, remainNum + num, refundId, log.getId(), changeType);

    }
}
