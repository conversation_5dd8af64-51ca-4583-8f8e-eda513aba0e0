package com.asiainfo.aisports.scheduler.job;

import com.asiainfo.aisports.annotation.RoutingKey;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.TrainingCourseReserve;
import com.asiainfo.aisports.model.ScheduleResult;
import com.asiainfo.aisports.persistence.core.TrainingCourseReserveMapper;
import com.asiainfo.aisports.scheduler.service.PrivateCourseService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by xzeng on 2019/3/29.
 * 私教课自动上课定时任务
 */
@Component
public class PrivateCourseSignJob implements ExecJob {
    @Autowired
    TrainingCourseReserveMapper trainingCourseReserveMapper;
    @Autowired
    PrivateCourseService privateCourseService;

    @Transactional
    @TargetDataSource(name = "{}")
    @Override
    public ScheduleResult execute(@RoutingKey Long centerId, JSONObject args) {
        //获取已经过期的预约记录
        List<TrainingCourseReserve> trainingCourseReserveList = trainingCourseReserveMapper.selectCenterExpiredResv(centerId);
        JSONObject logInfo = new JSONObject();
        for (TrainingCourseReserve trainingCourseReserve : trainingCourseReserveList) {
            try {
                privateCourseService.privateCourseEnter(trainingCourseReserve.getEnrollId(), "预约未上课自动签到", centerId, trainingCourseReserve.getReserveId(),
                        "预约未上课自动签到", Constants.SigninMode.AUTO);
            } catch (Exception e) {
                logInfo.put(trainingCourseReserve.getReserveId(), e.getMessage());
            }
        }
        return new ScheduleResult().set("logInfo", logInfo.toString());
    }
}
