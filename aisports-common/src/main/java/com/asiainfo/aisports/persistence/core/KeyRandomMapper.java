package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.KeyRandom;
import com.asiainfo.aisports.domain.core.KeyRandomKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface KeyRandomMapper {
    @Delete({
        "delete from key_random",
        "where key_id = #{keyId,jdbcType=VARCHAR}",
          "and venue_id = #{venueId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(KeyRandomKey key);

    @Insert({
        "insert into key_random (key_id, venue_id, ",
        "random_code, ticket_id, ",
        "bind_time)",
        "values (#{keyId,jdbcType=VARCHAR}, #{venueId,jdbcType=BIGINT}, ",
        "#{randomCode,jdbcType=VARCHAR}, #{ticketId,jdbcType=BIGINT}, ",
        "#{bindTime,jdbcType=TIMESTAMP})"
    })
    int insert(KeyRandom record);

    @Select({
        "select",
        "key_id, venue_id, random_code, ticket_id, bind_time",
        "from key_random",
        "where key_id = #{keyId,jdbcType=VARCHAR}",
          "and venue_id = #{venueId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="key_id", property="keyId", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="random_code", property="randomCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
        @Result(column="bind_time", property="bindTime", jdbcType=JdbcType.TIMESTAMP)
    })
    KeyRandom selectByPrimaryKey(KeyRandomKey key);

    @Update({
        "update key_random",
        "set random_code = #{randomCode,jdbcType=VARCHAR},",
          "ticket_id = #{ticketId,jdbcType=BIGINT},",
          "bind_time = #{bindTime,jdbcType=TIMESTAMP}",
        "where key_id = #{keyId,jdbcType=VARCHAR}",
          "and venue_id = #{venueId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(KeyRandom record);

    @Select({
        "<script>",
        "select",
        "key_id, venue_id, random_code, ticket_id, bind_time",
        "from key_random",
        "<where>",
        "<if test=\"keyId != null\">and key_id = #{keyId,jdbcType=VARCHAR}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"randomCode != null\">and random_code = #{randomCode,jdbcType=VARCHAR}</if>",
        "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
        "<if test=\"bindTime != null\">and bind_time = #{bindTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="key_id", property="keyId", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="random_code", property="randomCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
        @Result(column="bind_time", property="bindTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<KeyRandom> selectByFields(KeyRandom param);

    @Update({
        "<script>",
        "update key_random",
        "<set>",
        "<if test=\"randomCode != null\">random_code = #{randomCode,jdbcType=VARCHAR},</if>",
        "<if test=\"ticketId != null\">ticket_id = #{ticketId,jdbcType=BIGINT},</if>",
        "<if test=\"bindTime != null\">bind_time = #{bindTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where key_id = #{keyId,jdbcType=VARCHAR}",
          "and venue_id = #{venueId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(KeyRandom record);
}
