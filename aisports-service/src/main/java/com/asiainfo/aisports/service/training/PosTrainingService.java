package com.asiainfo.aisports.service.training;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.model.CustInfo;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.persistence.core.CourseEnrollMapper;
import com.asiainfo.aisports.persistence.core.StudentMapper;
import com.asiainfo.aisports.service.CustQueryService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by zhuyu on 2017/10/25.
 */
@Service
public class PosTrainingService {
    @Autowired
    CourseEnrollMapper courseEnrollMapper;
    @Autowired
    private CustQueryService custQueryService;
    @Autowired
    private StudentMapper studentMapper;

    /**
     * 根据手机号或者卡号查询学员报名记录
     *
     * @param phone
     * @param ecardNo
     * @param centerId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map> queryEnrollInfo(String phone, String ecardNo, Long centerId) {

        List<Map> list = courseEnrollMapper.selectEnrollInfo(phone, ecardNo, centerId);
        if (list.isEmpty()) {
            return list;
        }
        List<Map> resultList = Lists.newArrayList();
        Map first = list.get(0);
        Map tempCust = Maps.newHashMap();
        Long tempCustId = MapUtils.getLong(first, "custId");
        tempCust.put("custId", first.get("custId"));
        tempCust.put("custName", first.get("custName"));
        tempCust.put("gender", first.get("gender"));
        tempCust.put("ecardNo", first.get("ecardNo"));
        List<Map> studentInfoList = Lists.newArrayList();
        for (Map map : list) {
            Long custId = MapUtils.getLong(map, "custId");
            if (!custId.equals(tempCustId)) {
                tempCustId = custId;
                tempCust.put("studentInfoList", studentInfoList);
                resultList.add(tempCust);
                tempCust = Maps.newHashMap();
                tempCust.put("custId", map.get("custId"));
                tempCust.put("custName", map.get("custName"));
                tempCust.put("gender", map.get("gender"));
                tempCust.put("ecardNo", map.get("ecardNo"));
                studentInfoList = Lists.newArrayList();
            }
            Map studentInfo = Maps.newHashMap();
            studentInfo.put("stuId", map.containsKey("stuId") ? map.get("stuId") : "");
            studentInfo.put("stuName", map.containsKey("stuName") ? map.get("stuName") : "");
            studentInfo.put("gender", map.get("gender"));
            studentInfo.put("enrollId", map.containsKey("enrollId") ? map.get("enrollId") : "");
            studentInfo.put("courseName", map.containsKey("courseName") ? map.get("courseName") : "");
            studentInfo.put("startDate", map.containsKey("startDate") ? map.get("startDate") : "");
            studentInfo.put("endDate", map.containsKey("endDate") ? map.get("endDate") : "");
            studentInfo.put("remainNum", map.containsKey("remainNum") ? map.get("remainNum") : "");
            studentInfo.put("lessonNum", map.containsKey("lessonNum") ? map.get("lessonNum") : "");
            studentInfoList.add(studentInfo);
        }
        tempCust.put("studentInfoList", studentInfoList);
        resultList.add(tempCust);
        return resultList;
    }

    /**
     * 根据手机号或者卡号查询用户信息及用户名下的学员报名记录
     *
     * @param phone
     * @param ecardNo
     * @param centerId
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> queryEnrollInfoOfCustomer(String phone, String ecardNo, Long centerId) {
        List<DataMap> resultList = Lists.newArrayList();
        CustInfo custInfo = new CustInfo();
        custInfo.setCenterId(centerId);
        custInfo.setContactPhone(phone);
        custInfo.setEcardNo(ecardNo);
        custInfo.setCustState(Constants.CustomerState.NORMAL);
        List<CustInfo> custInfoList = custQueryService.queryCustInfoByField(custInfo);
        for (CustInfo customer: custInfoList) {
            DataMap dataMap = new DataMap();
            dataMap.put("custId", customer.getCustId());
            dataMap.put("custName", customer.getCustName());
            dataMap.put("gender", customer.getGender());
            dataMap.put("ecardNo", customer.getEcardNo());
            if (customer.getCustId() == null) {
                resultList.add(dataMap);
                continue;
            }
            List<Map> enrollInfoList = studentMapper.selectEnrollInfo(customer.getCustId(), centerId);
            enrollInfoList.removeAll(Collections.singleton(null));
            if (!enrollInfoList.isEmpty()) {
                dataMap.put("studentInfoList", enrollInfoList);
            }
            resultList.add(dataMap);
        }
        return resultList;
    }


}
