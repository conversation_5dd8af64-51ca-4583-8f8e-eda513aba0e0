package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class PerformTeamTicketRecord {

    private String id;
    private String performTicketId;
    private int sendNum; // 发放数量
    private String state; // 0-无效 1-有效
    private Date createTime;
    private Long createStaffId;
    private Date updateTime;
    private Long updateStaffId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPerformTicketId() {
        return performTicketId;
    }

    public void setPerformTicketId(String performTicketId) {
        this.performTicketId = performTicketId;
    }

    public int getSendNum() {
        return sendNum;
    }

    public void setSendNum(int sendNum) {
        this.sendNum = sendNum;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("PerformTeamTicketRecord{");
        sb.append("id='").append(id).append('\'');
        sb.append(", performTicketId='").append(performTicketId).append('\'');
        sb.append(", sendNum=").append(sendNum);
        sb.append(", state='").append(state).append('\'');
        sb.append(", createTime=").append(createTime);
        sb.append(", createStaffId=").append(createStaffId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append('}');
        return sb.toString();
    }
}
