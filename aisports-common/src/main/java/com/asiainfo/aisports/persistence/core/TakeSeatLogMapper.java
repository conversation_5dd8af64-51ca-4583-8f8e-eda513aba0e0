package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.TakeSeatLog;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface TakeSeatLogMapper {
    @Delete({
        "delete from take_seat_log",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into take_seat_log (id, perform_id, ",
        "stock_id, batch_id, ",
        "total_num, valid_num, ",
        "sold_num, finish_num, ",
        "state, center_id, ",
        "create_time, create_staff_id, ",
        "finish_time)",
        "values (#{id,jdbcType=BIGINT}, #{performId,jdbcType=VARCHAR}, ",
        "#{stockId,jdbcType=BIGINT}, #{batchId,jdbcType=BIGINT}, ",
        "#{totalNum,jdbcType=INTEGER}, #{validNum,jdbcType=INTEGER}, ",
        "#{soldNum,jdbcType=INTEGER}, #{finishNum,jdbcType=INTEGER}, ",
        "#{state,jdbcType=VARCHAR}, #{centerId,jdbcType=BIGINT}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{finishTime,jdbcType=TIMESTAMP})"
    })
    int insert(TakeSeatLog record);

    @Select({
        "select",
        "id, perform_id, stock_id, batch_id, total_num, valid_num, sold_num, finish_num, ",
        "state, center_id, create_time, create_staff_id, finish_time",
        "from take_seat_log",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
        @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
        @Result(column="batch_id", property="batchId", jdbcType=JdbcType.BIGINT),
        @Result(column="total_num", property="totalNum", jdbcType=JdbcType.INTEGER),
        @Result(column="valid_num", property="validNum", jdbcType=JdbcType.INTEGER),
        @Result(column="sold_num", property="soldNum", jdbcType=JdbcType.INTEGER),
        @Result(column="finish_num", property="finishNum", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="finish_time", property="finishTime", jdbcType=JdbcType.TIMESTAMP)
    })
    TakeSeatLog selectByPrimaryKey(Long id);

    @Update({
        "update take_seat_log",
        "set perform_id = #{performId,jdbcType=VARCHAR},",
          "stock_id = #{stockId,jdbcType=BIGINT},",
          "batch_id = #{batchId,jdbcType=BIGINT},",
          "total_num = #{totalNum,jdbcType=INTEGER},",
          "valid_num = #{validNum,jdbcType=INTEGER},",
          "sold_num = #{soldNum,jdbcType=INTEGER},",
          "finish_num = #{finishNum,jdbcType=INTEGER},",
          "state = #{state,jdbcType=VARCHAR},",
          "center_id = #{centerId,jdbcType=BIGINT},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "finish_time = #{finishTime,jdbcType=TIMESTAMP}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(TakeSeatLog record);

    @Select({
        "<script>",
        "select",
        "id, perform_id, stock_id, batch_id, total_num, valid_num, sold_num, finish_num, state, center_id, create_time, create_staff_id, finish_time",
        "from take_seat_log",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"performId != null\">and perform_id = #{performId,jdbcType=VARCHAR}</if>",
        "<if test=\"stockId != null\">and stock_id = #{stockId,jdbcType=BIGINT}</if>",
        "<if test=\"batchId != null\">and batch_id = #{batchId,jdbcType=BIGINT}</if>",
        "<if test=\"totalNum != null\">and total_num = #{totalNum,jdbcType=INTEGER}</if>",
        "<if test=\"validNum != null\">and valid_num = #{validNum,jdbcType=INTEGER}</if>",
        "<if test=\"soldNum != null\">and sold_num = #{soldNum,jdbcType=INTEGER}</if>",
        "<if test=\"finishNum != null\">and finish_num = #{finishNum,jdbcType=INTEGER}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"finishTime != null\">and finish_time = #{finishTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
        @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
        @Result(column="batch_id", property="batchId", jdbcType=JdbcType.BIGINT),
        @Result(column="total_num", property="totalNum", jdbcType=JdbcType.INTEGER),
        @Result(column="valid_num", property="validNum", jdbcType=JdbcType.INTEGER),
        @Result(column="sold_num", property="soldNum", jdbcType=JdbcType.INTEGER),
        @Result(column="finish_num", property="finishNum", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="finish_time", property="finishTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<TakeSeatLog> selectByFields(TakeSeatLog param);

    @Update({
        "<script>",
        "update take_seat_log",
        "<set>",
        "<if test=\"performId != null\">perform_id = #{performId,jdbcType=VARCHAR},</if>",
        "<if test=\"stockId != null\">stock_id = #{stockId,jdbcType=BIGINT},</if>",
        "<if test=\"batchId != null\">batch_id = #{batchId,jdbcType=BIGINT},</if>",
        "<if test=\"totalNum != null\">total_num = #{totalNum,jdbcType=INTEGER},</if>",
        "<if test=\"validNum != null\">valid_num = #{validNum,jdbcType=INTEGER},</if>",
        "<if test=\"soldNum != null\">sold_num = #{soldNum,jdbcType=INTEGER},</if>",
        "<if test=\"finishNum != null\">finish_num = #{finishNum,jdbcType=INTEGER},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"finishTime != null\">finish_time = #{finishTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(TakeSeatLog record);


    @Select({
            "<script>",
            "select a.id,f.name as perform_name,s.name as stock_name,a.total_num,a.valid_num,a.sold_num,a.finish_num,a.state,a.create_time,a.finish_time" ,
            "from take_seat_log a,perform f,perform_stock s" ,
            "where a.perform_id = f.id" ,
            "  and a.stock_id = s.id" ,
            "  and f.project_id = #{projectId}" ,
            "<if test=\"performId != null\">and a.perform_id = #{performId,jdbcType=VARCHAR}</if>",
            "<if test=\"stockId != null\">and a.stock_id = #{stockId}</if>",
            "<if test=\"state != null\">and a.state = #{state}</if>",
            "<if test=\"startDate != null\"> and a.create_time &gt;= #{startDate}</if>",
            "<if test=\"endDate != null\"> and a.create_time &lt;= #{endDate}</if>",
            "  order by create_time desc ",
            "</script>"

    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT),
            @Result(column="perform_name", property="performName", jdbcType=JdbcType.VARCHAR),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="total_num", property="totalNum", jdbcType=JdbcType.INTEGER),
            @Result(column="valid_num", property="validNum", jdbcType=JdbcType.INTEGER),
            @Result(column="sold_num", property="soldNum", jdbcType=JdbcType.INTEGER),
            @Result(column="finish_num", property="finishNum", jdbcType=JdbcType.INTEGER),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="finish_time", property="finishTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<DataMap> selectPageList(ParamMap param, RowBounds rowBounds);



    @Insert({
            "<script>",
            "insert into take_seat_log (id, perform_id, ",
            "stock_id, batch_id, ",
            "total_num, valid_num, ",
            "sold_num, finish_num, ",
            "state, center_id, ",
            "create_time, create_staff_id, ",
            "finish_time)",
            "values ",
            "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
            "(#{item.id,jdbcType=BIGINT}, #{item.performId,jdbcType=VARCHAR}, ",
            "#{item.stockId,jdbcType=BIGINT}, #{item.batchId,jdbcType=BIGINT}, ",
            "#{item.totalNum,jdbcType=INTEGER}, #{item.validNum,jdbcType=INTEGER}, ",
            "#{item.soldNum,jdbcType=INTEGER}, #{item.finishNum,jdbcType=INTEGER}, ",
            "#{item.state,jdbcType=VARCHAR}, #{item.centerId,jdbcType=BIGINT}, ",
            "#{item.createTime,jdbcType=TIMESTAMP}, #{item.createStaffId,jdbcType=BIGINT}, ",
            "#{item.finishTime,jdbcType=TIMESTAMP})",
            "</foreach>",
            "</script>",

    })
    int batchInsert(List<TakeSeatLog> takeSeatLogs);

    int update(TakeSeatLog takeSeatLog, Long centerId);
}
