package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.EntryInfo;
import com.asiainfo.aisports.domain.core.GroupDepositMemberChange;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface GroupDepositMemberChangeMapper {
    @Delete({
        "delete from group_deposit_member_change",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into group_deposit_member_change (id, relate_id, ",
        "change_date, ticket_id, ",
        "trade_id, change_num, ",
        "balance, cancel_trade_id, ",
        "state, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{relateId,jdbcType=BIGINT}, ",
        "#{changeDate,jdbcType=DATE}, #{ticketId,jdbcType=BIGINT}, ",
        "#{tradeId,jdbcType=BIGINT}, #{changeNum,jdbcType=INTEGER}, ",
        "#{balance,jdbcType=INTEGER}, #{cancelTradeId,jdbcType=BIGINT}, ",
        "#{state,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(GroupDepositMemberChange record);

    @Select({
        "select",
        "id, relate_id, change_date, ticket_id, trade_id, change_num, balance, cancel_trade_id, ",
        "state, create_time, create_staff_id, update_time, update_staff_id",
        "from group_deposit_member_change",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="relate_id", property="relateId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_date", property="changeDate", jdbcType=JdbcType.DATE),
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_num", property="changeNum", jdbcType=JdbcType.INTEGER),
        @Result(column="balance", property="balance", jdbcType=JdbcType.INTEGER),
        @Result(column="cancel_trade_id", property="cancelTradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    GroupDepositMemberChange selectByPrimaryKey(Long id);

    @Update({
        "update group_deposit_member_change",
        "set relate_id = #{relateId,jdbcType=BIGINT},",
          "change_date = #{changeDate,jdbcType=DATE},",
          "ticket_id = #{ticketId,jdbcType=BIGINT},",
          "trade_id = #{tradeId,jdbcType=BIGINT},",
          "change_num = #{changeNum,jdbcType=INTEGER},",
          "balance = #{balance,jdbcType=INTEGER},",
          "cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT},",
          "state = #{state,jdbcType=VARCHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(GroupDepositMemberChange record);

    @Select({
        "<script>",
        "select",
        "id, relate_id, change_date, ticket_id, trade_id, change_num, balance, cancel_trade_id, state, create_time, create_staff_id, update_time, update_staff_id",
        "from group_deposit_member_change",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"relateId != null\">and relate_id = #{relateId,jdbcType=BIGINT}</if>",
        "<if test=\"changeDate != null\">and change_date = #{changeDate,jdbcType=DATE}</if>",
        "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
        "<if test=\"tradeId != null\">and trade_id = #{tradeId,jdbcType=BIGINT}</if>",
        "<if test=\"changeNum != null\">and change_num = #{changeNum,jdbcType=INTEGER}</if>",
        "<if test=\"balance != null\">and balance = #{balance,jdbcType=INTEGER}</if>",
        "<if test=\"cancelTradeId != null\">and cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="relate_id", property="relateId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_date", property="changeDate", jdbcType=JdbcType.DATE),
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="change_num", property="changeNum", jdbcType=JdbcType.INTEGER),
        @Result(column="balance", property="balance", jdbcType=JdbcType.INTEGER),
        @Result(column="cancel_trade_id", property="cancelTradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<GroupDepositMemberChange> selectByFields(GroupDepositMemberChange param);

    @Update({
        "<script>",
        "update group_deposit_member_change",
        "<set>",
        "<if test=\"relateId != null\">relate_id = #{relateId,jdbcType=BIGINT},</if>",
        "<if test=\"changeDate != null\">change_date = #{changeDate,jdbcType=DATE},</if>",
        "<if test=\"ticketId != null\">ticket_id = #{ticketId,jdbcType=BIGINT},</if>",
        "<if test=\"tradeId != null\">trade_id = #{tradeId,jdbcType=BIGINT},</if>",
        "<if test=\"changeNum != null\">change_num = #{changeNum,jdbcType=INTEGER},</if>",
        "<if test=\"balance != null\">balance = #{balance,jdbcType=INTEGER},</if>",
        "<if test=\"cancelTradeId != null\">cancel_trade_id = #{cancelTradeId,jdbcType=BIGINT},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(GroupDepositMemberChange record);

    @Select({
            "select e.*"  ,
            "from group_deposit_member_change a,entry_info e"  ,
            "where a.ticket_id = e.ticket_id"  ,
            "and a.change_date = curdate()"  ,
            "and a.relate_id = #{relateId}"  ,
            "and e.venue_id = #{venueId}"  ,
            "order by e.enter_in_time desc"  ,
            "limit 1"
    })
    @Results({
            @Result(column = "ticket_id", property = "ticketId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "service_id", property = "serviceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "entry_trade_id", property = "entryTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_method", property = "enterMethod", jdbcType = JdbcType.CHAR),
            @Result(column = "key_id", property = "keyId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "key_bind_time", property = "keyBindTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "key_unbind_time", property = "keyUnbindTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_in_time", property = "enterInTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "enter_out_time", property = "enterOutTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "state", property = "state", jdbcType = JdbcType.VARCHAR),
            @Result(column = "trade_id", property = "tradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "enter_state", property = "enterState", jdbcType = JdbcType.CHAR),
            @Result(column = "enter_times", property = "enterTimes", jdbcType = JdbcType.INTEGER),
            @Result(column = "last_enter_time", property = "lastEnterTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "last_exit_time", property = "lastExitTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "ent_member_id", property = "entMemberId", jdbcType = JdbcType.BIGINT),
            @Result(column = "out_trade_id", property = "outTradeId", jdbcType = JdbcType.BIGINT),
            @Result(column = "last_paid_time", property = "lastPaidTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "related_staff_id", property = "relatedStaffId", jdbcType = JdbcType.BIGINT)
    })
    EntryInfo selectLastEntryInfo(@Param("relateId") Long relateId,
                                  @Param("venueId") Long venueId);
}
