package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class HolidayDays extends HolidayDaysKey {
    private String dayoffTag;

    private String holidayTypes;

    private Integer customWeekday;

    private String defaultHolidayTag;

    private Date createTime;

    private Date updateTime;

    private Long updateStaffId;

    public String getDayoffTag() {
        return dayoffTag;
    }

    public void setDayoffTag(String dayoffTag) {
        this.dayoffTag = dayoffTag == null ? null : dayoffTag.trim();
    }

    public String getHolidayTypes() {
        return holidayTypes;
    }

    public void setHolidayTypes(String holidayTypes) {
        this.holidayTypes = holidayTypes == null ? null : holidayTypes.trim();
    }

    public Integer getCustomWeekday() {
        return customWeekday;
    }

    public void setCustomWeekday(Integer customWeekday) {
        this.customWeekday = customWeekday;
    }

    public String getDefaultHolidayTag() {
        return defaultHolidayTag;
    }

    public void setDefaultHolidayTag(String defaultHolidayTag) {
        this.defaultHolidayTag = defaultHolidayTag == null ? null : defaultHolidayTag.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", dayoffTag=").append(dayoffTag);
        sb.append(", holidayTypes=").append(holidayTypes);
        sb.append(", customWeekday=").append(customWeekday);
        sb.append(", defaultHolidayTag=").append(defaultHolidayTag);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append("]");
        return sb.toString();
    }
}