package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class MiniAppStaff {
    private Long id;

    private String nickName;

    private Long miniAppId;

    private String miniAppOpenId;

    private Long staffId;

    private String state;

    private String avatar;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName == null ? null : nickName.trim();
    }

    public Long getMiniAppId() {
        return miniAppId;
    }

    public void setMiniAppId(Long miniAppId) {
        this.miniAppId = miniAppId;
    }

    public String getMiniAppOpenId() {
        return miniAppOpenId;
    }

    public void setMiniAppOpenId(String miniAppOpenId) {
        this.miniAppOpenId = miniAppOpenId == null ? null : miniAppOpenId.trim();
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar == null ? null : avatar.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", nickName=").append(nickName);
        sb.append(", miniAppId=").append(miniAppId);
        sb.append(", miniAppOpenId=").append(miniAppOpenId);
        sb.append(", staffId=").append(staffId);
        sb.append(", state=").append(state);
        sb.append(", avatar=").append(avatar);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}