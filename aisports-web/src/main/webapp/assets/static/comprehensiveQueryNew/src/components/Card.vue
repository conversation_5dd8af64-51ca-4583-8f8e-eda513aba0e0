<template>
    <div>
        <div class="query-line-box">
            <div class="query-line">
                <div class="query-box">
                    <div class="query-name">所属场馆</div>
                    <div class="query-form">
                        <el-select
                            v-model="venueId"
                            size="medium"
                            filterable
                            placeholder="请选择所属场馆">
                            <el-option
                                v-for="item in venueList"
                                :key="item.venueId"
                                :label="item.venueName"
                                :value="item.venueId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="query-box">
                    <div class="query-name">卡状态</div>
                    <div class="query-form">
                        <el-select
                            v-model="depositStatus"
                            size="medium"
                            filterable
                            placeholder="请选择卡状态">
                            <el-option
                                v-for="item in depositStatusList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="query-box">
                    <div class="query-name">销售时间</div>
                    <div class="query-form">
                        <el-date-picker
                            v-model="saleStartDate"
                            type="date"
                            size="medium"
                            prefix-icon="false"
                            clear-icon="false"
                            placeholder="开始日期">
                        </el-date-picker>
                        <div class="spe">-</div>
                        <el-date-picker
                            v-model="saleEndDate"
                            type="date"
                            size="medium"
                            prefix-icon="false"
                            clear-icon="false"
                            placeholder="结束日期">
                        </el-date-picker>
                    </div>
                </div>
                <div class="query-box">
                    <div class="query-name">到期时间</div>
                    <div class="query-form">
                        <el-date-picker
                            v-model="expireStartDate"
                            type="date"
                            size="medium"
                            prefix-icon="false"
                            clear-icon="false"
                            placeholder="开始日期">
                        </el-date-picker>
                        <div class="spe">-</div>
                        <el-date-picker
                            v-model="expireEndDate"
                            type="date"
                            size="medium"
                            prefix-icon="false"
                            clear-icon="false"
                            placeholder="结束日期">
                        </el-date-picker>
                    </div>
                </div>
            </div>
            <div class="query-line">
                <div class="query-box query-box-auto">
                    <div class="query-name">卡类别</div>
                    <div class="query-form">
                        <el-select
                            v-model="limitTypes"
                            size="medium"
                            multiple
                            filterable
                            collapse-tags
                            placeholder="请选择卡类别">
                            <el-option
                                v-for="item in cardTypeList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                            </el-option>
                        </el-select>
                        <el-select
                            v-model="productIds"
                            size="medium"
                            multiple
                            filterable
                            collapse-tags
                            placeholder="请选择">
                            <el-option
                                v-for="item in productList"
                                :key="item.productId"
                                :label="item.productName"
                                :value="item.productId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="query-line">
                <div class="query-box">
                    <div class="query-name">卡到期</div>
                    <div class="query-form">
                        <el-select
                            v-model="cardExpireTag"
                            size="medium"
                            placeholder="请选择">
                            <el-option
                                v-for="item in cardExpireTagList"
                                :key="item.key"
                                :label="item.name"
                                :value="item.key">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="query-box">
                    <div class="query-name">会籍顾问</div>
                    <div class="query-form">
                        <el-select
                            v-model="consultantId"
                            size="medium"
                            filterable
                            placeholder="请选择会籍顾问">
                            <el-option
                                v-for="item in consultantList"
                                :key="item.consultantId"
                                :label="item.consultantName"
                                :value="item.consultantId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="query-box">
                    <div class="query-name">教练</div>
                    <div class="query-form">
                        <el-select
                            v-model="coachId"
                            size="medium"
                            filterable
                            placeholder="请选择教练">
                            <el-option
                                v-for="item in coachList"
                                :key="item.coachId"
                                :label="item.coachName"
                                :value="item.coachId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="query-line balance-line">
                <div class="query-box">
                    <div class="query-name">储值卡</div>
                    <div class="query-form">
                        <el-input :disabled="balanceList[0].disabled" size="small" type="number" placeholder="最低额" v-model="balanceList[0].minBalance"></el-input>&nbsp;—&nbsp;<el-input :disabled="balanceList[0].disabled" size="small" placeholder="最高额" v-model="balanceList[0].maxBalance"></el-input>
                    </div>
                </div>
                <div class="query-box">
                    <div class="query-name">次卡</div>
                    <div class="query-form">
                        <el-input :disabled="balanceList[1].disabled" size="small" type="number" placeholder="最少次数" v-model="balanceList[1].minBalance"></el-input>&nbsp;—&nbsp;<el-input :disabled="balanceList[1].disabled" size="small" placeholder="最多次数" v-model="balanceList[1].maxBalance"></el-input>
                    </div>
                </div>
                <div class="query-box">
                    <div class="query-name">期间卡</div>
                    <div class="query-form">
                        <el-input :disabled="balanceList[2].disabled" size="small" type="number" placeholder="最少天数" v-model="balanceList[2].minBalance"></el-input>&nbsp;—&nbsp;<el-input :disabled="balanceList[2].disabled" size="small" placeholder="最多天数" v-model="balanceList[2].maxBalance"></el-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="btn-primary-line">
            <button
                class="btn btn-primary"
                @click="getCardList">查询</button>
        </div>
        <div class="table-box">
            <div class="add-btn-gray" >
                <el-switch
                    v-model="activeTag"
                    active-value="0"
                    inactive-value=""
                    active-text="显示未开卡的会员">
                </el-switch>
                <!-- <button
                    v-if="showExportButton"
                    class="btn btn-md btn-default add-btn-gray"
                    @click="exportCardList">
                    <i class="iconfont icon-daochu"></i>
                    <span>导出</span>
                </button> -->
                <el-dropdown v-if="showExportButton" @command="exportCardList">
                    <el-button  size="small" type="primary">
                        导出<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item  command="xlsx">xlsx</el-dropdown-item>
                        <el-dropdown-item  command="xls">xls</el-dropdown-item>
                    </el-dropdown-menu>
                </el-dropdown>
            </div>
            <table class="simple-splitter">
                <thead>
                    <tr>
                        <th>卡号</th>
                        <th>卡状态</th>
                        <th>开卡时间</th>
                        <th>卡主</th>
                        <th>卡类别</th>
                        <th>卡类型</th>
                        <th>余额</th>
                        <th>卡有效期</th>
                        <th>是否到期</th>
                        <th>联系电话</th>
                        <th>销售时间</th>
                        <th>所属场馆</th>
                        <th>会籍顾问</th>
                        <th>发展人</th>
                        <th>教练</th>
                        <th>卡备注</th>
                        <th>会员查询</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="item in list">
                        <td>{{ item.ecardNo }}</td>
                        <td>{{ item.depositStatus }}</td>
                        <td>{{ (item.activeTime || '').substring(0,10) }}</td>
                        <td>{{ item.custName }}</td>
                        <td>{{ item.productName }}</td>
                        <td>{{ item.productTypeName }}</td>
                        <td v-if="item.limitType==='1'">{{ item.balance }}次</td>
                        <td v-else-if="item.limitType==='2'">{{ m2front(item.balance) }}元</td>
                        <td v-else></td>
                        <td v-if="item.endDate.substring(0,10)==='2050-12-31'">永久</td>
                        <td v-else>{{ (item.endDate || '').substring(0,10) }}</td>
                        <td>{{ getDateStr(item.endDate) }}</td>
                        <td v-if="hideSensitiveTag">{{ hideSensitiveInfoOfPhone(item.contactPhone) }}</td>
                        <td v-else>{{ item.contactPhone }}</td>
                        <td>{{ (item.createTime || '').substring(0,10) }}</td>
                        <td>{{ item.venueName }}</td>
                        <td>{{ item.consultantName }}</td>
                        <td>{{ item.developerName }}</td>
                        <td>{{ item.coachName }}</td>
                        <td>
                          <template v-if="item.remark && item.remark.length>10">
                            <el-tooltip class="item" effect="light" :content="item.remark" placement="bottom">
                              <el-button> <span>{{ item.remark.substr(0,10) + '...'}}</span></el-button>
                            </el-tooltip>
                          </template>
                          <template v-else>
                            {{ item.remark }}
                          </template>
                        </td>
                        <td><div
                            class="iconfont icon-xiangyou-2"
                            @click="toMemberQuery(item.ecardNo)"></div></td>
                    </tr>
                </tbody>
            </table>
            <div class="bottom-line">
                <div class="total-num">合计：<i>{{ total }}</i>条</div>
                <div class="total-num">总剩余余额：<i>{{ m2front(cardTotalBalanceInfo.moneyTotal || 0) }}</i>元</div>
                <div class="total-num">总剩余次数：<i>{{ cardTotalBalanceInfo.timesTotal }}</i>次</div>
                <paginator
                    v-if="total"
                    :page-size="pageSize"
                    :total="total"
                    :current-page="pageNo"
                    @changepagesize="handlePageSizeChange"
                    @changepage="handlePageChange"></paginator>
            </div>
        </div>
    </div>
</template>

<script>
import { DatePicker, Select, Option, Switch, Input ,Tooltip,Button} from 'element-ui'
import * as API from './API'
import evt from 'utils/VueEventBus'
import param2list from 'utils/param2list'
import moment from 'moment'
import _ from 'lodash'
import Pagination from 'vue-components/Pagination.vue'
import mAlert from 'mAlert'
import { hideSensitiveInfoOfPhone } from 'utils/hideSensitiveTag'
import settings from 'aisportsConfig'
import { m2front, m2back } from 'utils/moneyUtils'

export default{
    components: {
        'el-date-picker': DatePicker,
        'el-select': Select,
        'el-option': Option,
        'el-switch': Switch,
        'el-input': Input,
        'el-tooltip':Tooltip,
        'el-button': Button,
        paginator: Pagination,
    },
    data() {
        return {
            consultantId: '',
            consultantList: [],
            coachId: '',
            coachList: [],
            userStatusList: [],
            genderList: [],
            venueId: '',
            venueList: [],
            depositStatus: '',
            depositStatusList: [],
            cardTypeList: [],

            saleStartDate: '',
            saleEndDate: '',
            expireStartDate: '',
            expireEndDate: '',
            limitTypes: [],
            productList: [],
            productIds: [],
            cardExpireTagList: [{
                key: '',
                name: '全部',
            }, {
                key: 1,
                name: '是',
            }, {
                key: 0,
                name: '否',
            }],
            cardExpireTag: '',
            hideSensitiveTag: '',
            showExportButton: false,
            activeTag: '',
            total: 0,
            pageNo: 1,
            pageSize: 20,
            list: [],
            cardTotalBalanceInfo: {},
            params: {},
            balanceList: [
                {
                    minBalance: '',
                    maxBalance: '',
                    disabled: false
                },
                {
                    minBalance: '',
                    maxBalance: '',
                    disabled: false
                },
                {
                    minBalance: '',
                    maxBalance: '',
                    disabled: false
                }
            ]
        }
    },
    watch: {
        saleStartDate() {
            this.watchStartDate('saleStartDate', 'saleEndDate')
        },
        saleEndDate() {
            this.watchEndDate('saleStartDate', 'saleEndDate')
        },
        expireStartDate() {
            this.watchStartDate('expireStartDate', 'expireEndDate')
        },
        expireEndDate() {
            this.watchEndDate('expireStartDate', 'expireEndDate')
        },
        limitTypes() {
            this.getProductList()
        },
         venueId(){
           this.getProductList()
         },
        activeTag() {
            this.getCardList()
        },
        balanceList: {
            handler(val) {
                const i = val.findIndex((item) => {
                    return item.minBalance || item.maxBalance
                })
                console.log(i)
                val.forEach((item, index) => {
                    if (i === -1) {
                        this.balanceList[index].disabled = false
                    } else {
                        this.balanceList[index].disabled = i !== index
                    }
                })
            },
            deep: true
        }
    },
    mounted() {
        this.memberListQueryInit()
    },
    methods: {
        memberListQueryInit() {
            API.memberListQueryInit((res) => {
                this.params.hideSensitiveTag = res.hideSensitiveTag
                this.params.showExportButton = res.showExportButton
                this.params.consultantList = [{
                    consultantId: '',
                    consultantName: '全部',
                }, ...res.consultantList || []]
                this.params.coachList = [{
                    coachId: '',
                    coachName: '全部',
                }, ...res.coachList || []]
                this.params.userStatusList = [{
                    key: '',
                    value: '全部',
                }]
                if (res.userStatusList && res.userStatusList.length) {
                    res.userStatusList.forEach((item) => {
                        this.params.userStatusList.push(...param2list(item))
                    })
                }
                this.params.venueList = [{
                    venueId: '',
                    venueName: '全部',
                }, ...res.venueList || []]
                this.params.depositStatusList = [{
                    key: '',
                    value: '全部',
                }]
                if (res.depositStatusList && res.depositStatusList.length) {
                    res.depositStatusList.forEach((item) => {
                        this.params.depositStatusList.push(...param2list(item))
                    })
                }
                this.params.cardTypeList = [{
                    key: '',
                    value: '全部',
                }]
                if (res.cardTypeList && res.cardTypeList.length) {
                    res.cardTypeList.forEach((item) => {
                        this.params.cardTypeList.push(...param2list(item))
                    })
                }
                this.hideSensitiveTag = this.params.hideSensitiveTag
                this.showExportButton = this.params.showExportButton
                this.consultantList = this.params.consultantList
                this.coachList = this.params.coachList
                this.userStatusList = this.params.userStatusList
                this.venueList = this.params.venueList
                this.depositStatusList = this.params.depositStatusList
                this.cardTypeList = this.params.cardTypeList
            })
        },
        m2front,
        hideSensitiveInfoOfPhone,
        watchStartDate(startDate, endDate) {
            if (moment(this[startDate]).format('x') > moment(this[endDate]).format('x')) {
                this[endDate] = this[startDate]
            }
        },
        watchEndDate(startDate, endDate) {
            if (moment(this[endDate]).format('x') < moment(this[startDate]).format('x')) {
                this[startDate] = this[endDate]
            }
        },
        getProductList() {
            API.getProductList({
                venueId: this.venueId,
                limitTypes: this.limitTypes.toString(),
            }, (res) => {
                this.productIds = []
                this.productList = res.productList
            })
        },
        formatDate(d) {
            if (d) {
                return moment(d).format('YYYYMMDD')
            }
            return ''

        },
        collectData() {
            const balance = this.balanceList.find((item) => {
                return item.minBalance || item.maxBalance
            })
            const params =  {
                page: this.pageNo,
                pageSize: this.pageSize,
                venueId: this.venueId,
                depositStatus: this.depositStatus,
                saleStartDate: this.formatDate(this.saleStartDate),
                saleEndDate: this.formatDate(this.saleEndDate),
                limitTypes: this.limitTypes.toString(),
                productIds: this.productIds.toString(),
                expireStartDate: this.formatDate(this.expireStartDate),
                expireEndDate: this.formatDate(this.expireEndDate),
                activeTag: this.activeTag,
                cardExpireTag: this.cardExpireTag,
                consultantId: this.consultantId,
                coachId: this.coachId,
            }
            if (balance) {
                params.minBalance = balance.minBalance
                params.maxBalance = balance.maxBalance
            }
            return params
        },
        getCardList() {
            API.getCardList(this.collectData(), (res) => {
                this.list = res.pageInfo.list
                this.pageNo = res.pageInfo.pageNum
                this.total = res.pageInfo.total
                this.cardTotalBalanceInfo = res.cardTotalBalanceInfo
            })
        },
        handlePageChange(p) {
            this.pageNo = p
            this.getCardList()
        },
        handlePageSizeChange(newPageSize) {
            this.pageSize = newPageSize
            this.getCardList()
        },
        getDateStr(d) {
            if (moment().diff(moment(d)) > 0) {
                return '是'
            }
            return '否'

        },
        toMemberQuery(ecardNo) {
            if (ecardNo) {
                // window.location.href = `${settings.urlPrefix}/memberSearch/index?ecardNo=${ecardNo}`
              window.open(`${settings.urlPrefix}/memberSearch/index?ecardNo=${ecardNo}`)
            }
        },
        exportCardList(exportType) {
        let params = this.collectData()
        params.exportType = exportType
            settings.util.simulationForm('/memberListQuery/exportCardList', params)
        },
    },
}
</script>
