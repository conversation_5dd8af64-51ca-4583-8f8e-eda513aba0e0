package com.asiainfo.aisports.service.wxpalms;

import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.service.async.AsyncManager;
import com.asiainfo.aisports.service.async.AsyncTaskFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateExpiredException;
import java.security.cert.CertificateFactory;
import java.security.cert.CertificateNotYetValidException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static org.apache.http.HttpHeaders.ACCEPT;
import static org.apache.http.HttpStatus.SC_OK;
import static org.apache.http.entity.ContentType.APPLICATION_JSON;

/**
 * <AUTHOR>
 * @date 2024/1/12 14:44
 */
public class PlatCertVerifier implements Verifier {

    private static final Logger logger = LoggerFactory.getLogger(PlatCertVerifier.class);
    private static final String CERT_DOWNLOAD_PATH = "https://api.mch.weixin.qq.com/v3/certificates";


    private final Map<BigInteger, X509Certificate> certificateMap = new ConcurrentHashMap<>();

    private WxPalmsClient certUpdateClient = null;

    private Long centerId;

    private final Object updateObj = new Object();

    public PlatCertVerifier(List<X509Certificate> certificateList, WxPalmsClient wxPalmsClient) {
        for (X509Certificate x509Certificate : certificateList) {
            certificateMap.put(x509Certificate.getSerialNumber(), x509Certificate);
        }
        this.certUpdateClient = wxPalmsClient;
        this.centerId = wxPalmsClient.getCenterId();

    }

    public PlatCertVerifier(List<X509Certificate> certificateList) {
        for (X509Certificate x509Certificate : certificateList) {
            certificateMap.put(x509Certificate.getSerialNumber(), x509Certificate);
        }
    }

    @Override
    public boolean verify(String certSerialNo, String message, String sign) {
        BigInteger bigInteger = new BigInteger(certSerialNo, 16);
        X509Certificate certificate = certificateMap.get(bigInteger);
        if (certificate == null) {
            try {
                certificate = updateCert(bigInteger);
            } catch (Exception e) {
                logger.error("update certificate failed", e);

            }
        }

        if (certificate == null) {
            throw new PalmsException("cannot find wxCert serialNo=" + certSerialNo);
        }

        try {
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(certificate);
            signature.update(message.getBytes(StandardCharsets.UTF_8));
            return signature.verify(Base64.getDecoder().decode(sign));
        } catch (NoSuchAlgorithmException e) {
            throw new PalmsException("当前Java环境不支持SHA256withRSA", e);
        } catch (SignatureException e) {
            throw new PalmsException("签名验证过程发生了错误", e);
        } catch (InvalidKeyException e) {
            throw new PalmsException("无效的证书", e);
        }
    }

    private X509Certificate updateCert(BigInteger certSerialNo) throws GeneralSecurityException, IOException {
        if (certUpdateClient == null) {
            return null;
        }

        X509Certificate certificate;
        synchronized (updateObj) {
            if ((certificate = this.certificateMap.get(certSerialNo)) != null) {
                return certificate;
            }

            HttpGet httpGet = new HttpGet(CERT_DOWNLOAD_PATH);
            httpGet.addHeader(ACCEPT, APPLICATION_JSON.toString());
            try (CloseableHttpResponse response = certUpdateClient.getHttpClient().execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();
                String body = EntityUtils.toString(response.getEntity());
                if (statusCode == SC_OK) {
                    List<X509Certificate> newCertList = deserializeToCerts(body);
                    for (X509Certificate x509Certificate : newCertList) {
                        this.certificateMap.put(x509Certificate.getSerialNumber(), x509Certificate);
                    }

                }

            }
            return this.certificateMap.get(certSerialNo);

        }
    }

    protected List<X509Certificate> deserializeToCerts(String body)
            throws GeneralSecurityException, IOException {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode dataNode = mapper.readTree(body).get("data");
        List<X509Certificate> newCertList = new ArrayList<>();
        if (dataNode != null) {
            for (int i = 0, count = dataNode.size(); i < count; i++) {
                JsonNode node = dataNode.get(i).get("encrypt_certificate");
                String serialNo = dataNode.get(i).get("serial_no").asText();
                //解密
                String cert = certUpdateClient.decryptToString(
                        node.get("associated_data").toString().replace("\"", "")
                                .getBytes(StandardCharsets.UTF_8),
                        node.get("nonce").toString().replace("\"", "")
                                .getBytes(StandardCharsets.UTF_8),
                        node.get("ciphertext").toString().replace("\"", ""));

                CertificateFactory cf = CertificateFactory.getInstance("X509");
                X509Certificate x509Cert = (X509Certificate) cf.generateCertificate(
                        new ByteArrayInputStream(cert.getBytes(StandardCharsets.UTF_8))
                );
                try {
                    x509Cert.checkValidity();
                } catch (CertificateExpiredException | CertificateNotYetValidException e) {
                    continue;
                }
                newCertList.add(x509Cert);

                AsyncManager.me().execute(AsyncTaskFactory.savePalmsCert(this.centerId, serialNo, cert, x509Cert.getNotBefore(), x509Cert.getNotAfter()));
            }
        }
        return newCertList;
    }

}
