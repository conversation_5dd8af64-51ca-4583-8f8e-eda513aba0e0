//结构.form-row>(.name>span)+(.detail>input)
//标题
.form-title{
  height: 55px;
  line-height: 55px;
  border-bottom: 1px solid #eff2f7;
  padding-left: 10px;
  position: relative;
  color: #000000;
  &:after{
    content: '';
    width: 4px;
    height: 14px;
    background-color: #1fa2f5;
    position: absolute;
    left: 0;
    top:50%;
    margin-top: -7px;
  }
}
//单行
.form-row{
  font-size: 0;
  min-height: 34px;
  position: relative;
  .input-tip{
    color: #fc5f45;
    margin-left: 138px;
    display: block;
    font-size: 12px;
    line-height: 18px;
  }
  &>*{
    font-size: 14px;
  }
  .name{
    width: 138px;
    text-align: right;
    display: inline-block;
    line-height: 34px;
    height: 34px;
    position: absolute;
    left: 0;
    top:0;
    &.required{
      &:before{
        content : '*';
        color : #fc5f45;
        display: inline-block;
        margin-right : 4px;
        font-size: 30px;
        height: 34px;
        line-height: 45px;
        vertical-align: middle;
      }
    }
    span{
      display: inline-block;
      height: 34px;
      line-height: 34px;
    }
  }
  .detail{
    padding-left: 138px;
    line-height: 34px;
    .radio-outer{
      span{
        vertical-align: middle;
      }
    }
    .radio-outer + .radio-outer{
      margin-left: 30px;
    }
    textarea{
      border: 1px solid #e0e0e0;
      width: 360px;
      height: 68px;
      padding: 10px;
      line-height: 18px;
      display: block;
    }
    .upload-btn{
      height: 34px;
      line-height: 34px;
      background-color: #1fa2f5;
      padding: 0 8px;
      color: #fff;
      display: inline-block;
      border-radius: 5px;
      cursor: pointer;
      .icon-shangchuan{
        margin-right: 5px;
      }
      position: relative;
      overflow: hidden;
      vertical-align: middle;
      .icon-shangchuan{
        margin-right: 5px;
      }
      input{
        opacity: 0;
        position: absolute;
        left: -10px;
        height: 34px;
        right: 0;
        bottom: 0;
        top:0;
        cursor: pointer;
      }
    }
    .upload-tips{
      display: inline-block;
      margin-left: 20px;
      vertical-align: middle;
    }
    .uploaded-files{
      margin-top: 10px;
      .file-list{
        height: 90px;
        padding: 10px;
        width: 360px;
        position: relative;
        .img-box{
          position: absolute;
          left: 10px;
          top: 10px;
          width: 70px;
          height: 70px;
          cursor: pointer;
          display: inline-block;
          img{
            width: 100%;
            height: 100%;
          }
          .icon-yanjing{
            position: absolute;
            width: 30px;
            text-align: center;
            height: 30px;
            line-height: 30px;
            left: 50%;
            margin-left: -15px;
            top:50%;
            margin-top: -15px;
            color: #fff;
            display: none;
          }
        }
        .img-name{
          width: 320px;
          padding-left: 80px;
          height: 70px;
          color: #666666;
          display: table;
          .inner{
            display: table-cell;
            vertical-align: middle;
            height: 70px;
            line-height: 20px;
          }
        }
        .icon-cuowu2{
          position: absolute;
          right: 10px;
          height: 10px;
          line-height: 10px;
          top: 50%;
          margin-top: -5px;
          color: #8d8d8e;
          cursor: pointer;
          display: none;
        }
        .icon-gou{
          width: 14px;
          text-align: center;
          height: 14px;
          line-height: 14px;
          background-color: #67c23a;
          border-radius: 100%;
          color: #fff;
          right: 10px;
          top: 50%;
          margin-top: -7px;
          position: absolute;
          font-size: 10px;
        }
        &:hover{
          background-color: #f5f7fa;
          border-radius: 3px;
          .icon-gou{
            display: none;
          }
          .icon-cuowu2{
            display: block;
          }
          .img-box{
            .icon-yanjing{
              display: block;
            }
          }
        }
      }
    }
  }
  label.detail{
    span{
      vertical-align: middle;
    }
  }
  &.text-line-row{
    margin-top: 10px!important;
  }
}
.form-row + .form-row{
  margin-top: 20px;
}
.tip-icon{
  display: inline-block;
  height: 14px;
  width: 14px;
  text-align: center;
  line-height: 14px;
  background-color: #333333;
  color: #fff;
  border-radius: 100%;
  margin-left: 5px;
  cursor: pointer;
}
.common-tips{
  height: 36px;
  line-height: 36px;
  border: 1px solid #333333;
  background-color: #fff;
  z-index: 1;
  border-radius: 5px;
  padding: 0 10px;
  white-space: nowrap;
  position: relative;
  &.trigger-top{
    &:after{
      position: absolute;
      border-style: solid;
      border-color: transparent transparent #000 transparent;
      border-width: 10px 6px 10px 6px;
      content: '';
      left: 16px;
      top: -21px;
      z-index: 1;
    }
    &:before{
      position: absolute;
      border-style: solid;
      border-color: transparent transparent #fff transparent;
      border-width: 10px 6px 10px 6px;
      content: '';
      left: 16px;
      top: -19px;
      z-index: 2;
    }
  }
  &.trigger-left{
    &:after{
      position: absolute;
      border-style: solid;
      border-color: transparent #000 transparent transparent;
      border-width: 6px 10px 6px 10px;
      content: '';
      left: -21px;
      top: 11px;
      z-index: 1;
    }
    &:before{
      position: absolute;
      border-style: solid;
      border-color: transparent #fff transparent transparent;
      border-width: 6px 10px 6px 10px;
      content: '';
      left: -19px;
      top: 11px;
      z-index: 2;
    }
  }
}


.search-line{
  font-size: 0;
  &>input,&>.input-wrapper{
    font-size: 14px;
    margin-right: 10px;
  }
}

.user-info-box{
  border-radius: 5px;
  padding: 10px;
  border : 1px solid #ccc;
  min-height: 172px;
  margin-top: 20px;
  @include avatar();
  .avatar-box{
    float: left;
  }
  .detail{
    margin-left:160px;
    table{
      width: 100%;
      table-layout: fixed;
      tr + tr{
        border-top : 1px dashed #e4e4e4;
      }
      td{
        padding-left: 24px;
        span{
          vertical-align: middle;
        }
        .title{
          display: inline-block;
          height: 50px;
          line-height: 50px;
          width: 70px;
          @include justify();
        }
      }
    }
  }
  .init{
    background-color: #fafafa;
    height: 150px;
    position: relative;
    overflow: hidden;
    p{
      padding: 10px;
      display: inline-block;
      color: #333333;
      font-size: 16px;
    }
    .icon-changguan{
      position: absolute;
      color: #f2f2f2;
      font-size: 220px;
      right: -23px;
      bottom: -99px;
    }
  }
}
.state{
  height: 24px;
  line-height: 24px;
  padding: 0 12px;
  margin-left: 20px;
  border: 1px solid;
  border-radius: 12px;
  font-size: 12px;
  display: inline-block;
}
.state + .state{
  margin-left: 10px;
}
.state-normal{
  background-color: #e8f6fe;
  color: #2ca8f6;
  border-color: #bbe3fc;
}
.state-warning{
  background-color: #feeeee;
  color: #fa5555;
  border-color: #fdcccc;
}
.state-over{
  background-color: #f3f3f5;
  color: #8e949f;
  border-color: #dbdde0;
}
.state-success{
  background-color: #f0f9eb;
  color: #67c23a;
  border-color: #d1edc4;
}