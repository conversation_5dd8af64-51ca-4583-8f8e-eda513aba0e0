package com.asiainfo.aisports.pay.client;

/**
 * Created by mi<PERSON><PERSON> on 15/1/23.
 */
public class StringUtils {
    private StringUtils() {
    }

    /**
     * Returns the given string if it is non-null; the empty string otherwise.
     *
     * @param string the string to test and possibly return
     * @return {@code string} itself if it is non-null; {@code ""} if it is null
     */
    public static String nullToEmpty(String string) {
        return (string == null) ? "" : string;
    }
}
