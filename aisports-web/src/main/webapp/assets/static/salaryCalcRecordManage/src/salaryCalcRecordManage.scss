@import "../../frame/src/frame";
@import "./form";
@import "../../common/element-theme-xports/element-variables";
@import "../../common/element-theme-xports/element-select.scss";
@import "../../../modules/libs/element-ui/element-ui-new/index.css";

.calcRecord {
  background: #fff;
  padding-bottom: 20px;
  .search-line {
    position: relative;
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    .btn-primary {
      margin-left: 10px;
    }
    .calc-date {
      .el-range-input {
        width: 39%;
      }
      .el-range-separator {
        display: flex;
        align-items: center;
      }
      .el-input__icon {
        display: flex;
        align-items: center;
      }
    }

    .btns {
      margin-left: 10px;
      .reset-btn {
        margin-left: 5px;
      }
    }
    .sponsor_add{
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      align-items: center;
      color: #7b7b7b;
     i{
      margin-right: 6px;
      color: #7b7b7b;
      font-size: 14px;
     }
    }
    .sponsor {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
    }
    .timeout{
      position: absolute;
      right: 140px;
      top: 50%;
      transform: translateY(-50%);
    }
    .el-input,
    .el-select {
      width: 220px;
      margin-right: 10px;
    }

    .reset-btn {
      color: #000 !important;
    }
  }
  .form{
    .el-input {
      width: 220px;
      margin-right: 10px;
    }
  }
  .form-btn {
    margin-top: 20px;
    display: flex;
    padding: 0 20px;
    justify-content: flex-end;
  }

  .pagination-wrap {
    margin-right: 20px;
  }
}

.custom-tag {
  border-radius: 20px !important;
}

.setting {
  color: #1fa2f5;
  cursor: pointer;
  font-size: 14px;
  margin-right: 10px;

  &:hover {
    border-bottom: 1px solid #1fa2f5;
  }
}

.el-dialog__body {
  padding: 20px;
}

.dialog-class {
  .form {
    .form-label {
      display: inline-block;
      min-width: 100px;
    }
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0px;
    }
  }
}
.panel-heading {
  overflow: hidden;
  line-height: 40px;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: space-between;
  span {
    font-size: 16px;
  }
}
