<template>
    <div class="query-form-container title-box">
        <h3
            v-if="showVisable"
            class="border-title">
            <slot
                v-if="$slots.title"
                name="title"></slot>
            <template v-else>{{ title }}</template>
        </h3>
        <el-form
            v-bind="$attrs"
            class="form-query">
            <slot></slot>
            <el-row class="form-action">
                <el-col
                    :span="19"
                    class="action-left">
                    <slot name="actionLeft"></slot>
                </el-col>
                <el-col
                    :span="5"
                    class="text-right action-right">
                    <slot name="actionRight"></slot>
                    <el-button
                        :loading="isQuerying"
                        type="primary"
                        class="btn-action"
                        @click="querySubmit">{{ isQuerying ? '查询中...' : '查询' }}</el-button>
                </el-col>
            </el-row>
        </el-form>
    </div>
</template>

<style lang="scss">
.query-form-container {
    padding-top: 5px;
    .border-title {
        .btn-return {
            position: absolute;
            right: 20px;
            bottom: 6px;
        }
    }
}

.form-query {
    position: relative;
    .el-row>.el-col:first-child {
        padding-left: 0;
    }
    .el-row>.el-col:last-child {
        padding-right: 0;
    }
    .el-col {
        padding: 0 5px;
    }
    .el-form-item {
        margin-bottom: 15px;
    }
    .form-action .el-col{
        padding: 0;
        min-height: 1px;
    }
}
</style>


<script>
export default {
    name: 'QueryForm',
    components: {
        'el-form':
            () => import('element-ui').then(({ Form }) => Form),
        'el-row':
            () => import('element-ui').then(({ Row }) => Row),
        'el-col':
            () => import('element-ui').then(({ Col }) => Col),
        'el-button':
            () => import('element-ui').then(({ Button }) => Button),
    },
    props: {
        showVisable: {
            type: Boolean,
            default: true,
        },
        title: {
            type: String,
            default: '查询',
        },
    },
    data() {
        return {
            isQuerying: false,
        }
    },
    methods: {
        querySubmit() {
            const self = this;
            self.isQuerying = true;
            new Promise((resolve, reject) => {
                setTimeout(() => {
                    reject('timeout');
                }, 30 * 1000);
                self.$emit('submit', resolve);
            }).then(() => {
                self.isQuerying = false;
            }).catch(() => {
                self.isQuerying = false;
            });
            return false;
        },
    },
}
</script>
