package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.service.PromotionService;
import com.asiainfo.aisports.web.WebResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by liuchangyuan on 2019-06-21.
 *
 * 营销活动查询接口
 */
@RestController
@RequestMapping("/prom")
public class PromotionController {
    @Autowired
    PromotionService promotionService;

    /**
     * 获取一卡通充值优惠
     *
     * @param centerId
     * @param staff
     * @return
     */
    @RequestMapping(value = "/getRechargePromotions", method = RequestMethod.GET)
    public WebResult findRechargePromotions(Long centerId, LoginStaff staff) {
        if (centerId == null) {
            centerId = staff.getCenterId();
        }
        //获取一卡通充值优惠(已经根据centerId进行筛选了)
        List<DataMap> promotionList = promotionService.findRechargePromotions(centerId);
        return new WebResult().set("result", promotionList);
    }
}
