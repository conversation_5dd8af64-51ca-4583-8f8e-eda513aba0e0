<template>
    <div class="title-box">
        <h1 style="display:block">
            <span>{{ pageTitle }}</span>
            <span
                class="btn-return"
                @click="backToList">
                <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
            </span>
        </h1>
        <div class="sub-box">
            <side-menu
                :tab-list = "tabList"
                :on-selected = "onSelected"
            ></side-menu>
            <div class="op-con">
                <router-view @statechange="handleStateChange"></router-view>
            </div>
        </div>
    </div>
</template>
<script>
import SideMenu from 'vue-components/SideMenu.vue'
import { initDateRangePicker, initSelectPicker } from 'utils/initPlugins'
import _ from 'lodash'
import settings from 'aisportsConfig'

export default{
    components: {
        SideMenu,
    },
    data() {
        return {
            params: {},
            currentPageName: 'edit',
            pageInfo: {},
            tabList: [],
            editPageInfo: {},
        }
    },
    computed: {
        pageTitle() {
            const pms = this.params
            const type = this.params.pageType
            const actionName = this.params.isCopy? '复制':type === 'add' ? '新增' : '编辑'
            const title = this.params.isCopy ? '优惠券': type === 'add' ? pms.couponTypeName : pms.couponName
            return actionName + title
        },
    },
    beforeRouteEnter(to, from, next) {
        next((vm) => {
            vm.getParams(to)
        })
    },
    methods: {
        handleStateChange(extParam) {
            this.getParams(this.$route, extParam)
        },
        backToList() {
            //                window.location.href = settings.urlPrefix + '/couponConfig/init';
            window.history.back()
        },
        getParams(to, extParam) {
            const pageType = to.query.pageType
            const basicRouteName = `${pageType}-basic`;
            const useRouteName = pageType + ((to.query.couponType !== '1' && to.query.couponType !== '5') ? '-use' : '-venue')
            const targetRouteName = to.name ? to.name : basicRouteName
            this.params = _.extend({}, to.query, extParam || {})
            this.tabList = [{
                tabText: '基本信息',
                active: targetRouteName === basicRouteName ? true : undefined,
                routerName: basicRouteName,
            },
            ]
            if (this.params.couponType !== '4') {
                this.tabList = [
                    ...this.tabList,
                    {
                        tabText: '使用配置',
                        routerName: useRouteName,
                        active: useRouteName === targetRouteName ? true : undefined,
                        disabled: pageType === 'add',
                    },
                ]
            }
        },
        onSelected(tab) {
            const couponType = this.params.couponType
            const pageType = this.params.pageType
            const routerInfo = {
                query: this.params,
                name: tab.routerName,
            }
            this.tabList = _.map(this.tabList, (t) => {
                t.active = tab.routerName === t.routerName ? true : undefined
                return t
            })
            this.$router.replace(routerInfo)
        },
    },
}
</script>
