package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.model.CustInfo;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.service.CourseEnrollFreezeService;
import com.asiainfo.aisports.service.CustQueryService;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.tools.TradeConstants;
import com.asiainfo.aisports.web.WebResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-04-07 11:45
 */
@Controller
@RequestMapping("/unfreezeEnroll")
public class UnFreezeEnrollController {

    @Autowired
    private CourseEnrollFreezeService courseEnrollFreezeService;

    @Autowired
    private CustQueryService custQueryService;


    @RequestMapping
    public String index(Model model) {
        model.addAttribute("tradeTypeCode", TradeConstants.TradeTypeCode.UNFREEZE_COURSE.toString());
        return "unfreezeEnroll/unfreezeEnroll";
    }


    @RequestMapping("/getUnFreezeCandidates")
    @ResponseBody
    public WebResult getUnFreezeCandidates(String ecardNo, LoginStaff staff) {
        if (staff.getInstId() == null) {
            return new WebResult(1, "当前场馆没有课程资源");
        }
        if (StringUtils.isBlank(ecardNo)) {
            return new WebResult(1, "请输入卡号");
        }
        CustInfo custInfo = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        List<DataMap> freezeList = courseEnrollFreezeService.getUnFreezeCandidates(custInfo.getCustId(), staff.getInstId());
        return new WebResult().set("list", freezeList);
    }

    @RequestMapping(value = "/unFreeze",method = RequestMethod.POST)
    @ResponseBody
    public WebResult unFreeze(Long[] freezeIds,
                              String ecardNo,
                              String unfreezeType,
                              String remark,
                              String agreementBase64, Long agreementId, String agreementInfo,
                              LoginStaff staff) {
        if (freezeIds == null || freezeIds.length == 0) {
            return new WebResult(1, "请选择冻结信息");
        }

        ServiceResult serviceResult = courseEnrollFreezeService.submitUnFreeze(new ArrayList<>(Arrays.asList(freezeIds)), ecardNo, unfreezeType, remark, agreementBase64, agreementId, agreementInfo, staff);
        WebResult result = new WebResult(serviceResult);
        result.setRedirect("/unfreezeEnroll");
        return result;
    }

}
