package com.asiainfo.aisports.service.wechat;

import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.WechatAccount;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.helper.Errors;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.WechatAccountMapper;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.WechatService;
import com.asiainfo.aisports.service.baidu.BaiduApiService;
import com.google.common.base.Strings;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by xzeng on 16/7/14.
 */
@Service
public class WechatAccountService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WechatAccountService.class);
    @Autowired
    WechatAccountMapper wechatAccountMapper;
    @Autowired
    VenueParamConfig venueParamConfig;
    @Autowired
    WechatService wechatService;
    @Autowired
    private BaiduApiService baiduApiService;

    /**
     * 根据主键获取公众号
     *
     * @param accountId
     * @return
     */
    @TargetDataSource(name = "center")
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public WechatAccount getAccountById(Long accountId) {
        return wechatAccountMapper.selectByPrimaryKey(accountId);
    }

    @TargetDataSource(name = "center")
    @Transactional(readOnly = true)
    public WechatAccount findByAppId(String appId) {
        WechatAccount wechatAccount = new WechatAccount();
        wechatAccount.setAppId(appId);
        wechatAccount.setState(Constants.Status.VALID);
        List<WechatAccount> wechatAccounts = wechatAccountMapper.selectByFields(wechatAccount);
        if (!wechatAccounts.isEmpty()) {
            return wechatAccounts.get(0);
        }
        return null;
    }

    /**
     * 获取自家的微信账号
     *
     * @return
     */
    @TargetDataSource(name = "center")
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public WechatAccount getWishareWechatAccount() {
        WechatAccount wechatAccount = new WechatAccount();
        wechatAccount.setSportsCenterId(0L);
        wechatAccount.setState(Constants.Status.VALID);
        wechatAccount.setAccountType(Constants.WechatAccountType.PUBLIC_ACCOUNT);
        List<WechatAccount> wechatAccounts = wechatAccountMapper.selectByFields(wechatAccount);
        if (wechatAccounts.isEmpty()) {
            return null;
        } else {
            return wechatAccounts.get(0);
        }
    }

    @TargetDataSource(name = "center")
    @Transactional(propagation = Propagation.REQUIRES_NEW, readOnly = true)
    public WechatAccount selectByCenterId(Long centerId, String accountType) {
        return wechatAccountMapper.selectByCenterId(centerId, accountType);
    }

    /**
     * 获取app微信支付公众号
     *
     * @param venueId
     * @param centerId
     * @return
     */
    @TargetDataSource(name = "center")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public WechatAccount getAppWechatPayAccount(Long venueId, Long centerId) {
        //先通过场馆id获取配置的微信支付账号
        Integer accountId = venueParamConfig.getInt(venueId, Constants.VenueParam.VENUE_APP_PAY_ACCOUNT);
        //根据centerId查询场馆的微信支付账号
        if (accountId == null) {
            accountId = venueParamConfig.getInt(centerId, Constants.VenueParam.APP_WECHAT_PAY_ACCOUNT);
        }
        //如果没查到场馆信息则返回错误
        if (accountId == null) {
            return null;
        }
        //获取微信公众号账号
        return wechatAccountMapper.selectByPrimaryKey(accountId.longValue());
    }

    /**
     * 获取微信扫码二维码所需的url串
     * @param centerId  中心ID
     * @param url   需要请求的url
     * @param paramMap  请求的参数map集
     * @return
     */
    @Transactional(readOnly = true)
    @TargetDataSource(name = "center")
    public String getQrCodeUrlForWechat(Long centerId, String url, Map<String, String> paramMap) {
        WechatAccount wechatAccount = wechatAccountMapper.selectByCenterId(centerId, Constants.WechatAccountType.PUBLIC_ACCOUNT);
        if (wechatAccount == null || wechatAccount.getAuthUrl() == null || wechatAccount.getWechatOriginId() == null || wechatAccount.getAppId() == null) {
            throw  new ServiceException(Errors.NOT_FOUND_WECAHT_ACCOUNT.getMessage());
        }
        StringBuilder encodeUrl = new StringBuilder();
        encodeUrl.append(wechatAccount.getAuthUrl());

        encodeUrl.append(url);
        encodeUrl.append( "?wechatOriginId=");
        encodeUrl.append( wechatAccount.getWechatOriginId());
        if (paramMap != null && !paramMap.isEmpty()) {
            Set entrySet = paramMap.entrySet();
            Iterator it = entrySet.iterator();
            encodeUrl.append("&");
            while(it.hasNext()) {
                Map.Entry  entry = (Map.Entry)it.next();
                encodeUrl.append(entry.getKey());
                encodeUrl.append("=");
                encodeUrl.append(entry.getValue());
                encodeUrl.append("&");
            }
        }
        StringBuilder resultUrl = new StringBuilder();
        resultUrl.append("https://open.weixin.qq.com/connect/oauth2/authorize");
        resultUrl.append("?appId=");
        resultUrl.append(wechatAccount.getAppId());
        resultUrl.append("&redirect_uri=");
        try {
            resultUrl.append(URLEncoder.encode(encodeUrl.toString(), "UTF-8"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new ServiceException("url转码失败！");
        }
        resultUrl.append("&response_type=code&scope=snsapi_base&state=100&connect_redirect=1#wechat_redirect");

        //获取token
        String token = wechatService.getAccessToken(wechatAccount.getAccountId());
        if (Strings.isNullOrEmpty(token)) {
            throw new ServiceException("获取token失败");
        }
        /*//长链接转短连接
        ServiceResult urlResult = WechatApiService.longToShort(token, resultUrl.toString());
        if (urlResult.getError() != 0) {
            throw new ServiceException(urlResult.getMessage());
        }
        return urlResult.getString("shortUrl");*/
        //ServiceResult serviceResult = baiduApiService.longToShort(resultUrl.toString());
        //return serviceResult.getError() == 0 ? MapUtils.getString(serviceResult, "shortUrl") : resultUrl.toString();
        return resultUrl.toString();
    }


    /**
     * 获取微信分享原始连接场链接
     * @param centerId
     * @param url
     * @param paramMap
     * @return
     */
    @Transactional(readOnly = true)
    @TargetDataSource(name = "center")
    public String getOriginQrCodeUrlForWechat(Long centerId, String url, Map<String, String> paramMap) {
        WechatAccount wechatAccount = wechatAccountMapper.selectByCenterId(centerId, Constants.WechatAccountType.PUBLIC_ACCOUNT);
        if (wechatAccount == null || StringUtils.isBlank(wechatAccount.getAuthUrl()) || wechatAccount.getWechatOriginId() == null || wechatAccount.getAppId() == null) {
            throw  new ServiceException(Errors.NOT_FOUND_WECAHT_ACCOUNT.getMessage());
        }
        StringBuilder encodeUrl = new StringBuilder();
        encodeUrl.append(wechatAccount.getAuthUrl());

        encodeUrl.append(url);
        encodeUrl.append( "?wechatOriginId=");
        encodeUrl.append( wechatAccount.getWechatOriginId());
        if (paramMap != null && !paramMap.isEmpty()) {
            Set entrySet = paramMap.entrySet();
            Iterator it = entrySet.iterator();
            encodeUrl.append("&");
            while(it.hasNext()) {
                Map.Entry  entry = (Map.Entry)it.next();
                encodeUrl.append(entry.getKey());
                encodeUrl.append("=");
                encodeUrl.append(entry.getValue());
                encodeUrl.append("&");
            }
        }
        StringBuilder resultUrl = new StringBuilder();
        resultUrl.append("https://open.weixin.qq.com/connect/oauth2/authorize");
        resultUrl.append("?appId=");
        resultUrl.append(wechatAccount.getAppId());
        resultUrl.append("&redirect_uri=");
        try {
            resultUrl.append(URLEncoder.encode(encodeUrl.toString(), "UTF-8"));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new ServiceException("url转码失败！");
        }
        resultUrl.append("&response_type=code&scope=snsapi_base&state=100&connect_redirect=1#wechat_redirect");

        return resultUrl.toString();
    }

    /**
     * 查询小程序支付账户
     *
     * @param wechatMiniAppId
     * @param venueId
     * @param centerId
     * @return
     */
    @TargetDataSource(name = "center")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public WechatAccount queryWechatMiniAppPayAccount(Long wechatMiniAppId, Long venueId, Long centerId) {
        //先通过场馆id获取配置的微信支付账号
        Integer accountId = venueParamConfig.getInt(venueId, Constants.VenueParam.WECHAT_MINI_APP_PAY_ACCOUNT);
        if (accountId != null) {
            return wechatAccountMapper.selectByPrimaryKey(accountId.longValue());
        }
        //再通过中心id获取配置的微信支付账号
        accountId = venueParamConfig.getInt(centerId, Constants.VenueParam.WECHAT_MINI_APP_PAY_ACCOUNT);
        if (accountId != null) {
            return wechatAccountMapper.selectByPrimaryKey(accountId.longValue());
        }
        //根据小程序Id查询支付账户
        return wechatAccountMapper.selectByWechatMiniAppId(wechatMiniAppId);
    }
}
