package com.asiainfo.aisports.service.venueCamera;

import com.asiainfo.aisports.annotation.Datasource;
import com.asiainfo.aisports.annotation.RoutingKey;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.common.HikvisionConstants;
import com.asiainfo.aisports.domain.core.CounterLog;
import com.asiainfo.aisports.domain.core.HikGroupRealtimeData;
import com.asiainfo.aisports.domain.core.HikGroupRealtimeDataHour;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.persistence.core.CounterLogMapper;
import com.asiainfo.aisports.persistence.core.HikGroupRealtimeDataHourMapper;
import com.asiainfo.aisports.persistence.core.HikGroupRealtimeDataMapper;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.tools.HikvisionOpenApiClient;
import com.asiainfo.aisports.tools.StringUtils;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 海康摄像头客流服务
 *
 * @auther: zhouwei
 * @date: 2022/5/10 16:32
 */
@Service
public class HikCameraFlowService {

    @Autowired
    private HikGroupRealtimeDataHourMapper hikGroupRealtimeDataHourMapper;

    @Autowired
    private HikGroupRealtimeDataMapper hikGroupRealtimeDataMapper;

    @Autowired
    private CounterLogMapper counterLogMapper;

    @Autowired
    private HikvisionOpenApiClient hikvisionOpenApiClient;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public void saveHour(@RoutingKey Long centerId, List<HikGroupRealtimeDataHour> realTimeInfos) {
        hikGroupRealtimeDataHourMapper.batchSave(realTimeInfos);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public void saveMinute(@RoutingKey Long centerId, List<HikGroupRealtimeData> realTimeInfos) {
        hikGroupRealtimeDataMapper.batchSave(realTimeInfos);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public List<DataMap> selectHikGroupRealtimeDataHour(@RoutingKey Long centerId, ParamMap param) {
        return hikGroupRealtimeDataHourMapper.selectHikGroupRealtimeDataHour(param);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public List<DataMap> selectHikGroupRealtimeDataMinute(@RoutingKey Long centerId, ParamMap param){
        return hikGroupRealtimeDataMapper.selectHikGroupRealtimeDataMinute(param);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public List<DataMap> selectHikGroupRealtimeDataWeekAndMonth(@RoutingKey Long centerId, ParamMap param){
        return hikGroupRealtimeDataHourMapper.selectHikGroupRealtimeDataWeekAndMonth(param);
    }

    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public List<CounterLog> selectWzDeviceCountData(@RoutingKey Long centerId, ParamMap param){
        return counterLogMapper.selectWzDeviceCountData(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<DataMap> queryCountYearInfo(@RoutingKey Long centerId, Long venueId) {
        return hikGroupRealtimeDataHourMapper.queryCountYearInfo(centerId, venueId);
    }

    @TargetDataSource(name = "{}")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public List<DataMap> queryCountHourInfo(@RoutingKey Long centerId, Long venueId, String queryDate) {
        return hikGroupRealtimeDataHourMapper.queryCountHourInfo(centerId, venueId, queryDate);
    }
    @TargetDataSource(name = "{}")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public DataMap queryCountAllInfo(@RoutingKey Long centerId, Long venueId) {
        return hikGroupRealtimeDataHourMapper.queryCountAllInfo(centerId, venueId);
    }
    @TargetDataSource(name = "{}")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    public DataMap queryCountDayInfo(@RoutingKey Long centerId, Long venueId, String queryDate) {
        return hikGroupRealtimeDataHourMapper.queryCountDayInfo(centerId,venueId, queryDate);
    }


    /**
     * 查询小时客流当天最新的一条数据
     * @param centerId
     * @param groupId
     * @param now
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public HikGroupRealtimeDataHour selectNewestHourByGroupId(@RoutingKey Long centerId, String groupId, Date now){
        return hikGroupRealtimeDataHourMapper.selectNewestByGroupId(groupId,now);
    }

    /**
     * 查询分钟客流当天最新的一条数据
     * @param centerId
     * @param groupId
     * @param now
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    @TargetDataSource(name = "{}")
    public HikGroupRealtimeData selectNewestMinuteByGroupId(@RoutingKey Long centerId, String groupId, Date now){
        return hikGroupRealtimeDataMapper.selectNewestByGroupId(groupId,now);
    }

    /**
     * 查询统计组实时数据
     * @param groupsIdList 统计组Id
     * @return
     */
    public ServiceResult queryGroupRealTimeData(List<String> groupsIdList, String granularity, String startTime, String endTime) {
        JSONObject jsonBody = new JSONObject();
        StringBuilder ids = new StringBuilder();
        for (int i = 0; i < groupsIdList.size(); i++) {
            if (!StringUtils.isEmpty(groupsIdList.get(i))) {
                ids.append(groupsIdList.get(i));
                if (i < groupsIdList.size() - 1) {
                    ids.append(",");
                }
            }
        }
        jsonBody.put("ids",ids.toString());
        jsonBody.put("granularity",granularity);
        jsonBody.put("startTime",startTime);
        jsonBody.put("endTime",endTime);
        String body = jsonBody.toString();
        return hikvisionOpenApiClient.callPostStringApi(HikvisionConstants.PASSENGER_GROUPS, body);
    }

    /**
     * 查询统计组实时数据
     * @param groupsIdList 统计组Id
     * @return
     */
    public ServiceResult queryGroupRealTimeCenterData(Long centerId, List<String> groupsIdList, String granularity, String startTime, String endTime) {
        JSONObject jsonBody = new JSONObject();
        StringBuilder ids = new StringBuilder();
        for (int i = 0; i < groupsIdList.size(); i++) {
            ids.append(groupsIdList.get(i));
            if(i < groupsIdList.size() - 1){
                ids.append(",");
            }
        }
        jsonBody.put("ids",ids.toString());
        jsonBody.put("granularity",granularity);
        jsonBody.put("startTime",startTime);
        jsonBody.put("endTime",endTime);
        String body = jsonBody.toString();
        return hikvisionOpenApiClient.callPostStringCenterApi(centerId, HikvisionConstants.PASSENGER_GROUPS, body);
    }

    /**
     * 封装请求参数、调用平台抓拍计划图片获取
     *
     * @param pageNo    页码
     * @param pageSize  每页数据记录数
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     */
    public ServiceResult getPictureInfos(Integer pageNo, Integer pageSize, String startTime, String endTime) {
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("pageNo", pageNo);
        jsonBody.put("pageSize", pageSize);
        jsonBody.put("startTime", startTime);
        jsonBody.put("endTime", endTime);
        String body = jsonBody.toString();
        ServiceResult serviceResult = hikvisionOpenApiClient.callPostStringApi(HikvisionConstants.VIDEO_PICTURE_INFOS, body);
        return serviceResult;
    }

    /**
     * 视频取流 m3u8格式
     * @param cameraIndexCode:监控点唯一标识
     * @return
     */
    public ServiceResult getVideoPreviewStreamM3U8(String cameraIndexCode){
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("cameraIndexCode", cameraIndexCode);
        jsonBody.put("streamType", 0);
        jsonBody.put("protocol", "hls");
        String body = jsonBody.toString();
        ServiceResult serviceResult = hikvisionOpenApiClient.callPostStringApi(HikvisionConstants.VIDEO_CAMERAS_PREVIEW_URL, body);
        return serviceResult;
    }

    /**
     * 监控点回放取流 m3u8格式
     * @param cameraIndexCode
     * @param beginTime
     * @param endTime
     * @return
     */
    public ServiceResult getVideoPlayBackStreamM3U8(String cameraIndexCode,String beginTime, String endTime){
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("cameraIndexCode", cameraIndexCode);
        jsonBody.put("protocol", "hls");
        jsonBody.put("recordLocation", "1");
        jsonBody.put("transmode", 1);
        jsonBody.put("beginTime", beginTime);
        jsonBody.put("endTime", endTime);
        String body = jsonBody.toString();
        ServiceResult serviceResult = hikvisionOpenApiClient.callPostStringApi(HikvisionConstants.VIDEO_CAMERAS_PLAYBACK_URL, body);
        return serviceResult;
    }

    /**
     * 手动触发设备抓图，返回图片的地址
     * @param cameraIndexCode
     * @return
     */
    public ServiceResult getImgUrlByCameraIndexCode(String cameraIndexCode){
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("cameraIndexCode", cameraIndexCode);
        String body = jsonBody.toString();
        ServiceResult serviceResult = hikvisionOpenApiClient.callPostStringApi(HikvisionConstants.VIDEO_MANUAL_CAPTURE, body);
        return serviceResult;
    }
}