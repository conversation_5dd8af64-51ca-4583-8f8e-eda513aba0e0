package com.asiainfo.aisports.service.job;

import com.asiainfo.aisports.TestApplicationContext;
import net.sf.json.JSONObject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class GroupLessonCancelJobTest extends TestApplicationContext {

    @Autowired
    private GroupLessonCancelJob groupLessonCancelJob;
    @Test
    public void execute() {
        JSONObject param = new JSONObject();
        param.put("lessonId", 486242);
        param.put("pushTime", 1666856963228L);
        param.put("deadLineMillis", 1666857180000L);


        groupLessonCancelJob.execute(10000000L, param);
    }
}
