<template>
    <div class="title-box">
        <h1 style="display: block;">
            <span>券类别管理</span>
            <span
                class="btn-return"
                @click="backToList">
                <i class="iconfont icon-zuojiantou"></i>
            </span>
        </h1>
        <div class="sub-box">
            <div class="coupon-type">
                <div class="btn-line">
                    <button
                        class="btn btn-md btn-default add-btn-gray btn-type"
                        @click="showCoverBox('add','')">
                        <i class="iconfont icon-jiahao"></i>
                        <span>新增</span>
                    </button>
                </div>
                <table class="simple-splitter">
                    <thead>
                        <tr>
                            <th>类型名称</th>
                            <th>显示顺序</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="item in paramList">
                            <td>{{ item.valueName }}</td>
                            <td>{{ item.position }}</td>
                            <td>
                                <span
                                    class="blue-underline-text"
                                    @click="showCoverBox('edit',item)">编辑</span>
                                <span
                                    class="blue-underline-text"
                                    @click="deleteParamByParamValue(item)">删除</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <cover-box-type ref="coverboxtype"></cover-box-type>
    </div>
</template>

<script>
import { queryParamList, deleteParamByParamValue } from './apis'
import mAlert from 'mAlert'
import CoverBoxType from './CoverBoxType.vue'
import evtBus from 'utils/VueEventBus'

export default{
    components: {
        CoverBoxType,
    },
    data() {
        return {
            paramList: [],
        }
    },
    mounted() {
        this.queryParamList()
        evtBus.$off('addType').$on('addType', (from) => {
            if (from === 'self') {
                this.queryParamList()
            }
        })
    },
    methods: {
        showCoverBox(type, item) {
            this.$refs.coverboxtype.showCoverBox(type, item, 'self')
        },
        backToList() {
            window.history.back()
        },
        queryParamList() {
            queryParamList({
                paramCode: 'coupon_class',
            }, (res) => {
                this.paramList = res.paramList
            })
        },
        deleteParamByParamValue(item) {
            this.$confirm('确认删除该类别？').then(() => {
                deleteParamByParamValue({
                    centerId: item.centerId,
                    venueId: item.venueId,
                    paramCode: item.paramCode,
                    paramValue: item.paramValue,
                }, () => {
                    mAlert.showSuccess('删除成功')
                    this.queryParamList()
                })
            })

        },
    },
}
</script>
