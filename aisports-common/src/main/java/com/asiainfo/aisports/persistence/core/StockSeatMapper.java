package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.StockSeat;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface StockSeatMapper {
    @Delete({
        "delete from stock_seat",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into stock_seat (id, stock_id, ",
        "imp_id, stock_name, ",
        "sub_row, row, seat, ",
        "state, person_id, ",
        "create_time, create_staff_id, ",
        "update_time, update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{stockId,jdbcType=BIGINT}, ",
        "#{impId,jdbcType=BIGINT}, #{stockName,jdbcType=VARCHAR}, ",
        "#{subRow,jdbcType=INTEGER}, #{row,jdbcType=INTEGER}, #{seat,jdbcType=INTEGER}, ",
        "#{state,jdbcType=VARCHAR}, #{personId,jdbcType=BIGINT}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(StockSeat record);

    @Select({
        "select",
        "id, stock_id, imp_id, stock_name, sub_row, row, seat, state, person_id, create_time, ",
        "create_staff_id, update_time, update_staff_id",
        "from stock_seat",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
        @Result(column="imp_id", property="impId", jdbcType=JdbcType.BIGINT),
        @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
        @Result(column="sub_row", property="subRow", jdbcType=JdbcType.INTEGER),
        @Result(column="row", property="row", jdbcType=JdbcType.INTEGER),
        @Result(column="seat", property="seat", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="person_id", property="personId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    StockSeat selectByPrimaryKey(Long id);

    @Update({
        "update stock_seat",
        "set stock_id = #{stockId,jdbcType=BIGINT},",
          "imp_id = #{impId,jdbcType=BIGINT},",
          "stock_name = #{stockName,jdbcType=VARCHAR},",
          "sub_row = #{subRow,jdbcType=INTEGER},",
          "row = #{row,jdbcType=INTEGER},",
          "seat = #{seat,jdbcType=INTEGER},",
          "state = #{state,jdbcType=VARCHAR},",
          "person_id = #{personId,jdbcType=BIGINT},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(StockSeat record);

    @Select({
        "<script>",
        "select",
        "id, stock_id, imp_id, stock_name, sub_row, row, seat, state, person_id, create_time, create_staff_id, update_time, update_staff_id",
        "from stock_seat",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"stockId != null\">and stock_id = #{stockId,jdbcType=BIGINT}</if>",
        "<if test=\"impId != null\">and imp_id = #{impId,jdbcType=BIGINT}</if>",
        "<if test=\"stockName != null\">and stock_name = #{stockName,jdbcType=VARCHAR}</if>",
        "<if test=\"subRow != null\">and sub_row = #{subRow,jdbcType=INTEGER}</if>",
        "<if test=\"row != null\">and row = #{row,jdbcType=INTEGER}</if>",
        "<if test=\"seat != null\">and seat = #{seat,jdbcType=INTEGER}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
        "<if test=\"personId != null\">and person_id = #{personId,jdbcType=BIGINT}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
        @Result(column="imp_id", property="impId", jdbcType=JdbcType.BIGINT),
        @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
        @Result(column="sub_row", property="subRow", jdbcType=JdbcType.INTEGER),
        @Result(column="row", property="row", jdbcType=JdbcType.INTEGER),
        @Result(column="seat", property="seat", jdbcType=JdbcType.INTEGER),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="person_id", property="personId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<StockSeat> selectByFields(StockSeat param);

    @Update({
        "<script>",
        "update stock_seat",
        "<set>",
        "<if test=\"stockId != null\">stock_id = #{stockId,jdbcType=BIGINT},</if>",
        "<if test=\"impId != null\">imp_id = #{impId,jdbcType=BIGINT},</if>",
        "<if test=\"stockName != null\">stock_name = #{stockName,jdbcType=VARCHAR},</if>",
        "<if test=\"subRow != null\">sub_row = #{subRow,jdbcType=INTEGER},</if>",
        "<if test=\"row != null\">row = #{row,jdbcType=INTEGER},</if>",
        "<if test=\"seat != null\">seat = #{seat,jdbcType=INTEGER},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
        "<if test=\"personId != null\">person_id = #{personId,jdbcType=BIGINT},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(StockSeat record);


    @Update({
            "update stock_seat set state='0',update_time=now(),update_staff_id=#{staffId} where stock_id = #{stockId} and state = '1'"
    })
    int invalidateByStockId(@Param("stockId") Long stockId, @Param("staffId") Long staffId);


    @Insert({
            "<script>",
            "insert into stock_seat (id, stock_id, ",
            "imp_id, stock_name, ",
            "sub_row, row, seat, ",
            "state, person_id, ",
            "create_time, create_staff_id, ",
            "update_time, update_staff_id)",
            "values",
            "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
            " (#{item.id,jdbcType=BIGINT}, #{item.stockId,jdbcType=BIGINT}, ",
            "#{item.impId,jdbcType=BIGINT}, #{item.stockName,jdbcType=VARCHAR}, ",
            "#{item.subRow,jdbcType=INTEGER}, #{item.row,jdbcType=INTEGER}, #{item.seat,jdbcType=INTEGER}, ",
            "#{item.state,jdbcType=VARCHAR}, #{item.personId,jdbcType=BIGINT}, ",
            "#{item.createTime,jdbcType=TIMESTAMP}, #{item.createStaffId,jdbcType=BIGINT}, ",
            "#{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateStaffId,jdbcType=BIGINT})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(List<StockSeat> seatList);
}
