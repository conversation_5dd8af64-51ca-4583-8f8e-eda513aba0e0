package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.UserOrderLog;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface UserOrderLogMapper {
    @Delete({
        "delete from user_order_log",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into user_order_log (id, net_user_id, ",
        "mobile_num, user_name, ",
        "open_id, channel_id, ",
        "center_id, venue_id, ",
        "service_id, trade_type_code, ",
        "trade_id, occupy_date, ",
        "remark, user_ip, ",
        "user_agent, operate_time, ",
        "create_time)",
        "values (#{id,jdbcType=BIGINT}, #{netUserId,jdbcType=BIGINT}, ",
        "#{mobileNum,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, ",
        "#{openId,jdbcType=VARCHAR}, #{channelId,jdbcType=BIGINT}, ",
        "#{centerId,jdbcType=BIGINT}, #{venueId,jdbcType=BIGINT}, ",
        "#{serviceId,jdbcType=BIGINT}, #{tradeTypeCode,jdbcType=BIGINT}, ",
        "#{tradeId,jdbcType=BIGINT}, #{occupyDate,jdbcType=DATE}, ",
        "#{remark,jdbcType=VARCHAR}, #{userIp,jdbcType=VARCHAR}, ",
        "#{userAgent,jdbcType=VARCHAR}, #{operateTime,jdbcType=TIMESTAMP}, ",
        "#{createTime,jdbcType=TIMESTAMP})"
    })
    int insert(UserOrderLog record);

    @Select({
        "select",
        "id, net_user_id, mobile_num, user_name, open_id, channel_id, center_id, venue_id, ",
        "service_id, trade_type_code, trade_id, occupy_date, remark, user_ip, user_agent, ",
        "operate_time, create_time",
        "from user_order_log",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_name", property="userName", jdbcType=JdbcType.VARCHAR),
        @Result(column="open_id", property="openId", jdbcType=JdbcType.VARCHAR),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_type_code", property="tradeTypeCode", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="occupy_date", property="occupyDate", jdbcType=JdbcType.DATE),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_ip", property="userIp", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_agent", property="userAgent", jdbcType=JdbcType.VARCHAR),
        @Result(column="operate_time", property="operateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP)
    })
    UserOrderLog selectByPrimaryKey(Long id);

    @Update({
        "update user_order_log",
        "set net_user_id = #{netUserId,jdbcType=BIGINT},",
          "mobile_num = #{mobileNum,jdbcType=VARCHAR},",
          "user_name = #{userName,jdbcType=VARCHAR},",
          "open_id = #{openId,jdbcType=VARCHAR},",
          "channel_id = #{channelId,jdbcType=BIGINT},",
          "center_id = #{centerId,jdbcType=BIGINT},",
          "venue_id = #{venueId,jdbcType=BIGINT},",
          "service_id = #{serviceId,jdbcType=BIGINT},",
          "trade_type_code = #{tradeTypeCode,jdbcType=BIGINT},",
          "trade_id = #{tradeId,jdbcType=BIGINT},",
          "occupy_date = #{occupyDate,jdbcType=DATE},",
          "remark = #{remark,jdbcType=VARCHAR},",
          "user_ip = #{userIp,jdbcType=VARCHAR},",
          "user_agent = #{userAgent,jdbcType=VARCHAR},",
          "operate_time = #{operateTime,jdbcType=TIMESTAMP},",
          "create_time = #{createTime,jdbcType=TIMESTAMP}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(UserOrderLog record);

    @Select({
        "<script>",
        "select",
        "id, net_user_id, mobile_num, user_name, open_id, channel_id, center_id, venue_id, service_id, trade_type_code, trade_id, occupy_date, remark, user_ip, user_agent, operate_time, create_time",
        "from user_order_log",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"netUserId != null\">and net_user_id = #{netUserId,jdbcType=BIGINT}</if>",
        "<if test=\"mobileNum != null\">and mobile_num = #{mobileNum,jdbcType=VARCHAR}</if>",
        "<if test=\"userName != null\">and user_name = #{userName,jdbcType=VARCHAR}</if>",
        "<if test=\"openId != null\">and open_id = #{openId,jdbcType=VARCHAR}</if>",
        "<if test=\"channelId != null\">and channel_id = #{channelId,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"serviceId != null\">and service_id = #{serviceId,jdbcType=BIGINT}</if>",
        "<if test=\"tradeTypeCode != null\">and trade_type_code = #{tradeTypeCode,jdbcType=BIGINT}</if>",
        "<if test=\"tradeId != null\">and trade_id = #{tradeId,jdbcType=BIGINT}</if>",
        "<if test=\"occupyDate != null\">and occupy_date = #{occupyDate,jdbcType=DATE}</if>",
        "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
        "<if test=\"userIp != null\">and user_ip = #{userIp,jdbcType=VARCHAR}</if>",
        "<if test=\"userAgent != null\">and user_agent = #{userAgent,jdbcType=VARCHAR}</if>",
        "<if test=\"operateTime != null\">and operate_time = #{operateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_name", property="userName", jdbcType=JdbcType.VARCHAR),
        @Result(column="open_id", property="openId", jdbcType=JdbcType.VARCHAR),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_type_code", property="tradeTypeCode", jdbcType=JdbcType.BIGINT),
        @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
        @Result(column="occupy_date", property="occupyDate", jdbcType=JdbcType.DATE),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_ip", property="userIp", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_agent", property="userAgent", jdbcType=JdbcType.VARCHAR),
        @Result(column="operate_time", property="operateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<UserOrderLog> selectByFields(UserOrderLog param);

    @Update({
        "<script>",
        "update user_order_log",
        "<set>",
        "<if test=\"netUserId != null\">net_user_id = #{netUserId,jdbcType=BIGINT},</if>",
        "<if test=\"mobileNum != null\">mobile_num = #{mobileNum,jdbcType=VARCHAR},</if>",
        "<if test=\"userName != null\">user_name = #{userName,jdbcType=VARCHAR},</if>",
        "<if test=\"openId != null\">open_id = #{openId,jdbcType=VARCHAR},</if>",
        "<if test=\"channelId != null\">channel_id = #{channelId,jdbcType=BIGINT},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "<if test=\"serviceId != null\">service_id = #{serviceId,jdbcType=BIGINT},</if>",
        "<if test=\"tradeTypeCode != null\">trade_type_code = #{tradeTypeCode,jdbcType=BIGINT},</if>",
        "<if test=\"tradeId != null\">trade_id = #{tradeId,jdbcType=BIGINT},</if>",
        "<if test=\"occupyDate != null\">occupy_date = #{occupyDate,jdbcType=DATE},</if>",
        "<if test=\"remark != null\">remark = #{remark,jdbcType=VARCHAR},</if>",
        "<if test=\"userIp != null\">user_ip = #{userIp,jdbcType=VARCHAR},</if>",
        "<if test=\"userAgent != null\">user_agent = #{userAgent,jdbcType=VARCHAR},</if>",
        "<if test=\"operateTime != null\">operate_time = #{operateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(UserOrderLog record);
}
