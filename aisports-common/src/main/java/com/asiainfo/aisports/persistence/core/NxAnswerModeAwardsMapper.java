package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.NxAnswerModeAwards;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface NxAnswerModeAwardsMapper {
    @Delete({
        "delete from nx_answer_mode_awards",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into nx_answer_mode_awards (id, mode_id, ",
        "answer_activity_id, lottery_activity_id, ",
        "single_accuracy, status, ",
        "sort, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{modeId,jdbcType=BIGINT}, ",
        "#{answerActivityId,jdbcType=BIGINT}, #{lotteryActivityId,jdbcType=BIGINT}, ",
        "#{singleAccuracy,jdbcType=DECIMAL}, #{status,jdbcType=CHAR}, ",
        "#{sort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(NxAnswerModeAwards record);

    @Select({
        "select",
        "id, mode_id, answer_activity_id, lottery_activity_id, single_accuracy, status, ",
        "sort, create_time, create_staff_id, update_time, update_staff_id",
        "from nx_answer_mode_awards",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="mode_id", property="modeId", jdbcType=JdbcType.BIGINT),
        @Result(column="answer_activity_id", property="answerActivityId", jdbcType=JdbcType.BIGINT),
        @Result(column="lottery_activity_id", property="lotteryActivityId", jdbcType=JdbcType.BIGINT),
        @Result(column="single_accuracy", property="singleAccuracy", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.CHAR),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    NxAnswerModeAwards selectByPrimaryKey(Long id);

    @Update({
        "update nx_answer_mode_awards",
        "set mode_id = #{modeId,jdbcType=BIGINT},",
          "answer_activity_id = #{answerActivityId,jdbcType=BIGINT},",
          "lottery_activity_id = #{lotteryActivityId,jdbcType=BIGINT},",
          "single_accuracy = #{singleAccuracy,jdbcType=DECIMAL},",
          "status = #{status,jdbcType=CHAR},",
          "sort = #{sort,jdbcType=INTEGER},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(NxAnswerModeAwards record);

    @Select({
        "<script>",
        "select",
        "id, mode_id, answer_activity_id, lottery_activity_id, single_accuracy, status, sort, create_time, create_staff_id, update_time, update_staff_id",
        "from nx_answer_mode_awards",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"modeId != null\">and mode_id = #{modeId,jdbcType=BIGINT}</if>",
        "<if test=\"answerActivityId != null\">and answer_activity_id = #{answerActivityId,jdbcType=BIGINT}</if>",
        "<if test=\"lotteryActivityId != null\">and lottery_activity_id = #{lotteryActivityId,jdbcType=BIGINT}</if>",
        "<if test=\"singleAccuracy != null\">and single_accuracy = #{singleAccuracy,jdbcType=DECIMAL}</if>",
        "<if test=\"status != null\">and status = #{status,jdbcType=CHAR}</if>",
        "<if test=\"sort != null\">and sort = #{sort,jdbcType=INTEGER}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "order by single_accuracy desc ",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="mode_id", property="modeId", jdbcType=JdbcType.BIGINT),
        @Result(column="answer_activity_id", property="answerActivityId", jdbcType=JdbcType.BIGINT),
        @Result(column="lottery_activity_id", property="lotteryActivityId", jdbcType=JdbcType.BIGINT),
        @Result(column="single_accuracy", property="singleAccuracy", jdbcType=JdbcType.DECIMAL),
        @Result(column="status", property="status", jdbcType=JdbcType.CHAR),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<NxAnswerModeAwards> selectByFields(NxAnswerModeAwards param);

    @Update({
        "<script>",
        "update nx_answer_mode_awards",
        "<set>",
        "<if test=\"modeId != null\">mode_id = #{modeId,jdbcType=BIGINT},</if>",
        "<if test=\"answerActivityId != null\">answer_activity_id = #{answerActivityId,jdbcType=BIGINT},</if>",
        "<if test=\"lotteryActivityId != null\">lottery_activity_id = #{lotteryActivityId,jdbcType=BIGINT},</if>",
        "<if test=\"singleAccuracy != null\">single_accuracy = #{singleAccuracy,jdbcType=DECIMAL},</if>",
        "<if test=\"status != null\">status = #{status,jdbcType=CHAR},</if>",
        "<if test=\"sort != null\">sort = #{sort,jdbcType=INTEGER},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(NxAnswerModeAwards record);

    @Select({
            "<script>",
            "select",
            "a.id, a.mode_id, a.answer_activity_id, a.lottery_activity_id, a.single_accuracy, b.name lottery_activity_name",
            "from nx_answer_mode_awards a",
            "left join nx_lottery_activity b on a.lottery_activity_id = b.activity_id",
            "<where>",
            "a.mode_id = #{modeId,jdbcType=BIGINT}",
            "</where>",
            "order by a.single_accuracy desc ",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="mode_id", property="modeId", jdbcType=JdbcType.BIGINT),
            @Result(column="answer_activity_id", property="answerActivityId", jdbcType=JdbcType.BIGINT),
            @Result(column="lottery_activity_id", property="lotteryActivityId", jdbcType=JdbcType.BIGINT),
            @Result(column="single_accuracy", property="singleAccuracy", jdbcType=JdbcType.DECIMAL),
            @Result(column="lottery_activity_name", property="lotteryActivityName", jdbcType=JdbcType.VARCHAR),
    })
    List<DataMap> selectByModeId(Long modeId);

    @Delete({
            "<script>",
            "delete from nx_answer_mode_awards",
            "where answer_activity_id = #{activityId,jdbcType=BIGINT} and mode_id not in (",
            "<foreach collection=\"idList\" item=\"item\" index=\"index\" separator=\",\">",
            "#{item,jdbcType=VARCHAR}",
            "</foreach>",
            ")",
            "</script>"
    })
    int deleteOtherModeAwards(@Param("activityId") Long activityId, @Param("idList") List<String> idList);

    @Select({
            "<script>",
            "select",
            "a.id, a.mode_id, a.answer_activity_id, a.lottery_activity_id, a.single_accuracy, b.name answer_activity_name",
            "from nx_answer_mode_awards a",
            "left join nx_answer_activity b on a.answer_activity_id = b.activity_id",
            "<where>",
            "a.lottery_activity_id = #{activityId,jdbcType=BIGINT}",
            "and (b.is_del = '0' or b.is_del is null)",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="mode_id", property="modeId", jdbcType=JdbcType.BIGINT),
            @Result(column="answer_activity_id", property="answerActivityId", jdbcType=JdbcType.BIGINT),
            @Result(column="lottery_activity_id", property="lotteryActivityId", jdbcType=JdbcType.BIGINT),
            @Result(column="single_accuracy", property="singleAccuracy", jdbcType=JdbcType.DECIMAL),
            @Result(column="answer_activity_name", property="answerActivityName", jdbcType=JdbcType.VARCHAR)
    })
    List<DataMap> queryByLotteryActivity(@Param("activityId") Long activityId);
}
