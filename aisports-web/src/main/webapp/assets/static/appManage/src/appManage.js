import Vue from 'vue'
import $ from 'jquery'
import 'frame'
import Router from 'vue-router'
import '../../common/element-theme/index.css'
import AdList from './components/AdList.vue'
import AdDetail from './components/AdDetail.vue'
import { initData } from './components/api'
import OperationText from 'vue-components/OperationText'
import ListPage from 'vue-components/ListPage'

Vue.use(Router)
Vue.use(OperationText)
Vue.use(ListPage)

const router = new Router({
    routes: [{
        name: 'list',
        path: '/',
        component: AdList,
    }, {
        name: 'detail',
        path: '/detail',
        component: AdDetail,
    }],
})


new Vue({
    el: '#app-manage',
    router,
    beforeMount() {
        this.getData()
    },
    mounted() {
    },
    methods: {
        getData() {
            initData(this.handleInitialData)
        },
        handleInitialData(data) {
            window.__venueList__ = data.venueList
            window.__courseList__ = data.courseList
            window.__contType__ = data.contType
            window.__campList__ = data.campList
            window.__app__ = data.app
        },
    },
})
