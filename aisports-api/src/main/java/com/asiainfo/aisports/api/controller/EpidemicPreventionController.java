package com.asiainfo.aisports.api.controller;

import com.asiainfo.aisports.api.web.WebResult;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.helper.Errors;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.service.EpidemicPreventionService;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.VerifyCodeService;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Set;

@RestController
@RequestMapping("/wechatAPI/epidemic")
public class EpidemicPreventionController {

    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private VerifyCodeService verifyCodeService;
    @Autowired
    private EpidemicPreventionService epidemicPreventionService;

    /**
     * 人脸质量检测接口
     *
     * @param centerId
     * @param photoUrl
     * @return
     */
    @RequestMapping(value = "/recognitionQuality", method = RequestMethod.GET)
    public WebResult recognitionQuality(@RequestParam Long centerId,
                                        @RequestParam String photoUrl) {
        WebResult result = new WebResult();
        result.putAll(epidemicPreventionService.recognitionQuality(photoUrl));
        return result;
    }

    /**
     * 保存或更新会员信息接口
     *
     * @param centerId
     * @param psptId
     * @param mobile
     * @param name
     * @param photoUrl
     * @param verifyCode
     * @return
     */
    @RequestMapping(value = "/addOrUpdateUser", method = RequestMethod.POST)
    public WebResult addOrUpdateUser(@RequestParam Long centerId,
                                     @RequestParam String psptId,
                                     @RequestParam String mobile,
                                     @RequestParam String name,
                                     @RequestParam String photoUrl,
                                     @RequestParam String verifyCode) {
        WebResult result = new WebResult();
        //先查询出可以上传到哪些人脸服务点
        String faceSetNames = venueParamConfig.getParam(centerId, Constants.VenueParam.EPIDEMIC_FACE_SET_NAMES);
        if (Strings.isNullOrEmpty(faceSetNames)) {
            return new WebResult(1, "该中心没有配置防疫登记可以上传的人脸服务组");
        }

        //校验验证码是否正确
        if (!verifyCodeService.checkVerifyCode(centerId, mobile, verifyCode, Constants.VerifyCodeType.WEIXIN)) {
            return new WebResult(Errors.ERROR_VERIFYCODE);
        }
        if (Strings.isNullOrEmpty(mobile) || Strings.isNullOrEmpty(psptId)) {
            return new WebResult(1, "手机号与身份证不能为空");
        }

        String[] groupIds = faceSetNames.split(",");
        Set<String> groups = Sets.newHashSet();
        groups.addAll(Arrays.asList(groupIds));
        if (Strings.isNullOrEmpty(mobile) && Strings.isNullOrEmpty(psptId)) {
            return new WebResult(1, "手机号和身份证号不能都为空");
        }
        String id = "";
        Set<String> existGroupIds = Sets.newHashSet();
        //先查询是否已经存在该员工
        //先根据手机号查询
        ServiceResult queryResult = epidemicPreventionService.queryUser(mobile, null);
        if (queryResult.getError() != 0) {
            return new WebResult(queryResult.getError(), queryResult.getMessage());
        }
        Object userList = queryResult.get("userList");
        if (userList != null) {
            JSONArray userArray = JSONArray.fromObject(userList);
            if (userArray.size() > 0) {
                id = MapUtils.getString(userArray.getJSONObject(0), "id");
                JSONArray groupList = userArray.getJSONObject(0).getJSONArray("groupList");
                if (!groupList.isEmpty()) {
                    for (int i = 0; i < groupList.size(); i++) {
                        JSONObject group = groupList.getJSONObject(i);
                        String groupId = group.getString("id");
                        existGroupIds.add(groupId);
                    }
                }
            }
        }

        if (Strings.isNullOrEmpty(id)) {
            //手机号没查到再根据身份证号查
            queryResult = epidemicPreventionService.queryUser(null, psptId);
            if (queryResult.getError() != 0) {
                return new WebResult(queryResult.getError(), queryResult.getMessage());
            }
            userList = queryResult.get("userList");
            if (userList != null) {
                JSONArray userArray = JSONArray.fromObject(userList);
                if (userArray.size() > 0) {
                    id = MapUtils.getString(userArray.getJSONObject(0), "id");
                    JSONArray groupList = userArray.getJSONObject(0).getJSONArray("groupList");
                    if (!groupList.isEmpty()) {
                        for (int i = 0; i < groupList.size(); i++) {
                            JSONObject group = groupList.getJSONObject(i);
                            String groupId = group.getString("id");
                            existGroupIds.add(groupId);
                        }
                    }
                }
            }
        }
        //如果是更新则把已经存在的分组也加进去
        if (!Strings.isNullOrEmpty(id)) {
            groups.addAll(existGroupIds);
        }
        //组装分组
        StringBuilder sb = new StringBuilder();
        for (String groupId : groups) {
            sb.append(groupId + ",");
        }
        result.putAll(epidemicPreventionService.addOrUpdateUser(id, name, mobile, psptId, photoUrl, sb.toString()));
        return result;
    }

}
