;define(function(require){
    var $=require("jquery");
    require("./ui");
    require("frame");
    require("select");
    var settings=require("aisportsConfig");
    var formControl = require("form-control");
   //基数退款切换效果（中间大块内容）
    function baseSwitch(){
    	$("#base-switch").on("click",function(){
    		$("#month-switch").removeClass("first-select");
            $("#time-switch").removeClass("second-select");
            $("#base-switch").addClass("first-select");
            $("#select-base").show();
            $("#select-month").hide();
            $("#select-time").hide();
    	})
    }

    //月数退款切换效果（中间大块内容）
    function monthSwitch(){
        $("#month-switch").on("click",function(){
            $("#base-switch").removeClass("first-select");
            $("#time-switch").removeClass("second-select");
            $("#month-switch").addClass("first-select");
            $("#select-base").hide();
            $("#select-month").show();
            $("#select-time").hide();   
        })
    }

    //期限后每月固定退款切换效果（中间大块内容）
    function timeSwitch(){
        $("#time-switch").on("click",function(){
            $("#base-switch").removeClass("first-select");
            $("#month-switch").removeClass("first-select");
            $("#time-switch").addClass("second-select");
            $("#select-base").hide();
            $("#select-month").hide();
            $("#select-time").show();
        })
    }

    //基数退款切换效果（右侧内容）
    function rightBaseSwitch(){
        $("#base-card-select").on("click",function(){
            $("#month-card-select").removeClass("you-select");
            $("#month-card-select").children().removeClass("rule-name")
            $("#time-card-select").removeClass("you-select");
            $("#time-card-select").children().removeClass("rule-name")
            $("#base-card-select").addClass("you-select");
            $("#base-card-select").children().addClass("rule-name")
            $("#base-card").show();
            $("#month-card").hide();
            $("#time-card").hide();
        })
    }

    //月卡退款切换效果（右侧内容）
    function rightMonthSwitch(){
        $("#month-card-select").on("click",function(){
            $("#base-card-select").removeClass("you-select");
            $("#base-card-select").children().removeClass("rule-name")
            $("#time-card-select").removeClass("you-select");
            $("#time-card-select").children().removeClass("rule-name")
            $("#month-card-select").addClass("you-select");
            $("#month-card-select").children().addClass("rule-name")
            $("#base-card").hide();
            $("#month-card").show();
            $("#time-card").hide();
        })
    }

   //期限后每月固定退款切换效果（右侧内容）
    function rightTimeSwitch(){
        $("#time-card-select").on("click",function(){
            $("#base-card-select").removeClass("you-select");
            $("#base-card-select").children().removeClass("rule-name")
            $("#month-card-select").removeClass("you-select");
            $("#month-card-select").children().removeClass("rule-name")
            $("#time-card-select").addClass("you-select");
            $("#time-card-select").children().addClass("rule-name")
            $("#base-card").hide();
            $("#month-card").hide();
            $("#time-card").show();
        })
    }

    //加号JS    添加期限后每月固定退款的规则
     function addTimeRule(){
        $("#add-time-rule").on("click",function(){
            var my_template=$($('#my-template').html());
            $(this).closest('tr').before(my_template);
            my_template.find('select').easyDropDown({});
        })
    }

    //删除JS    删除期限后每月固定退款的规则
     function deleteTimeRule(){
        $(document).on("click",".delete-div",function(){
            $(this).closest('tr').remove();
        })
    }




    //后台需要的js开始
    //基数退款的保存按钮
    function baseSave(){
        $("#base-save").on("click",function(){
            //基数
        })
    }

    //月卡退款的保存按钮
    function monthSave(){
        $("#month-save").on("click",function(){
            //基数
        })
    }

    //期限后每月固定退款的保存按钮
    function timeSave(){
        $("#time-save").on("click",function(){
            //基数
        })
    }


   
    $(function(){
        baseSwitch();
        monthSwitch();
        timeSwitch();
        rightBaseSwitch();
        rightMonthSwitch();
        rightTimeSwitch();
        baseSave();
        monthSave();
        timeSave();
        addTimeRule();
        deleteTimeRule();
    });
    
});