package com.asiainfo.aisports.service;

import com.amazonaws.services.dynamodbv2.xspec.S;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.GroupCardConstants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.domain.core.vo.productHolidayVo.ProductHolidayDayVo;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.ProductInfo;
import com.asiainfo.aisports.model.PromInfo;
import com.asiainfo.aisports.param.StaticParamConfig;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.DateJsonConfig;
import com.asiainfo.aisports.tools.TicketUtils;
import com.asiainfo.aisports.tools.TradeConstants;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.sf.ezmorph.object.DateMorpher;
import net.sf.json.JSONArray;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;
import net.sf.json.util.JSONUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * Created by Stomic on 14/12/8.
 */
@org.springframework.stereotype.Service
public class ProductService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductService.class);
    // 产品资源包元素，对应product_composition表element_type为0
    public static final String PRODUCT_RES = "0";
    // 产品物品元素，对应product_composition表element_type为1
    public static final String PRODUCT_GIFT = "1";
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private ProductCompositionMapper productCompositionMapper;
    @Autowired
    private ProductResMapper productResMapper;
    @Autowired
    private ProductGiftMapper productGiftMapper;
    @Autowired
    private SequenceWrapper sequenceWrapper;
    @Autowired
    private TradeProductMapper tradeProductMapper;
    @Autowired
    private ResDepositChangeMapper resDepositChangeMapper;
    @Autowired
    private ProductTypeMapper productTypeMapper;
    @Autowired
    private DateJsonConfig dateJsonConfig;
    @Autowired
    private TradeDepositMapper tradeDepositMapper;
    @Autowired
    private ProdOperatingLimitMapper prodOperatingLimitMapper;
    @Autowired
    private ProductReleaseChlMapper productReleaseChlMapper;
    @Autowired
    private ProductTradeFeeMapper productTradeFeeMapper;
    @Autowired
    private TradeExtraService tradeExtraService;
    @Autowired
    private ProductBackcardFeeMapper productBackcardFeeMapper;
    @Autowired
    private StaticParamConfig staticParamConfig;
    @Autowired
    private ProductTradeRestrictionMapper productTradeRestrictionMapper;
    @Autowired
    private ProductPriceMapper productPriceMapper;
    @Autowired
    private TradeMapper tradeMapper;
    @Autowired
    private TradeEnhanceCardMapper tradeEnhanceCardMapper;
    @Autowired
    private DepositMapper depositMapper;
    @Autowired
    private DepositGiftResMapper depositGiftResMapper;
    @Autowired
    private PromotionConfigureService promotionConfigureService;
    @Autowired
    private BuyGiftsMapper buyGiftsMapper;
    @Autowired
    private UserProductMapper userProductMapper;
    @Autowired
    private GoodsAttrMapper goodsAttrMapper;
    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private PromotionService promotionService;
    @Autowired
    private TradePayLogMapper tradePayLogMapper;
    @Autowired
    private DepositDetailMapper depositDetailMapper;
    @Autowired
    private TradeFeeMapper tradeFeeMapper;
    @Autowired
    private ResDepositChangeDetailMapper resDepositChangeDetailMapper;
    @Autowired
    private SiteServiceService siteServiceService;
    @Autowired
    private ServiceMapper serviceMapper;
    @Autowired
    private VenueMapper venueMapper;
    @Autowired
    private TradeFeeitemMapper tradeFeeitemMapper;
    @Autowired
    private ProductAttrService productAttrService;
    @Autowired
    private DepositService depositService;
    @Autowired
    HolidayConfigService holidayConfigService;

    static {
        String[] dateFormats = new String[]{Constants.DEFAULT_DATE_FORMAT, Constants.DEFAULT_TIME_FORMAT};
        JSONUtils.getMorpherRegistry().registerMorpher(new DateMorpher(dateFormats));
    }

    @Transactional(readOnly = true)
    public Product getProductById(Long productId) {
        return productMapper.selectByPrimaryKey(productId);
    }

    /**
     * 根据产品ID,查询产品的角色关系,产品元素构成等关系
     * table
     * 1.product
     * 2.product_relation
     * 3.relation_role
     * 4.product_composition
     * 5.product_res
     * 6.product_gift
     *
     * @param productId
     * @return
     */
    @Transactional(readOnly = true)
    public Product getValidProductById(Long productId) {
        return productMapper.getProductByProductId(productId);
    }

    /**
     * 根据产品ID查询产品的构成信息,包括资源包和物品
     * <p>
     * 1.product
     * 2.product_composition
     * 3.product_res
     * 4.product_gift
     *
     * @param productId
     * @return
     */
    @Transactional(readOnly = true)
    public List<ProductComposition> getProductCompositionByProductId(Long productId, Date acceptDate) {
        List<ProductComposition> productCompositionList = productCompositionMapper.getProductCompositionsByProductId(productId, acceptDate);
        for (ProductComposition productComposition : productCompositionList) {
            if (productComposition.getElementType().equalsIgnoreCase(PRODUCT_RES)) {
                productComposition.setProductRes(productResMapper.getProductResByElementId(productComposition.getElementId(), acceptDate));
            } else if (productComposition.getElementType().equalsIgnoreCase(PRODUCT_GIFT)) {
                //暂时没用到
                productComposition.setProductGift(productGiftMapper.getProductGiftByElementId(productComposition.getElementId(), acceptDate));
            }
        }
        return productCompositionList;
    }

    /**
     * 新办理产品时记录相关日志
     * <p>
     * table:
     * 1.deposit
     * 2.res_deposit_change
     * 3.user_product
     *
     * @param product
     * @param trade
     * @param businessType
     * @return
     */
    @Transactional
    public Long addProductLogForCreate(Product product, Trade trade, String businessType) {
        Long tradeId = trade.getTradeId();
        Map<String, String> extra = tradeExtraService.getTradeExtra(tradeId);

        //产品自定义开始时间
        String settingStartDate = extra.get(Constants.Extra.SETTING_STARTDATE);

        TradeProduct tradeProduct = new TradeProduct();
        tradeProduct.setTradeId(tradeId);
        tradeProduct.setUserId(trade.getUserId());
        tradeProduct.setProductId(product.getProductId());
        tradeProduct = tradeProductMapper.selectByPrimaryKey(tradeProduct);

        //插入user_product表
        Date startDate;
        if (settingStartDate != null) {
            startDate = tradeProduct.getStartDate();
        } else {
            //如果没有自定义开始时间,期间卡需要取顺延时间
            startDate = this.queryPeriodContinueDate(product, trade.getCustId());
        }
        UserProduct userProduct = this.generatorUserProductInfo(product, trade.getUserId(), trade.getTradeStaffId(), startDate, trade.getAcceptDate());
        userProductMapper.insert(userProduct);

        //更新trade_product表
        tradeProduct.setStartDate(userProduct.getStartDate());
        tradeProduct.setEndDate(userProduct.getEndDate());
        tradeProductMapper.updateByPrimaryKeySelective(tradeProduct);

        //获取销售数量
        int saleNum = tradeProduct.getAmount();

        Map<String, Object> result;
        Long depositId = null;
        Map<String, Object> depositParams = Maps.newHashMap(); //设置账本参数
        if (product.getProdTag() != null && product.getProdTag().equals(Constants.ProdTag.CARD)) {
            for (int i = 0; i < saleNum; i++) {
                depositParams.put("startNum", i);
                depositParams.put("endNum", i + 1);
                result = this.insertDeposit(product, trade, businessType, depositParams, startDate);
                startDate = (Date) result.get("startDate");
                if (depositId == null) {
                    depositId = (Long) result.get("depositId");
                }
            }
        } else {
            // 系统配置prodTag为空，都走这个分支
            depositParams.put("startNum", 0);
            depositParams.put("endNum", saleNum);
            result = this.insertDeposit(product, trade, businessType, depositParams, startDate);
            depositId = (Long) result.get("depositId");
        }
        return depositId;
    }

    /**
     * 插入账本
     *
     * @return
     */
    @Transactional(readOnly = true)
    public Map<String, Object> insertDeposit(Product product, Trade trade, String businessType, Map param, Date date) {
        //初始化用户信息
        Long acctId = MapUtils.getLong(param, "acctId");
        Long custId = MapUtils.getLong(param, "custId");
        Long userId = MapUtils.getLong(param, "userId");
        String ecardNo = MapUtils.getString(param, "ecardNo");
        Long tradeId = trade.getTradeId();
        if (acctId == null) {
            acctId = trade.getAcctId();
        }
        if (custId == null) {
            custId = trade.getCustId();
        }
        if (userId == null) {
            userId = trade.getUserId();
        }
        if (Strings.isNullOrEmpty(ecardNo)) {
            ecardNo = trade.getEcardNo();
        }

        //获取办卡数量
        int startNum = MapUtils.getIntValue(param, "startNum", 0);
        int endNum = MapUtils.getIntValue(param, "endNum", 1);

        Map<String, Object> result = Maps.newHashMap();
        List<ProductComposition> productCompositions = this.getProductCompositionByProductId(product.getProductId(), trade.getAcceptDate());
        for (ProductComposition productComposition : productCompositions) {
            if (!product.getProductMode().equalsIgnoreCase(Constants.ProductMode.GIFTS)) {
                ProductRes productRes = productComposition.getProductRes();
                Date now = new Date();

                // 团体次卡需要修改下数量
                processProductRes(productRes, product, trade);

                Long changeLogId = sequenceWrapper.tradeSequence();

                //添加用户产品资源帐本信息
                Deposit deposit = new Deposit();
                deposit.setDepositId(sequenceWrapper.depositSequence());
                deposit.setProductId(product.getProductId());
                deposit.setAcctId(acctId);
                deposit.setLimitType(productRes.getLimitTag());
                deposit.setStartDate(now);

                //设置资源账本的balance
                switch (Integer.parseInt(productRes.getResType())) {
                    case 0:
                    case 1:
                        deposit.setBalance(productRes.getResCnt() * (endNum - startNum));
                        break;
                    default:
                        break;
                }

                //设置账本的有效期
                switch (Integer.parseInt(productRes.getOffsetUnit())) {
                    case 0:
                        deposit.setStartDate(date);
                        deposit.setEndDate(DateCalUtil.getDateByOffset(DateCalUtil.DateMode.DAY, productRes.getOffsetValue().intValue() * (endNum - startNum), date, true));
                        break;
                    case 1:
                        deposit.setStartDate(date);
                        deposit.setEndDate(DateCalUtil.getDateByOffset(DateCalUtil.DateMode.MONTH, productRes.getOffsetValue().intValue() * (endNum - startNum), date, true));
                        break;
                    case 2:
                        deposit.setStartDate(date);
                        deposit.setEndDate(DateCalUtil.getDateByOffset(DateCalUtil.DateMode.SEASON, productRes.getOffsetValue().intValue() * (endNum - startNum), date, true));
                        break;
                    case 3:
                        deposit.setStartDate(date);
                        deposit.setEndDate(DateCalUtil.getDateByOffset(DateCalUtil.DateMode.YEAR, productRes.getOffsetValue().intValue() * (endNum - startNum), date, true));
                        break;
                    case 4:
                        deposit.setStartDate(DateCalUtil.getDateFromOffset(productRes.getOffsetStartDate()));
                        deposit.setEndDate(DateCalUtil.getDateFromOffset(productRes.getOffsetEndDate()));
                        break;
                    case 5:
                        deposit.setStartDate(new Date());
                        deposit.setEndDate(DateCalUtil.convertDate(productRes.getOffsetEndDate(), "yyyyMMdd"));
                        break;
                    case 6:
                        deposit.setStartDate(DateCalUtil.convertDate(productRes.getOffsetStartDate(), "yyyyMMdd"));
                        deposit.setEndDate(DateCalUtil.convertDate(productRes.getOffsetEndDate(), "yyyyMMdd"));
                        break;
                    default:
                        break;
                }

                deposit.setResType(productRes.getResType());
                deposit.setUpdateTime(now);
                deposit.setActiveTag(Constants.ActiveState.NOT_ACTIVE); //未激活
                deposit.setDepositStatus(Constants.DepositStatus.NORMAL); //状态正常

                //设置客户来源
                if (businessType.equals(Constants.BusinessType.INDIVIDUAL_CARD)
                        || businessType.equals(Constants.BusinessType.INDIVIDUAL_OLD_CARD)
                        || businessType.equals(Constants.BusinessType.BATCH_DOCARD_NEW_SPECARD)
                        || businessType.equals(Constants.BusinessType.BATCH_DOCARD_OLD_SPECARD)
                        || businessType.equals(Constants.BusinessType.SPEC_APPLY_NEW_SPECARD)) {
                    deposit.setSourceType(Constants.DepositSourceType.PERSONAL_CARD);
                } else if (businessType.equals(Constants.BusinessType.ENTERPRISE_CARD)) {
                    deposit.setSourceType(Constants.DepositSourceType.ENTERPRISE_CARD);
                }

                Long enhanceMoney = 0L;
                Long enhanceGrantMoney = 0L;

                TradeEnhanceCard tradeEnhanceCard = tradeEnhanceCardMapper.selectByTradeId(tradeId);

                // 升卡时主账本标识
                boolean enhanceTag = tradeEnhanceCard != null && tradeEnhanceCard.getProductIdB().equals(product.getProductId());

                DepositDetail grantDeposit = null;
                if (Constants.DepositLimitType.MONEY.equals(deposit.getLimitType())) {
                    List<TradePayLog> tradePayLogList = tradePayLogMapper.selectByTradeId(tradeId);
                    if (tradePayLogList.isEmpty() || product.getProductMode().equalsIgnoreCase(Constants.ProductMode.GIFTS_CARD)
                            || (tradeEnhanceCard != null && !enhanceTag)) {
                        // 0元支付以及赠卡都算赠款、还有升卡时非主账本
                        if (!enhanceTag) {
                            // 排除升卡业务主账本差价为0的场景
                            grantDeposit = new DepositDetail();
                            grantDeposit.setTradeId(tradeId);
                            grantDeposit.setDepositId(deposit.getDepositId());
                            grantDeposit.setAcctId(acctId);
                            grantDeposit.setPayModeCode(Constants.PayMode.GRANTS);
                            grantDeposit.setRechargeMoney(Long.valueOf(deposit.getBalance()));
                            grantDeposit.setBalance(grantDeposit.getRechargeMoney());
                            grantDeposit.setRealPay(0L);

//                            if (enhanceTag) {//主账本0元升卡(tradeType=34)时这边应该走不到了，在后面的分支处理
//                                enhanceGrantMoney += grantDeposit.getRechargeMoney();
//                            }
                        } else if (tradePayLogList.isEmpty() && enhanceTag) {
                            // 处理0元升卡主账本,结转金额转专项卡支付允许退款,剩下余额不可退
                            DepositDetail depositDetail = new DepositDetail();
                            depositDetail.setDepositDetailId(sequenceWrapper.depositDetailId());
                            depositDetail.setTradeId(tradeId);
                            depositDetail.setDepositId(deposit.getDepositId());
                            depositDetail.setAcctId(acctId);
                            depositDetail.setPayModeCode(Constants.PayMode.SPECIAL_CARD);
                            // 0元升卡可退原账本结转金额
                            Long cardUpgradeTradeConvertFee = cardUpgradeTradeConvertFee(tradeId);
                            depositDetail.setRechargeMoney(cardUpgradeTradeConvertFee);
                            depositDetail.setBalance(depositDetail.getRechargeMoney());
                            depositDetail.setRealPay(cardUpgradeTradeConvertFee);
                            depositDetailMapper.insert(depositDetail);

                            ResDepositChangeDetail resDepositChangeDetail = new ResDepositChangeDetail();
                            resDepositChangeDetail.setSubTradeId(sequenceWrapper.tradeSequence());
                            resDepositChangeDetail.setTradeId(tradeId);
                            resDepositChangeDetail.setDepositDetailId(depositDetail.getDepositDetailId());
                            resDepositChangeDetail.setChangeLogId(changeLogId);
                            resDepositChangeDetail.setAcctId(acctId);
                            resDepositChangeDetail.setDepositId(deposit.getDepositId());
                            resDepositChangeDetail.setShouldPay(depositDetail.getRechargeMoney());
                            resDepositChangeDetail.setRealPay(depositDetail.getRealPay());
                            resDepositChangeDetail.setCreateTime(now);
                            resDepositChangeDetail.setUpdateTime(now);
                            resDepositChangeDetail.setUpdateStaffId(trade.getTradeStaffId());
                            resDepositChangeDetail.setPayModeCode(depositDetail.getPayModeCode());
                            resDepositChangeDetail.setRefundedMoney(0L);
                            resDepositChangeDetailMapper.insert(resDepositChangeDetail);

                            enhanceMoney += depositDetail.getRechargeMoney();
                        }
                    } else {
                        Long shouldPayMoney = 0L;
                        TradeFee tradeFee = tradeFeeMapper.selectByPrimaryKey(tradeId);
                        for (TradePayLog tradePayLog : tradePayLogList) {
                            shouldPayMoney += tradePayLog.getShouldPay();

                            DepositDetail depositDetail = new DepositDetail();
                            depositDetail.setDepositDetailId(sequenceWrapper.depositDetailId());
                            depositDetail.setTradeId(tradeId);
                            depositDetail.setDepositId(deposit.getDepositId());
                            depositDetail.setAcctId(acctId);
                            depositDetail.setPayModeCode(tradePayLog.getPayModeCode());
                            // 升卡业务tradePayLogList里面只是差价,要重置DepositDetail&ResDepositChangeDetail
                            if (enhanceTag && trade.getTradeTypeCode() == TradeConstants.TradeTypeCode.CARD_UPGRADE_NEW.getLongValue() && shouldPayMoney.intValue() == tradeFee.getRealPay() ) {
                                Long cardUpgradeTradeConvertFee = cardUpgradeTradeConvertFee(tradeId);
                                TradeFeeitem cardUpgradeHandlingChargeFeeItem = tradeFeeitemMapper.selectByTradeId(tradeId);
                                Long fixDiffPayMoney = cardUpgradeTradeConvertFee - Optional.ofNullable(cardUpgradeHandlingChargeFeeItem).map(TradeFeeitem::getApayMoney).orElse(0L);
                                depositDetail.setRechargeMoney(Long.valueOf(tradePayLog.getShouldPay()) + fixDiffPayMoney);
                                depositDetail.setBalance(depositDetail.getRechargeMoney());
                                depositDetail.setRealPay(Long.valueOf(tradePayLog.getRealPay()) + fixDiffPayMoney);
                            } else {
                                depositDetail.setRechargeMoney(Long.valueOf(tradePayLog.getShouldPay()));
                                depositDetail.setBalance(depositDetail.getRechargeMoney());
                                depositDetail.setRealPay(Long.valueOf(tradePayLog.getRealPay()));
                            }
                            depositDetailMapper.insert(depositDetail);

                            ResDepositChangeDetail resDepositChangeDetail = new ResDepositChangeDetail();
                            resDepositChangeDetail.setSubTradeId(sequenceWrapper.tradeSequence());
                            resDepositChangeDetail.setTradeId(tradeId);
                            resDepositChangeDetail.setDepositDetailId(depositDetail.getDepositDetailId());
                            resDepositChangeDetail.setChangeLogId(changeLogId);
                            resDepositChangeDetail.setAcctId(acctId);
                            resDepositChangeDetail.setDepositId(deposit.getDepositId());
                            resDepositChangeDetail.setShouldPay(depositDetail.getRechargeMoney());
                            resDepositChangeDetail.setRealPay(depositDetail.getRealPay());
                            resDepositChangeDetail.setCreateTime(now);
                            resDepositChangeDetail.setUpdateTime(now);
                            resDepositChangeDetail.setUpdateStaffId(trade.getTradeStaffId());
                            resDepositChangeDetail.setPayModeCode(tradePayLog.getPayModeCode());
                            resDepositChangeDetail.setRefundedMoney(0L);
                            resDepositChangeDetailMapper.insert(resDepositChangeDetail);

                            enhanceMoney += depositDetail.getRechargeMoney();
                        }


                        if (tradeFee.getShouldPay() < deposit.getBalance() && !enhanceTag) {
                            // 支付金额和账本金额不一致的部分算赠款
                            grantDeposit = new DepositDetail();
                            grantDeposit.setTradeId(tradeId);
                            grantDeposit.setDepositId(deposit.getDepositId());
                            grantDeposit.setAcctId(acctId);
                            grantDeposit.setPayModeCode(Constants.PayMode.GRANTS);
                            grantDeposit.setRechargeMoney((long) deposit.getBalance() - tradeFee.getShouldPay());
                            grantDeposit.setBalance(grantDeposit.getRechargeMoney());
                            grantDeposit.setRealPay(0L);
                        }

                        if (shouldPayMoney < tradeFee.getShouldPay()) {
                            // 享受订单折扣的情况，升卡不享受订单折扣
                            if (grantDeposit != null) {
                                grantDeposit.setRechargeMoney(grantDeposit.getRechargeMoney() + tradeFee.getShouldPay() - shouldPayMoney);
                                grantDeposit.setBalance(grantDeposit.getBalance() + tradeFee.getShouldPay() - shouldPayMoney);
                            } else {
                                grantDeposit = new DepositDetail();
                                grantDeposit.setTradeId(tradeId);
                                grantDeposit.setDepositId(deposit.getDepositId());
                                grantDeposit.setAcctId(acctId);
                                grantDeposit.setPayModeCode(Constants.PayMode.GRANTS);
                                grantDeposit.setRechargeMoney(tradeFee.getShouldPay() - shouldPayMoney);
                                grantDeposit.setBalance(grantDeposit.getRechargeMoney());
                                grantDeposit.setRealPay(0L);
                            }
                        }

                    }
                }

                //如果是升卡后续操作，需要更新转卡记录表中的新的账本id，同时账本余额和有效期是不同的
                //升卡新增主卡账本才处理
                if (enhanceTag) {
                    int usedMoney = 0;
                    if (trade.getTradeTypeCode().longValue() == TradeConstants.TradeTypeCode.UPGRADE_CARD.getLongValue()) {
                        //老卡的deposit对象
                        Deposit oldDeposit = depositMapper.selectByPrimaryKey(tradeEnhanceCard.getDepositId());

                        String limitType = oldDeposit.getLimitType(); //标志卡的类型
                        Date startDate = oldDeposit.getStartDate(); //老卡的账本开户时间

                        //新卡的结束时间减去新卡的开始时间为偏移量，用偏移量加上老卡的开始时间得到结束时间
                        long startTime = startDate.getTime();
                        long endTime = deposit.getEndDate().getTime() - deposit.getStartDate().getTime() + startTime;

                        //升卡后更新卡的有效时间
                        Date endDate = new Date(endTime);
                        //结束时间需要加上被升级的卡延期的天数
                        if (oldDeposit.getExtendDays() != null) {
                            endDate = DateCalUtil.getDateAfterSomeDay(endDate, oldDeposit.getExtendDays());
                            deposit.setExtendDays(oldDeposit.getExtendDays());
                        }
                        deposit.setStartDate(startDate);
                        deposit.setEndDate(endDate);
                        deposit.setActiveTag(oldDeposit.getActiveTag());
                        deposit.setActiveTime(oldDeposit.getActiveTime());

                        //如果为计次卡或者余额卡的情况
                        if (limitType.equals(Constants.DepositLimitType.TIMES) || limitType.equals(Constants.DepositLimitType.MONEY)) {
                            //获取充值的次数
                            int chargeCnt = resDepositChangeMapper.getChargeCnt(tradeEnhanceCard.getDepositId());

                            //充值次数体现在resCnt中,不体现在balance
                            deposit.setResCnt(deposit.getBalance() + chargeCnt);

                            usedMoney = oldDeposit.getResCnt() - chargeCnt - oldDeposit.getBalance();
                            int myBalance = deposit.getBalance() - usedMoney;
                            if (myBalance < 0) {
                                throw new ServiceException("计次卡升卡不符合规则!");
                            }
                            //升卡后更新余额
                            deposit.setBalance(myBalance);
                        }
                        //更新trade_enhance_card表的升卡后的账本id
                        tradeEnhanceCard.setDepositIdB(deposit.getDepositId());
                        tradeEnhanceCardMapper.updateByPrimaryKeySelective(tradeEnhanceCard);
                    } else if (trade.getTradeTypeCode().longValue() == TradeConstants.TradeTypeCode.CARD_UPGRADE_NEW.getLongValue()) {
                        TradeEnhanceCard enhanceParam = new TradeEnhanceCard();
                        enhanceParam.setTradeId(trade.getTradeId());
                        List<TradeEnhanceCard> tradeEnhanceCardList = tradeEnhanceCardMapper.selectByFields(enhanceParam);

                        //更新trade_enhance_card表的升卡后的账本id
                        tradeEnhanceCardList.forEach(card -> {
                            card.setDepositIdB(deposit.getDepositId());
                            tradeEnhanceCardMapper.updateByPrimaryKeySelective(card);
                        });
                    }

                    if (enhanceGrantMoney > 0) {
                        // 这种情况只有账本余额全部算成赠款，需要减去已使用的金额，和账本余额保持一致
                        grantDeposit.setRechargeMoney(grantDeposit.getRechargeMoney() - usedMoney);
                        grantDeposit.setBalance(grantDeposit.getBalance() - usedMoney);
                    } else {
                        if (Constants.DepositLimitType.MONEY.equals(deposit.getLimitType())) {
                            if (enhanceMoney > deposit.getBalance()) {
                                throw new ServiceException("升卡方向有误，请检查升卡的明细");
                            }

                            //剩余的金额全部转专项卡支付，不允许退款
                            Long leftMoney = deposit.getBalance() - enhanceMoney;
                            if (leftMoney > 0) {
                                DepositDetail depositDetail = new DepositDetail();
                                depositDetail.setDepositDetailId(sequenceWrapper.depositDetailId());
                                depositDetail.setTradeId(tradeId);
                                depositDetail.setDepositId(deposit.getDepositId());
                                depositDetail.setAcctId(acctId);
                                depositDetail.setPayModeCode(Constants.PayMode.SPECIAL_CARD);
                                depositDetail.setRechargeMoney(leftMoney);
                                depositDetail.setBalance(leftMoney);
                                depositDetail.setRealPay(0L);
                                depositDetailMapper.insert(depositDetail);

                                ResDepositChangeDetail resDepositChangeDetail = new ResDepositChangeDetail();
                                resDepositChangeDetail.setSubTradeId(sequenceWrapper.tradeSequence());
                                resDepositChangeDetail.setTradeId(tradeId);
                                resDepositChangeDetail.setDepositDetailId(depositDetail.getDepositDetailId());
                                resDepositChangeDetail.setChangeLogId(changeLogId);
                                resDepositChangeDetail.setAcctId(acctId);
                                resDepositChangeDetail.setDepositId(deposit.getDepositId());
                                resDepositChangeDetail.setShouldPay(leftMoney);
                                resDepositChangeDetail.setRealPay(0L);
                                resDepositChangeDetail.setCreateTime(now);
                                resDepositChangeDetail.setUpdateTime(now);
                                resDepositChangeDetail.setUpdateStaffId(trade.getTradeStaffId());
                                resDepositChangeDetail.setPayModeCode(Constants.PayMode.SPECIAL_CARD);
                                resDepositChangeDetail.setRefundedMoney(0L);
                                resDepositChangeDetailMapper.insert(resDepositChangeDetail);
                            }
                        }
                    }
                }

                //判断是否有赠送资源
                String giftRes = tradeExtraService.getStringExtra(tradeId, Constants.GiftResTradeExt.GIFTRES_KEY);
                if (!StringUtils.isEmpty(giftRes) && Constants.ProductMode.BASIC.equals(product.getProductMode())) {
                    String[] giftRess = giftRes.split(Constants.GiftResTradeExt.GIFTRES_SPLIT);
                    for (int i = 0; i < giftRess.length; i++) {
                        String[] singleGiftRes = giftRess[i].split(Constants.GiftResTradeExt.GIFTRES_IDNUM_SPLIT);

                        DepositGiftRes depositGiftRes = new DepositGiftRes();
                        depositGiftRes.setPromId(Long.valueOf(singleGiftRes[0]));
                        depositGiftRes.setCreateTime(new Date());
                        depositGiftRes.setDepositId(deposit.getDepositId());
                        depositGiftRes.setResType(productRes.getLimitTag());
                        depositGiftRes.setResCnt(Integer.valueOf(singleGiftRes[1]));
                        //期间卡类型
                        if (singleGiftRes.length > 2 && !StringUtils.isEmpty(singleGiftRes[2])) {
                            String resUnit = singleGiftRes[2];
                            depositGiftRes.setResUnit(resUnit);
                            deposit.setEndDate(DateCalUtil.getDateWithOffset(DateCalUtil.getDateMode(resUnit), depositGiftRes.getResCnt(), deposit.getEndDate(), true));
                        } else {
                            //计次卡和余额卡
                            deposit.setBalance(deposit.getBalance() + depositGiftRes.getResCnt());

                            if (Constants.DepositLimitType.MONEY.equals(deposit.getLimitType())) {
                                if (grantDeposit != null) {
                                    grantDeposit.setRechargeMoney(grantDeposit.getRechargeMoney() + depositGiftRes.getResCnt());
                                    grantDeposit.setBalance(grantDeposit.getBalance() + depositGiftRes.getResCnt());
                                } else {
                                    grantDeposit = new DepositDetail();
                                    grantDeposit.setTradeId(tradeId);
                                    grantDeposit.setDepositId(deposit.getDepositId());
                                    grantDeposit.setAcctId(acctId);
                                    grantDeposit.setPayModeCode(Constants.PayMode.GRANTS);
                                    grantDeposit.setRechargeMoney(Long.valueOf(depositGiftRes.getResCnt()));
                                    grantDeposit.setBalance(grantDeposit.getRechargeMoney());
                                    grantDeposit.setRealPay(0L);
                                }
                            }
                        }
                        depositGiftResMapper.insert(depositGiftRes);
                    }
                }

                //期间卡计算余额
                List<Map<String, Object>> validPeriodList = this.getValidPeriodList(deposit.getProductId());
                if (Constants.DepositLimitType.PERIOD.equals(deposit.getLimitType())
                        || (Constants.DepositLimitType.TIMES.equals(deposit.getLimitType()) && venueParamConfig.getBoolean(trade.getCenterId(), Constants.VenueParam.TIMES_CARD_SKIP_UNAVAILABLE_DATE))) {
                    Integer days;
                    if (!Constants.OffsetUnit.FIXED_DATE.equals(productRes.getOffsetUnit())
                            && !Constants.OffsetUnit.FIXED_END_DATE.equals(productRes.getOffsetUnit())
                            && !Constants.OffsetUnit.FIXED_TIME.equals(productRes.getOffsetUnit())) {
                        Date realStart;
                        if (Constants.ActiveState.NOT_ACTIVE.equals(deposit.getActiveTag()) ||
                                deposit.getStartDate().after(now)) {
                            days = DateCalUtil.daysBetween(deposit.getStartDate(), deposit.getEndDate()) + 1;
                            realStart = deposit.getStartDate();
                        } else {
                            days = DateCalUtil.daysBetween(now, deposit.getEndDate()) + 1;
                            realStart = now;
                        }

                        //期间卡根据使用时段重新计算结束日期
                        deposit.setEndDate(DateCalUtil.getDepositValidDate(DateCalUtil.getDateBeforeSomeDay(realStart, 1), days, validPeriodList));
                    } else {
                        days = DateCalUtil.getDepositValidDays(deposit.getStartDate(), deposit.getEndDate(), validPeriodList);
                    }

                    //期间卡更新余额为天数
                    if (Constants.DepositLimitType.PERIOD.equals(deposit.getLimitType())) {
                        deposit.setBalance(days);
                    }
                }

                //处理有效期的时分秒精度
                deposit.setStartDate(DateCalUtil.trim(deposit.getStartDate()));
                deposit.setEndDate(DateCalUtil.endTime(deposit.getEndDate()));
                deposit.setCreateTime(now);
                deposit.setCreateVenueId(trade.getVenueId());
                if (deposit.getResCnt() == null) {
                    deposit.setResCnt(deposit.getBalance());
                }
                depositMapper.insert(deposit);
                result.put("depositId", deposit.getDepositId());

                if (grantDeposit != null) {
                    grantDeposit.setDepositDetailId(sequenceWrapper.depositDetailId());
                    depositDetailMapper.insert(grantDeposit);

                    ResDepositChangeDetail resDepositChangeDetail = new ResDepositChangeDetail();
                    resDepositChangeDetail.setSubTradeId(sequenceWrapper.tradeSequence());
                    resDepositChangeDetail.setTradeId(tradeId);
                    resDepositChangeDetail.setDepositDetailId(grantDeposit.getDepositDetailId());
                    resDepositChangeDetail.setChangeLogId(changeLogId);
                    resDepositChangeDetail.setAcctId(acctId);
                    resDepositChangeDetail.setDepositId(deposit.getDepositId());
                    resDepositChangeDetail.setShouldPay(grantDeposit.getRechargeMoney());
                    resDepositChangeDetail.setRealPay(0L);
                    resDepositChangeDetail.setCreateTime(now);
                    resDepositChangeDetail.setUpdateTime(now);
                    resDepositChangeDetail.setUpdateStaffId(trade.getTradeStaffId());
                    resDepositChangeDetail.setPayModeCode(grantDeposit.getPayModeCode());
                    resDepositChangeDetail.setRefundedMoney(0L);
                    resDepositChangeDetailMapper.insert(resDepositChangeDetail);
                }

                //插trade_deposit表
                TradeDeposit tradeDeposit = new TradeDeposit();
                tradeDeposit.setTradeId(tradeId);
                tradeDeposit.setEcardNo(ecardNo);
                tradeDeposit.setProductId(product.getProductId());
                tradeDeposit.setDepositId(deposit.getDepositId());
                tradeDeposit.setStartDate(deposit.getStartDate());
                tradeDeposit.setEndDate(deposit.getEndDate());
                tradeDeposit.setCustId(custId);
                tradeDeposit.setUserId(userId);
                tradeDeposit.setAcctId(acctId);
                tradeDeposit.setOriginTradeId(tradeId);
                tradeDepositMapper.insert(tradeDeposit);

                ResDepositChange resDepositChange = new ResDepositChange();
                resDepositChange.setTradeId(tradeId);
                resDepositChange.setDepositId(deposit.getDepositId());
                resDepositChange.setResChangeCnt(deposit.getBalance());
                resDepositChange.setResType(deposit.getResType());
                resDepositChange.setUpdateTime(now);
                resDepositChange.setUpdateVenueId(trade.getVenueId());
                resDepositChange.setUpdateStaffId(trade.getTradeStaffId());
                resDepositChange.setChangeLogId(changeLogId);
                resDepositChange.setBalance(deposit.getBalance());
                resDepositChangeMapper.insert(resDepositChange);

                date = DateCalUtil.getDateWithOffset(deposit.getEndDate(), "1s");
                result.put("startDate", date);
                //当用户目前没有生效的账本时 同时办理的产品关了自动激活开关 办完卡就直接激活
                if (Constants.ProductActive.NOT_AUTO_ACTIVE.equals(product.getIsActive()) && !product.getProductMode().equalsIgnoreCase(Constants.ProductMode.GIFTS_CARD)) {
                    boolean activeFlag = false;
                    if (Constants.DepositLimitType.PERIOD.equals(product.getLimitTag())) {
                        List<Deposit> depositList = depositService.queryValidPeriodDepositByService(deposit);
                        if (CollectionUtils.isEmpty(depositList)) {
                            activeFlag = true;
                        }
                    } else {
                        activeFlag = true;
                    }
                    if (activeFlag) {
                        deposit.setActiveTag(Constants.ActiveState.IS_ACTIVE);
                        deposit.setActiveTime(now);
                        depositMapper.updateByPrimaryKeySelective(deposit);
                        // 如果是固定日期的激活的时候可能在开始日期之后了，需要补扣次
                        if (Constants.OffsetUnit.FIXED_DATE.equals(productRes.getOffsetUnit()) ||
                                Constants.OffsetUnit.FIXED_END_DATE.equals(productRes.getOffsetUnit()) ||
                                Constants.OffsetUnit.FIXED_TIME.equals(productRes.getOffsetUnit())) {
                            if (Constants.DepositLimitType.PERIOD.equals(product.getLimitTag())) {
                                Date start = deposit.getStartDate();
                                Date end = deposit.getEndDate().after(now) ? DateCalUtil.getDateBeforeSomeDay(now, 1) : deposit.getEndDate();
                                if (start != null && end != null) {
                                    List<Date> dateList = DateCalUtil.getDepositValidDates(start, end, validPeriodList);
                                    List<ResDepositChange> resDepositChangeList = Lists.newArrayList();
                                    for (Date pastDate : dateList) {
                                        ResDepositChange depositChange = new ResDepositChange();
                                        depositChange.setChangeLogId(sequenceWrapper.tradeSequence());
                                        depositChange.setTradeId(trade.getTradeId());
                                        depositChange.setResChangeCnt(-1);
                                        depositChange.setResChangeDate(pastDate);
                                        depositChange.setResType(Constants.ProductResType.DAYS);
                                        depositChange.setDepositId(deposit.getDepositId());
                                        depositChange.setUpdateTime(now);
                                        depositChange.setUpdateStaffId(trade.getTradeStaffId());
                                        depositChange.setUpdateVenueId(trade.getVenueId());
                                        depositChange.setBalance(deposit.getBalance() - 1);
                                        depositChange.setOldEndDate(deposit.getEndDate());
                                        depositChange.setNewEndDate(deposit.getEndDate());
                                        resDepositChangeList.add(depositChange);
                                        deposit.setBalance(deposit.getBalance() - 1);
                                    }
                                    if (!resDepositChangeList.isEmpty()) {
                                        resDepositChangeMapper.insertBatch(resDepositChangeList);
                                        depositMapper.updateByDepositId(deposit.getDepositId(), -resDepositChangeList.size());
                                    }
                                }
                            }

                        }



                    }
                }
            }
        }
        return result;
    }

    private void processProductRes(ProductRes productRes, Product product, Trade trade) {
        String businessType = trade.getBusinessType();

        if (!Constants.BusinessType.GROUP_NEW_SPECIAL_CARD.equals(businessType) && !Constants.BusinessType.GROUP_OLD_SPECIAL_CARD.equals(businessType)) {
            return;
        }
        if (Constants.DepositLimitType.TIMES.equals(product.getLimitTag())) {
            Integer num = tradeExtraService.getIntExtra(trade.getTradeId(), GroupCardConstants.TradeExtra.GROUP_TIME_CARD_NUM);
            if (num == null) {
                throw new ServiceException("未能获取团体卡套餐数量");
            }
            productRes.setResCnt(num);

        }

        if (Constants.DepositLimitType.MONEY.equals(product.getLimitTag())) {
            Integer num = tradeExtraService.getIntExtra(trade.getTradeId(), GroupCardConstants.TradeExtra.GROUP_TIME_CARD_NUM);
            if (num == null) {
                throw new ServiceException("未能获取团体卡充值金额");
            }
            productRes.setResCnt(num);

        }
    }

    /**
     * 优惠券配置获取适用的产品列表
     *
     * @param venueId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Product> queryProductListByVenueId(Long venueId) {
        Product product = new Product();
        product.setVenueId(venueId);
        product.setStatus(Constants.Status.VALID);
        return productMapper.selectByFields(product);
    }

    /**
     * 获取简单的产品对象,只包括产品类型树及产品简单信息，包括基本产品和赠卡产品
     *
     * @param venueId
     * @param companyTag
     * @return
     */
    @Transactional(readOnly = true)
    public JSONArray getSimpleProduct(Long venueId, String companyTag) {
        JSONArray jsonArray = new JSONArray();

        //获取底层的产品类型
        ProductType productType = new ProductType();
        productType.setVenueId(venueId);
        productType.setParentTypeCode(0L);
        productType.setProductTypeLevel("1");
        List<ProductType> topLvlProductTypes = productTypeMapper.selectProductTypes(productType);

        //获取上层的产品类型
        for (ProductType productTypeTmp : topLvlProductTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("topProductTypeName", productTypeTmp.getProductTypeName());
            jsonObject.put("productTypeId", productTypeTmp.getProductTypeId());
            if (productTypeTmp.getServiceId() != null) {
                Service service = serviceMapper.selectByPrimaryKey(productTypeTmp.getServiceId());
                if (service != null) {
                    jsonObject.put("picUrl", service.getPicUrl());
                }
            }

            JSONArray secondJsonArray = new JSONArray();

            productType = new ProductType();
            productType.setParentTypeCode(productTypeTmp.getProductTypeId());
            List<ProductType> secondLvlProductTypes = productTypeMapper.selectByFields(productType);

            //根据得到的产品类型，得到相应的产品
            for (ProductType secondProductTypeTmp : secondLvlProductTypes) {
                JSONObject secondJsonObject = new JSONObject();
                secondJsonObject.put("secondProductTypeName", secondProductTypeTmp.getProductTypeName());

                Long productTypeId = secondProductTypeTmp.getProductTypeId();

                Map<String, Object> param = Maps.newHashMap();
                param.put("productType", productTypeId);
                param.put("venueId", venueId);
                param.put("companyTag", companyTag); //设置团体标识
                List<ProductInfo> tempProductList = productMapper.getProductListForBatchDelay(param);
                secondJsonObject.put("productList", tempProductList);

                secondJsonArray.add(secondJsonObject);
            }
            jsonObject.put("secondProduct", secondJsonArray);

            jsonArray.add(jsonObject);
        }

        return jsonArray;
    }

    /**
     * 根据场馆号获取产品
     *
     * @param venueId
     * @param siteId
     * @return
     */
    @Transactional(readOnly = true)
    public JSONArray getProductByVenueIdAndCompanyTag(Long venueId, String companyTag, Long siteId) {
        JSONArray jsonArray = new JSONArray();
        // 获取办卡业务在该服务点包含的运动项目
        Set<Long> serviceIds = new HashSet<>(siteServiceService.findServicesByTradeType(siteId, TradeConstants.TradeTypeCode.OPEN_CARD.toString()));

        //获取上层的产品类型(服务项)
        ProductType productType = new ProductType();
        productType.setVenueId(venueId);
        productType.setParentTypeCode(0L);
        productType.setProductTypeLevel("1");
        List<ProductType> topLvlProductTypes = productTypeMapper.selectProductTypes(productType);

        for (ProductType productTypeTmp : topLvlProductTypes) {
            if (!serviceIds.contains(productTypeTmp.getServiceId())) { // 如果该服务点没有这个运动项目则跳过
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("topProductTypeName", productTypeTmp.getProductTypeName());
            jsonObject.put("serviceId", productTypeTmp.getServiceId());

            //获取第二层结构(产品类型)
            productType = new ProductType();
            productType.setParentTypeCode(productTypeTmp.getProductTypeId());
            List<ProductType> secondLvlProductTypes = productTypeMapper.selectByFields(productType);

            JSONArray secondJsonArray = new JSONArray();
            for (ProductType secondProductTypeTmp : secondLvlProductTypes) {
                JSONObject secondJsonObject = new JSONObject();
                secondJsonObject.put("secondProductTypeName", secondProductTypeTmp.getProductTypeName());
                Long productTypeId = secondProductTypeTmp.getProductTypeId();

                //获取产品列表
                Map<String, Object> param = Maps.newHashMap();
                param.put("productType", productTypeId);
                param.put("venueId", venueId);
                param.put("siteId", siteId);
                param.put("companyTag", companyTag); //设置团体标识
                param.put("productMode", Constants.ProductMode.BASIC); // 只查询基本产品
                List<ProductInfo> tempProductList = productMapper.getValidProductsByProductTypeAndVenueId(param);

                JSONArray productList = new JSONArray();
                for (ProductInfo product : tempProductList) {
                    Long productId = product.getProductId();

                    //获取产品资源属性
                    String offsetUnit = null;
                    Long offsetValue = null;
                    String offsetStartDate = null;
                    String offsetEndDate = null;
                    List<ProductComposition> compositions = productCompositionMapper.getProductCompositionsByProductId(productId, new Date());
                    for (ProductComposition productComposition : compositions) {
                        if (productComposition.getElementType().equalsIgnoreCase(PRODUCT_RES)) {
                            productComposition.setProductRes(productResMapper.getProductResByElementId(productComposition.getElementId(), new Date()));
                            if (productComposition.getProductRes() != null) {
                                offsetUnit = productComposition.getProductRes().getOffsetUnit();
                                offsetValue = productComposition.getProductRes().getOffsetValue();
                                offsetStartDate = productComposition.getProductRes().getOffsetStartDate();
                                offsetEndDate = productComposition.getProductRes().getOffsetEndDate();
                            }
                        } else if (productComposition.getElementType().equalsIgnoreCase(PRODUCT_GIFT)) {
                            productComposition.setProductGift(productGiftMapper.getProductGiftByElementId(productComposition.getElementId(), new Date()));
                        }
                    }

                    JSONObject tempProduct = JSONObject.fromObject(product, dateJsonConfig.getDatejsonConfig());
                    //把不需要的两个值除掉,否则JSONObject无法通过http返回接口
                    tempProduct.remove("mainProductStartDate");
                    tempProduct.remove("mainProductEndDate");

                    if (!Strings.isNullOrEmpty(offsetUnit)) {
                        tempProduct.put("offsetUnit", offsetUnit);
                    }

                    if (Constants.OffsetUnit.FIXED_DATE.equals(offsetUnit) || Constants.OffsetUnit.FIXED_TIME.equals(offsetUnit)) {
                        tempProduct.put("offsetStartDate", offsetStartDate);
                        tempProduct.put("offsetEndDate", offsetEndDate);
                    } else if (Constants.OffsetUnit.FIXED_END_DATE.equals(offsetUnit)) {
                        tempProduct.put("offsetEndDate", offsetEndDate);
                    } else if (offsetValue != null) {
                        tempProduct.put("offsetValue", String.valueOf(offsetValue));
                    }

                    productList.add(tempProduct);
                }
                secondJsonObject.put("productList", productList);
                secondJsonArray.add(secondJsonObject);
            }

            jsonObject.put("secondProduct", secondJsonArray);
            jsonArray.add(jsonObject);
        }

        return jsonArray;
    }

    /**
     * 获取微信售卖的有效的产品
     *
     * @param shopId
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> getWechatValidProducts(Long shopId, String limitTag,String channels) {
        List<DataMap> productInfo = productMapper.getWechatValidProducts(shopId, Constants.GoodsType.SPECIAL_CARD, limitTag);
        //获取一下值
        this.dealProductInfo(productInfo,channels);
        return productInfo;
    }

    /**
     * 循环处理产品信息
     *
     * @param productInfo
     */
    private void dealProductInfo(List<DataMap> productInfo, String channels) {
        for (DataMap product : productInfo) {
            Long goodsId = (Long) product.get("goodsId");
            Long skuId = (Long) product.get("skuId");
            product.put("goodsAttrList", goodsAttrMapper.getGoodsAttr(goodsId, skuId));
            //获取优惠
            Long productId = MapUtils.getLong(product, "productId");
            product.put("specialCardPromotionTag", !promotionService.getValidGiftsByProduct(productId, null, channels).isEmpty() ? "1" : "0");
            List<PromInfo> limitedTimeDiscounts = promotionService.getLimitedTimeDiscountByProduct(productId, null, channels);
            product.put("limitedDiscountPriceTag", !limitedTimeDiscounts.isEmpty() ? "1" : "0");
            if (!limitedTimeDiscounts.isEmpty()) {
                Long discountMoney = limitedTimeDiscounts.stream().mapToLong(PromInfo::getDiscountMoney).min().getAsLong();
                product.put("discountMoney", discountMoney);
            }
        }
    }

    /**
     * 生成插入用户产品表的信息
     *
     * @param product
     * @param userId
     * @param staffId
     * @return
     */
    @Transactional
    public UserProduct generatorUserProductInfo(Product product, Long userId, Long staffId, Date startDate, Date acceptDate) {
        UserProduct userProduct = new UserProduct();
        userProduct.setProductId(product.getProductId());
        userProduct.setStartDate(startDate);
        userProduct.setOpenTime(new Date());
        userProduct.setUserId(userId);
        userProduct.setOpenStaff(staffId);
        userProduct.setEndDate(getProductEndDate(product, startDate, acceptDate));

        return userProduct;
    }

    @Transactional
    public Date getProductEndDate(Product product, Date startDate, Date acceptDate) {
        Date endDate = null;

        List<ProductComposition> productCompositions = this.getProductCompositionByProductId(product.getProductId(), acceptDate);
        if (productCompositions != null && !productCompositions.isEmpty()) {
            for (ProductComposition productComposition : productCompositions) {
                ProductRes productRes = productComposition.getProductRes();
                if (!product.getProductMode().equalsIgnoreCase(Constants.ProductMode.GIFTS)) {
                    switch (Integer.parseInt(productRes.getOffsetUnit())) {
                        case 0:
                            endDate = DateCalUtil.getDateByOffset(DateCalUtil.DateMode.DAY, productRes.getOffsetValue().intValue(), startDate, true);
                            break;
                        case 1:
                            endDate = DateCalUtil.getDateByOffset(DateCalUtil.DateMode.MONTH, productRes.getOffsetValue().intValue(), startDate, true);
                            break;
                        case 2:
                            endDate = DateCalUtil.getDateByOffset(DateCalUtil.DateMode.SEASON, productRes.getOffsetValue().intValue(), startDate, true);
                            break;
                        case 3:
                            endDate = DateCalUtil.getDateByOffset(DateCalUtil.DateMode.YEAR, productRes.getOffsetValue().intValue(), startDate, true);
                            break;
                        case 4:
                            endDate = DateCalUtil.endTime(DateCalUtil.getDateFromOffset(productRes.getOffsetEndDate()));
                            break;
                        case 5:
                            endDate = DateCalUtil.endTime(DateCalUtil.convertDate(productRes.getOffsetEndDate(), "yyyyMMdd"));
                            break;
                        case 6:
                            endDate = DateCalUtil.endTime(DateCalUtil.convertDate(productRes.getOffsetEndDate(), "yyyyMMdd"));
                            break;
                        default:
                            break;
                    }
                } else {
                    endDate = DateCalUtil.convertDate(Constants.DEFAULT_END_TIME, Constants.DEFAULT_TIME_FORMAT);
                }
            }
        }

        return endDate;
    }

    /**
     * 计算期间卡的顺延时间
     *
     * @param product
     * @param custId
     * @return
     */
    public Date queryPeriodContinueDate(Product product, Long custId) {
        Date date = new Date();
        if (Constants.DepositLimitType.PERIOD.equals(product.getLimitTag())) {
            Date dateTmp;
            //判断是否需要加上serviceId判断，默认用venue,配了用service(徐增加的)
            if (Constants.NewDepositTimeDelayRule.SERVICE_DELAY_RULE.equals(venueParamConfig.getParam(product.getVenueId(), Constants.VenueParam.NEW_DEPOSIT_TIME_DELAY_RULE))) {
                dateTmp = this.getServiceDelayRuleDate(product, custId);
            } else {
                dateTmp = tradeMapper.selectPeriodMaxDate(custId, product.getVenueId());
            }
            if (dateTmp != null && date.before(dateTmp)) {
                date = DateCalUtil.getDateAfterSomeDay(dateTmp, 1);
            }
        }
        return date;
    }

    /**
     * 获取按照项目顺延的日期
     *
     * @param product
     * @param custId
     * @return
     */
    public Date getServiceDelayRuleDate(Product product, Long custId) {
        //获取产品使用限制
        ProdOperatingLimit limit = new ProdOperatingLimit();
        limit.setProductId(product.getProductId());
        limit.setVenueId(product.getVenueId());
        List<ProdOperatingLimit> prodOperatingLimits = prodOperatingLimitMapper.selectProductServices(limit);

        List<Map<String, Object>> periodDateList = tradeMapper.selectPeriodDate(custId, product.getVenueId());
        Optional<Map<String, Object>> filterDate = periodDateList.stream().filter(n -> {
            String serviceIds = MapUtils.getString(n, "serviceIds");
            return prodOperatingLimits.stream().allMatch(prodOperatingLimit -> serviceIds.contains(prodOperatingLimit.getServiceId().toString()));
        }).findFirst();
        return filterDate.map(stringObjectMap -> DateCalUtil.convertDate(MapUtils.getString(stringObjectMap, "endDate"), Constants.DEFAULT_TIME_FORMAT)).orElse(null);
    }

    /**
     * 计算转卡后,账本的开始时间(该客户本来有此产品,此外又从另一个账户中转过来一个此产品)
     *
     * @param product
     * @param acctId
     * @return
     */
    public Date calculateTransferCardStartDate(Product product, Long acctId) {
        Date date = null;
        if (product.getLimitTag() != null && product.getLimitTag().equalsIgnoreCase(Constants.DepositLimitType.PERIOD)) {
            date = productMapper.getEndDateByAcctIdAndProductId(acctId, product.getVenueId());
        }
        return date;
    }

    @Transactional(readOnly = true)
    public Product selectByPrimaryKey(Long productId) {
        return productMapper.selectByPrimaryKey(productId);
    }

    /**
     * 产品价格优惠
     *
     * @param params
     * @param product
     * @param staff
     */
    @Transactional
    public void saveProductPrice(JSONObject params, Product product, LoginStaff staff) {
        // 判断是否有卡价格的优惠方案
        JSONObject discounts = params.getJSONObject("discounts");
        if (discounts == null || discounts.isEmpty()) {
            return;
        }
        String type = discounts.getString("type");
        JSONArray dlist = discounts.getJSONArray("dlist");
        List<ProductPrice> productPriceList = new ArrayList<ProductPrice>();
        for (Object drow : dlist) {
            JSONObject discount = (JSONObject) drow;

            ProductPrice productPrice = new ProductPrice();
            productPrice.setProductId(product.getProductId());
            // 类型 1-多买优惠 2-第n件优惠
            productPrice.setType(type);
            productPrice.setAmount(discount.getInt("count"));
            String valueType = discount.containsKey("valueType") && !JSONNull.getInstance().equals(discount.get("valueType")) ? discount.getString("valueType") : null;
            if (StringUtils.isEmpty(valueType)) {
                valueType = type.equals(Constants.ProductPrice.LADDER_DISCOUNT_TYPE) ? Constants.ProductPrice.CASH_VALUE_TYPE : Constants.ProductPrice.DISCOUNT_VALUE_TYPE;
            }
            // 值类型 1-单价 2-折扣
            productPrice.setValueType(valueType);
            productPrice.setValue(discount.getInt("discount"));
            productPrice.setStartDate(product.getStartDate());
            productPrice.setEndDate(product.getEndDate());
            productPrice.setCenterId(staff.getCenterId());
            productPriceList.add(productPrice);
        }

        if (!productPriceList.isEmpty()) {
            productPriceMapper.batchInsert(productPriceList);
        }
    }

    /**
     * 添加产品的规则
     *
     * @param params
     * @param staff
     * @return
     */
    @Transactional
    public void addProductRules(JSONObject params, Product product, LoginStaff staff) {
        JSONArray ruleArray = params.getJSONArray("cardOps");
        addProductTradeFee(product.getProductId(), product.getVenueId(), ruleArray, staff);
    }


    /**
     * 处理 产品卡操作
     *
     * @param productId
     * @param jsonArray
     * @param staff
     */
    @Transactional
    public void addProductTradeFee(Long productId, Long venueId, JSONArray jsonArray, LoginStaff staff) {
        Date now = new Date();
        Date endDate = null;
        try {
            endDate = new SimpleDateFormat(Constants.DEFAULT_TIME_FORMAT).parse(Constants.DEFAULT_END_TIME);
        } catch (ParseException e) {
            LOGGER.error("", e);
        }

        // 获取有效的 产品规则
        ProductTradeFee paramTradeFee = new ProductTradeFee();
        paramTradeFee.setProductId(productId);
        List<ProductTradeFee> productTradeFees = productTradeFeeMapper.selectValidByFields(paramTradeFee);

        //获取有效的 产品退款规则 这个只有一条数据
        ProductBackcardFee oldProductBackcardFee = productBackcardFeeMapper.selectValidInfoByProductId(productId);
        ProductBackcardFee newProBackFee = null;

        //处理退卡的逻辑
        for (Object tempRule : jsonArray) {
            JSONObject ruleJson = (JSONObject) tempRule;
            if (ruleJson.containsKey("rtId") && !StringUtils.isEmpty(ruleJson.getString("rtId"))) {
                Long ruleId = ruleJson.getLong("rtId");
                // 如果没有退专项卡规则，则直接插入
                newProBackFee = new ProductBackcardFee();
                newProBackFee.setEndDate(endDate);
                newProBackFee.setStartDate(now);
                newProBackFee.setVenueId(venueId);
                newProBackFee.setRuleId(ruleId);
                newProBackFee.setProductId(productId);
            }
        }

        if (newProBackFee != null) {
            if (oldProductBackcardFee == null) {
                productBackcardFeeMapper.insert(newProBackFee);
            } else if (!oldProductBackcardFee.getRuleId().equals(newProBackFee.getRuleId())) {
                oldProductBackcardFee.setEndDate(now);
                productBackcardFeeMapper.updateByPrimaryKeySelective(oldProductBackcardFee);
                productBackcardFeeMapper.insert(newProBackFee);
            }
        } else {
            if (oldProductBackcardFee != null) {
                oldProductBackcardFee.setEndDate(now);
                productBackcardFeeMapper.updateByPrimaryKeySelective(oldProductBackcardFee);
            }
        }

        for (Object tempRule : jsonArray) {
            JSONObject ruleJson = (JSONObject) tempRule;
            Long tradeType = ruleJson.getLong("itemId");
            Long ruleId = ruleJson.getLong("ruleId");
            //先定义一个 新的数据，用来与老数据比较
            ProductTradeFee newTradeFee = new ProductTradeFee();
            newTradeFee.setProductId(productId);
            newTradeFee.setEndDate(endDate);
            newTradeFee.setTradeType(tradeType);
            newTradeFee.setStartDate(now);
            newTradeFee.setRuleId(ruleId);
            newTradeFee.setVenueId(venueId);

            if (!productTradeFees.isEmpty()) {
                //如果不是空的，那么要遍历老数据
                boolean shouldInsert = true;
                for (int i = 0; i < productTradeFees.size(); i++) {

                    ProductTradeFee tradeFee = productTradeFees.get(i);
                    if (tradeFee.getTradeType().equals(newTradeFee.getTradeType()) && tradeFee.getRuleId().equals(newTradeFee.getRuleId())) {
                        //如果是同样的数据，说明这条没有改变 所以不做任何处理，把老数据list中的这条remove,老数据不需要再次遍历了。
                        productTradeFees.remove(tradeFee);
                        shouldInsert = false;
                        break;
                    } else if (tradeFee.getTradeType().equals(newTradeFee.getTradeType())) {
                        //如果业务类型是一样的  但是规则不一样，那就是说明 换了规则，把老数据逻辑删除，新增一条数据
                        // 然后停止循环
                        tradeFee.setEndDate(now);
                        productTradeFeeMapper.updateByPrimaryKeySelective(tradeFee);
                        break;
                    }
                }
                if (shouldInsert) {
                    productTradeFeeMapper.insert(newTradeFee);
                }
            } else {
                //如果老数据是空的 ，那么直接就插入就行了
                productTradeFeeMapper.insert(newTradeFee);
            }
        }
        if (!productTradeFees.isEmpty()) {
            for (ProductTradeFee tradeFee : productTradeFees) {
                tradeFee.setEndDate(now);
                productTradeFeeMapper.updateByPrimaryKeySelective(tradeFee);
            }
        }
    }


    /**
     * 根据产品ID和产品类型查询产品组成
     *
     * @param productId
     * @param productMode
     * @return
     */
    @Transactional(readOnly = true)
    public DataMap getProductResInfo(Long productId, String productMode) {
        if (productMode.equalsIgnoreCase(Constants.ProductMode.GIFTS)) {
            return productMapper.getProductGoodsInfo(productId);
        } else {
            return productResMapper.getProductResInfo(productId);
        }
    }

    @Transactional(readOnly = true)
    public List<DataMap> getProductVenueRelationsByProductId(Long productId) {
        return productReleaseChlMapper.getProductVenueRelationsByProductId(productId);
    }

    /**
     * 查询专项-余额卡界面对应的所有的业务类型
     * 不直接连数据库，从 memcache 读取
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<TradeType> getProductTradeType() {
        Set<Entry> productTradeTypes = staticParamConfig.getParams(Constants.StaticParam.PRODUCT_TRADE_TYPE);
        List<TradeType> tradeTypes = new ArrayList<TradeType>();
        for (Iterator<Entry> it = productTradeTypes.iterator(); it.hasNext(); ) {
            Entry productTradeType = it.next();
            TradeType tradeType = new TradeType();
            tradeType.setTradeTypeCode(Long.parseLong(productTradeType.getKey().toString()));
            tradeType.setTradeTypeName(productTradeType.getValue().toString());
            tradeTypes.add(tradeType);
        }
        return tradeTypes;
    }

    /**
     * 查询产品对应的余额卡业务类型
     *
     * @param productId
     * @return
     */
    @Transactional
    public List<DataMap> getProductTradeRestrictions(Long productId, Long venueId) {
        return productTradeRestrictionMapper.getProductTradeTypes(productId, venueId);
    }

    @Transactional(readOnly = true)
    public List<ProductPrice> getProductPrices(Long productId) {
        ProductPrice productPrice = new ProductPrice();
        productPrice.setProductId(productId);
        return productPriceMapper.selectByFields(productPrice);
    }

    /**
     * 解析优惠活动的json字符串
     *
     * @param proms 活动json字符串
     * @param flag  是否获取所有的优惠
     * @return
     */
    @Transactional(readOnly = true)
    public List<PromInfo> parsePromJson(String proms, boolean flag) {
        if (Strings.isNullOrEmpty(proms)) {
            return null;
        }

        Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        List<PromInfo> promList = gson.fromJson(proms, new TypeToken<List<PromInfo>>() {
        }.getType());

        //获取赠品对应的产品
        for (PromInfo promInfo : promList) {
            if (flag) {
                //获取所有的优惠
                Long promId = promInfo.getPromId();
                promInfo.setBuyGifts(promotionConfigureService.queryGiftRes(promId));
                promInfo.setCoupons(buyGiftsMapper.selectCouponByPromId(promId));
                promInfo.setGifts(buyGiftsMapper.selectProductByPromId(promId));
            }

            //赠送产品
            if (promInfo.getGifts() != null) {
                List<Product> productList = promInfo.getGifts();
                List<Product> newList = Lists.newArrayList();
                for (Product prod : productList) {
                    Product validProduct = getValidProductById(prod.getProductId());
                    if (validProduct != null) {
                        newList.add(validProduct);
                    } else {
                        throw new ServiceException("该营销活动的赠送产品已过期!");
                    }
                }
                promInfo.setGifts(newList);
            }
        }
        return promList;
    }

    /**
     * 根据tradeId查询产品ID信息,用于查询业务办理产品的详细信息
     *
     * @param tradeId
     * @return
     */
    public List<TradeProduct> getTradeProductListByTradeId(Long tradeId) {
        TradeProduct param = new TradeProduct();
        param.setTradeId(tradeId);
        return tradeProductMapper.selectByFields(param);
    }

    /**
     * 根据serviceId和venueId,centerId查询产品
     *
     * @param serviceId
     * @param venueId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Product> getProductListByServiceId(Long serviceId, Long venueId) {
        return productMapper.selectByServiceId(serviceId, venueId);
    }

    /**
     * 获取账本有效使用时段
     *
     * @param productId
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getValidPeriodList(Long productId) {
        List<Map<String, Object>> validPeriodList = Lists.newArrayList();

        List<Map<String, Object>> totalValidPeriodList = prodOperatingLimitMapper.selectValidPeriods(productId);
        if (!totalValidPeriodList.isEmpty()) {
            //所有场馆、服务项的使用时间是一致的,取出1个来计算
            Long venueId = (Long) totalValidPeriodList.get(0).get("venueId");
            Long serviceId = (Long) totalValidPeriodList.get(0).get("serviceId");
            for (Map period : totalValidPeriodList) {
                if (serviceId.equals(period.get("serviceId")) && venueId.equals(period.get("venueId"))) {
                    validPeriodList.add(period);
                } else {
                    break;
                }
            }
        }

        return validPeriodList;
    }

    /**
     * 获取有效的产品列表
     *
     * @param params
     * @return
     */
    @Transactional(readOnly = true)
    public List<Product> selectValidProductListByParams(Product params) {
        return productMapper.selectValidByFields(params);
    }

    /**
     * 查询某场馆对外销售产品
     *
     * @param centerId
     * @param venueId
     * @param channelId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map> queryOpenProduct(Long centerId, Long venueId, Long productId, Long channelId) {
        return productMapper.selectOpenProduct(centerId, venueId, productId, channelId);
    }

    /**
     * 获取指定销量前几位的产品
     *
     * @param shopId
     * @param campId
     * @param goodsType
     * @param limit
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> getRecommendCards(Long shopId, Long campId, Long goodsType, Integer limit) {
        return productMapper.selectRecommendCards(shopId, campId, goodsType, limit);
    }

    /**
     * 获取营销活动相关的专项卡列表
     *
     * @param campId
     * @return
     */
    @Transactional(readOnly = true)
    public ServiceResult getCampCards(Long campId) {
        ServiceResult result = new ServiceResult();
        List<DataMap> validProducts = productMapper.selectRecommendCards(null, campId, Constants.GoodsType.SPECIAL_CARD, null);
        result.put("validProducts", validProducts);
        if (!validProducts.isEmpty()) {
            List<Long> venueIds = validProducts.stream().map(a -> a.getLong("venueId")).collect(Collectors.toList());
            result.put("venueList", venueMapper.selectVenuesByIds(venueIds));
            List<Long> serviceIds = validProducts.stream().map(a -> a.getLong("serviceId")).collect(Collectors.toList());
            result.put("serviceList", serviceMapper.selectServicesByServiceIds(serviceIds));
        }
        //获取一下值
        this.dealProductInfo(validProducts, null);
        return result;
    }

    /**
     * 获取营销活动相关的专项卡列表
     *
     * @param campId
     * @return
     */
    @Transactional(readOnly = true)
    public ServiceResult getCampIndexCards(Long campId, Integer limit) {
        ServiceResult result = new ServiceResult();
        List<DataMap> validProducts = productMapper.selectRecommendCards(null, campId, Constants.GoodsType.SPECIAL_CARD, limit);
        return result.set("validProducts", validProducts);
    }


    /**
     * 校验产品是否还在售卖中为pos
     * @param productId
     * @return
     */
    public Long checkProductSellDate(Long productId) {
        return productMapper.checkProductDateValid(productId);
    }

    /**
     * 根据custId查询产品的续卡价格（用户必须买过这个产品且产品的续卡价格大于0）
     *
     * @param custId    用户id
     * @param productId 产品id
     * @return
     */
    public Integer queryContinuePriceByCustId(Long custId, Long productId) {
        if (custId == null || productId == null) {
            return null;
        }
        return productMapper.queryContinuePriceByCustId(custId, productId);
    }

    /**
     * 升卡业务余额卡账本数据处理
     *
     * @param tradePayLogList
     * @param cardUpgradeTrade
     * @param shouldPayMoney
     * @param enhanceMoney
     */
    private void generateCardUpgradeMoneyDepositDetailInfo(List<TradePayLog> tradePayLogList, Trade cardUpgradeTrade, Long shouldPayMoney, Long enhanceMoney) {
        Long cardUpgradeTradeConvertFee = cardUpgradeTradeConvertFee(cardUpgradeTrade.getTradeId());
        TradeFeeitem tradeFeeitem = tradeFeeitemMapper.selectByTradeId(cardUpgradeTrade.getTradeId());
    }

    /**
     * 升卡业务原账本折算转移金额
     */
    private Long cardUpgradeTradeConvertFee(Long cardUpgradeTradeId) {
        Long totalConvertFee = null;
            TradeEnhanceCard param = new TradeEnhanceCard();
            param.setTradeId(cardUpgradeTradeId);
            List<TradeEnhanceCard> tradeEnhanceCards = tradeEnhanceCardMapper.selectByFields(param);
            if (!tradeEnhanceCards.isEmpty()) {
                totalConvertFee = tradeEnhanceCards.stream().map(TradeEnhanceCard::getConvertFee).reduce(0L, Long::sum);
            }
        return totalConvertFee;
    }

    public Map<String, List<ProductInfo>> getProductListForDelaySelect(Date startDate, Long venueId) {
        Map<String, Object> param = new HashMap<>();
        param.put("startDate", startDate);
        param.put("venueId", venueId);

        List<ProductInfo> productInfoList = productMapper.getProductListForBatchDelayNew(param);

        return productInfoList.stream().collect(Collectors.groupingBy(ProductInfo::getLimitTag));

    }

    /**
     *  通用-获取产品不可用日期
     *  1. 专项卡根据当前卡的有效期范围、可使用日期、可使用月份计算出该卡的有效期内不可用日期并展示。
     * 1）有效期内没有不可用日期时，不展示“不可用”信息。
     * 2）后台配置的不可用周数、日期分开显示，如左侧图使用段落符号分隔：
     * ①不可用周数为单值时，显示为“周X”。
     * ②不可用周数为多值且为连续日期时，显示为“周X至周Y”。
     * ③不可用周数为多值且为非连续日期时，显示为“周X、周Y”。
     * ④不可用日期为单值时，显示为“月.日”（如1.1）。
     * ⑤不可用日期为多值且为连续日期时，显示为“月.日-月.日”，如“1.1-1.31”。
     * ⑥不可用日期为多值且为非连续日期时，显示为“月.日、月.日”，如“1.1、4.4”。
     * ⑦不可用日期为节假日时，显示为“节假日名称(月.日-月.日)”，节假日日期按照查看时间当年的日期显示，如“劳动节(5.1-5.5)、国庆节(10.1-10.7)”。
     * ⑧不可用具体日期和节假日重复时，仅显示节假日范围即可。
     * 2）当不可用日期为节假日且有效期开始或结束日期在某节假日日期范围内时，不可用日期完整显示该节假日日期范围。
     */
    public String productDisabledPeriod(Long productId, Long venueId){

        if (productId == null || venueId == null) return null;

        StringBuilder disabledPeriod = new StringBuilder();

        Product product = Optional.ofNullable(productMapper.selectByPrimaryKey(productId)).orElse(new Product());
        //处理星期
        if (!Constants.AuditTag.CANCEL.equals(product.getLimitTag())){
            handleWeek(productId, disabledPeriod);
        }
        //处理日期
        handleDateStr(Optional.ofNullable(prodOperatingLimitMapper.selectProductsDateByProductIdAndVenueId(productId, venueId)).orElse(Lists.newArrayList()), disabledPeriod);

        return disabledPeriod.toString();
    }

    private void handleWeek(Long productId, StringBuilder disabledPeriod) {
        //判断该卡是否限制了周几使用
        String useWeekdaysStr = productAttrService.getStringExtra(productId, Constants.ProductAttr.USE_WEEKDAYS);

        StringBuilder weekDayChinese = new StringBuilder();
        if (!Strings.isNullOrEmpty(useWeekdaysStr)) {

            // 标记数组中每个数字是否出现
            boolean[] found = new boolean[9]; // 大小为9，因为索引从0开始，但范围到8

            // 解析给定字符串，并标记出现的数字
            for (String numStr : useWeekdaysStr.split(",")) {
                int num = Integer.parseInt(numStr);
                if (num >= 1 && num <= 8) { // 确保数字在范围内
                    found[num] = true;
                }
            }

            // 查找并列出缺失的数字
            for (int i = 1; i <= 8; i++) {
                if (!found[i]) {
                    weekDayChinese.append(TicketUtils.weekDayName(i)).append("，");
                }
            }

            String string = weekDayChinese.toString();
            if (!StringUtils.isEmpty(weekDayChinese)){

                // 查找最后一个逗号的索引
                int lastIndex = string.lastIndexOf('，');

                // 如果找到了逗号，则进行替换
                if (lastIndex != -1) {
                    string = string.substring(0, lastIndex)+"\n";
                }
            }

            //处理节假日时间
            if (!StringUtils.isEmpty(weekDayChinese) && weekDayChinese.toString().contains("节假日")){
                string = string.replace("节假日","");
                disabledPeriod.append(string);
                handleHoliday(productId, disabledPeriod);
            }else {
                disabledPeriod .append(string);
            }
        }
    }

    private void handleHoliday(Long productId, StringBuilder disabledPeriod) {
        Long centerId = Optional.ofNullable(productMapper.selectCenterIdByKey(productId)).orElse(null);

        if (centerId != null){
            List<ProductHolidayDayVo> holidayDays = Optional.ofNullable(holidayConfigService.selectProductHolidayDaysByKey(centerId, LocalDate.now().withDayOfYear(1), LocalDate.now().plusYears(1).withDayOfYear(1))).orElse(Lists.newArrayList());

            StringBuilder stringBuilder = new StringBuilder();
            printDates(holidayDays, stringBuilder);

            String string = stringBuilder.toString();

            // 查找最后一个逗号的索引
            int lastIndex = string.lastIndexOf('，');

            // 如果找到了逗号，则进行替换
            if (lastIndex != -1) {
                string = string.substring(0, lastIndex)+"\n";
                disabledPeriod .append(string);
            }
        }

    }

    private void printDates(List<ProductHolidayDayVo> list, StringBuilder builder) {
        if (list.isEmpty()) {
            return;
        }

        ProductHolidayDayVo prev = null;
        ProductHolidayDayVo start = null;
        boolean inSequence = false;

        for (int i = 0; i < list.size(); i++) {
            ProductHolidayDayVo current = list.get(i);

            if (prev == null || prev.getName().equals(current.getName()) && prev.getDate().plusDays(1).equals(current.getDate())) {
                // 如果当前项与前一项名称相同且日期连续
                if (!inSequence) {
                    // 开始一个新的连续序列
                    start = current;
                    inSequence = true;
                }
            } else {
                // 如果不连续或者名称不同
                if (inSequence) {
                    // 打印之前的连续序列
                    if (start.getDate().equals(prev.getDate())) {
                        // 只有一个日期时，不需要使用~
                        builder.append(start.getName() + "(" + start.getDate() + ")，");
                    } else {
                        builder.append(start.getName() + "(" + start.getDate() + "~" + prev.getDate() + ")，");
                    }
                    inSequence = false; // 重置连续序列状态
                }

                // 重置start（如果当前项是新的连续序列的开始）
                if (i + 1 < list.size() && current.getName().equals(list.get(i + 1).getName()) &&
                        current.getDate().plusDays(1).equals(list.get(i + 1).getDate())) {
                    start = current;
                    inSequence = true;
                }

                // 如果当前项与前一项名称不同或者不是连续序列的一部分，单独打印
                if (!current.getName().equals(prev == null ? null : prev.getName())
                        && list.stream().filter(var->var.getName().equals(current.getName())).count() < 2) {
                    builder.append(current.getName() + "(" + current.getDate() + ")，");
                }
            }

            prev = current; // 更新前一个元素
        }

        // 检查并打印最后一个连续序列（如果有的话，且不是单独打印的）
        if (inSequence) {
            builder.append(start.getName() + "(" + start.getDate() + "~" + prev.getDate() + ")，");
        }
    }

    private void handleDateStr(List<Map> dateList, StringBuilder disabledPeriod) {

        // 获取当前年份
        int currentYear = Year.now().getValue();

        // 修正日期并添加年份
        String[][] correctedRanges = new String[dateList.size()][];
        for (int i = 0; i < dateList.size(); i++) {
            Map map = dateList.get(i);
            if (!map.containsKey("startDate") || !map.containsKey("endDate")) continue;

            String start = currentYear + "-" + MapUtils.getString(map, "startDate");
            String end = currentYear + "-" + MapUtils.getString(map, "endDate");
            correctedRanges[i] = new String[]{start, end};
        }

        String dateStr = Optional.ofNullable(findMissingDates(Arrays.stream(correctedRanges).collect(Collectors.toList())).stream().collect(Collectors.joining(","))).orElse(null);
        if (!StringUtils.isEmpty(dateStr)) disabledPeriod.append(dateStr);
    }

    private static List<String> findMissingDates(List<String[]> ranges) {

        List<String> missingDates = new ArrayList<>();

        // 假设ranges已经按起始日期排序
        LocalDate currentDate = ranges.isEmpty() ? LocalDate.now().withDayOfYear(1) : LocalDate.parse(ranges.get(0)[0], DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        for (String[] range : ranges) {
            LocalDate startDate = LocalDate.parse(range[0], DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate endDate = LocalDate.parse(range[1], DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            // 跳过小于当前日期的范围
            if (startDate.isAfter(currentDate)) {
                // 填充当前日期到范围开始日期之前的日期
                while (!currentDate.isAfter(startDate) && !currentDate.isEqual(startDate)) {
                    missingDates.add(currentDate.toString());
                    currentDate = currentDate.plusDays(1);
                }
            }

            // 更新当前日期为范围的结束日期的下一天
            currentDate = endDate.plusDays(1);
        }

        return Optional.ofNullable(mergeContinuousDateRanges(missingDates)).orElse(null);
    }

    private static List<String> mergeContinuousDateRanges(List<String> dates) {
        List<String> mergedRanges = new ArrayList<>();
        if (dates == null || dates.size() == 0) {
            return mergedRanges;
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate currentStartDate = LocalDate.parse(dates.get(0), formatter);
        String currentRangeStart = dates.get(0);

        for (int i = 1; i < dates.size(); i++) {
            LocalDate nextDate = LocalDate.parse(dates.get(i), formatter);

            if (nextDate.isAfter(currentStartDate.plusDays(1))) {
                mergedRanges.add(currentRangeStart.substring(5,10) + (currentRangeStart.equals(dates.get(i-1)) ? "" : "~" + dates.get(i-1).substring(5,10)));
                currentRangeStart = dates.get(i);
            }

            currentStartDate = nextDate;
        }

        mergedRanges.add(currentRangeStart.substring(5,10) + (mergedRanges.isEmpty() || !currentRangeStart.equals(dates.get(dates.size() - 1)) ? "~" + dates.get(dates.size() - 1).substring(5,10) : ""));

        return Optional.ofNullable(mergedRanges).orElse(null);
    }
}
