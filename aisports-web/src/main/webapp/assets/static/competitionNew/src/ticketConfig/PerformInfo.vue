<template>
    <div class="title-box">
        <h1 style="display: block">
            <span>{{ performId ? '编辑' : '新建' }}场次</span>
            <a
                class="btn-return"
                title="后退"
                @click.prevent="goprev">
                <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
            </a>
        </h1>
        <div class="sub-box">
            <side-tab-set ref="sidetabsetBox">
                <side-tab-item title="基本信息">
                    <dl class="f-line">
                        <dt class="f-label">场次名称</dt>
                        <dd class="f-field">
                            <input
                                v-model="info.name"
                                class="normal"
                                required
                                placeholder="请输入场次名称">
                        </dd>
                    </dl>
                    <dl class="f-line">
                        <dt class="f-label">是否限购</dt>
                        <dd class="f-field">
                            <label class="pure-switch">
                                <input
                                    v-model="hasBuyLimit"
                                    :true-value="'1'"
                                    :false-value="'0'"
                                    type="checkbox">
                                <span class="bc"></span>
                                <span class="pin"></span>
                            </label>
                            <span>单场限购</span>
                            <input
                                :disabled="hasBuyLimit==='0'"
                                v-model="info.buyLimit"
                                class="tiny">
                            <span class="unit">张</span>
                        </dd>
                    </dl>
                    <dl class="f-line">
                        <dt class="f-label">票区图</dt>
                        <dd class="f-field">
                            <img-uploader v-model="info.remark"></img-uploader>
                        </dd>
                    </dl>
                    <dl class="f-line">
                        <dt class="f-label">比赛地点</dt>
                        <dd class="f-field">
                            <g-select
                                :options="locationList"
                                v-model="info.locationId"></g-select>
                            <button
                                class="btn btn-md btn-default add-btn-gray"
                                @click="addNewAddr">
                                <i class="iconfont icon-jiahao"></i><span>新增</span>
                            </button>
                        </dd>
                    </dl>
                    <dl class="f-line">
                        <dt class="f-label">比赛时间</dt>
                        <dd class="f-field">
                            <el-date-picker
                                v-model="performDate"
                                type="date"
                                placeholder="选择日期"></el-date-picker>
                            <el-time-picker
                                v-model="performTime"
                                is-range
                                format="HH:mm"
                                placeholder="选择时间"></el-time-picker>
                        </dd>
                    </dl>
                    <dl class="f-line">
                        <dt class="f-label">销售日期</dt>
                        <dd class="f-field">
                            <el-date-picker
                                v-model="salesDateRange"
                                type="datetimerange"
                                placeholder="选择日期时间"></el-date-picker>
                        </dd>
                    </dl>
                    <dl class="f-line">
                        <dt class="f-label">赛事详情</dt>
                        <dd class="f-field">
                            <script
                                id="description"
                                type="text/plain"></script>
                        </dd>
                    </dl>
                    <dl class="f-line submit-line">
                        <dd class="f-field">
                            <button
                                type="button"
                                class="btn btn-lg btn-default"
                                @click="cancel">取消</button>
                            <button
                                type="button"
                                class="btn btn-lg btn-primary"
                                @click="confirm">提交</button>
                        </dd>
                    </dl>
                    <perform-location
                        ref="location"
                        @update="getPerformLocationList"></perform-location>
                </side-tab-item>
                <side-tab-item
                    ref="repertoryInfo"
                    title="库存信息">
                    <div class="ticket-list-wrap">
                        <div class="selcet-section repertory-select-section">
                            <el-select v-model="repertoryStockId" placeholder="全部票区" filterable  @change="filterStock" clearable>
                                <el-option
                                    v-for="stock in stockList"
                                    :key="stock.id"
                                    :label="stock.name"
                                    :value="stock.id">
                                </el-option>
                            </el-select>
                            <button
                                class="btn btn-md btn-default add-btn-gray"
                                @click="addRepertory">
                                <i class="iconfont icon-jiahao"></i>
                                <span>新增</span>
                            </button>
                        </div>
                        <table class="simple-splitter">
                            <tr>
                                <th>序号</th>
                                <th>票区</th>
                                <th>总座位数</th>
                                <th>已占座位</th>
                                <th>剩余座位</th>
                                <th>具体位置</th>
                                <th>操作</th>
                            </tr>
                            <tr v-for="(stock,stockIndex) in filterStocklist"  >
                                <td>{{ stockIndex+1 }}</td>
                                <td>
                                    {{ stock.name }}
                                </td>
                                <td>{{ stock.totalAmount }}</td>
                                <td>{{ stock.totalAmount - stock.remainAmount}}</td>
                                <td>{{ stock.remainAmount }}</td>
                                <td>{{ stock.location }}</td>
                                <td>
                                    <i
                                        class="iconfont icon-bianji"
                                        @click="editStock(stock)"></i>
                                    <i
                                        class="iconfont icon-shanchu"
                                        @click="deleteStock(stock.id)"></i>
                                </td>
                            </tr>
                        </table>

                        <repertoryItemDialog
                            ref="repertoryBox"
                            :perform-id="performId"
                            @update="getStockList"></repertoryItemDialog>
                    </div>
                </side-tab-item>
                <side-tab-item
                    ref="channelInfo"
                    title="渠道合作">
                    <div class="ticket-list-wrap">
                        <div class="add-section">
                            <button
                                class="btn btn-md btn-default add-btn-gray"
                                @click="addChannel">
                                <i class="iconfont icon-jiahao"></i>
                                <span>新增</span>
                            </button>
                        </div>
                        <table class="simple-splitter">
                            <tr>
                                <th>序号</th>
                                <th>合作商户名称</th>
                                <th>联系人</th>
                                <th>联系人手机号</th>
                                <th>企业地点</th>
                                <th>权益名称</th>
                                <th>权益内容</th>
                                <th>权益时效</th>
                                <th>操作</th>
                            </tr>
                            <tr v-for="(right,rightindex) in rightsList" >
                                <td>{{ rightindex + 1 }}</td>
                                <td>{{ right.pmName || ''}}</td>
                                <td>{{ right.contactPerson || ''}}</td>
                                <td>{{ right.contactPhone|| '' }}</td>
                                <td>{{ right.contactAddr || ''}}</td>
                                <td>{{ right.name }}</td>
                                <td>{{ right.content }}</td>
                                <td v-if="right.validType == 2">{{ right.startDate+'-'+ right.endDate }}</td>
                                <td v-if="right.validType == 1">永久有效</td>
                                <td>
                                    <i
                                        class="iconfont icon-bianji"
                                        @click="editRight(right)"></i>
                                    <i
                                        class="iconfont icon-shanchu"
                                        @click="deleteRight(right.id)"></i>
                                </td>
                            </tr>
                        </table>
                        <div class="accumulate-footer" v-if="channelInfoTickets.total > 10">
                          <pagination :current-page="channelInfoTickets.pageNum" :pageSizes='[10]' :page-size="channelInfoTickets.pageSize"   :total="channelInfoTickets.total"
                            @changepage="handleChangePage" @changepagesize="handleChangePageSize">
                          </pagination>
                        </div>
                        <channelDialog
                            ref="channelBox"
                            :perform-id="performId"
                            @update="getRightsPageList"></channelDialog>
                    </div>
                </side-tab-item>
                <side-tab-item
                    ref="ticketsPrices"
                    :disabled="!performId"
                    title="票价格设置">
                    <div class="ticket-list-wrap">
                        <div class="selcet-section">
                            <input v-model="performTicketName" class="normal" required placeholder="请输入票名称" />

                            <el-select v-model="ticketKind" placeholder="票类型" clearable >
                                <el-option
                                    v-for="(type, key) in ticketTypesList"
                                    :key="type.value"
                                    :label="type.label"
                                    :value="type.value">
                                </el-option>
                            </el-select>
                            <el-select v-model="stockId" placeholder="票区名称"  clearable>
                                <el-option
                                    v-for="stock in stockList"
                                    :key="stock.id"
                                    :label="stock.name"
                                    :value="stock.id">
                                </el-option>
                            </el-select>
                            <el-button icon="el-icon-search"  @click="getPerformTicketPageList">查询</el-button>
                        </div>
                        <div class="add-section">
                        
                            <button
                                class="btn btn-md btn-default add-btn-gray"
                                @click="addTicket">
                                <i class="iconfont icon-jiahao"></i>
                                <span>新增</span>
                            </button>
                        </div>

                        <table class="simple-splitter">
                            <tr>
                                <th>票名称</th>
                                <th>票类型</th>
                                <th>票价（元）</th>
                                <th>票区名称</th>
                                <th>总票数（张）</th>
                                <th>剩余票数（张）</th>
                                <th>核销区域</th>
                                <th>权益</th>
                                <th>操作</th>
                            </tr>
                            <tr v-for="ticket in performTickets" >
                                <td>{{ ticket.performTicketName }}</td>
                                <td>{{ ticket.ticketTypeName }}</td>
                                <td>
                                    {{ m2front(ticket.price) }}
                                </td>
                                <td>{{ ticket.stockName }}</td>
                                <td>{{ ticket.totalAmount }}</td>
                                <td>{{ ticket.remainAmount }}</td>
                                <td>{{ ticket.siteName }}</td>
                                <td>{{ ticket.rightsName }}</td>
                                <td>
                                    <i
                                        class="iconfont icon-bianji"
                                        @click="editTicket(ticket)"></i>
                                    <i
                                        class="iconfont icon-shanchu"
                                        @click="deleteTicket(ticket.performTicketId)"></i>
                                </td>
                            </tr>
                        </table>
                        <div class="page_footer">
                              <el-pagination layout="prev, pager, next" background :total="ticketPageInfo.total" @current-change="handleRightsPageChange"> </el-pagination>
                        </div>
                        
                        <ticket-item-box
                            ref="ticketBox"
                            :perform-id="performId"
                            @update="getTickets"></ticket-item-box>
                    </div>
                </side-tab-item>
                <!-- 团体票配置 -->
                <side-tab-item title="团体票配置" ref="teamTicket" name="teamTicket">
                    <div class="ticket-list-wrap">
                        <div class="selcet-section">
                            <input v-model="teamTicketName" class="normal" required placeholder="请输入票名称" />
                            <el-select v-model="teamTicketStockId" placeholder="票区名称"  clearable>
                                <el-option
                                    v-for="stock in stockList"
                                    :key="stock.id"
                                    :label="stock.name"
                                    :value="stock.id">

                                </el-option>
                            </el-select>
                            <el-button icon="el-icon-search"  @click="getTeamPerformTicketPageList">查询</el-button>
                        </div>
                        <div class="add-section">
                        
                            <button
                                class="btn btn-md btn-default add-btn-gray"
                                @click="addTeamTicket">
                                <i class="iconfont icon-jiahao"></i>
                                <span>新增</span>
                            </button>
                        </div>

                        <table class="simple-splitter">
                            <tr>
                                <th>票区名称</th>
                                <th>票价（元）</th>
                                <th>票名称</th>
                                <th>总票数（张）</th>
                                <th>剩余票数（张）</th>
                                <th>核销区域</th>
                                <th>权益</th>
                                <th>操作</th>
                            </tr>
                            <tr v-for="ticket in teamTicketList" >
                                <td>{{ ticket.stockName }}</td>
                                <td>{{ m2front(ticket.price) }}</td>
                                <td>{{ ticket.performTicketName }}</td>
                                <td>{{ ticket.totalAmount }}</td>
                                <td>{{ ticket.remainAmount }}</td>
                                <td>{{ ticket.siteName }}</td>
                                <td>{{ ticket.rightsName }}</td>
                                <td>
                                    <i
                                        class="iconfont icon-bianji"
                                        @click="editTeamTicket(ticket)"></i>
                                    <i
                                        class="iconfont icon-shanchu"
                                        @click="deleteTeamTicket(ticket.performTicketId)"></i>
                                </td>
                            </tr>
                        </table>
                        <div class="page_footer">
                              <el-pagination layout="prev, pager, next" background :total="ticketTeamPageInfo.total" @current-change="handleTeamPageChange"> </el-pagination>
                        </div>
                        
                        <teamTicketItemBox
                            ref="teamTicketItemBox"
                            :perform-id="performId"
                            @update="getTeamPerformTicketPageList"></teamTicketItemBox>
                    </div>
                </side-tab-item>
            </side-tab-set>
        </div>
    </div>
</template>

<script>
import umEditorStyle from 'umEditorStyle'
import 'umEditorConfig'
import 'umEditor'
import { SideTabSet, SideTabItem } from 'vue-components/sideTabs'
import Pagination from 'vue-components/Pagination.vue'
import ImgUploader from 'vue-components/ImgUploader.vue'
import * as service from './service'
import GSelect from 'vue-components/GSelect.vue'
import 'utils/confirm'
import { showSuccess, showWrong } from 'mAlert'
import TicketItemBox from './ticketItemBox.vue'
import teamTicketItemBox from './teamTicketItemBox.vue'
import PerformLocation from './PerformLocation.vue'
import repertoryItemDialog  from "./repertoryItemDialog.vue"
import channelDialog  from "./channelDialog.vue"
import _ from 'lodash'
import moment from 'moment'
import { get } from 'jquery'
import { number } from 'echarts/lib/export'

export default {
    name: 'PerformInfo',
    components: {
        SideTabSet,
        SideTabItem,
        ImgUploader,
        GSelect,
        TicketItemBox,
        teamTicketItemBox,
        PerformLocation,
        repertoryItemDialog,
        channelDialog,
      pagination: Pagination,

    },
    data() {
        return {
            hasBuyLimit: '0',
            locationList: [],
            ticketFilters:[],
            // performTime: [],
            ticketTypesList: [
                { value: '1', label: '普通票' },
                { value: '2', label: '套票' },
                { value: '3', label: '客场球迷票' },
                { value: '4', label: '外地成人票' },
                { value: '5', label: '外地套票' },
                { value: '6', label: '儿童票'},
                { value: '7', label: '外地儿童票'},
            ],
            ticketPageInfo: {
                pageNum: 1,
                pageSize: 10,
                total: 0,
                totalPage: 0,
                list: [],
              },
            ticketTeamPageInfo:{
                pageNum: 1,
                pageSize: 10,
                total: 0,
                list: [],

            },
            repertoryStockId:'',
            filterStocklist:[],
            stockId:'',
            ticketKind:'',
            ticketTypes:{
              '1': "普通票",
              '2': "套票",
              '3': "客场球迷票",
              '4': "外地成人票",
              '5': "外地套票",
              '6': "儿童票",
              '7': "外地儿童票",
              '8': "团体票"
            },
            performTicketName:'',
            teamTicketName:'',
            teamTicketStockId:'',
            info: {
                name: '',
                remark: '',
                locationId: '',
                performDate: '',
                buyLimit: '',
            },

            performTickets: [],
            teamTicketList: [],
            stockList: [],
            rightsList:[],
            pageInfoTickets: {
                pageNum: 1,
                pageSize: 10,
               total: 0,
            }, 
            channelInfoTickets: {
                pageNum: 1,
                pageSize: 10,
                total: 0,
            },

            siteList:[],
            rightsList2:[],
        }
    },
    computed: {
        projectId() {
            return this.$route.query.projectId
        },
        performId() {
            return this.$route.query.performId
        },
        performDate: {
            set(val) {
                this.$set(this.info, 'performDate', moment(val).format('YYYY-MM-DD'))
            },
            get() {
                if (!this.info.performDate) {
                    this.info.performDate = moment().format('YYYY-MM-DD')
                }
                return moment(this.info.performDate, 'YYYY-MM-DD').toDate()
            },
        },
        performTime: {
            set(val) {
                this.$set(this.info, 'startTime', moment(val[0]).format('HHmm'))
                this.$set(this.info, 'endTime', moment(val[1]).format('HHmm'))
            },
            get(val) {
                if (!this.info.startTime) {
                    this.$set(this.info, 'startTime', moment().format('HHmm'))
                }
                if (!this.info.endTime) {
                    this.$set(this.info, 'endTime', moment().format('HHmm'))
                }

                return [moment(this.info.startTime, 'HHmm').toDate(),
                    moment(this.info.endTime, 'HHmm').toDate()]
            },
        },
        salesDateRange: {
            set(val) {
                this.$set(this.info, 'saleStartDate', moment(val[0]).format('YYYY-MM-DD HH:mm:ss'))
                this.$set(this.info, 'saleEndDate', moment(val[1]).format('YYYY-MM-DD HH:mm:ss'))
            },
            get() {
                if (!this.info.saleStartDate) {
                    this.$set(this.info, 'saleStartDate', moment().format('YYYY-MM-DD HH:mm:ss'))
                }
                if (!this.info.saleEndDate) {
                    this.$set(this.info, 'saleEndDate', moment().format('YYYY-MM-DD HH:mm:ss'))
                }

                return [moment(this.info.saleStartDate, 'YYYY-MM-DD HH:mm:ss').toDate(),
                    moment(this.info.saleEndDate, 'YYYY-MM-DD HH:mm:ss').toDate()]
            },
        },
    },
    watch: {
        hasBuyLimit(val) {
            if (val === '0') {
                this.info.buyLimit = ''
            }
        },
    },
    mounted() {
        this.getPerformLocationList()
        this.ue = UM.getEditor('description', {
            initialFrameHeight: 300,
        })

        if (this.performId) {
            service.getPerformItemInfo(this.performId, (res) => {
                this.info = Object.assign(this.info, res.performBaseInfo)

	                this.ue.ready(() => {
	                    this.ue.setContent(this.info.description)
	            	})

		            if (!this.info.buyLimit) {
		                this.hasBuyLimit = '0'
		            } else {
		                this.hasBuyLimit = '1'
		            }
	            })
            this.getRightsList()
            this.getStockList()
            this.getRightsPageList()
            this.getPerformTicketPageList()
            this.getTeamPerformTicketPageList()
        }
    },
    beforeDestroy() {
        this.ue.destroy()
    },
    methods: {
        m2front,
        selectTicket(){

        },
        filterStock(){
            if(this.repertoryStockId){
                this.filterStocklist = this.stockList.filter(
                    (item)=>{
                        return item.id == this.repertoryStockId
                    }
                )
            }else{
                this.filterStocklist = this.stockList
            }
        },
        getPerformTicketPageList(){
            let params = {
                performId: this.performId,
                stockId: this.stockId,
                ticketName:this.performTicketName ,
                ticketKind:this.ticketKind ,
                pageNum:this.ticketPageInfo.pageNum,
                pageSize:10,
            }
            service.getPerformTicketPageList(params,res=>{
                this.performTickets = res.pageInfo.list.map(
                    (ticket) => {
                        console.log( this.rightsList,' this.rightsList')
                        ticket.ticketTypeName = this.ticketTypes[ticket.ticketKind]
                        if(ticket.rightsIds){
                            ticket.rightsIds = ticket.rightsIds.split(',')
                            ticket.rightIds = ticket.rightsIds.map(item => Number(item))
                            ticket.rightsName = ticket.rightsIds.map(rightId => {
                                let rightitem = this.rightsList2[rightId]
                                return rightitem
                            }).join(',')

                        }else{
                            ticket.rightsName = ''
                            ticket.rightsIds = []
                            ticket.rightIds = []
                        }
                        return ticket
                    },
                )
                console.log(this.performTickets,'performTickets')
                this.ticketPageInfo.total = res.pageInfo.total
            })
        },
        getTeamPerformTicketPageList(){
            service.getTeamPerformTicketPageList({
                performId: this.performId,
                stockId: this.teamTicketStockId,
                ticketName:this.teamTicketName ,
                pageNum:this.ticketTeamPageInfo.pageNum,
                pageSize:10,
            },res => {
                this.teamTicketList = res.pageInfo.list.map(ticket => {
                        if(ticket.rightsIds){
                            ticket.rightsIds = ticket.rightsIds.split(',')
                            ticket.rightIds = ticket.rightsIds.map(item => Number(item))
                            ticket.rightsName = ticket.rightsIds.map(rightId => {
                                let rightitem = this.rightsList2[rightId]
                                return rightitem
                            }).join(',')   
                        }else{
                            ticket.rightsName = ''
                            ticket.rightsIds = []
                            ticket.rightIds = []
                        }
                        return ticket
                })
                this.ticketTeamPageInfo.total = res.pageInfo.total

            })

        },
        handleRightsPageChange(pageNum) {
          this.ticketPageInfo.pageNum = pageNum;
          this.getPerformTicketPageList();
        },
        handleTeamPageChange(pageNum) {
          console.log(pageNum,'pageNum')
          this.ticketTeamPageInfo.pageNum = pageNum;
          this.getTeamPerformTicketPageList();
        },
        tabTag(index){
            this.$refs.sidetabsetBox.activateTabIndex(index)
        },
        getStockList(){
            service.getStockList(this.performId, (res) => {
                // this.performTickets = res.performTickets
                this.stockList = res.stockList
                this.filterStocklist = this.stockList.map((item) => {
                    return{
                        ...item,
                    }
                })
            })

        },
        getRightsList() {
          service.getRightsList({ performId: this.performId }, (res) => {
            console.log(res);
            this.rightsList2 =  res.rightsList.reduce((map, type) => {
            map[type.id] = type.name;
            return map;
            }, {});
            console.log(this.rightsList2,123456);
            this.getTickets()
          });
        },
        getRightsPageList(pageNum){
            let params = {
                performId:this.performId,
                pageNum,
                pageSize:10}
            service.getRightsPageList(params,(res) => {
                this.rightsList = res.pageInfo.list
                this.channelInfoTickets.total = res.pageInfo.total
                this.channelInfoTickets.pageNum = res.pageInfo.pageNum
            })
        },
        
        handleChangePage(pageNum) {
            this.getRightsPageList(pageNum);
        },
      
        handleChangePageSize(pageSize) {
          this.channelInfoTickets.pageSize = pageSize;
          this.getRightsPageList(1); // 重置为第一页
        },
        goprev() {
            vueRouter.go(-1)
        },
        getPerformLocationList() {
            service.getPerformLocationList((res) => {
                this.locationList = res.locationList.map(lo => ({
                    name: lo.name,
                    value: lo.id,
                }))
                console.log(this.locationList,'locationList')
            })
        },
        addNewAddr() {
            this.$refs.location.show()
        },
        cancel() {
            this.$confirm('确认取消操作吗？').then(() => {
                vueRouter.go(-1)
            })
        },
        confirm() {
            if (!$(this.$el).validate()) {
                return
            }
            if (!this.info.locationId) {
                showWrong('请选择比赛地址')
                return
            }
            if (!this.ue.getContent()) {
                showWrong('请填写赛事详情')
                return
            }

            if (this.hasBuyLimit === '0') {
                this.info.buyLimit = undefined
            }
            service.updatePerformInfo({
                ...this.info,
                projectId: this.projectId,
                performName: this.info.name,
                description: this.ue.getContent(),
                address: (_.find(this.locationList, { value: this.info.locationId }) || {}).name,
            	}, (res) => {
                if (this.performId) {
                    showSuccess('编辑成功')
                } else {
                    showSuccess('新增成功')
                    vueRouter.replace({
                        path: 'perform',
                        query: {
                            projectId: this.projectId,
                            performId: res.performId,
                        },
                    })
                }
            })
        },
        getTickets() {
            this.getPerformTicketPageList()
        },
        deleteTicket(ticketId) {
            this.$confirm('是否确认删除？').then(() => {
                service.deletePerformTicket(ticketId, (res) => {
	                    showSuccess('删除成功')
		                this.getTickets()
		            })
           		})
        },
        deleteTeamTicket(ticketId) {
            this.$confirm('是否确认删除？').then(() => {
                service.deletePerformTicket(ticketId, (res) => {
	                    showSuccess('删除成功')
		                this.getTeamPerformTicketPageList()
		            })
           		})
        },

        addTicket() {
            this.$refs.ticketBox.show()
        },
        addTeamTicket() {
            this.$refs.teamTicketItemBox.show()
        },
        /**
         * 编辑库存信息
         * @param stock 库存对象
         */
        editStock(stock) {
            this.$refs.repertoryBox.show(stock)
        },

        /**
         * 删除库存
         * @param stockId 库存ID
         */
        deleteStock(stockId) {
            this.$confirm('是否确认删除？').then(() => {
                service.deleteStock(stockId, (res) => {
                    showSuccess('删除成功')
                    this.getStockList()
                })
            })
        },
        /**
         * 编辑权益信息
         * @param right 权益对象
         */
        editRight(right) {
            this.$refs.channelBox.show(right)
        },

        /**
         * 删除权益
         * @param rightId 权益ID
         */
        deleteRight(rightId) {
            this.$confirm('是否确认删除？').then(() => {
                service.delRights(rightId, (res) => {
                    showSuccess('删除成功')
                    this.getRightsPageList()
                })
            })
        },
        addRepertory(){
            this.$refs.repertoryBox.show()
        },
        addChannel() {
            this.$refs.channelBox.show()
        },
        editTicket(ticket) {
            this.$refs.ticketBox.show(ticket)
        },
        editTeamTicket(ticket) {
            this.$refs.teamTicketItemBox.show(ticket)
        },
    },
}
</script>

<style lang="scss">
    .pure-switch {
        margin-top: 7px;
    }
    .el-button {
      border-radius: 8px;
      width: 100px;
      height: 36px;
      padding: 0 15px;
    }
    .selcet-section{
        display: flex;
        align-items: center;
        height: 80px;
        gap: 20px;
    }
    .repertory-select-section{
        justify-content: space-between;
    }
    .page_footer{
        padding: 20px 0;
    }
    .tiny {
        width: 85px !important;
    }

    .ticket-list-wrap {
        padding: 0 20px;
    }
</style>
