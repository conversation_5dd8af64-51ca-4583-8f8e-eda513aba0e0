package com.asiainfo.aisports.service.job;

import com.asiainfo.aisports.annotation.RoutingKey;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.cache.RedisKeyEnum;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.ProjectConstants;
import com.asiainfo.aisports.domain.core.Perform;
import com.asiainfo.aisports.domain.core.PerformStock;
import com.asiainfo.aisports.model.project.PerformTicketVo;
import com.asiainfo.aisports.model.project.PerformVo;
import com.asiainfo.aisports.model.project.StockVo;
import com.asiainfo.aisports.persistence.core.PerformMapper;
import com.asiainfo.aisports.persistence.core.PerformStockMapper;
import com.asiainfo.aisports.persistence.core.TakeSeatLogMapper;
import com.asiainfo.aisports.service.ProjectNewService;
import com.asiainfo.aisports.utils.RedisKeyGenerator;
import net.sf.json.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class ProjectTakeSeatJob implements Job {
    private static final Logger logger = LoggerFactory.getLogger(ProjectTakeSeatJob.class);

    @Autowired
    private RedisKeyGenerator redisKeyGenerator;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private ProjectNewService projectNewService;

    @Autowired
    private PerformStockMapper performStockMapper;

    @Autowired
    private PerformMapper performMapper;

    @Autowired
    private TakeSeatLogMapper takeSeatLogMapper;


    @Override
    @TargetDataSource(name = "{}")
    public void execute(@RoutingKey Long centerId, JSONObject params) {
        logger.info("task seat,centerId=[{}],params=[{}]", centerId, params);

        String logIds = params.getString("logIds");



    }
}
