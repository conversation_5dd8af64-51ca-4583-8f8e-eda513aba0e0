package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.HealthRecordService;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.*;
import com.asiainfo.aisports.param.StaticParamConfig;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.pay.client.PayClient;
import com.asiainfo.aisports.pay.client.Result;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.CoachService;
import com.asiainfo.aisports.service.*;
import com.asiainfo.aisports.service.anhui.province.EntryTicketService;
import com.asiainfo.aisports.service.chongqing.healthCode.HealthCodeService;
import com.asiainfo.aisports.service.chuzhou.healthCode.AnKangCodeService;
import com.asiainfo.aisports.service.groupcard.GroupCardEnterService;
import com.asiainfo.aisports.service.job.JobEnum;
import com.asiainfo.aisports.service.mq.JobMessageService;
import com.asiainfo.aisports.service.training.TcLessonResvService;
import com.asiainfo.aisports.service.training.TcLessonService;
import com.asiainfo.aisports.service.training.TrainingCourseService;
import com.asiainfo.aisports.service.training.TrainingManageService;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.JSONObjectUtils;
import com.asiainfo.aisports.tools.TradeConstants;
import com.asiainfo.aisports.web.WebResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import com.google.gson.GsonBuilder;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by wanglu5 on 15/3/13.
 */
@Controller
@RequestMapping(value = "/enterHall")
public class EnterHallController {
    private static final Logger LOGGER = LoggerFactory.getLogger(EnterHallController.class);

    //日期格式
    private static final ThreadLocal<DateFormat> shortDateFormat = ThreadLocal.withInitial(() -> new SimpleDateFormat(Constants.DEFAULT_DATE_FORMAT));

    private static final ThreadLocal<DateFormat> timeFormat = ThreadLocal.withInitial(() -> new SimpleDateFormat(Constants.DEFAULT_TIME_FORMAT));

    private static final String TRADE_TYPE = TradeConstants.TradeTypeCode.TICKET_CHECK.toString();

    @Autowired
    private CheckTicketService checkTicketService;
    @Autowired
    private CustQueryService custQueryService;
    @Autowired
    private PayClient payClient;
    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private EnterHallService enterHallService;
    @Autowired
    private StaticParamConfig staticParamConfig;
    @Autowired
    private CoachService coachService;
    @Autowired
    private EntCustMemberMapper entCustMemberMapper;
    @Autowired
    private EnterpriseMapper enterpriseMapper;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private CouponService couponService;
    @Autowired
    private TradeTicketMapper tradeTicketMapper;
    @Autowired
    private TicketTypeService ticketTypeService;
    @Autowired
    private MoneyCardEnterHallService moneyCardEnterHallService;
    @Autowired
    private HolidayService holidayService;
    @Autowired
    private EcardQrcodeService ecardQrcodeService;
    @Autowired
    private SiteServiceService siteServiceService;
    @Autowired
    private DepositService depositService;
    @Autowired
    private StudentService studentService;
    @Autowired
    private TrainingCourseService trainingCourseService;
    @Autowired
    private TradeDepositChangeMapper tradeDepositChangeMapper;
    @Autowired
    private TcLessonResvService tcLessonResvService;
    @Autowired
    private TrainingManageService trainingManageService;
    @Autowired
    private TradeExtraService tradeExtraService;
    @Autowired
    private AnKangCodeService anKangCodeService;
    @Autowired
    private EntryTicketService entryTicketService;
    @Autowired
    private TradeMapper tradeMapper;
    @Autowired
    private JobMessageService jobMessageService;

    @Autowired
    private HealthRecordService healthRecordService;

    @Autowired
    private TcLessonService tcLessonService;
    @Autowired
    private HealthCodeService ykmHealthCodeService;

    @Autowired
    private GroupCardEnterService groupCardEnterService;

    @Autowired
    private KeyRandomService keyRandomService;
    @Autowired
    private CusEcardTabMapper cusEcardTabMapper;


    /**
     * 入馆页面
     *
     * @return
     */
    @RequestMapping(value = "/init")
    public String enterHallInit(LoginStaff staff, ModelMap modelMap) {
        this.getInitParam(staff, modelMap);
        return "/enterHall/enterHall";
    }

    /**
     * 入馆页面(新)
     */
    @RequestMapping(value = "/newInit")
    public String enterHallNewInit(LoginStaff staff, ModelMap modelMap,LoginSite loginSite) {
        Long venueId = staff.getVenueId();
        if (loginSite != null && loginSite.getVenueId() == 0) {
            venueId = loginSite.getCurVenueId(TRADE_TYPE);
            if (loginSite.getVenueList(TRADE_TYPE) == null || loginSite.getVenueList(TRADE_TYPE).isEmpty()) {
                modelMap.put("message", "该中心服务点未配置验卡入馆业务，请在<服务点管理>功能中配置。");
                return "/error";
            }
            //将登录的venueId重新赋值为选择的venueId
            staff.setVenueId(venueId);
        }
        modelMap.put("tradeTypeCode", TRADE_TYPE);
        this.getInitParam(staff, modelMap);
        return "/enterHallNew/enterHallNew";
    }

    /**
     * 获取入馆页面需要初始化的参数
     *
     * @param staff
     * @param modelMap
     */
    private void getInitParam(LoginStaff staff, ModelMap modelMap) {
        modelMap.put("needKeyServiceIds", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.HAS_KEY));
        modelMap.put("notKeyboardInputCard", venueParamConfig.judgeInputCard(staff.getVenueId(), TradeConstants.TradeTypeCode.TICKET_CHECK.toString()).toString());
        modelMap.put("defaultManyPerson", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.DEFAULT_MANY_PERSON));
        modelMap.put("enterHallTrainingAdvanceQuery", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_TRAINING_ADVANCED_QUERY));
        modelMap.put("courseEnterCanBrushKey", venueParamConfig.getBoolean(staff.getVenueId(), Constants.VenueParam.COURSE_ENTER_CAN_BRUSH_KEY));
        modelMap.put("privateCourseEntryInfo", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.PRIVATE_COURSE_ENTRY_INFO_TAG));
        modelMap.put("repeatEnterKey", venueParamConfig.getBoolean(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_TIMING_CARD_REPEAT_ALERT));
        modelMap.put("continueTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.TICKET_ENTERHALL_CONTINUE_TAG));
        modelMap.put("waterControlTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_INIT_WATER));
        modelMap.put("waterInitAmount", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.WATER_INIT_AMOUNT));
        modelMap.put("cardCashMoney", venueParamConfig.getInt(staff.getVenueId(), Constants.VenueParam.CARD_CASH_MONEY));
        modelMap.put("family_card_tag", venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.FAMILY_CARD_TAG));
        modelMap.put("keyCashMoney", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.CHANGE_KEY_CASH_MONEY));
    }

    /**
     * 根据输入信息获取可入馆的项目
     *
     * @param input
     * @param staff
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/getEnterhallData")
    @ResponseBody
    public WebResult getEnterhallData(@RequestParam(value = "input") String input,
                                      @RequestParam(value = "noCardTag", required = false) String noCardTag,
                                      LoginStaff staff,LoginSite loginSite) throws Exception {
        if (loginSite != null && loginSite.getVenueId() == 0) {
            //将登录的venueId重新赋值为选择的venueId
            staff.setVenueId(loginSite.getCurVenueId(TRADE_TYPE));
        }
        if (Strings.isNullOrEmpty(input)) {
            return new WebResult(1, "查询条件不能为空");
        }

        List<Long> serviceIdList = siteServiceService.findServicesByTradeType(staff.getSiteId(), TradeConstants.TradeTypeCode.TICKET_CHECK.toString());
        if (serviceIdList.isEmpty()) {
            return new WebResult(10000, "您的工号未配置服务点或服务点未包含入馆功能");
        }

        // 滁州体育馆
        if (Constants.CommonCompare.NUMBER_ONE.equals(venueParamConfig.getParam(staff.getCenterId(),Constants.HealthCode.CHUZHOU_SPORTS_TAG,Constants.CommonCompare.NUMBER_ZERO))){
            // 如果是安康码链接 https://akm.ahzwfw.gov.cn/akm-sj-mgr/index.html#/myAkm?isScan=1&id=akm:qrcode:31c8f5f489ef4a95b8b49e317bfd46ed&cityNo=340000000000
            if (input.contains("akm.ahzwfw")) {
                ServiceResult userInfo = entryTicketService.getUserInfoWithHealthCode(staff.getCenterId(), input);

                if (userInfo.getError() != 0) {
                    return new WebResult(1, userInfo.getMessage());
                }

                String hotoolPost = userInfo.getString("hotoolPost");
                JSONObject js = JSONObjectUtils.parseObject(hotoolPost);

                String data = js.getString("data");
                JSONObject jsonObject = JSONObjectUtils.parseObject(data);
                String userPhone = jsonObject.getString("userPhone");
                String psptId = jsonObject.getString("idCardNo");

                // 根据用户手机号获取可用入馆票资源
                ServiceResult ticketWithUserPhone = entryTicketService.getTicketWithUserPhone(staff.getCenterId(), userPhone);
                if (ticketWithUserPhone.getError() == 0) {
                    String tradeTicket = ticketWithUserPhone.getString("tradeTicket");
                    JSONObject tradeTicketJson = JSONObjectUtils.parseObject(tradeTicket);

                    // 将安康码扫描转化为其中一张入馆票，用这个ticketNo去进行入馆资源查询
                    input = tradeTicketJson.getString("ticketNo");
                }else {
                    // 没查到入馆票 将安康码内容的身份证信息代入查询用户其余信息等
                    CustInfo param = new CustInfo();
                    param.setPsptId(psptId);
                    List<CustInfo> custList = custQueryService.findByPsptIdAndName(param);
                    if (custList != null && !custList.isEmpty()){
                        input = custList.get(0).getEcardNo();
                    }
                }
            }
        }

        // 查看是否是渝快码，是的话查询最新的一张卡号
        if (input.contains("YKM")){
            input = checkYKMCode(input, staff.getCenterId(), staff.getVenueId());
        }


        if (!Constants.Tag.YES.equals(noCardTag)) { // 如果页面可以输入卡号，则先查询是否是卡号
            String ecardNo = "";
            // 如果输入值长度为18且是EN开头，则从缓存获取对应的卡号
            if (input.length() == 18 && input.startsWith("EN")) {
                ecardNo = ecardQrcodeService.getEcardNo(input.substring(2), staff.getCenterId());
            }
            // 如果缓存中未查询到，则认为输入的是卡号
            if (Strings.isNullOrEmpty(ecardNo)) {
                ecardNo = input;
            }
            CustInfo custInfo = custQueryService.findCustMsgByEcardNo(ecardNo, staff.getCenterId());
            if (custInfo != null) {
                // 校验会员有效性
                WebResult result = this.checkCustInfo(custInfo);
                if (result.getError() != 0) {
                    return result;
                }

                WebResult webResult;
                if (Constants.CustomerType.ENTERPRISE.equals(custInfo.getCustType())) { // 企业卡查询
                    webResult = findEntCustDeposits(custInfo, serviceIdList, staff);
                } else if (Constants.CustomerType.GROUP.equals(custInfo.getCustType())) {
                    // 团体卡
                    webResult = findGroupDeposits(custInfo, serviceIdList, staff);

                } else { //普通卡查询
                    webResult = findCustDeposits(custInfo, serviceIdList, staff);
                }

                if (webResult.getError() == 0) {
                    webResult.set("custInfo", custInfo);
                    webResult.set("type", Constants.EnterHallType.CARD);
                    //客户标签
                    webResult.set("custTab",cusEcardTabMapper.listNoPage(ecardNo,staff.getCenterId()));
                }
                return webResult;
            }
        }

        // 根据票号查询
        List resultList = checkTicketService.findTicketListByTicketId(input, staff.getVenueId(), serviceIdList);
        if (!resultList.isEmpty()) {
            return new WebResult().set("type", Constants.EnterHallType.TICKET).set("resultList", resultList);
        }

        // 根据券号查询
        CouponEntity couponEntity = enterHallService.getCouponEntityByNo(input);
        if (couponEntity != null) {
            Coupon coupon = couponService.getCouponById(couponEntity.getCouponId());
            ServiceResult serviceResult = enterHallService.checkCouponService(coupon.getValue(), staff.getCenterId(), serviceIdList);
            if (serviceResult.getError() != 0) {
                return new WebResult(serviceResult.getError(), serviceResult.getMessage());
            }
            ServiceResult checkRsult = enterHallService.checkCoupon(couponEntity, staff);
            if (checkRsult.getError() != 0) {
                return new WebResult(checkRsult.getError(), checkRsult.getMessage());
            }

            return new WebResult()
                    .set("type", Constants.EnterHallType.COUPON)
                    .set("couponName", coupon.getCouponName())
                    .set("couponEntity", couponEntity)
                    .set("expireDate", shortDateFormat.get().format(couponEntity.getExpireDate()) + " " + coupon.getStartSegment() + ":00-" + coupon.getEndSegment() + ":00");
        }

        // 根据身份证号查询
        List<CouponEntity> couponEntities = couponService.getValidEntitys(input, null);
        if (!couponEntities.isEmpty()) {
            List<Map> couponInfoList = this.checkCoupon(staff, couponEntities, serviceIdList);
            if (couponInfoList.isEmpty()) {
                return new WebResult(1, "未查询到可用券");
            }
            return new WebResult().set("type", Constants.EnterHallType.COUPON).set("couponInfoList", couponInfoList);
        }
        return new WebResult(1, "没有有效的可入馆信息");
    }

    private WebResult findGroupDeposits(CustInfo custInfo, List<Long> serviceIdList, LoginStaff staff) {
        ServiceResult enterHallData = groupCardEnterService.findEnterHallData(custInfo, serviceIdList, staff);
        return new WebResult().setAll(enterHallData);
    }

    private String checkYKMCode(String input, Long centerId, Long venueId) {
        return ykmHealthCodeService.selectCardByPhone(input,centerId,venueId);
    }

    private WebResult findCustDeposits(CustInfo custInfo, List<Long> serviceIdList, LoginStaff staff) throws Exception {
        boolean noBuyTicket = venueParamConfig.getBoolean(staff.getCenterId(), Constants.VenueParam.ENTER_HALL_NOT_BUY_TICKET);

        //中心服务点登录的instId为null 换成当前登录的venueId
        staff.setInstId(staff.getInstId() == null ? staff.getVenueId() : staff.getInstId());
        //查询当前场馆下的所有有效的教练
        List<Coach> coachList = coachService.selectVenueCoachList(staff.getVenueId());
        // 查询有效的专项卡
        List<DepositInfo> depositInfos;
        if (noBuyTicket) {
            depositInfos = enterHallService.getDepositList(staff, custInfo, serviceIdList,
                    Constants.DepositLimitType.PERIOD, Constants.DepositLimitType.TIMES);
        } else {
            depositInfos = enterHallService.getDepositList(staff, custInfo, serviceIdList,
                    Constants.DepositLimitType.MONEY, Constants.DepositLimitType.PERIOD, Constants.DepositLimitType.TIMES);
        }

        // 获取用户可使用的私教课程
        List<CommonCourseEnroll> privateCourses = enterHallService.getPrivateCourses(custInfo.getCustId(), staff.getInstId());

        if (!privateCourses.isEmpty()) {
            for (CommonCourseEnroll commonCourseEnroll : privateCourses) {
                //当前场馆下的所有有效的教练
                commonCourseEnroll.setCourseWithCoachList(coachList);
            }
        }

        // 获取用户可使用的培训课程
        List<CourseEnrollPlus> trainingCourses;

        if (venueParamConfig.getBoolean(staff.getCenterId(), Constants.VenueParam.TRAINING_COURSE_NEW_MODEL)) {
            trainingCourses = enterHallService.getNewCourses(Collections.singletonList(custInfo.getCustId()), null, staff.getInstId());
        } else {
            trainingCourses = enterHallService.getCourses(custInfo.getCustId(), staff.getVenueId(), staff.getCenterId(), null, staff.getInstId(), null);
        }

        //查询每个培训课程对应能选取的所有教练
        if (!trainingCourses.isEmpty()){
            for (CourseEnrollPlus courseEnrollPlus :trainingCourses) {
                //当前场馆下的所有有效的教练
                courseEnrollPlus.setCourseWithCoachList(coachList);
            }
        }

        // 获取用户可使用的滑冰课程
        List<Map<String, Object>> skatingCourses = enterHallService.getSkatingCourseEnrollInfo(custInfo.getCustId(), staff.getInstId());

        // 获取用户可使用的优惠券
        List<CouponEntity> couponEntities = couponService.getValidEntitys(null, custInfo.getCustId());
        List<Map> couponInfoList = this.checkCoupon(staff, couponEntities, serviceIdList);

        List<Map> ecardTicketList = Lists.newArrayList();
        if (!noBuyTicket) {
            // 一卡通可入馆的票
            ecardTicketList = enterHallService.queryEcardTicketList(custInfo, staff);
        }

        if (depositInfos.isEmpty() && privateCourses.isEmpty() && trainingCourses.isEmpty() && skatingCourses.isEmpty() && couponInfoList.isEmpty() && ecardTicketList.isEmpty()) {
            return new WebResult(1, "没有有效的可入馆信息");
        } else {
            List<Map<String, Object>> trainingCourseReserveList = enterHallService.queryReserveListByCustId(custInfo.getCustId());
            //查询当天有效的培训预约
            List<DataMap> tcLessonReserveList = tcLessonResvService.selectByCustId(custInfo.getCustId(), staff.getVenueId());
            return new WebResult().set("enterpriseTag", Constants.Tag.NO)
                    .set("depositInfo", depositInfos)
                    .set("requirePassword", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_REQUIRE_PASSWORD))
                    .set("privateCourses", privateCourses)
                    .set("trainingCourses", trainingCourses)
                    .set("skatingCourses", skatingCourses)
                    .set("ecardTicketList", ecardTicketList)
                    .set("couponInfoList", couponInfoList)
                    .set("trainingCourseReserveList", trainingCourseReserveList)
                    .set("tcLessonReserveList", tcLessonReserveList)
                    //天通苑定制参数 用于-入馆仅一条信息时也会进行弹窗选择 1为有效参数
                    .set("tianTongYuanInTheLibrary", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.TIAN_TONG_YUAN_IN_THE_LIBRARY));
        }
    }

    private WebResult findEntCustDeposits(CustInfo custInfo, List<Long> serviceIdList, LoginStaff staff) throws Exception {
        // 获取公司信息
        Enterprise enterprise = enterpriseMapper.selectByPrimaryKey(custInfo.getEnterpriseId());
        if (enterprise == null) {
            return new WebResult(1, "企业信息不存在");
        }

        // 获取成员信息
        List<EntCustMember> entCustMemberList = findEntCustMembers(custInfo.getCustId());
        if (entCustMemberList.isEmpty()) {
            return new WebResult(1, "该企业卡用户没有成员信息");
        }

        // 获取专项卡信息
        // 查询有效的专项卡
        List<DepositInfo> depositInfoList = enterHallService.getDepositList(staff, custInfo, serviceIdList,
                Constants.DepositLimitType.PERIOD, Constants.DepositLimitType.TIMES);
        if (depositInfoList.isEmpty()) {
            return new WebResult(1, "该企业用户没有有效专项卡信息");
        }

        return new WebResult()
                .set("enterpriseTag", Constants.Tag.YES)
                .set("enterprise", enterprise)
                .set("depositInfoList", depositInfoList)
                .set("entCustMemberList", entCustMemberList);
    }

    /**
     * 验卡入馆刷卡查询
     *
     * @param ecardNo
     * @param staff   登陆员工
     * @return
     */
    @RequestMapping(value = "/checkInputCard")
    @ResponseBody
    public WebResult checkInputCard(@RequestParam(value = "ecardNo") String ecardNo,
                                    LoginStaff staff) throws Exception {
        CustInfo custInfo = custQueryService.findCustMsgByEcardNo(ecardNo, staff.getCenterId());
        if (custInfo == null) {
            return new WebResult(1, "未查询到客户信息");
        }

        //校验会员有效性
        WebResult webResult = checkCustInfo(custInfo);
        if (webResult.getError() != 0) {
            return webResult;
        }

        List<Long> serviceIdList = siteServiceService.findServicesByTradeType(staff.getSiteId(), TradeConstants.TradeTypeCode.TICKET_CHECK.toString());
        if (serviceIdList.isEmpty()) {
            return new WebResult(10000, "您的工号未配置服务点或服务点未包含入馆功能");
        }

        List<DepositInfo> depositInfos;
        //查询有效的专项卡
        if (!Constants.CustomerType.ENTERPRISE.equals(custInfo.getCustType())) {
            depositInfos = enterHallService.getDepositList(staff, custInfo, serviceIdList,
                    Constants.DepositLimitType.PERIOD, Constants.DepositLimitType.TIMES, Constants.DepositLimitType.MONEY);
        } else { //企业卡用户不需要查询余额卡信息
            depositInfos = enterHallService.getDepositList(staff, custInfo, serviceIdList,
                    Constants.DepositLimitType.PERIOD, Constants.DepositLimitType.TIMES);
        }
        if (depositInfos.isEmpty()) {
            return new WebResult(1, "该用户没有有效卡信息");
        }

        return webResult.set("depositInfo", depositInfos)
                .set("custInfo", custInfo)
                .set("requirePassword", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_REQUIRE_PASSWORD));
    }


    /**
     * 校验卡信息
     *
     * @param custInfo
     * @return
     */
    private WebResult checkCustInfo(CustInfo custInfo) {
        if (custInfo.getEcardCustId() == null) {
            return new WebResult(1, "客户资料不完整");
        }
        if (Constants.UserStatus.LOSS.equals(custInfo.getUserStatus()) || Constants.UserStatus.PRE_LOSS.equals(custInfo.getUserStatus())) {
            return new WebResult(1, "一卡通已挂失");
        }
        return new WebResult();
    }

    /**
     * 验卡入馆提交
     *
     * @param inputNum  入馆个数
     * @param custId
     * @param passWord  一卡通密码
     * @param depositId
     * @param ecardNo   一卡通号
     * @param keyIds    钥匙数组
     * @param staff     登陆员工
     * @param tickets   [{ticketTypeId: 票类型Id, num: 票数量}]
     * @return
     */
    @RequestMapping(value = "/submitEcard")
    @ResponseBody
    public WebResult submitEcard(@RequestParam(value = "custId") Long custId,
                                 @RequestParam(value = "ecardNo") String ecardNo,
                                 @RequestParam(value = "passWord", required = false) String passWord,
                                 @RequestParam(value = "inputNum") int inputNum,
                                 @RequestParam(value = "depositId", required = false) Long depositId,
                                 @RequestParam(value = "keyIds", required = false) String[] keyIds,
                                 @RequestParam(value = "memberId", required = false) Long memberId,
                                 @RequestParam(value = "tickets", required = false) String tickets,
                                 @RequestParam(value = "serviceId", required = false) Long serviceId,
                                 @RequestParam(value = "cashPledge", required = false) Long cashPledge,
                                 @RequestParam(value = "randomCode", required = false) String[] randomCode,
                                 LoginStaff staff,LoginSite loginSite) {
        if (inputNum == 0) {
            return new WebResult(1, "入馆个数不能为0,请重新选择");
        }

        if (loginSite != null && loginSite.getVenueId() == 0) {
            //将登录的venueId重新赋值为选择的venueId
            staff.setVenueId(loginSite.getCurVenueId(TRADE_TYPE));
        }

        keyRandomService.saveRandomCode(keyIds, staff.getVenueId(), randomCode);

        //专项卡转卡未完工或待审核不能入馆
        if (depositId != null) {
            Map<String, Object> map = tradeDepositChangeMapper.selectByDepositId(depositId);
            String subscribeState = MapUtils.getString(map, "subscribeState");
            if (Constants.SubscribeState.UNCOMPLETED.equals(subscribeState) || Constants.SubscribeState.PENDING.equals(subscribeState)) {
                return new WebResult(1, "专项卡转卡未完工或待审核不能入馆");
            }
        }

        CustInfo custInfo = custQueryService.findByCustId(custId);
        if (custInfo == null || custInfo.getEcardCustId() == null) {
            return new WebResult(1, "客户资料不全");
        }

        //安康码校验
        ServiceResult anKangCodeResult = anKangCodeService.checkHealthCodeWithPhone(staff, custInfo.getCustName(), custInfo.getContactPhone());
        if (anKangCodeResult.getError() != 0) {
            return new WebResult(1, anKangCodeResult.getMessage());
        }

        //判断是否配置了校验密码
        String requirePassword = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_REQUIRE_PASSWORD);
        if (!Strings.isNullOrEmpty(requirePassword) && memberId == null) { //企业入馆暂时不校验
            Deposit deposit = depositService.findById(depositId);
            if (requirePassword.contains(deposit.getLimitType())) {
                if (Strings.isNullOrEmpty(passWord)) {
                    return new WebResult(1, "请输入密码");
                }
                Result result = payClient.validatePassword(custInfo.getCenterId(), custInfo.getEcardCustId(), passWord);
                if (result.getCode() != 0) {
                    return new WebResult(result.getCode(), "一卡通校验失败:" + result.getMessage());
                }
            }
        }

        WebResult webResult = new WebResult();

        if (!Strings.isNullOrEmpty(tickets)) {
            //如果票信息不为空,表示是余额专项卡或一卡通付款
            webResult.set("tradeId", moneyCardEnterHallService.moneyCardEnterHall(staff, ecardNo, tickets, keyIds, serviceId, depositId, null).get("tradeId"));
        } else {
            if (depositId == null) {
                LOGGER.error("该卡[{}]入馆时depositId为空", ecardNo);
                return new WebResult(1, "入馆失败，未选择可入馆的卡，请查询后重试");
            }
            //调用公共的方法入馆啦
            ServiceResult serviceResult = enterHallService.enterHall(depositId, ecardNo, staff, keyIds, inputNum, memberId, serviceId, Constants.TicketState.CHECKED, Constants.EnterMethodCode.MEMBER_CARD, cashPledge, false);
            webResult.putAll(serviceResult);
            if (webResult.getError() == 0) {
                webResult.set("redirect", "/enterHall/init");
            }
        }
        // 滁州体育馆
        if (Constants.CommonCompare.NUMBER_ONE.equals(venueParamConfig.getParam(staff.getCenterId(),Constants.HealthCode.CHUZHOU_SPORTS_TAG,Constants.CommonCompare.NUMBER_ZERO))){
            // 入馆成功，同步安徽省平台的入馆信息
            DataMap dataMap = new DataMap();
            dataMap.put("tradeId",MapUtils.getLong(webResult,"tradeId"));
            jobMessageService.sendMessage(staff.getCenterId(), JobEnum.ANHUI_PROVINCE_ALL_USER_ENTRY_JOB, dataMap);
//        this.synchronousAnhuiProviceEntryWithEcard(MapUtils.getLong(webResult,"tradeId"));
        }
        String healthTips = null;
        String enterHallCheckHealth = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_CHECK_HEALTH);
        if (StringUtils.isNotEmpty(enterHallCheckHealth) && "1".equals(enterHallCheckHealth)) {
            healthTips = healthRecordService.checkHealthAgreementResultStr(null,staff.getCenterId(),staff.getVenueId(),custId);
            webResult.put("healthTips", healthTips);
        }

        return webResult.set("printTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_PRINT_TICKET))
                .set("enterprisePrintTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTERPRISE_CARD_ENTER_HALL_PRINT_TICKET))
                .set("healthTips",healthTips);
    }

    /**
     * 进行重复入馆判断
     *
     * @param depositId
     * @return
     */
    @RequestMapping(value = "/isRepeatEnter")
    @ResponseBody
    public WebResult isRepeatEnter(@RequestParam(value = "depositId") Long depositId) {
        Date noEnterOutTime = checkTicketService.getNoEnterOutTime(depositId);
        if (noEnterOutTime != null) {
            return new WebResult(1, "期间卡在 " + timeFormat.get().format(noEnterOutTime) + " 入馆后，没有出馆。是否仍然入馆");
        }
        return new WebResult(0, "success");
    }

    /**
     * 验证二维码票是否有效(根据票号,获取订单所购买的所有的票)
     *
     * @param ticketId 票id
     * @param staff    登陆员工
     * @return
     */
    @RequestMapping(value = "/judgeTicketValid")
    @ResponseBody
    public WebResult judgeQRCodeTicketValid(@RequestParam(value = "ticketId", required = false) String ticketId,
                                            LoginStaff staff) {
        List<Long> serviceIdList = siteServiceService.findServicesByTradeType(staff.getSiteId(), TradeConstants.TradeTypeCode.TICKET_CHECK.toString());
        if (serviceIdList.isEmpty()) {
            return new WebResult(10000, "您的工号未配置服务点或服务点未包含入馆功能");
        }

        List resultList = checkTicketService.findTicketListByTicketId(ticketId, staff.getVenueId(), serviceIdList);
        if (resultList.isEmpty()) {
            return new WebResult(1, "没有查到符合的票信息!");
        }
        return new WebResult().set("resultList", resultList);
    }

    /**
     * 验票入馆提交
     *
     * @param staff   登陆员工
     * @param tickets [{"ticketId":2019041100155536,"keyId":"016","continueTag":"1"}]
     * @return
     */
    @RequestMapping(value = "/updateTicketState")
    @ResponseBody
    public WebResult updateQRCodeTicketState(@RequestParam(value = "tickets") String tickets,
                                             LoginStaff staff) {
        String healthTips = null;
        List<DataMap> ticketList = new GsonBuilder().create().fromJson(tickets, new TypeToken<List<DataMap>>() {
        }.getType());
        if (ticketList.isEmpty()) {
            return new WebResult(1, "没有选择需要入馆的票!");
        }
        Long venueId = staff.getVenueId();
        for (DataMap dataMap : ticketList) {
            TradeTicket ticket = tradeTicketMapper.selectByPrimaryKey(dataMap.getLong("ticketId"));
            if (null == venueId) {
                venueId = ticket.getVenueId();
            }

            //获取场馆配置的入馆规则
            String enterHallRuleStr = venueParamConfig.getParam(venueId, Constants.VenueParam.ENTER_HALL_RULE);
            JSONObject enterHallRule = Strings.isNullOrEmpty(enterHallRuleStr) ? null : JSONObject.fromObject(enterHallRuleStr);
            ServiceResult checkResult = checkTicketService.validateTicket(ticket, enterHallRule, false, Constants.Tag.NO);
            if (checkResult.getError() != 0) {
                WebResult webResult = new WebResult();
                webResult.putAll(checkResult);
                return webResult;
            }
            //分享票校验
            if (dataMap.containsKey("ticketUserId") && dataMap.getLong("ticketUserId") != null) {
                Long ticketUserId = dataMap.getLong("ticketUserId");
                ServiceResult serviceResult = checkTicketService.checkTicketUser(ticketUserId);
                if (serviceResult.getError() != 0) {
                    return new WebResult(serviceResult.getError(), serviceResult.getMessage());
                }
            }

            // 获取该场馆是否校验健康承诺书
            String enterHallCheckHealth = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_CHECK_HEALTH);
            if (StringUtils.isNotEmpty(enterHallCheckHealth) && "1".equals(enterHallCheckHealth)) {
                healthTips = healthRecordService.checkHealthAgreementResultStr(null,staff.getCenterId(),staff.getVenueId(),ticket.getCustId());
            }

        }
        //执行批量票入馆动作
        checkTicketService.batchTicketEnterHall(ticketList, venueId, staff.getStaffId(), null);
        return new WebResult(0, "更新票据状态成功").set("redirect", "/enterHall/init").set("healthTips", healthTips);
    }

    /**
     * 判断是否绑定钥匙
     *
     * @param productId 产品id
     * @param staff     登陆员工
     * @return
     */
    @RequestMapping(value = "/checkKey")
    @ResponseBody
    public WebResult checkKey(@RequestParam(value = "productId") String productId, LoginStaff staff) {
        WebResult webResult = new WebResult();
        webResult.set("hasKey", 0);
        String serviceId = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.HAS_KEY);

        List<ProdOperatingLimit> list = checkTicketService.getService(productId, staff);
        for (ProdOperatingLimit limit : list) {
            if (StringUtils.isNotEmpty(serviceId) && serviceId.contains(String.valueOf(limit.getServiceId()))) {
                webResult.set("hasKey", 1);
                limit.setIsKey("1");
            } else {
                limit.setIsKey("0");
            }
            limit.setServiceName(staticParamConfig.getName(Constants.StaticParam.SERVICE, limit.getServiceId()));
        }

        return webResult.set("list", list);
    }

    /**
     * 根据券号判断优惠券是否有效,或者根据证件号查询出该证件号下有效的券
     *
     * @param staff
     * @param couponNo
     * @param psptId
     * @return
     */
    @RequestMapping("/judgeCouponValid")
    @ResponseBody
    public WebResult judgeCoupon(LoginStaff staff,
                                 @RequestParam(value = "couponNo", required = false) String couponNo,
                                 @RequestParam(value = "psptId", required = false) String psptId,
                                 @RequestParam(value = "ecardNo", required = false) String ecardNo) {
        List<Long> serviceIdList = siteServiceService.findServicesByTradeType(staff.getSiteId(), TradeConstants.TradeTypeCode.TICKET_CHECK.toString());
        if (serviceIdList.isEmpty()) {
            return new WebResult(10000, "您的工号未配置服务点或服务点未包含入馆功能");
        }

        //如果券号不为空,表示校验的是优惠券
        if (!Strings.isNullOrEmpty(couponNo)) {
            CouponEntity couponEntity = enterHallService.getCouponEntityByNo(couponNo);

            ServiceResult checkRsult = enterHallService.checkCoupon(couponEntity, staff);
            if (checkRsult.getError() != 0) {
                return new WebResult(checkRsult.getError(), checkRsult.getMessage());
            }

            Coupon coupon = (Coupon) checkRsult.get("coupon");
            ServiceResult serviceResult = enterHallService.checkCouponService(coupon.getValue(), staff.getCenterId(), serviceIdList);
            if (serviceResult.getError() != 0) {
                return new WebResult(serviceResult.getError(), serviceResult.getMessage());
            }

            return new WebResult().set("couponName", coupon.getCouponName())
                    .set("couponEntity", couponEntity)
                    .set("expireDate", shortDateFormat.get().format(couponEntity.getExpireDate()) + " " + coupon.getStartSegment() + ":00-" + coupon.getEndSegment() + ":00");
        }

        if (!Strings.isNullOrEmpty(psptId) || !Strings.isNullOrEmpty(ecardNo)) {
            List<CouponEntity> couponEntities;
            if (!Strings.isNullOrEmpty(psptId)) {
                couponEntities = couponService.getValidEntitys(psptId, null);
            } else {
                CustInfo custInfo = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
                if (custInfo == null) {
                    return new WebResult(1, "会员信息不存在");
                }
                couponEntities = couponService.getValidEntitys(null, custInfo.getCustId());
            }
            List<Map> couponInfoList = this.checkCoupon(staff, couponEntities, serviceIdList);

            if (couponInfoList.isEmpty()) {
                return new WebResult(1, "未查询到可用券");
            }
            return new WebResult().set("couponInfoList", couponInfoList);
        }
        return new WebResult();
    }

    /**
     * 校验券信息
     *
     * @param staff
     * @param couponEntities
     */
    private List<Map> checkCoupon(LoginStaff staff, List<CouponEntity> couponEntities, List<Long> serviceIdList) {
        List<Map> couponInfoList = Lists.newArrayList();

        Integer weekDay = holidayService.getHoliday(staff.getCenterId(), new Date(), true);
        //循环再次判断券信息
        for (CouponEntity couponEntity : couponEntities) {
            ServiceResult checkRsult = enterHallService.checkCoupon(couponEntity, staff);
            if (checkRsult.getError() == 0) {
                Coupon coupon = (Coupon) checkRsult.get("coupon");
                TicketTypePrice ticketTypePrice = ticketTypeService.queryTicketTypePrice(coupon.getValue(), weekDay);
                if (ticketTypePrice != null) {
                    serviceIdList.stream().forEach(serviceId -> {
                        if (serviceId.equals(ticketTypePrice.getServiceId())) {
                            Map map = Maps.newHashMap();
                            map.put("couponEntity", couponEntity);
                            map.put("couponName", coupon.getCouponName());
                            map.put("expireDate", shortDateFormat.get().format(couponEntity.getExpireDate()) + " " + coupon.getStartSegment() + ":00-" + coupon.getEndSegment() + ":00");
                            couponInfoList.add(map);
                        }
                    });
                }
            }
        }
        return couponInfoList;
    }

    /**
     * 优惠券入馆提交处理
     *
     * @param couponNo
     * @param keyId
     * @return
     */
    @RequestMapping("/submitCouponInfo")
    @ResponseBody
    public WebResult submitCouponInfo(@RequestParam(value = "couponNo") String couponNo,
                                      @RequestParam(value = "keyId", required = false) String keyId,
                                      @RequestParam(value = "randomCode", required = false) String randomCode,
                                      LoginStaff staff) {
        WebResult webResult = new WebResult();
        CouponEntity couponEntity = enterHallService.getCouponEntityByNo(couponNo);
        Integer weekDay = holidayService.getHoliday(staff.getCenterId(), new Date(), true);

        //判断券的状态是否正确
        ServiceResult checkRsult = enterHallService.checkCoupon(couponEntity, staff);
        if (checkRsult.getError() != 0) {
            return new WebResult(checkRsult.getError(), checkRsult.getMessage());
        }

        keyRandomService.saveRandomCode(keyId, staff.getVenueId(), randomCode);

        //判断是否配置了票类型
        Coupon coupon = (Coupon) checkRsult.get("coupon");
        Integer couponAmount;
        TicketTypePrice ticketTypePrice = ticketTypeService.queryTicketTypePrice(coupon.getValue(), weekDay);
        if (ticketTypePrice == null) {
            webResult.setError(1);
            webResult.setMessage("该优惠券对应的票非法");
            return webResult;
        } else {
            couponAmount = ticketTypePrice.getPrice();
        }

        String[] keyIds = {keyId};
        enterHallService.submitCouponInfo(staff, couponEntity, coupon, ticketTypePrice.getServiceId(), couponAmount, keyIds, null);

        return webResult.set("redirect", "/enterHall/init");
    }

    /**
     * 培训入馆获取报名信息
     *
     * @param ecardNo
     * @param staff
     * @return
     */
    @RequestMapping(value = "/getEnrollList")
    @ResponseBody
    public WebResult getEnrollList(@RequestParam(value = "ecardNo") String ecardNo,
                                   LoginStaff staff) {
        CustInfo custInfo = custQueryService.findCustMsgByEcardNo(ecardNo, staff.getCenterId());
        if (custInfo == null) {
            return new WebResult(1, "未查询到客户信息");
        }
        //校验会员有效性
        WebResult webResult = checkCustInfo(custInfo);
        if (webResult.getError() != 0) {
            return webResult;
        }
        //获取用户可使用的私教课程
        List<CommonCourseEnroll> privateCourses = enterHallService.getPrivateCourses(custInfo.getCustId(), staff.getInstId());
        //获取用户可使用的培训课程
        List<CourseEnrollPlus> trainingCourses = enterHallService.getCourses(custInfo.getCustId(), staff.getVenueId(), staff.getCenterId(), null, staff.getInstId(), null);
        //获取可入馆的滑冰课程记录
        List<Map<String, Object>> skatingCourses = enterHallService.getSkatingCourseEnrollInfo(custInfo.getCustId(), staff.getInstId());
        return new WebResult().set("custInfo", custInfo)
                .set("privateCourses", privateCourses)
                .set("trainingCourses", trainingCourses)
                .set("skatingCourses", skatingCourses);
    }

    /**
     * 获得选择的培训的课程的教练列表，筛选可上选择的上课时间
     *
     * @param instId
     * @return
     */
    @RequestMapping("/getCoachList")
    @ResponseBody
    public WebResult getCoachList(@RequestParam(value = "instId") Long instId,
                                  @RequestParam(value = "serviceId") Long serviceId) {
        WebResult webResult = new WebResult();
        List<Coach> coachList = coachService.selectServiceCoachList(instId, serviceId == 0 ? null : serviceId);
        webResult.set("coachList", coachList);
        return webResult;
    }

    /**
     * 校验课程当天是否可上课
     *
     * @param enrollId
     * @param privateTag
     * @param classDate
     * @param coachId 按照教练id筛选
     * @return
     */
    @RequestMapping(value = "/checkClassDate")
    @ResponseBody
    public WebResult checkClassDate(@RequestParam(value = "enrollId") Long enrollId,
                                    String privateTag,
                                    @RequestParam(value = "classDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date classDate,
                                    Long coachId,
                                    LoginStaff staff) {
        WebResult webResult = new WebResult();

        if (Constants.PrivateTag.YES.equals(privateTag)) {
            webResult.putAll(enterHallService.checkPrivateDate(enrollId, staff.getVenueId(), classDate, coachId));
            return webResult;
        }

        String newModeTag = venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.TRAINING_COURSE_NEW_MODEL);
        if (!Constants.Tag.YES.equals(newModeTag)) {
            webResult.putAll(enterHallService.checkClassDate(enrollId, classDate, false));
        } else {
            webResult.putAll(enterHallService.checkNewClassDate(enrollId, staff.getVenueId(), classDate, coachId));
        }

        return webResult;
    }

    /**
     * 获取私教课教练列表
     *
     * @param courseId
     * @return
     */
    @RequestMapping(value = "/queryPrivateCoachList")
    @ResponseBody
    public WebResult queryPrivateCoachList(@RequestParam(value = "courseId") Long courseId) {
        TrainingCourse trainingCourse = trainingCourseService.queryTrainngCourse(courseId);
        return new WebResult().set("coachList", coachService.selectServiceCoachList(trainingCourse.getInstId(), trainingCourse.getServiceId()));
    }

    /**
     * 私教入馆提交
     *
     * @param enrollId 报名id
     * @param staff    登陆员工
     * @param changeNum    多扣次扣除数量
     * @return
     */
    @RequestMapping(value = "/privateCourseEnter")
    @ResponseBody
    public WebResult privateCourseEnter(@RequestParam(value = "enrollId", required = false) Long enrollId,
                                        @RequestParam(value = "ecardNo", required = false) String ecardNo,
                                        @RequestParam(value = "reserveId", required = false) Long reserveId,
                                        @RequestParam(value = "lessonId", required = false) Long lessonId,
                                        @RequestParam(value = "remark", required = false) String remark,
                                        @RequestParam(value = "noReserveTag", required = false) String noReserveTag,
                                        @RequestParam(value = "coachId", required = false) Long coachId,
                                        @RequestParam(value = "changeCoachId", required = false) Long changeCoachId,
                                        @RequestParam(value = "changeNum", required = false) Long changeNum,
                                        @RequestParam(value = "randomCode", required = false) String randomCode,
                                        String keyId, LoginStaff staff,LoginSite loginSite) {
        if (loginSite != null && loginSite.getVenueId() == 0) {
            //将登录的venueId重新赋值为选择的venueId
            Long curVenueId = loginSite.getCurVenueId(TradeConstants.TradeTypeCode.TICKET_CHECK.toString());
            staff.setVenueId(curVenueId);
        }
        CustInfo customer = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        if (customer == null) {
            return new WebResult(1, "未查询到用户信息");
        }
        keyRandomService.saveRandomCode(keyId, staff.getVenueId(), randomCode);

        //安康码校验
        ServiceResult anKangCodeResult = anKangCodeService.checkHealthCodeWithPhone(staff, customer.getCustName(), customer.getContactPhone());
        if (anKangCodeResult.getError() != 0) {
            return new WebResult(1, anKangCodeResult.getMessage());
        }

        Student student = studentService.selectPrivateStu(enrollId);
        if (student == null || !customer.getCustId().equals(student.getCustId())) {
            return new WebResult(1, "学员信息异常");
        }

        //查询是否有预约记录,并对当天有多个预约记录的按开始时间排序
        if (reserveId == null && !Constants.Tag.YES.equals(noReserveTag)) {
            List<TrainingCourseReserve> trainingCourseReserves = enterHallService.queryByEnrollId(enrollId, Constants.PrivateBookingState.BOOK, DateCalUtil.trim(new Date()));
            trainingCourseReserves = trainingCourseReserves.stream().sorted(Comparator.comparing(TrainingCourseReserve::getStartTime)).collect(Collectors.toList());
            reserveId = trainingCourseReserves.isEmpty() ? null : trainingCourseReserves.get(0).getReserveId();
        }

        ServiceResult result = enterHallService.privateCourseEnter(enrollId, keyId, remark, staff, customer, reserveId, "私教入馆扣次", Constants.SigninMode.PLATFORM, coachId, null, lessonId, changeNum);

        if(lessonId != null){
            tcLessonService.updateLesson(lessonId, staff, Constants.TcLessonState.FINISH_LESSON);
        }

        //如果选择了教练，保存在trade_extra表（根据trade_id去打印信息的）中，入馆票打印的时候，先查询这个改动的教练
        if ( null != changeCoachId){
            enterHallService.addTradeExtraWithCoach(result.getLong("tradeId"),changeCoachId);
        }

        if (result.getError() == 0) {
            String healthTips = null;
            // 获取该场馆是否校验健康承诺书
            String enterHallCheckHealth = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_CHECK_HEALTH);
            if (StringUtils.isNotEmpty(enterHallCheckHealth) && "1".equals(enterHallCheckHealth)) {
                healthTips = healthRecordService.checkHealthAgreementResultStr(null,staff.getCenterId(),staff.getVenueId(),customer.getCustId());
            }
            return new WebResult(0, "私教入馆成功")
                    .set("redirect", "/enterHall/init")
                    .set("healthTips",healthTips)
                    .set("printTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.COURSE_ENTERHALL_PRINT_TICKET))
                    .set("tradeId", result.get("tradeId"));
        } else {
            return new WebResult(result.getError(), result.getMessage()).set("redirect", "/enterHall/init");
        }
    }

    /**
     * 培训入馆提交
     *
     * @param enrollId 报名id
     * @param staff    登陆员工
     * @param changeNum    多课次扣除数量
     * @return
     */
    @RequestMapping(value = "/courseEnter")
    @ResponseBody
    public WebResult courseEnter(@RequestParam(value = "enrollId") Long enrollId,
                                 @RequestParam(value = "ecardNo") String ecardNo,
                                 @RequestParam(value = "lessonDayId", required = false) Long lessonDayId,
                                 @RequestParam(value = "classDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date classDate,
                                 @RequestParam(value = "expireTag") String expireTag,
                                 @RequestParam(value = "keyId", required = false) String keyId,
                                 @RequestParam(value = "changeCoachId", required = false) Long changeCoachId,
                                 @RequestParam(value = "changeNum", required = false) Long changeNum,
                                 @RequestParam(value = "randomCode", required = false) String randomCode,
                                 LoginStaff staff) {
        //校验客户信息
        CustInfo customer = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        if (customer == null) {
            return new WebResult(1, "未查询到用户信息");
        }

        keyRandomService.saveRandomCode(keyId, staff.getVenueId(), randomCode);

        //安康码校验
        ServiceResult anKangCodeResult = anKangCodeService.checkHealthCodeWithPhone(staff, customer.getCustName(), customer.getContactPhone());
        if (anKangCodeResult.getError() != 0) {
            return new WebResult(1, anKangCodeResult.getMessage());
        }

        //校验学员信息
        Student student = studentMapper.selectCourseStu(enrollId);
        if (student == null || !customer.getCustId().equals(student.getCustId())) {
            return new WebResult(1, "学员信息异常");
        }

        ServiceResult serviceResult;
        String newModeTag = venueParamConfig.getParam(staff.getCenterId(), Constants.VenueParam.TRAINING_COURSE_NEW_MODEL);
        if (Constants.Tag.YES.equals(newModeTag)) {
            serviceResult = enterHallService.newCourseEnter(enrollId, keyId, staff, customer, lessonDayId, Constants.SigninMode.PLATFORM, null, changeNum);
        } else {
            serviceResult = enterHallService.courseEnter(enrollId, keyId, staff, customer, lessonDayId, classDate, expireTag, null, Constants.EnterMethodCode.MEMBER_CARD,changeNum);
        }
        if (serviceResult.getError() != 0) {
            return new WebResult(serviceResult.getError(), serviceResult.getMessage());
        }
        // 入馆新培训扣次后课表打上已上课标识
        if(lessonDayId != null){
            tcLessonService.updateLesson(lessonDayId, staff, Constants.TcLessonState.FINISH_LESSON);
        }

        Long tradeId = (Long) serviceResult.get("tradeId");

        //如果选择了教练，保存在trade_extra表（根据trade_id去打印信息的）中，入馆票打印的时候，先查询这个改动的教练
        if ( null != changeCoachId){
            enterHallService.addTradeExtraWithCoach(tradeId,changeCoachId);
        }

        String healthTips = null;
        // 获取该场馆是否校验健康承诺书
        String enterHallCheckHealth = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_CHECK_HEALTH);
        if (StringUtils.isNotEmpty(enterHallCheckHealth) && "1".equals(enterHallCheckHealth)) {
            healthTips = healthRecordService.checkHealthAgreementResultStr(null,staff.getCenterId(),staff.getVenueId(),customer.getCustId());
        }

        return new WebResult(0, "培训入馆成功!")
                .set("redirect", "/enterHall/init")
                .set("healthTips",healthTips)
                .set("printTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.COURSE_ENTERHALL_PRINT_TICKET))
                .set("tradeId", tradeId);
    }

    /**
     * 滑冰课程入馆
     *
     * @param enrollId
     * @param ecardNo
     * @param staff
     * @return
     */
    @RequestMapping("/skatingCourseEnter")
    @ResponseBody
    public WebResult skatingCourseEnter(@RequestParam(value = "enrollId") Long enrollId,
                                        @RequestParam(value = "ecardNo") String ecardNo,
                                        LoginStaff staff) {
        CustInfo customer = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        if (customer == null) {
            return new WebResult(1, "未查询到用户信息");
        }
        Student student = studentMapper.selectCourseStu(enrollId);
        if (student == null || !customer.getCustId().equals(student.getCustId())) {
            return new WebResult(1, "学员信息异常！");
        }
        Long tradeId = enterHallService.skatingCourseEnter(enrollId, staff, customer);
        return new WebResult(0, "滑冰课程入馆成功!")
                .set("redirect", "/enterHall/init")
                .set("printTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.COURSE_ENTERHALL_PRINT_TICKET))
                .set("tradeId", tradeId);

    }

    /**
     * 获取场馆教练
     *
     * @param staff 登陆员工
     * @return
     */
    @RequestMapping(value = "/getCoaches")
    @ResponseBody
    public WebResult getCoaches(@RequestParam(value = "serviceId") Long serviceId,
                                LoginStaff staff) {
        List<Coach> coaches = coachService.selectServiceCoachList(staff.getInstId(), serviceId);
        return new WebResult(0, "获取教练成功!").set("coaches", coaches);
    }

    /**
     * 企业用户入馆查询
     *
     * @param staff 登陆员工
     * @return
     */
    @RequestMapping(value = "/enterpriseEnterHall")
    @ResponseBody
    public WebResult enterpriseEnterHall(@RequestParam(value = "ecardNo") String ecardNo,
                                         LoginStaff staff) throws Exception {
        CustInfo custInfo = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
        if (custInfo == null) {
            return new WebResult(1, "未查询到客户信息");
        }

        //校验会员有效性
        WebResult webResult = checkCustInfo(custInfo);
        if (webResult.getError() != 0) {
            return webResult;
        }

        //校验是否是企业用户
        if (!Constants.CustomerType.ENTERPRISE.equals(custInfo.getCustType())) {
            return new WebResult(1, "该卡用户不是企业用户");
        }

        //获取公司信息
        Enterprise enterprise = enterpriseMapper.selectByPrimaryKey(custInfo.getEnterpriseId());
        if (enterprise == null) {
            return new WebResult(1, "企业信息不存在");
        }

        //获取成员信息
        List<EntCustMember> entCustMemberList = findEntCustMembers(custInfo.getCustId());
        if (entCustMemberList.isEmpty()) {
            return new WebResult(1, "该企业卡用户没有成员信息");
        }

        List<Long> serviceIdList = siteServiceService.findServicesByTradeType(staff.getSiteId(), TradeConstants.TradeTypeCode.TICKET_CHECK.toString());
        if (serviceIdList.isEmpty()) {
            return new WebResult(10000, "您的工号未配置服务点或服务点未包含入馆功能");
        }

        //获取专项卡信息
        //查询有效的专项卡
        List<DepositInfo> depositInfoList = enterHallService.getDepositList(staff, custInfo, serviceIdList,
                Constants.DepositLimitType.PERIOD, Constants.DepositLimitType.TIMES);
        if (depositInfoList.isEmpty()) {
            return new WebResult(1, "该企业用户没有有效专项卡信息");
        }

        return new WebResult(0, "获取成功!").set("custInfo", custInfo)
                .set("enterprise", enterprise)
                .set("depositInfoList", depositInfoList)
                .set("entCustMemberList", entCustMemberList);
    }

    private List<EntCustMember> findEntCustMembers(Long custId) {
        EntCustMember param = new EntCustMember();
        param.setEntCustId(custId);
        param.setState(Constants.Status.VALID);
        return entCustMemberMapper.selectByFields(param);
    }

    /**
     * 企业卡入馆提交
     *
     * @param custId
     * @param ecardNo
     * @param passWord
     * @param inputNum
     * @param depositId
     * @param keyIds
     * @param serviceId
     * @param cashPledge
     * @param memberIds
     * @param staff
     * @return
     */
    @RequestMapping(value = "/submitMemberEcard")
    @ResponseBody
    public WebResult submitMemberEcard(@RequestParam(value = "custId") Long custId,
                                       @RequestParam(value = "ecardNo") String ecardNo,
                                       @RequestParam(value = "passWord", required = false) String passWord,
                                       @RequestParam(value = "inputNum") int inputNum,
                                       @RequestParam(value = "depositId", required = false) Long depositId,
                                       @RequestParam(value = "keyIds", required = false) String[] keyIds,
                                       @RequestParam(value = "serviceId", required = false) Long serviceId,
                                       @RequestParam(value = "cashPledge", required = false) Long cashPledge,
                                       String memberIds,
                                       LoginStaff staff) {
        if (inputNum == 0) {
            return new WebResult(1, "入馆个数不能为0,请重新选择");
        }

        //专项卡转卡未完工或待审核不能入馆
        if (depositId != null) {
            Map<String, Object> map = tradeDepositChangeMapper.selectByDepositId(depositId);
            String subscribeState = MapUtils.getString(map, "subscribeState");
            if (Constants.SubscribeState.UNCOMPLETED.equals(subscribeState) || Constants.SubscribeState.PENDING.equals(subscribeState)) {
                return new WebResult(1, "专项卡转卡未完工或待审核不能入馆");
            }
        }

        CustInfo custInfo = custQueryService.findByCustId(custId);
        if (custInfo == null || custInfo.getEcardCustId() == null) {
            return new WebResult(1, "客户资料不全");
        }

        //判断是否配置了校验密码
        String requirePassword = venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_REQUIRE_PASSWORD);
        if (!Strings.isNullOrEmpty(requirePassword) && StringUtils.isBlank(memberIds)) { //企业入馆暂时不校验
            Deposit deposit = depositService.findById(depositId);
            if (requirePassword.contains(deposit.getLimitType())) {
                if (Strings.isNullOrEmpty(passWord)) {
                    return new WebResult(1, "请输入密码");
                }
                Result result = payClient.validatePassword(custInfo.getCenterId(), custInfo.getEcardCustId(), passWord);
                if (result.getCode() != 0) {
                    return new WebResult(result.getCode(), "一卡通校验失败:" + result.getMessage());
                }
            }
        }
        WebResult webResult = new WebResult();
        if (depositId == null) {
            LOGGER.error("该卡[{}]入馆时depositId为空", ecardNo);
            return new WebResult(1, "入馆失败，未选择可入馆的卡，请查询后重试");
        }
        //调用公共的方法入馆啦
        ServiceResult serviceResult = new ServiceResult();
        if (StringUtils.isNotBlank(memberIds)) {
            //企业卡多人入馆
            serviceResult = enterHallService.memberEnterHall(depositId, ecardNo, staff, keyIds, inputNum, memberIds, serviceId, Constants.TicketState.CHECKED, Constants.EnterMethodCode.MEMBER_CARD, cashPledge, false);
        }
        webResult.putAll(serviceResult);
        if (webResult.getError() == 0) {
            webResult.set("redirect", "/enterHall/init");
        }
        // 滁州体育馆
        if (Constants.CommonCompare.NUMBER_ONE.equals(venueParamConfig.getParam(staff.getCenterId(),Constants.HealthCode.CHUZHOU_SPORTS_TAG,Constants.CommonCompare.NUMBER_ZERO))){
            // 入馆成功，同步安徽省平台的入馆信息
            DataMap dataMap = new DataMap();
            dataMap.put("tradeId",MapUtils.getLong(webResult,"tradeId"));
            jobMessageService.sendMessage(staff.getCenterId(), JobEnum.ANHUI_PROVINCE_ALL_USER_ENTRY_JOB, dataMap);
//        this.synchronousAnhuiProviceEntryWithEcard(MapUtils.getLong(webResult,"tradeId"));
        }
        return webResult.set("printTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_PRINT_TICKET))
                .set("enterprisePrintTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTERPRISE_CARD_ENTER_HALL_PRINT_TICKET));
    }

    @RequestMapping("/submitGroupEcard")
    @ResponseBody
    public WebResult submitGroupEcard(@RequestParam(value = "custId") Long custId,
                                      @RequestParam(value = "ecardNo") String ecardNo,
                                      @RequestParam(value = "passWord", required = false) String passWord,
                                      @RequestParam(value = "inputNum") int inputNum,
                                      @RequestParam(value = "depositId", required = false) Long depositId,
                                      @RequestParam(value = "keyIds", required = false) String[] keyIds,
                                      @RequestParam(value = "serviceId", required = false) Long serviceId,
                                      @RequestParam(value = "cashPledge", required = false) Long cashPledge,
                                      Long[] relateIds,
                                      LoginStaff staff) {
        if (inputNum == 0) {
            return new WebResult(1, "入馆个数不能为0,请重新选择");
        }

        //专项卡转卡未完工或待审核不能入馆
        if (depositId != null) {
            Map<String, Object> map = tradeDepositChangeMapper.selectByDepositId(depositId);
            String subscribeState = MapUtils.getString(map, "subscribeState");
            if (Constants.SubscribeState.UNCOMPLETED.equals(subscribeState) || Constants.SubscribeState.PENDING.equals(subscribeState)) {
                return new WebResult(1, "专项卡转卡未完工或待审核不能入馆");
            }
        }

        CustInfo custInfo = custQueryService.findByCustId(custId);
        if (custInfo == null || custInfo.getEcardCustId() == null) {
            return new WebResult(1, "客户资料不全");
        }

        WebResult webResult = new WebResult();
        if (depositId == null) {
            LOGGER.error("该卡[{}]入馆时depositId为空", ecardNo);
            return new WebResult(1, "入馆失败，未选择可入馆的卡，请查询后重试");
        }
        if (relateIds == null || relateIds.length == 0) {
            return new WebResult(1, "入馆失败，请选择成员");

        }

        Deposit deposit = depositService.findById(depositId);

        ServiceResult serviceResult = groupCardEnterService.enterHall(relateIds, deposit, custInfo, staff, Collections.singletonList(serviceId), Constants.EnterMethodCode.GROUP_CARD, inputNum, keyIds, cashPledge, false);

        webResult.putAll(serviceResult);
        if (webResult.getError() == 0) {
            webResult.set("redirect", "/enterHall/init");
        }
        // 滁州体育馆
        if (Constants.CommonCompare.NUMBER_ONE.equals(venueParamConfig.getParam(staff.getCenterId(),Constants.HealthCode.CHUZHOU_SPORTS_TAG,Constants.CommonCompare.NUMBER_ZERO))){
            // 入馆成功，同步安徽省平台的入馆信息
            DataMap dataMap = new DataMap();
            dataMap.put("tradeId",MapUtils.getLong(webResult,"tradeId"));
            jobMessageService.sendMessage(staff.getCenterId(), JobEnum.ANHUI_PROVINCE_ALL_USER_ENTRY_JOB, dataMap);
        }
        return webResult.set("printTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTER_HALL_PRINT_TICKET))
                .set("enterprisePrintTicketTag", venueParamConfig.getParam(staff.getVenueId(), Constants.VenueParam.ENTERPRISE_CARD_ENTER_HALL_PRINT_TICKET));

    }

    /**
     * 查询验票信息
     *
     * @param staff
     * @param ticketId
     * @param type
     * @return
     */
    @RequestMapping("/getTicketEnterInfo")
    @ResponseBody
    public WebResult getTicketEnterInfo(LoginStaff staff, @RequestParam String ticketId,
                                        @RequestParam(value = "type", defaultValue = Constants.EnterHallTicketType.FIELD_TICKET) String type) {
        List<Map<String, Object>> ticketEnterInfo = Lists.newArrayList();
        switch (type) {
            case Constants.EnterHallTicketType.FIELD_TICKET:
                ticketEnterInfo = enterHallService.getTicketEnterInfo(staff.getCenterId(), ticketId);
                break;
            case Constants.EnterHallTicketType.SCATTERED_TICKET:
                ticketEnterInfo = enterHallService.scatteredTicketEnterInfo(staff.getCenterId(), ticketId);
                break;
            default:
                break;
        }
        return new WebResult().set("ticketInfos", ticketEnterInfo);
    }

    /**
     * 安徽省平台
     * 一卡通入馆同步入馆信息
     *
     * @param tradeId
     */
    private void synchronousAnhuiProviceEntryWithEcard(Long tradeId) {
        Trade trade = tradeMapper.selectByPrimaryKey(tradeId);

        // 滁州体育馆
        if (Constants.CommonCompare.NUMBER_ONE.equals(venueParamConfig.getParam(trade.getCenterId(), Constants.HealthCode.CHUZHOU_SPORTS_TAG, Constants.CommonCompare.NUMBER_ZERO))) {
            // 查询是否存在已同步标志
            if (!Constants.CommonCompare.NUMBER_ONE.equals(tradeExtraService.getTradeExtra(trade.getTradeId(),Constants.AnHuiProvince.ALL_USER_ENTRY_INFO_UPLOAD_TAG))){
                CustInfo custInfo = custQueryService.findByCustId(trade.getCustId());
                String userName = custInfo == null ? null : custInfo.getCustName();
                String userPhone = custInfo == null ? null : custInfo.getContactPhone();
                String psptId = custInfo == null ? null : custInfo.getPsptId();
                // 入馆行为录入（一卡通入馆）
                ServiceResult result = entryTicketService.allUserEntryInfoUpload(trade.getCenterId(), userName, userPhone, psptId, Constants.CommonCompare.NUMBER_THREE, null, null,trade.getTradeDesc());

                if (result.getError() != 0){
                    throw new ServiceException(result.getMessage(),1);
                }
                // 同步完后添加一个tradeExtra信息标识当前订单已经同步过，防止因为不同的业务重复同步订单
                tradeExtraService.replace(trade.getTradeId(),Constants.AnHuiProvince.ALL_USER_ENTRY_INFO_UPLOAD_TAG,"1");
            }

        }
    }


}
