<template>
    <div class="edit-match-subject-page">
        <div class="title-box">
            <h1 style="display:block">
                <span style="font: 20px bold;">{{ mode }}赛事</span>
                <span class="btn-return" @click="backToList">
                    <i class="iconfont icon-zuojiantou"></i>
                </span>
            </h1>
        </div>
        <div class="body">
            <form-line label="专题名称" required>
                <input 
                    v-validate="'required'" 
                    v-model="detail.title"
                    :disabled="isEffective"
                    maxlength="20" 
                    type="text" 
                    class="normal" 
                    placeholder="请输入专题名称">
            </form-line>
            <form-line label="专题描述" required>
                <input 
                    v-validate="'required'" 
                    v-model="detail.description" 
                    :disabled="isEffective"
                    maxlength="20" 
                    type="text" 
                    class="normal" 
                    placeholder="请输入专题描述">
            </form-line>
            <form-line label="专题封面" required>
                <div ref="fileUploader" class="file-uploader">
                    <img v-if="coverImage" :src="coverImage" />
                    <div class="file-uploader-inner">
                        <i class="iconfont icon-jiahao2"></i>
                    </div>
                    <input id="upload" :disabled="isEffective" :style="isEffective ? 'cursor: auto' : ''" name="upload" type="file" onchange="uploadFile()" />
                </div>
                <p style="color: #999999;">{{ remark }}</p>
                <p>或选择封面模板</p>
                <div class="image-template">
                    <img 
                        v-for="(item, index) in imageTempList" 
                        :src="item" 
                        :key="index" 
                        width="90" 
                        height="68" 
                        style="border-radius: 4px;margin-right: 10px;"
                        @click="handleSelectTemp(item)">
                </div>
            </form-line>
            <form-line label="专题排序" required>
                <input v-validate="'required'" v-model="detail.sort" :disabled="isEffective" type="text" class="normal" placeholder="请输入专题排序">
            </form-line>
            <form-line label="有效期" required>
                <el-date-picker
                    v-model="timeRange"
                    :disabled="isEffective"
                    type="daterange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </form-line>
            <form-line label="包含赛事" required>
                <div class="match-list-wrap">
                    <div class="tip">
                        <i class="iconfont icon-guize1"></i>
                        {{ tip }}
                    </div>
                    <div id="match-list" class="match-list">
                        <draggable v-model="selectedMatchList" animation="1000" @start="onStart" @end="onEnd">
                            <transition-group>
                                <div class="match-item" v-for="(item, index) in selectedMatchList" :key="index" :data-id="item.id">
                                    <div class="match-state">
                                        <span :class="campStatusStyle[item.campStatusName]" class="public-status">{{ item.campStatusName }}</span>
                                    </div>
                                    <div class="match-name">{{ item.title }}</div>
                                    <div class="remove-match" @click="removeMatch(item)">
                                        <i class="iconfont icon-cuowu1"></i>
                                    </div>
                                </div>
                            </transition-group>
                        </draggable> 
                    </div>
                    <div class="add-btn" v-if="!isEffective">
                        <span class="blue-underline-text" @click="handleAddMatch">新增赛事</span>
                    </div>
                    <el-popover
                        ref="popover1"
                        placement="right"
                        width="250"
                        trigger="click">
                        <div class="popover-select">
                            <el-select
                                v-model="camp"
                                size="medium"
                                clearable
                                placeholder="请选择赛事"
                            >
                                <el-option
                                    v-for="item in campList"
                                    :key="item.campId"
                                    :label="item.title"
                                    :value="item.campId"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div class="popover-btns">
                            <button class="btn btn-default btn-grey" @click="hidePopover">取消</button>
                            <button class="btn btn-primary" @click="confirmAddMatch">确定</button>
                        </div>
                    </el-popover>
                </div>
            </form-line>
            <div class="form-line form-line-btn" v-if="!isEffective">
                <div class="form-field">
                    <button class="btn btn-primary" @click="handleSave">保存</button>
                    <button class="btn btn-default btn-grey" onClick="window.history.back()">取消</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import dayjs from 'dayjs'
import * as API from '../api'
import { vValidate } from 'directives/vValidate'
import mAlert from 'mAlert'
import storeFile from "utils/storeFile";
import draggable from 'vuedraggable'

window.uploadFile = function () {
    storeFile().then((data) => {
        $(document).trigger('upload-success', data)
    })
}

    export default {
        mixins: [vValidate],
        components: {
            draggable,
            "el-select": () => import("element-ui").then(({ Select }) => Select),
            "el-option": () => import("element-ui").then(({ Option }) => Option),
            "el-popover": () => import("element-ui").then(({ Popover }) => Popover),
            "el-button": () => import("element-ui").then(({ Button }) => Button),
            'el-date-picker': () => import('element-ui').then(({ DatePicker }) => DatePicker),
        },
        data() {
            return {
                isEffective: false, // 如果上架了，不能编辑
                id: '',
                detail: {},
                coverImage: '',
                remark: '建议尺寸750*562px，支持jpeg、png、bmp，不超过500kb',
                tip: '可上下拖动赛事做排序展示，将应用于小程序。',
                selectedMatchList: [],
                imageTempList: [
                    'http://xports-test.oss-cn-hangzhou.aliyuncs.com/dev/public/miniapp/material/match-template-1.png',
                    'http://xports-test.oss-cn-hangzhou.aliyuncs.com/dev/public/miniapp/material/match-template-2.png',
                    'http://xports-test.oss-cn-hangzhou.aliyuncs.com/dev/public/miniapp/material/match-template-3.png',
                    'http://xports-test.oss-cn-hangzhou.aliyuncs.com/dev/public/miniapp/material/match-template-4.png'
                ],
                camp: '',
                campList: [],
                campStatusStyle: {
                    '报名中': 'applying',
                    '进行中': 'progressing',
                    '已结束': 'finished'
                },
                sort: '',
                timeRange: [],
                currentTime: new Date(),
            }
        },
        computed: {
            mode() {
                return this.id && this.id != '' ? '编辑' : '新增'  
            },
        },
        mounted() {
            if (this.$route.query.id) {
                this.id = this.$route.query.id;
                this.isEffective = this.$route.query.status == '0' ? false : true; // 0-下架 1-上架
                this.getMatchSubjectDetail()
            }
            this.init()
            $(document).on('upload-success', (e, data) => {
                console.log(data)
                this.coverImage = data.url
            })
        },
        beforeDestroy() {
            $(document).off('upload-success')
        },
        methods: {
            init() {
                this.getMatchList()
            },
            getMatchSubjectDetail() {
                API.querySeriesCampaignDetail({ id: this.id }, (res) => {
                    this.detail = res.detail,
                    this.coverImage = res.detail.coverImg,
                    this.timeRange = [res.detail.seriesStartDate, res.detail.seriesEndDate]
                    this.selectedMatchList = res.detail.campList
                })
            },
            getMatchList() {
                API.queryCampaignList({
                    pageSize: 50
                }, (res) => {
                    this.campList = res.campList.list;
                    // this.campList.forEach(i => {
                    //     i.diffStartTime = dayjs(this.currentTime).diff(i.campStartDate, 'day')
                    //     i.diffEndTime = dayjs(this.currentTime).diff(i.campEndDate, 'day')
                    //     i.diffEnrollStartTime = dayjs(this.currentTime).diff(i.enrollStartDate, 'day')
                    //     i.diffEnrollEndTime = dayjs(this.currentTime).diff(i.enrollEndDate, 'day')
                    // })
                })
            },
            backToList() {
                window.history.back()
            },
            // // 图片上传
            // handleUpload() {
            //     storeFile().then((data) => {
            //         console.log(data)
            //         this.detail.coverImage = data.url;
            //         this.coverImage = data.url
            //     });
            // },
            // handleUploadFailure(err) {
            //     mAlert.showWrong(err.message)
            // },
            handleSelectTemp(image) {
                if (this.isEffective) {
                    return
                }
                this.detail.coverImage = image;
                this.coverImage = image
            },
            handleAddMatch() {
                this.$refs.popover1.doShow()
            },
            hidePopover() {
                this.$refs.popover1.doClose()
            },
            confirmAddMatch() {
                let selectedMatch = this.campList.find(i => i.campId == this.camp);
                if (this.selectedMatchList.includes(selectedMatch)) {
                    mAlert.showWarning('已包含该赛事')
                    return
                }
                this.selectedMatchList.push(selectedMatch);
                this.hidePopover();
                this.camp = ''
            },
            removeMatch(item) {
                let removeIndex = this.selectedMatchList.indexOf(item)
                this.selectedMatchList.splice(removeIndex,1);
            },
            handleSave() {
                let params;
                const formatCampList = this.selectedMatchList.map(i => ({
                    campId: i.campId,
                    sort: this.selectedMatchList.indexOf(i)
                }));
                if (this.id) {
                    params = {
                        id: this.id,
                        title: this.detail.title,
                        description: this.detail.description,
                        seriesStartDate: this.timeRange[0],
                        seriesEndDate: this.timeRange[1],
                        coverImg: this.coverImage,
                        sort: this.detail.sort,
                        campList: JSON.stringify(formatCampList),
                    }
                } else {
                    params = {
                        title: this.detail.title,
                        description: this.detail.description,
                        seriesStartDate: this.timeRange[0],
                        seriesEndDate: this.timeRange[1],
                        coverImg: this.coverImage,
                        sort: this.detail.sort,
                        campList: JSON.stringify(formatCampList),
                    }
                }
                console.log('params', params)
                this.validateAll().then(isOk => {
                    if (!isOk || this.selectedMatchList.length == 0 || this.coverImage.length == 0 || this.selectedMatchList.length == 0) {
                        mAlert.showWrong('请输入必填项')
                        return
                    }
                    API.addOrEditSeriesCampaign({...params}, (res) => {
                        if (res.error == 0) {
                            mAlert.showSuccess('操作成功')
                            this.backToList()
                        } else mAlert.showWrong('操作失败')
                    })
                })
            },
            //开始拖拽事件
            onStart(){
                this.drag=true;
            },
            //拖拽结束事件
            onEnd() {
                this.drag=false;
            },
        },
    }
</script>
