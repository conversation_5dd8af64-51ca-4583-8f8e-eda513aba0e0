<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aisports</artifactId>
        <groupId>com.asiainfo.aisports</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../aisports-aggregator/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.xports.amin</groupId>
    <artifactId>xports-admin-web</artifactId>

    <packaging>war</packaging>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>dev-server</id>
            <properties>
                <env>dev-server</env>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <env>prd</env>
            </properties>
        </profile>
        <profile>
            <id>tst</id>
            <properties>
                <env>tst</env>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>com.asiainfo.aisports</groupId>
            <artifactId>xports-pay-client</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.asiainfo.aisports</groupId>
            <artifactId>aisports-web-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
            <version>1.6.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
            <version>1.6.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.8.0</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.8.0</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-log4j</artifactId>
            <version>1.7.27</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>xports-admin-web</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.0</version>
                <configuration>
                    <warSourceExcludes>assets/*,assets/build/**,assets/config/**,assets/dist/static/**,assets/node_modules/**,assets/src/**,assets/static/**,assets/test/**</warSourceExcludes>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 资源根目录排除各环境的配置，使用单独的资源目录来指定 -->
                <excludes>
                    <exclude>prd/*</exclude>
                    <exclude>dev/*</exclude>
                    <exclude>dev-server/*</exclude>
                    <exclude>tst/*</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/${env}</directory>
            </resource>
        </resources>
    </build>

</project>
