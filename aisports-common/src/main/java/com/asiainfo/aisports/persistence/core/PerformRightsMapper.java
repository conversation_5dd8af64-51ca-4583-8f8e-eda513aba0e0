package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.PerformRights;

import java.util.Date;
import java.util.List;

import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.PerformRightsPlus;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

public interface PerformRightsMapper {
    @Delete({
        "delete from perform_rights",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into perform_rights (id, perform_id, ",
        "pm_id, name, content, ",
        "valid_type, start_date, ",
        "end_date, state, ",
        "create_time, create_staff_id, ",
        "update_time, update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{performId,jdbcType=VARCHAR}, ",
        "#{pmId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, ",
        "#{validType,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP}, ",
        "#{endDate,jdbcType=TIMESTAMP}, #{state,jdbcType=VARCHAR}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(PerformRights record);

    @Select({
        "select",
        "id, perform_id, pm_id, name, content, valid_type, start_date, end_date, state, ",
        "create_time, create_staff_id, update_time, update_staff_id",
        "from perform_rights",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
        @Result(column="pm_id", property="pmId", jdbcType=JdbcType.BIGINT),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="content", property="content", jdbcType=JdbcType.VARCHAR),
        @Result(column="valid_type", property="validType", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    PerformRights selectByPrimaryKey(Long id);

    @Update({
        "update perform_rights",
        "set perform_id = #{performId,jdbcType=VARCHAR},",
          "pm_id = #{pmId,jdbcType=BIGINT},",
          "name = #{name,jdbcType=VARCHAR},",
          "content = #{content,jdbcType=VARCHAR},",
          "valid_type = #{validType,jdbcType=VARCHAR},",
          "start_date = #{startDate,jdbcType=TIMESTAMP},",
          "end_date = #{endDate,jdbcType=TIMESTAMP},",
          "state = #{state,jdbcType=VARCHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(PerformRights record);

    @Select({
        "<script>",
        "select",
        "id, perform_id, pm_id, name, content, valid_type, start_date, end_date, state, create_time, create_staff_id, update_time, update_staff_id",
        "from perform_rights",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"performId != null\">and perform_id = #{performId,jdbcType=VARCHAR}</if>",
        "<if test=\"pmId != null\">and pm_id = #{pmId,jdbcType=BIGINT}</if>",
        "<if test=\"name != null\">and name = #{name,jdbcType=VARCHAR}</if>",
        "<if test=\"content != null\">and content = #{content,jdbcType=VARCHAR}</if>",
        "<if test=\"validType != null\">and valid_type = #{validType,jdbcType=VARCHAR}</if>",
        "<if test=\"startDate != null\">and start_date = #{startDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"endDate != null\">and end_date = #{endDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
        @Result(column="pm_id", property="pmId", jdbcType=JdbcType.BIGINT),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="content", property="content", jdbcType=JdbcType.VARCHAR),
        @Result(column="valid_type", property="validType", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PerformRights> selectByFields(PerformRights param);

    @Update({
        "<script>",
        "update perform_rights",
        "<set>",
        "<if test=\"performId != null\">perform_id = #{performId,jdbcType=VARCHAR},</if>",
        "<if test=\"pmId != null\">pm_id = #{pmId,jdbcType=BIGINT},</if>",
        "<if test=\"name != null\">name = #{name,jdbcType=VARCHAR},</if>",
        "<if test=\"content != null\">content = #{content,jdbcType=VARCHAR},</if>",
        "<if test=\"validType != null\">valid_type = #{validType,jdbcType=VARCHAR},</if>",
        "<if test=\"startDate != null\">start_date = #{startDate,jdbcType=TIMESTAMP},</if>",
        "<if test=\"endDate != null\">end_date = #{endDate,jdbcType=TIMESTAMP},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(PerformRights record);


    @Select({
            "select r.id,r.name,r.content,r.valid_type,r.start_date,r.end_date,r.pm_id,m.name as pm_name,m.contact_addr" ,
            "from perform_ticket_rights a,perform_rights r,activity_merchant m" ,
            "where a.perform_ticket_id = #{performTicketId}" ,
            "  and a.rights_id = r.id" ,
            "and r.pm_id = m.id",
            "  and #{date} between  a.start_date and a.end_date"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="pm_id", property="pmId", jdbcType=JdbcType.BIGINT),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="content", property="content", jdbcType=JdbcType.VARCHAR),
            @Result(column="valid_type", property="validType", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="pm_id", property="pmId", jdbcType=JdbcType.BIGINT),
            @Result(column="pm_name", property="pmName", jdbcType=JdbcType.VARCHAR),
            @Result(column="contact_addr", property="contactAddr", jdbcType=JdbcType.VARCHAR),

    })
    List<PerformRightsPlus> selectByPerformTicketId(@Param("performTicketId") String performTicketId, @Param("date") Date date);


    @Select({
            "<script>",
            "select a.id,a.name,a.content,a.valid_type,a.start_date,a.end_date,b.name as pm_name,b.contact_person,b.contact_phone,b.contact_addr" ,
            "from perform_rights a,activity_merchant  b" ,
            "<where>",
            "a.pm_id = b.id",
            "<if test=\"id != null\">and a.id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"performId != null\">and a.perform_id = #{performId,jdbcType=VARCHAR}</if>",
            "<if test=\"pmId != null\">and a.pm_id = #{pmId,jdbcType=BIGINT}</if>",
            "<if test=\"name != null\">and a.name = #{name,jdbcType=VARCHAR}</if>",
            "<if test=\"content != null\">and a.content = #{content,jdbcType=VARCHAR}</if>",
            "<if test=\"validType != null\">and a.valid_type = #{validType,jdbcType=VARCHAR}</if>",
            "<if test=\"startDate != null\">and a.start_date = #{startDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"endDate != null\">and a.end_date = #{endDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"state != null\">and a.state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and a.create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and a.create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and a.update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and a.update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="content", property="content", jdbcType=JdbcType.VARCHAR),
            @Result(column="valid_type", property="validType", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="pm_name", property="pmName", jdbcType=JdbcType.VARCHAR),
            @Result(column="contact_person", property="contactPerson", jdbcType=JdbcType.VARCHAR),
            @Result(column="contact_phone", property="contactPhone", jdbcType=JdbcType.VARCHAR),
            @Result(column="contact_addr", property="contactAddr", jdbcType=JdbcType.VARCHAR),
    })
    List<DataMap> selectPageListByFields(PerformRights param, RowBounds rowBounds);

    @Select({
            "<script>",
            "select a.id,a.name,a.content,a.valid_type,a.start_date,a.end_date,b.name as pm_name,b.contact_person,b.contact_phone,b.contact_addr" ,
            "from perform_rights a,activity_merchant  b" ,
            "where a.pm_id=b.id",
            "and a.id in",
            "<foreach collection=\"rightsIdList\" item=\"id\" open=\"(\" separator=\",\" close=\")\">",
            "#{id}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="content", property="content", jdbcType=JdbcType.VARCHAR),
            @Result(column="valid_type", property="validType", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="pm_name", property="pmName", jdbcType=JdbcType.VARCHAR),
            @Result(column="contact_person", property="contactPerson", jdbcType=JdbcType.VARCHAR),
            @Result(column="contact_phone", property="contactPhone", jdbcType=JdbcType.VARCHAR),
            @Result(column="contact_addr", property="contactAddr", jdbcType=JdbcType.VARCHAR),
    })
    List<DataMap> selectByIds(@Param("rightsIdList") List<Long> rightsIdList);
}
