package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class PalmsAccount {
    private Long id;

    private String mchId;

    private String apiSecret;

    private String apiCertPws;

    private String apiCertNo;

    private String state;

    private Long centerId;

    private Date createTime;

    private Date updateTime;

    private byte[] apiCertFile;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId == null ? null : mchId.trim();
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret == null ? null : apiSecret.trim();
    }

    public String getApiCertPws() {
        return apiCertPws;
    }

    public void setApiCertPws(String apiCertPws) {
        this.apiCertPws = apiCertPws == null ? null : apiCertPws.trim();
    }

    public String getApiCertNo() {
        return apiCertNo;
    }

    public void setApiCertNo(String apiCertNo) {
        this.apiCertNo = apiCertNo == null ? null : apiCertNo.trim();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public byte[] getApiCertFile() {
        return apiCertFile;
    }

    public void setApiCertFile(byte[] apiCertFile) {
        this.apiCertFile = apiCertFile;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", mchId=").append(mchId);
        sb.append(", apiSecret=").append(apiSecret);
        sb.append(", apiCertPws=").append(apiCertPws);
        sb.append(", apiCertNo=").append(apiCertNo);
        sb.append(", state=").append(state);
        sb.append(", centerId=").append(centerId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}
