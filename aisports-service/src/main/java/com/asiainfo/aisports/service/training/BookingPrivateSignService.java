package com.asiainfo.aisports.service.training;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.PrivateCourseEnroll;
import com.asiainfo.aisports.domain.core.Student;
import com.asiainfo.aisports.domain.core.TcLessonResv;
import com.asiainfo.aisports.domain.core.TrainingCourseReserve;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.CommonCourseEnroll;
import com.asiainfo.aisports.model.CustInfo;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.CustQueryService;
import com.asiainfo.aisports.service.SequenceWrapper;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Millet on 2015/9/23.
 * 私教预约service
 */
@Service
public class BookingPrivateSignService {
    @Autowired
    TrainingCourseReserveMapper trainingCourseReserveMapper;
    @Autowired
    PrivateCourseEnrollMapper privateCourseEnrollMapper;
    @Autowired
    ClassAttendDetailMapper classAttendDetailMapper;
    @Autowired
    SequenceWrapper sequenceWrapper;
    @Autowired
    CustQueryService custQueryService;
    @Autowired
    StudentMapper studentMapper;

    @Autowired
    TcLessonResvMapper tcLessonResvMapper;

    /**
     * 获取教练的私教报名信息
     *
     * @param pageNo  页数
     * @param ecardNo 一卡通号
     * @param coachId 教练id
     * @return
     */
    @Transactional(readOnly = true)
    public List<CommonCourseEnroll> getPrivateEnroll(String ecardNo, Long coachId, Integer pageNo, LoginStaff staff) {
        List<CommonCourseEnroll> courseEnrollList = Lists.newArrayList();
        if (coachId == null) {
            return courseEnrollList;
        }

        if (pageNo == null) {
            pageNo = 1;
        }

        List<Long> studentIds = null;
        if (!Strings.isNullOrEmpty(ecardNo)) {
            CustInfo customer = custQueryService.findByEcardNo(ecardNo, staff.getCenterId());
            Student stuParam = new Student();
            stuParam.setCustId(customer.getCustId());
            stuParam.setState(Constants.Status.VALID);
            List<Student> studentList = studentMapper.selectByFields(stuParam);
            if (studentList.isEmpty()) {
                return courseEnrollList;
            } else {
                studentIds = Lists.newArrayList();
                for (Student student : studentList) {
                    studentIds.add(student.getStuId());
                }
            }
        }
        courseEnrollList = privateCourseEnrollMapper.selectByCoachId(coachId, studentIds, staff.getInstId(), new RowBounds(pageNo, Constants.PAGE_SIZE));

        return getEnrollExtraInfo(courseEnrollList);
    }

    /**
     * 获取预约记录,上课情况,是否结束状态
     *
     * @param courseEnrollList
     * @return
     */
    @Transactional(readOnly = true)
    public List<CommonCourseEnroll> getEnrollExtraInfo(List<CommonCourseEnroll> courseEnrollList) {
        if (courseEnrollList.isEmpty()) {
            return courseEnrollList;
        }

        Date date = DateCalUtil.trim(new Date());
        for (CommonCourseEnroll commonCourseEnroll : courseEnrollList) {
            if (!commonCourseEnroll.getEndDate().before(date)) {
                commonCourseEnroll.setIsOverdue("false");

                //获取私教的预约时间
                List<TrainingCourseReserve> list = trainingCourseReserveMapper.selectByEnrollId(commonCourseEnroll.getEnrollId(), Constants.PrivateBookingState.BOOK, date);
                if (!list.isEmpty()) {
                    //默认取最近的预约记录，默认应该只有1条
                    TrainingCourseReserve courseReserve = list.get(0);
                    commonCourseEnroll.setReserveDate(new SimpleDateFormat("yyyy/MM/dd").format(courseReserve.getReserveDate()));
                    commonCourseEnroll.setStartTime(com.asiainfo.aisports.tools.StringUtils.tranferTime(courseReserve.getStartTime(), ":"));
                    commonCourseEnroll.setEndTime(com.asiainfo.aisports.tools.StringUtils.tranferTime(courseReserve.getEndTime(), ":"));
                    commonCourseEnroll.setReserveId(courseReserve.getReserveId());
                }
            } else {
                commonCourseEnroll.setIsOverdue("true");
            }

            //获取最近上课记录
            List<Map<String, Object>> lessonRecords = classAttendDetailMapper.getRecordListByEnrollId(commonCourseEnroll.getEnrollId());
            if (!lessonRecords.isEmpty()) {
                commonCourseEnroll.setClassDate(new SimpleDateFormat("yyyy-MM-dd").format(lessonRecords.get(0).get("classDate")));
            }
        }

        return courseEnrollList;
    }

    /**
     * 获取固定时间段预约记录
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getBookingRecord(Long coachId, Date reserveDate, String startTime, String endTime) {
        return trainingCourseReserveMapper.getBookingRecord(coachId, reserveDate, Integer.parseInt(startTime), Integer.parseInt(endTime));
    }

    /**
     * 取消预约
     *
     * @return
     */
    @Transactional
    public void cancelBooking(Long reserveId) {
        TrainingCourseReserve trainingCourseReserve = trainingCourseReserveMapper.selectByPrimaryKey(reserveId);
        if (trainingCourseReserve == null) {
            throw new ServiceException("未查询到预约信息");
        }
        TrainingCourseReserve record = new TrainingCourseReserve();
        record.setState(Constants.PrivateBookingState.CANCEL);
        record.setReserveId(reserveId);
        record.setUpdateTime(new Date());
        trainingCourseReserveMapper.updateByPrimaryKeySelective(record);
        // 预约课程话 也一同取消
        if (trainingCourseReserve.getLessonResvId() != null) {
            TcLessonResv tcLessonResv = tcLessonResvMapper.selectByPrimaryKey(trainingCourseReserve.getLessonResvId());
            tcLessonResv.setState(Constants.LessonResvState.CANCELED);
            tcLessonResv.setUpdateTime(new Date());
            tcLessonResvMapper.updateByPrimaryKeySelective(tcLessonResv);
        }
    }

    /**
     * 提交预约
     *
     * @return
     */
    @Transactional
    public void submitBooking(Long enrollId, Date reserveDate, String startTime, String endTime, String remark) {
        Date now = new Date();
        //获取报名信息
        PrivateCourseEnroll privateCourseEnroll = privateCourseEnrollMapper.selectByPrimaryKey(enrollId);

        TrainingCourseReserve record = new TrainingCourseReserve();
        record.setReserveId(sequenceWrapper.reserveSequence());
        record.setStudentId(privateCourseEnroll.getStudentId());
        record.setCoachId(privateCourseEnroll.getCoachId());
        record.setCourseId(privateCourseEnroll.getCourseId());
        record.setEnrollId(enrollId);
        record.setState(Constants.PrivateBookingState.BOOK);
        record.setRemark(remark);
        record.setReserveDate(reserveDate);
        record.setStartTime(startTime);
        record.setEndTime(endTime);
        record.setCreateTime(now);
        record.setUpdateTime(now);
        trainingCourseReserveMapper.insert(record);
    }

    /**
     * 修改课程预约时间
     *
     * @return
     */
    @Transactional
    public void changeBooking(Long reserveId, Date reserveDate, String startTime, String endTime, String remark) {
        TrainingCourseReserve record = new TrainingCourseReserve();
        record.setReserveId(reserveId);
        record.setUpdateTime(new Date());
        record.setReserveDate(reserveDate);
        record.setStartTime(startTime);
        record.setEndTime(endTime);
        record.setRemark(remark);
        trainingCourseReserveMapper.updateByPrimaryKeySelective(record);
    }
}
