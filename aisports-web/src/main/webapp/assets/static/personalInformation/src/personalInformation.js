import Vue from 'vue'
import VueRouter from 'vue-router'
import store from './vuex/store'
import routes from './routes'
import App from './App.vue'
import VueEventBus from 'utils/VueEventBus'
import ListPage from 'vue-components/ListPage'


Vue.use(VueRouter)
Vue.use(ListPage)

window.SEARCHBYID = function (idcard) {
    VueEventBus.$emit('readcard', idcard);
}

const router = new VueRouter({
    routes,
})

new Vue({
    el: '.personal-information',
    components: {
        app: App,
    },
    store,
    router,
})
