<template>
    <div class="title-box group-detail-wrap">
        <h1 style="display: block">
            <span>{{ isEdit ? '编辑' : '新建' }}工作组</span>
            <a
                href="#/"
                class="btn-return"
                title="后退"
                @click.prevent="goprev">
                <i class="iconfont icon-zuojiantou"></i>
            </a>
        </h1>
        <div class="sub-box">
            <side-tab-set>
                <side-tab-item title="基本信息">
                    <div
                        ref="basicInfoWrap"
                        class="basic-info-wrap">
                        <dl class="f-line">
                            <dt class="f-label">
                                组名称：
                            </dt>
                            <dd class="f-field">
                                <input
                                    v-model="groupName"
                                    type="text"
                                    class="normal"
                                    required>
                            </dd>
                        </dl>
                        <dl class="f-line">
                            <dt class="f-label">
                                归属场馆：
                            </dt>
                            <dd class="f-field">
                                <select
                                    v-model="selectedVenue"
                                    class="qselect"
                                    required
                                    vforce>
                                    <option
                                        value=""
                                        disabled>请选择场馆</option>
                                    <option
                                        v-for="(venue, i) in venueList"
                                        :value="venue.venueId"
                                        :key="i">
                                        {{ venue.venueName }}
                                    </option>
                                </select>
                            </dd>
                        </dl>
                        <dl class="f-line">
                            <dt class="f-label">
                                组类别：
                            </dt>
                            <dd class="f-field">
                                <select
                                    v-model="groupKind"
                                    class="qselect"
                                    required
                                    vforce>
                                    <option
                                        value=""
                                        disabled>请选择组类别</option> -->
                                    <option
                                        v-for="(groupKindItem, i) in groupKindList"
                                        :value="groupKindItem.paramValue"
                                        :key="i">
                                        {{ groupKindItem.valueName }}
                                    </option>
                                </select>
                            </dd>
                        </dl>
                        <dl class="f-line">
                            <dt class="f-label">
                                启用状态：
                            </dt>
                            <dd class="f-field">
                                <label class="label-item">
                                    <input
                                        v-model="state"
                                        type="radio"
                                        name="activate"
                                        value="1">
                                    <span>启用</span>
                                </label>
                                <label class="label-item">
                                    <input
                                        v-model="state"
                                        type="radio"
                                        name="activate"
                                        value="0">
                                    <span>未启用</span>
                                </label>
                            </dd>
                        </dl>
                        <dl class="f-line submit-line">
                            <dd class="f-field">
                                <button
                                    class="btn btn-md btn-primary"
                                    @click="submit">保存</button>
                                <button
                                    class="btn btn-md btn-cancel"
                                    @click="goprev">取消</button>
                            </dd>
                        </dl>
                    </div>
                </side-tab-item>
                <side-tab-item
                    :disabled="!isEdit"
                    title="组成员">
                    <group-member
                        v-if="isEdit"
                        :group-id="groupId"></group-member>
                </side-tab-item>
            </side-tab-set>
        </div>
    </div>
</template>

<script>
import { SideTabSet, SideTabItem } from 'vue-components/sideTabs'
import { initSelectPicker } from 'utils/initPlugins'
import venueList from './venueList'
import 'xValidate'
import { showSuccess } from 'mAlert'
import GroupMember from './GroupMember.vue'

export default {
    components: {
        SideTabSet,
        SideTabItem,
        GroupMember,
    },
    props: ['groupId'],
    data() {
        return {
            selectedVenue: '',
            venueList: [],
            groupKindList: [],
            workGroup: false,

            groupName: '',
            groupKind: '',
            state: '1',
        }
    },
    computed: {
        isEdit() {
            return !!this.groupId
        },
    },
    watch: {
        selectedVenue() {
            this.getGroupKindList()
        },
    },
    mounted() {
        venueList.then(({ venueList }) => {
            this.venueList = venueList
        }).then(this.initSelectPicker)

        if (this.isEdit) {
            this.getGroupBasicInfo()
        }
    },
    methods: {
        goprev() {
            vueRouter.go('-1')
        },
        initSelectPicker() {
            if (this.workGroup && !this.groupKind) {
                this.groupKind = this.workGroup.groupKindCode
            }
            if (this.workGroup && !this.selectedVenue) {
                this.selectedVenue = this.workGroup.venueId
            }

            this.$nextTick(() => {
                initSelectPicker(this.$el)
            })
        },
        getGroupKindList() {
            if (!this.selectedVenue) {
                return
            }
            $.get('/workGroupManage/getGroupKindListForVenue', {
                venueId: this.selectedVenue,
            }, ({ groupKindList }) => {
                this.groupKindList = groupKindList
                this.initSelectPicker()
            })
        },
        submit() {
            if (!$(this.$refs.basicInfoWrap).validate()) {
                return
            }

            $.post('/workGroupManage/saveWorkGroupBaseInfo', {
                groupId: this.groupId,
                groupName: this.groupName,
                groupKind: this.groupKind,
                venueId: this.selectedVenue,
                state: this.state,
            }, ({ groupId }) => {
                if (this.isEdit) {
                    showSuccess('修改成功')
                } else {
                    showSuccess('新增成功')
                    vueRouter.push({ path: 'group-detail', query: { groupId } })
                }
            })
        },
        getGroupBasicInfo() {
            $.get('/workGroupManage/getGroupBaseInfo', {
                groupId: this.groupId,
            }, ({ workGroup }) => {
                this.workGroup = workGroup

                this.groupName = workGroup.groupName
                this.groupKind = workGroup.groupKindCode
                this.selectedVenue = workGroup.venueId
                this.state = workGroup.status
            })
        },
    },
}
</script>

<style lang="scss">
	.basic-info-wrap{
		padding: 10px;
	}
</style>
