package com.asiainfo.aisports.tools;

/**
 * project:aisports
 * package:com.asiainfo.aisports.tools
 * Created by Stomic on 14/12/10.
 */
public class TradeConstants {
    //业务类型定义
    public enum TradeTypeCode {
        // 支付
        PAY_TRADE(1),
        // 支付退款
        REFUND_TRADE(2),
        //办卡
        OPEN_CARD(10),
        //场地预订
        VEN_REVERSE(11),
        //访客体验
        VISITOR_EXPERIENCE(12),
        //VIP特赦
        VIP_SPECIAL_PARDON(13),
        //检票入馆
        TICKET_CHECK(14),
        //购票(场地类)
        BUY_TICKET_FOR_FIELD(15),
        //购票(次票)
        BUY_TICKET_FOR_TIMES(16),
        //高温瑜珈
        YOGA(17),
        //批量办卡
        BATCH_CARD_TRADE(18),
        //补卡
        MAKE_UP_CARD(21),
        //冻卡
        FREEZE_CARD(22),
        //解冻
        CANCEL_FROZEN(23),
        //专项卡退卡
        CARD_WITHDRAW(24),
        // 一卡通清退
        CARD_REFUND(26),
        //批量延期
        BATCH_DELAY_CARDS(27),
        //个人延卡
        PERSONAL_DELAY_CARD(28),
        // 预挂失
        PRE_LOSS_REPORT(30),
        // 挂失
        LOSS_REPORT(31),
        // 解挂
        CANCEL_LOSS_REPORT(32),
        //转卡
        TRANSFER_CARD(33),
        //升卡
        UPGRADE_CARD(34),
        //转帐
        TRANSFER_ECARD(35),
        //一卡通充值
        ECARD_RECHARGE(36),
        //一卡通批量充值
        BATCH_RECHARGE(37),
        //第三方消费
        THIRD_PARTY_SPENDING(38),
        //商品售卖
        GOODS_SALE(39),
        //一卡通充值返销
        ECARD_RECHARGE_CANCEL(40),
        //柜子租赁
        CABINET_RENT(41),
        //退柜结算
        CABINET_BACK(42),
        //延期开卡
        DELAY_OPEN_CARD(43),
        //办卡业务返销
        CARD_CANCEL_TRADE(44),
        //出馆
        EXIT_VENUE(45),
        //器材租赁退押金结算
        RENT_CLOSE(46),
        //物品租赁
        RENT_GOODS(47),
        //预定取消
        CANCEL_TICKET(49),
        //退手续费
        REFUND_FEE(50),
        //修改密码
        CHANGE_PASSWORD(51),
        //费用收取
        SEPECIAL_CHARGE(52),
        //协议占场
        FIELD_CYCLE_OCCUPY(53),
        //电子优惠券赠送
        COUPON_COMPAIGN(54),
        //协议占场票数据生成
        OCCUPY_TRADE_TICKET(55),
        //退货
        RETURN_GOODS(56),
        //一卡通延期
        ECARD_DELAY(58),
        //课程培训
        COURSE_ENROLL(59),
        //私教课程报名
        PRIVATE_COURSE_ENROLL(60),
        //课程转让
        COURSE_TRANSFER(61),
        //课程延期
        COURSE_DELAY(62),
        //退课
        REFUND_COURSE(63),
        //占场暂停
        OCCUPY_PAUSE(64),
        //赠卡
        OPEN_CARD_BY_DISCOUNT(65),
        //占场预定
        OCCUPY_FIELD(66),
        //修改租柜人信息
        CHANGE_RENT_MESSAGE(67),
        //预约教练
        COACH_APPOINTMENT(68),
        //一卡通转账转入
        TRANSFER_ECARD_IN(70),
        //特殊费用退款
        SPECIAL_CHARGE_REFUND(71),
        //挂账结算
        ON_CREDIT_SETTLE(72),
        //企业客户挂账协议
        ON_CREDIT_AGREEMENT(73),
        //占场预定取消
        OCCUPY_FIELD_CANCEL(74),
        //预约教练取消
        CANCEL_COACH_APPOINTMENT(75),
        //调班换课
        REPLACE_CLASS_OR_LESSON(76),
        //上课签到（已废弃）
        CLASS_RECORD(77),
        //上课取消签到（已废弃）
        CLASS_CANCEL_RECORD(78),
        //培训扣次
        SIGN_REDUCE(79),
        //活动占场
        ACTIVITY_OCCUPY_FIELD(80),
        //购买优惠券
        BUY_COUPON(81),
        //培训补次
        SIGN_ADD(82),
        //活动报名
        ENROLL_CAMPAIGN(83),
        //场馆活动管理
        CAMPAIGN_MANAGE_CHARGE(84),
        //专项储值卡充值
        SPECIAL_CARD_RECHARGE(85),
        //物业租赁
        PROPERTY_RENT_CHARGE(86),
        //物业定期收费(水电)
        PROPERTY_REGULAR_FEE(87),
        //预约教练退款
        REFUND_COACH_APPOINTMENT(88),
        // 返销
        CANCEL_TRADE(90),
        // 换场
        CHANGE_FIELD(91),
        // 转卡转入
        TRANSFER_CARD_IN(92),
        //赛事活动票
        BUY_PERFORM_TICKET(94),
        //物业租赁退租
        RECEDE_RENT(95),
        //洗澡充值
        RECHARGE_BATHE(96),
        //出库
        DELIVERY_APPROVE(97),
        //入库
        STOCK_APPROVE(98),
        //批量培训报名
        BTACH_TRAINING_ENROLL(99),
        //专项卡变更
        SPECIAL_CARD_CHANGE(101),
        //批量活动报名
        BATCH_ENROLL_CAMPAIGN(102),
        //活动报名取消
        CANCEL_CAMP(103),
        //活动管理
        CAMPAIGN_MANAGE(104),
        //物业租赁
        PROPERTY_RENT(105),
        //转柜
        TRANSFER_CABINET(106),
        //滑冰课程预约
        SKATING_COURSE_APPOINT(107),
        //滑冰课程结账
        PAY_SKATING_COURSE(108),
        //滑冰课程更换课时
        CHANGE_SKATING_COURSE(109),
        //冰课取消预约
        CANCEL_SKATING_COURSE(110),
        //专项卡激活
        ACTIVATE_CARD(111),
        //取消冻卡
        CANCEL_FREEZE_CARD(113),
        //调柜
        REPLACE_CABINET(112),
        //赠课
        HANDSEL_LESSON(115),
        //订金收取
        DEPOSIT_CHARGE(116),
        //订金退款
        DEPOSIT_REFUND(118),
        //换课
        CHANGE_COURSE(121),
        //启迪上课交费
        PAY_IN_CLASS(122),
        //商品销售返销
        CANCEL_GOODS_SALE(125),
        //器材租赁退押金结算
        RENT_CANCEL(126),
        //卡升级
        CARD_UPGRADE_NEW(129),
        //批量私教报名
        BATCH_PRIVATE_SIGN(130),
        //第三方消费退款
        THIRD_PARTY_REFUND(131),
        //票押金退款
        CASH_PLEDGE_REFUND(132),
        //批量发放优惠券
        BATCH_ISSUE_COUPON(134),
        // 押金转收入
        CASH_PLEDGE_TRANS_INCOME(135),
        // 场地预占
        ADVANCE_OCCUPY(137),
        // 审核不通过退款
        REFUSE_REFUND(138),
        // 积分兑换
        PO_EXCHANGE(139),
        // 创建约赛
        GAME(140),
        // 约赛押金收取
        GAME_MARGIN_CHARGE(141),
        //取消参与约赛
        CANCEL_GAME(142),
        // 后付费票
        POST_PAY_TICKET(143),
        // 团购券销售
        SALE_GROUP_BUYING_TICKET(144),
        // 团购券销售取消
        SALE_GROUP_BUYING_TICKET_CANCEL(145),
        // 租柜费用收取
        LOCKER_CHARGE(146),
        // 学校课入馆
        SCHOOLCLASS_ENTER(147),
        // 教室占场
        ROOM_OCCUPY(148),
        // 教室占场取消
        ROOM_OCCUPY_CANCEL(149),
        //运费退款
        EXPRESS_REFUND(150),
        // 课程转入
        COURSE_TRANSFER_INTO(151),
        //协会会员入会或续费
        ASSOCIATION_MEMBER_RECHARGE(152),
        //健康跑小程序活动报名
        PUB_ACTIVITY_ENROLLMENT(153),
        //游艇租赁
        YACHT_RENT(199),
        //订单充值
        YACHT_RECHARGE(200),
        // 退柜退款
        CABINET_BACK_REFUND(201),
        // 优惠券退款
        COUPON_BACK_REFUND(202),
        // 课程调整总课时
        ENROLL_ADJUST_TOTAL_NUM(203),
        // 课程调整剩余课时
        ENROLL_ADJUST_REMAIN_NUM(204),
        // 停车缴费
        PARKING_CHARGE(205),
        // 批量冻卡
        BATCH_FREEZE_CARDS(206),
        // 课程批量延期
        BATCH_COURSE_DELAY(207),
        // 手环超时使用费用
        KEY_OVERTIME_FEE(208),
        // 超时出馆退款
        EXIT_VENUE_REFUND(209),
        // 重庆奥体停车券
        AOTI_PARKING_COUPONS(210),
        //积分兑换商品
        SP_PO_EXCHANGE(211),
        // 约球占场
        FIELD_GROUP_OCCUPY(214),
        // 约球报名
        FIELD_GROUP_SIGN(215),
        // 约球报名取消
        FIELD_GROUP_SIGN_REFUND(216),
        // 滁州全民免费日直接入馆
        CHUZHOU_HOLIDAY_ENTRY(217),
        // 资源分享
        SHARE_DEPOSIT(218),
        // 领取分享的资源
        RECEIVE_SHARE_DEPOSIT(219),
        // 退回领取分享的资源
        ROLLBACK_SHARE_DEPOSIT(220),
        // 费用返还
        COST_RETURN(223),
        // 团课预定
        GROUP_LESSON_BOOK(230),
        // 团课排队
        GROUP_LESSON_BOOK_WAITING(231),
        // 团课预定取消
        GROUP_LESSON_BOOK_CANCEL(232),

        GROUP_LESSON_BOOK_WAITING_REFUND(233),

        //到期自动扣次
        AUTOMATIC_REDUCE(234),

        // 特殊冻卡 ref FREEZE_CARD
        SPECIAL_FREEZE_CARD(235),

        // 补冻卡 ref FREEZE_CARD
        RE_FREEZE_CARD(236),

        // 长训期课自动扣次
        LONG_TERM_ENROLL_REDUCE(237),

        // 冻课
        FREEZE_COURSE(238),

        UNFREEZE_COURSE(239),

        // 场馆场地百度直播购买
        FIELD_BAIDU_LIVE(240),
        // 停车预约
        PARKING_RESV(241),

        // 停车预约退款
        PARKING_RESV_REFUND(242),
        // 清柜审核
        CLEAR_CABINET(244),

        // 取消冻课
        CANCEL_FREEZE_COURSE(243),

        // 团体卡办卡
        OPEN_GROUP_CARD(245),
        // 批量冻柜
        BATCH_FREEZE_CABINET(246),
        // 取消批量冻柜
        CANCEL_BATCH_FREEZE_CABINET(247),
        // 预占取消
        ADVANCE_OCCUPY_CANCEL(248),


        // 卡升级返销
        CARD_UPGRADE_CANCEL_TRADE(250),
        // 器材租赁押金退款
        RENT_PLEDGE_REFUND(251),
        // 柜子租赁押金退款
        RENT_CABINET_REFUND(252),
        BUY_NEW_PERFORM_TICKET(256),
        SEND_TEAM_PERFORM_TICKET(257),

                ;
        private int type;

        TradeTypeCode(int type) {
            this.type = type;
        }

        @Override
        public String toString() {
            return String.valueOf(this.type);
        }

        public int getValue() {
            return this.type;
        }

        public long getLongValue() {
            return type;
        }
    }

    public static boolean isFrozenCard(Long tradeTypeCode) {
        return tradeTypeCode != null &&
                (tradeTypeCode == TradeTypeCode.FREEZE_CARD.getLongValue()
                        || tradeTypeCode == TradeTypeCode.SPECIAL_FREEZE_CARD.getLongValue()
                        || tradeTypeCode == TradeTypeCode.RE_FREEZE_CARD.getLongValue()
                );
    }
}
