<template>
    <div class="title-box">
        <query-area @query="query">
            <el-select
                v-if="centerTag"
                v-model="venueId"
                placeholder="请选择场馆"
                filterable
                size="medium">
                <el-option
                    v-for="item in venueList"
                    :key="item.venueId"
                    :label="item.venueName"
                    :value="item.venueId">
                </el-option>
            </el-select>
            <el-select
                v-model="agreementType"
                filterable
                size="medium">
                <el-option
                    v-for="item in agreementTypeList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                </el-option>
            </el-select>
            <el-select
                v-model="channelId"
                filterable
                size="medium">
                <el-option
                    v-for="item in channelList"
                    :key="item.paramKey"
                    :label="item.paramValue"
                    :value="item.paramKey">
                </el-option>
            </el-select>
            <v-input
                v-model.trim="agreementName"
                placeholder="请输入协议名称"
                type="text"
                @keydown.enter="query"></v-input>
        </query-area>
        <operation-area>
              <button
                v-if="electronicSealFlag == '1'"
                class="btn btn-md btn-default"
                type="button"
                @click="goToSealConfig"
            ><span>电子公章配置</span></button>
            <button
                class="btn btn-md btn-default"
                type="button"
                @click="showCoverBox"
            ><span>新增协议</span></button>
        </operation-area>
        <result-area>
            <xc-table>
                <xc-thead>
                    <th>协议名称</th>
                    <th>协议类型</th>
                    <th>所属服务</th>
                    <th>所属场馆</th>
                    <th>使用渠道</th>
                    <th>更新时间</th>
                    <th>操作</th>
                </xc-thead>
                <xc-body>
                    <tr v-for="item in list">
                        <td>{{ item.agreementName }}</td>
                        <td>{{ item.agreementTypeName }}</td>
                        <td>{{ item.serviceName }}</td>
                        <td>{{ item.venueName }}</td>
                        <td>{{ item.channelName }}</td>
                        <td>{{ item.updateTime }}</td>
                        <td>
                            <template v-if="item.operateTag==='1'">
                                <span
                                    class="blue-underline-text"
                                    @click="toDetail(item,'edit')">编辑</span>
                                <span
                                    class="blue-underline-text"
                                    @click="deleteProtocal(item.agreementId)">删除</span>
                            </template>
                        </td>
                    </tr>
                </xc-body>
            </xc-table>
            <pagination
                v-if="list.length"
                :current-page="pageNum"
                :page-size="pageSize"
                :total="total"
                @changepage="changePage"
                @changepagesize="changePageSize"></pagination>
        </result-area>
        <cover-box
            ref="agreement"
            title="新增协议"
            width="500"
            @submit="toDetail({},'add')">
            <div class="form-list">
                <div class="form-line">
                    <label class="form-label">协议类型</label>
                    <div class="form-field">
                        <el-select
                            v-model="coverAgreementType"
                            placeholder="请选择协议类型"
                            filterable
                            size="medium">
                            <template v-if="centerTag">
                                <el-option
                                    v-for="item in agreementTypeList"
                                    v-if="item.id"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </template>
                            <template v-else>
                                <el-option
                                    v-for="item in agreementTypeList"
                                    v-if="item.id && item.level==='2'"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                                </el-option>
                            </template>
                        </el-select>
                    </div>
                </div>
                <div
                    v-if="centerTag && level==='2'"
                    class="form-line">
                    <label class="form-label">所属场馆</label>
                    <div class="form-field">
                        <el-select
                            v-model="coverVenueId"
                            filterable
                            placeholder="请选择场馆"
                            size="medium">
                            <el-option
                                v-for="item in venueList"
                                v-if="item.venueId"
                                :key="item.venueId"
                                :label="item.venueName"
                                :value="item.venueId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div
                    v-if="serviceTag==='1'"
                    class="form-line">
                    <label class="form-label">所属服务</label>
                    <div class="form-field">
                        <el-select
                            v-model="coverServiceId"
                            filterable
                            placeholder="请选择场馆服务项目"
                            size="medium">
                            <el-option
                                v-for="item in serviceList"
                                :key="item.serviceId"
                                :label="item.serviceName"
                                :value="item.serviceId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </div>
        </cover-box>
    </div>
</template>

<script>
import * as API from '../utils/api'
import _ from 'lodash'
import pagination from 'vue-mixins/pagination'
import mAlert from 'mAlert'
import 'utils/confirm'
import hashParamsKit from 'utils/hashParamsKit'

export default{
    mixins: [pagination],
    data() {
        return {
            centerTag: true,
            agreementType: '',
            agreementTypeList: [],
            channelId: '',
            channelList: [],
            venueList: [],
            venueId: '',
            agreementName: '',
            coverAgreementType: '',
            coverVenueId: '',
            serviceTag: '0',
            coverServiceId: '',
            serviceList: [],
            tradeTypeCode: '',
            level: '',
            electronicSealFlag:'0'
        }
    },
    watch: {
        coverAgreementType(val) {
            const obj = _.find(this.agreementTypeList, { id: val }) || {}
            this.serviceTag = obj.serviceTag || '0'
            this.tradeTypeCode = obj.tradeTypeCode || ''
            this.level = obj.level || ''
            if (!this.centerTag || this.level === '1') {
                this.coverVenueId = null
                this.queryServiceListByVenueId()
            }
        },
        coverVenueId(val) {
            if (val) {
                this.queryServiceListByVenueId(val)
            }
        },
    },
    mounted() {
        this.initialHashQuery()
        this.queryCommonParam()
    },
    methods: {
        initialHashQuery() {
            const hashParam = hashParamsKit.getHashParams()
            if (hashParam.initialQuery) {
                this.pageNum = parseInt(hashParam.pageNo || 1)
                this.pageSize = parseInt(hashParam.pageSize || 10)
                this.venueId = hashParam.venueId
                this.agreementType = hashParam.agreementType
                this.channelId = hashParam.channelId
                this.agreementName = decodeURIComponent(hashParam.agreementName)
            }
            this.query()
        },
        toDetail(item, pageType) {
            const obj = {}
            if (pageType === 'add') {
                if (!this.coverAgreementType) {
                    mAlert.showWarning('请选择协议类型')
                    return
                }
                if ((this.centerTag && this.level === '2') && !this.coverVenueId) {
                    mAlert.showWarning('请选择场馆')
                    return
                }
                if (this.serviceTag === '1' && !this.coverServiceId) {
                    mAlert.showWarning('请选择服务')
                    return
                }
                obj.agreementType = this.coverAgreementType
                obj.agreementTypeName = _.find(this.agreementTypeList, { id: this.coverAgreementType }).name
                if (this.coverServiceId) {
                    obj.serviceId = this.coverServiceId
                    obj.serviceName = _.find(this.serviceList, { serviceId: this.coverServiceId }).serviceName
                }
                if (this.coverVenueId) {
                    obj.venueId = this.coverVenueId
                    obj.venueName = _.find(this.venueList, { venueId: this.coverVenueId }).venueName
                }
                obj.tradeTypeCode = this.tradeTypeCode
                obj.coverVenueId = this.coverVenueId
            }
            obj.agreementId = item.agreementId
            obj.pageType = pageType
            if (item.sealPicPath) {
                obj.sealPicPath = item.sealPicPath
            }
            this.$router.push({
                name: 'detail',
                query: obj,
            })
        },
        queryServiceListByVenueId() {
            API.queryServiceListByVenueId({
                venueId: this.coverVenueId,
            }).then((res) => {
                this.coverServiceId = ''
                this.serviceList = res.serviceList || []
            })
        },
        queryCommonParam() {
            API.queryCommonParam().then((res) => {
                this.centerTag = res.centerTag
                this.electronicSealFlag = res.electronicSealFlag;
                const initAgreementTypeObj = {
                    id: '',
                    name: '全部协议类型',
                }
                const agreementTypeList = res.agreementTypeList || []
                this.agreementTypeList = [initAgreementTypeObj, ...agreementTypeList]
                const initChannelObj = {
                    paramKey: '',
                    paramValue: '全部渠道',
                }
                const channelList = res.channelList || []
                this.channelList = [initChannelObj, ...channelList]
                const initVenueObj = {
                    venueId: '',
                    venueName: '全部场馆',
                }
                const venueList = res.venueList || []
                this.venueList = [initVenueObj, ...venueList]
            })
        },
        query() {
            API.queryAgreementList({
                pageNo: this.pageNum,
                pageSize: this.pageSize,
                venueId: this.venueId,
                agreementType: this.agreementType,
                channelId: this.channelId,
                agreementName: this.agreementName,
            }).then((res) => {
                this.setPageInfo(res.agreementList || {})
            })
        },
        showCoverBox() {
            this.$refs.agreement.show()
        },
        deleteProtocal(id) {
            this.$confirm('确认删除该规则？').then(() => {
                API.deleteProtocal({
                    agreementId: id,
                }).then(() => {
                    mAlert.showSuccess('删除成功')
                    this.query()
                })
            })
        },
        goToSealConfig(){
            this.$router.push({
                name: 'sealConfig',
                query: {},
            })
        }
    },
}
</script>
