package com.asiainfo.aisports.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.util.DesensitizedUtil;
import com.asiainfo.aisports.annotation.RoutingKey;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.cache.RedisKeyEnum;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.ProjectConstants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.RedisLockException;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.helper.ResultCode;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.project.*;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.service.api.CommonService;
import com.asiainfo.aisports.service.async.AsyncManager;
import com.asiainfo.aisports.service.async.AsyncTaskFactory;
import com.asiainfo.aisports.service.job.JobEnum;
import com.asiainfo.aisports.service.mq.CommonMessageService;
import com.asiainfo.aisports.service.mq.JobMessageService;
import com.asiainfo.aisports.service.syncdata.RepeatSyncException;
import com.asiainfo.aisports.service.trade.PayTrade;
import com.asiainfo.aisports.service.trade.TradePayResult;
import com.asiainfo.aisports.service.trade.TradePayService;
import com.asiainfo.aisports.service.trade.TradePayServiceFactory;
import com.asiainfo.aisports.tools.*;
import com.asiainfo.aisports.utils.RedisKeyGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.sf.json.JSONArray;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/11 10:51
 */
@Service
public class PerformNewTicketService {


    private static final Logger log = LoggerFactory.getLogger(PerformNewTicketService.class);


    private static final String PERFORM_TICKET_QUEUE = "performTicketQueue"; // 赛事票



    @Autowired
    private ProjectNewService projectNewService;


    @Autowired
    private PerformNewTicketMapper performNewTicketMapper;


    @Autowired
    private PerformMapper performMapper;



    @Autowired
    private TradeTicketMapper tradeTicketMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;


    @Autowired
    private RedisKeyGenerator redisKeyGenerator;

    @Autowired
    private RedisScript<Long> performTicketScript;


    @Autowired
    private RedisCommonService redisCommonService;

    @Autowired
    private PerformStockMapper performStockMapper;



    @Autowired
    private BufferIdGenService idGenService;
    @Autowired
    private TradeService tradeService;

    @Autowired
    ApiAppInfo apiAppInfo;

    @Autowired
    private TradeTypeService tradeTypeService;

    @Autowired
    private CommonMessageService commonMessageService;

    @Autowired
    private CenterService centerService;


    @Autowired
    private TradeTicketPersonMapper tradeTicketPersonMapper;
    @Autowired
    private TradeTicketAttrMapper tradeTicketAttrMapper;


    @Autowired
    private PerformRightsMapper performRightsMapper;

    @Autowired
    private TradeTicketRightsMapper tradeTicketRightsMapper;

    @Autowired
    private CommonService commonService;
    @Autowired
    private TradeExtraService tradeExtraService;

    @Autowired
    private PayService payService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private ProjectTradeTicketService projectTradeTicketService;

    @Autowired
    private TradeTicketAttrService tradeTicketAttrService;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private PerformAsyncLogMapper performAsyncLogMapper;

    @Autowired
    private JobMessageService jobMessageService;
    @Autowired
    NetTradeMapper netTradeMapper;
    @Autowired
    SequenceWrapper sequenceWrapper;

    @Autowired
    private PerformStockService performStockService;

    @Autowired
    private TradeCacheService tradeCacheService;


    public ServiceResult order(Long netUserId, String projectId, String performId, String performTicketId, Integer num,
                               String psptInfo, Long centerId, Long channelId, String ip) {


        ProjectVo projectVo = projectNewService.getProjectInfoById(projectId);
        if (projectVo == null) {
            return new ServiceResult(400, "活动火爆R201");
        }

        List<PerformVo> performList = projectNewService.getPerformList(projectId);
        if (CollectionUtils.isEmpty(performList)) {
            return new ServiceResult(400, "活动火爆R301");
        }
        PerformVo perform = performList.stream()
                .filter(p -> p.getId().equals(performId))
                .findFirst()
                .orElse(null);


        Date now = new Date();

        if (perform == null) {
            return new ServiceResult(1003, "比赛场次不存在");
        }

        PerformTicketVo performNewTicket = projectNewService.getPerformTicketDetail(performTicketId);

/*        List<PerformTicketVo> performNewTickets = perform.getTicketList();

        if (performNewTickets.isEmpty()) {
            return new ServiceResult(1003, "比赛场次没有可售票");
        }
        PerformTicketVo performNewTicket = getPerformNewTicket(performNewTickets, performTicketId);*/
        if (performNewTicket == null) {
            return new ServiceResult(1003, "比赛票不存在");
        }

        if (now.before(perform.getSaleStartDate()) || now.after(perform.getSaleEndDate())) {
            return new ServiceResult(1003, "比赛场次不在售票时间内");
        }

        if (DateCalUtil.cureDate().after(perform.getPerformDate())) {
            return new ServiceResult(1003, performNewTicket.getName() + "演出日期不能小于今天");
        }

        Set<Object> cacheGroupIdList = redisTemplate.opsForSet().members(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_USER_GROUP_ORDER, performId, netUserId));
        if (CollectionUtils.isNotEmpty(cacheGroupIdList)) {
            return new ServiceResult(1005, "您已下单套票");
        }


        String realFlag = projectVo.getRealFlag();


        Integer buyLimit = perform.getBuyLimit();
        if (num > buyLimit) {
            return new ServiceResult(1001, "购票数量已经达到购买上限");
        }

        Integer boughtNum = 0;

        Set<Object> cacheTicketIdList = redisTemplate.opsForSet().members(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_USER_ORDER, performId, netUserId));


        List<DataMap> boughtTicketList = tradeTicketMapper.selectBoughtTicket(netUserId, performId);
        for (DataMap bm : boughtTicketList) {
//            PerformTicketVo performTicket = getPerformNewTicket(performNewTickets, bm.getString("performTicketId"));
            PerformTicketVo performTicket = projectNewService.getPerformTicketDetail(bm.getString("performTicketId"));
            if (performTicket != null) {
                if (isGroupTicket(performTicket.getTicketKind())) {
                    return new ServiceResult(1001, "您已购买了套票");
                }
                boughtNum++;
            }
        }

        if (CollectionUtils.isNotEmpty(cacheTicketIdList)) {
            for (Object ti : cacheTicketIdList) {
                if (boughtTicketList.stream().noneMatch(b -> b.getString("ticketId").equals(ti.toString()))) {
                    boughtNum++;
                }
            }
        }

        if (boughtNum > 0 && isGroupTicket(performNewTicket.getTicketKind())) {
            return new ServiceResult(1001, "您已购买了单票，不能购买套票");
        }


        if ((boughtNum + num) > buyLimit) {
            return new ServiceResult(1001, "购票数量已经达到购买上限");
        }


        List<SignPsptInfo> signPsptInfos = new ArrayList<>();
        if (Constants.Tag.YES.equals(realFlag)) {
            // 校验身份证信息
            if (psptInfo == null || psptInfo.isEmpty()) {
                return new ServiceResult(2001, "请填写身份证信息");
            }

            signPsptInfos = formatPsptInfo(psptInfo);

            List<String> psptIdList = signPsptInfos.stream().map(SignPsptInfo::getId).distinct().collect(Collectors.toList());
            if (psptIdList.size() < signPsptInfos.size()) {
                return new ServiceResult(2002, "身份证信息不能重复");
            }
            int singleTicketNum = performNewTicket.getAdultNum() + performNewTicket.getMinorNum();
            if (signPsptInfos.size() != singleTicketNum * num) {
                return new ServiceResult(2003, "身份证信息数量不匹配，请检查");
            }
            if (performNewTicket.getMinorNum() > 0) {
                List<SignPsptInfo> minorPsptInfos = signPsptInfos.stream()
                        .filter(info -> "2".equals(info.getTag()))
                        .collect(Collectors.toList());
                if (minorPsptInfos.size() != performNewTicket.getMinorNum() * num) {
                    return new ServiceResult(2004, "未成年人身份证信息数量不匹配，请检查");
                }
            }

            if (ProjectConstants.TicketKind.AWAY.equals(performNewTicket.getTicketKind())) {
                String rule = performNewTicket.getRule();
                if (!signPsptInfos.stream().allMatch(info -> info.getId().startsWith(rule))) {
                    return new ServiceResult(2005, "不满足客场票的身份证规则，请检查");
                }
            }

            if (isOutTicket(performNewTicket.getTicketKind())) {
                String rule = performNewTicket.getRule();
                if (signPsptInfos.stream().anyMatch(info -> info.getId().startsWith(rule))) {
                    return new ServiceResult(2005, "不满足外地票的身份证规则，请检查");
                }
            }


            List<DataMap> psptIdTickectList = tradeTicketMapper.selectBoughtTicketByPsptId(psptIdList, performId);
            if (!psptIdTickectList.isEmpty()) {
                return new ServiceResult(2006, "【" + psptIdTickectList.get(0).getString("psptName") + "】已经购票/有待支付的订单。");
            }

            for (SignPsptInfo signPsptInfo : signPsptInfos) {
                Set<Object> psptOrder = redisTemplate.opsForSet().members(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PSPT_ORDER, performId, signPsptInfo.getId()));
                if (CollectionUtils.isNotEmpty(psptOrder)) {
                    return new ServiceResult(2007, "【" + signPsptInfo.getName() + "】已经购票/有待支付的订单。");
                }
            }

        }

        int deductNum = (performNewTicket.getAdultNum() + performNewTicket.getMinorNum()) * num;
        String stockKey = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_STOCK, performNewTicket.getStockId());
        Long orderNum = redisTemplate.execute(performTicketScript, Collections.singletonList(stockKey), deductNum);
        if (orderNum == null) {
            return new ServiceResult(101, "活动火爆，请稍后再试");
        }
        if (orderNum < 0) {
            String lockKey = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_STOCK_LOCK, performNewTicket.getStockId());
            ;
            String lockValue = UUID.randomUUID().toString();
            boolean lock = redisCommonService.lock(lockKey, lockValue, 5000);
            if (lock) {
                try {
                    orderNum = redisTemplate.execute(performTicketScript, Collections.singletonList(stockKey), deductNum);
                    if (orderNum < 0) {
                        PerformStock performStock = getTicketStock(performNewTicket.getStockId());

                        if (performStock.getRemainAmount() == null || performStock.getRemainAmount() < deductNum) {
                            return new ServiceResult(3, "活动库存不足啦");
                        }
                        //加载库存
                        redisTemplate.opsForHash().putIfAbsent(stockKey, "stock", performStock.getRemainAmount());
                        redisTemplate.opsForHash().putIfAbsent(stockKey, "order", 0);
                        return new ServiceResult(103, "活动火爆，再试试");
                    }

                } finally {
                    redisCommonService.unLock(lockKey, lockValue);
                }
            } else {
                return new ServiceResult(102, "活动火爆，请稍后再试2");
            }

        }

        if (orderNum == 0) {
            // 更新缓存信息
            jobMessageService.sendMessage(centerId, JobEnum.PERFORM_STOCK_UPDATE_JOB, buildUpdateCacheParam(projectId, performId, performNewTicket.getStockId(), deductNum));

            return new ServiceResult(2, "已经抢完啦，看看其他的票吧");
        }

        Long tradeId = idGenService.get("trade");

        String tradeCacheKey = redisKeyGenerator.generateKey(RedisKeyEnum.CACHE_TRADE, tradeId);
        TradeCache tc = buildTradeCacheInfo(projectVo, perform, performNewTicket, num, signPsptInfos, tradeId, netUserId, centerId, channelId);
        redisTemplate.opsForValue().set(tradeCacheKey, tc);

        List<TicketCache> tcs = tc.getTicketCaches();
        for (TicketCache ticketCache : tcs) {
            if (isGroupTicket(performNewTicket.getTicketKind())) {
                redisTemplate.opsForSet().add(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_USER_GROUP_ORDER, performId, netUserId), ticketCache.getTicketId());
            } else {
                redisTemplate.opsForSet().add(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_USER_ORDER, performId, netUserId), ticketCache.getTicketId());
            }

            List<TicketPsptInfo> psptInfos = ticketCache.getPsptInfos();
            if (CollectionUtils.isNotEmpty(psptInfos)) {
                for (TicketPsptInfo psptInfo1 : psptInfos) {
                    redisTemplate.opsForSet().add(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PSPT_ORDER, performId, psptInfo1.getId()), ticketCache.getTicketId());
                }
            }


        }

        commonMessageService.sendMessage(Constants.MQExchange.PERFORM_TICKET_EXCHANGE, PERFORM_TICKET_QUEUE, JacksonUtil.writeObj2Json(MapUtil.build("type", "trade", "data", tc, "centerId", centerId)));


        Trade trade = new Trade();
        trade.setTradeId(tc.getTradeId());
        trade.setTradeTypeCode(tc.getTradeTypeCode());
        trade.setExpireTime(DateCalUtil.convertDate(tc.getExpireTime(), "yyyy-MM-dd HH:mm:ss"));
        trade.setTradeDesc(projectVo.getName());
        trade.setRemark(buildTradeInfo(perform, performNewTicket, num, psptInfo));
        trade.setPayTfee(tc.getPrice());
        trade.setServiceId(0L);
        trade.setCenterId(centerId);
        trade.setVenueId(tc.getVenueId());
        trade.setBusinessType(Constants.BusinessType.SIMPLE_TICKET_SALE);
        trade.setSubscribeState(Constants.SubscribeState.UNCOMPLETED);
        trade.setPayState(Constants.PayState.UNPAID);
        trade.setCancelTag(Constants.CancelTag.NORMAL);
        trade.setAcceptDate(DateCalUtil.convertDate(tc.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        if (trade.getPayTfee() == 0) {
            trade.setSubscribeState(Constants.SubscribeState.COMPLETED);
            trade.setPayState(Constants.PayState.PAID);
            FinishCache finishCache = new FinishCache();
            finishCache.setTradeId(tradeId);
            commonMessageService.sendMessage(Constants.MQExchange.PERFORM_TICKET_EXCHANGE, PERFORM_TICKET_QUEUE, JacksonUtil.writeObj2Json(MapUtil.build("type", "finish", "data", finishCache, "centerId", centerId)));

        }

        AsyncManager.me().execute(AsyncTaskFactory.recordTradeIp(tradeId, ip, centerId));

        return new ServiceResult().set("tradeId", tradeId)
                .set("expireTime", DateCalUtil.convertDate(tc.getExpireTime(), "yyyy-MM-dd HH:mm:ss"))
                .set("stockId", performNewTicket.getStockId())
                .set("num", (performNewTicket.getAdultNum() + performNewTicket.getMinorNum()) * num)
                .set("trade", trade)
                .set("extraList", buildPlatProjectInfo(projectVo, perform, performNewTicket, num))
                .set("payState", trade.getPayTfee() == 0 ? Constants.PayState.PAID : Constants.PayState.UNPAID)
                .set("payTfee", tc.getPrice());
    }

    private Map buildPlatProjectInfo(ProjectVo projectVo, PerformVo perform, PerformTicketVo performNewTicket, Integer num) {
        Map<String, Object> map = new HashMap<>();
        map.put("projectName", projectVo.getName());
        map.put("image", projectVo.getImage());
        map.put("performName", perform.getName());
        map.put("performDate", perform.getPerformDate());
        map.put("startTime", perform.getStartTime());
        map.put("endTime", perform.getEndTime());
        map.put("performTicketName", performNewTicket.getName());
        map.put("stockName", performNewTicket.getStockName());
        map.put("siteName", performNewTicket.getSiteName());
        map.put("address", perform.getAddress());
        map.put("buyNum", num);
        return MapUtil.build("extraKey", "projectInfo", "value", map);


    }

    private Map<String, Object> buildUpdateCacheParam(String projectId, String performId, Long stockId, int deductNum) {
        return MapUtil.build("projectId", projectId, "performId", performId, "stockId", stockId, "num", deductNum);
    }



    private static boolean isOutTicket(String ticketType) {
        return ProjectConstants.TicketKind.OUT_GROUP.equals(ticketType) || ProjectConstants.TicketKind.OUT_ADULT.equals(ticketType) || ProjectConstants.TicketKind.OUT_MINOR.equals(ticketType);
    }

    private boolean isGroupTicket(String ticketKind) {
        return ProjectConstants.TicketKind.GROUP.equals(ticketKind) || ProjectConstants.TicketKind.OUT_GROUP.equals(ticketKind);

    }

    private TradeCache buildTradeCacheInfo(ProjectVo project, PerformVo perform, PerformTicketVo performTicket, Integer num, List<SignPsptInfo> psptInfo, Long tradeId, Long netUserId, Long centerId, Long channelId) {

        TradeCache tc = new TradeCache();
        tc.setTradeId(tradeId);
        tc.setTradeTypeCode(TradeConstants.TradeTypeCode.BUY_NEW_PERFORM_TICKET.getLongValue());
        tc.setNetUserId(netUserId);
        tc.setCenterId(centerId);
        tc.setChannelId(channelId);
        tc.setCreateTime(DateCalUtil.date2String(new Date(), "yyyy-MM-dd HH:mm:ss"));
        if (apiAppInfo != null) {
            tc.setChannelAppId(apiAppInfo.getAppId());
        }
        tc.setProjectId(project.getId());
        tc.setProjectName(project.getName());
        tc.setPerformId(perform.getId());
        tc.setPerformName(perform.getName());
        tc.setPerformDate(DateCalUtil.date2String(perform.getPerformDate(), "yyyy.MM.dd"));
        tc.setStartTime(perform.getStartTime());
        tc.setEndTime(perform.getEndTime());
        tc.setSiteName(performTicket.getSiteName());
        tc.setStockName(performTicket.getStockName());
        tc.setImage(project.getImage());
        tc.setPerformTicketId(performTicket.getId());
        tc.setPerformTicketName(performTicket.getName());
        tc.setPrice(performTicket.getPrice() * num);
        tc.setTicketCaches(buildTicketCaches(performTicket, num, psptInfo, centerId));
        TradeType tradeType = tradeTypeService.getTradeTypeByTradeTypeId(TradeConstants.TradeTypeCode.BUY_NEW_PERFORM_TICKET.getLongValue());
        if (tradeType == null) {
            throw new ServiceException("业务类型未配置", -1, ResultCode.BUSINESS_TYPE_NOT_CONFIG);
        }
        tc.setVenueId(performTicket.getVenueId());
        // 如果订单有效期未设置，则设置订单有效期
        tc.setExpireTime(DateCalUtil.date2String(DateCalUtil.getDateWithOffset(new Date(), tradeType.getValidPeriod()), "yyyy-MM-dd HH:mm:ss"));
        tc.setAddress(perform.getAddress());


        return tc;
    }

    private List<TicketCache> buildTicketCaches(PerformTicketVo performTicket, Integer num, List<SignPsptInfo> psptInfo, Long centerId) {
        List<TicketCache> tcList = new ArrayList<>();

        Center center = centerService.findCenterByCenterId(centerId);

        List<List<TicketPsptInfo>> psptPartition = allocatePspt(num, psptInfo, performTicket);
        for (int i = 0; i < num; i++) {
            TicketCache tc = new TicketCache();
            tc.setPrice(performTicket.getPrice());
            tc.setTicketId(idGenService.get("ticket"));
            tc.setTicketNo(TicketUtils.geneTicketNo(center.getTenantId(), tc.getTicketId()));
            tc.setTicketTypeId(performTicket.getTicketTypeId());
            if (!psptPartition.isEmpty()) {
                tc.setPsptInfos(psptPartition.get(i));
            }
            tcList.add(tc);
        }

        return tcList;
    }

    private List<List<TicketPsptInfo>> allocatePspt(Integer num, List<SignPsptInfo> psptInfo, PerformTicketVo performTicket) {
        if (psptInfo.isEmpty()) {
            return new ArrayList<>();
        }
        List<List<TicketPsptInfo>> pll = new ArrayList<>();
        for (int i = 0; i < num; i++) {
            pll.add(new ArrayList<>());
        }

        if (isGroupTicket(performTicket.getTicketKind())) {
            Map<String, List<SignPsptInfo>> pml = psptInfo.stream().collect(Collectors.groupingBy(SignPsptInfo::getTag));
            for (Map.Entry<String, List<SignPsptInfo>> stringListEntry : pml.entrySet()) {
                List<SignPsptInfo> signPsptInfos = stringListEntry.getValue();
                List<List<SignPsptInfo>> partition = Lists.partition(signPsptInfos, calculatePartitionNum(stringListEntry.getKey(), performTicket));
                for (int i = 0; i < num; i++) {
                    List<SignPsptInfo> sdList = partition.get(i);
                    pll.get(i).addAll(convertPsptInfo(sdList));

                }

            }
        } else {
            for (int i = 0; i < psptInfo.size(); i++) {
                pll.get(i).addAll(convertPsptInfo(Collections.singletonList(psptInfo.get(i))));

            }


        }


        return pll;

    }

    private int calculatePartitionNum(String key, PerformTicketVo performTicket) {

        if (ProjectConstants.PersonType.ADULT.equals(key)) {
            return performTicket.getAdultNum();
        }
        return performTicket.getMinorNum();
    }

    private List<TicketPsptInfo> convertPsptInfo(List<SignPsptInfo> sdList) {
        List<TicketPsptInfo> list = new ArrayList<>();
        for (SignPsptInfo signPsptInfo : sdList) {
            TicketPsptInfo ticketPsptInfo = new TicketPsptInfo();
            ticketPsptInfo.setType(signPsptInfo.getType());
            ticketPsptInfo.setId(signPsptInfo.getId());
            ticketPsptInfo.setName(signPsptInfo.getName());
            ticketPsptInfo.setTag(signPsptInfo.getTag());
            ticketPsptInfo.setMobileNum(signPsptInfo.getMobileNum());
            list.add(ticketPsptInfo);
        }
        return list;
    }

    private String buildTradeInfo(PerformVo perform, PerformTicketVo performNewTicket, Integer num, String psptInfo) {
        return /*DateCalUtil.date2String(perform.getPerformDate(), "yyyy.MM.dd")
                + " " + DateCalUtil.convertTime(perform.getStartTime(), null)
                + " " +*/ perform.getName() + "-" + performNewTicket.getName() + "-" + performNewTicket.getStockName();
    }

    private long calculateEndMinutes(Perform perform) {

        Date saleEndDate = perform.getSaleEndDate();
        if (DateCalUtil.isSameDay(new Date(), saleEndDate)) {
            long period = DateCalUtil.minutesBetween(new Date(), saleEndDate);
            if (period < 0) {
                return 2;
            }
            return period;
        } else {
            // 如果不是同一天，返回当天剩余的秒数
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(saleEndDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            return DateCalUtil.minutesBetween(new Date(), calendar.getTime());
        }
    }

    private PerformStock getTicketStock(Long stockId) {
        PerformStock performStock = performStockMapper.selectByPrimaryKey(stockId);
        return performStock;
    }

    private  List<SignPsptInfo> formatPsptInfo(String psptInfo) {

        List<SignPsptInfo> signPsptInfos = new ArrayList<>();
        String[] psptArray = psptInfo.split(",");
        for (String pspt : psptArray) {
            String[] psptDetails = pspt.split(":");
            if (psptDetails.length == 5) {
                SignPsptInfo signPsptInfo = new SignPsptInfo(psptDetails[0], psptDetails[1], psptDetails[2], psptDetails[3], psptDetails[4]);
                signPsptInfos.add(signPsptInfo);
            } else {
                log.warn("Invalid passport info format: {}", pspt);
            }
        }
        return signPsptInfos;



    }


    private PerformTicketVo getPerformNewTicket(List<PerformTicketVo> ticketList, String performTicketId) {
        return ticketList.stream().filter(p -> p.getId().equals(performTicketId))
                .findFirst()
                .orElse(null);

    }




    @Transactional
    @TargetDataSource(name = "{}")
    public TradePayResult finish(FinishCache data, @RoutingKey Long centerId) {
        Long tradeId = data.getTradeId();
        Trade trade = tradeService.getTradeByTradeId(tradeId);
        if (trade == null) {
            log.warn("finish Trade not found for tradeId: {}", tradeId);
            TradeCache tc = (TradeCache) redisTemplate.opsForValue().get(redisKeyGenerator.generateKey(RedisKeyEnum.CACHE_TRADE, tradeId));
            if (tc != null) {
                projectTradeTicketService.saveTrade(tc, centerId);
            }
        }
        trade = tradeService.getTradeByTradeId(tradeId);
        String attachStr = data.getAttach();
        DataMap attach = JacksonUtil.readJson2Object(attachStr, DataMap.class);

        Map<String, String> tradeExtra = tradeExtraService.getTradeExtra(tradeId);


        //app不传工号
        LoginStaff staff = commonService.getLoginStaff(null, trade.getChannelId(), centerId, trade.getVenueId());;


        Map<String, Object> params = Maps.newHashMap();


        TradePayService tradePayService =
                TradePayServiceFactory.createTradePayService(applicationContext, trade.getTradeTypeCode());
        if (tradePayService == null) {
            throw new ServiceException("无法处理的支付类型");
        }

        TradePayResult tradePayResult;
        if (trade.getPayTfee() == 0) {
            tradePayResult = tradePayService.payZero(trade, staff);
        } else {
            PayTrade payTrade = tradePayService.createPayTrade(trade, JSONArray.fromObject(tradeExtra.get(Constants.Extra.PAY_GROUP)));
            payTrade.setManualReduction(0);
            payTrade.setDiscount(0); // 设置订单折扣
            payTrade.setPremiumAmount(0); // 设置订单折扣
            tradePayResult = tradePayService.pay(payTrade, null, staff);
            if (tradePayResult.getError() != 0) {
                return tradePayResult;

            }

            tradePayResult = tradePayService.finishTrade(trade);

        }

        return tradePayResult;



    }

    public List<DataMap> getBoughtTicketList(Long netUserId, Long centerId) {
       return performNewTicketMapper.selectBoughtList(netUserId);
    }

    public DataMap getTicketInfo(Long ticketId) {

        DataMap ticketInfo = performNewTicketMapper.getTicketInfo(ticketId);
        ticketInfo.set("rightsList", getTicketRightList(ticketId));
        ticketInfo.set("personList", getPersonList(ticketId));
        return ticketInfo;


    }

    private List<DataMap> getPersonList(Long ticketId) {

        TradeTicketPerson param = new TradeTicketPerson();
        param.setTicketId(ticketId);
        List<TradeTicketPerson> personList = tradeTicketPersonMapper.selectByFields(param);
        List<DataMap> dataList = new ArrayList<>();
        for (TradeTicketPerson ttp : personList) {
            DataMap map = new DataMap();
            map.put("name", ttp.getName());
            map.put("pspstId", DesensitizedUtil.idCardNum(ttp.getPsptId(), 3, 4));
            map.put("psptType", ttp.getPsptType());
            map.put("tag", ttp.getTag());
            dataList.add(map);

        }

        return dataList;


    }

    private List<DataMap> getTicketRightList(Long ticketId) {
        return tradeTicketRightsMapper.selectRightList(ticketId);
    }

    public void returnStock(Long stockId, int num, Long centerId) {
        String stockKey = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_STOCK, stockId);

        if (redisTemplate.hasKey(stockKey)) {
            redisTemplate.opsForHash().increment(stockKey, "order", -num);
        }

        jobMessageService.sendMessage(centerId, JobEnum.PERFORM_STOCK_SHOW_JOB, MapUtil.build("stockId", stockId, "num", num));


    }

    public DataMap buildTradeProjectInfo(Trade trade, List<TradeTicket> tradeTickets) {


        TradeTicket tradeTicket = tradeTickets.get(0);
        TradeTicketAttr attr = tradeTicketAttrService.findByKey(tradeTicket.getTicketId(), Constants.TradeTicketAttr.PERFORM_TICKET_ID);
        String performTicketId = attr.getAttrValue();

        return performNewTicketMapper.selectOrderInfoById(performTicketId);


    }


    public ServiceResult  ticketQrCode(Long ticketId, Long centerId) {

        String content = ticketId.toString() + System.currentTimeMillis();
        String qrCode = DigestUtils.md5Hex(content).substring(8, 24);

        String validPeriod = "15s";
        Object valid = redisTemplate.opsForValue().get(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_TICKET_VALID_PERIOD, centerId));
        if (valid != null) {
            validPeriod = valid.toString();
        }

        Date now = new Date();
        Date expireDate = DateCalUtil.getDateWithOffset(now, validPeriod);
        int seconds = DateCalUtil.secondsBetween(now, expireDate);
        redisTemplate.opsForValue().set(redisKeyGenerator.generateKey(RedisKeyEnum.TICKET_QRCODE, qrCode), ticketId, seconds, TimeUnit.SECONDS);

        return new ServiceResult().set("qrCode", "FT/" + qrCode)
                .set("expireDate", expireDate)
                .set("validDesc", DateCalUtil.getNameWithOffset(validPeriod))
                .set("seconds", seconds);

    }

    public void reloadmessage(Long id) {
        PerformAsyncLog log = performAsyncLogMapper.selectByPrimaryKey(id);
        if (log == null) {
            throw new ServiceException("未找到记录");
        }
        if (log != null) {
            commonMessageService.sendMessage(Constants.MQExchange.PERFORM_TICKET_EXCHANGE, PERFORM_TICKET_QUEUE, log.getContent());
        }
    }

    public void cancelPerformNewTicket(Long ticketId, String performTicketId) {
        PerformNewTicket performNewTicket = performNewTicketMapper.selectByPrimaryKey(performTicketId);
        if(performNewTicket == null) {
            return;
        }
        tradeTicketPersonMapper.updateByTicketId(ticketId, Constants.TicketState.CANCELED);

        int num = performNewTicket.getAdultNum() + performNewTicket.getMinorNum();
//        returnStock(performNewTicket.getStockId(), num);
        performStockMapper.updateRemain(performNewTicket.getStockId(), num);

    }

    public DataMap buildTradeProjectInfo(TradeCache tc) {
        DataMap map = new DataMap();
        map.put("projectId", tc.getProjectId());
        map.put("image", tc.getImage());
        map.put("performDate", tc.getPerformDate());
        map.put("startTime", tc.getStartTime());
        map.put("endTime", tc.getEndTime());
        map.put("projectName", tc.getProjectName());
        map.put("performName", tc.getPerformName());
        map.put("stockName", tc.getStockName());
        map.put("siteName", tc.getSiteName());
        map.put("performTicketName", tc.getPerformTicketName());
        map.put("address", tc.getAddress());
        map.put("buyNum", tc.getTicketCaches().size());
        return map;


    }

    public List<DataMap> buildTicketList(TradeCache tc) {
        List<TicketCache> ticketCaches = tc.getTicketCaches();
        List<DataMap> tickets = Lists.newArrayList();
        for (TicketCache ticketCache : ticketCaches) {
            DataMap temp = new DataMap();
            BeanUtils.copyProperties(ticketCache, temp);
            temp.put("ticketId", ticketCache.getTicketId());
            temp.put("ticketNo", ticketCache.getTicketNo());
            temp.put("startTime", tc.getStartTime());
            temp.put("endTime", tc.getStartTime());
            temp.put("payMoney", tc.getPrice());
            temp.put("ticketTypeName", tc.getPerformTicketName());
            tickets.add(temp);
        }
        return tickets;
    }

    @Transactional
    @TargetDataSource(name = "{}")
    public void deductStock(Long tradeId, @RoutingKey Long centerId) {

        Trade trade = tradeService.getTradeByTradeId(tradeId);
        if (trade == null) {
            log.error("<deductStock>tradeId={} not found", tradeId);
            return;
        }

        if (!Constants.SubscribeState.COMPLETED.equals(trade.getSubscribeState())) {
            log.error("<deductStock>tradeId={} not finish", tradeId);
            throw new ServiceException("<deductStock>tradeId={} not finish");
        }

        // 扣除库存
        List<TradeTicket> ticketList = tradeTicketMapper.selectByTradeId(tradeId);
        TradeTicket tone = ticketList.get(0);
        String performTicketId = Optional.ofNullable(tradeTicketAttrService.findByKey(tone.getTicketId(), Constants.TradeTicketAttr.PERFORM_TICKET_ID))
                .map(TradeTicketAttr::getAttrValue).orElseThrow(() -> new ServiceException("未查询到赛事票信息"));
        PerformNewTicket performNewTicket = performNewTicketMapper.selectByPrimaryKey(performTicketId);
        int reduceNum = (performNewTicket.getAdultNum() + performNewTicket.getMinorNum()) * ticketList.size();

        String lockKey = "project:perform:stock:lock:" + performNewTicket.getStockId();
        boolean lock = redisCommonService.tryLock(lockKey, tradeId.toString(), 1000, 100, 1000);
        if (!lock) {
            throw new RedisLockException("lock failed");
        }
        try {
            performStockService.updateRemain(performNewTicket.getStockId(), reduceNum);

        } finally {
            redisCommonService.unLock(lockKey, tradeId.toString());
        }



    }

    public void tradeExpired(JSONObject params, Long centerId) {
        Long tradeId = params.getLong("tradeId");
        TradeCache cache = tradeCacheService.getCacheById(tradeId);
        if (cache != null) {
            cache.getTicketCaches().forEach(ticketCache -> {
                redisTemplate.opsForSet().remove(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_USER_ORDER, cache.getPerformId(), cache.getNetUserId()), ticketCache.getTicketId());
                redisTemplate.opsForSet().remove(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_USER_GROUP_ORDER, cache.getPerformId(), cache.getNetUserId()), ticketCache.getTicketId());

                ticketCache.getPsptInfos().forEach(psptInfo -> {
                    redisTemplate.opsForSet().remove(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PSPT_ORDER, cache.getPerformId(), psptInfo.getId()), ticketCache.getTicketId());

                });
            });
        }

        returnStock(MapUtils.getLong(params, "stockId"), MapUtils.getInteger(params, "num"), centerId);


    }

    @Transactional(rollbackFor = Exception.class)
    public ServiceResult bind(Long netUserId, String ticketId, String psptInfo, Long centerId, Long channelId) {
        ServiceResult serviceResult = new ServiceResult();
        // 校验身份证信息
        if (Strings.isNullOrEmpty(psptInfo)) {
            return new ServiceResult(2001, "请填写身份证信息");
        }
        DataMap teamTicketInfo = tradeTicketMapper.selectTeamTicketInfo(Long.valueOf(ticketId));
        if (teamTicketInfo == null) {
            return new ServiceResult(2002, "票不存在");
        }
        TradeTicketPerson param = new TradeTicketPerson();
        param.setTicketId(Long.valueOf(ticketId));
        List<TradeTicketPerson> ticketPersonList = tradeTicketPersonMapper.selectByFields(param);
        if (!Constants.TicketState.UNBIND.equals(teamTicketInfo.getString("state")) || CollUtil.isNotEmpty(ticketPersonList)) {
            return new ServiceResult(2003, "票已被绑定");
        }
        Date now = new Date();
        String[] psptDetails = psptInfo.split(":");
        if (psptDetails.length == 5) {
            SignPsptInfo signPsptInfo = new SignPsptInfo(psptDetails[0], psptDetails[1], psptDetails[2], psptDetails[3], psptDetails[4]);
            List<DataMap> psptIdTickectList = tradeTicketMapper.selectBoughtTicketByPsptId(Lists.newArrayList(signPsptInfo.getId()), teamTicketInfo.getString("performId"));
            if (!psptIdTickectList.isEmpty()) {
                return new ServiceResult(2006, "【" + psptIdTickectList.get(0).getString("psptName") + "】已经购票/有待支付的订单。");
            }
            TradeTicketPerson tradeTicketPerson = new TradeTicketPerson();
            tradeTicketPerson.setId(idGenService.get("trade_ticket_person"));
            tradeTicketPerson.setTicketId(Long.valueOf(ticketId));
            tradeTicketPerson.setPsptType(signPsptInfo.getType());
            tradeTicketPerson.setPsptId(signPsptInfo.getId());
            tradeTicketPerson.setName(signPsptInfo.getName());
            tradeTicketPerson.setMobileNum(signPsptInfo.getMobileNum());
            tradeTicketPerson.setTag(signPsptInfo.getTag());
            tradeTicketPerson.setState(Constants.TicketState.FETCHED);
            tradeTicketPerson.setCreateTime(now);
            tradeTicketPersonMapper.insert(tradeTicketPerson);
            NetTrade netTrade = new NetTrade();
            netTrade.setTradeId(teamTicketInfo.getLong("tradeId"));
            netTrade.setNetUserId(netUserId);
            netTrade.setCreateTime(now);
            netTradeMapper.insert(netTrade);
            TradeTicket ticket = new TradeTicket();
            ticket.setTicketId(Long.valueOf(ticketId));
            ticket.setState(Constants.TicketState.FETCHED);
            ticket.setFetchTicketTime(now);
            tradeTicketMapper.updateByPrimaryKeySelective(ticket);
            //权益
            tradeTicketRightsMapper.updateByTradeId(teamTicketInfo.getLong("tradeId"), ProjectConstants.RightsState.VALID);
        } else {
            log.warn("Invalid passport info format: {}", psptInfo);
        }
        return serviceResult;
    }

    public DataMap getTeamTicketInfo(Long ticketId) {
        return performNewTicketMapper.getTicketInfo(ticketId);
    }


    static class SignPsptInfo {
        private String type;
        private String id;
        private String name;
        /**
         * 1-成人
         * 2-儿童
         */
        private String tag;

        private String mobileNum;

        public SignPsptInfo(String type, String id, String name, String birthDay,String mobileNum) {
            this.type = type;
            this.id = id;
            this.name = name;
            if( birthDay != null && !birthDay.isEmpty()) {
                this.tag = DateCalUtil.isAdult(birthDay) ? "1" : "2"; // 根据生日判断成人或儿童
            } else {
                this.tag = "1"; // 默认成人
            }
            this.mobileNum = mobileNum;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public String getMobileNum() {
            return mobileNum;
        }

        public void setMobileNum(String mobileNum) {
            this.mobileNum = mobileNum;
        }
    }
}
