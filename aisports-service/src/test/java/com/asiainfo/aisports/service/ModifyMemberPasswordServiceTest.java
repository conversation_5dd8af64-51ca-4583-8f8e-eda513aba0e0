
package com.asiainfo.aisports.service;


import com.asiainfo.aisports.TestApplicationContext;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.TradeTicketTypeName;
import com.asiainfo.aisports.model.CustInfo;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.pay.client.Result;
import com.asiainfo.aisports.persistence.core.TradeTicketMapper;
import org.apache.ibatis.session.RowBounds;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ModifyMemberPasswordServiceTest extends TestApplicationContext {
    @Autowired
    ModifyMemberPasswordService modifyMemberPasswordService;
    @Autowired
    CustQueryService custQueryService;

    @Test
    public void testModifyPassword(){
        String ecardNo = "8000000000043";
        String password = "123";
        String reason = "0987654321oiuytrewq";
        LoginStaff staff = new LoginStaff();
        staff.setVenueId(10000003L);
        staff.setStaffId(29L);
        staff.setCenterId(1000000L);
        CustInfo custInfo = custQueryService.findByEcardNo(ecardNo,staff.getCenterId());
        if (custInfo ==null){
            System.out.print("#######"+"没了"+"%%%%%%%%%%%%%");
        }else {
            Result result = modifyMemberPasswordService.submit(staff, custInfo, password, reason,"", 1L, "");
            System.out.print("#######"+result+"%%%%%%%%%%%%%");
        }
//        int num = modifyMemberPasswordService.submit(staff,custInfo,password,reason);


        //$10$T9B9SOYVCPvtXuRREq0In.u.74QfmiBB5bYO.ne8Vm0CmyFJ/7pFG
        //$2a$10$FWfdT6gEY3oeaCQ/HVBNBOV3wbOPuC3FeI7A.xVPxvTm7hfOPFq/e   123456
        //$2a$10$d7z2uNbNpBpOFf2pnr2Arus27E0Csa6PrOQcYAyeNB5sElyEdjeKq
    }

}
