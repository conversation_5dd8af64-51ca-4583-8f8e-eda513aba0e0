package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class NetTrade {
    private Long tradeId;

    private Long netUserId;

    private Date createTime;

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    public Long getNetUserId() {
        return netUserId;
    }

    public void setNetUserId(Long netUserId) {
        this.netUserId = netUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", tradeId=").append(tradeId);
        sb.append(", netUserId=").append(netUserId);
        sb.append(", createTime=").append(createTime);
        sb.append("]");
        return sb.toString();
    }
}