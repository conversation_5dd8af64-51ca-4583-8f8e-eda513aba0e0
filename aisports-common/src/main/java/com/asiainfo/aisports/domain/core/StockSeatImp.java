package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class StockSeatImp {
    private Long id;

    private Long batchId;

    private Long stockId;

    private String stockName;

    private Integer row;

    private Integer startSeat;

    private Integer endSeat;

    private String invalidSeat;

    private Integer num;

    private String distType;

    private String state;

    private Date createTime;

    private Long createStaffId;

    private Date updateTime;

    private Long updateStaffId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBatchId() {
        return batchId;
    }

    public void setBatchId(Long batchId) {
        this.batchId = batchId;
    }

    public Long getStockId() {
        return stockId;
    }

    public void setStockId(Long stockId) {
        this.stockId = stockId;
    }

    public String getStockName() {
        return stockName;
    }

    public void setStockName(String stockName) {
        this.stockName = stockName == null ? null : stockName.trim();
    }

    public Integer getRow() {
        return row;
    }

    public void setRow(Integer row) {
        this.row = row;
    }

    public Integer getStartSeat() {
        return startSeat;
    }

    public void setStartSeat(Integer startSeat) {
        this.startSeat = startSeat;
    }

    public Integer getEndSeat() {
        return endSeat;
    }

    public void setEndSeat(Integer endSeat) {
        this.endSeat = endSeat;
    }

    public String getInvalidSeat() {
        return invalidSeat;
    }

    public void setInvalidSeat(String invalidSeat) {
        this.invalidSeat = invalidSeat == null ? null : invalidSeat.trim();
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getDistType() {
        return distType;
    }

    public void setDistType(String distType) {
        this.distType = distType == null ? null : distType.trim();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", batchId=").append(batchId);
        sb.append(", stockId=").append(stockId);
        sb.append(", stockName=").append(stockName);
        sb.append(", row=").append(row);
        sb.append(", startSeat=").append(startSeat);
        sb.append(", endSeat=").append(endSeat);
        sb.append(", invalidSeat=").append(invalidSeat);
        sb.append(", num=").append(num);
        sb.append(", distType=").append(distType);
        sb.append(", state=").append(state);
        sb.append(", createTime=").append(createTime);
        sb.append(", createStaffId=").append(createStaffId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append("]");
        return sb.toString();
    }
}