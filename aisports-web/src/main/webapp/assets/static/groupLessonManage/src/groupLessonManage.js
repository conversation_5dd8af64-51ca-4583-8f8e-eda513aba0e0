import Vue from 'vue'
import 'frame'
import Router from 'vue-router'
import routes from './components/routes'
import { queryPlanList } from './components/service'
import EleMultiCascader from "vue-components/EleMultiCascader/src/index"

// 可动态加载的级联选择器
Vue.use(EleMultiCascader)
Vue.use(Router)
const router = new Router({ routes })
window.vueRouter = router
window.hasPermission = $('#deal-tag').val() === '1';
window.planTag = $('#plan-tag').val() === '1';
new Vue({
    el: '#grouping-manage',
    data: {
        planList: [],
    },
    mounted() {
        if (window.planTag) {
            queryPlanList().then((data) => {
                this.planList = data.planList
            })
        }
    },
    template: '<router-view></router-view>',
    router,
})
