package com.asiainfo.aisports.scheduler.service.humanResource;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.SalaryCalcExprParam;
import com.asiainfo.aisports.scheduler.service.humanResource.functions.IfFunction;
import com.asiainfo.aisports.scheduler.service.humanResource.resolver.VariableResolver;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.StringUtils;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * aviator执行器
 *
 * @auther: zhouwei
 * @date: 2022/3/8 17:54
 */
@Component
public class AviatorHandler implements ApplicationContextAware {

    private ApplicationContext applicationContext;
    private static final String SUFFIX = "VariableResolver";
    // v2计算公式的参数字符前缀，如 a20220122 *IF( a20220122<100,100,120)+ a20220121*IF(a20220121 <50,80,100)
    private static final String EXPR_PARAM_PREFIX_V2 = "a";
    private final Logger logger = LoggerFactory.getLogger(AviatorHandler.class);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
        AviatorEvaluator.addFunction(new IfFunction());
    }

    /**
     * 执行计算公式
     *
     * @param expression 公式
     * @param vars       resolver使用到的相关业务参数
     * @param cached     是否缓存expression编译结果
     * @return 单位为分的薪资
     */
    public Long exec(String expression, Map<String, Object> vars, boolean cached) {
        logger.debug("------>aviator exec start:\nexpression:{}\nvars:{}\ncached:{}", expression, vars, cached);
        Expression compiledExpression = validate(expression, cached);
        List<String> variableNames = compiledExpression.getVariableNames();
        Map<String, Object> env = new HashMap<>(variableNames.size());
        for (String resolverName : variableNames) {
            // 校验参数是否合法
            String matchName = getResolverByName(resolverName);
            if (!applicationContext.containsBean(matchName)) {
                throw new IllegalArgumentException("cannot resolve this variable: " + resolverName);
            }
            Object value = applicationContext.getBean(matchName, VariableResolver.class).resolve(vars);
            if (value != null) {
                env.put(resolverName, value);
            }
        }
        logger.debug("------>aviator exec end:\nexpression:{}\nenv:{}\nvars:{}", expression, env, vars);
        Object result = compiledExpression.execute(env);
        if (result instanceof Long) {
            return (Long) result;
        }
        return ((Double) result).longValue();
    }


    /**
     * 执行表达式
     *
     * @param expression    计算公式
     * @param varsMap       计算公式中的参数映射表 SalaryCalcExprParam.id : 中文参数名
     * @param exprParams    计算公式中的参数详情 SalaryCalcExprParam.id: SalaryCalcExprParam
     * @param cached        是否缓存 false-v2默认不缓存因为公式不定
     * @return
     */
    public Double execV2(String expression, Map<String, Object> vars, Map<String, Object> varsMap, Map<Long, SalaryCalcExprParam> exprParams, boolean cached) {
        logger.debug("------>aviator exec v2 start:\nexpression:{}\nvars:{}\nexprParams:{}\ncached:{}", expression, varsMap, exprParams, cached);
        Expression compiledExpression = validate(expression, cached);
        List<String> variableNames = compiledExpression.getVariableNames();
        Map<String, Object> env = new HashMap<>(variableNames.size());
        // 判断计算方式
        String payRoll = MapUtils.getString(vars, "payRoll");
        Double resultValue = 0D;
        Date salaryStartDate = (Date) MapUtils.getObject(vars, "salaryStartDate");
        Date salaryEndDate = (Date) MapUtils.getObject(vars, "salaryEndDate");
        if (Constants.SalaryTemplateCustomItemConstants.DAY_BASED_PAY_ROLL.equals(payRoll)) {
            Date currentDate = salaryStartDate;
            while (!currentDate.after(salaryEndDate)) {
                vars.put("salaryStartDate", currentDate);
                vars.put("salaryEndDate", DateCalUtil.addDays(currentDate, 1));
                resultValue += execOnce(expression, compiledExpression, env, vars, exprParams);
                currentDate = DateCalUtil.addDays(currentDate, 1);
            }
        } else {
            vars.put("salaryEndDate", DateCalUtil.addDays(salaryEndDate, 1));
            resultValue += execOnce(expression, compiledExpression, env, vars, exprParams);
        }
        return resultValue;
    }

    private Double execOnce(String expression, Expression compiledExpression, Map<String, Object> env,
                          Map<String, Object> vars, Map<Long, SalaryCalcExprParam> exprParams) {
        for (String resolverName : compiledExpression.getVariableNames()) {
            // 校验参数是否合法
            if (StringUtils.isEmpty(resolverName)) {
                throw new IllegalArgumentException("cannot find target variable resolver: empty variable");
            }
            Long actualVariableId = Long.valueOf(resolverName.substring(EXPR_PARAM_PREFIX_V2.length()));
            if (!exprParams.containsKey(actualVariableId)) {
                throw new IllegalArgumentException("cannot find target variable resolver: " + resolverName);
            }
            SalaryCalcExprParam salaryCalcExprParam = exprParams.get(actualVariableId);
            vars.put("exprParams", salaryCalcExprParam);
            Object value = applicationContext.getBean(getResolverV2(salaryCalcExprParam.getType()), VariableResolver.class).resolve(vars);
            if (value != null) {
                env.put(resolverName, value);
            }
        }
        Object result = compiledExpression.execute(env);
        logger.debug("------>aviator exec v2 end:\nexpression:{}\nvars:{}\nexprParams:{}\nresult:{}", expression, vars, exprParams, result);
        return ((Double) result);
    }

    private String getResolverByName(String variable) {
        if (StringUtils.isEmpty(variable)) {
            throw new IllegalArgumentException("cannot find target variable resolver: empty variable");
        }
        return variable + SUFFIX;
    }

    private String getResolverV2(String exprParamType) {
        if (StringUtils.isEmpty(exprParamType)) {
            throw new IllegalArgumentException("cannot find target variable resolver: empty variable");
        }
        switch (exprParamType) {
            case Constants.SalaryCalcExprParamType.CONSULTANT_SALE:
                return "consultantSaleVariableResolver";
            case Constants.SalaryCalcExprParamType.COACH_COURSE:
                return "coachAttendCourseVariableResolver";
            case Constants.SalaryCalcExprParamType.PERFORMANCE:
                return "performanceTargetVariableResolver";
            case Constants.SalaryCalcExprParamType.GENERIC_PERFORMANCE:
                return "genericPerformanceVariableResolver";
            default:
                throw new IllegalArgumentException("cannot find target variable resolver:" + exprParamType);
        }
    }

    public Expression validate(String expression, boolean cached) {
        try {
            AviatorEvaluator.validate(expression);
            return AviatorEvaluator.compile(expression, cached);
        } catch (Exception e) {
            throw new RuntimeException("公式校验失败!");
        }
    }
}