package com.asiainfo.aisports.scheduler.service.custRemind;

import com.asiainfo.aisports.domain.core.CustReminderRule;
import com.asiainfo.aisports.scheduler.TestApplicationContext;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CustRemind04ServiceTest extends TestApplicationContext {

    @Autowired
    private CustRemind04Service custRemind04Service;

    @Test
    public void generateCustRemind() {

        CustReminderRule custReminderRule = new CustReminderRule();
        custReminderRule.setContent("{\"time_amount\":\"1\",\"time_unit\":\"M\",\"limit_type\":\"0,1,2\"}");
        custReminderRule.setNoticeType("4");
        custReminderRule.setVenueId(10000002L);
        custReminderRule.setRuleId("11111111");
        custReminderRule.setReminderType("04");
        custRemind04Service.generateCustRemind(custReminderRule);
    }
}