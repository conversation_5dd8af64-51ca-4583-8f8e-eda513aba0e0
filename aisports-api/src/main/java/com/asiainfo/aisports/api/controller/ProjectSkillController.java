package com.asiainfo.aisports.api.controller;

import com.asiainfo.aisports.annotation.IncludeRepeat;
import com.asiainfo.aisports.annotation.RateLimit;
import com.asiainfo.aisports.annotation.RepeatSubmit;
import com.asiainfo.aisports.api.web.WebResult;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.service.*;
import com.asiainfo.aisports.service.mq.ExpTradeMessageService;
import net.sf.json.JSONObject;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/project/skill")
@Validated
public class ProjectSkillController {
    @Autowired
    ProjectNewService projectNewService;

    @Autowired
    ExpTradeMessageService delayedMessageService;


    @Autowired
    PerformNewTicketService performNewTicketService;



    /**
     * 获取票务活动列表
     *
     * @param centerId
     * @return
     */
    @RequestMapping("/getProjectList")
    public WebResult getProjectList(@RequestParam("centerId") Long centerId) {
        return new WebResult().set("projectList", projectNewService.getProjectList(centerId));
    }

    /**
     * 获取票务活动
     *
     * @param projectId
     * @return
     */
    @RequestMapping("/getProjectInfo")
    public WebResult getProjectInfo(@RequestParam("projectId") String projectId) {
        return new WebResult().set("project", projectNewService.getProjectInfoById(projectId));
    }

    /**
     * 获得赛事活动场次
     *
     * @param projectId
     * @return
     */
    @RequestMapping("/getPerformList")
    public WebResult getPerformList(@RequestParam("projectId") String projectId) {
        return new WebResult().set("performList", projectNewService.getPerformList(projectId));
    }

    @RequestMapping("/getPerformDetail")
    public WebResult getPerformDetail(@RequestParam("performId") String performId) {
        return new WebResult().set("performList", projectNewService.getPerformDetail(performId));
    }


    @RequestMapping("/getStockTicketList")
    public WebResult getStockTicketList(@RequestParam("stockId") Long stockId) {
        return new WebResult().set("ticketList", projectNewService.getStockTicketList(stockId));
    }

    /**
     * 获取票的权益信息
     *
     * @param performTicketId
     * @return
     */
    @RequestMapping("/getTicketRightsList")
    public WebResult getTicketRightsList(@RequestParam("performTicketId") String performTicketId) {
        return new WebResult().set("rightsList", projectNewService.getTicketRightsList(performTicketId));
    }


    /**
     * 进行下单
     *
     * @param netUserId
     * @param centerId
     * @param channelId
     * @return
     */
    @RateLimit(permitsPerSecond = 1000)
    @RepeatSubmit(onlyInclude = true, message = "请勿重复提交订单")
    @RequestMapping("/performTicketOrder")
    public WebResult performTicketOrder(@IncludeRepeat @NotNull @RequestParam(value = "netUserId") Long netUserId,
                                        @NotBlank String performTicketId,
                                        @NotBlank String projectId,
                                        @NotBlank String performId,
                                        @NotNull Integer num,
                                        String psptInfo,
                                        String ip,
                                        @RequestParam(value = "centerId") Long centerId,
                                        @RequestParam(value = "channelId") Long channelId) {
        WebResult webResult = new WebResult();


        ServiceResult serviceResult = performNewTicketService.order(netUserId, projectId, performId, performTicketId, num, psptInfo, centerId, channelId, ip);

        // 把订单放入超时订单队列
        Date expireTime = (Date) serviceResult.get("expireTime");
        if (expireTime != null) {
            Long interval = expireTime.getTime() - new Date().getTime();
            JSONObject message = new JSONObject();
            message.put("tradeId", serviceResult.get("tradeId"));
            message.put("projectId", serviceResult.get("projectId"));
            message.put("performId", serviceResult.get("performId"));
            message.put("stockId", serviceResult.get("stockId"));
            message.put("num", serviceResult.get("num"));
            message.put("centerId", centerId);
            delayedMessageService.sendMessage(message.toString(), interval.intValue());
        }

        webResult.putAll(serviceResult);
        return webResult;
    }

    @RequestMapping("/getTicketList")
    public WebResult getTicketList(@NotNull Long netUserId, Long centerId) {

        List<DataMap> ticketList = performNewTicketService.getBoughtTicketList(netUserId, centerId);
        return new WebResult().set("ticketList", ticketList);
    }


    @RequestMapping("/getTicketInfo")
    public WebResult getTicketInfo(@NotNull Long ticketId, Long centerId) {
        DataMap ticketInfo = performNewTicketService.getTicketInfo(ticketId);
        return new WebResult().set("ticketInfo", ticketInfo);

    }


    @RequestMapping("/getTicketQrCode")
    public WebResult ticketQrCode(Long ticketId, Long centerId) {
        if (ticketId == null) {
            return new WebResult(1, "ticketId缺失");
        }
        ServiceResult result = performNewTicketService.ticketQrCode(ticketId, centerId);
        return new WebResult().setAll(result);
    }

    @RateLimit(permitsPerSecond = 1000)
    @RepeatSubmit(onlyInclude = true, message = "请勿重复绑定")
    @RequestMapping("/bindPerformTicket")
    public WebResult bindPerformTicket(@IncludeRepeat @NotNull @RequestParam(value = "netUserId") Long netUserId,
                                       @IncludeRepeat @NotBlank String ticketId,
                                        String psptInfo,
                                        @RequestParam(value = "centerId") Long centerId,
                                        @RequestParam(value = "channelId") Long channelId) {
        WebResult webResult = new WebResult();


        ServiceResult serviceResult = performNewTicketService.bind(netUserId, ticketId, psptInfo, centerId, channelId);
        webResult.putAll(serviceResult);
        return webResult;
    }

    @RequestMapping("/getTeamTicketInfo")
    public WebResult getTeamTicketInfo(@NotNull Long ticketId, Long centerId) {
        return new WebResult().set("ticketInfo", performNewTicketService.getTeamTicketInfo(ticketId));
    }

}
