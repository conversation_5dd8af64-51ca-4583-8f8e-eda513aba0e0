package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.SalaryCalcRecord;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;

public interface SalaryCalcRecordMapper {
    @Delete({
        "delete from salary_calc_record",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into salary_calc_record (id, salary_month, ",
        "state, error_msg, center_id, ",
        "create_time, create_staff_id, ",
        "update_time, update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{salaryMonth,jdbcType=VARCHAR}, ",
        "#{state,jdbcType=CHAR}, #{errorMsg,jdbcType=VARCHAR}, #{centerId,jdbcType=BIGINT}, ",
        "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(SalaryCalcRecord record);

    @Select({
        "select",
        "id, salary_month, state, error_msg, center_id, create_time, create_staff_id, ",
        "update_time, update_staff_id",
        "from salary_calc_record",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="salary_month", property="salaryMonth", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="error_msg", property="errorMsg", jdbcType=JdbcType.VARCHAR),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    SalaryCalcRecord selectByPrimaryKey(Long id);

    @Update({
        "update salary_calc_record",
        "set salary_month = #{salaryMonth,jdbcType=VARCHAR},",
          "state = #{state,jdbcType=CHAR},",
          "error_msg = #{errorMsg,jdbcType=VARCHAR},",
          "center_id = #{centerId,jdbcType=BIGINT},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(SalaryCalcRecord record);

    @Select({
        "<script>",
        "select",
        "id, salary_month, state, error_msg, center_id, create_time, create_staff_id, update_time, update_staff_id",
        "from salary_calc_record",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"salaryMonth != null\">and salary_month = #{salaryMonth,jdbcType=VARCHAR}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"errorMsg != null\">and error_msg = #{errorMsg,jdbcType=VARCHAR}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="salary_month", property="salaryMonth", jdbcType=JdbcType.VARCHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="error_msg", property="errorMsg", jdbcType=JdbcType.VARCHAR),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<SalaryCalcRecord> selectByFields(SalaryCalcRecord param);

    @Update({
        "<script>",
        "update salary_calc_record",
        "<set>",
        "<if test=\"salaryMonth != null\">salary_month = #{salaryMonth,jdbcType=VARCHAR},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"errorMsg != null\">error_msg = #{errorMsg,jdbcType=VARCHAR},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(SalaryCalcRecord record);

    @Select({
            "<script>",
            "select a.id as salary_record_id, a.salary_month, a.state, a.error_msg, a.center_id, a.create_time, a.create_staff_id, a.update_time, a.update_staff_id,",
            "(select s.staff_name from staff_salary_calc_record b, staff s where a.id = b.salary_calc_record_id and b.staff_id = s.staff_id and b.state = '1' limit 1) as first_staff,",
            "(select count(*) from staff_salary_calc_record b where a.id = b.salary_calc_record_id and b.state = '1') as amount",
            "from salary_calc_record a",
            "<where>",
            "a.center_id = #{centerId, jdbcType=BIGINT} and a.state != '3'",
            "<if test=\"calcStartTime != null \"> and a.create_time &gt;= #{calcStartTime, jdbcType=TIMESTAMP}</if>",
            "<if test=\"calcEndTime != null \"> and a.create_time &lt;= #{calcEndTime, jdbcType=TIMESTAMP}</if>",
            "</where>",
            "order by a.id desc",
            "</script>",
    })
    @Results({
            @Result(column="salary_record_id", property="salaryRecordId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="salary_month", property="salaryMonth", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
            @Result(column="error_msg", property="errorMsg", jdbcType=JdbcType.VARCHAR),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="first_staff", property="firstStaff", jdbcType=JdbcType.BIGINT),
            @Result(column="amount", property="amount", jdbcType=JdbcType.BIGINT)
    })
    List<DataMap> salaryCalcRecordList(@Param("calcStartTime") Date calcStartTime, @Param("calcEndTime") Date calcEndTime,
                                       @Param("centerId") Long centerId, RowBounds rowBounds);
}