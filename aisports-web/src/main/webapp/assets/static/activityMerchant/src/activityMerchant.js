import Vue from 'vue'
import $ from 'jquery'
import 'frame'
import VueRouter from 'vue-router'
import routes from './router'
// import '../../common/element-theme/index.css'
// import store from './vuex/store'
import {Select, Option, Input, Button, Table,Tabs, TabPane, TableColumn, Pagination, Form, FormItem, Dialog, Message,Cascader,
  CascaderPanel, Upload, DatePicker, MessageBox, Autocomplete, Radio, RadioGroup, Checkbox, CheckboxButton, CheckboxGroup, Divider,} from 'element-ui-2.15.5'
// import Viewer from 'v-viewer'
// import 'viewerjs/dist/viewer.css'

Vue.prototype.$message = Message;
// Vue.prototype.$dispatch = (...value) => store.dispatch(...value);
// Vue.prototype.$commit = (...value) => store.commit(...value);
Vue.use(VueRouter)
Vue.use(Select);
Vue.use(Option);
Vue.use(Input);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Button);
Vue.use(Table);
Vue.use(Tabs);
Vue.use(TabPane);
Vue.use(TableColumn);
Vue.use(Pagination);
Vue.use(Dialog);
Vue.use(DatePicker);
Vue.use(Upload);
Vue.use(Cascader)
Vue.use(CascaderPanel)
Vue.use(Autocomplete);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(Checkbox);
Vue.use(CheckboxButton);
Vue.use(CheckboxGroup);
Vue.use(Divider);

Vue.prototype.$msgbox = MessageBox;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$confirm = MessageBox.confirm;
Vue.prototype.$prompt = MessageBox.prompt;
// Viewer.setDefaults({
//   Options: { "inline": true, "button": true, "navbar": true, "title": true, "toolbar": true, "tooltip": true, "movable": true, "zoomable": true, "rotatable": true, "scalable": true, "transition": true, "fullscreen": true, "keyboard": true, "url": "data-source" }
// });

const router = new VueRouter({
  routes,
})

window.vueRouter = router

new Vue({
  el: '#activityMerchant',
  router,
})
