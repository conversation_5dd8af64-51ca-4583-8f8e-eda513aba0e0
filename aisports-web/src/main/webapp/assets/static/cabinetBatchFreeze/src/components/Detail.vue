<template>
    <div class="new-cabinet">
        <header class="header">
            <div>新增批量冻柜</div>
            <div class="back" @click="back()"><i class="iconfont icon-zu<PERSON>ji<PERSON><PERSON>"></i></div>
        </header>
        <main>
            <aside>
                <section class="search">
                    <el-input placeholder="输入柜子名称/楼层/柜子号等关键词搜索" v-model="searchinput">
                        <el-button @click="showFreezeCabinet()" slot="append" icon="el-icon-search"></el-button>
                    </el-input>
                    <div class="flex-between"><span>本馆柜子资源共{{ cabinetTotalNum }}项</span> <el-button @click="checkAllBox"
                            type="text">全选</el-button>
                    </div>
                </section>
                <section>
                    <div class="selectType" v-for="(item, index) in cabinetAreaList">
                        <el-checkbox :indeterminate="item.isIndeterminate" v-model="item.checkAll"
                            @change="handleCheckAllChange($event, index)">{{
                                item.cabinetArea.areaName }}</el-checkbox>
                        <el-checkbox-group v-model="checkedCities[index]" @change="handleCheckedCitiesChange">
                            <el-checkbox v-for="(floor, index2) in item.cabinetInfo" :label="index + '-' + index2"
                                :key="index2">{{ floor.cabinetLayer.layerNum }}层</el-checkbox>
                        </el-checkbox-group>
                    </div>
                </section>
            </aside>
            <section class="form">
                <div v-if="checkedFloorsBox.length">已选择{{ boxListlength }}项目</div>
                <div v-if="checkedFloorsBox.length" class="content">
                    <div :class="['box', item.isChecked ? 'active' : '']" @click="selectBox(item, index)"
                        v-for="(item, index) in boxList" :key="index">{{
                            item.cabinetNo || '' }}</div>
                </div>
                <div class="time-note">
                    <div class="date">
                        <el-date-picker v-model="startTime" value-format="yyyy-MM-dd" format="yyyy-MM-dd" type="date"
                            placeholder="选择日期">
                        </el-date-picker>
                        <el-select v-model="selMethod" placeholder="请选择">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                            </el-option>
                        </el-select>
                        <el-date-picker v-if="isSelect" value-format="yyyy-MM-dd" format="yyyy-MM-dd" v-model="endTime"
                            type="date" placeholder="选择日期">
                        </el-date-picker>
                        <el-input-number v-else  class="inputDay" :min="1" :max="1000" controls-position="right" v-model="freezeDay"  placeholder="请输入冻卡天数"></el-input-number>
                        <!-- <el-input-number v-model="num"  @change="handleChange" ></el-input-number> -->
                    </div>
                    <div class="remarks">
                        <el-input v-model="notes" placeholder="备注"></el-input>
                    </div>
                    <!-- <span class="time">冻柜时间：</span>
                    <el-date-picker v-model="selectdate" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                    <span class="note">备注：</span>
                    <el-input v-model="notes"></el-input> -->
                </div>
            </section>
        </main>
        <footer>
            <div><el-button @click="submit" type="primary">提 交</el-button></div>
        </footer>
    </div>
</template>

<script>
import * as API from './API'
import mAlert from 'mAlert'
import dayjs from "dayjs";
// import { Select, Option, DatePicker, Switch } from 'element-ui'
import moment from 'moment'
import _ from 'lodash'



export default {
    components: {
    },
    computed: {
    },
    watch: {
        selMethod(newVal, oldVal) {
            console.log(123)
            if (newVal !== oldVal) {
                this.isSelect = newVal == 1 ? true : false;
            }
        },
        endTime(newEnd, oldEnd) {
            var nEnd = new Date(newEnd);
            var startDate = new Date(this.startTime);
            if (nEnd.getTime() < startDate.getTime()) {
                this.startTime = this.endTime;
            }
            // this.findDepositNumAll()
        },
        startTime(newstrat, oldstrat){
            let startDate = new Date(newstrat)
            if (this.freezeDay === '') {
                    this.endTime = dayjs(startDate).add('30', 'd').format('YYYY-MM-DD')
                    
                } else {
                    this.endTime = dayjs(startDate).add(this.freezeDay, 'd').format('YYYY-MM-DD')
                }
                console.log(this.endTime)
        },
        freezeDay: {
            handler(val) {
                let startDate = new Date(this.startTime);
                if (val === '') {
                    this.endTime = dayjs(startDate).add('30', 'd').format('YYYY-MM-DD')
                    
                } else {
                    this.endTime = dayjs(startDate).add(val, 'd').format('YYYY-MM-DD')
                }
                console.log(this.endTime)
            },
            immediate: true
        }
    },
    data() {
        return {
            boxList: [],
            searchinput: '',
            boxListlength: 0,
            cabinetNum: 0,
            selectdate: '',
            notes: '',
            cabinetSum: 0,
            checkAll: false,
            checkedCities: [],
            isIndeterminate: true,
            cabinetNos: [], // 柜子号
            checkedFloorsBox: [],
            cabinetAreaList: [],
            options: [
                {
                    label: "冻柜天数",
                    value: "0",
                },
                {
                    label: "冻柜到",
                    value: "1",
                },
            ],
            startTime: "",
            selMethod: "0",
            endTime: "",
            freezeDay: 1,
            isSelect: false,
            cabinetTotalNum: '0', // 柜子总数
            pickerOptions: {
                disabledDate: this.disabledDate
            }
        }
    },
    mounted() {
        this.showFreezeCabinet()
        this.initDate()
    },
    methods: {
        back() {
            window.history.back()
        },
        disabledDate(date) {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // 将时间调整为 00:00:00
            // return date.getTime() < today.getTime();
            console.log(date, 'test-date')
            return false
        },
        selectBox(item, index) {
            console.log('点击了', item)
            if (item.isChecked) {
                this.boxList[index].isChecked = false
                console.log(this.boxList[index], index)
            } else {
                this.boxList[index].isChecked = true
                console.log(this.boxList[index], index)
            }
            this.boxListlength = this.boxList.filter(item => item.isChecked).map(item => item.cabinetId).length
        },
        initDate() {
            var date = new Date();
            this.startTime = dayjs(date).format("YYYY-MM-DD");
            console.log(this.startTime, 'this.startTime');
        },
        showFreezeCabinet() {
            API.showFreezeCabinet({ keyNum: this.searchinput }, (res) => {
                let length = res.cabinetAreaList.length
                this.cabinetTotalNum = res.cabinetTotalNum || 0
                this.checkedCities = new Array(length).fill([]);
                this.checkedFloorsBox = new Array(length).fill([]);
                // let sum = res.cabinetAreaList.reduce((total, obj) => total + obj.item.list.length, 0);
                // this.cabinetNum = res.cabinetAreaList.reduce((total, obj) => {
                //     let cabinetSum = obj.cabinetInfo.reduce((acc, cabinet) => acc + cabinet.cabinetList.length, 0);
                //     return total + cabinetSum;
                // }, 0);
                this.cabinetAreaList = res.cabinetAreaList.map((item, index) => {
                    item.isIndeterminate = false;
                    item.checkAll = false;
                    return item
                })
                this.initCheckedFloorsBox()
            })
            console.log(this.boxList, '123456')
        },
        initCheckedFloorsBox() {
            this.cabinetAreaList.forEach((item, index) => {
                this.checkedFloorsBox[index] = item.cabinetInfo.length ? new Array(item.cabinetInfo.length).fill([]) : [];
            })
        },
        checkAllBox() {
            this.checkAll = !this.checkAll
            this.checkedFloorsBox.forEach((item, index) => {
                this.cabinetAreaList[index].checkAll = this.checkAll
                this.handleCheckAllChange(this.checkAll, index)
            })
        },
        handleCheckAllChange(val, index) {
            console.log(val, index)
            let length = this.checkedFloorsBox[index].length
            let floorList = []
            for (let i = 0; i < length; i++) {
                floorList.push(index + '-' + i);
            }
            this.checkedCities[index] = val ? floorList : [];
            this.handleCheckedCitiesChange()
            this.cabinetAreaList[index].isIndeterminate = false;
        },
        handleCheckedCitiesChange() {
            // console.log(value,this.checkedCities)
            let list = this.checkedCities.flat(2) // 拍平被选择的楼层
            this.initCheckedFloorsBox() // 把柜子号初始化为空。
            // 赋值给对应的柜子
            list.forEach((item) => {
                if (item) {
                    let [index1, index2] = item.split('-')
                    console.log(index1, index2)
                    // this.checkedFloorsBox[index1] = []
                    this.checkedFloorsBox[index1][index2] = this.cabinetAreaList[index1].cabinetInfo[index2].cabinetList.map((item) => {

                        return { isChecked: true, cabinetId: item.cabinetId, cabinetNo: item.cabinetNo }
                    })
                    // let checkedCount = value.length;
                }
            })
            console.log(this.checkedFloorsBox)
            this.boxList = this.checkedFloorsBox.flat(3)
            this.boxListlength = this.boxList.filter(item => item.isChecked).map(item => item.cabinetId).length
            // console.log(this.boxList, '123456')
            // this.checkAll = checkedCount === this.cities.length;
            // this.cabinetAreaList[].isIndeterminate = checkedCount > 0 && checkedCount < this.cities.length;
        },
        submit() {
            if (this.startTime === undefined || this.startTime === '') {
                console.log('startTime 字段为空');
                return mAlert.showWarning('请填写开始时间')
            }
            if (this.endTime === undefined || this.endTime === '') {
                console.log('endTime 字段为空');
                return mAlert.showWarning('请填写结束时间')

            }
            if (this.boxList === undefined || this.boxList.length === 0) {
                console.log('boxList 字段为空');
                return mAlert.showWarning('请选择柜子')

            }
            if (this.notes === undefined || this.notes === '') {
                console.log('notes 字段为空');
                return mAlert.showWarning('请填写备注')

            }
            let cabinetIdList = this.boxList.filter(item => item.isChecked).map(item => item.cabinetId)
            let cabinetNos = cabinetIdList.length == 1 ? cabinetIdList[0] : cabinetIdList.join(',');
            let data = {
                startDate: this.startTime,
                endDate: this.endTime,
                cabinetNos: cabinetNos,
                remark: this.notes
            }
            console.log(data, '是不是list')
            this.$confirm(`您申请冻柜延期, 确认提交吗?`, '确认操作', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                API.submit(data, (res) => {
                    if (res.error == 0) {
                        mAlert.showSuccess('批量冻柜申请成功')
                    } else {
                        mAlert.showWrong(res.message)
                    }
                })
            })
        },

    },
}
</script>
<style lang="scss" scoped>
.new-cabinet {
    height: calc(100vh - 80px);
    position: relative;
}

.flex-between {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;

    span {
        height: 40px;
        line-height: 40px;
    }
}

header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    font-size: 20px;
    font-weight: 400;
    margin: 10px 0px;
    padding: 0 20px;
    background-color: #fafafa;

    .back {
        height: 50px;
        width: 50px;
        border-radius: 50%;
        border: 1px solid #ccc;
        display: flex;
        justify-content: center;
        align-items: center;

        i {
            font-size: 24px;
        }
    }
}

main {
    display: flex;
    height: calc(100% - 140px);
    margin: 20px;

    aside {
        width: 370px;
        height: 100%;
        border: 1px solid #ccc;
        border-right: none;
        padding: 10px;
        overflow: auto;

        // background-color: pink;
        .selectType {
            .el-checkbox-group {
                display: flex;
                flex-flow: column nowrap;

                .el-checkbox {
                    margin: 0;
                    height: 30px;
                    padding: 5px 20px 5px 20px;

                    &:hover {
                        background-color: #bac7d6;
                    }
                }
            }
        }
    }

    section.form {
        flex: 1;
        // display: flex;
        // flex-flow: row wrap;
        // align-content: flex-start;
        padding: 10px;
        height: 100%;
        border: 1px solid #ccc;
        position: relative;

        .content {
            height: calc(100% - 120px);
            overflow: auto;
            display: flex;
            flex-flow: row wrap;
            align-content: flex-start;
        }

        .box {
            width: 100px;
            height: 100px;
            border-radius: 5px;
            background-color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 10px 20px;
            border: 2px solid #ccc;
            cursor: pointer;

        }

        .active {
            background-color: #dbead9;
        }

        .time-note {
            position: absolute;
            width: 100%;
            padding: 10px 30px;
            bottom: 0;
            align-items: center;

            .date {
                display: flex;

                div {
                    margin-right: 10px;
                }

                .inputDay {
                    width: 200px;
                }
            }

            .remarks {
                margin: 10px 0;
            }
        }
    }
}

footer {
    position: absolute;
    height: 60px;
    padding: 0 20px;
    background-color: #fafafa;
    width: 100%;
    display: flex;
    bottom: 0;
    align-items: center;
    justify-content: end;

}
</style>