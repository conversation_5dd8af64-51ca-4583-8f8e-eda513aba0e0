/**
 * checkbox 和radio button
 */

 define(function(require){
 	return function($){
 		var handleRadio = function(){
	        $("input[type='radio']").on("change", function(){
	        	var iptName = $(this).attr("name");
	        	var ipt = $("input[name='" + iptName + "']:checked");
	            ipt.closest(".box-radio").find(".on").removeClass("on");
	            ipt.closest(".box-radio-item").addClass("on");
	        });
	    };
	    var handleControlToggle = function(){
	    	$("[data-slide-toggle]").each(function(){
	    		var _this = $(this),
	    			controlName = $(this).attr("name"),
	    			targetId = _this.data("slide-toggle"),
	    			controlSelector = "[data-slide-toggle-tg=" + targetId + "]";

	    		$("[name=" + controlName + "]").on("click", function(){
	    			if(_this[0].checked){
	    				$(controlSelector).slideDown();
	    			}else{
	    				$(controlSelector).slideUp();
	    			}
	    		});
	    		$("[name=" + controlName + "]").trigger("change");
	    	});
	    };
	    $(function(){
	        handleRadio();
	        handleControlToggle();
	    });
 	};
 	
 });