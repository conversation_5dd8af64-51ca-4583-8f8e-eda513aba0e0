package com.asiainfo.aisports.service.job;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/30.
 */
public enum JobEnum {
    BATCH_DELAY_CARD_JOB("batchDelayCardJob"), // 批量验卡
    BATCH_DO_CARD_JOB("batchDoCardJob"), // 批量办卡
    BATCH_RECHARGE_JOB("batchRechargeJob"), // 批量充值
    BATCH_SEND_MESSAGE_JOB("batchSendMessageJob"),  //批量发短信
    BATCH_COURSE_ENROLL_JOB("batchCourseEnrollJob"), //批量培训报名
    BATCH_COURSE_ENROLL_NEW_JOB("batchCourseEnrollNewJob"), //批量培训报名(新)
    BATCH_PRIVATE_SIGN_JOB("batchPrivateSignJob"), //批量私教报名
    SYNC_STAFF_JOB("syncStaffJob"),
    SYNC_ROLE_JOB("syncRoleJob"),
    SYNC_ROLE_MENU_JOB("syncRoleMenuJob"),
    SYNC_VENUE_JOB("syncVenueJob"),
    SYNC_CENTER_JOB("syncCenterJob"),
    SYNC_STAFF_ROLE_JOB("syncStaffRoleJob"),
    SYNC_SMS_SEND_LOG_JOB("syncSmsSendLogJob"),
    SYNC_SYS_INVOICE_LOG_JOB("syncSysInvoiceLogJob"),
    THIRD_PAY_QUERY_JOB("thirdPayQueryJob"),
    THIRD_CANCEL_QUERY_JOB("thirdCancelQueryJob"),// 三方退款查询
    SYNC_VENUE_PARAM_JOB("syncVenueParamJob"),
    BATCH_IMPORT_VISITOR("batchImportVisitorJob"),
    ALI_PUSH_VERIFY_CODE_JOB("aliPushVerifyCodeJob"),
    ALI_PUSH_CARD_INFO_JOB("aliPushCardInfoJob"),
    ALI_PUSH_REFUND_RESULT_JOB("aliPushRefundResultJob"),
    ALI_PUSH_REFUND_NOTIFY_JOB("aliPushRefundNotifyJob"),
    CUST_FACE_JOB("custFaceJob"),
    PUSH_WECHAT_MESSAGE_JOB("pushWechatMessageJob"), //推送微信模板
    EXPORT_TO_WORD_JOB("exportToWordJob"), //导出到word
    BATCH_ISSUE_COUPON("batchIssueCouponJob"),//批量发放优惠券
    BATCH_ISSUE_COUPON_BY_ROLE("batchIssueCouponByRoleJob"),//根据角色批量发放优惠券
    OPEN_API_NOTICE_REFUND_TICKET_JOB("openApiNoticeRefundTicketJob"), //退票审核通知
    OPEN_API_PUSH_TICKET_STATE_JOB("openApiPushTicketStateJob"), // 票状态使用通知
    CASH_PLEDGE_REFUND_JOB("cashPledgeRefundJob"), //押金退款
    NOTICE_RELEASE_CABINET_JOB("noticeReleaseCabinetJob"), //释放柜子通知
    GET_POINTS_JOB("getPointsJob"),//获取积分队列
    AUTO_EXIT_GATE_JOB("autoExitGateJob"),//自动出馆队列
    GAME_AUTO_CANCEL_JOB("gameAutoCancelJob"),//自动取消约赛队列
    CREATE_INVOICE_JOB("createInvoiceJob"),//开票队列
    RESOURCE_CONSUME_NOTICE_JOB("resourceConsumeNoticeJob"),
    DEPOSIT_CONSUME_NOTICE_JOB("depositConsumeNoticeJob"),
    CARD_RECYCLE_JOB("cardRecycleJob"),//卡回收队列
    REMOVE_FILE_JOB("removeFileJob"), // 删除文件
    MAIL_NOTICE_JOB("mailNoticeJob"), //邮件通知队列
    SYSTEM_WARNING_JOB("systemWarningJob"), //系统警报队列
    TRADE_FINISH_ORDER_JOB("tradeFinishOrderJob"),//订单完工通知全名健身平台同步数据
    MINI_APP_NOTICE_JOB("miniAppNoticeJob"),//小程序订阅消息通知队列
    UNBINDCARD_NOTICE_JOB("unbindCardNoticeJob"),//解绑一卡通通知
    CANCEL_CAMP_NOTICE_JOB("cancelCampNoticeJob"),//活动报名取消通知
    CAMP_AUDIT_NOTICE_JOB("campAuditNoticeJob"),//活动报名审核
    PING_YANG_ORDER_POSITION_SYNC_JOB("pingYangOrderPositionSyncJob"),//平阳全民健身平台同步正向订单消息队列
    PING_YANG_ORDER_REVERSE_SYNC_JOB("pingYangOrderReverseSyncJob"),//平阳全民健身平台同步逆向订单消息队列
    PING_YANG_ORDER_BY_TIMES_SYNC_JOB("pingYangOrderByTimesSyncJob"),//平阳全民健身平台同步扣次订单消息队列
    ALI_PUSH_PRODUCT_STATE_JOB("aliPushProductStateJob"),//对接阿里体育-场馆产品状态推送消息队列
    PING_YANG_DEPOSIT_SYNC_JOB("pingYangDepositSyncJob"),//平阳全民健身平台同步押金消息队列
    SERIES_TICKET_EXIT_HALL_JOB("seriesTicketExitHallJob"),//连续票批量出馆队列
    LIGHT_CONTROL_JOB("lightControlJob"),//场地灯控制的消息队列
    LIGHT_CONTROL_COMMAND_JOB("lightControlCommandJob"),//场地灯控制命令重发的消息队列
    QDGX_TODO_PUSH_JOB("qdgxTODOPushJob"),//国信待办事项推送的消息队列
    TNCI_PUSH_JOB("tnciPushJob"),//国信对接天时同城-异步推送信息的消息队列
    EXIT_AUTO_GATE_WITH_CASHPLEDGE_JOB("exitAutoGateWithCashpledgeJob"),//出馆自动结算超时费的处理
    PING_YANG_SYNC_ADMIN_JOB("pingYangSyncAdminJob"),//平阳中心同步管理员数据到园秀系统
    CONTRACT_FILE_JOB("contractFileJob"),//处理“大家签”合同文件，将下载链接里的文件传到oss上
    GXT_EARN_POINTS_JOB("gxtEarnPointsJob"),//国信通积分通积服务
    GXT_REVERT_EARN_POINTS_JOB("gxtRevertEarnPointsJob"),//国信通积分通积撤销服务
    GXT_REDUCE_POINTS_JOB("gxtReducePointsJob"),//国信通积分通兑服务
    GXT_REVERT_REDUCE_POINTS_JOB("gxtRevertReducePointsJob"),//国信通积分通兑撤销服务
    GROUP_PURCHASE_END_TRADE_JOB("groupPurchaseEndTradeJob"),//拼团结束订单
    GROUP_PURCHASE_ROBOT_JOB("groupPurchaseRobotJob"),//拼团自动添加订单
    BARGAIN_ROBOT_JOB("bargainRobotJob"),//砍价自动砍完任务
    REJECT_BATCH_FREEZE_CARD_JOB("rejectBatchFreezeCardJob"),//自动拒绝批量冻卡申请
    SIMPLE_TICKET_LIGHT_CONTROL_JOB("simpleTicketLightControlJob"),//散客票（次票）灯控
    REMOVE_LIMIT_LIST_JOB("removeLimitListJob"),//删除黑名单用户
    ZHE_JIANG_PUBLIC_VENUES_DATA_META_COLLECTION_JOB("zheJiangPublicVenuesDataMetaCollectionJob"),//浙江公共体育馆数据元采集
    FIELD_SOLD_OUT_NOTICE_JOB("fieldSoldOutNoticeJob"),//通知平台下架场地
    TICKET_TYPE_SOLD_OUT_NOTICE_JOB("ticketTypeSoldOutNoticeJob"),//通知平台下架次票
    BRACELET_FETCH_LOG_RESOLVER_JOB("braceletFetchLogResolverJob"),//手环机日志处理任务
    AUTO_DELAY_SPECIAL_CARD_JOB("autoDelaySpecialCardJob"),//手持办理专项卡将老卡做自动延期
    SEND_DATA_TO_NOTIFY_URL_JOB("sendDataToNotifyUrlJob"),//发送通知消息
    CHAMBER_AUTO_FINISH_JOB("chamberAutoFinishJob"),//约球超时定时任务
    CHAMBER_OCCUPY_FIELD_JOB("chamberOccupyFieldJob"),//约球占场定时任务
    CHAMBER_SIGN_CANCEL_JOB("chamberSignCancelJob"),//约球自动取消定时任务
    SYNC_COMMUNITY_ACCT_JOB("syncCommunityAcctJob"),
    SALARY_CALC_JOB("salaryCalcJob"),//国信薪酬计算
    ANHUI_PROVINCE_ALL_USER_ENTRY_JOB("anhuiProvinceAllUserEntryJob"),//安徽省平台入馆信息同步
    ANHUI_PROVINCE_ALL_USER_CONSUME_JOB("anhuiProvinceAllUserConsumeJob"),//安徽省平台消费信息同步
    HANDLE_INVALID_SHARE_DEPOSIT_JOB("handleInvalidShareDepositJob"),//次卡分享资源的过期处理
    PARKING_EXIT_PAY_NOTIFY_JOB("parkingExitPayNotifyJob"),//停车出场缴费通知任务

    BATCH_HANDLE_ROOM_OCCUPY_CONFLICT_JOB("batchHandleRoomOccupyConflictJob"),//批量处理教室占场冲突
    GROUP_LESSON_WAITING_TO_BOOK_JOB("groupLessonWaitingToBookJob"),//团课候补订单上位

    GROUP_LESSON_BOOK_FINISH_JOB("groupLessonBookFinishJob"),//团课预定完工
    GROUP_LESSON_WAITING_FINISH_JOB("groupLessonWaitingFinishJob"),//团课排队完工

    GROUP_LESSON_BOOK_CANCEL_JOB("groupLessonBookCancelJob"),//团课预定取消
    GROUP_LESSON_WAITING_CANCEL_JOB("groupLessonWaitingCancelJob"),//团课排队取消

    GROUP_LESSON_WAITING_AUTO_CANCEL_JOB("groupLessonWaitingAutoCancelJob"),//团课排队取消

    GROUP_LESSON_CANCEL_JOB("groupLessonCancelJob"),//团课取消

    ENROLL_FREEZE_AUTO_CANCEL_JOB("enrollFreezeAutoCancelJob"),//冻课审核自动取消
    CABINET_FREEZE_AUTO_CANCEL_JOB("cabinetFreezeAutoCancelJob"),//冻柜审核自动取消


    SYNC_CENTER_TO_THIRD_JOB("syncCenterToThirdJob"),
    LED_SEND_MESSAGE("ledSendMessageJob"),//大连led显示屏推送

    WX_PALMS_LOCKER_CANCEL_JOB("wxPalmsLockerCancelJob"),
    PUSH_PARKING_COUPON_JOB("pushParkingCouponJob"),
    PERFORM_STOCK_UPDATE_JOB("performStockUpdateJob"),
    PERFORM_STOCK_SHOW_JOB("performStockShowJob"),


    CANCEL_ROOM_OCCUPY("cancelRoomOccupyJob"),
    PROJECT_TAKE_SEAT_JOB("projectTakeSeatJob");

    private String value;

    JobEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
