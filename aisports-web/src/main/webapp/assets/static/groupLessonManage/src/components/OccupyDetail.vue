<template>
    <div class="occupy-box">
        <div class="c-line">
            <div class="form-label">占用时段</div>
            <div class="form-field">
                <div class="time-item"
                    v-for="item in timeArr" 
                    :key="item.startTime + '' + item.endTime"
                    :disabled="item.disabled"
                    :class="{'checked': item.checked, 'disabled': item.disabled}"
                    @click="changeTimeItemState(item)"
                >
                    {{ item.startTime }}-{{ item.endTime }}
                </div>
            </div>
        </div>
        <occpy-detail-center-admin v-if="centerAdmin == '1'" ref="selection" v-bind="$props" ></occpy-detail-center-admin>
        <occpy-detail-venue-admin v-else ref="selection" v-bind="$props"></occpy-detail-venue-admin>
    </div>
</template>

<script>
import OccupyDetailCenterAdmin from './OccupyDetailCenterAdmin.vue'
import OccupyDetailVenueAdmin from './OccupyDetailVenueAdmin.vue'

console.log(OccupyDetailCenterAdmin)

export default{
    components: {
        'occpy-detail-center-admin': OccupyDetailCenterAdmin,
        'occpy-detail-venue-admin': OccupyDetailVenueAdmin
    },
    props: ['info', 'changeTag', 'extClass'],
    data() {
        return {
            centerAdmin: $('#center-admin').val(),
            timeArr: [],
        }
    },
    mounted() {
        console.log(this.$attrs)
        if (this.info.timeArr) {
            this.timeArr = this.info.timeArr
        }
    },
    watch: {
        'info.timeArr': {
            handler() {
                this.timeArr = this.info.timeArr
            },
            deep: true
        },
    },
    methods: {
        changeTimeItemState(item){
            if(!item.disabled){
                this.$set(item, 'checked', !item.checked)
            }
        },
        getSelectedFieldIds() {
            return this.$refs.selection.getSelectedFieldIds()
        }
    }
}
</script>
