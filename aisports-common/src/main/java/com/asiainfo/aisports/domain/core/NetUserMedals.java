package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class NetUserMedals {
    private Long id;

    private Long netUserId;

    private Integer medalId;

    private Date effectiveTime;

    private Date getTime;

    private String getStatus;

    private Long centerId;

    private Long venueId;

    private Long tradeId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNetUserId() {
        return netUserId;
    }

    public void setNetUserId(Long netUserId) {
        this.netUserId = netUserId;
    }

    public Integer getMedalId() {
        return medalId;
    }

    public void setMedalId(Integer medalId) {
        this.medalId = medalId;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getGetTime() {
        return getTime;
    }

    public void setGetTime(Date getTime) {
        this.getTime = getTime;
    }

    public String getGetStatus() {
        return getStatus;
    }

    public void setGetStatus(String getStatus) {
        this.getStatus = getStatus == null ? null : getStatus.trim();
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public Long getVenueId() {
        return venueId;
    }

    public void setVenueId(Long venueId) {
        this.venueId = venueId;
    }

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", netUserId=").append(netUserId);
        sb.append(", medalId=").append(medalId);
        sb.append(", effectiveTime=").append(effectiveTime);
        sb.append(", getTime=").append(getTime);
        sb.append(", getStatus=").append(getStatus);
        sb.append(", centerId=").append(centerId);
        sb.append(", venueId=").append(venueId);
        sb.append(", tradeId=").append(tradeId);
        sb.append("]");
        return sb.toString();
    }
}