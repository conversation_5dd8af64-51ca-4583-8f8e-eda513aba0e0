@import "../../common/element-theme-xports/element-variables";
@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../common/scss/ecardQuery";

.title-box {
  min-height: 620px;
}
.field-content{
  display: inline-block;
  max-width: 475px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.form-box{
  margin-top: 20px;
  @include formLine(60px);
  .form-field-mul{
    font-size: 0;
    .el-date-editor{
      vertical-align: middle;
    }
    .td{
      display: inline-block;
      font-size: 14px;
      text-align: center;
      vertical-align: middle;
      width: 265px;
      margin-left: 10px;
      .el-date-editor{
        width: 120px!important;
      }
    }
  }
  .form-line + .form-line{
    margin-top: 20px;
  }
  .form-line + .form-line-btn{
    margin-top: 40px;
  }
  .form-line-btn{
    font-size: 0;
    .btn + .btn{
      margin-left: 10px;
    }
  }
}
.conditions{
  background-color: #fafafa;
  .main-condition{
    height: 80px;
    border-bottom: 1px solid #dddddd;
    display: flex;
    .sports-list {
      height: 80px;
      width: 200px;
      border-right: 1px solid #f0f0f0;
      z-index: 1;
      position: relative;
      .current {
        position: relative;
        display: block;
        height: 80px;
        line-height: 80px;
        cursor: pointer;
        color: #585a5b;
        font-size: 0;
        padding-left: 27px;
        &:after {
          content: " ";
          @include triangle(bottom, 8px, 5px, #ccc);
          position: absolute;
          top: 36px;
          right: 10%;
        }
        &.active{
          &:after{
            transform: rotate(180deg);
          }
        }
        &:hover {
          color: #1fa2f5;
        }
        .iconfont {
          display: inline-block;
          font-size: 35px;
          line-height: 80px;
          color: #1fa2f5;
        }
        .title {
          display: inline-block;
          vertical-align: top;
          font-size: 18px;
          margin-left: 20px;
        }
      } // 下拉列表
      .sports {
        position: absolute;
        background-color: #fff;
        width: 100%;
        border: 1px solid #ddd;
        z-index: 10;
        li {
          cursor: pointer;
          text-indent: 16px;
          line-height: 34px;
          a {
            display: block;
          }
          &:hover {
            background-color: #eee!important;
          }
        }

        li:nth-child(odd) {
          background-color: #f9f9f9;
        }
      }
    }
    .time-box{
      width: 220px;
      border-left: 1px solid #f0f0f0;
      text-align: center;
      line-height: 80px;
      .el-date-editor{
        width: 195px;
      }
    }
    .workday-box{
      display: flex;
      flex: 1;
      .item{
        flex: 1;
        text-align: center;
        line-height: 80px;
        cursor: pointer;
        font-size: 18px;
        &.checked{
          color: #1fa2f5;
          border-bottom: 5px solid #1fa2f5;
        }
      }
    }
  }
  .date-box{
    padding: 12px 20px 0 106px;
    position: relative;
    height: 50px;
    overflow: hidden;
    .name{
      position: absolute;
      left: 20px;
      top: 17px;
    }
    .item-box{
      display: inline-block;
    }
    .item{
      display: inline-block;
      margin: 0 10px 12px 0;
      height: 26px;
      line-height: 26px;
      padding: 0 15px;
      border-radius: 13px;
      color: #7b7b7b;
      border: 1px solid #7b7b7b;
      cursor: pointer;
      background-color: #fff;
      &:last-child{
        margin-right: 0;
      }
      &.checked{
        border: 1px solid #1fa2f5;
        color: #1fa2f5;
      }
    }
    .icon-shousuojiantou{
      cursor: pointer;
      position: absolute;
      right: 20px;
      top: 15px;
      transform: rotate(180deg);
    }
    &.active{
      height: auto;
      .icon-shousuojiantou{
        transform: rotate(0);
      }
    }
  }
}
.sub-content{
  padding: 0 20px 80px;
  .room-list-box{
    margin-top: 20px;
  }
  .room-list-title{
    color: #000000;
    padding-left: 10px;
    font-weight: bold;
    position: relative;
    line-height: 14px;
    &:before{
      position: absolute;
      left: 0;
      top:0;
      bottom: 0;
      width: 4px;
      background-color: #1fa2f5;
      content: '';
    }
  }
  .room-list{
    margin-top: 15px;
    font-size: 0;
    .item{
      margin: 0 20px 20px 0;
      cursor: pointer;
      border-radius: 2px;
      width: 124px;
      height: 70px;
      line-height: 70px;
      text-align: center;
      border: 1px solid #e0e0e0;
      display: inline-block;
      font-size: 14px;
      &.disabled{
        color: #c9c9c9;
        border: 1px solid #e8e8e8;
      }
      &.checked{
        border: 2px solid #57b956;
        background: url(../images/choosebg.png) right bottom no-repeat;
      }
      &:last-child{
        margin-right: 0;
      }
    }
  }
  .occupy-reason{
    padding-top: 20px;
    border-top: 1px solid #e4e4e4;
    .occupy-reason-title{
      margin-bottom: 20px;
    }
    .normal-textarea{
      height: 108px;
      padding: 10px;
    }
  }
  .btns-line{
    margin-top: 40px;
    font-size: 0;
    .btn + .btn{
      margin-left: 10px;
    }
  }
}
.submit{
  @include bSubmit();
  .member-verify {
    float: left;
    margin-left: 200px;
    padding-left: 20px;
    .circle {
      display: inline-block;
      @include circle(5px);
      background-color: #86dc85;
      margin-right: 15px;
    }
    .member-verify-cardno {
      margin-left: 10px;
    }
    .member-reverify {
      background-color: #fff;
      margin-left: 6px;
      display: inline-block;
      height: 20px;
      line-height: 20px;
      vertical-align: middle;
      .iconfont {
        font-size: 20px;
        color: #56b854;
        background-color: #fff;
        @include circle(20px);
      }
    }
  }
  .selected-places {
    display: inline-block;
    margin-right: 30px;
    >span {
      vertical-align: top;
    }
    .selected-places-wrapper {
      position: relative;
      display: inline-block;
      font-size: 30px; // 只选了单个场地
      .single-selected {
        font-size: 14px;
        text-align: center;
        margin-top: 5px;
        width: 300px;
        height: 25px;
        line-height: 25px;
        >em {
          display: inline-block;
          border: 1px solid #f25232;
          color: #f25232;
          @include circleBox(13px);
          padding: 0 10px;
          margin: 16px 2px 0;
          vertical-align: top;
          &.coachstr {
            cursor: pointer;
          }
        }
      } // 选中多个场地
      .multi-selected {
        line-height: 40px;
        >em {
          color: #f25232;
          vertical-align: middle;
        }
      }
      .selected-places-list {
        position: absolute;
        bottom: 50px;
        left: 0;
        width: 400px;
        font-size: 14px;
        line-height: 25px;
        >li {
          text-align: left;
          margin-top: 5px;
          >em {
            display: inline-block;
            border: 1px solid #f25232;
            color: #f25232;
            @include circleBox(13px);
            padding: 0 10px;
            margin: 0 2px;
            &.coachstr {
              cursor: pointer;
            }
          }
        }
      }
    }
    .open-selected {
      cursor: pointer;
      vertical-align: 5px;
      &.active {
        transform: rotate(180deg);
        display: inline-block;
        height: 17px;
        vertical-align: middle;
        line-height: 7px;
      }
    }
  }
  .show-sum{
    display: inline-block;
    margin-right: 20px;
    margin-top:16px;
    height: 27px;
    line-height: 25px;
    padding: 0 22px;
    color: #56b854;
    border: 1px solid #56b854;
    @include circleBox(14px);
    vertical-align: top;
    cursor: pointer;
  }
}
.form-box{
  @include formLine(107px);
  padding-bottom: 100px;
  .form-field{
    padding-right: 20px;
    textarea{
      width: 360px;
      height: 109px;
      resize: none;
    }
  }
  .occupy-info{
    padding:0 0 20px 18px;
    border-radius: 4px;
    border: 1px solid #cccccc;
    width: 688px;
    .card-info{
      padding: 12px 0;
      @include clearfix();
      border-bottom: 1px dashed #e4e4e4;
      .card-no{
        font-size: 0;
        display: inline-block;
        i{
          color: #1fa2f5;
          font-size: 16px;
        }
        span{
          font-weight: bold;
          color: #333333;
          margin-left: 10px;
          font-size: 14px;
        }
      }
      .card-balance{
        float: right;
        margin-right: 20px;
        i{
          color: #56b854;
          font-size: 16px;
        }
      }
    }
    .dates{
      margin-top: 20px;
      font-weight: bold;
    }
    .item-box{
      padding-right: 2px;
      font-size: 0;
      .item{
        width: 50%;
        display: inline-block;
        padding:0 33px 0 15px;
        margin-top: 12px;
        font-size: 14px;
        position: relative;
        vertical-align: top;
        &:before{
          content: '';
          width: 6px;
          height: 6px;
          border-radius: 100%;
          background-color: #1fa2f5;
          left: 0;
          top: 6px;
          position: absolute;
        }
      }
    }
  }
  .form-line + .form-line{
    margin-top: 20px;
  }
}
.detail-info{
  padding: 0 0 30px 20px;
  border-bottom: 1px dashed #eff2f7;
  font-size: 0;
  .item{
    width: 25%;
    font-size: 14px;
    display: inline-block;
    color: #333333;
    vertical-align: top;
    margin-top: 30px;
    padding-right: 20px;
    i{
      color: #999999;
      display: inline-block;
    }
  }
  .item-all{
    width: 100%;
    position: relative;
    padding-left: 45px;
    .name{
      position: absolute;
      left: 0;
      top:0;
      color: #999999;
    }
    .info-item + .info-item{
      margin-top: 10px;
      line-height: 20px;
      padding-right: 200px;
    }
  }
}
.switch-items{
  margin-top: 20px;
  padding:0 0 15px 20px;
  border-bottom: 1px solid #dddddd;
  .item{
    display: inline-block;
    cursor: pointer;
    position: relative;
    &.checked{
      color: #1fa2f5;
      &:after{
        content: '';
        left: 2px;
        right: 2px;
        height: 2px;
        position: absolute;
        background-color: #1fa2f5;
        bottom: -16px;
      }
    }
  }
  .item + .item{
    margin-left: 60px;
  }
}
.table-box{
  padding: 20px 20px 80px;
  .btn-area{
    text-align: right;
    .btn{
      padding: 7px 13px;
    }
  }
  .simple-splitter{
    margin-top: 15px;
    .cancel{
      color: #999999;
    }
  }
}
.simple-splitter-conflict{
  width: 100%;
  thead{
    .with-ipt{
      width: 125px;
      text-align: left;
      padding-left: 10px;
      input{
        vertical-align: middle;
        margin-right: 15px;
      }
      span{
        display: inline-block;
        vertical-align: middle;
      }
    }
  }
  tbody{
    &:before{
      height: 10px;
      display: block;
      content: '';
      background-color: #fff;
    }
    tr{
      border-left: 1px solid #ebebeb;
      td{
        border-bottom: 1px solid #ebebeb;
        border-right: 1px solid #ebebeb;
      }
      .table-title{
        background-color: #f5f5f5;
        border-top: 1px solid #ebebeb;
        border-bottom: none;
        text-align: left;
        padding: 13px 30px 13px 10px;
        font-size: 0;
        position: relative;
        .td-ipt{
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
        }
        .info{
          display: inline-block;
          vertical-align: middle;
          font-size: 14px;
        }
        .info + .info{
          margin-left: 30px;
        }
        .r-box{
          position: absolute;
          right: 20px;
          top:50%;
          transform: translateY(-50%);
          cursor: pointer;
          color: #7e7e7e;
          .notice-success{
            position: absolute;
            right: -3px;
            bottom: 4px;
            width: 10px;
            height: 10px;
            background: #fff url("../images/notice-success.png") center no-repeat;
            border-radius: 100%;
            display: none;
          }
          &.checked{
            display: block;
          }
        }
      }
      .td-info + .td-info{
        margin-top: 5px;
      }
    }
  }
}
.conflict-box{
  border: 1px solid #ebebeb;
  .conflict-box-title{
    padding: 12px 10px;
    background-color: #f5f5f5;
    .item{
      display: inline-block;
      font-weight: bold;
    }
    .item + .item{
      margin-left: 40px;
      font-weight: normal;
    }
  }
  .info-box{
    padding: 10px;
    max-height: 260px;
    overflow-y: auto;
    .simple-splitter{
      margin-top: 0;
      border: 1px solid #ebebeb;
    }
  }
}
.el-popover{
  max-width: 500px;
}