@import "../../common/element-theme-xports/element-variables";
@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../../sea_modules/bootstrap-select/bootstrap-select";
@import "../../common/scss/layout/query-list";

.title-box{
  min-height: 620px;
}
.form_line_display{
  display: flex;
  align-items: center;
  .form_field_noLeft:first-child{
    margin-left: 0px;
  }
  .form_field_noLeft{
    margin-left: 10px;
  }
}
.table_list{
	margin-top: 20px;
	overflow: scroll;
	.el-table{
		min-height: 300px;
	  max-height: 600px;
	  }
  }
.table_list_none{
  margin-top: 20px;
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #eee;
  border-radius: 4px;
  .icon-xiangqing1{
    font-size: 40px;
    color: #ccc;
  }

}
//新增按钮
.add-btn-con{
  // width: 101px;
  text-align: right;
  position: relative;
  &>button {
    display: inline-block;
  }
  .bs-select{
    width: 152px!important;
  }
  .bootstrap-select.bs-select{
    position: absolute;
    top : 2px;
    right: 0px;
    @include clearfix();
    button{
      opacity: 0;
      width: 101px;
      float: right;
    }
  }
  .dropdown-menu{
    &:before{
      position: absolute;
      top : -6px;
      right: 23px;
      content: ' ';
      width: 10px;
      height: 10px;
      background-color: #fff;
      @include arrow(top,rgba(0,0,0,.15),1px);
    }
  }
}
.cover-box{
  .inner-box{
    @include formLine(100px);
    .form-line + .form-line{
      margin-top: 15px;
    }
  }
}
.query-list{
  .list-container{
    display: block;
  }
}
.paginationer{
  margin-top: 15px;
}

.no-wrap {
  cursor: pointer;
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
