@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../common/scss/bigTabs";
@import "../../../sea_modules/bootstrap-select/bootstrap-select";

.remain-message-alert-config{
	padding: 20px;
	.sub-box{
		padding: 0;
	}
}
.tab-alert-setup{
	padding: 10px;
}
.tab-alert-history{
	padding: 0;
	margin: 20px;
	min-height: 600px;
}
.alert-type-filters{
	@include clearfix;
	margin: 10px;
	.alert-type-filter{
		float: left;
		width: 82px;
		text-align: center;
		line-height: 26px;
		cursor: pointer;
		border-radius: 5px;
		color: #999;
		&.cur{
			background-color: #1fa1f5;
			cursor: default;
			color: #fff;
		}
	}
}
.table-alert-history{
	//margin: 20px 10px;
}