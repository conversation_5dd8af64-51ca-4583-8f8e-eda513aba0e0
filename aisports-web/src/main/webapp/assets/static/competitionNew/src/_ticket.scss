.title-box .sub-box {
  padding: 0;
  margin: -1px;
}

.wrapper .x-box .sub-box {
  min-height: auto;
}

.query-section {
  padding: 20px;
  border-bottom: 1px solid #ddd;
  button + button{
    margin-left: 20px;
  }
  .add-btn-gray {
    float: right;
  }
}
.splitter-wrap {
  padding: 20px;
}

.btn-return {
  float: right;
}

.panel-heading {
  overflow: hidden;
  line-height: 40px;

  span {
    font-size: 20px;
  }
}
.panel--send-num{
  border: 0;
}
.add-section{
  padding: 20px 0 10px;
  text-align: right;
}

.img-div{
  position: relative;
  display: inline-block;
  width: 20px;
  .icon-iconfonttupian{
    color: #c2c0c0;
    cursor: pointer;
  }
  &:hover{
    .img-box{
      display: block;
    }
  }
  .img-box{
    display: none;
    position: absolute;
    width: 91px;
    left: 31px;
    top: -12px;
    height: 90px;
    padding: 5px 2px;
    border: 1px solid #e0e0e0;
    background-color: #fff;
    z-index: 2;
    border-radius: 5px;
    text-align: center;
    @include boxShadow(0,0,5px,#ececec);
    &:before{
      content: " ";
      position: absolute;
      background-color: #fff;
      @include arrow(right, #e0e0e0, 1px);
      top: 20px;
      left: -5px;
      width: 8px;
      height: 8px;
    }
    img{
      max-width: 85px;
      max-height: 85px;
    }
  }
  &:hover{
    .img-box{
      display: block;
    }
  }
}

// 票配置
.comp-ticket-config {
  .icon-shanchu,.icon-bianji {
    color: #d7d7d7;
    cursor: pointer;
  }
  .icon-shanchu{
    font-size: 24px;
  }
  .icon-bianji{
    font-size: 20px;
  }
  .faded.iconfont {
    color: #eee;
  }
}