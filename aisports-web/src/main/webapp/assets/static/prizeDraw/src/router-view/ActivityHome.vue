<template>
  <div class="activity-home">
    <div class="activity-home-head">
      <div class="activity-home-head__left"> 活动主页 </div>
      <div class="activity-home-head__right">
        <div class="btn-return" @click="goBack">
          <div class="iconfont icon-zuojiantou" style="font-size: 24px"></div>
        </div>
      </div>
    </div>
    <div class="activity-home-body">
      <div class="activity-home-body__intro">
        <div class="intro-left">
          <div class="intro-left-banner">
            <img :src="transverseImg" />
          </div>
          <div class="intro-left-content">
            <div class="content-title">
              <span class="value">{{ activityInfo.name }}</span>
              <span :style="parseStyle(activityState)" class="status">{{
                parseStatus(activityState)
              }}</span>
            </div>
            <div class="content-time">
              <span class="content-time-start"
                >开始时间: {{ activityInfo.startDate }}</span
              >
              <span class="content-time-end"
                >结束时间: {{ activityInfo.endDate }}</span
              >
            </div>
          </div>
        </div>
        <div class="intro-right">
          <el-button icon="iconfont icon-bianji" @click="toDetail"
            >编辑活动</el-button
          >
          <el-button
            v-show="activityState != 0"
            icon="iconfont icon-xiajia2"
            @click="dropAction"
            >下架活动</el-button
          >
          <el-button
            v-show="activityState == 0"
            icon="iconfont icon-fabu"
            @click="uploadAction"
            >上架活动</el-button
          >
          <el-button
            v-show="activityState == 0"
            icon="iconfont icon-lajixiang"
            @click="handleDelete"
            >删除活动</el-button
          >
        </div>
      </div>
      <div class="activity-home-body__line"> </div>
      <div class="activity-home-body__overview">
        <div class="overview-title">执行数据</div>
        <div class="overview-block">
          <div class="overview-block-wrap">
            <PrizeOverview :overview-list="overviewList" />
          </div>
        </div>
        <div class="overview-block">
          <AnswerPeopleOverview :activity-id="activityId" />
        </div>
      </div>
    </div>
  </div>
</template>


<script>
import PrizeOverview from '../components/PrizeOverview.vue'
import AnswerPeopleOverview from '../components/AnswerPeopleOverview.vue'
import {
  getActivityDetail,
  takeOffActivity,
  publishActivity,
  deleteActivity,
  getAnswerDetail,
} from '../utils/api'
import { statusList } from '../utils/index'
import choujiang1 from '../images/choujiang1.png'
import choujiang2 from '../images/choujiang2.png'

export default {
  name: 'ActivityHome',
  components: {
    PrizeOverview,
    AnswerPeopleOverview,
  },
  data() {
    return {
      prizeList: [
        // {
        //   title: '抽奖活动1',
        //   list: [
        //     {
        //       title: '参与抽奖',
        //       value: 16078, // item.value.toLocaleString()
        //       showUnit: true,
        //     },
        //     {
        //       title: '一等奖',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '距离最长的内容30px',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '三等奖',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '奖项名称奖项名称',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '再接再厉',
        //       value: '1/12/20',
        //     },
        //   ],
        // },
        // {
        //   title: '抽奖活动2',
        //   list: [
        //     {
        //       title: '参与抽奖',
        //       value: 16078, // item.value.toLocaleString()
        //       showUnit: true,
        //     },
        //     {
        //       title: '一等奖',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '距离最长的内容30px',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '三等奖',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '奖项名称奖项名称',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '再接再厉',
        //       value: '1/12/20',
        //     },
        //   ],
        // },
        // {
        //   title: '抽奖活动3',
        //   list: [
        //     {
        //       title: '参与抽奖',
        //       value: 16078, // item.value.toLocaleString()
        //       showUnit: true,
        //     },
        //     {
        //       title: '一等奖',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '距离最长的内容30px',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '三等奖',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '奖项名称奖项名称',
        //       value: '1/12/20',
        //     },
        //     {
        //       title: '再接再厉',
        //       value: '1/12/20',
        //     },
        //   ],
        // },
      ],
      showPrizeNumber: 1,
      expand: false,
      activityInfo: {},
      overviewList: [],
      activityState: '',
    }
  },
  computed: {
    showExpand() {
      return this.prizeList.length > 1
    },
    activityId() {
      return this.$route.query.activityId || ''
    },
    transverseImg() {
      const currentTimeStamp = new Date().getTime()
      return currentTimeStamp % 2 === 0 ? choujiang2 : choujiang1
    },
  },
  created() {
    this.getActivityDetailInfo()
    this.getAnswerDetailInfo()
  },
  methods: {
    getAnswerDetailInfo() {
      return getAnswerDetail({
        activityId: this.activityId,
      }).then((res) => {
        console.log(res, '=sdsd')
        const { lotteryTimes, awardList } = res
        this.overviewList = awardList.map((item) => ({
          title: item.name,
          value: (item.lotteryCount || 0) + '/' + (item.totalQuantity || 0),
        }))
        this.overviewList.unshift({
          title: '参与抽奖',
          value: lotteryTimes || 0,
          showUnit: true,
        })
        // this.getPrizeList(modeEnrollNumList)
      })
    },
    // getPrizeList(modeEnrollNumList) {
    //   modeEnrollNumList.forEach((item) => {
    //     item.lotteryActivityList.forEach((tag) => {
    //       this.prizeList.push({
    //         title: item.modeName + '/' + tag.lotteryActivityName,
    //         list: [
    //           {
    //             title: '参与抽奖',
    //             value: tag.lotteryNum.toLocaleString(), // item.value.toLocaleString()
    //             showUnit: true,
    //           },
    //           ...tag.awardLotteryList.map((i) => ({
    //             title: i.activityAwardName || '',
    //             value:
    //               (i.lotteryNumber || 0) +
    //               '/' +
    //               (i.receiveNumber || 0) +
    //               '/' +
    //               (i.totalQuantity || 0),
    //           })),
    //         ],
    //       })
    //     })
    //   })
    // },
    getActivityDetailInfo() {
      return getActivityDetail({
        activityId: this.activityId,
      }).then((res) => {
        this.activityInfo = res.activity
        this.activityState = res.activityState
      })
    },
    parseStatus(value) {
      const target = statusList.find((item) => item.value == value) || {}
      return target.label || ''
    },
    parseStyle(value) {
      const target = statusList.find((item) => item.value == value) || {}
      return {
        color: target.color,
        backgroundColor: target.backgroundColor,
      }
    },
    goBack() {
      this.$router.go(-1)
    },
    expandAction() {
      this.expand = !this.expand
      if (this.expand) {
        this.showPrizeNumber = this.prizeList.length
      } else {
        this.showPrizeNumber = 1
      }
    },
    toDetail() {
      this.$router.push(`/activity?activityId=${this.activityId}`)
    },
    handleDelete() {
      this.$confirm('确认删除该活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return deleteActivity({
            activityId: this.activityId,
          })
        })
        .then((res) => {
          if (res.error == 0) {
            this.$showMessage({
              message: '删除成功',
              type: 'success',
            })
            this.$router.go(-1)
          }
        })
        .catch(() => {})
    },
    dropAction() {
      this.$confirm('确认下架该活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return takeOffActivity({
            activityId: this.activityId,
          })
        })
        .then((res) => {
          if (res.error == 0) {
            this.$showMessage({
              message: '下架成功',
              type: 'success',
            })
            this.getActivityDetailInfo()
          }
        })
        .catch(() => {})
    },
    uploadAction() {
      this.$confirm('确认上架该活动?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return publishActivity({
            activityId: this.activityId,
          })
        })
        .then((res) => {
          if (res.error == 0) {
            this.$showMessage({
              message: '上架成功',
              type: 'success',
            })
            this.getActivityDetailInfo()
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss">
.activity-home {
  margin: 16px;
  border-radius: 16px 16px 0 0;

  &-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 60px;
    border-radius: 16px 16px 0 0;
    background-color: #fafafa;
    padding: 0 16px;

    &__left {
      color: #333333;
      font-weight: 500;
      font-size: 18px;
    }

    &__right {
      height: 40px;
      width: 40px;
      border: 1px solid #a4a8b3;
      border-radius: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      z-index: 200;

      .iconfont {
        color: #a4a8b3;
      }
    }
  }

  &-body {
    background-color: #fff;
    padding: 16px;

    &__intro {
      display: flex;
      height: 100px;
      justify-content: space-between;
      align-items: center;

      .intro-left {
        display: flex;
        justify-content: center;
        align-items: center;
        &-banner {
          width: 270px;
          height: 90px;

          img {
            width: 100%;
            height: 100%;
            border-radius: 6px;
          }
        }

        &-content {
          margin-left: 10px;
          .content-title {
            .value {
              color: #333333;
              font-weight: 500;
              font-size: 16px;
            }

            .status {
              display: inline-block;
              width: 60px;
              height: 24px;
              border-radius: 4px;
              text-align: center;
              line-height: 24px;
            }
          }

          .content-time {
            display: flex;
            flex-direction: column;
            color: #7b7b7b;
            font-weight: 400;
            font-size: 14px;
            margin-top: 20px;
          }
        }
      }

      .intro-right {
        .iconfont {
          margin-right: 10px;
          position: relative;
          top: 2px;
        }
      }
    }

    &__line {
      /* 直线 */
      width: 100%;
      height: 1px;
      border: 1px dotted #e0e0e0;
      margin: 20px 0;
    }

    &__overview {
      .overview-title {
        color: #333333;
        font-weight: 500;
        font-size: 16px;
      }

      .overview-block {
        margin-top: 16px;
        &-title {
          display: flex;
          align-items: center;
          margin-bottom: 14px;
          &__line {
            display: inline-block;
            width: 3px;
            height: 14px;
            border-radius: 2px;
            background-color: #1fa2f5;
          }

          &__value {
            color: #333333;
            font-weight: 500;
            font-size: 14px;
            margin-left: 10px;

            .suffix {
              color: #999999;
              font-weight: 400;
              font-size: 12px;
              margin-left: 10px;
            }
          }
        }

        &-wrap {
          &__expand {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 24px;
            cursor: pointer;

            .icon {
              display: inline-block;
              width: 24px;
              height: 24px;
              border-radius: 100%;
              text-align: center;
              line-height: 24px;
              background-color: #ebebeb;

              .iconfont {
                color: #000000;
                opacity: 0.4;
              }
            }
          }
        }
      }
    }
  }
}
</style>