package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.AppNotificationTemplate;
import com.asiainfo.aisports.model.CenterResource;
import com.asiainfo.aisports.persistence.core.StaffResourceMapper;
import com.asiainfo.aisports.push.PushClient;
import com.asiainfo.aisports.tools.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Created by fuzf on 2017/7/18.
 */

@Service
public class AppNotificationPushService {
    @Autowired
    private PushClient pushClient;
    @Autowired
    private StaffResourceMapper staffResourceMapper;

    /**
     * 推送消息
     *
     * @param appNotificationTemplateList 模板
     * @param staffId
     * @param data
     */
    @Transactional
    public void sendPush(List<AppNotificationTemplate> appNotificationTemplateList, Long staffId, Map<String, Object> data) {
        for (AppNotificationTemplate template : appNotificationTemplateList) {
            if (Constants.NotificationAppType.OA.equals(template.getAppType())) {
                String message = StringUtils.formatTemplate(template.getContent(), data);
                CenterResource centerResource = null;
                List<CenterResource> centerResources = staffResourceMapper.selectCentersByStaffId(staffId);
                if (!centerResources.isEmpty()) {
                    centerResource = centerResources.get(0);
                }
                pushClient.sendPush(String.valueOf(staffId), message, template.getActionUrl(), template.getTemplateName(), centerResource != null ? centerResource.getCenterId() : null);
            }
        }
    }
}
