<#setting date_format="yyyy-MM-dd HH:mm:ss">
<#assign hasOperationCol = true>
<table class="table table-striped table-th-border table-delay">
    <tbody>
    <tr>
        <th>业务描述</th>
        <th>场馆</th>
        <th>金额</th>
        <th>折扣</th>
        <th>手续费</th>
        <th>状态</th>
        <th>操作</th>
    </tr>
    </tbody>
    <#if serviceAcceptInfoList??>
    <#-- 工单List集合遍历-->
        <#list serviceAcceptInfoList as serviceAccept>
            <#if serviceAccept.compTradeId??>
                <#if serviceAccept.compTradeSort??>
                    <#-- 组合订单第一个 -->
                    <#if serviceAccept.compTradeSort == 'start'>
                        <tbody class="comptrade">
                        <tr class="for-border">
                            <td colspan="5" class="table-title">
                                <div class="info-line">
                                    <span class="info-line-bold"><i class="comp-sign">组合</i>${serviceAccept.compTrade.acceptDate!""}</span>
                                    <span class="tradeId info-line-bold">订单号：<i>#{serviceAccept.compTrade.compTradeId!""}</i></span>
                                    <span class="info-line-bold">订单来源：${serviceAccept.compTrade.channelName!""}</span>
                                </div>
                                <div class="info-line">
                                    <span>卡号：${serviceAccept.compTrade.ecardNo!""}</span>
                                    <span>姓名：${serviceAccept.compTrade.custName!""}</span>
                                    <span>提交人：${serviceAccept.compTrade.tradeStaffName!""}</span>
                                </div>
                            </td>
                            <td colspan="2" class="table-title table-title-center">
                            </td>
                        </tr>
                        <tr data-state="${serviceAccept.compTradeSort}">
                            <td colspan="7" class="inner-td">
                                <table class="table table-striped table-th-border table-delay">
                                    <tbody class="compTrade">
                                    <#include "orderRowHeader.ftl">
                                    <#include "orderRow.ftl">
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    <#-- 组合订单最后一个 -->
                    <#elseif serviceAccept.compTradeSort == 'end'>
                        <tr data-state="${serviceAccept.compTradeSort}">
                            <td colspan="7" class="inner-td">
                                <table class="table table-striped table-th-border table-delay">
                                    <tbody class="compTrade">
                                    <#include "orderRowHeader.ftl">
                                    <#include "orderRow.ftl">
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    <#-- 组合订单在当前分页最后一条 -->
                    <#elseif serviceAccept.compTradeSort == 'point'>
                        <tbody class="comptrade">
                        <tr class="for-border">
                            <td colspan="6" class="table-title">
                                <div class="info-line">
                                    <span class="info-line-bold"><i class="comp-sign">组合</i>${serviceAccept.compTrade.acceptDate!""}</span>
                                    <span class="tradeId info-line-bold">订单号：<i>#{serviceAccept.compTrade.compTradeId!""}</i></span>
                                    <span class="info-line-bold">订单来源：${serviceAccept.compTrade.channelName!""}</span>
                                </div>
                                <div class="info-line">
                                    <span>卡号：${serviceAccept.compTrade.ecardNo!""}</span>
                                    <span>姓名：${serviceAccept.compTrade.custName!""}</span>
                                    <span>提交人：${serviceAccept.compTrade.tradeStaffName!""}</span>
                                </div>
                            </td>
                            <td colspan="1" class="table-title table-title-center">
                            </td>
                        </tr>
                        <tr data-state="${serviceAccept.compTradeSort}">
                            <td colspan="7" class="inner-td">
                                <table class="table table-striped table-th-border table-delay">
                                    <tbody class="compTrade">
                                    <#include "orderRowHeader.ftl">
                                    <#include "orderRow.ftl">
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </#if>
                <#else>
                    <#-- 组合订单中间的 -->
                    <tr>
                        <td colspan="7" class="inner-td">
                            <table class="table table-striped table-th-border table-delay">
                                <tbody class="compTrade">
                                <#include "orderRowHeader.ftl">
                                <#include "orderRow.ftl">
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </#if>
            <#else>
                <#-- 普通订单 -->
                <tbody data-compTradeId="#{serviceAccept.compTradeId!0}" data-start="${serviceAccept.compTradeSort!""}">
                <#include "orderRowHeader.ftl">
                <#include "orderRow.ftl">
                </tbody>
            </#if>
        </#list>
    </#if>
</table>
<div class="f-line">
    <#include "../public/pageInfo.ftl">
</div>


