package com.asiainfo.aisports.controller;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.CoachLevelMapper;
import com.asiainfo.aisports.persistence.core.CoachServiceMapper;
import com.asiainfo.aisports.service.CoachService;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.StaffService;
import com.asiainfo.aisports.service.VenueServService;
import com.asiainfo.aisports.web.WebResult;
import com.github.pagehelper.PageInfo;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by yuanxy on 2015/3/4.
 */
@Controller
@RequestMapping("/coachManage")
public class CoachManageController {
    @Autowired
    private CoachService coachService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private CoachLevelMapper coachLevelMapper;
    @Autowired
    private CoachServiceMapper coachServiceMapper;
    @Autowired
    private VenueServService venueServService;
    @Autowired
    private VenueParamConfig venueParamConfig;

    /**
     * 教练管理初始化
     *
     * @return
     */
    @RequestMapping("/init")
    public String init() {
        return "/newCoachManage/newCoachManage";
    }

    /**
     * 获取该场馆教练信息集合
     *
     * @param staff
     * @param keyWord
     * @param pageNo
     * @return
     */
    @RequestMapping("/getCoachList")
    @ResponseBody
    public WebResult getCoachList(LoginStaff staff,
                                  @RequestParam(value = "keyWord", required = false) String keyWord,
                                  @RequestParam(value = "isSiftCoach", required = false) String isSiftCoach,
                                  @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo) {
        if (staff.getVenueId() == null) {
            return new WebResult(1, "员工没有管理场馆");
        }

        WebResult webResult = new WebResult();
        List<JSONObject> coachList = coachService.findBatchCoach(pageNo, keyWord, staff.getVenueId(), staff.getCenterId(), isSiftCoach);
        webResult.set("coachList", coachList);
        webResult.set("pageInfo", new PageInfo<>(coachList));
        return webResult;
    }

    /**
     * 编辑或新增教练信息(页面的跳转)
     *
     * @param staff
     * @param modelMap
     * @param coachId
     * @return
     */
    @RequestMapping("/editCoach")
    public String getEditCoachInfo(LoginStaff staff, ModelMap modelMap,
                                   @RequestParam(value = "coachId", required = false) Long coachId) {
        //场馆员工集合
        List<Staff> staffList = staffService.getVenueStaffListByVenueId(staff.getVenueId());
        //场馆服务集合
        List<DataMap> venueServiceList = venueServService.findByCenterIdAndVenueId(staff.getCenterId(), staff.getVenueId());
        //教练经理集合
        List<Map> coachManageList = coachService.selectManageStaffByRoleGroup(staff.getVenueId(), Constants.RoleGroup.TRAINING_MANAGE);

        modelMap.put("staffList", staffList);
        modelMap.put("venueServiceList", venueServiceList);
        modelMap.put("coachManageList", coachManageList);

        //教练基础信息
        if (coachId != null) {
            Coach coach = coachService.findCoachByCoachId(coachId);
            modelMap.put("coach", coach);
            modelMap.put("coachId", coachId);
            modelMap.put("staffId", coach.getStaffId());
        }

        //配置参数的场馆显示培训执教课程选项,默认不显示
        Boolean trainingShowFlag = false;
        String coachCourseTag = venueParamConfig.getParam(staff.getVenueId(),Constants.VenueParam.COACH_COURSE_TAG);
        if ("1".equals(coachCourseTag)) {
            trainingShowFlag = true;
        }
        modelMap.put("trainingShowFlag",trainingShowFlag);

        return "/newCoachManage/editCoach";
    }

    /**
     * 获取教练的基本信息
     *
     * @param staff
     * @param modelMap
     * @param coachId
     * @return
     */
    @RequestMapping("/getCoachBaseInfo")
    public String getCoachBaseInfo(LoginStaff staff, ModelMap modelMap,
                                   @RequestParam(value = "coachId", required = false) Long coachId) {
        //场馆员工集合
        List<Staff> staffList = staffService.getVenueStaffListByVenueId(staff.getVenueId());
        //教练经理集合
        List<Map> coachManageList = coachService.selectManageStaffByRoleGroup(staff.getVenueId(), Constants.RoleGroup.TRAINING_MANAGE);

        modelMap.put("staffList", staffList);
        modelMap.put("coachManageList", coachManageList);

        //教练基础信息
        if (coachId != null) {
            Coach coach = coachService.findCoachByCoachId(coachId);
            modelMap.put("coach", coach);
            modelMap.put("coachId", coachId);
        }

        return "/newCoachManage/coachBaseInfo";
    }

    /**
     * 获取教练的工作内容信息
     *
     * @param staff
     * @param modelMap
     * @param coachId
     * @return
     */
    @RequestMapping("/getCoachWorkContent")
    public String getCoachWorkContent(LoginStaff staff, ModelMap modelMap,
                                      @RequestParam(value = "coachId", required = false) Long coachId) {
        if (coachId != null) {
            //查询教练信息
            Coach coach = coachService.findCoachByCoachId(coachId);
            //查询员工工作时间
            List<StaffWorkTime> staffWorkTimeList = coachService.searchStaffWorkList(coach.getStaffId(), staff.getCenterId());
            //查询教练陪练价格表
            List<CoachTrainingPrice> coachTrainingPriceList = coachService.getCoachTrainingList(coachId, staff.getCenterId());
            //教练所有的service
            List<DataMap> coachServiceList = coachService.getCoachService(coachId);

            modelMap.put("coach", coach);
            modelMap.put("staffWorkTimeList", staffWorkTimeList);
            modelMap.put("coachTrainingPriceList", coachTrainingPriceList);
            modelMap.put("coachServiceList", coachServiceList);

            //场馆服务集合
            List<DataMap> venueServiceList = venueServService.findByCenterIdAndVenueId(staff.getCenterId(), staff.getVenueId());
            for (DataMap service : venueServiceList) {
                CoachLevel coachLevel = new CoachLevel();
                coachLevel.setServiceId((Long) service.get("serviceId"));
                coachLevel.setState("1");
                coachLevel.setCenterId(staff.getCenterId());
                List<CoachLevel> coachLevelList = coachLevelMapper.selectByFields(coachLevel).stream().sorted(Comparator.comparing(CoachLevel::getSort)).collect(Collectors.toList());
                service.put("coachLevelList", coachLevelList);

                CoachServiceKey coachServiceKey = new CoachServiceKey();
                coachServiceKey.setCoachId(coachId);
                coachServiceKey.setServiceId((Long) service.get("serviceId"));
                com.asiainfo.aisports.domain.core.CoachService coachServiceLevel = coachServiceMapper.selectByPrimaryKey(coachServiceKey);
                if (coachServiceLevel != null) {
                    service.put("selectedLevel", coachServiceLevel.getCoachLevel());
                }
            }
            modelMap.put("venueServiceList", venueServiceList);
        }

        return "/newCoachManage/workContent";
    }

    /**
     * 获取教练相册管理内容
     *
     * @param staff
     * @param coachId
     * @return
     */
    @RequestMapping("/getCoachAlbumContent")
    @ResponseBody
    public WebResult getCoachAlbumContent(LoginStaff staff,
                                          @RequestParam(value = "coachId") Long coachId) {
        WebResult webResult = new WebResult();
        webResult.set("trainingMediaResourceList", coachService.getCoachAlbumContent(coachId, staff.getCenterId()));
        return webResult;
    }

    /**
     * 删除教练管理的相册中的某张照片
     *
     * @param staff
     * @param mediaResourceId
     * @return
     */
    @RequestMapping("/deleteCoachPhoto")
    @ResponseBody
    public WebResult deleteCoachPhoto(LoginStaff staff,
                                      @RequestParam(value = "mediaResourceId") Long mediaResourceId) {
        WebResult webResult = new WebResult();
        ServiceResult serviceResult = coachService.deleteCoachPhoto(staff.getStaffId(), mediaResourceId);
        webResult.putAll(serviceResult);
        return webResult;
    }

    /**
     * 插入教练管理的图片
     *
     * @param request
     * @param staff
     * @param coachId
     * @return
     * @throws IOException
     */
    @RequestMapping("/insertCoachPhoto")
    @ResponseBody
    public WebResult insertCoachPhoto(HttpServletRequest request, LoginStaff staff,
                                      @RequestParam(value = "coachId") Long coachId) throws IOException {
        request.setCharacterEncoding("utf-8");
        WebResult webResult = new WebResult();
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
        if (multipartResolver.isMultipart(request)) {
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            Iterator<String> ite = multiRequest.getFileNames();
            while (ite.hasNext()) {
                MultipartFile file = multiRequest.getFile(ite.next());
                ServiceResult serviceResult = coachService.insertCoachPhoto(file, staff, coachId);
                webResult.putAll(serviceResult);
            }
        }
        return webResult;
    }

    /**
     * 选择关联员工时,获取员工信息
     *
     * @param staffId
     * @return
     */
    @RequestMapping("/queryStaffInfo")
    @ResponseBody
    public WebResult queryStaffInfo(Long staffId) {
        Staff staff = staffService.findByStaffId(staffId);
        return new WebResult().set("staff", staff);
    }

    /**
     * 将教练状态置为失效
     *
     * @param staff
     * @param coachId
     * @return
     */
    @RequestMapping("/delete")
    @ResponseBody
    public WebResult delete(LoginStaff staff, @RequestParam(value = "coachId") Long coachId) {
        WebResult webResult = new WebResult();
        int changeNum = coachService.updateExpireCoach(coachId, staff);
        if (changeNum > 0) {
            webResult.setMessage("教练删除成功");
        } else {
            webResult.setError(1);
            webResult.setMessage("教练删除失败");
        }
        return webResult;
    }

    /**
     * 修改或新增教练基础信息
     *
     * @param staff
     * @param coachId
     * @param coachName
     * @param image
     * @param honor
     * @param strength
     * @param staffId
     * @param gender
     * @param mobileNum
     * @param idCardNum
     * @param managerId
     * @return
     */
    @RequestMapping("/submitBaseInfo")
    @ResponseBody
    public WebResult submitBaseInfo(LoginStaff staff,
                                    @RequestParam(value = "coachId", required = false) Long coachId,
                                    @RequestParam(value = "coachName") String coachName,
                                    @RequestParam(value = "image", required = false) String image,
                                    @RequestParam(value = "honor", required = false) String honor,
                                    @RequestParam(value = "strength", required = false) String strength,
                                    @RequestParam(value = "staffId", required = false) Long staffId,
                                    @RequestParam(value = "gender") String gender,
                                    @RequestParam(value = "mobileNum", required = false) String mobileNum,
                                    @RequestParam(value = "isSiftCoach", required = false) String isSiftCoach,
                                    @RequestParam(value = "sort", required = false) String sort,
                                    @RequestParam(value = "idCardNum", required = false) String idCardNum,
                                    @RequestParam(value = "managerId", required = false) Long managerId) {
        if (staff.getInstId() == null) {
            return new WebResult(1, "员工没有管理场馆");
        }

        if (coachId == null) {
            //检验关联员工是否重复
            if (staffId != null) {
                Coach oldCoach = coachService.findVenueCoachByStaffId(staffId, staff.getInstId());
                if (oldCoach != null) {
                    return new WebResult(1, "请重新选择关联员工，该员工已存在关联教练信息");
                }
            }
        } else {
            Coach oldCoach = coachService.findCoachByCoachId(coachId);
            if (!oldCoach.getStaffId().equals(staffId)) {
                Coach theCoach = coachService.findVenueCoachByStaffId(staffId, staff.getInstId());
                if (theCoach != null) {
                    return new WebResult(1, "请重新选择关联员工，该员工已存在关联教练信息");
                }
            }
        }

        WebResult webResult = new WebResult();
        webResult.putAll(coachService.submitCoachBaseInfo(staff, coachId, coachName, image, honor, strength, staffId, gender, mobileNum, idCardNum, managerId, isSiftCoach, sort));
        if (webResult.getError() == 0) {
            webResult.setRedirect("/coachManage/init");
        }
        return webResult;
    }

    /**
     * 处理教练的工作内容信息(增删改)
     *
     * @param staff
     * @param coachId
     * @param staffId
     * @param sparringTag
     * @param price
     * @param serviceString
     * @param workActionString
     * @return
     */
    @RequestMapping("/submitContentInfo")
    @ResponseBody
    public WebResult submitContentInfo(LoginStaff staff,
                                       @RequestParam(value = "coachId") Long coachId,
                                       @RequestParam(value = "staffId") Long staffId,
                                       @RequestParam(value = "sparringTag") String sparringTag,
                                       @RequestParam(value = "price", required = false) Integer price,
                                       @RequestParam(value = "serviceString", required = false) String serviceString,
                                       @RequestParam(value = "serviceLevelString", required = false) String serviceLevelString,
                                       @RequestParam(value = "workActionString", required = false) String workActionString,
                                       @RequestParam Long[] courseIds) {
        coachService.saveWorkInfo(staff, coachId, staffId, sparringTag, price, serviceString, serviceLevelString, workActionString, courseIds);
        return new WebResult();
    }

    /**
     * 根据运动项目查询该私教课程集合
     *
     * @param serviceIds
     * @param coachId
     * @return
     */
    @RequestMapping(value = "/queryPersonalCourse",method = RequestMethod.GET)
    @ResponseBody
    public WebResult queryPersonalCourse(String[] serviceIds, @RequestParam(value = "coachId") Long coachId,
                                         @RequestParam(value = "privateTag") String privateTag) {
        DataMap dataMap = coachService.queryPersonalCourse(serviceIds, coachId, privateTag);
        WebResult webResult = new WebResult();
        webResult.putAll(dataMap);
        return webResult;
    }
}
