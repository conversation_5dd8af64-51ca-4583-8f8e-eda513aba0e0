@import "../../common/element-theme-xports/element-variables";
@import "../../frame/src/frame";
@import "../../common/scss/umeditor";
@import "./styles/common.scss";
@import "./pages/StudentList.scss";
@include setInputPlaceholderColor(#c3c8cf);
@include setTextareaPlaceholderColor(#c3c8cf);

//表单部分
.op-con{
  padding: 20px;
}
.form-row{
  font-size: 0px;
  display: flex;
  &>*{
    font-size: 14px;
  }
  &>label{
    width: 138px;
    text-align: right;
    display: inline-block;
    line-height: 34px;
    &.sm-label{
      width: auto;
    }
    &.check-label{
      text-align: left;
    }
  }
}
.form-line {
  @include formLine(140px);
  .form-field, .form-label {
      line-height: 36px;
  }
}
.check-label{
  span{
    line-height: 15px;
  }
  span,input{
    vertical-align: middle;
  }
  input{
    margin-right: 5px;
  }
}
.check-label + .check-label{
  margin-left : 20px;
}
.check-input {
  display: inline-block;
  height: 32px;
  vertical-align: bottom;
}
.form-row + .form-row{
  margin-top: 20px;
}
//必填样式
.required:before{
  content: '*';
  vertical-align: middle;
  margin: -20px 6px 0 0;
  color: red;
}
.btn-group {
  width: 120px !important;
}
//腾讯地图
.map-modal {
  .inner-box {
    background: #fff;
    padding: 5px;
    border-radius: 3px;
  }
  .close-modal-btn {
    background: #666;
    width: 35px;
    height: 35px;
    position: absolute;
    right: 5px !important;
    top: 5px !important;
    text-align: center;
    .iconfont {
      margin-top: 5px;
      display: inline-block;
    }
  }
}

.bottom-btns-basic{
  margin: 40px 0 0 140px;
  button + button{
    margin-left: 10px;
  }
}

.map-wrap {
  height: 470px;
}

.map-loc-digest {
  border: 1px solid #ddd;
  padding: 10px;
  padding-top: 2px;
  display: inline-block;
  margin-top: 15px;
  .map-wrap {
    width: 240px;
    height: 240px;
  }
}
//上传按钮
.uploadBtn {
  width: 100px;
  height: 36px;
  padding: 8px 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}


//start 上传图片
.upload-area{
  width: 120px;
  border: 1px dashed #ddd;
  padding: 10px;
  .upload-area-bc{
    background-color: #f2f3f6;
    height: 120px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    img{
      //max-width: 100%;
      //max-height: 100%;
    }
    input{
      position: absolute;
      width: 120px;
      height: 120px;
      
      opacity: 0;
      top: 0;
      left: 0;
    }
    .icon-jiahao2{
      font-size: 28px;
      color: #ccc;
    }
  }
}

.link-a { 
  text-decoration:underline;
  color:#1fa2f5;
}