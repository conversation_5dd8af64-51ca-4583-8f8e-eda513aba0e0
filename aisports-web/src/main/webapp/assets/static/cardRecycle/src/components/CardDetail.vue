<template>
    <div class="card-detail-page title-box">
        <h1 style="display: block">
            <span>详情</span>
            <a
                href="#/"
                class="btn-return"
                title="后退"
                @click.prevent="goprev">
                <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
            </a>
        </h1>
        <div class="info-wrap">
            <div class="row">
                <div class="col-xs-4 info-item">
                    <span class="info-item-hd">流水号：</span>
                    <span class="info-item-bd">{{ info.id }}</span>
                </div>
                <div class="col-xs-4 info-item">
                    <span class="info-item-hd">回收数量：</span>
                    <span class="info-item-bd">{{ info.num || 0 }}张</span>
                </div>

                <div class="col-xs-4 info-item">
                    <span class="info-item-hd">状态：</span>
                    <span
                        v-if="info.state === '2'"
                        class="info-item-bd status-btn-md status-success">成功</span>
                    <span
                        v-if="info.state === '3'"
                        class="info-item-bd status-btn-md status-error">失败</span>
                    <span
                        v-if="info.state === '1'"
                        class="info-item-bd status-btn-md status-blue">未处理</span>
                    <span
                        v-if="info.state === '0'"
                        class="info-item-bd status-btn-md status-default">取消</span>
                </div>
                <div
                    class="col-xs-4 info-item">
                    <span class="info-item-hd">提交时间：</span>
                    <span class="info-item-bd">{{ info.createTime }}</span>
                </div>
                <div class="col-xs-4 info-item">
                    <span class="info-item-hd">操作员工：</span>
                    <span class="info-item-bd">{{ info.staffName }}</span>
                </div>
                <div class="col-xs-4 info-item">
                    <span class="info-item-hd">备注：</span>
                    <span class="info-item-bd">{{ info.remark }}</span>
                </div>
            </div>
        </div>
        <div class="list-box">

        </div>
        <query-area
            :btn-text="'查询'"
            @query="getCardList">
            <el-select
                v-model="state"
                size="medium"
                placeholder="请选择状态">
                <el-option
                    value=""
                    label="全部状态"></el-option>
                <el-option
                    v-for="item in paramList"
                    :value="item.paramKey"
                    :key="item.paramKey"
                    :label="item.paramValue"
                ></el-option>
            </el-select>
            <input
                v-model="ecardNo"
                type="text"
                class="normal"
                placeholder="请输入卡号"
                @keydown.enter="getCardList">
            <button
                class="btn btn-md btn-default add-btn-gray"
                @click="exportList">导出</button>
        </query-area>
        <result-area>
            <xc-table>
                <xc-thead>
                    <th>卡号</th>
                    <th>提交时间</th>
                    <th>状态</th>
                    <th>执行结果</th>
                </xc-thead>
                <xc-body>
                    <template v-if="cardRecycleList.length">
                        <tr
                            v-for="(item, index) in cardRecycleList.slice((pageNo-1)*pageSize, (pageNo-1)*pageSize + pageSize)"
                            :key="index">
                            <td>{{ item.cardNo }}</td>
                            <td>{{ item.createTime }}</td>
                            <td>
                                <span
                                    v-if="item.state === '2'"
                                    class="status-btn-md status-success">成功</span>
                                <span
                                    v-if="item.state === '3'"
                                    class="status-btn-md status-error">失败</span>
                                <span
                                    v-if="item.state === '1'"
                                    class="status-btn-md status-blue">未处理</span>
                                <span
                                    v-if="item.state === '0'"
                                    class="status-btn-md status-default">取消</span>
                            </td>
                            <td>{{ item.result }}</td>
                        </tr>
                    </template>
                    <tr v-else>
                        <td colspan="4">暂无数据</td>
                    </tr>
                </xc-body>
            </xc-table>
            <pagination
                v-if="cardRecycleList.length"
                :current-page="pageNo"
                :page-size="pageSize"
                :total="total"
                @changepage="changePage"
                @changepagesize="changePageSize"></pagination>
            <div
                v-if="successNum || failNum"
                class="result-detail">
                <span>合计：成功{{ successNum }}条</span>
                <span>失败{{ failNum }}条</span>
            </div>
        </result-area>
    </div>
</template>
<script>
import {
    queryDetail,
    queryStaticParamList,
} from '../utils/api'
import Vue from 'vue'
import XcTable from 'vue-components/XcTable'
import ListPage from 'vue-components/ListPage'
import Pagination from 'vue-components/Pagination.vue'
import ButtonWithSelect from 'vue-components/ButtonWithSelect.vue'
import { filter, map } from 'lodash'
import moment from 'moment'
import 'utils/confirm'
import mAlert from 'mAlert'

const settings = require('aisportsConfig');

Vue.use(ListPage)
Vue.use(XcTable)
export default {
    components: {
        pagination: Pagination,
    },
    data() {
        return {
            pageNo: 1,
            pageSize: 10,
            total: 0,

            id: '',
            info: {},
            cardRecycleList: [],
            paramList: [],
            state: '',
            ecardNo: '',
            cardRecycleDetailList: [],

            successNum: 0,
            failNum: 0,
        }
    },
    mounted() {
        this.id = this.$route.query.id
        this.getDetail()
        this.getParamList()
    },
    methods: {
        goprev() {
            vueRouter.go(-1)
        },
        changePage(val) {
            this.pageNo = val
        },
        changePageSize(val) {
            this.pageSize = val
        },
        getDetail() {
            queryDetail(this.id).then((data) => {
                this.info = data.cardRecycle || {}
                this.info.createTime = moment(this.info.createTime).format('YYYY/MM/DD HH:mm:ss')
                this.total = data.cardRecycleDetailList.length
                this.cardRecycleDetailList = map(data.cardRecycleDetailList, m => ({
                    ...m,
                    createTime: moment(m.createTime).format('YYYY/MM/DD HH:mm:ss'),
                }))
                this.cardRecycleList = [...this.cardRecycleDetailList]

                this.successNum = filter(data.cardRecycleDetailList, m => m.state === '2').length
                this.failNum = filter(data.cardRecycleDetailList, m => m.state === '3').length
            })
        },
        getParamList() {
            queryStaticParamList().then(({ paramList }) => {
                this.paramList = paramList || []
            })
        },
        getCardList() {
            this.pageNo = 1
            this.pageSize = 10
            let list = []
            list = filter(this.cardRecycleDetailList, m => (this.state ? m.state === this.state : m.state !== ''))

            list = filter(list, m => (this.ecardNo ? m.cardNo === this.ecardNo || m.cardNo.slice(0, this.ecardNo.length) === this.ecardNo : m.cardNo !== ''))
            this.cardRecycleList = list
            this.total = this.cardRecycleList.length
        },
        exportList() {
            window.open(`${settings.urlPrefix}/cardRecycle/exportRecycleList?id=${this.id}&state=${this.state}&ecardNo=${this.ecardNo}`)
        },
    },
}
</script>
