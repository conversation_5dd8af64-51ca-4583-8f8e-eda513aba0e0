<template>
  <div>
    <div class="detail-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/counterManage' }">
          海康摄像头管理
        </el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/counterDetail' }">
          详情
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <el-tabs :tab-position="tabPosition">
      <el-tab-pane label="基本信息">
        <el-form :model="camera" ref="basicForm" label-width="100px" :rules="rules" label-suffix=" : ">
          <el-form-item label="编号" prop="cameraId">
            <el-input v-model="camera.cameraId"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="camera.name"></el-input>
          </el-form-item>
          <el-form-item label="账号" prop="accountId">
            <el-select
              placeholder="请选择账号"
              v-model="camera.accountId"
              :disabled="camera.id!=='' && camera.accountId !== ''"
              filterable>
              <el-option
                v-for="account in accountList"
                :key="account.accountId"
                :label="account.account"
                :value="account.accountId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属租户" prop="tenantId">
            <el-select
              placeholder="请选择租户"
              v-model="camera.tenantId"
              :disabled="camera.id!=='' && camera.tenantId !==''"
              @change="changeTenant"
              filterable>
              <el-option
                v-for="tenant in tenantList"
                :key="tenant.tenantId"
                :label="tenant.tenantName"
                :value="tenant.tenantId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属中心" prop="centerId">
            <el-select
              placeholder="请选择中心"
              v-model="camera.centerId"
              @change="changeCenter"
              clearable
              filterable>
              <el-option
                v-for="center in centerList"
                :key="center.centerId"
                :label="center.centerName"
                :value="center.centerId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="归属场馆" prop="venueId">
            <el-select
              placeholder="请选择场馆"
              v-model="camera.venueId"
              @change="changeVenue"
              clearable
              filterable>
              <el-option
                v-for="venue in venueList"
                :key="venue.venueId"
                :label="venue.venueName"
                :value="venue.venueId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所在区域" prop="areaId">
            <el-select
              placeholder="请选择区域"
              v-model="camera.areaId"
              clearable
              filterable>
              <el-option
                v-for="area in areaList"
                :key="area.areaId"
                :label="area.areaName"
                :value="area.areaId">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="SysCode" prop="sysCode">
            <el-input v-model="camera.sysCode"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="save()">保存</el-button>
            <el-button @click="cancel">取消</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import {center, camera, param} from '../api'

  export default {
    data() {
      return {
        tenantList: [],
        centerList: [],
        venueList: [],
        accountList: [],
        areaList: [],
        tabPosition: 'top',
        camera: {
          id: '',
          cameraId: '',
          name: '',
          centerId: '',
          venueId: '',
          accountId: '',
          areaId: '',
          tenantId: '',
          sysCode: ''
        },
        rules: {
          cameraId: [
            {required: true, message: '请输入编码', trigger: 'blur'},
            {min: 3, max: 20, message: '编码长度必须大于3位', trigger: 'blur'}
          ],
          name: [
            {required: true, message: '请输入名称', trigger: 'blur'}
          ],
          accountId: [
            {required: true, message: '请选择账号', trigger: 'blur'}
          ],
          tenantId: [
            {required: true, message: '请选择归属租户', trigger: 'change'}
          ],
          sysCode: [
            {required: true, message: '请输入sysCode', trigger: 'blur'}
          ],
          centerId: [
            {required: true, message: '请选择中心', trigger: 'change'}
          ],
          venueId: [
            {required: true, message: '请选择场馆', trigger: 'change'}
          ]
        }
      }
    },
    methods: {
      getArgs() {
        let promiseAll = Promise.all([
          center.getTenantList()
            .then(({data = {}}) => {
              this.tenantList = data.tenantList || []
            }),
          camera.findAccounts()
            .then(({data = {}}) => {
              this.accountList = data.accounts || []
            })
        ]);
        return promiseAll
      },
      findById() {
        if (this.camera.id) {
          camera.findDetail({id: this.camera.id})
            .then(this.handleDetail)
        }
      },
      getCenters() {
        if (this.camera.tenantId) {
          param.findCetners({"tenantId": this.camera.tenantId})
            .then(({data = {}}) => {
              this.centerList = data.result || []
            })
        }
      },
      getVenues() {
        if (this.camera.centerId) {
          param.findVenues({"centerId": this.camera.centerId})
            .then(({data = {}}) => {
              this.venueList = data.result || []
            })
        }
      },
      getAreaList() {
        if (this.camera.venueId) {
          camera.findAreaList({
            "venueId": this.camera.venueId,
            "tenantId": this.camera.tenantId
          }).then(({data = {}}) => {
              this.areaList = data.result || []
            })
        }
      },
      changeTenant() {
        this.camera.centerId = ''
        this.camera.venueId = ''
        this.getCenters()
      },
      changeCenter() {
        this.camera.venueId = ''
        this.getVenues()
      },
      changeVenue() {
        this.camera.areaId = ''
        this.getAreaList()
      },
      handleDetail({data}) {
        this.camera.id = data.camera.id || ''
        this.camera.cameraId = data.camera.cameraId || ''
        this.camera.name = data.camera.name || ''
        this.camera.tenantId = data.camera.tenantId || ''
        this.camera.centerId = data.camera.centerId || ''
        this.camera.venueId = data.camera.venueId || ''
        this.camera.accountId = data.camera.accountId || ''
        this.camera.sysCode = data.camera.sysCode || ''
        this.camera.areaId = data.camera.areaId || ''
        this.getCenters()
        this.getVenues()
        this.getAreaList()
      },
      save() {
        this.$refs['basicForm'].validate((valid) => {
          if (valid) {
            this.$refs['basicForm'].clearValidate();
            return camera.save(this.camera).then(({data = {}}) => {
              this.camera.id = data.id || ''
              this.$message.success("保存成功！")
              this.findById()
            })
          } else {
            return false;
          }
        })
      },
      cancel() {
        this.$router.go(-1)
      },
      resetForm(formName) {
        this.$refs[formName].resetFields();
      }
    },
    mounted() {
      this.camera.id = this.$route.query.id || ''
      this.getArgs().then(this.findById)
    }
  };
</script>
