<template>
  <cover-box ref="box" :width="600" :title="operation === 'add' ? '新增团体票' : '修改团体票信息'" @submit="submit">
    <!-- 票区库存 -->
    <dl class="f-line">
      <dt class="f-label">票区库存</dt>
      <dd class="f-field">
        <el-select v-model="ticketInfo.stockId" placeholder="请选择" @change="getStockInfo" :disabled="operation === 'edit'">
          <el-option v-for="area in stockList" :key="area.id" :label="area.name" :value="area.id"> </el-option>
        </el-select>
        <!-- <a href="javascript:;" @click="addTicketArea">新增库存</a> -->
      </dd>
      <dd class="f-field" v-if="stockInfo.totalAmount">
        <span>总座位数：{{ stockInfo.totalAmount }} 已占：{{ stockInfo.totalAmount - stockInfo.remainAmount }} 剩余：{{ stockInfo.remainAmount }}</span>
      </dd>
    </dl>

    <!-- 票类型 -->
    <!-- <dl class="f-line">
      <dt class="f-label">票类型</dt>
      <dd class="f-field">
        <el-select v-model="ticketInfo.ticketKind" placeholder="请选择" disabled>
          <el-option v-for="type in ticketTypes" :key="type.value" :label="type.label" :value="type.value"> </el-option>
        </el-select>
      </dd>
    </dl> -->
    <dl class="f-line" v-if="showRule">
      <dt class="f-label">*身份证地址码</dt>
      <dd class="f-field">
        <input v-model="ticketInfo.rule" class="normal" required placeholder="请输入身份证前6位地址码" />
      </dd>
    </dl>

    <!-- 成年人/未成年人 -->
    <dl class="f-line" v-if="showAdultNum">
      <dt class="f-label">*成年人</dt>
      <dd class="f-field">
        <input v-model="ticketInfo.adultNum" class="normal" required placeholder="请输入" />
        <span>人</span>
      </dd>
    </dl>
    <dl class="f-line" v-if="showAdultNum">
      <dt class="f-label">*未成年人</dt>
      <dd class="f-field">
        <input v-model="ticketInfo.minorNum" class="normal" required placeholder="请输入" />
        <span>人</span>
      </dd>
    </dl>

    <!-- 票名称 -->
    <dl class="f-line">
      <dt class="f-label">*票名称</dt>
      <dd class="f-field">
        <input v-model="ticketInfo.performTicketName" class="normal" required placeholder="请输入" />
        <div v-if="errors.performTicketName" class="error">{{ errors.performTicketName }}</div>
      </dd>
    </dl>
    <!-- 团体名称 -->
    <dl class="f-line">
      <dt class="f-label">*团体名称</dt>
      <dd class="f-field">
        <input v-model="ticketInfo.teamName" class="normal" required placeholder="请输入" />
      </dd>
    </dl>

    <!-- 票价格 -->
    <dl class="f-line">
      <dt class="f-label">*票价格</dt>
      <dd class="f-field">
        <input v-model="ticketInfo.price" class="normal" required placeholder="请输入" />
        <span class="unit">元</span>
      </dd>
    </dl>

    <!-- 核销区域 -->
    <dl class="f-line">
      <dt class="f-label">核销区域</dt>
      <dd class="f-field">
        <el-select v-model="ticketInfo.siteId" placeholder="请选择">
          <el-option v-for="area in siteList" :key="area.id" :label="area.name" :value="area.id"> </el-option>
        </el-select>
        <!-- <a href="javascript:;" @click="addValidationArea">新增核销区域（服务点）</a> -->
      </dd>
    </dl>

    <!-- 权益 -->
    <dl class="f-line">
      <dt class="f-label">权益</dt>
      <dd class="f-field">
        <el-select v-model="ticketInfo.rightIds" multiple placeholder="请选择">
          <el-option v-for="right in rightsList" :key="right.value" :label="right.label" :value="right.value"> </el-option>
        </el-select>
        <!-- <a href="javascript:;" @click="addRights">新增权益</a> -->
      </dd>
    </dl>
  </cover-box>
</template>

<script>
import CoverBox from "vue-components/CoverBox.vue";
import * as service from "./service";
import { showSuccess } from "mAlert";
import "xValidate";

const defaultTicket = {
  stockId: "",
  ticketKind: 8,
  adultNum: "",
  minorNum: "",
  performTicketName: "",
  teamName:'',
  price: "",
  siteId: "",
  rightIds: [],
};
export default {
  components: {
    CoverBox,
  },
  props: ["performId"],
  /**
   * stockId 库存id
   *ticketKind 票种类 1-普通；2-套票;3-客场球迷票
   *adultNum 成人数量
   *minorNum 儿童数量
   *rule   客场的身份证号
   *siteId 核销服务点
   *rightIds  权益id 逗号拼接，
   */
  data() {
    return {
      operation: "add",
      ticketInfo: {
        stockId: "",
        ticketKind: 8,
        adultNum: "",
        rule: "",
        minorNum: "",
        performTicketName: "",
        teamName:'',
        price: "",
        siteId: "",
        rightIds: [],
      },
      stockList: [], // 票区库存列表
      ticketTypes: [
        // { label: "普通票", value: 1 },
        // { label: "套票", value: 2 },
        // { label: "客场球迷票", value: 3 },
        // { label: "外地成人票", value: 4 },
        // { label: "外地套票", value: 5 },
        // { label: "儿童票", value: 6 },
        // { label: "外地儿童票", value: 7 },
        { label: "团体票", value: 8 },
      ], // 票类型列表 1-普通；2-套票;3-客场球迷票
      validationAreas: [], // 核销区域列表
      rightsList: [], // 权益列表
      totalSeats: 1000, // 总座位数
      occupiedSeats: 100, // 已占座位数
      remainingSeats: 900, // 剩余座位数
      stockInfo: {
        totalAmount: 0,
        remainAmount: 0,
        occupiedSeats: 0,
      },
      siteList: [],
      errors: {}, // 错误信息
    };
  },
  computed: {
    showRule() {
        let list = [3,4,5,7]
        return list.includes(this.ticketInfo.ticketKind)
    },
    showAdultNum() {
        let list = [2,5]
        return list.includes(this.ticketInfo.ticketKind)
    },
  },
  methods: {
    show(data) {
      if (!data) {
        this.operation = "add";
        this.ticketInfo = Object.assign({}, defaultTicket);
      } else {
        this.operation = "edit";
        this.ticketInfo = Object.assign({}, data, { price: m2front(data.price),ticketKind:Number(data.ticketKind) });
      }
      console.log(data,'123456');
      this.$refs.box.show();
      this.initInfo();
    },
    initInfo() {
      this.getRightsList();
      this.getStockList();
      this.getSiteList();
    },
    getRightsList() {
      service.getRightsList({ performId: this.performId }, (res) => {
        console.log(res);
        this.rightsList = res.rightsList.map((item) => {
          return { value: item.id, label: item.name };
        });
      });
    },
    getStockList() {
      service.getStockList(this.performId, (res) => {
        console.log(res);
        this.stockList = res.stockList;
        // this.validationAreas = res.validationAreas.map((item) => {
        //     return { value: item.id, label: item.name };
        // })
      });
    },
    getSiteList() {
      service.getSiteList({}, (res) => {
        this.siteList = res.siteList;
      });
    },
    getStockInfo(item) {
      this.stockInfo = this.stockList.find((item) => {
        if (item.id === this.ticketInfo.stockId) {
          return item;
        }
      });
      console.log(this.stockInfo, "123132");
    },
    hide() {
      this.$refs.box.hide();
    },
    addTicketArea() {
      // 新增票区库存的逻辑
      console.log("新增票区库存");
    },
    addValidationArea() {
      // 新增核销区域的逻辑
      console.log("新增核销区域");
    },
    addRights() {
      // 新增权益的逻辑
      console.log("新增权益");
    },
    submit() {
      if (!$(this.$el).validate()) {
        return;
      }
      // 检查票名称是否重复
      if (this.checkTicketNameDuplicate()) {
        this.errors.performTicketName = "票名称重复";
        return;
      } else {
        this.errors.performTicketName = "";
      }
      console.log(this.ticketInfo, "this.ticketInfo")
      let params = {
        ...this.ticketInfo,
        price: m2back(this.ticketInfo.price),
        performId: this.performId,
        rightIds: this.ticketInfo.rightIds.join(","),
      };
      service.insertOrUpdatePerformTicket(params, (res) => {
        if (this.operation === "add") {
          showSuccess("添加成功");
        } else {
          showSuccess("修改成功");
        }

        this.$emit("update");
        this.hide();
      });
    },
    checkTicketNameDuplicate() {
      // 检查票名称是否重复的逻辑
      // 这里只是一个示例，实际需要根据你的业务逻辑实现
      return this.ticketInfo.performTicketName === "已存在的票名称";
    },
  },
};
</script>
