/**
 * Created by nightost on 15/9/17.
 * 大屏获取天气
 */
define(function(require,exports,module){
    var $ = require('jquery'),
        _ = require('underscore'),
        weatherIns,
        chineseDaysStr = ['星期天','星期一','星期二','星期三','星期四','星期五','星期六'];
        args = {
            serverUrl : "",
            city : ''
        };
    //天气实力
    weatherIns = {
        timer : null,
        weatherData : null,
        container : null,
        template : null,
        block : null,
        init : function(){
            var  timer = this.timer;
            this.block = $('.block.weather');
            args.serverUrl = this.block.attr('data-url')||'';
            args.city = this.block.attr('data-city')||'';
            this.template = _.template($('#weather-temp').html());
            if(this.block.hasClass('wts')){
                this.container = this.block.find('.block-con');
            }
            else{
                this.container = this.block;
            }
            this.getWeatherData();
            if(timer === null){
                timer = setInterval($.proxy(this.getWeatherData,this),1000*60*60*3);
            }
        },
        /**
         * 获取天气
         */
        getWeatherData : function(){
            var _this = this;
            $.ajax({
                url : args.serverUrl,
                noAutoCover : true,
                type:'GET',
                success : function(msg){
                    _this.weatherData = msg;
                    _this.renderWeather();
                },
                error : function(){

                }
            });
        },
        /**
         * render weather
         */
        renderWeather : function() {
            if (this.container === null || this.container.length === 0)return;
            var container = this.container,
                weatherData = this.weatherData;
                tempFn = this.template;
            this.getCurDate();
            this.getCurPlace();
            container.find('.weather-con').html(tempFn(weatherData));
            container.find('.place-bg img').attr('src','/assets/sea_modules/static/images/' + this.weatherData.place + '1.jpg')
            .on('error', function() {
                $(this).attr('src', '/assets/sea_modules/static/images/weather-bg-default.jpg')
            })
        },
        /**
         * get cur date
         */
        getCurDate : function(){
            var curDay,dateIns,day,month,date;
            //平台天气不靠谱
            /*try{
                curDay = this.weatherData.weather.daily[0].date;
            }
            catch(e){
                throw new Error('天气数据格式可能变更.')
            }
             dateIns = new Date(curDay);
            */
            //使用本地时间
            dateIns = new Date();
            day = dateIns.getDay();
            month = dateIns.getMonth() + 1;
            date = dateIns.getDate();
            this.weatherData.dateInfo = {
                day : chineseDaysStr[day],
                date : month + '月' + date + '日'
            }
        },
        /**
         * get place
         */
        getCurPlace : function(){
            this.weatherData.place = this.block.attr('data-city');
        }
    };
    module.exports = weatherIns;
});
