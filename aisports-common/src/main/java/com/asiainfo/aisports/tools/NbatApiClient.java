package com.asiainfo.aisports.tools;

import com.aliyun.oss.ServiceException;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.sf.json.JSONObject;
import org.apache.http.protocol.HTTP;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.DigestUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * 宁波奥体签名工具
 *
 * @auther: zhouwei
 * @date: 2021/7/19 19:04
 */
public class NbatApiClient {

    private static final Logger LOG = LoggerFactory.getLogger(NbatApiClient.class);
    private static final Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

    private String appKey;
    private String parkId;
    private String carTypeId;
    private String carModelId;
    private String parkInfoUrl;
    private String modifyCarInfoUrl;
    private String delCarInfoUrl;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getParkId() {
        return parkId;
    }

    public void setParkId(String parkId) {
        this.parkId = parkId;
    }

    public String getCarTypeId() {
        return carTypeId;
    }

    public void setCarTypeId(String carTypeId) {
        this.carTypeId = carTypeId;
    }

    public String getCarModelId() {
        return carModelId;
    }

    public void setCarModelId(String carModelId) {
        this.carModelId = carModelId;
    }

    public String getParkInfoUrl() {
        return parkInfoUrl;
    }

    public void setParkInfoUrl(String parkInfoUrl) {
        this.parkInfoUrl = parkInfoUrl;
    }

    public String getModifyCarInfoUrl() {
        return modifyCarInfoUrl;
    }

    public void setModifyCarInfoUrl(String modifyCarInfoUrl) {
        this.modifyCarInfoUrl = modifyCarInfoUrl;
    }

    public String getDelCarInfoUrl() {
        return delCarInfoUrl;
    }

    public void setDelCarInfoUrl(String delCarInfoUrl) {
        this.delCarInfoUrl = delCarInfoUrl;
    }

    private void wrapParams(Map<String, Object> requestParams, Map<String, Object> signParams) {
        Map<String, Object> wrappedParam = new HashMap<>(signParams);
        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, Object> paramEntry : wrappedParam.entrySet()) {
            String paramName = paramEntry.getKey();
            Object paramValue = paramEntry.getValue();
            if (paramValue != null) {
                if (builder.length() > 0) {
                    builder.append("&");
                }
                builder.append(paramName).append("=").append(paramValue);
            }
        }
        try {
            String md5AppKey = DigestUtils.md5DigestAsHex(getAppKey().getBytes("UTF-8")).toUpperCase();
            builder.append("&").append("appKey").append("=").append(md5AppKey);
            String sign = DigestUtils.md5DigestAsHex(builder.toString().getBytes("UTF-8")).toUpperCase();
            requestParams.put("sign", sign);
            requestParams.put("appKey", md5AppKey);
        } catch (UnsupportedEncodingException e) {
            LOG.error("", e);
        }
    }

    public String postJson(String requestUrl, Map<String, Object> requestParams, Map<String, Object> signParams) {
        HttpRequest request = HttpRequest.post(requestUrl);
        request.header(HTTP.CONTENT_TYPE, "application/json;charset=utf-8");
        wrapParams(requestParams, signParams);
        request.send(gson.toJson(requestParams));
        LOG.debug(request + gson.toJson(requestParams));
        if (request.ok()) {
            return request.body();
        }
        throw new ServiceException("推送车牌信息失败:" + gson.toJson(requestParams));
    }

    public static NbatApiClient build(JSONObject apiParams) {
        if (apiParams == null||!apiParams.containsKey("appKey")
                || !apiParams.containsKey("carModelId")
                || !apiParams.containsKey("carTypeId")
                || !apiParams.containsKey("parkId")
                || !apiParams.containsKey("parkInfoUrl")
                || !apiParams.containsKey("delCarInfoUrl")
                || !apiParams.containsKey("modifyCarInfoUrl")
        ) {
            throw new ServiceException("推送车牌配置缺失");
        }
        NbatApiClient nbatApiClient = new NbatApiClient();
        nbatApiClient.setAppKey(apiParams.getString("appKey"));
        nbatApiClient.setCarModelId(apiParams.getString("carModelId"));
        nbatApiClient.setCarTypeId(apiParams.getString("carTypeId"));
        nbatApiClient.setParkId(apiParams.getString("parkId"));
        nbatApiClient.setParkInfoUrl(apiParams.getString("parkInfoUrl"));
        nbatApiClient.setDelCarInfoUrl(apiParams.getString("delCarInfoUrl"));
        nbatApiClient.setModifyCarInfoUrl(apiParams.getString("modifyCarInfoUrl"));
        return nbatApiClient;
    }

    @Override
    public String toString() {
        return "NbatApiClient{" +
                "appKey='" + appKey + '\'' +
                ", parkId='" + parkId + '\'' +
                ", carTypeId='" + carTypeId + '\'' +
                ", carModelId='" + carModelId + '\'' +
                ", parkInfoUrl='" + parkInfoUrl + '\'' +
                ", modifyCarInfoUrl='" + modifyCarInfoUrl + '\'' +
                ", delCarInfoUrl='" + delCarInfoUrl + '\'' +
                '}';
    }
}