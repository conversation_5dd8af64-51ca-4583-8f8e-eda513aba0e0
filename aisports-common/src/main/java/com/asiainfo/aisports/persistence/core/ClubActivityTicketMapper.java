package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.ClubActivityTicket;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface ClubActivityTicketMapper {
    @Delete({
        "delete from club_activity_ticket",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into club_activity_ticket (id, activity_id, ",
        "ticket_id, room_id, ",
        "state, tenant_id, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{id,jdbcType=BIGINT}, #{activityId,jdbcType=BIGINT}, ",
        "#{ticketId,jdbcType=BIGINT}, #{roomId,jdbcType=BIGINT}, ",
        "#{state,jdbcType=CHAR}, #{tenantId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(ClubActivityTicket record);

    @Select({
        "select",
        "id, activity_id, ticket_id, room_id, state, tenant_id, create_time, create_staff_id, ",
        "update_time, update_staff_id",
        "from club_activity_ticket",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="activity_id", property="activityId", jdbcType=JdbcType.BIGINT),
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
        @Result(column="room_id", property="roomId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    ClubActivityTicket selectByPrimaryKey(Long id);

    @Update({
        "update club_activity_ticket",
        "set activity_id = #{activityId,jdbcType=BIGINT},",
          "ticket_id = #{ticketId,jdbcType=BIGINT},",
          "room_id = #{roomId,jdbcType=BIGINT},",
          "state = #{state,jdbcType=CHAR},",
          "tenant_id = #{tenantId,jdbcType=BIGINT},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(ClubActivityTicket record);

    @Select({
        "<script>",
        "select",
        "id, activity_id, ticket_id, room_id, state, tenant_id, create_time, create_staff_id, update_time, update_staff_id",
        "from club_activity_ticket",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"activityId != null\">and activity_id = #{activityId,jdbcType=BIGINT}</if>",
        "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
        "<if test=\"roomId != null\">and room_id = #{roomId,jdbcType=BIGINT}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"tenantId != null\">and tenant_id = #{tenantId,jdbcType=BIGINT}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="activity_id", property="activityId", jdbcType=JdbcType.BIGINT),
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
        @Result(column="room_id", property="roomId", jdbcType=JdbcType.BIGINT),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="tenant_id", property="tenantId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<ClubActivityTicket> selectByFields(ClubActivityTicket param);

    @Update({
        "<script>",
        "update club_activity_ticket",
        "<set>",
        "<if test=\"activityId != null\">activity_id = #{activityId,jdbcType=BIGINT},</if>",
        "<if test=\"ticketId != null\">ticket_id = #{ticketId,jdbcType=BIGINT},</if>",
        "<if test=\"roomId != null\">room_id = #{roomId,jdbcType=BIGINT},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"tenantId != null\">tenant_id = #{tenantId,jdbcType=BIGINT},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(ClubActivityTicket record);
}