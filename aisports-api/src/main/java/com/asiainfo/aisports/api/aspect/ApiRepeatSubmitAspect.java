package com.asiainfo.aisports.api.aspect;

import com.asiainfo.aisports.annotation.ExcludeRepeat;
import com.asiainfo.aisports.annotation.IncludeRepeat;
import com.asiainfo.aisports.annotation.RepeatSubmit;
import com.asiainfo.aisports.domain.core.Staff;
import com.asiainfo.aisports.exception.RepeatSubmitException;
import com.asiainfo.aisports.tools.JacksonUtil;
import net.rubyeye.xmemcached.MemcachedClient;
import org.apache.commons.codec.digest.DigestUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.ui.ModelMap;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by zhouqq on 22/11/11
 */
@Aspect
@Component
public class ApiRepeatSubmitAspect {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiRepeatSubmitAspect.class);

    @Autowired
    MemcachedClient memcachedClient;


    @Around("@annotation(com.asiainfo.aisports.annotation.RepeatSubmit)")
    public Object checkReSubmit(ProceedingJoinPoint joinPoint) throws Throwable {


        String cacheKey = null;
        Object result;

        try {
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            RepeatSubmit annotation = method.getAnnotation(RepeatSubmit.class);
            int interval = annotation.interval();
            String message = annotation.message();
            boolean onlyInclude = annotation.onlyInclude();
            //入参
            Object[] args = joinPoint.getArgs();
            //入参名称
            String[] parameterNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
            Parameter[] parameters = method.getParameters();
            //所有的注解
            Annotation[][] parameterAnnotations = method.getParameterAnnotations();

            List<Object> candidateArgList = new ArrayList<>();
            candidateArgList.add(getMethodUrl(method));
            if (args != null) {
                for (int i = 0; i < parameters.length; i++) {
                    String parameterName = parameterNames[i];
                    Object arg = args[i];
                    Annotation[] annotations = parameterAnnotations[i];
                    if (onlyInclude) {
                        //遍历注解，找到IncludeRepeat的
                        final IncludeRepeat includeRepeat =
                                getAnnotationByType(annotations, IncludeRepeat.class);
                        if (includeRepeat == null) {
                            continue;
                        }
                    } else {
                        final ExcludeRepeat excludeRepeat =
                                getAnnotationByType(annotations, ExcludeRepeat.class);
                        if (excludeRepeat != null || ignoreArg(arg, parameterName)) {
                            continue;
                        }
                    }
                    candidateArgList.add(arg);

                }
            }
            //加入缓存，如果失败，说明正在请求该方法，抛异常
            String lockKey = DigestUtils.md5Hex(JacksonUtil.writeObj2Json(candidateArgList));
            cacheKey = "REPEAT_SUBMIT/" + lockKey;
            boolean flag = memcachedClient.add(cacheKey, interval, System.currentTimeMillis());
            if (!flag) {
                throw new RepeatSubmitException(message);
            }
        } catch (RepeatSubmitException e) {
            throw e;
        } catch (Exception e) {
            LOGGER.error("checkReSubmit,error", e);
        }
        try {
            //执行方法
            result =  joinPoint.proceed();
        } finally {
            if (cacheKey != null) {
                //方法结束后，删除缓存，用户可以继续请求该方法
                try {
                    memcachedClient.delete(cacheKey);
                } catch (Exception e) {
                    LOGGER.error("checkReSubmit,delete({}) error", cacheKey, e);
                }
            }

        }
        return result;
    }

    private static <T extends Annotation> T getAnnotationByType(final Annotation[] annotations,
                                                                final Class<T> clazz) {
        T result = null;
        for (final Annotation annotation : annotations) {
            if (clazz.isAssignableFrom(annotation.getClass())) {
                result = (T) annotation;
                break;
            }
        }
        return result;
    }

    private String getMethodUrl(Method method) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            if (request != null) {
                return request.getRequestURI();
            }
        }
        return method.getDeclaringClass().getName() + "_" + method.getName();
    }

    private boolean ignoreArg(Object param, String parameterName) {
        if (param instanceof HttpServletRequest
                || param instanceof HttpServletResponse
                || param instanceof MultipartFile
                || param instanceof MultipartFile[]
                || param instanceof ModelMap
                || param instanceof Model
                || param instanceof Staff
                || param instanceof byte[]) {
            return true;
        }
        if (parameterName.contains("base64") || parameterName.contains("Base64") || parameterName.contains("agreement")) {
            return true;
        }

        return false;
    }
}
