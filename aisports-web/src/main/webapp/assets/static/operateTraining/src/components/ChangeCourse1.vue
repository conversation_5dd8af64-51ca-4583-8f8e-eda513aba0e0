<template>
    <div class="title-box">
        <h1 style="display: block;">
            <span>换课</span>
            <a href="javascript:window.history.go(-1)" title="后退" class="btn-return">
                <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
            </a>
        </h1>
        <div class="sub-box">
            <div class="c-line">
                <div class="form-label required">课程类型</div>
                <div class="form-field">
                    <label>
                        <input v-model="privateTag" type="radio" value="0">
                        <span>培训</span>
                    </label>
                    <label>
                        <input
                            v-model="privateTag"
                            type="radio"
                            value="1">
                        <span>私教</span>
                    </label>
                </div>
            </div>

            <template v-if="privateTag==='0'">
                <div class="c-line">
                    <div class="form-label required">选择服务</div>
                    <div class="form-field">
                        <el-select v-model="serviceId" size="medium" placeholder="请选择服务">
                            <el-option v-for="item in serviceList" :key="item.serviceId" :label="item.serviceName"
                                :value="item.serviceId">
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div class="c-line">
                    <div class="form-label required">选择类型</div>
                    <div class="form-field">
                        <el-select v-validate="'required'" v-model="courseType" name="courseType" size="medium"
                            placeholder="请选择类型">
                            <el-option v-for="item in serviceArray" :key="item.typeId" :label="item.typeName"
                                :value="item.typeId">
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div v-if="newModelTag==='0'" class="c-line">
                    <div class="form-label required">选择课程</div>
                    <div class="form-field">
                        <el-select v-model="courseId" size="medium" placeholder="请选择课程">
                            <el-option v-for="item in courseList" :key="item.courseId" :label="item.courseName"
                                :value="item.courseId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="newModelTag==='1'" class="c-line">
                    <div class="form-label required">选择课程</div>
                    <div class="form-field">
                        <el-select v-model="courseId" size="medium" filterable placeholder="请选择课程">
                            <el-option v-for="item in courseList" :key="item.courseId" :label="item.courseName"
                                :value="item.courseId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="course.classTag!=='1' && course.palceTag==='1' && newModelTag==='1'" key="placeRow"
                    class="c-line">
                    <div class="form-label required">上课地点</div>
                    <div class="form-field">
                        <el-select v-validate="'required'" v-model="placeId" name="place">
                            <el-option v-for="item in placeList" :key="item.placeId" :label="item.placeName"
                                :value="item.placeId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="course.classTag==='0' && course.timeTag==='1' && newModelTag==='1'" key="timeRow"
                    class="c-line">
                    <div class="form-label required">上课时间</div>
                    <div class="form-field">
                        <el-select v-validate="'required'" :multiple-limit="timeNum" v-model="longLessonId" name="week"
                            size="medium" multiple>
                            <el-option v-for="item in tcLongLessonList" :key="item.longLessonId" :label="item.name"
                                :value="item.longLessonId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div v-if="course.classTag==='1' && newModelTag==='1'" class="c-line">
                    <div class="form-label required">选择期次</div>
                    <div class="form-field">
                        <el-select v-validate="'required'" v-model="newModelTermId" name="newModelTermId"
                            placeholder="请选择期次" size="medium">
                            <el-option v-for="item in newModelTermList" :key="item.termId" :label="item.termName"
                                :value="item.termId">
                            </el-option>
                        </el-select>
                        <el-select v-model="newModelClassId" placeholder="请选择班级" size="medium">
                            <el-option v-for="item in newModelClassList" :disabled="item.disabled" :key="item.classId"
                                :label="item.className" :value="item.classId">
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div v-if="newModelTag==='0'" class="c-line">
                    <div class="form-label required">选择课时</div>
                    <div class="form-field">
                        <el-select v-validate="'required'" v-if="course.classTag === '1'" v-model="termId" name="term"
                            size="medium" placeholder="请选择期次">
                            <el-option v-for="item in termList" :key="item.termId" :label="item.termName"
                                :value="item.termId">
                            </el-option>
                        </el-select>
                        <el-select v-validate="'required'" v-model="lessonId" size="medium" name="lesson"
                            placeholder="请选择课时">
                            <el-option v-for="item in lessonList" :key="item.lessonId" :label="item.lessonName"
                                :value="item.lessonId">
                            </el-option>
                        </el-select>
                        <el-select v-if="course.classTag === '1'" v-model="classId" name="class" size="medium"
                            placeholder="请选择班级">
                            <el-option v-for="item in classList" :key="item.classId" :label="item.className"
                                :value="item.classId">
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div v-if="course.chooseTimeAttr == '1'" class="c-line">
                    <div class="form-label required">上课时间</div>
                    <div class="form-field">
                        <el-select v-validate="'required'" v-model="courseTime" name="coursetime" multiple size="medium"
                            placeholder="请选择上课时间">
                            <el-option v-for="item in lesson.lessonDays" :key="item.id"
                                :label="weekDays[item.weekDay] + item.lessonStart + '-' + item.lessonEnd"
                                :value="item.id">
                            </el-option>
                        </el-select>
                        <div class="tips">提示：最多选择{{ course.weekLessonNum }}节课</div>
                    </div>
                </div>

                <div v-if="course.classTag==='0'" class="c-line">
                    <div class="form-label required">有效期</div>
                    <div class="form-field">
                        <el-date-picker v-validate="'required'" v-model="effectDate"
                            :disabled="classType=='0'&&!refund.unit=='节'" name="effectdate" size="medium"
                            type="daterange" range-separator="至">
                        </el-date-picker>
                    </div>
                </div>
            </template>

            <!-- <template v-if="privateTag=='1'">
                <div class="c-line">
                    <div class="form-label required">选择服务</div>
                    <div class="form-field">
                        <el-select
                            v-model="serviceId"
                            size="medium"
                            placeholder="请选择服务">
                            <el-option
                                v-for="item in serviceList"
                                :key="item.serviceId"
                                :label="item.serviceName"
                                :value="item.serviceId">
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div class="c-line">
                    <div class="form-label">选择教练</div>
                    <div class="form-field">
                        <el-select
                            v-model="coachId"
                            size="medium"
                            placeholder="请选择教练">
                            <el-option
                                v-for="item in service.coaches"
                                :key="item.coachId"
                                :label="item.coachName"
                                :value="item.coachId">
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div class="c-line">
                    <div class="form-label required">选择课程</div>
                    <div class="form-field">
                        <el-select
                            v-model="courseId"
                            size="medium"
                            placeholder="请选择课程">
                            <el-option
                                v-for="item in courseList"
                                :key="item.courseId"
                                :label="item.courseName"
                                :value="item.courseId">
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <div class="c-line" >
                    <div class="form-label required">有效期</div>
                    <div class="form-field">
                        <el-date-picker
                            v-validate="'required'"
                            v-model="effectDate"
                            :disabled="classType=='0'"
                            name="effectdate1"
                            size="medium"
                            type="daterange"
                            range-separator="至">
                        </el-date-picker>
                    </div>
                </div>
            </template> -->
            <div class="c-line" v-if="getFee && courseId&&privateTag!=='1'">
                <div class="form-label required">换课类型</div>
                <div class="form-field">
                    <label>
                        <input v-model="classType" type="radio" value="0">
                        <span>平移换课</span>
                    </label>
                    <label>
                        <input v-model="classType" type="radio" value="1">
                        <span>自由换课</span>
                    </label>
                </div>
            </div>
            <!-- <div class="c-line">
                <div class="form-label">是否补差价</div>
                <div class="form-field form-field-lineheight">
                    <el-switch
                        v-model="getFee">
                    </el-switch>
                </div>
            </div> -->
            <!-- <div
                v-if="!getFee && courseId"
                class="c-line">
                <div
                    v-if="privateTag!=='1' && course && course.trainingType==='4'"
                    class="form-label required">课程天数</div>
                <div
                    v-else
                    class="form-label required">课程节数</div>
                <div class="form-field">
                    <el-input-number
                        v-model="courseNum"
                        :min="1"
                        size="medium"></el-input-number>
                    <div
                        v-if="refund.leftDays"
                        
                        class="tips">注：原课程总价{{ refund.payFee/100 }}元，剩余{{ refund.leftDays }}天，折合剩余金额≈{{ refund.refundMoney/100 }}元，折合新课程≈{{ refund.courseNum }} {{ refund.unit }}</div>
                         <div
                        v-if="refund.leftDays"
                        class="tips">注：原课程（余{{refund.leftDays}}天/总{{refund.totalDays}}天）剩余价值{{ refund.refundMoney/100 }}元，新课程（余{{classNumber}}{{refund.unit}}/总{{course.lessonNum}}{{refund.unit}}）价值{{refund.newPrice}}元，需要补差价{{ refund.price }}元。</div>
                    <div
                        v-else
                        class="tips">注：原课程总价{{ refund.payFee/100 }}元，剩余课时{{ refund.remainNum }}节，折合剩余金额≈{{ refund.refundMoney/100 }}元，折合新课程≈{{ refund.courseNum }} {{ refund.unit }}</div> 
                         <div
                        v-else
                        class="tips">注：原课程（余{{refund.remainNum}}节/总{{refund.lessonNum}}节）剩余价值{{ refund.refundMoney/100 }}元，新课程（余{{classNumber}}{{ refund.unit }}/总{{course.lessonNum}}{{ refund.unit }}）价值{{refund.newPrice}}元，需要补差价{{ refund.price }}元。</div>
                </div>
            </div> -->
            <div class="c-line" v-if="getFee && courseId&&classType=='1'">
                <div class="form-label">课程节数</div>
                <div class="form-field form-field-lineheight">
                    <el-input-number size="mini" class="one" v-if="refund.leftDays" v-model="classNumber"
                        @change="handleChange" :min="1"
                        :max="(refund.leftDays<course.lessonNum)?refund.leftDays:course.lessonNum" label="描述文字">
                    </el-input-number>
                    <el-input-number size="mini" class="two" v-if="!refund.leftDays && classType=='0'"
                        v-model="classNumber" @change="handleChange" :min="1"
                        :max="(refund.remainNum<course.lessonNum)?refund.remainNum:course.lessonNum" label="描述文字">
                    </el-input-number>
                    <el-input-number size="mini" class="three" v-if="!refund.leftDays && classType=='1'"
                        v-model="classNumber" :data-remainNum='refund.remainNum' :data-lessonNum='course.lessonNum'
                        @change="handleChange" :min="1"
                        :max="(refund.remainNum<course.lessonNum)?course.lessonNum:refund.remainNum" label="描述文字">
                    </el-input-number>
                </div>
            </div>
            <div v-if="getFee && courseId&&privateTag!=='1'" class="c-line">
                <div class="form-label required">差价金额</div>
                <div class="form-field">
                    <div class="input-with-unit">
                        <input v-validate="'required'" v-model="price" name="price" type="text" class="normal">
                        <div class="unit">元</div>
                    </div>
                    <!-- <div
                        v-if="refund.leftDays"
                        class="tips">注：原课程总价{{ refund.payFee/100 }}元，剩余{{ refund.leftDays }}天，折合剩余金额≈{{ refund.refundMoney/100 }}元，补差价金额{{ refund.price }}元</div> -->
                    <div v-if="refund.leftDays" class="tips">
                        注：原课程（余{{refund.leftDays}}天/总{{refund.totalDays}}天）剩余价值{{ refund.refundMoney/100 }}元，新课程（余{{classNumber}}{{refund.unit}}/总{{course.lessonNum}}{{refund.unit}}）价值{{Math.round((pitchNumber*classNumber)*100)/100}}元，需要补差价{{ refund.price }}元。
                    </div>
                    <!-- <div
                        v-else
                        class="tips">注：原课程总价{{ refund.payFee/100 }}元，剩余课时{{ refund.remainNum }}节，折合剩余金额≈{{ refund.refundMoney/100 }}元，补差价金额{{ refund.price }}元</div> -->
                    <div v-else class="tips">
                        注：原课程（余{{refund.remainNum}}节/总{{refund.lessonNum}}节）剩余价值{{ refund.refundMoney/100 }}元，新课程（余{{classNumber}}{{refund.unit}}/总{{course.lessonNum}}{{refund.unit}}）价值{{Math.round((pitchNumber*classNumber)*100)/100}}元，需要补差价{{ refund.price }}元。
                    </div>
                </div>
            </div>

            <div class="c-line c-line-top">
                <div class="form-label form-label-empty"></div>
                <div class="form-field">
                    <button class="btn btn-md btn-primary" @click="submit">确认</button>
                    <button class="btn btn-md btn-cancel" @click="goBack">取消</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import $ from 'jquery'
    import mAlert from 'mAlert'
    import param2list from 'utils/param2list'
    import _ from 'lodash'
    import moment from 'moment'
    import {
        m2back,
        m2front
    } from 'utils/moneyUtils'
    import settings from 'aisportsConfig'
    import 'utils/confirm'
    import {
        vValidate
    } from 'directives/vValidate'

    export default {
        components: {
            'el-select': () => import('element-ui').then(({
                Select
            }) => Select),
            'el-option': () => import('element-ui').then(({
                Option
            }) => Option),
            'el-switch': () => import('element-ui').then(({
                Switch
            }) => Switch),
            'el-input-number': () => import('element-ui').then(({
                InputNumber
            }) => InputNumber),
            'el-date-picker': () => import('element-ui').then(({
                DatePicker
            }) => DatePicker),
        },
        mixins: [vValidate],
        data() {
            return {
                enrollId: this.$route.query.enrollId,
                oldPrivateTag: this.$route.query.privateTag,
                instId: this.$route.query.instId,
                custId: this.$route.query.custId,
                serviceId: '',
                refund: {},
                privateTag: '0',
                classType: '0',
                serviceList: [],
                courseList: [],
                courseId: '',
                course: {},
                effectDate: [],
                weekDays: ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                inTag: '0',
                getFee: true,
                courseNum: '',
                price: '',
                classNumber: 1,
                serviceArray: [],
                courseType: '',
                courseTypeObj: {},
                termList: [],
                termId: '',
                term: {},
                lessonList: [],
                lessonId: '',
                lesson: {},
                classList: [],
                classId: '',
                courseTime: [],
                pitchNumber: '',
                serviceObj: {},
                coachId: '',
                service: {},

                newModelTag: '',
                placeList: [],
                placeId: '',
                longLessonId: [],
                tcLongLessonList: [],
                timeNum: 0,
                newModelTermList: [],
                newModelTermId: '',
                newModelClassList: [],
                newModelClassId: '',
                unitStatus: true
            }
        },

        watch: {
            classType(val) {
                this.effectDate[0] = new Date(new Date().toLocaleDateString().replace(/(年|月)/g, '/').replace('日', '')
                    .replace(/[^\d-/]/g, ''))
                if (val === '0') {
                    this.myClassNumber()
                    this.classNumberSet()
                } else if (val === '1') {
                    this.myClassNumber()
                    this.classNumberSet()
                }
                if (this.course.trainingType === '4') {
                    this.effectDate = [
                        moment(this.effectDate[0]).format('YYYY-MM-DD'),
                        this.getEnddateByValidPeriodClass(moment(this.effectDate[0]).format('YYYY-MM-DD')),
                    ]
                } else {
                    this.effectDate = [
                        moment(this.effectDate[0]).format('YYYY-MM-DD'),
                        this.getEnddateByValidPeriod(moment(this.effectDate[0]).format('YYYY-MM-DD')),
                    ]

                }

            },
            "course.lessonNum"() {
                if (this.privateTag != '1' && this.classType != '0') {
                    this.myClassNumber()
                }

            },
            privateTag() {
                this.serviceId = ''
                this.courseId = ''
                this.serviceList = []
                if (this.privateTag === '1') {
                    this.service = {}
                    this.serviceObj = {}
                    this.courseList = []
                } else {
                    this.serviceArray = []
                }
                if (this.refund.error === 0) {
                    this.queryCourseList()
                }
            },
            serviceId() {
                if (this.inTag != '0') {
                    this.courseId = ''
                    if (this.privateTag === '1') {
                        this.service = {}
                        this.serviceObj = {}
                        this.courseList = []
                    } else {
                        this.courseType = ''
                        this.serviceArray = []
                    }
                    this.queryCourseList()
                }
                this.inTag = '1'
            },
            coachId() {
                this.courseId = ''
                this.serviceObj = {}
                this.courseList = []
                if (this.refund.error === 0) {
                    this.queryCourseList()
                }
            },
            courseType() {
                this.courseId = ''
                this.termId = ''
                this.lessonId = ''
                this.classId = ''
                this.courseTime = []
                this.effectDate = []
                this.courseTypeObj = _.find(this.serviceArray, item => item.typeId == this.courseType)
                if (this.newModelTag === '1') {
                    this.courseList = (this.courseTypeObj || {}).courseList || []
                } else {
                    this.courseList = (this.courseTypeObj || {}).trainingCourse || []
                }
            },
            courseId() {
                if (this.privateTag != '1') {
                    this.termId = ''
                    this.newModelTermId = ''
                    this.lessonId = ''
                    this.classId = ''
                    this.placeId = ''
                    this.courseTime = []
                    this.effectDate = []
                    this.longLessonId = []
                    this.course = _.find(this.courseList, item => item.courseId == this.courseId) || {}
                    this.timeNum = parseInt(this.course.timeNum) || 0
                    this.placeList = this.course.placeList || []
                    if (this.course.classTag === '0') {
                        this.setEffectDate(true)
                    }
                    if (this.course.classTag === '1') {
                        this.newModelTermList = this.course.termList || []
                        this.termList = this.course.trainingTerm || []
                    } else if (this.courseTypeObj && this.courseTypeObj.trainingLesson) {
                        this.lessonList = _.filter(this.courseTypeObj.trainingLesson, item => item.courseId == this
                            .courseId) || []
                    }

                    this.refund.courseNum = parseInt(this.refund.refundMoney / (this.course.price / this.course
                        .lessonNum))
                    this.courseNum = this.refund.courseNum
                    this.refund.unit = this.course.trainingType === '4' ? '天' : '节'
                    if (this.refund.unit == '节') {
                        this.unitStatus = false
                    } else this.unitStatus = true
                    // this.refund.price = m2front(this.course.price - this.refund.refundMoney)
                    this.refund.newPrice = m2front(this.course.price)
                    this.pitchNumber = m2front(this.course.price) / this.course.lessonNum
                    // if(this.classType=='0'){
                    // this.classNumber=this.refund.remainNum||this.refund.leftDays
                    // }else{
                    //     this.classNumber=this.course.lessonNum
                    // }

                    this.myClassNumber()

                } else {
                    const thisCourse = _.find(this.courseList, item => item.courseId == this.courseId) || {}
                    this.refund.courseNum = parseInt(this.refund.refundMoney / (this.course.price / this.course
                        .lessonNum))
                    this.courseNum = this.refund.courseNum
                    this.refund.unit = '节'
                    this.refund.price = m2front(thisCourse - this.refund.refundMoney)
                    this.price = this.refund.price
                }
                //选择平移换课的时候有效期禁用 但是要有初始值
                this.effectDate[0] = new Date(new Date().toLocaleDateString().replace(/(年|月)/g, '/').replace('日', '')
                    .replace(/[^\d-/]/g, ''))
                this.effectDate = [
                    moment(this.effectDate[0]).format('YYYY-MM-DD'),
                    this.getEnddateByValidPeriodClass(moment(this.effectDate[0]).format('YYYY-MM-DD')),
                ]
            },
            getFee() {
                this.setEffectDate()
            },
            effectDate(val) {
                if (val.length > 0) {
                    let timestamp = val[1] - val[0]
                    if (timestamp === 0) {
                        this.classNumber = 1
                    }
                    if (timestamp) {
                        let day = parseInt((timestamp / 1000 / 60 / 60) / 24) + 1
                        let numbers = ''
                        if (this.refund.leftDays) numbers = this.refund.leftDays
                        else if (this.refund.remainNum) numbers = this.refund.remainNum
                        if (day - numbers > 0 && this.unitStatus) {
                            mAlert.showWrong('有效期选择不能超过剩余课程天数')
                            this.effectDate = []
                            return
                        } else if (day - numbers < 0 && this.unitStatus && this.classType == '0') {
                            mAlert.showWrong('有效期选择不能少于剩余课程天数')
                            this.effectDate = []
                            return
                        } else {
                            //是期课做限制 是次课不做限制  新课程节数要做限制  不能大于总课程节数
                            if (this.unitStatus) {
                                this.classNumber = day
                                this.classNumberSet()
                            } else {
                                this.myClassNumber()
                            }
                        }
                    }
                }

                if (val && val.length && !this.getFee && this.course.trainingType === '4') {
                    this.courseNum = moment(val[1]).diff(moment(val[0]), 'days') + 1
                }
            },
            courseNum() {
                if (this.course.trainingType === '4') {
                    this.effectDate = [
                        moment(this.effectDate[0]).format('YYYY-MM-DD'),
                        this.getEnddateByValidPeriodClass(moment(this.effectDate[0]).format('YYYY-MM-DD')),
                    ]
                } else {
                    this.effectDate = [
                        moment(this.effectDate[0]).format('YYYY-MM-DD'),
                        this.getEnddateByValidPeriod(moment(this.effectDate[0]).format('YYYY-MM-DD')),
                    ]
                }
            },
            placeId() {
                const obj = _.find(this.placeList, {
                    placeId: this.placeId
                }) || {}
                this.tcLongLessonList = obj.tcLongLessonList || []
                this.setLessonTime()
                this.longLessonId = []
            },
            termId() {
                this.lessonId = ''
                this.classId = ''
                this.term = _.find(this.termList, item => item.termId == this.termId) || {}
                if (this.courseTypeObj && this.courseTypeObj.trainingLesson) {
                    this.lessonList = _.filter(this.courseTypeObj.trainingLesson, item => (item.courseId == this
                        .courseId) && (item.termId == this.termId)) || []
                }
            },
            newModelTermId(val) {
                this.newModelClassId = ''
                this.newModelClassList = (_.find(this.newModelTermList, {
                    termId: val
                }) || {}).classList || []
                this.newModelClassList.forEach((item) => {
                    const capacity = item.capacity === undefined || item.capacity === null ? '不限' : parseInt(
                        item.capacity)
                    const divideNum = parseInt(item.divideNum) || 0
                    if (capacity !== '不限' && capacity <= divideNum) {
                        this.$set(item, 'disabled', true)
                    }
                    this.$set(item, 'classNameStr', `${item.className}(${divideNum}/${capacity})`)
                })
            },
            lessonId() {
                this.classId = ''
                this.courseTime = []
                this.lesson = _.find(this.lessonList, item => item.lessonId == this.lessonId) || {}
                if (this.course.classTag == '1') {
                    this.classList = this.lesson.trainingClass || []
                }
            },
        },
        mounted() {
            this.queryRefundMoney()

        },
        methods: { 
            myClassNumber() {
                if (this.refund.leftDays) {
                    if (this.refund.leftDays < this.course.lessonNum) {
                        this.classNumber = this.refund.leftDays
                    } else {
                        this.classNumber = this.course.lessonNum
                    }
                } else if (this.refund.remainNum) {
                    if (this.refund.remainNum < this.course.lessonNum && this.classType === '0') {
                        this.classNumber = this.refund.remainNum
                    } else {
                        this.classNumber = this.course.lessonNum
                    }
                }
                let prone = Math.round((this.pitchNumber * this.classNumber) * 100) / 100
                this.refund.price = m2front((prone * 100) - this.refund.refundMoney)
                this.price = this.refund.price
            },
            classNumberSet() {
                this.refund.price = m2front((this.classNumber * this.pitchNumber) * 100 - this.refund.refundMoney)
                this.price = this.refund.price
            },
            //课程节数
            handleChange(value) {
                if (this.refund.leftDays && this.refund.leftDays < this.course.lessonNum) {
                    if (value > this.refund.leftDays) {
                        this.myClassNumber()
                        return
                    }
                } else if (this.refund.remainNum && this.refund.remainNum < this.course.lessonNum) {
                    if (this.classType == '0') {
                        if (value > this.refund.remainNum) {
                            this.myClassNumber()
                            return
                        }
                    } else {
                        if (value > this.course.lessonNum) {
                            this.myClassNumber()
                            return
                        }

                    }
                    }
                    this.refund.price = m2front((value * this.pitchNumber) * 100 - this.refund.refundMoney)
                    this.price = this.refund.price
                    if (this.course.trainingType === '4') {
                        this.effectDate = [
                            moment(this.effectDate[0]).format('YYYY-MM-DD'),
                            this.getEnddateByValidPeriodClass(moment(this.effectDate[0]).format('YYYY-MM-DD')),
                        ]
                    }

                },
                setEffectDate(tag) {
                        if (tag) {
                            return
                        }
                        this.effectDate = [
                            moment().format('YYYY-MM-DD'),
                            this.getEnddateByValidPeriod(moment().format('YYYY-MM-DD')),
                        ]

                    },
                    //重写时间联动
                    getEnddateByValidPeriodClass(startDate) {
                        let endDate = ''
                        let coursePeriod
                        coursePeriod = `${this.classNumber}d`

                        let len = coursePeriod.length,
                            unit = coursePeriod.substring(len - 1, len),
                            num = parseInt(coursePeriod.substring(0, len - 1));
                        if (unit === 'd') {
                            endDate = moment(startDate).add(num, 'days').subtract(1, 'days').format('YYYY-MM-DD');
                        }
                        if (unit === 'M') {
                            endDate = moment(startDate).add(num, 'months').subtract(1, 'days').format('YYYY-MM-DD');
                        }
                        if (unit === 'y') {
                            endDate = moment(startDate).add(num, 'years').subtract(1, 'days').format('YYYY-MM-DD');
                        }
                        return endDate
                    },
                    getEnddateByValidPeriod(startDate) {

                        let endDate = ''
                        let coursePeriod
                        if (!this.getFee && this.course.trainingType === '4') {
                            coursePeriod = `${this.courseNum}d`
                        } else {
                            if (!this.course || !this.course.validPeriod) {
                                return moment()
                            }
                            coursePeriod = this.course.validPeriod
                        }
                        let len = coursePeriod.length,
                            unit = coursePeriod.substring(len - 1, len),
                            num = parseInt(coursePeriod.substring(0, len - 1));
                        if (unit === 'd') {
                            endDate = moment(startDate).add(num, 'days').subtract(1, 'days').format('YYYY-MM-DD');
                        }
                        if (unit === 'M') {
                            endDate = moment(startDate).add(num, 'months').subtract(1, 'days').format('YYYY-MM-DD');
                        }
                        if (unit === 'y') {
                            endDate = moment(startDate).add(num, 'years').subtract(1, 'days').format('YYYY-MM-DD');
                        }
                        return endDate
                    },
                    setLessonTime() {
                        this.tcLongLessonList.forEach((item) => {
                            if (item.startTime && item.endTime) {
                                item.name = `${this.weekDays[item.weekday] + item.startTime.substring(0, 2)}:${
                        item.startTime.substring(2, 4)}-${item.endTime.substring(0, 2)}:${
                        item.endTime.substring(2, 4)}`
                            } else {
                                item.name = this.weekDays[item.weekday]
                            }
                        })
                    },
                    goBack() {
                        window.history.back()
                    },
                    submit(e) {
                        if (!this.courseId) {
                            mAlert.showWrong('请选择课程')
                            return
                        }
                        if (!this.serviceId) {
                            mAlert.showWrong('请选择服务')
                            return
                        }
                        if (this.refund.length === 0 || this.refund.error !== 0) {
                            mAlert.showWrong('无法获取课程剩余折合金额，不能换课')
                            return
                        }
                        this.$validator.validateAll().then((result) => {
                            if (!result) {
                                return
                            }
                            let chargeType = this.getFee ? '1' : '0'
                            if (this.privateTag == '0') chargeType = '2'
                            const data = {
                                courseId: this.courseId,
                                serviceId: this.serviceId,
                                custId: this.custId,
                                chargeType,
                                refundFee: this.refund.refundMoney,
                                enrollId: this.enrollId,
                                privateTag: this.privateTag,
                                instId: this.instId,
                                convertNum: this.classNumber,
                                convertFee: this.price * 100
                            }
                            if (this.newModelTag === '1') {
                                data.placeId = this.placeId ? this.placeId : undefined
                                data.lessonDayId = this.longLessonId.length ? this.longLessonId.toString() :
                                    undefined
                            }

                            if (this.privateTag === '0') {
                                if (this.course.classTag === '1') {
                                    if (this.newModelTag === '1') {
                                        _.extend(data, {
                                            termId: this.newModelTermId
                                        })
                                    } else {
                                        _.extend(data, {
                                            termId: this.termId
                                        })
                                    }

                                    if (this.classId) {
                                        _.extend(data, {
                                            classId: this.classId
                                        })
                                    }
                                    if (this.newModelClassId) {
                                        _.extend(data, {
                                            classId: this.newModelClassId
                                        })
                                    }
                                } else {
                                    _.extend(data, {
                                        startDate: moment(this.effectDate[0]).format('YYYY-MM-DD')
                                    })
                                    _.extend(data, {
                                        endDate: moment(this.effectDate[1]).format('YYYY-MM-DD')
                                    })

                                    if (this.course.chooseTimeAttr == '1') {
                                        if (this.courseTime.length > this.course.weekLessonNum) {
                                            mAlert.showWrong('上课时间超过限制数量')
                                            return
                                        }
                                        _.extend(data, {
                                            lessonDayId: this.courseTime.join(',')
                                        })
                                    }
                                }

                                if (this.lessonId) {
                                    _.extend(data, {
                                        lessonId: this.lessonId
                                    })
                                }
                            } else {
                                if (this.effectDate.length <= 1) {
                                    mAlert.showWrong('请选择有效期')
                                    return
                                }

                                _.extend(data, {
                                    startDate: moment(this.effectDate[0]).format('YYYY-MM-DD')
                                })
                                _.extend(data, {
                                    endDate: moment(this.effectDate[1]).format('YYYY-MM-DD')
                                })

                                if (this.coachId) {
                                    _.extend(data, {
                                        coachId: this.coachId
                                    })
                                }
                            }
                            if (chargeType == '0') {
                                if (this.courseNum <= 0) {
                                    mAlert.showWrong('课程数量不合法')
                                    return
                                }
                                _.extend(data, {
                                    convertNum: this.courseNum
                                })
                            }
                            if (chargeType == '1') {
                                if (isNaN(this.price)) {
                                    mAlert.showWrong('差价金额不合法')
                                    return
                                }
                                if (this.price < 0) {
                                    this.$confirm(`是否确认退款${this.price.slice(1)}元`).then(() => {
                                        _.extend(data, {
                                            convertFee: m2back(this.price)
                                        })
                                        this.changeCourseSubmit(e, data)
                                    })
                                } else {
                                    _.extend(data, {
                                        convertFee: m2back(this.price)
                                    })
                                    this.changeCourseSubmit(e, data)
                                }
                            } else {
                                this.changeCourseSubmit(e, data)
                            }
                        })
                    },
                    changeCourseSubmit(e, data) {
                        $.ajax({
                            url: '/operateTraining/changeCourse',
                            data: {
                                order: JSON.stringify(data)
                            },
                            onceSubmit: e.currentTarget,
                            success: (data) => {
                                if (data.error === 0) {
                                    mAlert.showSuccess('换课提交成功')
                                    if (data.auditTag === '0' && this.getFee && this.price >= 0) {
                                        settings.util.simulationForm('/pay', {
                                            tradeId: data.tradeId
                                        })
                                    } else {
                                        window.history.back()
                                    }
                                } else {
                                    mAlert.showWrong(data.message)
                                }
                            },
                        })
                    },
                    queryRefundMoney() {
                        $.ajax({
                            url: '/operateTraining/queryRefundMoney',
                            data: {
                                enrollId: this.enrollId,
                                privateTag: this.oldPrivateTag,
                            },
                            success: (data) => {
                                if (data.error === 0) {
                                    this.refund = data
                                    this.queryCourseList()
                                } else {
                                    mAlert.showWrong(data.message)
                                }
                            },
                        })
                    },
                    queryCourseList() {
                        $.ajax({
                            url: '/operateTraining/queryCourseList',
                            data: {
                                privateTag: this.privateTag,
                                instId: this.instId,
                                serviceId: this.serviceId,
                                coachId: this.coachId,
                            },
                            success: (data) => {
                                if (data.error === 0) {
                                    this.newModelTag = data.newModeTag
                                    if (!this.serviceId) {
                                        this.serviceId = data.service.serviceId
                                        this.serviceList = data.serviceList
                                    }

                                    if (this.privateTag == '0') {
                                        this.serviceArray = data.serviceArray
                                    } else {
                                        this.serviceObj = data.serviceObj
                                        this.courseList = this.serviceObj.courseList
                                        this.service = _.find(this.serviceList, item => item.serviceId ==
                                            this.serviceId) || {}
                                    }
                                } else {
                                    mAlert.showWrong(data.message)
                                }
                            },
                        })
                    },
            },
        }
</script>