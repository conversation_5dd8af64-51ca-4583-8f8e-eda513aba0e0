package com.asiainfo.aisports.service;

import com.asiainfo.aisports.TestApplicationContext;
import com.asiainfo.aisports.service.mq.MyDataSyncMessageService;
import com.asiainfo.aisports.service.syncdata.DataSyncEnum;
import com.asiainfo.aisports.service.syncdata.SyncDataMessage;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/1/30 09:42
 */
public class TestDataSyncService extends TestApplicationContext {

    @Autowired
    private MyDataSyncMessageService dataSyncMessageService;


    @Test
    public void test() {

        dataSyncMessageService.sendMessage(SyncDataMessage.build().centerId(10000000L)
                .sync(DataSyncEnum.TRADE).addPushKey(2024013002337042L), 3000);

    }
}
