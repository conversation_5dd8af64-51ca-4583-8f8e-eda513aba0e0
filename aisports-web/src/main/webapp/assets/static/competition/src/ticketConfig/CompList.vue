<template>
    <div class="comp-list">
        <div class="query-section">
            <input
                v-model="queryName"
                class="normal input-query"
                type="text"
                placeholder="请输入项目名称">
            <button
                class="btn btn-md btn-primary"
                @click="handleQueryClick">查询</button>
            <button
                class="btn btn-md btn-default add-btn-gray"
                @click="handleNewClick">
                <i class="iconfont icon-jiahao"></i>
                <span>新增</span>
            </button>
            <button
                class="btn btn-md btn-default add-btn-gray"
                @click="handleNewProject">
                <span>项目类型管理</span>
            </button>
        </div>
        <result-area>
            <table class="simple-splitter">
                <tbody>
                    <tr>
                        <th>项目名称</th>
                        <th>项目时间</th>
                        <th>是否上架</th>
                        <th>操作</th>
                    </tr>
                    <tr
                        v-for="item in list"
                        :key="item.id">
                        <td>{{ item.name }}</td>
                        <td>{{ item.performStartDate.substr(0,10)+' ~ '+item.performEndDate.substr(0,10) }}</td>
                        <td>
                            <checkbox
                                :value="item.status === '1'"
                                @input="updateProjStatus($event, item)"></checkbox>
                        </td>
                        <td>
                            <operation-text @click="view(item)">查看</operation-text>
                            <operation-text
                                v-if="item.status === '2'"
                                @click="delComp(item)">删除</operation-text>
                        </td>
                    </tr>
                </tbody>
            </table>
            <pagination
                v-if="pageInfo.total !== 0"
                :page-size="pageInfo.pageSize"
                :style="{marginTop: '15px'}"
                :total="pageInfo.total"
                :current-page="pageInfo.currentPage"
                @changepagesize="handlePageSizeChange"
                @changepage="handlePageChange"></pagination>
        </result-area>
    </div>
</template>

<script>
import Pagination from 'vue-components/Pagination.vue'
import 'frame'
import moment from 'moment'
import * as service from './service'
import showConfirm from 'helpers/showConfirm'
import mAlert from 'mAlert'
import Checkbox from 'vue-components/Checkbox.vue'

export default {
    components: {
        Pagination,
        Checkbox,
    },
    data() {
        return {
            list: [],
            queryName: '',
            pageInfo: {
                currentPage: 1,
                total: 0,
                pageSize: 10,
            },
        }
    },
    mounted() {
        this.loadList()
    },
    methods: {
        moment(str) {
            return moment(str).format('YYYY/MM/DD HH:mm')
        },
        loadList(page = 1) {
            service.getValidProjectList({
                projectName: this.queryName,
                pageNum: page,
                pageSize: this.pageInfo.pageSize,
            }, (data) => {
                this.list = data.pageInfo.list || []
                this.pageInfo.currentPage = data.pageInfo.pageNum
                this.pageInfo.total = data.pageInfo.total
            })
        },
        handleNewProject() {
            this.$router.push({
                name: 'venueStaticParamManage',
                query: {
                    paramCode: 'project_types',
                    paramName: '项目类型',
                },
            })
        },
        handlePageSizeChange(newPageSize) {
            this.pageInfo.pageSize = newPageSize
            this.loadList()
        },
        handlePageChange(newPage) {
            this.currentPage = newPage
            this.loadList(newPage)
        },
        handleQueryClick() {
            this.loadList()
        },
        handleEditClick(item) {
            this.$emit('edit', item)
        },
        handleNewClick() {
            this.$router.push('/comp')
        },
        delComp(item) {
            showConfirm({
                title: '提示',
                content: '确定删除？',
                onConfirm: () => {
                    service.delProject(item.id, () => {
                        mAlert.showSuccess('删除成功！')
                        this.loadList()
                    })
                },
            })
        },
        updateProjStatus(e, item) {
            service.updateProjectReleaseStatus({
                projectId: item.id,
                status: e ? '1' : '2',
            }, () => {
                mAlert.showSuccess('更新成功！')
                this.loadList()
            })
        },
        view(item) {
            this.$router.push({
                path: 'comp',
                query: {
                    projectId: item.id,
                },
            })
        },
    },
}
</script>
