package com.asiainfo.aisports.service.orderinfo;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.Product;
import com.asiainfo.aisports.domain.core.ServiceAcceptInfo;
import com.asiainfo.aisports.persistence.core.MemberSearchMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by wangdd on 16/4/5.
 * 私教报名
 */
@Service
public class Trade19OrderInfoService implements HandleOrderInfo{

    @Autowired
    MemberSearchMapper memberSearchMapper;

    /**
     * 私教报名
     * @param info
     * @return
     */
    public ServiceAcceptInfo handleOrderInfo(ServiceAcceptInfo info){
        StringBuilder privateName = new StringBuilder("私教名称：");
        StringBuilder remarkStr = new StringBuilder();
        List<Product> productList = memberSearchMapper.getProductById(info.getTradeId());
        for (Product product : productList) {
            if (Constants.ProductMode.PRIVATE_TRAINING.equals(product.getProductMode())) {
                privateName.append(product.getProductName()).append("&nbsp;");
            } else if (Constants.ProductMode.GIFTS.equals(product.getProductMode())
                    || Constants.ProductMode.GIFTS_CARD.equals(product.getProductMode())) {
                remarkStr.append(product.getProductName()).append("&nbsp;");
            }
        }
        info.setShowInfo1(privateName.toString());
        if (remarkStr.length() > 0) {
            info.setShowInfo2("参加活动：" + remarkStr.toString());
        }
        return info;
    }
}
