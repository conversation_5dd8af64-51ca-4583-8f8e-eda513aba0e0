package com.asiainfo.aisports.service;

import cn.hutool.core.date.DateUtil;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.service.qrcode.QrcodeCacheService;
import com.asiainfo.aisports.service.qrcode.QrcodeCacheServiceFactory;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by wangdd on 16/3/23.
 * 一卡通二维码service
 */
@Service
public class EcardQrcodeService {
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private VenueParamConfig venueParamConfig;

    /**
     * 将卡号生成二维码,并存到缓存中
     * key = qrcode value = ecardNo
     * @param ecardNo
     */
    @Transactional
    public String transformEcardNo(String ecardNo, Long centerId) {
        String qrcode = DigestUtils.md5Hex(ecardNo + DateUtil.current());
        qrcode = qrcode.substring(8,24);
        String type = venueParamConfig.getParam(centerId, Constants.VenueParam.ECARD_QRCODE_CACHE_TYPE, Constants.EcardQrcodeCacheUnit.SIXTY);
        QrcodeCacheService qrcodeCacheService = QrcodeCacheServiceFactory.createQrcodeCacheService(applicationContext, type);
        if (qrcodeCacheService == null) {
            throw new ServiceException("查询一卡通二维码缓存类型失败");
        }
        qrcodeCacheService.putQrcode(qrcode, ecardNo);
        return qrcode;
    }

    /**
     * 根据二维码来获取相应的卡号
     * @param qrcode
     * @return
     */
    @Transactional
    public String getEcardNo(String qrcode, Long centerId) {
        String type = Constants.EcardQrcodeCacheUnit.SIXTY;
        if (centerId != null) {
            type = venueParamConfig.getParam(centerId, Constants.VenueParam.ECARD_QRCODE_CACHE_TYPE, Constants.EcardQrcodeCacheUnit.SIXTY);
        }
        QrcodeCacheService qrcodeCacheService = QrcodeCacheServiceFactory.createQrcodeCacheService(applicationContext, type);
        if (qrcodeCacheService == null) {
            throw new ServiceException("查询一卡通二维码缓存类型失败");
        }
        return qrcodeCacheService.getEcardNoByQrcode(qrcode.toLowerCase());
    }

}
