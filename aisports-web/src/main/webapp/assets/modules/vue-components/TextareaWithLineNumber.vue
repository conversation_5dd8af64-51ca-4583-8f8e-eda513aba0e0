/**
 * Created by liang on 16/9/10.
 */

<template>
  <div class="textarea--with-line-number">
    <div class="line-numbers-wrap">
      <ul v-bind:style="{top: (top * (-1)) + 'px'}" class="line-numbers">
        <li v-for="line in lines">{{line}}</li>
      </ul>
    </div>
    <textarea v-el:textarea v-on:scroll="handleScroll" v-bind:placeholder="placeholder" class="remark" v-on:input="handleChange">{{value}}</textarea>
  </div>
</template>

<script>
  export default {
    props: ['value', 'onChange', 'placeholder'],

    data() {
      return {
        lines: 0,
        top: 1
      }
    },

    computed: {
      lines() {
        return this.value.split('\n').map((line, index) => (index + 1))
      }
    },

    methods: {
      handleChange(e) {
        this.onChange(e)
      },

      handleScroll(e) {
        this.top = this.$els.textarea.scrollTop
      }
    }
  }
</script>

<style lang="scss" scoped>
.textarea--with-line-number {
  position: relative;

  textarea {
    padding: 12px 20px 12px 70px;
    line-height: 28px;
  }
}

.line-numbers-wrap {
  min-height: 278px;
  overflow: hidden;
  position: relative;
  background: #eee;
  width: 60px;
  position: absolute;
  top: 1px;
  left: 1px;
}

.line-numbers {
  position: absolute;
  top: 1px;
  left: 1px;
  padding: 12px 10px;
  text-align: right;
  box-sizing: border-box;
  width: 60px;

  li {
    line-height: 28px;
  }
}
</style>
