package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.TradeTicketPerson;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.project.TicketPersonForSeat;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface TradeTicketPersonMapper {
    @Delete({
            "delete from trade_ticket_person",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
            "insert into trade_ticket_person (id, ticket_id, ",
            "name, pspt_type, ",
            "pspt_id, mobile_num, ",
            "tag, state, check_date, ",
            "create_time, update_time)",
            "values (#{id,jdbcType=BIGINT}, #{ticketId,jdbcType=BIGINT}, ",
            "#{name,jdbcType=VARCHAR}, #{psptType,jdbcType=VARCHAR}, ",
            "#{psptId,jdbcType=VARCHAR}, #{mobileNum,jdbcType=VARCHAR}, ",
            "#{tag,jdbcType=VARCHAR}, #{state,jdbcType=VARCHAR}, #{checkDate,jdbcType=TIMESTAMP}, ",
            "#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})"
    })
    int insert(TradeTicketPerson record);

    @Select({
            "select",
            "id, ticket_id, name, pspt_type, pspt_id, mobile_num, tag, state, check_date, ",
            "create_time, update_time",
            "from trade_ticket_person",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="pspt_type", property="psptType", jdbcType=JdbcType.VARCHAR),
            @Result(column="pspt_id", property="psptId", jdbcType=JdbcType.VARCHAR),
            @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
            @Result(column="tag", property="tag", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="check_date", property="checkDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    TradeTicketPerson selectByPrimaryKey(Long id);

    @Update({
            "update trade_ticket_person",
            "set ticket_id = #{ticketId,jdbcType=BIGINT},",
            "name = #{name,jdbcType=VARCHAR},",
            "pspt_type = #{psptType,jdbcType=VARCHAR},",
            "pspt_id = #{psptId,jdbcType=VARCHAR},",
            "mobile_num = #{mobileNum,jdbcType=VARCHAR},",
            "tag = #{tag,jdbcType=VARCHAR},",
            "state = #{state,jdbcType=VARCHAR},",
            "check_date = #{checkDate,jdbcType=TIMESTAMP},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(TradeTicketPerson record);

    @Select({
            "<script>",
            "select",
            "id, ticket_id, name, pspt_type, pspt_id, mobile_num, tag, state, check_date, create_time, update_time",
            "from trade_ticket_person",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "<if test=\"name != null\">and name = #{name,jdbcType=VARCHAR}</if>",
            "<if test=\"psptType != null\">and pspt_type = #{psptType,jdbcType=VARCHAR}</if>",
            "<if test=\"psptId != null\">and pspt_id = #{psptId,jdbcType=VARCHAR}</if>",
            "<if test=\"mobileNum != null\">and mobile_num = #{mobileNum,jdbcType=VARCHAR}</if>",
            "<if test=\"tag != null\">and tag = #{tag,jdbcType=VARCHAR}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"checkDate != null\">and check_date = #{checkDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="pspt_type", property="psptType", jdbcType=JdbcType.VARCHAR),
            @Result(column="pspt_id", property="psptId", jdbcType=JdbcType.VARCHAR),
            @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
            @Result(column="tag", property="tag", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="check_date", property="checkDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<TradeTicketPerson> selectByFields(TradeTicketPerson param);

    @Update({
            "<script>",
            "update trade_ticket_person",
            "<set>",
            "<if test=\"ticketId != null\">ticket_id = #{ticketId,jdbcType=BIGINT},</if>",
            "<if test=\"name != null\">name = #{name,jdbcType=VARCHAR},</if>",
            "<if test=\"psptType != null\">pspt_type = #{psptType,jdbcType=VARCHAR},</if>",
            "<if test=\"psptId != null\">pspt_id = #{psptId,jdbcType=VARCHAR},</if>",
            "<if test=\"mobileNum != null\">mobile_num = #{mobileNum,jdbcType=VARCHAR},</if>",
            "<if test=\"tag != null\">tag = #{tag,jdbcType=VARCHAR},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"checkDate != null\">check_date = #{checkDate,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "</set>",
            "where id = #{id,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(TradeTicketPerson record);


    @Insert({
            "<script>",

            "insert into trade_ticket_person (id, ticket_id, ",
            "name, pspt_type, ",
            "pspt_id,mobile_num, tag, state, ",
            "check_date, create_time, ",
            "update_time)",
            "values",
            "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
            " (#{item.id,jdbcType=BIGINT}, #{item.ticketId,jdbcType=BIGINT}, ",
            "#{item.name,jdbcType=VARCHAR}, #{item.psptType,jdbcType=VARCHAR}, ",
            "#{item.psptId,jdbcType=VARCHAR}, #{item.mobileNum,jdbcType=VARCHAR}, #{item.tag,jdbcType=VARCHAR}, #{item.state,jdbcType=VARCHAR}, ",
            "#{item.checkDate,jdbcType=TIMESTAMP}, #{item.createTime,jdbcType=TIMESTAMP}, ",
            "#{item.updateTime,jdbcType=TIMESTAMP})",
            "</foreach>",
            "</script>"

    })
    int batchInsert(@Param("list") List<TradeTicketPerson> personList);


    @Select({
            "update trade_ticket_person a,trade_ticket b" ,
            "set a.state = #{ticketState} , a.update_time = now()" ,
            "where a.ticket_id = b.ticket_id" ,
            "and b.trade_id = #{tradeId}"
    })
    Integer updateByTradeId(@Param("tradeId") Long tradeId, @Param("ticketState") String ticketState);

    @Select({
            "<script>",
            "select",
            "a.id, a.ticket_id, a.name, a.mobile_num, a.pspt_type, a.pspt_id, a.tag, a.state, a.check_date, a.create_time, a.update_time, ifnull(b.reason, '') reason",
            "from trade_ticket_person a",
            "left join activity_black_list b ON a.pspt_type = b.pspt_type  AND a.pspt_id = b.pspt_id and b.status = 1",
            "<where>",
            "<if test=\"id != null\">and a.id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"ticketId != null\">and a.ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
            "<if test=\"name != null\">and a.name = #{name,jdbcType=VARCHAR}</if>",
            "<if test=\"mobileNum != null\">and a.mobile_num = #{mobileNum,jdbcType=VARCHAR}</if>",
            "<if test=\"psptType != null\">and a.pspt_type = #{psptType,jdbcType=VARCHAR}</if>",
            "<if test=\"psptId != null\">and a.pspt_id = #{psptId,jdbcType=VARCHAR}</if>",
            "<if test=\"tag != null\">and a.tag = #{tag,jdbcType=VARCHAR}</if>",
            "<if test=\"state != null\">and a.state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"checkDate != null\">and a.check_date = #{checkDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createTime != null\">and a.create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateTime != null\">and a.update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
            @Result(column="pspt_type", property="psptType", jdbcType=JdbcType.VARCHAR),
            @Result(column="pspt_id", property="psptId", jdbcType=JdbcType.VARCHAR),
            @Result(column="tag", property="tag", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="check_date", property="checkDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="reason", property="reason", jdbcType=JdbcType.VARCHAR)
    })
    List<DataMap> getPersons(TradeTicketPerson param);

    @Select({
            "update trade_ticket_person " ,
            "set state = #{ticketState} , update_time = now()" ,
            "where ticket_id = #{ticketId}" ,
    })
    Integer updateByTicketId(@Param("ticketId") Long ticketId, @Param("ticketState") String ticketState);

    @Select({
            "select a.id,a.ticket_id,t.trade_id,pt.type,t.trade_id_b,(pt.adult_num+pt.minor_num) as ticket_num" ,
            " from trade_ticket_person a," ,
            "      trade_ticket tt" ,
            "          left join trade_ticket_attr attr on tt.ticket_id = attr.ticket_id and attr_code = 'perform_ticket_id'," ,
            "     trade t," ,
            "     perform_new_ticket pt" ,
            " where a.ticket_id = tt.ticket_id" ,
            " and attr_value = pt.id" ,
            " and t.trade_id = tt.trade_id" ,
            " and a.state in ('2','4')" ,
            " and pt.stock_id = #{stockId}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
            @Result(column="trade_id", property="tradeId", jdbcType=JdbcType.BIGINT),
            @Result(column="type", property="type", jdbcType=JdbcType.VARCHAR),
            @Result(column="trade_id_b", property="tradeIdB", jdbcType=JdbcType.BIGINT),
            @Result(column="ticket_num", property="ticketNum", jdbcType=JdbcType.BIGINT),
    })
    List<TicketPersonForSeat> selectSeatCandidateList(@Param("stockId") Long stockId);
}
