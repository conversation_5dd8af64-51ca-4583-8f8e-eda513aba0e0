package com.asiainfo.aisports.db;

import com.google.common.collect.Maps;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.bind.RelaxedPropertyResolver;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * Created by liuchangyuan on 2016/12/16.
 */
public class DynamicDataSourceRegister
        implements ApplicationContextAware, ApplicationListener<ContextRefreshedEvent>, EnvironmentAware {

    // 如配置文件中未指定数据源类型，使用该默认值
    private static final Object DATASOURCE_TYPE_DEFAULT = "org.apache.tomcat.jdbc.pool.DataSource";

    private ApplicationContext applicationContext;

    // 存放DataSource配置的集合，模型<dataSourceName,dataSourceMap>
    private Map<String, Map<String, Object>> dataSourceInfoMap = Maps.newHashMap();

    @Resource(name="centerDataSource")
    private javax.sql.DataSource dataSource;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     *  监听容器刷新事件 OR Start Event
     *
     * @param contextRefreshedEvent
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        registerDynamicDataSource();
    }

    /**
     * 注册动态数据源
     */
    private void registerDynamicDataSource() {
        // 把数据源bean注册到容器中
        addBeanToApplication(dataSourceInfoMap);
    }


    /**
     * 功能说明：根据DataSource创建bean并注册到容器中
     *
     * @param customDataSourceMap
     */
    private void addBeanToApplication(Map<String, Map<String, Object>> customDataSourceMap) {
        DefaultListableBeanFactory defaultListableBeanFactory = (DefaultListableBeanFactory) applicationContext.getAutowireCapableBeanFactory();
        BeanDefinitionBuilder definitionBuilder;

        Map<Object, Object> targetDataSources = Maps.newHashMap();

        // 将默认数据源放入 targetDataSources map中
        targetDataSources.put("center", dataSource);
        DynamicDataSourceContextHolder.addDataSource("center");

        // 根据数据源得到数据，动态创建数据源bean 并将bean注册到applicationContext中去
        for (Entry<String, Map<String, Object>> entry : customDataSourceMap.entrySet()) {
            // bean ID
            String key = entry.getKey();
            Map<String, Object> dsMap = entry.getValue();
            Object type = dsMap.get("type");
            if (type == null){
                type = DATASOURCE_TYPE_DEFAULT;// 默认DataSource
            }

            // 创建bean
            definitionBuilder = BeanDefinitionBuilder.rootBeanDefinition(type.toString());
            definitionBuilder.getBeanDefinition().setAttribute("id", key);
            for (Entry<String, Object> dsEntry : dsMap.entrySet()) {
                definitionBuilder.addPropertyValue(dsEntry.getKey(), dsEntry.getValue());
            }
            // 注册bean
            defaultListableBeanFactory.registerBeanDefinition(key, definitionBuilder.getBeanDefinition());
            // 放入map中，注意一定是刚才创建bean对象
            targetDataSources.put(key, applicationContext.getBean(key));
            DynamicDataSourceContextHolder.addDataSource(key);
        }

        definitionBuilder = BeanDefinitionBuilder.rootBeanDefinition(DynamicDataSource.class);
        definitionBuilder.getBeanDefinition().setAttribute("id", "dynamicDataSource");

        definitionBuilder.addPropertyValue("defaultTargetDataSource", dataSource);
        definitionBuilder.addPropertyValue("targetDataSources", targetDataSources);

        // 注册Bean
        defaultListableBeanFactory.registerBeanDefinition("dynamicDataSource", definitionBuilder.getBeanDefinition());
        // 必须重新初始化 AbstractRoutingDataSource 中的 resolvedDataSources，动态切换才会生效
        DynamicDataSource dynamicDataSource = (DynamicDataSource)applicationContext.getBean("dynamicDataSource");
        dynamicDataSource.afterPropertiesSet();

        // 修改 transactionManager 的 dataSource
        DataSourceTransactionManager transactionManager = (DataSourceTransactionManager) applicationContext.getBean("transactionManager");
        transactionManager.setDataSource(dynamicDataSource);

        autowiredDynamicDataSource(dynamicDataSource);
    }

    /**
     * 注入动态数据源到jdbcTemplate、sqlSessoinTemplate
     *
     * @param dynamicDataSource
     */
    private void autowiredDynamicDataSource(DynamicDataSource dynamicDataSource){
        SqlSessionFactory sqlSessionFactory = applicationContext.getBean(SqlSessionFactory.class);
        org.apache.ibatis.mapping.Environment myBatisEnvironment = sqlSessionFactory.getConfiguration().getEnvironment();
        sqlSessionFactory.getConfiguration().setEnvironment(new org.apache.ibatis.mapping.Environment(myBatisEnvironment.getId(), myBatisEnvironment.getTransactionFactory(), dynamicDataSource));
    }

    /**
     * 加载多数据源配置
     */
    @Override
    public void setEnvironment(Environment environment) {
        RelaxedPropertyResolver propertyResolver = new RelaxedPropertyResolver(environment, "sharding.datasource.");
        String dsPrefixs = propertyResolver.getProperty("names");
        for (String dsPrefix : dsPrefixs.split(",")) {// 多个数据源
            Map<String, Object> dsMap = new HashMap<>(propertyResolver.getSubProperties(dsPrefix + "."));
            dsMap.put("url", propertyResolver.getProperty(dsPrefix + ".url"));
            dsMap.put("username", propertyResolver.getProperty(dsPrefix + ".username"));
            dsMap.put("password", propertyResolver.getProperty(dsPrefix + ".password"));
            dataSourceInfoMap.put(dsPrefix, dsMap);
        }
    }

}
