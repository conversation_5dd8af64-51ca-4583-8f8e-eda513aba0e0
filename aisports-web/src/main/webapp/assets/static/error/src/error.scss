@import "../../frame/src/frame";
@import "../../common/scss/form";
.main{
  background: $mainBgColor;
}
.error,.succ{
  background: #f1f2f7;
  padding: 20px 20px 40px;
  .title-box{
    margin-top: 10px;
  }
}
.error-content{
  position: relative;
  width: 350px;
  margin: 120px auto 229px;
  .icon-cuowu{
    position: absolute;
    font-size: 46px;
    color: #f25232;
    line-height: 1em;
  }
  .icon-jinggao{
    position: absolute;
    font-size: 46px;
    color: #f4aa1c;
    line-height: 1em;
  }
  .error-info{
    margin-left: 60px;
    line-height: 1;
    h2{
      color: #333;
      margin: 0;
      font-size: 20px;
    }
    p{
      font-size: 14px;
      color: #999;
      margin-top: 8px;
    }
  }

}
.btn-region{
  margin-top: 40px;
  // text-align: center;
}
.succ-content{
  position: relative;
  width: 350px;
  margin: 120px auto 200px; 
}
.laughing-face{
  position: absolute;
  width: 159px;
  height: 144px;
  background: url(../images/succ-face.png) no-repeat;

}
.succ-info{
  margin-left: 180px;
  padding-left: 20px;
  border-left: 2px solid #ededed;
  h2{
    font-size: 20px;
  }
  p{
    color: #999;
  }
}
.btn-region-succ{
  margin-top: 20px;
}