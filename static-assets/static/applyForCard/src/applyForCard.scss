@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../common/scss/dropDownTab";
$icon-size:48px;
$icon-round:24px;
$label-width:135px;
input[type="checkbox"],input[type="radio"]{
  width: 15px;
  height: 15px;
}
.main{
  background: $mainBgColor url("../images/step-bg.gif") repeat-y 80px 140px;
  .apply-for-card{
    fieldset{
      padding-bottom: 20px;
    }
    background:$mainBgColor url("../images/step-bg.gif") repeat-y 80px 140px;
    position: relative;
    padding: 0px 68px $mainContentPaddingBottom $mainPadding;
    @include transiTion(all,.3s);
    .top-info{
      position: absolute;
      height: 72px;
      line-height: 72px;
      padding-left: $mainPadding;
      background-color: $mainBgColor;
    }
    .query-res-list-wrap{
      margin: 0px;
      width: 640px;
      .q-label{
        width: 5em;
      }
      .val{
        .num{
          font-size: $ml-font-size;
          color: #56b854;
        }
      }
      ul:nth-child(n+2){
        li{
          border-top: 1px dashed #e4e4e4;
        }
      }
      ul:nth-child(3n-2){
        .liststyle{
          background-color: #e64c66;
        }
      }
      ul:nth-child(3n-1){
        .liststyle{
          background-color: #ffab00;
        }
      }
      ul:nth-child(3n){
        .liststyle{
          background-color: #86dc85;
        }

      }
    }
    .user-info-area{
      margin-left: 0px;
      width: 640px;
      margin-top: 15px;
    }
    .v-process{
      padding-top: 72px;
      /*.v-line{
        position: absolute;
        top: 140px;
        bottom: 0px;
        left: 80px;
        border-right: 1px solid #d7d9de;
      }*/
      .step{
        margin-bottom: 72px;
        @extend .clearfix;
        @include transiTion(all,.3s);
        .f-line{
          .f-label{
            width: 135px;
          }
          .f-field{
            margin-left: 135px;
            .set-psw{
              margin-top: 6px;
            }
            .set-master{
              margin-left: 11px;
              label{
                margin-left: 5px;
              }
            }
            .select-deduction{
              line-height: 15px;
              label{
                margin-left: 5px;
                .num{
                  margin-left: 15px;
                  color: #f25533;
                }
              }
            }
            .nav{
              float: none;
              @extend .clearfix;
              ul{
              float: right;
            }
            }
            .total-info{
              height: 48px;
              line-height: 48px;
              border-bottom: 1px solid #dddddd;
              background-color: #f1f4f6;
              float: none;
              margin-left:0px;
              @extend .clearfix;
              &>div{
                line-height: 48px;
                height: 48px;
                .list-circle{
                  line-height: 5px;
                  vertical-align: middle;
                  margin-right: 5px;
                }
              }
              .total-num{
                float: left;
                margin-left: 40px;
              }
              .total-money{
                float: right;
                margin-right: 90px;
              }
              em{
                font-size: $ml-font-size;
                line-height: $ml-font-size;
                color: #5ab958;
              }
            }
          }
          .save-info{
            width: 150px;
            padding: 8px 38px;
            margin-top: 25px;
            font-size: 14px;
          }
          .save-table{
            margin-top: 24px;
            th{
              .iconfont{
                color: #333;
              }
            }
          }
          .query-res-list{
            padding-right: $mainPadding;
            .table{
              float: none;
              margin-bottom: 0px;
            }
          }
        }
        .select-card-type{
          .card-type{
            margin-right: 60px;
            label{
              margin-left: 5px;
            }
          }
        }
        .deduction{
          min-height: 15px;
        }
        .left-label{
          float: left;
          width: 120px;
          text-align: center;
          .icon{
            @include size($icon-size,$icon-size);
            background-color: #bdc3c7;
            text-align: center;
            line-height: $icon-size;
            @include circleBox($icon-round);
            display: inline-block;
            .iconfont,.sports{
              color: #fff;
            }
          }
          .active{
            background-color: #56b854;
          }
          .icon-title{
            margin-top: 10px;
            width: 120px;
            text-align: center;
            height: 24px;
            line-height: 24px;
            @include circleBox(12px);
            border: 1px solid #56b854;
            background-color: #fff;
          }
        }
        .box{
          margin-left: 125px;
          background-color: #fff;
          padding: 40px 0px;
          position: relative;
          @include circleBox(4px);
          .left-triangle{
            @include triangle(left,10px,9px,#fff);
            position: absolute;
            top:15px;
            left: -10px;
          }
          .total-price{
            height: 42px;
            padding-bottom: 20px;
            padding-left: 65px;
            border-bottom: 1px solid #eff2f7;
            .num{
              color: #56b854;
              font-size: $ml-font-size;
            }
          }
          .protocol{
            input,label{
              vertical-align: middle;
            }
            label{
              margin-left: 7px;
              color: $main-text-color;
            }
          }
          .select-type{
            margin-left: $label-width;
            .type{
              display: inline-block;
              @include circleBox(4px);
              width: 212px;
              height: 113px;
              line-height: 113px;
              font-size: $l-font-size;
              text-align: center;
              background-color: #fff;
              border: 1px solid #60cfa6;
              color: $main-text-color;
              position: relative;
              margin-right: 15px;
              overflow: hidden;
              cursor: pointer;

              i{
                position: absolute;
                right: -13px;
                top: 4px;
                @include opaciTy(.3);
              }
            }
            .active{
              background-color: #60cfa6;
              color: #fff;
            }
          }
          .op-btns{
            margin-top: 40px;
            margin-left: $label-width;
            .btn{
              margin-right: 10px;
            }
            .btn-lg{
              width: 185px;
              height: 45px;
            }
            .actived{
              background-color: #1fa2f5;
              color: #fff;
            }
            .disabled{
              background: #e8e8e8;
              color: $main-text-color;
            }
          }
        }
      }
      }
    }
  }
.selected-card-info{
  .inner-box{
    width: 850px;
    @media screen and (max-width: 850px){
      width: 90%;
    }
    .sliminfo{
      border: 1px solid #cccccc;
      height: 450px;
      h3{
        font-size: 18px;
        color: $main-text-color;
        text-align: center;
        line-height: 44px;
        margin-top:0px;
        margin-bottom:0px;
      }
      .card-feture{
        padding: 15px 28px;
        dl{
          margin-bottom: 40px;
          dt{
            margin-bottom: 18px;
          }
        }
      }
    }
  }
}