@import "../../frame/src/frame";
@import "../../common/scss/bigTabs";
@import "../../common/scss/widges/popbox";
@import "./components/_dist";

.member-query-result{
	padding: 0 20px 40px;
	min-height: 460px;
}
.pagination-container{
	margin-top: 20px;
}
.records-container{
	display: inline-block;
	position: relative;
	width: 58px;
}
.distribute-record-box{
	position: absolute;
	left: -254px;
	top: -90px;
	width: 242px;
	.panel-body {
		height: 167px;
	}
	.arrow-right {
		position: absolute;
		width: 14px;
		height: 14px;
		margin-top: -10px;
		top: 50%;
		right: -6px;
		background-color: #fff;
		@include arrow("left", #e0e0e0, 1px);
	}
}
.distribute-record-item{
	position: relative;
	margin-left: 22px;
	margin-top: 10px;
	&:before {
		content: " ";
		position: absolute;
		background-color: #bdbdbd;
		@include circle(6px);
		top: 7px;
		left: -15px;
	}
	&:first-child:before{
		background-color: #fc8068;
	}
}
.distribute-record-item-hd{
	font-weight: bold;
	margin-top: 4px;
}
.distribute-record-item-bd{
	color: #999;
}

