package com.asiainfo.aisports.tools;

import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HTTP;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.Map;

/**
 * Created by tamir on 15/6/10.
 */
public class HttpRequestUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(HttpRequestUtil.class);
    private static final String RESULT_FORMAT = "{\"error\":%d, \"message\":\"%s\"}";

    /**
     * 获取json格式的返回
     *
     * @param url
     * @param data
     * @return
     */
    public static JSONObject getJsonResponse(String url, Map<String, String> data) throws IOException {
        if (data == null) {
            data = Maps.newHashMap();
        }
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;
        try {
            SSLContext sslContext = SSLContexts.custom().build();
            SSLConnectionSocketFactory sf = new SSLConnectionSocketFactory(
                    sslContext,
                    new String[]{"TLSv1", "TLSv1.1", "TLSv1.2"},
                    null,
                    SSLConnectionSocketFactory.getDefaultHostnameVerifier());
            client = HttpClients.custom()
                    .setSSLSocketFactory(sf)
                    .build();
            RequestBuilder requestBuilder = RequestBuilder.get()
                    .setUri(url);
            data.forEach(requestBuilder::addParameter);
            HttpUriRequest httpUriRequest = requestBuilder.build();
            response = client.execute(httpUriRequest);
            HttpEntity httpEntity = response.getEntity();
            String body = EntityUtils.toString(httpEntity);
            EntityUtils.consume(httpEntity);
            return JSONObject.fromObject(body);
        } catch (IOException | NoSuchAlgorithmException | KeyManagementException e) {
            LOGGER.error("", e);
            return serviceError();
        } finally {
            if (response != null) {
                response.close();
            }
            if (client != null) {
                client.close();
            }
        }
    }

    /**
     * post数据
     *
     * @param url
     * @param data
     * @return
     */
    public static String postData(String url, String data) {
        HttpRequest httpRequest = HttpRequest.post(url);
        httpRequest.send(data);
        if (httpRequest.ok()) {
            return httpRequest.body();
        } else {
            return String.format(RESULT_FORMAT, httpRequest.code(), httpRequest.body());
        }
    }

    /**
     * 以json的形式发起post请求
     * @param url
     * @param data
     * @return
     */
    public static String postPicStreamData(String url, String data) {
        String result = null;
        HttpClient httpClient = HttpClientBuilder.create().build();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader(HTTP.CONTENT_TYPE, "application/json");
        try {
            StringEntity se = new StringEntity(data, StandardCharsets.UTF_8);
            se.setContentType("application/json");
            httpPost.setEntity(se);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                byte[] bytes = EntityUtils.toByteArray(resEntity);
                JSONObject jsonObject = JSONObjectUtils.parseObject(new String(bytes));
                // 返回的是json，说明有错误
                if (jsonObject != null) {
                    result = jsonObject.toString();
                }
                EntityUtils.consume(resEntity);
            }
        } catch (IOException e) {
            LOGGER.error("", e);
        }
        return result;
    }

    /**
     * 根据header，post数据
     *
     * @param requestUrl
     * @param data
     * @return
     */
    public static String postWithHeader(String requestUrl, Map<String, Object> data, Map<String, String> headers) {
        try {
            URL url = new URL(requestUrl);

            HttpRequest request = HttpRequest.post(url);
            if (!headers.isEmpty()) {
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    request.header(header.getKey(), header.getValue());
                }
            }
            request.send(JSONObject.fromObject(data).toString());

            if (request.ok()) {
                return request.body();
            } else {
                return String.format(RESULT_FORMAT, request.code(), request.body());
            }
        } catch (MalformedURLException e) {
            LOGGER.error("", e);
            return String.format(RESULT_FORMAT, 1, e.getMessage());
        }
    }

    /**
     * get请求
     *
     * @param url
     * @param params
     * @return
     */
    public static JSONObject get(String url, Map<String, Object> params) {
        if (params == null) {
            params = Maps.newHashMap();
        }
        HttpRequest httpRequest = HttpRequest.get(url.trim(), params, true);
        return parseResponse(httpRequest);
    }

    /**
     * get请求
     *
     * @param url
     * @param params
     * @return
     */
    public static JSONObject getWithHeader(String url, Map<String, Object> params, String authorization) {
        if (params == null) {
            params = Maps.newHashMap();
        }
        HttpRequest httpRequest = HttpRequest.get(url.trim(), params, true).header("Authorization", "Basic " + authorization);
        return parseResponse(httpRequest);
    }

    /**
     * 格式为Content-Type:application/x-www-form-urlencoded;charset=utf-8
     * 的post请求
     *
     * @return
     */
    /*---post 请求--请求格式为Content-Type:application/x-www-form-urlencoded;charset=utf-8---*/
    public static JSONObject postWithXWWWForm(String requestPath, Map<String, String> param) {

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        RestTemplate restTemplate = new RestTemplate();

        MultiValueMap<String, String> forms = new LinkedMultiValueMap<String, String>();

        //往forms添加请求数据
        for (Map.Entry<String, String> entry : param.entrySet()) {
            forms.put(entry.getKey(), Collections.singletonList(entry.getValue()));
        }

        org.springframework.http.HttpEntity<MultiValueMap<String, String>> httpEntity = new org.springframework.http.HttpEntity<MultiValueMap<String, String>>(forms, headers);

        //获取返回数据
        String body = restTemplate.postForObject(requestPath, httpEntity, String.class);

        return JSONObjectUtils.parseObject(body);
    }

    private static JSONObject parseResponse(HttpRequest httpRequest) {
        if (httpRequest != null && httpRequest.ok()) {
            String response = httpRequest.body();
            LOGGER.debug("got http response : {}", response);
            return JSONObject.fromObject(response);
        } else {
            return serviceError();
        }
    }

    private static JSONObject serviceError() {
        JSONObject rsp = new JSONObject();
        rsp.put("error", "1");
        rsp.put("message", "服务异常，非常抱歉给您带来不便，请您稍后重试！");
        return rsp;
    }

    /**
     * 需要添加header
     *
     * @param url
     * @param authToken
     * @return
     */
    public static JSONObject request(String url, String authToken) {
        HttpRequest request = null;
        try {
            LOGGER.info("requestUrl:{}", url);
            request = new HttpRequest(url, "GET");
            request.authorization(authToken);
        } catch (RuntimeException e) {
            LOGGER.error("", e);
        }
        return parseResponse(request);
    }


    /**
     * 从http请求中获取IP地址
     *
     * @param request
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (Strings.isNullOrEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (Strings.isNullOrEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (Strings.isNullOrEmpty(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip.split(",")[0];
    }
}
