@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../../sea_modules/bootstrap/assets/stylesheets/bootstrap/_datepicker3";
@import "../../../sea_modules/bootstrap-select/bootstrap-select";

.wrapper{
  .main-delay{
    position: static;
  }
}
.delay{
  padding: 20px 20px 90px 20px;
  position: relative;
  .title-box{
    overflow: visible;
    .sub-box{
      padding: 0;
    }
    @include lrLayout();
  }
}
.delay{
  .f-line{
    margin:0 0 15px 0;
    overflow: hidden;
    .btn-15{
      margin-left: 15px;
    }
    textarea{
      width: 100%;
      padding: 10px 0 0 17px;
      min-height: 60px;
      resize: none;
      border: 1px solid #ccc;
    }
    .bottom-input{
      width: 526px;
    }
  }
}
.makeup-bottom{
  width: 100%;
  height: 95px;
  background: url("../images/bottom-bg.png") top left repeat-x;
  background-color: #fff;
  .btn-bottom{
    padding: 0;
    width: 189px;
    height: 81px;
    line-height: 81px;
    text-align: center;
    font-size: 24px;
    border-radius: 0;
    float: right;
    margin-top: 14px;
    letter-spacing: 15px;
  }
  span{
    float: right;
    font-size: 14px;
    margin:14px 40px 0 0;
    height: 81px;
    line-height: 81px;
    font-weight: bold;
    i{
      color: #f25232;
      font-size: 24px;
      vertical-align: middle;
      font-weight: normal;
      padding-left: 10px;
    }
  }
}
.bottom-search-line{
  margin: 20px 0 0 20px;
  padding-bottom: 0;
}
.bottom-content-box{
  padding: 10px;
  padding-bottom: 0;
  border: solid 1px #cccccc;
  border-radius: 4px;
}
.border-content{
  min-height: 150px;
  background: url(../images/card-freeze-bg.png) no-repeat right bottom #fafafa;
}
h3{
  text-indent: 10px;
  padding-top: 10px;
  margin: 0;
  font-size: 16px;
}
.card-search{
  .table-delay{
    thead{
      border: none;
    }
    tr{
      th{
      }
      .section{
        width: 22px;
        height: 28px;
        color: #fff;
        line-height: 26px;
        font-size: 12px;
        display: inline-block;
        margin-left: 5px;
      }
      .section-red{
        background: url("../images/section_bg.png") left 0 no-repeat;
      }
      .section-blue{
        background: url("../images/section_bg.png") left -28px no-repeat;
      }
      .section-green{
        background: url("../images/section_bg.png") left -56px no-repeat;
      }
      .icon-liwu{
        font-size: 20px;
        color: #fb8b77;
      }
      input{
        width: 15px;
        height: 15px;
      }
    }
  }
}
.show-box{
  margin-bottom: 15px;
  padding: 0 10px 0 0;
  .no-padding{
    padding: 0;
  }
  table{
    tr{
      td{
        padding: 20px 20px 20px 20px;
      }
      .td-bg{
        background-color: #eef7fe;
        width: 150px;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        text-align: center;
      }
      .ccharge{
        width: 74px;
        height: 34px;
        margin-right: 5px;
        text-indent: 10px;
      }
      .sm{
        width: 200px;
      }
      .update{
        color: #f25232;
      }
    }
  }
}
.advance-search {
  position: relative;
  .icon-weirili {
    position: absolute;
    height: 36px;
    line-height: 36px;
    right: 5px;
    top: 0;
    cursor: pointer;
    font-size: 23px;
    color: #a4a8b3;
  }
}
.submit{
  text-align: right;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 80px;
  line-height: 80px;
  background-color: rgba(255,255,255,0.6);
  .sum{
    margin-right: 36px;
    em{
      color: #f25232;
      font-size: 24px;
      margin-left: 10px;
    }
  }
  .btn{
    background-color: #1fa2f5;
    color: #fff;
    width: 188px;
    height: 80px;
    font-size: 24px;
    vertical-align: top;
    @include circleBox(0);
  }
}
.sign{
  input[type="checkbox"]{
    vertical-align: middle;
    width: 15px;
    height: 15px;
  }
  span{
    vertical-align: middle;
  }
  font-size: 14px;
  line-height: 14px;
  color: #686868;
}
.btn-50{
  padding: 10px 50px;
}

.no-overflow{
  overflow: visible;
}
.color-style-list {
  padding: 10px 0;
  li+li{
    border-top: dashed #e0e0e0 1px;
  }
  li {
    background-color: #fafafa;
    height: 50px;
    line-height: 50px;
    border-top: none;
    width: 100%;
    overflow: hidden;
    i {
      margin-left: 9px;
      float: left;
      margin-top: 23px;
    }
    div {
      width: 45%;
      float: left;
      color: #333333;
      font-size: 14px;
      .section {
        width: 22px;
        height: 28px;
        color: #fff;
        line-height: 26px;
        font-size: 12px;
        display: inline-block;
        text-align: center;
        margin-left: 10px;
      }
      .section-red {
        background: url("../images/section_bg.png") left 0 no-repeat;
      }
      .section-blue {
        background: url("../images/section_bg.png") left -28px no-repeat;
      }
      .section-green {
        background: url("../images/section_bg.png") left -56px no-repeat;
      }
      .special-num {
        color: #56b854;
        font-size: 20px;
        display: inline-block;
        margin-top: -8px;
      }
    }
  }
}
.dropdown-menu{
  li{
    height: auto;
    border: none;
  }
}



