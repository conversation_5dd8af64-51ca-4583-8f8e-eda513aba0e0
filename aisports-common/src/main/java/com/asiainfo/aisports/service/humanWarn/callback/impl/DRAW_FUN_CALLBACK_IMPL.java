package com.asiainfo.aisports.service.humanWarn.callback.impl;

import com.asiainfo.aisports.service.humanWarn.callback.DRAW_FUN_CALLBACK;
import com.sun.jna.NativeLong;
import com.sun.jna.Pointer;

public class DRAW_FUN_CALLBACK_IMPL implements DRAW_FUN_CALLBACK {
	@Override
	public void invoke(<PERSON>Long lLiveHandle, Pointer hDC, Pointer pUser) {
		System.out.println("DRAW_FUN_CALLBACK_IMPL 回调方法被执行");
	}
}
