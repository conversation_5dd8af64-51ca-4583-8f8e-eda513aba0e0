package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class GroupPurchaseItem {
    private Long groupPurchaseItemId;

    private Long groupPurchaseId;

    private Long groupPurchasePrice;

    private Long soldNum;

    private Integer buyNumLimit;

    private Long saleProductId;

    private String autoFinish;

    private Integer joinTimesLimit;

    private Integer initJoinerNum;

    private Integer groupNumLimit;

    private Long totalNum;

    private String state;

    private String frontShow;

    private Long createStaffId;

    private Date createTime;

    private Long updateStaffId;

    private Date updateTime;

    public Long getGroupPurchaseItemId() {
        return groupPurchaseItemId;
    }

    public void setGroupPurchaseItemId(Long groupPurchaseItemId) {
        this.groupPurchaseItemId = groupPurchaseItemId;
    }

    public Long getGroupPurchaseId() {
        return groupPurchaseId;
    }

    public void setGroupPurchaseId(Long groupPurchaseId) {
        this.groupPurchaseId = groupPurchaseId;
    }

    public Long getGroupPurchasePrice() {
        return groupPurchasePrice;
    }

    public void setGroupPurchasePrice(Long groupPurchasePrice) {
        this.groupPurchasePrice = groupPurchasePrice;
    }

    public Integer getGroupNumLimit() {
        return groupNumLimit;
    }

    public void setGroupNumLimit(Integer groupNumLimit) {
        this.groupNumLimit = groupNumLimit;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getFrontShow() {
        return frontShow;
    }

    public void setFrontShow(String frontShow) {
        this.frontShow = frontShow == null ? null : frontShow.trim();
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getSoldNum() {
        return soldNum;
    }

    public void setSoldNum(Long soldNum) {
        this.soldNum = soldNum;
    }

    public Integer getBuyNumLimit() {
        return buyNumLimit;
    }

    public void setBuyNumLimit(Integer buyNumLimit) {
        this.buyNumLimit = buyNumLimit;
    }

    public Long getSaleProductId() {
        return saleProductId;
    }

    public void setSaleProductId(Long saleProductId) {
        this.saleProductId = saleProductId;
    }

    public String getAutoFinish() {
        return autoFinish;
    }

    public void setAutoFinish(String autoFinish) {
        this.autoFinish = autoFinish == null ? null : autoFinish.trim();
    }

    public Integer getJoinTimesLimit() {
        return joinTimesLimit;
    }

    public void setJoinTimesLimit(Integer joinTimesLimit) {
        this.joinTimesLimit = joinTimesLimit;
    }

    public Integer getInitJoinerNum() {
        return initJoinerNum;
    }

    public void setInitJoinerNum(Integer initJoinerNum) {
        this.initJoinerNum = initJoinerNum;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", groupPurchaseItemId=").append(groupPurchaseItemId);
        sb.append(", groupPurchaseId=").append(groupPurchaseId);
        sb.append(", groupPurchasePrice=").append(groupPurchasePrice);
        sb.append(", groupNumLimit=").append(groupNumLimit);
        sb.append(", totalNum=").append(totalNum);
        sb.append(", state=").append(state);
        sb.append(", frontShow=").append(frontShow);
        sb.append(", createStaffId=").append(createStaffId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", soldNum=").append(soldNum);
        sb.append(", buyNumLimit=").append(buyNumLimit);
        sb.append(", saleProductId=").append(saleProductId);
        sb.append(", autoFinish=").append(autoFinish);
        sb.append(", joinTimesLimit=").append(joinTimesLimit);
        sb.append(", initJoinerNum=").append(initJoinerNum);
        sb.append("]");
        return sb.toString();
    }
}