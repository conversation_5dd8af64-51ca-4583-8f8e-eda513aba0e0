package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.UserAccessLog;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface UserAccessLogMapper {
    @Delete({
        "delete from user_access_log",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into user_access_log (id, net_user_id, ",
        "mobile_num, user_name, ",
        "open_id, channel_id, ",
        "center_id, page_url, ",
        "remark, user_ip, ",
        "user_agent, visit_time, ",
        "create_time)",
        "values (#{id,jdbcType=BIGINT}, #{netUserId,jdbcType=BIGINT}, ",
        "#{mobileNum,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, ",
        "#{openId,jdbcType=VARCHAR}, #{channelId,jdbcType=BIGINT}, ",
        "#{centerId,jdbcType=BIGINT}, #{pageUrl,jdbcType=VARCHAR}, ",
        "#{remark,jdbcType=VARCHAR}, #{userIp,jdbcType=VARCHAR}, ",
        "#{userAgent,jdbcType=VARCHAR}, #{visitTime,jdbcType=TIMESTAMP}, ",
        "#{createTime,jdbcType=TIMESTAMP})"
    })
    int insert(UserAccessLog record);

    @Select({
        "select",
        "id, net_user_id, mobile_num, user_name, open_id, channel_id, center_id, page_url, ",
        "remark, user_ip, user_agent, visit_time, create_time",
        "from user_access_log",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_name", property="userName", jdbcType=JdbcType.VARCHAR),
        @Result(column="open_id", property="openId", jdbcType=JdbcType.VARCHAR),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="page_url", property="pageUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_ip", property="userIp", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_agent", property="userAgent", jdbcType=JdbcType.VARCHAR),
        @Result(column="visit_time", property="visitTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP)
    })
    UserAccessLog selectByPrimaryKey(Long id);

    @Update({
        "update user_access_log",
        "set net_user_id = #{netUserId,jdbcType=BIGINT},",
          "mobile_num = #{mobileNum,jdbcType=VARCHAR},",
          "user_name = #{userName,jdbcType=VARCHAR},",
          "open_id = #{openId,jdbcType=VARCHAR},",
          "channel_id = #{channelId,jdbcType=BIGINT},",
          "center_id = #{centerId,jdbcType=BIGINT},",
          "page_url = #{pageUrl,jdbcType=VARCHAR},",
          "remark = #{remark,jdbcType=VARCHAR},",
          "user_ip = #{userIp,jdbcType=VARCHAR},",
          "user_agent = #{userAgent,jdbcType=VARCHAR},",
          "visit_time = #{visitTime,jdbcType=TIMESTAMP},",
          "create_time = #{createTime,jdbcType=TIMESTAMP}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(UserAccessLog record);

    @Select({
        "<script>",
        "select",
        "id, net_user_id, mobile_num, user_name, open_id, channel_id, center_id, page_url, remark, user_ip, user_agent, visit_time, create_time",
        "from user_access_log",
        "<where>",
        "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
        "<if test=\"netUserId != null\">and net_user_id = #{netUserId,jdbcType=BIGINT}</if>",
        "<if test=\"mobileNum != null\">and mobile_num = #{mobileNum,jdbcType=VARCHAR}</if>",
        "<if test=\"userName != null\">and user_name = #{userName,jdbcType=VARCHAR}</if>",
        "<if test=\"openId != null\">and open_id = #{openId,jdbcType=VARCHAR}</if>",
        "<if test=\"channelId != null\">and channel_id = #{channelId,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"pageUrl != null\">and page_url = #{pageUrl,jdbcType=VARCHAR}</if>",
        "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
        "<if test=\"userIp != null\">and user_ip = #{userIp,jdbcType=VARCHAR}</if>",
        "<if test=\"userAgent != null\">and user_agent = #{userAgent,jdbcType=VARCHAR}</if>",
        "<if test=\"visitTime != null\">and visit_time = #{visitTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="net_user_id", property="netUserId", jdbcType=JdbcType.BIGINT),
        @Result(column="mobile_num", property="mobileNum", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_name", property="userName", jdbcType=JdbcType.VARCHAR),
        @Result(column="open_id", property="openId", jdbcType=JdbcType.VARCHAR),
        @Result(column="channel_id", property="channelId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="page_url", property="pageUrl", jdbcType=JdbcType.VARCHAR),
        @Result(column="remark", property="remark", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_ip", property="userIp", jdbcType=JdbcType.VARCHAR),
        @Result(column="user_agent", property="userAgent", jdbcType=JdbcType.VARCHAR),
        @Result(column="visit_time", property="visitTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<UserAccessLog> selectByFields(UserAccessLog param);

    @Update({
        "<script>",
        "update user_access_log",
        "<set>",
        "<if test=\"netUserId != null\">net_user_id = #{netUserId,jdbcType=BIGINT},</if>",
        "<if test=\"mobileNum != null\">mobile_num = #{mobileNum,jdbcType=VARCHAR},</if>",
        "<if test=\"userName != null\">user_name = #{userName,jdbcType=VARCHAR},</if>",
        "<if test=\"openId != null\">open_id = #{openId,jdbcType=VARCHAR},</if>",
        "<if test=\"channelId != null\">channel_id = #{channelId,jdbcType=BIGINT},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"pageUrl != null\">page_url = #{pageUrl,jdbcType=VARCHAR},</if>",
        "<if test=\"remark != null\">remark = #{remark,jdbcType=VARCHAR},</if>",
        "<if test=\"userIp != null\">user_ip = #{userIp,jdbcType=VARCHAR},</if>",
        "<if test=\"userAgent != null\">user_agent = #{userAgent,jdbcType=VARCHAR},</if>",
        "<if test=\"visitTime != null\">visit_time = #{visitTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where id = #{id,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(UserAccessLog record);
}
