package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.RpReportFieldAttr;
import com.asiainfo.aisports.domain.core.RpReportFieldAttrKey;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface RpReportFieldAttrMapper {
    @Delete({
        "delete from rp_report_field_attr",
        "where field_id = #{fieldId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}",
          "and report_id = #{reportId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(RpReportFieldAttrKey key);

    @Insert({
        "insert into rp_report_field_attr (field_id, attr_code, ",
        "report_id, attr_value, ",
        "create_staff_id, create_time, ",
        "update_staff_id, update_time)",
        "values (#{fieldId,jdbcType=BIGINT}, #{attrCode,jdbcType=VARCHAR}, ",
        "#{reportId,jdbcType=BIGINT}, #{attrValue,jdbcType=VARCHAR}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP})"
    })
    int insert(RpReportFieldAttr record);

    @Select({
        "select",
        "field_id, attr_code, report_id, attr_value, create_staff_id, create_time, update_staff_id, ",
        "update_time",
        "from rp_report_field_attr",
        "where field_id = #{fieldId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}",
          "and report_id = #{reportId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="field_id", property="fieldId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="report_id", property="reportId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    RpReportFieldAttr selectByPrimaryKey(RpReportFieldAttrKey key);

    @Update({
        "update rp_report_field_attr",
        "set attr_value = #{attrValue,jdbcType=VARCHAR},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
        "where field_id = #{fieldId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}",
          "and report_id = #{reportId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(RpReportFieldAttr record);

    @Select({
        "<script>",
        "select",
        "field_id, attr_code, report_id, attr_value, create_staff_id, create_time, update_staff_id, update_time",
        "from rp_report_field_attr",
        "<where>",
        "<if test=\"fieldId != null\">and field_id = #{fieldId,jdbcType=BIGINT}</if>",
        "<if test=\"attrCode != null\">and attr_code = #{attrCode,jdbcType=VARCHAR}</if>",
        "<if test=\"reportId != null\">and report_id = #{reportId,jdbcType=BIGINT}</if>",
        "<if test=\"attrValue != null\">and attr_value = #{attrValue,jdbcType=VARCHAR}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="field_id", property="fieldId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="report_id", property="reportId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<RpReportFieldAttr> selectByFields(RpReportFieldAttr param);

    @Update({
        "<script>",
        "update rp_report_field_attr",
        "<set>",
        "<if test=\"attrValue != null\">attr_value = #{attrValue,jdbcType=VARCHAR},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where field_id = #{fieldId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}",
          "and report_id = #{reportId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(RpReportFieldAttr record);

    @Select({
            "<script>",
            "select",
            "field_id, attr_code, report_id, attr_value, update_time",
            "from rp_report_field_attr",
            "where report_id = #{reportId,jdbcType=BIGINT}",
            "<if test=\"fieldId != null\">and field_id = #{fieldId,jdbcType=BIGINT}</if>",
            "order by field_id",
            "</script>"
    })
    @Results({
            @Result(column="field_id", property="fieldId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
            @Result(column="report_id", property="reportId", jdbcType=JdbcType.BIGINT),
            @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<DataMap> selectFieldsAttrsByReportId(@Param("reportId") Long reportId, @Param("fieldId") Long fieldId);
}