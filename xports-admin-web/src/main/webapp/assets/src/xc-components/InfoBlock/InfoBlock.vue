<template>
  <div class="info-block">
    <h1 class="title">{{title}}</h1>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {}
  },
  props: ['title']
}
</script>
<style>
  .info-block{
    background-color: #fff;
    color: #303133;
    padding-top: 20px;
  }
  .info-block h1{
    font-size: 16px;
    margin: 0;
    font-weight: normal;
  }
  .info-block>.content{
    padding: 20px 0;
  }
  .info-block .title{
    line-height: 40px;
    border-bottom: 1px solid #409EFF;
  }
</style>
