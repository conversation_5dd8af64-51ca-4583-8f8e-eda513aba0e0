<template>
    <div class="dist-wrap">
        <basic-modal
            :width="890"
            :visible="value"
            :on-request-close="handleModalClose"
            :on-confirm="handleModalConfirm"
            title="会员分配">
            <div
                ref="form"
                class="form-wrap leave-modal-wrap">
                <ul class="result-list">
                    <li
                        :class="{active: resultIndex === 0}"
                        @click="handleResultClick(0)">单个员工分配</li>
                    <li
                        :class="{active: resultIndex === 1}"
                        @click="handleResultClick(1)">批量员工分配</li>
                </ul>
                <div class="additional-info">
                    <header>
                        <h3 class="left">已选会员数（{{ selectedNum }}人）</h3>
                        <ul class="labels right">
                            <li class="lbl-member">正式会员</li>
                            <li class="lbl-visitor">潜在会员</li>
                        </ul>
                        <div class="advance-search right">
                            <input
                                v-model="conQuery"
                                placeholder="员工查询"
                                class="normal"
                                type="text">
                            <i
                                id="out-ad-search"
                                class="iconfont icon-sousuo"></i>
                        </div>
                    </header>
                    <table class="simple-splitter">
                        <tbody>
                            <tr ref="thead">
                                <th
                                    v-if="resultIndex === 0"
                                    class="tr-placeholder"></th>
                                <th>员工</th>
                                <th>工作组</th>
                                <th>会员分配情况</th>
                                <th v-if="resultIndex === 1">本次分配数量</th>
                            </tr>
                        </tbody>
                    </table>
                    <div class="simple-splitter-wrap">
                        <table class="simple-splitter">
                            <tbody>
                                <tr
                                    v-for="(log, index) in filteredCons"
                                    ref="tbody"
                                    :key="log.staffId">
                                    <td v-if="resultIndex === 0">
                                        <input
                                            :value="log.staffId"
                                            v-model="modalData.selectedCon"
                                            type="radio"
                                            class="radio"></input>
                                    </td>
                                    <td>{{ log.staffName }}</td>
                                    <td>{{ log.groupName }}</td>
                                    <td>
                                        <dist-bar
                                            :left="log.memberStaffNum"
                                            :right="log.visitorStaffNum"
                                            :max="maxConNum"></dist-bar>
                                        <span :class="{'dist-total': log.totalNum != 0}">{{ log.totalNum }}人</span>
                                    </td>
                                    <td v-if="resultIndex === 1">
                                        <input
                                            v-if="index === 0"
                                            v-model="log.num"
                                            class="normal dist-num"
                                            type="text"
                                            vtext="分配数量必须是数字！"
                                            vtype="number"
                                            data-placement="bottom">
                                        <input
                                            v-if="index !== 0"
                                            v-model="log.num"
                                            class="normal dist-num"
                                            type="text"
                                            vtext="分配数量必须是数字！"
                                            vtype="number">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </basic-modal>
        <div
            v-if="selectedNum !== 0"
            class="side-widget">
            <div class="con-count">
                <div class="red-dot">{{ selectedNum }}</div>
                <i class="iconfont icon-iconfontqunzu"></i>
            </div>
            <div class="con-dist">
                <p>
                <strong>{{ selectedNum }}</strong>人</p>
                <button
                    class="btn btn-md btn-primary"
                    @click="openModal">分配会员</button>
            </div>
        </div>
    </div>
</template>

<script>
import Modal from 'vue-components/Modal.vue'
import BasicModal from 'vue-components/BasicModal'
import GSelect from 'vue-components/GSelect.vue'
import Checkbox from 'vue-components/Checkbox.vue'
import _ from 'lodash'
import 'xValidate'
import { getGuidanceInfo } from './services'
import DistBar from './DistBar.vue'
import mAlert from 'mAlert'

// events: submit, input
export default {
    components: {
        'basic-modal': BasicModal,
        'g-select': GSelect,
        checkbox: Checkbox,
        DistBar,
    },
    // use value for modal show
    props: ['initialData', 'value', 'siteOptions', 'selectedNum'],
    data() {
        return {
            resultIndex: 0,
            consultants: [],
            conQuery: '',
            modalData: {
                selectedCon: '',
            },
        }
    },
    computed: {
        maxConNum() {
            return _.max(_.map(this.consultants, 'totalNum'))
        },
        filteredCons() {
            if (this.conQuery) {
                return this.consultants.filter(con => con.staffName.indexOf(this.conQuery) !== -1)
            }
            return this.consultants
        },
    },
    watch: {
        value(val) {
            if (val) {
                this.$nextTick(this.syncTheadWidth)
            }
        },
        resultIndex() {
            this.$nextTick(this.syncTheadWidth)
        },
    },
    mounted() {
        this.modalData = _.assign({}, this.modalData, this.initialData)
        this.loadCons()
    },
    methods: {
        loadCons() {
            getGuidanceInfo().then((data) => {
                this.consultants = data.list.map(con => ({
                    ...con,
                    staffId: `${con.staffId},${con.groupId}`,
                    num: '',
                }))
            }).then(() => { this.$nextTick(this.syncTheadWidth) })
        },
        syncTheadWidth() {
            if (this.$refs.tbody.length > 0) {
                const tbodyLine = $(this.$refs.tbody[0]).find('td')
                $(this.$refs.thead).find('th').each(function (index) {
                    $(this).width(tbodyLine.eq(index).outerWidth())
                })
            }
        },
        openModal() {
            this.$emit('input', true)
        },
        handleResultClick(code) {
            this.resultIndex = code
        },
        handleModalClose() {
            this.$emit('input', false)
        },
        reset() {
            this.resultIndex = 0
            this.conQuery = ''
            this.modalData = {
                selectedCon: '',
            }
        },
        handleModalConfirm() {
            if (!$(this.$refs.form).validate()) {
                return false
            }
            if (this.resultIndex === 0) {
                if (!this.modalData.selectedCon) {
                    mAlert.showWrong('请选择会籍顾问！')
                    return false
                }
                this.$emit('submit', [{
                    id: this.modalData.selectedCon,
                    num: this.selectedNum,
                }])
            } else {
                if (this.consultants.length === 0) {
                    mAlert.showWrong('未填写分配数量！')
                    return false
                }
                const isAllNumValid = this.consultants.filter(con => con.num).every(con => con.num >= 0)

                if (!isAllNumValid) {
                    mAlert.showWrong('分配数量不能为负！')
                    return false
                }

                const result = this.consultants
                    .filter(con => Number(con.num) > 0)
                    .map(con => ({
                        id: con.staffId,
                        num: Number(con.num),
                    }))

                const num = result.reduce((a, b) => a + b.num, 0)

                if (num !== this.selectedNum) {
                    mAlert.showWrong(`分配的数量(${num})和所选会员数量(${this.selectedNum})不一致！`)
                    return false
                }

                this.$emit('submit', result)
                this.reset()
            }
        },
    },
}
</script>
