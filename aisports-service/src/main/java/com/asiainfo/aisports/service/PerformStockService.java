package com.asiainfo.aisports.service;

import com.asiainfo.aisports.persistence.core.PerformStockMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2025/6/25 17:33
 */
@Service
public class PerformStockService {

    @Autowired
    private PerformStockMapper performStockMapper;


    @Transactional
    public void updateRemain(Long stockId, int reduceNum) {
        performStockMapper.updateRemain(stockId, -reduceNum);

    }
}
