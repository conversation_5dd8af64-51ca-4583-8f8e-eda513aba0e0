@import "vals";
@import "../../../common/scss/mixin";
$ticket-padding : 24px;
$ticket-item-height : 72px;
@mixin ticketPrice(){
  .ticket-price{
    position:relative;
    .swiper-con{
      margin: 0px 24px;
    }
    .list-con{
      li{
        height: $ticket-item-height;
        line-height: $ticket-item-height;
        border-bottom: 1px solid rgba(255,255,255,.1);
        @include clearfix();
      }
      .ticket-price{
        float: right;
      }
      .ticket-name{
        float: left;
      }
    }
  }
}