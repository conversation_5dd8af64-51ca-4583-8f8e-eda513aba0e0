<template>
    <div class="title-box">
        <h1 style="display: block">
            <span>{{title}}</span>
            <span class="btn-return"
                onClick="window.history.back()">
                <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
            </span>
        </h1>
        <div class="display-box">
            <div class="score-item-list" v-if="calculateMode === '2'">
                <score-item
                    action="detail"
                    v-for="item in scoreItemList"
                    :data="item">
                </score-item>
            </div>
            <configurable-table
                :column-config="table.config"
                :data="table.data"
                v-if="table.data.length > 0">
                <template slot="opt" slot-scope="slotProps">
                    <a class="blue-underline-text">{{slotProps.data.text}}</a>
                </template>
            </configurable-table>
            <pagination
                v-if="table.data.length > 0"
                :current-page="paginationModel.currentPage"
                :page-size="paginationModel.pageSize"
                :total="paginationModel.total"
                @changepage="onPageChange"
                @changepagesize="onPageSizeChange"
                style="margin-top: 20px;">
            </pagination>
        </div>
        
    </div>
</template>
<script>
import Pagination from 'vue-components/Pagination.vue'
import ScoreItem from 'business/Score/ScoreItem.vue'
import * as API from '../utils/api.js'
import moment from 'moment'

const complaintConfig = [
    { key: 'projectName', label: '项目目名称' },
    { key: 'id', label: '投诉单号' },
    { key: 'complaintItemName', label: '投诉内容' },
    { key: 'createTime', label: '投诉时间' },
    { key: 'phoneNum', label: '电话号码' },
    { key: 'opt', label: '操作', slot: 'opt' },
]

const activityConfig = [
    { key: 'activityNo', label: '活动编号' },
    { key: 'name', label: '活动名称' },
    { key: 'organizer', label: '活动主办方' },
    { key: 'dateRange', label: '活动日期' },
    { key: 'value', label: '计次' },
    { key: 'opt', label: '操作', slot: 'opt' },
]

export default {
    components: {
        Pagination,
        ScoreItem
    },
    props: {
        
    },
    data() {
        return {
            title: '',
            id: '',
            calculateMode: '',
            scoreItemList: [],
            table: {
                config: [],
                data: []
            },
            paginationModel: {
                currentPage: 1,
                pageSize: 10,
                total: 0,
            }
        }
    },
    watch: {
        
    },
    mounted() {
        this.title = this.$route.query.title
        this.id = this.$route.query.id
        this.calculateMode = this.$route.query.calculateMode
        if(this.calculateMode === '2') this.getScore()
        else if(this.calculateMode === '1') this.getComplaintScore()
        else if(this.calculateMode === '3' || this.calculateMode === '4') this.getActivityScore()
    },
    methods: {
        getScore() {
            if(!this.id) return
            let req = {
                scoreSubjectId: this.id,
                scoreCycle: this.$route.query.scoreCycle,
                year: this.$route.query.year
            }
            if(this.$route.query.scoreCycle === '2') req.quarter = this.$route.query.quarter
            API.findArtificialScoreList(req).then(res => {
                this.scoreItemList = res.dataList
            })
        },
        getComplaintScore() {
            let req = {
                scoreSubjectId: this.id,
                scoreCycle: this.$route.query.scoreCycle,
                year: this.$route.query.year,
                pageNum: this.paginationModel.currentPage,
                pageSize: this.paginationModel.pageSize
            }
            if(this.$route.query.scoreCycle === '2') req.quarter = this.$route.query.quarter
            API.findComplaintScoreList(req).then(res => {
                this.table.config = complaintConfig
                this.table.data = res.dataList.list.map(item => ({
                    ...item,
                    createTime: item.createTime.replace(/-/g, '/'),
                    opt: { text: '查看' }
                }))
            })
        },
        getActivityScore() {
            let req = {
                scoreSubjectId: this.id,
                scoreCycle: this.$route.query.scoreCycle,
                calculateMode: this.calculateMode,
                year: this.$route.query.year,
                pageNum: this.paginationModel.currentPage,
                pageSize: this.paginationModel.pageSize
            }
            if(this.$route.query.scoreCycle === '2') req.quarter = this.$route.query.quarter
            API.findActivityScoreList(req).then(res => {
                this.table.config = activityConfig
                this.table.data = res.dataList.list.map(item => ({
                    ...item,
                    dateRange: moment(item.startDate, 'YYYY-MM-DD hh:mm:ss').format('YYYY/MM/DD')
                        + ' - '
                        + moment(item.endDate, 'YYYY-MM-DD hh:mm:ss').format('YYYY/MM/DD'),
                    opt: { text: '查看' }
                }))
            })
        },
        onPageChange(newPage) {
            document.body.scrollTop = 0
            this.paginationModel.currentPage = newPage
        },
        onPageSizeChange(newPageSize) {
            this.paginationModel.pageSize = newPageSize
        }
    }
}
</script>