package com.asiainfo.aisports.service.postPay;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR>
 * @date 2021-07-23 14:24
 */
public class PostBillingStrategyFactory {

    private PostBillingStrategyFactory(){

    }


    public static BillingStrategyService getPostBillingStrategyService(ApplicationContext applicationContext, String postBillingMode) {
        if (StringUtils.isBlank(postBillingMode)) {
            throw new IllegalArgumentException("postBillingMode不能为空");
        }
        return (BillingStrategyService) applicationContext.getBean("postPay" + postBillingMode + "BillingStrategyService");
    }
}
