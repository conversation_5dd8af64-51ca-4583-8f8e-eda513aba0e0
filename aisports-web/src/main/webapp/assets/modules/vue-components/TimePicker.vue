<template>
  <div class="picker-wrap" ref="selectWrap">
    <div class="picker">
      <input class="normal" type="text" :placeholder="placeholder || '开始时间'" v-model="display" readonly />
    </div>
    <div class="menu" v-if="open">
      <div class="option-wrap" v-for="option in optionsToShow" @click="handleOptionClick(option)" :class="{ 'active': option.value === value }">
        <span>{{option.name}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import _ from 'lodash'
import isDescendant from 'utils/isDescendant'
import addZero from 'utils/addZero'

const timeOptions = []

for (let i = 0; i < 24; i++) {
  _.times(2, num => {
    const hour = i
    const min = addZero(num * 30)
    const value = (2 * i + num).toString()
    const time = `${hour}:${min}`

    timeOptions.push({
      name: time,
      value: time
    })
  })
}
timeOptions.push({
  name: '24:00',
  value: '24:00'
})

export default {
  props: ['value', 'placeholder', 'options'],

  data() {
    return {
      open: false,
      optionsToShow: this.options ? this.options : timeOptions
    }
  },

  computed: {
    display() {
      if (this.optionsToShow && this.optionsToShow.length !== 0) {
        const result = _.find(this.optionsToShow, { value: this.value })

        if (result) {
          return result.name
        }

        return ''
      }

      return ''
    }
  },

  mounted() {
    window.addEventListener('click', this.handleGlobalClick)
  },

  destroyed() {
    window.removeEventListener('click', this.handleGlobalClick)
  },

  methods: {
    handleGlobalClick(e) {
      if (!isDescendant(this.$refs.selectWrap, e.target)) {
        this.open = false
      } else {
        this.open = !this.open
      }
    },

    handleOptionClick(option) {
      this.$emit('input', option.value)
    },
  }
}
</script>

<style lang="scss" scoped>
$common_shadow2: 0 4px 16px rgba(0, 0, 0, 0.3);

.picker-wrap {
  position: relative;
  width: 93px;
  display: inline-block;
}

.menu {
  position: absolute;
  width: 100%;
  max-height: 128px;
  border: 1px solid #ddd;
  padding: 2px 5px;
  left: 0;
  max-width: 340px;
  background: #fff;
  border-radius: 3px;
  overflow-y: scroll;
  z-index: 900;
  // box-shadow: $common_shadow2;
}

.option-wrap {
  float: left;
  width: 50%;
  cursor: pointer;
  padding: 0 5px;
  font-size: 12px;

  &.active {
    background: #ddd;
  }
}

.normal {
  width: 100%;
  cursor: pointer;
}
</style>
