package com.asiainfo.aisports.service.training;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.TcPlanStep;
import com.asiainfo.aisports.domain.core.TcPlanStepItem;
import com.asiainfo.aisports.domain.core.TcPlanStepItemFile;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.persistence.core.TcPlanStepItemFileMapper;
import com.asiainfo.aisports.persistence.core.TcPlanStepItemMapper;
import com.asiainfo.aisports.persistence.core.TcPlanStepMapper;
import com.asiainfo.aisports.service.SequenceWrapper;
import com.asiainfo.aisports.service.ServiceResult;
import com.google.common.base.Strings;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by xzeng on 19/1/14.
 * 教案步骤
 */
@Service
public class ApiTcPlanStepService {
    @Autowired
    TcPlanStepMapper tcPlanStepMapper;
    @Autowired
    TcPlanStepItemMapper tcPlanStepItemMapper;
    @Autowired
    SequenceWrapper sequenceWrapper;
    @Autowired
    private TcPlanStepItemFileMapper tcPlanStepItemFileMapper;

    /**
     * 查询有效的步骤列表
     *
     * @param planId
     * @return
     */
    @Transactional(readOnly = true)
    public List<DataMap> findValidStepList(Long planId) {
        TcPlanStep tcPlanStep = new TcPlanStep();
        tcPlanStep.setPlanId(planId);
        tcPlanStep.setState(Constants.Status.VALID);
        List<DataMap> tcPlanStepList = tcPlanStepMapper.selectTcPlanStepList(tcPlanStep);
        tcPlanStepList.forEach(a -> {
            Long stepId = a.getLong("stepId");
            if (stepId != null) {
                //查询步骤项目
                TcPlanStepItem tcPlanStepItem = new TcPlanStepItem();
                tcPlanStepItem.setStepId(stepId);
                tcPlanStepItem.setState(Constants.Status.VALID);
                List<DataMap> tcPlanStepItemList = tcPlanStepItemMapper.selectTcPlanStepItemList(tcPlanStepItem);
                tcPlanStepItemList.forEach(b -> {
                    Long itemId = b.getLong("itemId");
                    if (itemId != null) {
                        TcPlanStepItemFile tcPlanStepItemFile = new TcPlanStepItemFile();
                        tcPlanStepItemFile.setItemId(itemId);
                        tcPlanStepItemFile.setState(Constants.Status.VALID);

                        b.put("fileList", tcPlanStepItemFileMapper.selectByFields(tcPlanStepItemFile));
                    } else {
                        b.put("fileList", new ArrayList<TcPlanStepItemFile>());
                    }
                });

                a.put("planStepItems", tcPlanStepItemList);
            } else {
                a.put("planStepItems", new ArrayList<TcPlanStepItem>());
            }
        });
        return tcPlanStepList;
    }

    /**
     * 修改项目步骤（新增，修改）
     *
     * @param tcPlanStep
     * @param tcPlanStepItems
     * @param staffId
     * @return
     */
    @Transactional
    public ServiceResult editPlanStep(TcPlanStep tcPlanStep, JSONArray tcPlanStepItems, Long staffId) {
        Date now = new Date();
        tcPlanStep.setUpdateStaffId(staffId);
        tcPlanStep.setUpdateTime(now);
        if (tcPlanStep.getStepId() == null) {
            //新增
            tcPlanStep.setStepId(sequenceWrapper.tcPlanStepSequence());
            tcPlanStep.setState(Constants.Status.VALID);
            tcPlanStep.setCreateTime(now);
            tcPlanStep.setCreateStaffId(staffId);
            tcPlanStepMapper.insert(tcPlanStep);
        } else {
            //编辑
            //删除旧的步骤项目
            tcPlanStepItemMapper.deleteByStepId(tcPlanStep.getStepId(), staffId);

            //删除当前步骤下旧的图片
            tcPlanStepItemFileMapper.deleteByStepId(tcPlanStep.getStepId(), staffId);

            tcPlanStepMapper.updateByPrimaryKeySelective(tcPlanStep);
        }

        //新增教学内容
        for (int i = 0; i < tcPlanStepItems.size(); i++) {
            JSONObject object = tcPlanStepItems.getJSONObject(i);
            String itemName = object.getString("itemName");
            Integer groupNum = object.getInt("groupNum");
            String content = object.getString("content");

            TcPlanStepItem tcPlanStepItem = new TcPlanStepItem();
            tcPlanStepItem.setItemId(sequenceWrapper.tcPlanStepItemSequence());
            tcPlanStepItem.setPlanId(tcPlanStep.getPlanId());
            tcPlanStepItem.setStepId(tcPlanStep.getStepId());
            tcPlanStepItem.setItemName(itemName);
            tcPlanStepItem.setGroupNum(groupNum);
            tcPlanStepItem.setContent(content);
            tcPlanStepItem.setState(Constants.Status.VALID);
            tcPlanStepItem.setCreateTime(now);
            tcPlanStepItem.setCreateStaffId(staffId);
            tcPlanStepItem.setUpdateTime(now);
            tcPlanStepItem.setUpdateStaffId(staffId);

            tcPlanStepItemMapper.insert(tcPlanStepItem);

            //保存图片
            if (!Strings.isNullOrEmpty(object.getString("fileList"))) {
                JSONArray fileList = JSONArray.fromObject(object.getString("fileList"));
                for (int j = 0; j < fileList.size(); j++) {
                    JSONObject file = fileList.getJSONObject(j);
                    String fileName = file.getString("fileName");

                    TcPlanStepItemFile tcPlanStepItemFile = new TcPlanStepItemFile();
                    tcPlanStepItemFile.setFileId(sequenceWrapper.fileIdSequence());
                    tcPlanStepItemFile.setItemId(tcPlanStepItem.getItemId());
                    tcPlanStepItemFile.setPlanId(tcPlanStep.getPlanId());
                    tcPlanStepItemFile.setStepId(tcPlanStep.getStepId());
                    tcPlanStepItemFile.setFileName(fileName);
                    tcPlanStepItemFile.setState(Constants.Status.VALID);
                    tcPlanStepItemFile.setCreateTime(now);
                    tcPlanStepItemFile.setCreateStaffId(staffId);
                    tcPlanStepItemFile.setUpdateTime(now);
                    tcPlanStepItemFile.setUpdateStaffId(staffId);

                    tcPlanStepItemFileMapper.insert(tcPlanStepItemFile);
                }
            }
        }

        return new ServiceResult();
    }

    /**
     * 删除教案步骤
     *
     * @param tcPlanStepId
     * @param staffId
     * @return
     */
    @Transactional
    public ServiceResult deletePlanStep(Long tcPlanStepId, Long staffId) {
        Date now = new Date();
        TcPlanStep tcPlanStep = tcPlanStepMapper.selectByPrimaryKey(tcPlanStepId);
        tcPlanStep.setUpdateStaffId(staffId);
        tcPlanStep.setUpdateTime(now);
        tcPlanStep.setState(Constants.Status.INVALID);

        tcPlanStepMapper.updateByPrimaryKeySelective(tcPlanStep);

        //删除当前步骤所属步骤项目
        tcPlanStepItemMapper.deleteByStepId(tcPlanStep.getStepId(), staffId);

        return new ServiceResult();
    }

    /**
     * 删除教案步骤项目
     *
     * @param tcPlanStepItemId
     * @param staffId
     * @return
     */
    @Transactional
    public ServiceResult deletePlanStepItem(Long tcPlanStepItemId, Long staffId) {
        Date now = new Date();
        TcPlanStepItem tcPlanStepItem = tcPlanStepItemMapper.selectByPrimaryKey(tcPlanStepItemId);
        tcPlanStepItem.setState(Constants.Status.INVALID);
        tcPlanStepItem.setUpdateTime(now);
        tcPlanStepItem.setUpdateStaffId(staffId);

        tcPlanStepItemMapper.updateByPrimaryKeySelective(tcPlanStepItem);
        return new ServiceResult();
    }

    /**
     * 删除教案步骤项目图片
     *
     * @param tcPlanStepItemFileId
     * @param staffId
     * @return
     */
    @Transactional
    public ServiceResult deletePlanStepItemFile(Long tcPlanStepItemFileId, Long staffId) {
        Date now = new Date();
        TcPlanStepItemFile tcPlanStepItemFile = tcPlanStepItemFileMapper.selectByPrimaryKey(tcPlanStepItemFileId);
        tcPlanStepItemFile.setState(Constants.Status.INVALID);
        tcPlanStepItemFile.setUpdateTime(now);
        tcPlanStepItemFile.setUpdateStaffId(staffId);

        tcPlanStepItemFileMapper.updateByPrimaryKeySelective(tcPlanStepItemFile);
        return new ServiceResult();
    }
}
