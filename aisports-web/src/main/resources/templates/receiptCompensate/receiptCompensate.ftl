<#include "../public/macros.ftl">
<!DOCTYPE html>
<html>
<head>
<#assign assetState="compressed" />
    <@head_declares />
    <title>收据补打</title>
    <@head_includes path="receiptCompensate" filename="receiptCompensate" />
    <#--  <link rel="stylesheet" href="/assets/release/static/receiptCompensate/receiptCompensate.css"/>  -->
    <#--  <@head_includes />  -->
</head>
<body>
<#--目前只是tradeId的自动查询,如果有卡号的自动查询,auto-search-type通过语句填写 trandeId 或者cardNo-->
<input type="hidden" value="<#if tradeId??>${tradeId}</#if>" auto-search-type = 'tradeId' id="autoSearchType"/>
<div class="wrapper">
    <!--header-->
<#include "../public/header.ftl">
    <!--left menu-->
<#include "../public/aside.ftl">
    <div class="main" ${bodyStyle!}>
        <div class="review">
            <div class="top-info clearfix">
                <div class="title-box">
                    <div class="sub-box">
                        <form>
                            <fieldset>
                                <!-- 查询 -->
                                <div class="query">
                                    <div class="mem-submit">
                                        <select name="" class="select-picker" id="queryKey">
                                            <option value="1" <#if tradeId??>selected</#if>>流水号</option>
                                            <option value="0">卡号</option>
                                        </select>
                                        <input type="text" placeholder="请输入流水号" placeholder="" class="normal md" id="eCardNo">
                                        <button type="button" class="btn btn-md btn-primary s-btn" id="query-btn">查询</button>
                                    </div>
                                    <div class="mem-submit extra-conditions" id="extra-conditions">
                                        <div class="conditions">
                                            <select name="" class="select-picker" id="tradeTypeCode">
                                                <option value="" selected>请选择业务类型</option>
                                            <#list tradeTypes as tradeType>
                                                <option value="${tradeType.key!''}">${tradeType.value!''}</option>
                                            </#list>
                                            </select>
                                            <select name="" class="select-picker" id="staffId">
                                                <option value="" selected>请选择销售员</option>
                                            <#list staffList as staff>
                                                <option value="${staff.staffId!''}">${staff.staffName!''}</option>
                                            </#list>
                                            </select>
                                        </div>
                                        <div class="conditions">
                                            <div class="dateSelect">
                                                <div class="date-plugin date start">
                                                    <input name="daterange" readonly type="text" placeholder="起始日期" id="startDate">
                                                    <label for="sartDate">
                                                        <i class="iconfont icon-weirili" id="start"></i>
                                                    </label>
                                                </div>
                                                <div class="bootstrap-timepicker">
                                                    <input type="text" value="00:00:00" class="timepicker" id="startTime" placeholder="起始时间" />
                                                </div>
                                            </div>
                                            <span class="line-center"></span>
                                            <div class="dateSelect">
                                                <div class="date-plugin date end">
                                                    <input name="daterange" readonly type="text" placeholder="结束日期" id="endDate" />
                                                    <label for="endDate">
                                                        <i class="iconfont icon-weirili" id="end"></i>
                                                    </label>
                                                </div>
                                                <div class="bootstrap-timepicker">
                                                    <input type="text" class="timepicker" id="endTime" placeholder="结束时间" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 主要内容 -->
                                <div class="content">
                                    <div class="background-img">
                                        <div class="content-main" id="receiptList">
                                            <#include "receiptList.ftl">
                                        </div>
                                        <div class="f-line f-line-btn">
                                            <button type="button" class="btn btn-lg btn-hw-1fa2f5 prev" id="print">打印</button>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                    </div>
                    <!--第二屏-->
                </div>
            </div>
        </div>
    </div>
</div>
<@card_includes iccard='true' />
<#--  <script type="text/javascript">
    seajs.use("/assets/static/receiptCompensate/src/receiptCompensate");
</script>  -->
<@foot_includes path="receiptCompensate" filename="receiptCompensate" />

</body>
</html>