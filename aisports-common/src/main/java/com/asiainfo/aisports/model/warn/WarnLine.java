package com.asiainfo.aisports.model.warn;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;

public class WarnLine {

    @JacksonXmlProperty(localName = "x1")
    private WarnDto x1;
    @JacksonXmlProperty(localName = "y1")
    private WarnDto y1;
    @JacksonXmlProperty(localName = "x2")
    private WarnDto x2;
    @JacksonXmlProperty(localName = "y2")
    private WarnDto y2;
    @JacksonXmlProperty(localName = "Direct")
    private WarnDto Direct;

    public WarnDto getX1() {
        return x1;
    }

    public void setX1(WarnDto x1) {
        this.x1 = x1;
    }

    public WarnDto getY1() {
        return y1;
    }

    public void setY1(WarnDto y1) {
        this.y1 = y1;
    }

    public WarnDto getX2() {
        return x2;
    }

    public void setX2(WarnDto x2) {
        this.x2 = x2;
    }

    public WarnDto getY2() {
        return y2;
    }

    public void setY2(WarnDto y2) {
        this.y2 = y2;
    }

    public WarnDto getDirect() {
        return Direct;
    }

    public void setDirect(WarnDto direct) {
        Direct = direct;
    }
}
