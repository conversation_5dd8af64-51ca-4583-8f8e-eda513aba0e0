<template>
    <div class="title-box">
        <h1 style="display:block">
            <span>新增</span>
            <span
                    class="btn-return"
                    @click="goBack">
                <i class="iconfont icon-zuojiantou"></i>
            </span>
        </h1>
        <div class="sub-box">
            <div class="form-line">
                <div class="seach-line">
                    <div class="form-label">客户卡号
                    </div>
                    <div class="form-field">
                        <ecard-search
                                :value="editObj.ecardNo"
                                v-model="editObj.ecardNo"
                                icon="icon-sousuo"
                                width="250"
                                advance-search="true"
                                placeholder="请输入卡号"
                                @keydown.enter="query"
                                @search="query"
                                @select-card="handleSelectCard"
                        >
                        </ecard-search>
                        <button
                                id="query"
                                class="btn btn-md btn-primary query searchBtn"
                                @click="query">查询
                        </button>
                    </div>
                </div>
            </div>
            <div class="form-line">
                <div class="form-label required">手机号码</div>
                <div class="form-field">
                    <v-input
                            :disabled="mobileNumInputDisabledTag"
                            v-validate="'required|mobile'"
                            name="mobileNum"
                            v-model="editObj.mobileNum"
                            @change="checkMobileNum"
                            placeholder="请输入手机号码"
                    ></v-input>
                </div>
            </div>
            <div class="form-line">
                <div class="form-label required">测试人
                </div>
                <div class="form-field">
                    <v-input
                            v-model="editObj.memberName"
                            v-validate="'required'"
                            name="memberName"
                            :disabled="memberNameInputDisabledTag"
                            placeholder="请输入测试人姓名"
                    ></v-input>
                </div>
            </div>
            <div class="form-line">
                <div class="form-label required">报告名称
                </div>
                <div class="form-field ">
                    <v-input
                            v-model="editObj.reportName"
                            v-validate="'required'"
                            name="reportName"
                            placeholder="请输入报告名称"
                    ></v-input>
                </div>
            </div>
            <div class="form-line">
                <div class="form-label required">体测时间
                </div>
                <div class="form-field">
                    <el-date-picker
                            v-model="editObj.testTime"
                            v-validate="'required'"
                            name="testTime"
                            type="date"
                            :picker-options="pickerOptions"
                            placeholder="选择日期">
                    </el-date-picker>
                </div>
            </div>
            <div class="form-line">
                <div class="form-label">设备厂家
                </div>
                <div class="form-field">
                    <el-select
                            v-model="editObj.equipmentModelId"
                            clearable
                            class="input-wrapper"
                            placeholder="请选择设备厂家">
                        <el-option
                                v-for="item in equipmentModelList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div class="form-line">
                <div class="form-label required">项目图片
                </div>
                <div class="upload-container">
                    <el-upload
                            ref="upload"
                            :action="serverUrl"
                            :on-preview="handlePictureCardPreview"
                            :on-remove="handleRemove"
                            :on-success="uploadImgSuccess"
                            :show-file-list="true"
                            :file-list="showImgSource"
                            :before-upload="beforeAvatarUpload"
                            :data="{path: 'image'}"
                            accept=".jpeg, .jpg, .png, .bmp"
                            list-type="picture-card">
                        <i class="el-icon-plus">
                        </i>
                    </el-upload>
                    <el-dialog :visible.sync="dialogVisible">
                        <img
                                :src="dialogImageUrl"
                                width="100%"
                                alt="">
                    </el-dialog>
                </div>
            </div>

            <div class="form-line no-colon">
                <div class="form-label"></div>
                <div class="form-field">
                    <button
                            class="btn btn-md btn-primary btn-confirm"
                            @click="confirm">保存
                    </button>
                    <button
                            class="btn btn-md btn-cancel"
                            @click="goBack">取消
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {showSuccess, showWrong} from 'mAlert'
    import settings from 'aisportsConfig'
    import 'utils/confirm'
    import * as apis from '../utils/api'
    import _ from 'lodash'
    import moment from 'moment'
    import EcardSearch from 'business/EcardSearch.vue'
    import {vValidate} from 'directives/vValidate'

    export default {
        name: 'BmReport',
        components: {
            EcardSearch,
        },
        mixins: [vValidate],
        data() {
            return {
                dialogImageUrl: '',
                dialogVisible: false,
                equipmentModelList: [],
                equipmentModelId: '',
                showImgSource: [],
                editObj: {
                    ecardNo: '',
                    mobileNum: '',
                    memberName: '',
                    reportName: '',
                    testTime: '',
                    equipmentModelId: '',
                    paths: '',
                },
                pictureList: [],
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() > Date.now() - 8.64e6
                    },
                },
                mobileNumInputDisabledTag: false,
                memberNameInputDisabledTag: false,
            }
        },
        computed: {
            ossUrl() {
                return window.ossUrl
            },
            serverUrl() {
                return `${settings.urlPrefix}/upload/temp`
            },
        },
        created() {
            this.getArgs()
        },
        methods: {
            goBack() {
                vueRouter.go(-1)
            },
            getArgs() {
                return apis.findBmEquipmentModel().then((res) => {
                    this.equipmentModelList = res.equipmentModelList
                })
            },
            query() {
                apis.findValidMember(
                    {ecardNo: this.editObj.ecardNo,}
                ).then((res) => {
                    if (res.member) {
                        this.editObj.memberName = res.member.name
                        this.editObj.mobileNum = res.member.mobileNum
                        this.mobileNumInputDisabledTag = true,
                        this.memberNameInputDisabledTag = true
                    } else {
                        this.editObj.memberName = ''
                        this.editObj.mobileNum = ''
                        this.mobileNumInputDisabledTag = false,
                        this.memberNameInputDisabledTag = false
                    }
                })
            },
            checkMobileNum() {
                apis.findValidMember(
                    {mobileNum: this.editObj.mobileNum}
                ).then((res) => {
                    if (res.member) {
                        this.editObj.memberName = res.member.name
                        this.memberNameInputDisabledTag = true
                    } else {
                        this.editObj.memberName = ''
                        this.memberNameInputDisabledTag = false
                    }
                })

            },
            handleSelectCard(data) {
                $('#ecardNo').val(data.ecardNo),
                    this.editObj.ecardNo = data.ecardNo,
                    setTimeout(() => {
                        $('#query').trigger('click')
                    })
                $('#phoneNumber').focus()
            },
            handlePictureCardPreview(file) {
                this.dialogImageUrl = file.url
                this.dialogVisible = true
            },
            handleRemove(res) {
                this.pictureList = _.filter(this.pictureList, m => m.path !== (res.path || res.response.filePath))
                let list = _.filter(this.trainingImgResources, m => m.path === (res.path || res.response.filePath))
                if (list.length) {
                    this.removePath.push(list[0].id)
                }
            },
            uploadImgSuccess(res, file) {
                this.pictureList = [
                    ...this.pictureList,
                    {
                        name: res.fileName,
                        url: this.ossUrl + res.fileName,
                        path: res.fileName,
                    }
                ]
            },
            /**
             * 图片限制格式和大小
             */
            beforeAvatarUpload(file) {
                const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/bmp';
                const isLimitMb = file.size / 1024 / 1024 <= 5;

                if (!isJPG) {
                    mAlert.showWrong('上传图片只能是jpeg、png、bmp格式!');
                }
                if (!isLimitMb) {
                    mAlert.showWrong('上传图片大小不能超过5MB!');
                }
                return isJPG && isLimitMb;
            },
            confirm() {
                this.validateAll().then((result) => {
                    if (!result) {
                        return
                    }
                    let list = [...this.pictureList]
                    if (!list.length) {
                        showWrong('请上传图片')
                        return
                    }

                    this.editObj.testTime = moment(this.editObj.testTime).format('YYYY-MM-DD HH:mm')
                    this.editObj.paths = _.map(list, m => m.path).join(',')
                    apis.save(this.editObj).then((res) => {
                        if (res.error === 0) {
                            showSuccess('保存成功')
                            this.goBack()
                        } else {
                            showWrong('保存失败')
                        }
                    })
                })
            },
        },
    }
</script>
