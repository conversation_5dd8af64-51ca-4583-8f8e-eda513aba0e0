package com.asiainfo.aisports.domain.core;

public class HousePropertyRentalAttrKey {
    private String rentId;

    private String attrCode;

    public String getRentId() {
        return rentId;
    }

    public void setRentId(String rentId) {
        this.rentId = rentId == null ? null : rentId.trim();
    }

    public String getAttrCode() {
        return attrCode;
    }

    public void setAttrCode(String attrCode) {
        this.attrCode = attrCode == null ? null : attrCode.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", rentId=").append(rentId);
        sb.append(", attrCode=").append(attrCode);
        sb.append("]");
        return sb.toString();
    }
}