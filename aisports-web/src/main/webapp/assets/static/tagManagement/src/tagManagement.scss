@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../../modules/libs/element-ui/element-ui-new/index.css";
.el-table th{
  background-color: #f5f5f5 !important;
  font-size: 14px  !important;
  color: #333  !important;
  font-weight: bold;
}
.el-input__inner {
  border-radius: 4px !important;
  border: 1px solid #e0e0e0 !important;
  height: 36px !important;

}
.left-blue::before{
  display: inline-block;
  content: "";
  border-radius: 2px;
  width: 3px;
  height: 14px;
  margin-right: 10px;
  background-color: #1FA2F5;
}
.tag-auto-info{
  width: 100%;
  min-height: 50px;
  border-radius: 4px;
  border: 1px solid #EAEAEA;
  background-color: #FAFAFA;
  padding: 10px;
  margin: 10px 0;
  .data-info{
    border-radius: 4px;
    padding: 14px 10px;
  }
  .info-item{
    display: flex;
    align-items: center;
    .info-type{
      font-size: 14px;
      color: #999;
      align-self: flex-start;
    }
    .subitem-title{
      padding-right: 10px;
    }
    .content-item{
      display: flex;
      align-items: center;
      .title{
        width: 100px;
      }
      .service{
        width: 80px;
      }
      .time{
        width: 180px;
      }
      .type{
        width: 70px;
      }
    }
    .info-content{
      font-size: 14px;
      color: #333;
    }
  }
}
.auto-tag-content{
  border-top: 1px solid #e0e0e0 ;
  padding: 20px 0;
  .auto-title{
    display: flex;
    align-items: center;
    .title{
      color: #333333;
      font-weight: 600;
      font-size: 14px;
      line-height: 23px;
      padding-right: 14px;
    }
    .desc{
      color: #999999;
      font-weight: 400;
      font-size: 12px;
    }
  }
  .auto-item{
    margin-top: 20px;
    display: flex;
    align-items: center;
    // height: 38px;
    .lable-name{
      color: #333333;
      font-weight: 400;
      font-size: 14px;
      width: 90px;
      text-align: right;
      margin-right: 10px;
    }
    .buy-data{
      width: 862px;
      border-radius: 4px;
      background-color: #FFFFFF;
      border: 1px solid #EAEAEA;
      padding: 10px;
      .data-info{
        border-radius: 4px;
        background-color: #FAFAFA;
        padding: 14px 10px;
      }
      .info-name{
        color: #333333;
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 10px;
      }
      .add-color{
        color: #24a4f5;
        cursor: pointer;
        font-size: 20px;
        line-height: 35px;
      }
      .info-value{
        display: flex;
        align-items: center;
        flex-flow: row wrap;
        gap: 10px;
        
        // justify-content: space-between;
        .info-value-content{
          display: flex;
          align-items: center;
          gap: 10px;
          // flex: 1;
        }
        .unit{
          line-height: 36px;
        }
        // .num-range-tips,{
        //   border-right: 1px solid #E0E0E0;
        //   height: 36px;
        // }
        // .num-unit{
        //   height: 36px;
        //   width: 100%;
        // }

        .num-range{
          display: grid;
          grid-template-columns: 68px 68px 14px 68px 36px;
          grid-template-rows: 36px;
          align-items: center;
          justify-items: center;
          width: 252px;
          height: 36px;
          
          border-radius: 4px;
          border: 1px solid #E0E0E0;
          color: #999;
          .border-left{
          border-left: 1px solid #E0E0E0;

          }
          .border-right{
          border-right: 1px solid #E0E0E0;
            
          }
          .input-num2{
            width: 68px;

            .el-input__inner{
              border: none !important;
              padding:  0 0 0 10px;
              height: 32px !important;
            }
          }
        }
        .select-state{
          width: 120px;
        }
        
        .delete-color{
          color: #f25232;
          cursor: pointer;
          font-size: 20px;
        }
        .input-num{
          width: 120px;
        }
      }
    }

    .lable-value{

    }
  }
}
.el-range-separator{
  display: flex !important;
  align-items: center;
  // line-height: 2 !important;
}
.dialog-footer{
  padding: 24px 20px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e0e0e0 ;

}
.newtag-title{
  font-weight: 600;
  font-size: 16px;
  color: #333333;
  line-height: 64px;
  height: 64px;
  padding-left: 24px;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e0e0e0 ;
  align-items: center;
}
.newtag-content{
  padding-left: 24px;
}
.action-btns{
  display: flex;
  justify-content: space-between;
  width: 150px;
  margin: 0 auto;
}
.tag-btns{
  display: flex;
  justify-content: center;
  margin: 0 auto;
}
.tag-common{
  display: inline-block;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  border-radius: 12px;
  padding: 0 20px;
}
.tag-active{
  background-color: rgba(103, 194, 58, 0.15);
  color: #67c23a;
  border: 1px solid #67c23a;
}
.tag-inactive{
  color: #878D99;
  background-color: rgba(135, 141, 153, 0.1);
  border: 1px solid rgba(135, 141, 153, 0.3);
}
.tag-btns{

}
.el-button{
  height: 36px !important;
  line-height: 35px !important;
  padding: 0 15px !important;
  border-radius: 4px !important;
}
.el-table__row{
  height: 40px;
}
.el-table th{
  padding: 10px 0 !important;
}
.el-table td{
  padding: 0 !important;
}
.title-box{
    background-color: #fff;
    min-height: 620px;
    h1{
      display: block;
    }
  }
  .newAdd{
    display:flex;
    justify-content:end;
  }
  .query-line{
    padding: 20px;
    border-bottom: 1px solid #e0e0e0;
    font-size: 0;
  }
  .add-line{
    padding: 20px;
    text-align: right;
    a{
      min-width: 98px;
    }
  }
  .table-box{
    padding: 0 20px;
    .blue-underline-text + .blue-underline-text{
      margin-left: 10px;
    }
  }
  .form-line {
    @include formLine(120px);
    margin-top: 20px;
    .form-field-switch{
      line-height: 32px;
    }
    .form-field{
      font-size: 0;
      .el-select{
        vertical-align: middle;
      }
      .normal-left{
        margin-left: 10px;
        vertical-align: middle;
      }
      .normal-top{
        margin-top: 10px;
        display: block;
      }
    }
    .form-table-box{
      width: 781px;
      padding: 10px;
      border: 1px solid #cccccc;
      border-radius: 5px;
      .el-select,.normal-left{
        width: 182px;
      }
      .icon-shanchu{
        color: #7b7b7b;
        cursor: pointer;
        font-size: 24px;
      }
      .add-text{
        display: inline-block;
        color: #1fa2f5;
        cursor: pointer;
        padding: 10px 0 0 20px;
      }
    }
  }
  .form-line-btn{
    margin-top: 40px;
    padding-bottom: 80px;
    .btn+ .btn{
      margin-left: 10px;
    }
  }
  .add-rule{
    cursor: pointer;
    color: #1fa2f5;
    vertical-align: middle;
    &.add-rule-left{
      margin-left: 3px;
    }
  }

  .review-info-item{
    margin: 10px auto;
  }
.page-tag{
    width: 600px;
    height: 40px;
    margin: 20px;
    background-color: #fff;
    display: flex;
    border-radius: 10px;
    border: 1px solid #1684fc;
    overflow: hidden;
    .tag-item{
        width: 300px;
        line-height: 40px;
        font-size: 18px;
        background-color: #fff;
        color: #1684fc;
        text-align: center;
    }
    .active-tag{
        background-color: #1684fc;
        color: #fff;
    }
}
.batch-review{
  display: flex;
  justify-content: flex-end;
  margin: 20px;
}
.page-tag1{
  width: 300px;
  
}
.time-tips{
    font-size: 15px;
    line-height: 35px;
}


.timeline {
    margin: 0;
    font-size: 18px;
    list-style: none;
    background-color: #fff;
    margin: 20px 0;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.item-tail {
    position: absolute;
    left: 8px;
    height: 100%;
    border-left: 3px solid #999;
}

.item-tail-active{
    border-left: 3px solid #2395FF;
}
.timeline-item {
    margin-left: 5px;
    .item-tail:last-child{
        display: none;
    }
}
/deep/ .el-collapse-item__header{
    font-size: 18px m !important;
}
.item-node {
    left: -2px;
    width: 24px;
    height: 24px;
    position: absolute;
    background-color: #fff;
    border: 6px solid #999;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

 
.item-node-active {
    border: 6px solid #2395FF;
}

.item-wrapper {
    position: relative;
    padding-left: 28px;
    top: -3px;
}

.item-content {
    color: #333;
    font-size: 18px;
}

.item-time {
    color: #999;
    line-height: 1;
    font-size: 18px;
}
.detail-item1{
    margin-right: 20px;
}

.item-process {
    margin-top: 8px;
    font-size: 18px;
    color: #999;

}

.item-result {
    font-size: 18px;
    border-radius: 10px;
    background-color: #F1F4F8;
    padding: 20px;
    margin-top: 20px;
}

.item-status {
    font-size: 18px;
    margin-top: 8px;
    padding: 5px 10px;
    border-radius: 4px;
    background-color: rgba(35, 149, 255, 0.1);
    border: 1px solid rgba(35, 149, 255, 0.5);
    color: #2395FF;
}

.item-status-pass {
    background-color: rgba(0, 211, 139, 0.1);
    border: 1px solid rgba(0, 211, 139, 0.5);
    color: #00D38B;
}

.item-status-fail {
    color: #FF3955;
    background-color: rgba(255, 57, 85, 0.1);
    border: 1px solid rgba(255, 57, 85, 0.5);
}

.item-status-wait {
    background-color: #F0F0F0;
    border: 1px solid rgba(198, 198, 198, 0.5);
}
.flex-row-b {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.flex-row-s {
    display: flex;
    align-items: center;
}