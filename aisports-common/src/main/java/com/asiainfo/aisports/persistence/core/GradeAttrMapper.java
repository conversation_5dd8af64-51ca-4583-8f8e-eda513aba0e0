package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.GradeAttr;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.Date;
import java.util.List;

public interface GradeAttrMapper {
    @Delete({
            "delete from grade_attr",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
            "insert into grade_attr (id, grade_id, ",
            "attr_code, attr_value, ",
            "start_date, end_date, ",
            "create_time, create_staff_id, ",
            "update_time, update_staff_id)",
            "values (#{id,jdbcType=BIGINT}, #{gradeId,jdbcType=VARCHAR}, ",
            "#{attrCode,jdbcType=VARCHAR}, #{attrValue,jdbcType=VARCHAR}, ",
            "#{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, ",
            "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
            "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(GradeAttr record);

    @Select({
            "select",
            "id, grade_id, attr_code, attr_value, start_date, end_date, create_time, create_staff_id, ",
            "update_time, update_staff_id",
            "from grade_attr",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="grade_id", property="gradeId", jdbcType=JdbcType.VARCHAR),
            @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    GradeAttr selectByPrimaryKey(Long id);

    @Update({
            "update grade_attr",
            "set grade_id = #{gradeId,jdbcType=VARCHAR},",
            "attr_code = #{attrCode,jdbcType=VARCHAR},",
            "attr_value = #{attrValue,jdbcType=VARCHAR},",
            "start_date = #{startDate,jdbcType=TIMESTAMP},",
            "end_date = #{endDate,jdbcType=TIMESTAMP},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(GradeAttr record);

    @Select({
            "<script>",
            "select",
            "id, grade_id, attr_code, attr_value, start_date, end_date, create_time, create_staff_id, update_time, update_staff_id",
            "from grade_attr",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"gradeId != null\">and grade_id = #{gradeId,jdbcType=VARCHAR}</if>",
            "<if test=\"attrCode != null\">and attr_code = #{attrCode,jdbcType=VARCHAR}</if>",
            "<if test=\"attrValue != null\">and attr_value = #{attrValue,jdbcType=VARCHAR}</if>",
            "<if test=\"startDate != null\">and start_date = #{startDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"endDate != null\">and end_date = #{endDate,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="grade_id", property="gradeId", jdbcType=JdbcType.VARCHAR),
            @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR),
            @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<GradeAttr> selectByFields(GradeAttr param);

    @Update({
            "<script>",
            "update grade_attr",
            "<set>",
            "<if test=\"gradeId != null\">grade_id = #{gradeId,jdbcType=VARCHAR},</if>",
            "<if test=\"attrCode != null\">attr_code = #{attrCode,jdbcType=VARCHAR},</if>",
            "<if test=\"attrValue != null\">attr_value = #{attrValue,jdbcType=VARCHAR},</if>",
            "<if test=\"startDate != null\">start_date = #{startDate,jdbcType=TIMESTAMP},</if>",
            "<if test=\"endDate != null\">end_date = #{endDate,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where id = #{id,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(GradeAttr record);


    @Select({
            "<script>",
            "select",
            "id, grade_id, attr_code, attr_value, start_date, end_date, create_time, create_staff_id, update_time, update_staff_id",
            "from grade_attr",
            "where",
            "grade_id = #{gradeId,jdbcType=BIGINT}",
            "<if test=\"attrCode != null\">and attr_code = #{attrCode,jdbcType=VARCHAR}</if>",
            " and #{currentTime,jdbcType=TIMESTAMP} between start_date and end_date ",
            "</script>"
    })
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "grade_id", property = "gradeId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "attr_code", property = "attrCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "attr_value", property = "attrValue", jdbcType = JdbcType.VARCHAR),
            @Result(column = "start_date", property = "startDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "end_date", property = "endDate", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<GradeAttr> selectByAttrCode(@Param("gradeId") String gradeId,
                                     @Param("attrCode") String attrCode,
                                     @Param("currentTime") Date currentTime);

    @Update({
            "<script>",
            "update grade_attr set update_time = now(),update_staff_id = #{staffId},end_date = #{endDate,jdbcType=TIMESTAMP}",
            "where grade_id = #{gradeId}",
            "and end_date &gt; #{endDate,jdbcType=TIMESTAMP}",
            "<if test=\"attrCode != null\">and attr_code = #{attrCode,jdbcType=VARCHAR}</if>",
            "</script>"

    })
    int invalidOldAttr(@Param("gradeId") String gradeId,
                       @Param("attrCode") String attrCode,
                       @Param("endDate") Date endDate,
                       @Param("staffId") Long staffId);

}
