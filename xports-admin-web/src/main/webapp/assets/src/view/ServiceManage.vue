<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/serviceManage' }">
        服务管理
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div class="query-area">
      <el-row :gutter="20">
        <xc-query-col>
          <el-input v-model="query.serviceName" placeholder="请输入名称" clearable="true"></el-input>
        </xc-query-col>
        <xc-query-col>
          <el-select
            placeholder="选择上级服务"
            v-model="query.parentServiceId"
            clearable="true"
            filterable="true"
            @change="getList">
            <el-option
              v-for="item in serviceList"
              :key="item.serviceId"
              :label="item.serviceName"
              :value="item.serviceId">
            </el-option>
          </el-select>
        </xc-query-col>
        <xc-query-col>
          <el-button type="primary" @click="getList">查询</el-button>
        </xc-query-col>
        <xc-query-right-col>
          <el-button  @click="showAddDialog">新增</el-button>
        </xc-query-right-col>
      </el-row>
    </div>
    <div class="result-area">
      <el-table
        :data="list"
        style="width: 100%">
        <el-table-column
          prop="serviceId"
          label="服务编码">
        </el-table-column>
        <el-table-column
          prop="serviceName"
          label="服务名称">
        </el-table-column>
        <el-table-column
          prop="parentServiceName"
          label="上级服务">
        </el-table-column>
        <el-table-column
          prop="bookTag"
          label="">
          <template slot-scope="scope">
            {{scope.row.bookTag === '1' ? '可以预订': '不可预订'}}
          </template>
        </el-table-column>
        <el-table-column
          prop="picUrl"
          label="图片">
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="更新时间">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作">
          <template slot-scope="scope">
            <el-button type="button" size="small" @click="showViewDialog(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-area clearfix">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="defaultPageSizes"
        :page-size="pageSize"
        :layout="defaultPageLayout"
        :total="total">
      </el-pagination>
    </div>
    <el-dialog
      title="支付方式"
      :visible.sync="dialogVisible">
      <el-form :model="form" ref="dialogForm" :rules="rules" :label-width="formLabelWidth" label-suffix=" : ">
        <el-form-item label="服务编码" prop="serviceId">
          <el-input v-model="form.serviceId" disabled></el-input>
        </el-form-item>
        <el-form-item label="服务名称" prop="serviceName">
          <el-input v-model="form.serviceName" placeholder="请输入服务名称"></el-input>
        </el-form-item>
        <el-form-item label="上级服务" prop="parentServiceId">
          <el-select
            placeholder="选择上级服务"
            v-model="form.parentServiceId">
            <el-option
              v-for="item in serviceList"
              :key="item.serviceId"
              :label="item.serviceName"
              :value="item.serviceId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否可预订" prop="bookTag">
          <el-select
            placeholder="是否可预订"
            v-model="form.bookTag">
            <el-option
              v-for="item in bookTagList"
              :key="item.key"
              :label="item.value"
              :value="item.key">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="图片" prop="picUrl">
          <el-input v-model="form.picUrl" placeholder="请输入图片样式"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="save(false)">保 存</el-button>
        <el-button type="primary" @click="save(true)" v-if="form.isNew">保存并继续</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import queryUtils from '../layout/queryUtils'
  import {service} from '../api'

  export default {
    data() {
      return {
        query: {
          serviceName: '',
          parentServiceId: ''
        },
        dialogVisible: false,
        formLabelWidth: '120px',
        form: {
          serviceId: '',
          serviceName: '',
          descripe: '',
          bookTag: '',
          picUrl: '',
          parentServiceId: '',
          isNew: false
        },
        bookTagList: [{'key': '1', value: '可以预订'}, {'key': '0', value: '不可预订'}],
        serviceList: [],
        rules: {
          serviceName: [
            {required: true, message: '请输入服务名称', trigger: 'blur'},
            {max: 25, message: '名称长度不能超过25位', trigger: 'blur'}
          ]
        }
      }
    },
    methods: {
      getArgs() {
        let promiseAll = Promise.all([
          service.findParentServices()
            .then(({data = {}}) => {
              this.serviceList = data.services
            })
        ])
        return promiseAll
      },
      getList() {
        service.list(this.query).then(this.handleList)
      },
      showAddDialog () {
        this.form.isNew = true
        this.dialogVisible = true
        this.form.serviceId = ''
        this.form.serviceName = ''
        this.form.descripe = ''
        this.form.bookTag = '0'
        this.form.picUrl = ''
        this.form.parentServiceId = ''
        this.$refs['dialogForm'].clearValidate()
      },
      showViewDialog (item) {
        this.form.isNew = false
        this.dialogVisible = true
        this.form.serviceId = item.serviceId
        this.form.serviceName = item.serviceName
        this.form.descripe = item.descripe
        this.form.bookTag = item.bookTag
        this.form.picUrl = item.picUrl
        this.form.parentServiceId = item.parentServiceId
        this.$refs['dialogForm'].clearValidate()
      },
      save(more) {
        this.$refs['dialogForm'].validate((valid) => {
          if (valid) {
            this.$refs['dialogForm'].clearValidate();
            return service.save(this.form).then(({data = {}}) => {
              this.$message({type:'success', message:'保存成功！'})
              if (!more) {
                this.dialogVisible = false;
              } else {
                this.form.serviceId = ''
                this.form.serviceName = ''
                this.form.descripe = ''
                this.form.bookTag = '0'
                this.form.picUrl = ''
                this.form.parentServiceId = ''
              }
              this.getList()
            })
          } else {
            return false;
          }
        })
      },
    },

    mixins: [queryUtils],
    mounted() {
      this.getArgs().then(this.getList)
    }
  };
</script>
