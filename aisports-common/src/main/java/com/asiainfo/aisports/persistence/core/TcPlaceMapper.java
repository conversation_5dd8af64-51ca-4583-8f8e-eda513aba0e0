package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.TcPlace;
import com.asiainfo.aisports.domain.core.TcPlaceInfo;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

public interface TcPlaceMapper {
    @Delete({
        "delete from tc_place",
        "where place_id = #{placeId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long placeId);

    @Insert({
        "insert into tc_place (place_id, place_name, ",
        "place_desc, state, venue_id, ",
        "center_id, contact_person, ",
        "contact_phone, latitude, ",
        "longitude, city_code, ",
        "district_code, address, ",
        "remark, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{placeId,jdbcType=BIGINT}, #{placeName,jdbcType=VARCHAR}, ",
        "#{placeDesc,jdbcType=VARCHAR}, #{state,jdbcType=CHAR}, #{venueId,jdbcType=BIGINT}, ",
        "#{centerId,jdbcType=BIGINT}, #{contactPerson,jdbcType=VARCHAR}, ",
        "#{contactPhone,jdbcType=VARCHAR}, #{latitude,jdbcType=VARCHAR}, ",
        "#{longitude,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, ",
        "#{districtCode,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, ",
        "#{remark,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(TcPlace record);

    @Select({
        "select",
        "place_id, place_name, place_desc, state, venue_id, center_id, contact_person, ",
        "contact_phone, latitude, longitude, city_code, district_code, address, remark, ",
        "create_time, create_staff_id, update_time, update_staff_id",
        "from tc_place",
        "where place_id = #{placeId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
        @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
        @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
        @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
        @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
        @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
        @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
        @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
        @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
        @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
        @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    TcPlace selectByPrimaryKey(Long placeId);

    @Update({
        "update tc_place",
        "set place_name = #{placeName,jdbcType=VARCHAR},",
        "place_desc = #{placeDesc,jdbcType=VARCHAR},",
        "state = #{state,jdbcType=CHAR},",
        "venue_id = #{venueId,jdbcType=BIGINT},",
        "center_id = #{centerId,jdbcType=BIGINT},",
        "contact_person = #{contactPerson,jdbcType=VARCHAR},",
        "contact_phone = #{contactPhone,jdbcType=VARCHAR},",
        "latitude = #{latitude,jdbcType=VARCHAR},",
        "longitude = #{longitude,jdbcType=VARCHAR},",
        "city_code = #{cityCode,jdbcType=VARCHAR},",
        "district_code = #{districtCode,jdbcType=VARCHAR},",
        "address = #{address,jdbcType=VARCHAR},",
        "remark = #{remark,jdbcType=VARCHAR},",
        "create_time = #{createTime,jdbcType=TIMESTAMP},",
        "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
        "update_time = #{updateTime,jdbcType=TIMESTAMP},",
        "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where place_id = #{placeId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(TcPlace record);

    @Select({
        "<script>",
        "select",
        "place_id, place_name, place_desc, state, venue_id, center_id, contact_person, contact_phone, latitude, longitude, city_code, district_code, address, remark, create_time, create_staff_id, update_time, update_staff_id",
        "from tc_place",
        "<where>",
        "<if test=\"placeId != null\">and place_id = #{placeId,jdbcType=BIGINT}</if>",
        "<if test=\"placeName != null\">and place_name = #{placeName,jdbcType=VARCHAR}</if>",
        "<if test=\"placeDesc != null\">and place_desc = #{placeDesc,jdbcType=VARCHAR}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"contactPerson != null\">and contact_person = #{contactPerson,jdbcType=VARCHAR}</if>",
        "<if test=\"contactPhone != null\">and contact_phone = #{contactPhone,jdbcType=VARCHAR}</if>",
        "<if test=\"latitude != null\">and latitude = #{latitude,jdbcType=VARCHAR}</if>",
        "<if test=\"longitude != null\">and longitude = #{longitude,jdbcType=VARCHAR}</if>",
        "<if test=\"cityCode != null\">and city_code = #{cityCode,jdbcType=VARCHAR}</if>",
        "<if test=\"districtCode != null\">and district_code = #{districtCode,jdbcType=VARCHAR}</if>",
        "<if test=\"address != null\">and address = #{address,jdbcType=VARCHAR}</if>",
        "<if test=\"remark != null\">and remark = #{remark,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
        @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
        @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
        @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
        @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
        @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
        @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
        @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
        @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
        @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
        @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<TcPlace> selectByFields(TcPlace param);

    @Update({
        "<script>",
        "update tc_place",
        "<set>",
        "<if test=\"placeName != null\">place_name = #{placeName,jdbcType=VARCHAR},</if>",
        "<if test=\"placeDesc != null\">place_desc = #{placeDesc,jdbcType=VARCHAR},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"venueId != null\">venue_id = #{venueId,jdbcType=BIGINT},</if>",
        "<if test=\"centerId != null\">center_id = #{centerId,jdbcType=BIGINT},</if>",
        "<if test=\"contactPerson != null\">contact_person = #{contactPerson,jdbcType=VARCHAR},</if>",
        "<if test=\"contactPhone != null\">contact_phone = #{contactPhone,jdbcType=VARCHAR},</if>",
        "<if test=\"latitude != null\">latitude = #{latitude,jdbcType=VARCHAR},</if>",
        "<if test=\"longitude != null\">longitude = #{longitude,jdbcType=VARCHAR},</if>",
        "<if test=\"cityCode != null\">city_code = #{cityCode,jdbcType=VARCHAR},</if>",
        "<if test=\"districtCode != null\">district_code = #{districtCode,jdbcType=VARCHAR},</if>",
        "<if test=\"address != null\">address = #{address,jdbcType=VARCHAR},</if>",
        "<if test=\"remark != null\">remark = #{remark,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where place_id = #{placeId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(TcPlace record);

    @Select({
        "<script>",
        "select",
        "place_id, place_name, place_desc, state, tp.venue_id, " ,
            "(select venue_name from venue v where tp.venue_id=v.venue_id) venue_name," ,
            "center_id, contact_person, contact_phone, latitude, longitude, city_code, district_code, address, remark, create_time, create_staff_id, update_time, update_staff_id",
        "from tc_place tp",
        "<where>",
        "state = #{state,jdbcType=CHAR}",
        "and tp.center_id = #{centerId,jdbcType=BIGINT}",
        "<if test=\"venueId != null\">and tp.venue_id = #{venueId,jdbcType=BIGINT}</if>",
         "<if test=\"venueIdList != null and venueIdList.size &gt;0\">and tp.venue_id in ",
            "<foreach collection=\"venueIdList\" item=\"venueId\" index=\"index\" separator=\",\" open = \"(\" end = \")\">",
                "#{venueId,jdbcType=BIGINT}",
            "</foreach>)</if>",
        "<if test=\"placeName != null\">and place_name like  CONCAT('%', #{placeName},'%') </if> ",
        "</where>",
        "ORDER BY update_time desc",
        "</script>"
    })
    @Results({
        @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
        @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
        @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
        @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
        @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
        @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
        @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
        @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
        @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
        @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
        @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<TcPlaceInfo> selectPlacesByName(ParamMap paramMap, RowBounds rowBounds);

    @Select({
        "<script>",
        "select",
        "place_id, place_name, place_desc, state, tp.venue_id, " +
            "center_id, contact_person, contact_phone, latitude, longitude, city_code, district_code, address, remark, create_time, create_staff_id, update_time, update_staff_id, ",
        "(select venue_name from venue v where tp.venue_id=v.venue_id) venue_name," +
            "(select a.parent_code from venue_center.area a where a.area_code=tp.city_code and a.area_level=" + Constants.AreaLevel.CITY + ") province_code " +
            "from tc_place tp",
        "where place_id = #{placeId,jdbcType=BIGINT}",
        "</script>"
    })
    @Results({
        @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
        @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
        @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
        @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
        @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
        @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
        @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
        @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "province_code", property = "provinceCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
        @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
        @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
        @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    TcPlaceInfo selectPlaceDetails(Long placeId);

    /**
     * 查询某课程配置的选择上课地点
     *
     * @param courseId
     * @return
     */
    @Select({
        "select b.place_id, b.place_name, b.place_desc, b.state, b.venue_id, b.center_id, b.contact_person, b.contact_phone, b.latitude, b.longitude, b.city_code, b.district_code, b.address, b.remark, b.create_time, b.create_staff_id, b.update_time, b.update_staff_id",
        "from training_course_attr a,tc_place b,training_course_attr c",
        "where a.course_id = #{courseId,jdbcType=BIGINT}  and a.course_id = c.course_id and  c.attr_code = 'choose_class_place' and c.attr_value = '1'  ",
        "and a.attr_code = 'class_place' and now() between a.start_date and a.end_date  and  find_in_set(b.place_id,a.attr_value) and b.state = '1'"
    })
    @Results({
        @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
        @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
        @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
        @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
        @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
        @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
        @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
        @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
        @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
        @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
        @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<TcPlace> queryCoursePlace(@Param("courseId") Long courseId);

    @Select({
        "<script>",
        "select",
        "place_id, place_name, place_desc, state, venue_id, center_id, contact_person, ",
        "contact_phone, latitude, longitude, city_code, district_code, address, remark, ",
        "create_time, create_staff_id, update_time, update_staff_id",
        "from tc_place",
        "where state = 1",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"venueId != null\">and venue_id = #{venueId,jdbcType=BIGINT}</if>",
        "</script>"
    })
    @Results({
        @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
        @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
        @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
        @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
        @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
        @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
        @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
        @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
        @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
        @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
        @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
        @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
        @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
        @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<TcPlace> selectValidByVenueId(@Param("centerId") Long centerId, @Param("venueId") Long venueId);

    @Select({
        "select",
        "place_id, place_name, place_desc, address, remark",
        "from tc_place",
        "where find_in_set(place_id, #{placeIds,jdbcType=VARCHAR}) and state = '1'",
    })
    @Results({
        @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
        @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
        @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
        @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
        @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR)
    })
    List<Map<String, Object>> selectPlaceList(@Param("placeIds") String placeIds);

    @Select({
            "<script>",
            "select",
            "a.place_id, a.place_name, a.place_desc, a.state, a.venue_id, a.center_id, a.contact_person, a.contact_phone,",
            "a.latitude, a.longitude, a.city_code, a.district_code, a.address, a.remark, a.create_time, a.create_staff_id,",
            "a.update_time, a.update_staff_id, b.venue_name, c.parent_code province_code, c.area_name city_name,",
            "(select d.area_name from area d where a.district_code=d.area_code) district_name",
            "from tc_place a left join area c on a.city_code = c.area_code, venue b",
            "<where>",
            "a.venue_id = b.venue_id",
            "<if test=\"placeId != null\">and a.place_id = #{placeId,jdbcType=BIGINT}</if>",
            "<if test=\"placeName != null\">and a.place_name = #{placeName,jdbcType=VARCHAR}</if>",
            "<if test=\"placeDesc != null\">and a.place_desc = #{placeDesc,jdbcType=VARCHAR}</if>",
            "<if test=\"state != null\">and a.state = #{state,jdbcType=CHAR}</if>",
            "<if test=\"venueId != null\">and a.venue_id = #{venueId,jdbcType=BIGINT}</if>",
            "<if test=\"companyId != null\">and b.company_id = #{companyId,jdbcType=BIGINT}</if>",
            "<if test=\"centerId != null\">and a.center_id = #{centerId,jdbcType=BIGINT}</if>",
            "<if test=\"contactPerson != null\">and a.contact_person = #{contactPerson,jdbcType=VARCHAR}</if>",
            "<if test=\"contactPhone != null\">and a.contact_phone = #{contactPhone,jdbcType=VARCHAR}</if>",
            "<if test=\"latitude != null\">and a.latitude = #{latitude,jdbcType=VARCHAR}</if>",
            "<if test=\"longitude != null\">and a.longitude = #{longitude,jdbcType=VARCHAR}</if>",
            "<if test=\"cityCode != null\">and a.city_code = #{cityCode,jdbcType=VARCHAR}</if>",
            "<if test=\"districtCode != null\">and a.district_code = #{districtCode,jdbcType=VARCHAR}</if>",
            "<if test=\"address != null\">and a.address = #{address,jdbcType=VARCHAR}</if>",
            "<if test=\"remark != null\">and a.remark = #{remark,jdbcType=VARCHAR}</if>",
            "</where>",
            "order by a.venue_id, a.place_id",
            "</script>"
    })
    @Results({
            @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
            @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
            @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
            @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "city_name", property = "cityName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "province_code", property = "provinceCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "district_name", property = "districtName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "venue_name", property = "venueName", jdbcType = JdbcType.VARCHAR)
    })
    List<Map<String, Object>> selectPlacesOrderByVenue(Map<String, Object> param, RowBounds rowBounds);


    @Select({
            "select",
            "tp.place_id, ifnull(tpl.place_name,tp.place_name) place_name, ifnull(tpl.place_desc,tp.place_desc) place_desc, ifnull(tpl.address,tp.address) address, ifnull(tpl.remark,tp.remark) remark ",
            "from tc_place tp",
            "left join tc_place_lang tpl on tp.place_id =tpl.place_id and tpl.language = #{lang,jdbcType=VARCHAR} ",
            "where find_in_set(tp.place_id, #{placeIds,jdbcType=VARCHAR}) and tp.state = '1'",
    })
    @Results({
            @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
            @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR)
    })
    List<Map<String, Object>> selectPlaceListLang(@Param("placeIds") String placeIds, @Param("lang") String lang);


    @Select({
            "<script>",
            "select tp.*,tl.lesson_id" ,
            "from tc_lesson tl,tc_place tp" ,
            "where tl.place_id = tp.place_id" ,
            "and tl.lesson_id in ",
            "<foreach item=\"lessonId\" collection=\"lessonIds\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
            "#{lessonId,jdbcType=BIGINT}",
            "</foreach>",
            "</script>"
    })
    @Results({
            @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "place_id", property = "placeId", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "place_name", property = "placeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "place_desc", property = "placeDesc", jdbcType = JdbcType.VARCHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "venue_id", property = "venueId", jdbcType = JdbcType.BIGINT),
            @Result(column = "center_id", property = "centerId", jdbcType = JdbcType.BIGINT),
            @Result(column = "contact_person", property = "contactPerson", jdbcType = JdbcType.VARCHAR),
            @Result(column = "contact_phone", property = "contactPhone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "latitude", property = "latitude", jdbcType = JdbcType.VARCHAR),
            @Result(column = "longitude", property = "longitude", jdbcType = JdbcType.VARCHAR),
            @Result(column = "city_code", property = "cityCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "district_code", property = "districtCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "address", property = "address", jdbcType = JdbcType.VARCHAR),
            @Result(column = "remark", property = "remark", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "lesson_id", property = "lessonId", jdbcType = JdbcType.BIGINT),
    })
    List<DataMap> selectByLessonIds(@Param("lessonIds") List<Long> lessonIds);

}
