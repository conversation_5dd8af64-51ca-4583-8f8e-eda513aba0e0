package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class CourseEnroll {
    private Long enrollId;

    private Long studentId;

    private Long courseId;

    private Long termId;

    private Long lessonId;

    private Long placeId;

    private Long instId;

    private String state;

    private String classState;

    private String evaluation;

    private Date createTime;

    private Date updateTime;

    private Long updateStaffId;

    private String remark;

    private Long lessonNum;

    private Long freeLessonNum;

    private Integer remainNum;

    private Long groupEnrollId;

    private Long createTradeId;

    private Date startDate;

    private Date endDate;

    private Long cancelTradeId;

    private String remindTag;

    private Date lastRemindTime;

    private Long renewedEnrollId;

    private Long coachId;

    private String freeTag;

    private String activeTag;

    private Date activeTime;

    public Long getEnrollId() {
        return enrollId;
    }

    public void setEnrollId(Long enrollId) {
        this.enrollId = enrollId;
    }

    public Long getStudentId() {
        return studentId;
    }

    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }

    public Long getCourseId() {
        return courseId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getTermId() {
        return termId;
    }

    public void setTermId(Long termId) {
        this.termId = termId;
    }

    public Long getLessonId() {
        return lessonId;
    }

    public void setLessonId(Long lessonId) {
        this.lessonId = lessonId;
    }

    public Long getPlaceId() {
        return placeId;
    }

    public void setPlaceId(Long placeId) {
        this.placeId = placeId;
    }

    public Long getInstId() {
        return instId;
    }

    public void setInstId(Long instId) {
        this.instId = instId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getClassState() {
        return classState;
    }

    public void setClassState(String classState) {
        this.classState = classState == null ? null : classState.trim();
    }

    public String getEvaluation() {
        return evaluation;
    }

    public void setEvaluation(String evaluation) {
        this.evaluation = evaluation == null ? null : evaluation.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Long getLessonNum() {
        return lessonNum;
    }

    public void setLessonNum(Long lessonNum) {
        this.lessonNum = lessonNum;
    }

    public Long getFreeLessonNum() {
        return freeLessonNum;
    }

    public void setFreeLessonNum(Long freeLessonNum) {
        this.freeLessonNum = freeLessonNum;
    }

    public Integer getRemainNum() {
        return remainNum;
    }

    public void setRemainNum(Integer remainNum) {
        this.remainNum = remainNum;
    }

    public Long getGroupEnrollId() {
        return groupEnrollId;
    }

    public void setGroupEnrollId(Long groupEnrollId) {
        this.groupEnrollId = groupEnrollId;
    }

    public Long getCreateTradeId() {
        return createTradeId;
    }

    public void setCreateTradeId(Long createTradeId) {
        this.createTradeId = createTradeId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getCancelTradeId() {
        return cancelTradeId;
    }

    public void setCancelTradeId(Long cancelTradeId) {
        this.cancelTradeId = cancelTradeId;
    }

    public String getRemindTag() {
        return remindTag;
    }

    public void setRemindTag(String remindTag) {
        this.remindTag = remindTag == null ? null : remindTag.trim();
    }

    public Date getLastRemindTime() {
        return lastRemindTime;
    }

    public void setLastRemindTime(Date lastRemindTime) {
        this.lastRemindTime = lastRemindTime;
    }

    public Long getRenewedEnrollId() {
        return renewedEnrollId;
    }

    public void setRenewedEnrollId(Long renewedEnrollId) {
        this.renewedEnrollId = renewedEnrollId;
    }

    public Long getCoachId() {
        return coachId;
    }

    public void setCoachId(Long coachId) {
        this.coachId = coachId;
    }

    public String getFreeTag() {
        return freeTag;
    }

    public void setFreeTag(String freeTag) {
        this.freeTag = freeTag == null ? null : freeTag.trim();
    }

    public String getActiveTag() {
        return activeTag;
    }

    public void setActiveTag(String activeTag) {
        this.activeTag = activeTag == null ? null : activeTag.trim();
    }

    public Date getActiveTime() {
        return activeTime;
    }

    public void setActiveTime(Date activeTime) {
        this.activeTime = activeTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", enrollId=").append(enrollId);
        sb.append(", studentId=").append(studentId);
        sb.append(", courseId=").append(courseId);
        sb.append(", termId=").append(termId);
        sb.append(", lessonId=").append(lessonId);
        sb.append(", placeId=").append(placeId);
        sb.append(", instId=").append(instId);
        sb.append(", state=").append(state);
        sb.append(", classState=").append(classState);
        sb.append(", evaluation=").append(evaluation);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append(", remark=").append(remark);
        sb.append(", lessonNum=").append(lessonNum);
        sb.append(", freeLessonNum=").append(freeLessonNum);
        sb.append(", remainNum=").append(remainNum);
        sb.append(", groupEnrollId=").append(groupEnrollId);
        sb.append(", createTradeId=").append(createTradeId);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", cancelTradeId=").append(cancelTradeId);
        sb.append(", remindTag=").append(remindTag);
        sb.append(", lastRemindTime=").append(lastRemindTime);
        sb.append(", renewedEnrollId=").append(renewedEnrollId);
        sb.append(", coachId=").append(coachId);
        sb.append(", freeTag=").append(freeTag);
        sb.append(", activeTag=").append(activeTag);
        sb.append(", activeTime=").append(activeTime);
        sb.append("]");
        return sb.toString();
    }
}