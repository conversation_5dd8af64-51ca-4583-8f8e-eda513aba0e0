package com.asiainfo.aisports.scheduler.service;

import com.asiainfo.aisports.annotation.Datasource;
import com.asiainfo.aisports.annotation.RoutingKey;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.scheduler.service.cache.TuPuAccessTokenCacheService;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.tools.StringUtils;
import com.asiainfo.aisports.tools.TuPuCameraApiClient;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR> bizhicheng
 * @description : 图谱客流对接请求service
 * @date : 2021/09/17
 */
@Service
public class TuPuCameraService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TuPuCameraService.class);

    @Autowired
    private TuPuCameraApiClient tuPuCameraApiClient;
    @Autowired
    private TuPuAccessTokenCacheService tuPuAccessTokenCacheService;
    @Autowired
    private PassengerFlowHourMapper passengerFlowHourMapper;
    @Autowired
    private PassengerFlowTypeAnalysisMapper passengerFlowTypeAnalysisMapper;
    @Autowired
    private THourCameraMapper tHourCameraMapper;
    @Autowired
    private PassengerFlowMinuteMapper passengerFlowMinuteMapper;

    private static final String ROOT_URL = "https://api.bi.tuputech.com";
    private static final String AUTH_URL = "/v1/auth/token";
    private static final String FLOW_CHART_URL = "/v2/flow/chart";
    // 图普token设置的过期时间,缓存的token会先过期
    private static final Long TU_PU_TOKEN_EXPIRE_TIME = 1800L;

    /**
     * 获取图谱平台的授权
     *
     * @param centerId
     * @return
     */
    @Transactional
    public ServiceResult authToken(String key, String secret, Long centerId, Long venueId) {
        ParamMap params = new ParamMap();
        params.put("secret", secret);
        params.put("expiresIn", TU_PU_TOKEN_EXPIRE_TIME);
        ServiceResult authResult = tuPuCameraApiClient.post(ROOT_URL + AUTH_URL + "/" + key, params);
        String accessToken = MapUtils.getString(authResult, "token", null);
        if (StringUtils.isEmpty(accessToken)) {
            authResult.setError(1);
            authResult.setMessage("获取accessToken失败");
            return authResult;
        }
        tuPuAccessTokenCacheService.refreshToken(venueId, accessToken);
        return authResult.set("accessToken", accessToken);
    }

    /**
     * 根据中心获取AccessToken
     *
     * @param centerId
     * @return
     */
    private String getAccessToken(String key, String secret, Long centerId, Long venueId) {
        String authToken = tuPuAccessTokenCacheService.getToken(venueId);
        if (StringUtils.isEmpty(authToken)) {
            ServiceResult authTokenResult = this.authToken(key, secret, centerId, venueId);
            if (authTokenResult.getError() == 0) {
                authToken = authTokenResult.getString("accessToken");
            }
        }
        return authToken;
    }

    /**
     * 客流报表
     *
     * @param object
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional
    public ServiceResult queryFlowChartData(JSONObject object,Long centerId, Long venueId, String startTime, String endTime, ParamMap otherMap) {
        String UID = object.getString("uid");
        String SID = object.getString("sid");
        //String inZoneId = object.getString("inZoneId");
        //String outZoneId = object.getString("outZoneId");
        String zoneIds = object.getString("zoneIds");

        ParamMap childrenMap = new ParamMap();
        childrenMap.put("_id", "zoneId");
        childrenMap.put("values", new ArrayList(Arrays.asList(zoneIds.split(","))));
        ParamMap childrenMap2 = new ParamMap();
        childrenMap2.put("_id", "movement");
        childrenMap2.put("values", new String[]{"in","out"});
        List<Map> list = new ArrayList<>();
        list.add(childrenMap);
        list.add(childrenMap2);
        if (!otherMap.isEmpty()) {
            list.add(otherMap);
        }
        ParamMap bodyMap = new ParamMap();
        bodyMap.put("chartType", "line");
        bodyMap.put("interval", "1d");
        bodyMap.put("countingBy", "pedestrian");
        bodyMap.put("start", startTime);
        bodyMap.put("end", endTime);
        bodyMap.put("aggregations", list);

        ServiceResult serviceResult = tuPuCameraApiClient.post(ROOT_URL + FLOW_CHART_URL + "?UID=" + UID + "&SID=" + SID, bodyMap, getAccessToken(object.getString("tuPuAppKey"), object.getString("tuPuAppSecret"), centerId, venueId));
        if ("200".equals(serviceResult.getString("code"))) {
            serviceResult.setError(0);
        }
        return serviceResult;
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Map counterInfoList(@RoutingKey Long centerId, Long venueId, Date startDate, Date endDate) {
        return passengerFlowHourMapper.queryLastInfoByVenueId(venueId, startDate, endDate);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int savePassengerFlowHour(@RoutingKey Long centerId, PassengerFlowHour param) {
        return passengerFlowHourMapper.insert(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int savePassengerFlowMinute(@RoutingKey Long centerId, PassengerFlowMinute param) {
        return passengerFlowMinuteMapper.insert(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<PassengerFlowHour> queryPassengerFlowHour(@RoutingKey Long centerId, PassengerFlowHour param) {
        return passengerFlowHourMapper.selectDailyList(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<DataMap> selectByParamGroupByEquipment(@RoutingKey Long centerId, ParamMap paramMap) {
        return passengerFlowHourMapper.selectByParamGroupByEquipment(paramMap);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<PassengerFlowHour> queryMonthList(@RoutingKey Long centerId, PassengerFlowHour param) {
        return passengerFlowHourMapper.selectMonthList(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int savePassengerFlowAnalysis(@RoutingKey Long centerId, List<PassengerFlowTypeAnalysis> list) {
        return passengerFlowTypeAnalysisMapper.batchInsert(list);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public int saveTHourCameraInfo(@RoutingKey Long centerId, @Datasource String dataSource, THourCamera param) {
        return tHourCameraMapper.insert(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<THourCamera> queryTHourCameraInfo(@RoutingKey Long centerId, @Datasource String dataSource, THourCamera param) {
        return tHourCameraMapper.selectDayCameraList(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<THourCamera> queryMonthCameraList(@RoutingKey Long centerId, @Datasource String dataSource, THourCamera param) {
        return tHourCameraMapper.selectMonthCameraList(param);
    }

    @TargetDataSource(name = "{}")
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<THourCamera> queryByRoutingKey(@RoutingKey Long centerId, @Datasource String dataSource, ParamMap param) {
        return tHourCameraMapper.selectHourCameraList(param);
    }
}
