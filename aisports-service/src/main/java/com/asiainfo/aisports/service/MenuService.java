package com.asiainfo.aisports.service;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.Menu;
import com.asiainfo.aisports.persistence.core.FavoriteMenuMapper;
import com.asiainfo.aisports.persistence.core.MenuMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by micha<PERSON> on 15/4/9.
 */
@Service
public class MenuService {
    @Autowired
    FavoriteMenuMapper favoriteMenuMapper;
    @Autowired
    MenuMapper menuMapper;
    @Autowired
    SequenceWrapper sequenceWrapper;

    /**
     * 根据员工Id获得收藏的菜单
     *
     * @param staffId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Menu> findFavoriteByStaffId(Long staffId) {
        return favoriteMenuMapper.selectByOwnerId(new String[]{staffId.toString()}, Constants.Owner.STAFF);
    }

    @Transactional(readOnly = true)
    public Menu findById(Long menuId) {
        return menuMapper.selectByPrimaryKey(menuId);
    }
}
