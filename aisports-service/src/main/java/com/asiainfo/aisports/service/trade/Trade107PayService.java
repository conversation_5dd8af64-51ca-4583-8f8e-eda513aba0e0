package com.asiainfo.aisports.service.trade;

import com.asiainfo.aisports.domain.core.Staff;
import com.asiainfo.aisports.domain.core.Trade;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.service.ServiceResult;
import com.asiainfo.aisports.service.SkatingCourseService;
import com.yunyu.park.payment.enums.ConsumeTypeEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by wangdd on 2017/3/10.
 * 次课教练预约支付功能
 */
@Service
public class Trade107PayService extends TicketTradePayService{

    private static final String URL = "/skatingCourse/appointInit";

    @Autowired
    SkatingCourseService skatingCourseService;

    @Transactional
    @Override
    public TradePayResult finishTrade(Trade trade) {
        skatingCourseService.finishAppoint(trade);
        super.countPoints(trade);
        // 平阳场馆同步订单
        super.pingYangSyncFinishedOrder(trade, ConsumeTypeEnums.NormalConsume.getValue());
        return new TradePayResult().set("redirect", getUrl(trade));
    }

    @Override
    public TradePayResult cancelPay(Trade trade, Staff staff) {
        TradePayResult tradePayResult = new TradePayResult();
        ServiceResult serviceResult = skatingCourseService.cancelTrade(trade, (LoginStaff)staff);
        tradePayResult.putAll(serviceResult);
        tradePayResult.set("redirect", URL);
        return tradePayResult;
    }



    @Override
    public String getUrl(Trade trade) {
        return URL;
    }

}
