/**
 * 多层菜单
 * 带有搜索功能
 */
.multi-menu{
  min-width:300px;
  border: 1px solid #e8e8e8;
  border-radius:5px;
  overflow: hidden;
  .top-layer.no-cdr{
    &>dt:after{
      border: 0!important;
    }
  }
  .menu-search-con{
    padding: 6px 10px;
    background-color: #fafafa;
    border-bottom: 1px solid #e8e8e8;
    input{
      width: 280px;
    }
  }
  dl{
    dd{
      display: none;
    }
    &.active{
      &>dd{
        display: block;
      }
    }

  }
  .menu-con{
    max-height: 494px;
    overflow-y: auto;
    dt{
      height: 40px;
      line-height: 40px;
      overflow: hidden;
      position: relative;
      cursor: pointer;
    }
    dl[data-index = '0']{
      //border-bottom:1px solid #e8e8e8;
      &>dt{
        padding : 0px 10px;
        background-color: #fafafa;
        font-weight: bold;
        border-bottom:1px solid #e8e8e8;
        &:after{
          display: block;
          content: ' ';
          position: absolute;
          right: 13px;
          top : 50%;
          margin-top: -4px;
          @include triangle(right , 8px,4.5px,#ccc);
        }
      }
      &>dd{
        border-bottom:1px solid #e8e8e8;
        overflow: hidden;
      }
      &.active{
        &>dt{
          &:before{
            content : '';
            position: absolute;
            top : 0px;
            left : 0px;
            bottom : 0px;
            width: 3px;
            background-color: #1fa2f5;
          }
          &:after{
            transform: rotate3d(0,0,1,90deg);
          }
        }
      }
    }
    .sub{
      dt{
        background: #fff;
        padding: 0px 10px 0px 17px;
        font-weight: normal;
        //border-bottom : 1px dashed #f0f0f8;
        &:before{
          display: inline-block;
          content: ' ';
          @include triangle(right , 6px,3.5px,#ccc);
          margin-right: 10px;
          margin-bottom: 2px;
        }
      }
      dd{
        border-top : 1px dashed #f0f0f8;
      }
      &.active{
        &>dt{
          background: #fafafa;
          &:before{
            transform: rotate3d(0,0,1,90deg);
            border-left-color : #1fa2f5;
          }
        }
      }
    }
    .sub + .sub{
      border-top : 1px dashed #f0f0f8;
    }
    /*.sub:last-child{
      &>dt{
        border-bottom : none;
      }
      &>dd{
        border-bottom : none;
      }
    }*/
    .leaf-layer{
      dt{
        background-color: #fff;
      }
      dt:before{
        visibility: hidden;
      }
      dd{
        display: none;
      }
      &.active{
        dt{
          background: #eef7fe;
          color : #1fa2f5;
        }
      }
    }
  }
}