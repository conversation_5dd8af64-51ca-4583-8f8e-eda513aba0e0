import $ from 'jquery'
import intercept from 'utils/intercept'
import { util } from 'aisportsConfig'

// 获取场馆列表
export function queryStaffResourceVenueList(data) {
    return $.get('/commonParam/queryStaffResourceVenueList', data).then(intercept)
}

// 获取服务列表
export function queryServiceListByVenueId(venueId) {
    return $.get('/commonParam/queryServiceListByVenueId', { venueId }).then(intercept)
}

// 获取课程列表
export function queryCourseList(data) {
    return $.get('/divideClassNew/queryCourseList', data).then(intercept)
}

// 获取待分班学员列表
export function queryStudentListForDivide(data) {
    return $.get('/divideClassNew/queryStudentListForDivide', data).then(intercept)
}

// 获取期次列表
export function queryTermList(data) {
    return $.get('/divideClassNew/queryTermList', data).then(intercept)
}

// 获取学员详情
export function queryStudentDetail(enrollId) {
    return $.get('/divideClassNew/queryStudentDetail', {
        enrollId,
    }).then(intercept)
}

// 获取可以选择的班级列表
export function queryClassList(termId) {
    return $.get('/divideClassNew/queryClassList', {
        termId,
    }).then(intercept)
}

// 获取分班班级列表
export function queryDividingClassList(data) {
    return $.get('/divideClassNew/queryDividingClassList', data).then(intercept)
}

// 获取班级学员列表
export function queryClassStudentList(data) {
    return $.get('/divideClassNew/queryClassStudentList', data).then(intercept)
}

// 导出班级学员列表
export function exportClassStudentList(classId) {
    util.simulationForm('/divideClassNew/exportClassStudentList', { classId })
}

// 提交分班
export function submitDividing(data) {
    return $.post('/divideClassNew/submitDividing', data).then(intercept)
}

// 换班
export function submitDividingForDivided(data) {
    return $.post('/divideClassNew/submitDividingForDivided', data).then(intercept)
}

export function settleClass(data) {
    return $.post('/divideClassNew/settleClass', data).then(intercept)
}

export function queryEnrollDetailList(data) {
    return $.get('/divideClassNew/queryEnrollDetailList', data).then(intercept)
}

export function removeStudent(data) {
    return $.post('/divideClassNew/removeStudent', data).then(intercept)
}

export function batchRemoveStudent(data) {
    return $.post('/divideClassNew/batchRemoveStudent', data).then(intercept)
}
