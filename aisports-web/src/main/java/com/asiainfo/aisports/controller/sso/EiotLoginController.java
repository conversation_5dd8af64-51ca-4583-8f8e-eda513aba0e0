package com.asiainfo.aisports.controller.sso;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.SSOClient;
import com.asiainfo.aisports.domain.core.StaffRoleKey;
import com.asiainfo.aisports.helper.Errors;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.StaffRoleInfo;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.service.MenuQueryService;
import com.asiainfo.aisports.service.RoleMenuFunctionService;
import com.asiainfo.aisports.service.StaffRoleService;
import com.asiainfo.aisports.service.sso.SSOClientService;
import com.asiainfo.aisports.service.sso.SSOLoginService;
import com.asiainfo.aisports.web.WebResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by liuchangyuan on 2018/12/19.
 */
@Controller
@RequestMapping("/eiot")
public class EiotLoginController {

    @Autowired
    private VenueParamConfig venueParamConfig;

    @RequestMapping("")
    public String init() {
        return "eiot/eiot";
    }

    /**
     * 获取授权Token
     *
     * @return
     */
    @RequestMapping(value = "/getSearchEnergyUrl", method = RequestMethod.GET)
    @ResponseBody
    public WebResult getSearchEnergyUrl() {
        String url = venueParamConfig.getParam(0L, Constants.VenueParam.EIOT_HTML_URL, "http://ip");
        String cid = venueParamConfig.getParam(0L, Constants.VenueParam.EIOT_CID, "test123456");
        long timestamp = System.currentTimeMillis();
        String co = cid+timestamp;
        String md5 = DigestUtils.md5DigestAsHex(co.getBytes(StandardCharsets.UTF_8));
        if(md5.length()>15){
            md5 = md5.substring(0,15).toUpperCase();
        }
        StringBuffer param = new StringBuffer("?cid=");
        param.append(cid).append("&ms=").append(timestamp).append("&ha=").append(md5);
        return new WebResult().set("param", param.toString()).set("url",url+param.toString());
    }



}
