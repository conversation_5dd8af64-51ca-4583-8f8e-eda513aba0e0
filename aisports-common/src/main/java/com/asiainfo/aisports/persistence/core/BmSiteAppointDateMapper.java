package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.BmSiteAppointDate;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface BmSiteAppointDateMapper {
    @Delete({
            "delete from bm_site_appoint_date",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
            "insert into bm_site_appoint_date (id, bm_site_id, ",
            "start_date, end_date, weekday, ",
            "skip_holiday, state, create_time, ",
            "create_staff_id, update_time, ",
            "update_staff_id)",
            "values (#{id,jdbcType=BIGINT}, #{bmSiteId,jdbcType=BIGINT}, ",
            "#{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{weekday,jdbcType=VARCHAR}, ",
            "#{skipHoliday,jdbcType=CHAR}, #{state,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
            "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
            "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(BmSiteAppointDate record);

    @Select({
            "select",
            "id, bm_site_id, start_date, end_date, weekday, skip_holiday, state, create_time, ",
            "create_staff_id, update_time, update_staff_id",
            "from bm_site_appoint_date",
            "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "bm_site_id", property = "bmSiteId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_date", property = "startDate", jdbcType = JdbcType.DATE),
            @Result(column = "end_date", property = "endDate", jdbcType = JdbcType.DATE),
            @Result(column = "weekday", property = "weekday", jdbcType = JdbcType.VARCHAR),
            @Result(column = "skip_holiday", property = "skipHoliday", jdbcType = JdbcType.CHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    BmSiteAppointDate selectByPrimaryKey(Long id);

    @Update({
            "update bm_site_appoint_date",
            "set bm_site_id = #{bmSiteId,jdbcType=BIGINT},",
            "start_date = #{startDate,jdbcType=DATE},",
            "end_date = #{endDate,jdbcType=DATE},",
            "weekday = #{weekday,jdbcType=VARCHAR},",
            "skip_holiday = #{skipHoliday,jdbcType=CHAR},",
            "state = #{state,jdbcType=CHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(BmSiteAppointDate record);

    @Select({
            "<script>",
            "select",
            "id, bm_site_id, start_date, end_date, weekday, skip_holiday, state, create_time, create_staff_id, update_time, update_staff_id",
            "from bm_site_appoint_date",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=BIGINT}</if>",
            "<if test=\"bmSiteId != null\">and bm_site_id = #{bmSiteId,jdbcType=BIGINT}</if>",
            "<if test=\"startDate != null\">and start_date = #{startDate,jdbcType=DATE}</if>",
            "<if test=\"endDate != null\">and end_date = #{endDate,jdbcType=DATE}</if>",
            "<if test=\"weekday != null\">and weekday = #{weekday,jdbcType=VARCHAR}</if>",
            "<if test=\"skipHoliday != null\">and skip_holiday = #{skipHoliday,jdbcType=CHAR}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "bm_site_id", property = "bmSiteId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_date", property = "startDate", jdbcType = JdbcType.DATE),
            @Result(column = "end_date", property = "endDate", jdbcType = JdbcType.DATE),
            @Result(column = "weekday", property = "weekday", jdbcType = JdbcType.VARCHAR),
            @Result(column = "skip_holiday", property = "skipHoliday", jdbcType = JdbcType.CHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<BmSiteAppointDate> selectByFields(BmSiteAppointDate param);

    @Update({
            "<script>",
            "update bm_site_appoint_date",
            "<set>",
            "<if test=\"bmSiteId != null\">bm_site_id = #{bmSiteId,jdbcType=BIGINT},</if>",
            "<if test=\"startDate != null\">start_date = #{startDate,jdbcType=DATE},</if>",
            "<if test=\"endDate != null\">end_date = #{endDate,jdbcType=DATE},</if>",
            "<if test=\"weekday != null\">weekday = #{weekday,jdbcType=VARCHAR},</if>",
            "<if test=\"skipHoliday != null\">skip_holiday = #{skipHoliday,jdbcType=CHAR},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where id = #{id,jdbcType=BIGINT}",
            "</script>"
    })
    int updateByPrimaryKeySelective(BmSiteAppointDate record);

    @Select({
            "<script>",
            "select",
            "id, bm_site_id, start_date, end_date, weekday, skip_holiday, state, create_time, create_staff_id, update_time, update_staff_id",
            "from bm_site_appoint_date",
            "<where>",
            "bm_site_id = #{bmSiteId,jdbcType=BIGINT} and state='1'",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "bm_site_id", property = "bmSiteId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_date", property = "startDate", jdbcType = JdbcType.DATE),
            @Result(column = "end_date", property = "endDate", jdbcType = JdbcType.DATE),
            @Result(column = "weekday", property = "weekday", jdbcType = JdbcType.VARCHAR),
            @Result(column = "skip_holiday", property = "skipHoliday", jdbcType = JdbcType.CHAR),
            @Result(column = "state", property = "state", jdbcType = JdbcType.CHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_staff_id", property = "createStaffId", jdbcType = JdbcType.BIGINT),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "update_staff_id", property = "updateStaffId", jdbcType = JdbcType.BIGINT)
    })
    List<DataMap> selectByBmSiteId(Long bmSiteId);
}