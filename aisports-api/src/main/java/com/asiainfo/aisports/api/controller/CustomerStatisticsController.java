package com.asiainfo.aisports.api.controller;

import com.asiainfo.aisports.api.web.WebResult;
import com.asiainfo.aisports.domain.core.Staff;
import com.asiainfo.aisports.service.CustomerStatisticsService;
import com.asiainfo.aisports.service.StaffService;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 需求5648创建
 * User: 齐彦洲
 * Date: 2017/11/22
 * Time: 11:16
 */
@RestController
@RequestMapping(value = "/api/statistics")
public class CustomerStatisticsController {

    @Autowired
    CustomerStatisticsService customerStatisticsService;
    @Autowired
    StaffService staffService;

    /**
     * 会员统计
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @RequestMapping(value = "/initByDate")
    public WebResult initByDate(@RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startTime,
                                @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endTime,
                                @RequestParam(value = "centerId") Long centerId,
                                @RequestParam(value = "forward", required = false) String forward,
                                @RequestParam(value = "unit", required = false) String unit,
                                @RequestParam(value = "staffId") Long staffId) {
        Staff staff = staffService.findByStaffId(staffId);
        WebResult webResult = new WebResult();
        webResult.set("info", customerStatisticsService.init(startTime, endTime, staff,unit));
        if (!Strings.isNullOrEmpty(forward)) {
            // 获取前一天23:59:59
            Date lastEndTime = DateCalUtil.getDateWithOffset(startTime, "-1d");
            lastEndTime = DateCalUtil.endTime(lastEndTime);
            // 获取 forward天前的日期
            Date lastStartTime = DateCalUtil.getDateWithOffset(startTime, "-" + forward + "d");
            webResult.set("lastInfo", customerStatisticsService.init(lastStartTime, lastEndTime, staff,unit));
        }
        return webResult;
    }
}
