package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.CenterLang;
import com.asiainfo.aisports.domain.core.CenterLangKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface CenterLangMapper {
    @Delete({
        "delete from center_lang",
        "where center_id = #{centerId,jdbcType=BIGINT}",
          "and language = #{language,jdbcType=VARCHAR}"
    })
    int deleteByPrimaryKey(CenterLangKey key);

    @Insert({
        "insert into center_lang (center_id, language, ",
        "center_name, create_time, ",
        "create_staff_id, update_time, ",
        "update_staff_id)",
        "values (#{centerId,jdbcType=BIGINT}, #{language,jdbcType=VARCHAR}, ",
        "#{centerName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
        "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(CenterLang record);

    @Select({
        "select",
        "center_id, language, center_name, create_time, create_staff_id, update_time, ",
        "update_staff_id",
        "from center_lang",
        "where center_id = #{centerId,jdbcType=BIGINT}",
          "and language = #{language,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="language", property="language", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="center_name", property="centerName", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    CenterLang selectByPrimaryKey(CenterLangKey key);

    @Update({
        "update center_lang",
        "set center_name = #{centerName,jdbcType=VARCHAR},",
          "create_time = #{createTime,jdbcType=TIMESTAMP},",
          "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where center_id = #{centerId,jdbcType=BIGINT}",
          "and language = #{language,jdbcType=VARCHAR}"
    })
    int updateByPrimaryKey(CenterLang record);

    @Select({
        "<script>",
        "select",
        "center_id, language, center_name, create_time, create_staff_id, update_time, update_staff_id",
        "from center_lang",
        "<where>",
        "<if test=\"centerId != null\">and center_id = #{centerId,jdbcType=BIGINT}</if>",
        "<if test=\"language != null\">and language = #{language,jdbcType=VARCHAR}</if>",
        "<if test=\"centerName != null\">and center_name = #{centerName,jdbcType=VARCHAR}</if>",
        "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="language", property="language", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="center_name", property="centerName", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<CenterLang> selectByFields(CenterLang param);

    @Update({
        "<script>",
        "update center_lang",
        "<set>",
        "<if test=\"centerName != null\">center_name = #{centerName,jdbcType=VARCHAR},</if>",
        "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where center_id = #{centerId,jdbcType=BIGINT}",
          "and language = #{language,jdbcType=VARCHAR}",
        "</script>"
    })
    int updateByPrimaryKeySelective(CenterLang record);
}