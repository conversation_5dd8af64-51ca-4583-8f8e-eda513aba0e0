package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class TradePointsPayLog {
    private Long payLogId;

    private Long pointsAccountId;

    private Long tradeId;

    private Integer payPoints;

    private Long tradeStaffId;

    private Date tradeDate;

    private Long tradeVenueId;

    private Integer drawbackPoints;

    private String remark;

    private Long channelId;

    private Date updateTime;

    private Long updateStaffId;

    public Long getPayLogId() {
        return payLogId;
    }

    public void setPayLogId(Long payLogId) {
        this.payLogId = payLogId;
    }

    public Long getPointsAccountId() {
        return pointsAccountId;
    }

    public void setPointsAccountId(Long pointsAccountId) {
        this.pointsAccountId = pointsAccountId;
    }

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    public Integer getPayPoints() {
        return payPoints;
    }

    public void setPayPoints(Integer payPoints) {
        this.payPoints = payPoints;
    }

    public Long getTradeStaffId() {
        return tradeStaffId;
    }

    public void setTradeStaffId(Long tradeStaffId) {
        this.tradeStaffId = tradeStaffId;
    }

    public Date getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(Date tradeDate) {
        this.tradeDate = tradeDate;
    }

    public Long getTradeVenueId() {
        return tradeVenueId;
    }

    public void setTradeVenueId(Long tradeVenueId) {
        this.tradeVenueId = tradeVenueId;
    }

    public Integer getDrawbackPoints() {
        return drawbackPoints;
    }

    public void setDrawbackPoints(Integer drawbackPoints) {
        this.drawbackPoints = drawbackPoints;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", payLogId=").append(payLogId);
        sb.append(", pointsAccountId=").append(pointsAccountId);
        sb.append(", tradeId=").append(tradeId);
        sb.append(", payPoints=").append(payPoints);
        sb.append(", tradeStaffId=").append(tradeStaffId);
        sb.append(", tradeDate=").append(tradeDate);
        sb.append(", tradeVenueId=").append(tradeVenueId);
        sb.append(", drawbackPoints=").append(drawbackPoints);
        sb.append(", remark=").append(remark);
        sb.append(", channelId=").append(channelId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append("]");
        return sb.toString();
    }
}