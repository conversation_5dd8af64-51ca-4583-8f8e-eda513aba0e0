define(function(){ 
    return function getQueryVariable(variable) {
        var query = window.location.search
        if(query[0] === '?'){
            query = query.substring(1)
        }
        if(query[query.length - 1] === '/') {
            query = query.substr(0, query.length-1)
        }

        var vars = query.split("&")
        var varsObj = {}
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=")
            if(variable && (pair[0] == variable)){
                return pair[1]
            } else if(pair[1] !== undefined) {
                varsObj[pair[0]] = pair[1]
            }
        }
        if(variable) {
            return false
        }
        return varsObj
    }
})

