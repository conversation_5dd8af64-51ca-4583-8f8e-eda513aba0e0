<template>
  <div class="ecard-earch-wrap with-advance"  id="advanceQuery">
    <div class="ecard-earch-inner">
      <v-input id="ecardNo2"
          :ime-disabled=true
          v-on="$listeners"
          :disabled="disabled"
          v-model="innerValue"
      ></v-input>
      <span class="quick-search-icon" :data-index="index">高级</span>
    </div>
    <div class="cover-box cover-box-supersearch" id="superSearchBox2" style="display:none;">
      <div class="inner-box" style="width: 750px;min-height: 462px;">
        <h2>高级查询<span class="close-btn" @click="hide"><i class="iconfont icon-cuowu-1"></i></span></h2>
        <div class="content-box">
          <div class="f-line pad-ad-search">
            <input type="text" id="advanced-query2" placeholder="请输入姓名/手机号/身份证号" class="normal ad-search" />
            <button id="adInfoInput2" class="btn btn-md  btn-info btn-ad-search" data-click="q2">查询</button>
          </div>
          <div class="content text-area" id="search-user-list2" >
            <table data-label-row class="table table-striped table-th-border">
              <thead>
              <tr>
                <th>&nbsp;</th>
                <th>姓名</th>
                <th>卡号</th>
                <th>手机</th>
                <th>证件号</th>
                <th>余额</th>
                <th>储值卡余额</th>
              </tr>
              </thead>
            </table>
          </div>
        </div>
        <div class="coverbox-ft btns clearfix">
          <button class="btn btn-md btn-default close-btn" @click="hide">取消</button>
          <button id="adCardLossSubmit2" class="btn btn-md btn-primary confirm">确定</button>
        </div>
      </div>
    </div>
    <input type="hidden" id="ad-username"/>
    <input type="hidden" id="ad-sex"/>
    <input type="hidden" id="ad-birthday"/>
    <input type="hidden" id="ad-address"/>
    <input type="hidden" id="ad-tmp"/>
    <input type="hidden" id="ad-validTime"/>
    <input type="hidden" id="ad-picture"/>
  </div>
</template>

<script>
import VInput from 'vue-components/form-elements/VInput.vue'
import ClickOutside from 'vue-click-outside'
export default {
  inheritAttrs: false,
  components: {
    VInput,
  },
  directives: {
    ClickOutside
  },
  props : ['value' , 'disableReadCard','disabled', 'index'],
  computed: {
    innerValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  beforeCreate() {
    this.$listeners.input = () => {
    }
  },
  mounted() {
    this.loadSuperSearch()
    this.searchById()
  },
  methods: {
    searchById() {
      if (!this.disableReadCard) {
        window.SEARCHBYID = this.searchCardNo
      }
    },
    handleClose() {
    },
    loadSuperSearch() {
      import('./advanceQuery').then(superSearch => {
        superSearch(card => {
          if (!this.index || (this.index && this.index === parseInt(card.index))) {
            this.searchCardNo(card.ecardNo)
            this.$emit('select-card', card)
          }
        })
      });
    },
    searchCardNo(ecardNo) {
      this.$emit('input', ecardNo)

      this.$nextTick(() => {
        this.$emit('search')
      })
    },
    hide() {
      $('#superSearchBox2').hide()
    }
  }
}
</script>

<style lang="scss">
.ecard-earch-wrap {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  height: 36px;
  line-height: 36px;
  .ecard-earch-inner {
    font-size: 0;
  }
  input {
    font-size: 14px;
  }
  &.with-advance{
    input{
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
  .quick-search-icon {
    font-size: 14px;
    cursor: pointer;
    display: inline-block;
    height: 36px;
    line-height: 34px;
    width: 65px;
    margin-left: -1px;
    background-color: #eee;
    border: 1px solid #ccc;
    text-align: center;
    vertical-align: middle;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
}
.pad-ad-search {
  margin-top: 0;
}
</style>
