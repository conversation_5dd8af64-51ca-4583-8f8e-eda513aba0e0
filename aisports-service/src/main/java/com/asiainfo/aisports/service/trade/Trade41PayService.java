package com.asiainfo.aisports.service.trade;

import com.asiainfo.aisports.domain.core.Trade;
import com.asiainfo.aisports.service.RentCabinetService;
import com.yunyu.park.payment.enums.ConsumeTypeEnums;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by qianxin on 15/3/16.
 */
@Service
public class Trade41PayService extends BaseTradePayService {
    private static final String URL = "/rentCabinet/apply";

    @Autowired
    RentCabinetService rentCabinetService;

    @Override
    @Transactional
    public TradePayResult finishTrade(Trade trade){
        rentCabinetService.finishOrder(trade);
        super.countPoints(trade);
        // 平阳场馆同步订单
        super.pingYangSyncFinishedOrder(trade, ConsumeTypeEnums.NormalConsume.getValue());
        return new TradePayResult().set("redirect", URL);
    }

    @Override
    public String getUrl(Trade trade) {
        return URL;
    }
}
