import { showWarning } from 'mAlert';
import { initSelectPicker } from 'utils/initPlugins';
import filterIntersectionBy from 'utils/filterIntersectionBy';

export function addAllotItem(items, item, validateItems) {
    if (!$(this.$els.allotDetailLines).validate()) {
        return;
    }

    if (!validateItems.every(ai => ai.skuId !== item.skuId)) {
        showWarning(`商品 ${item.name} 规格 ${item.specName}已添加`);
        return;
    }

    this.addedItems.push(item);

    this.resetAddItem();
}

export function getOutWareGoods(outWarehouseId) {
    $.get('/goodsStockTransfer/getWareGoods', {
        id: outWarehouseId,
    }, (data) => {
        this.outWareGoods = data.goodsList;
        this.goodsSpecs = [];
        this.$nextTick(function () {
            initSelectPicker(this.$els.itemAdd)
        })
    })
}

export function getGoodsSpecs(outWarehouseId, goodsId) {
    $.get('/goodsStockTransfer/getWareGoodsSkuList', {
        goodsId,
        warehouseId: outWarehouseId,
    }, (data) => {
        this.goodsSpecs = data.goodsSkuList;

        this.$nextTick(function () {
            initSelectPicker(this.$els.itemAdd)
        })
    })
}

export function resetAddItem() {
    this.selectedGood = '';
    this.selectedSpec = '';
    this.itemPrice = '';
    this.itemNum = '';

    this.$nextTick(function () {
        initSelectPicker(this.$els.itemAdd)
    })
}

export function showBatchAddGoods() {
    if (!$(this.$els.itemCat).validate()) {
        return;
    }
    this.batchAddGoods = true;
}

export function batchGoodClose() {
    this.batchAddGoods = false;
}

export function changeSpec() {
    this.itemPrice = this.selectedSpec.costPrice / 100
}

export function batchConfirm(goods) {
    const batchAddedGoods = goods.map(item => ({
        skuId: item.skuId,
        specName: item.specName,
        goodsId: item.goodsId,
        goodsNo: item.goodsNo,
        name: item.goodsName,
        num: item.goodsNum,
        price: item.costPrice / 100,
        goodsCnt: item.goodsCnt,
    }))
    const prevLength = this.addedItems.length
    const addedLength = batchAddedGoods.length

    this.addedItems = [...this.addedItems, ...filterIntersectionBy(
        batchAddedGoods,
        this.validateAddedItems || this.addedItems, item => item.skuId,
    )];
    this.batchAddGoods = false;

    if ((prevLength + addedLength) > this.addedItems.length) {
        showWarning('批量添加的商品中存在已添加过的规格， 已经去除')
    }
}
