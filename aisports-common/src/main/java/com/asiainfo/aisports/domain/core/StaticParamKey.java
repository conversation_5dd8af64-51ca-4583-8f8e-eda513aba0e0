package com.asiainfo.aisports.domain.core;

import java.io.Serializable;

public class StaticParamKey implements Serializable {
    private String attrCode;

    private String attrValue;

    public String getAttrCode() {
        return attrCode;
    }

    public void setAttrCode(String attrCode) {
        this.attrCode = attrCode == null ? null : attrCode.trim();
    }

    public String getAttrValue() {
        return attrValue;
    }

    public void setAttrValue(String attrValue) {
        this.attrValue = attrValue == null ? null : attrValue.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", attrCode=").append(attrCode);
        sb.append(", attrValue=").append(attrValue);
        sb.append("]");
        return sb.toString();
    }
}