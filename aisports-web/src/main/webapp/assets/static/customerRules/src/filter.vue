<template>
    <div class="content-outer">
        <div
            v-if="venueList.length > 1"
            class="sub-title">使用场馆：</div>
        <div
            v-if="venueList.length > 1"
            class="venue-list">
            <div class="detail-line">
                <select
                    v-model="venueId"
                    class="qselect">
                    <option
                        v-for = "item in venueList"
                        :value="item.venueId">{{ item.venueName }}</option>
                </select>
            </div>
        </div>
        <div class="sub-title">是否开启客户过滤规则：</div>
        <div class="switch-line">
            <label>
                <input
                    v-model="status"
                    type="radio"
                    value="true"
                    name="r">
                <span>开启</span>
            </label>
            <label>
                <input
                    v-model="status"
                    type="radio"
                    value="false"
                    name="r">
                <span>关闭</span>
            </label>
        </div>
        <div class="sub-title">当客户满足以下任意条件时，进行客户过滤：</div>
        <div class="config-box">
            <div class="detail-line with-input">
                <input
                    v-model="memberFilterTag"
                    type="checkbox">
                <div class="detail">如该手机号码已是正式会员，则不能录入系统，如到期未续课，超过
                    <input
                        v-if="memberFilter"
                        v-model="memberFilter"
                        required
                        max="365"
                        min="3"
                        vtype="number"
                        vtext="请填写正确的数字"
                        type="text"
                        class="normal"
                        placeholder="3-365内的整数"
                        maxlength="3">
                    <input
                        v-else
                        v-model="memberFilter"
                        type="text"
                        class="normal"
                        placeholder="3-365内的整数"
                        maxlength="3">
                    天，可以重新成为潜在客户；
                </div>
            </div>
            <div class="detail-line with-input">
                <input
                    v-model="repeatPhoneFilterTag"
                    type="checkbox">
                <div class="detail">
                    如该手机号码上次分配时间超过
                    <input
                        v-if="repeatPhoneFilter"
                        v-model="repeatPhoneFilter"
                        required
                        max="365"
                        min="3"
                        vtype="number"
                        vtext="请填写正确的数字"
                        type="text"
                        class="normal"
                        placeholder="3-365内的整数"
                        maxlength="3">
                    <input
                        v-else
                        v-model="repeatPhoneFilter"
                        type="text"
                        class="normal"
                        placeholder="3-365内的整数"
                        maxlength="3">
                    天，且无成单或无客户意向等级，可重复录入系统（将该号码的上次分配关系进行作废，重复的号码只记录来源渠道和来源时间，不参与推广人员分配）；
                </div>
            </div>
            <div class="detail-line">
                <input
                    v-model="blacklistFilter"
                    type="checkbox">
                <div class="detail">在黑名单列表中的客户不得导入；</div>
            </div>
        </div>
        <button
            class="btn btn-lg btn-primary"
            @click="submit">保存设置</button>
    </div>
</template>

<script>
import * as API from './APi'
import mAlert from 'mAlert'
import 'xValidate'
import { initSelectPicker } from 'utils/initPlugins'


export default{
    data() {
        return {
            venueId: '',
            status: '',
            memberFilter: '',
            repeatPhoneFilter: '',
            blacklistFilter: false,
            venueList: window.__venueList__,
        }
    },
    computed: {
        memberFilterTag: {
            get() {
                if (this.memberFilter) {
                    return true
                }
                return false

            },
            set() {
                this.memberFilter = ''
            },

        },
        repeatPhoneFilterTag: {
            get() {
                if (this.repeatPhoneFilter) {
                    return true
                }
                return false

            },
            set() {
                this.repeatPhoneFilter = ''
            },
        },
    },
    watch: {
        venueId() {
            this.init()
        },
    },
    mounted() {
        if (this.venueList.length != 0) {
            this.venueId = this.venueList[0].venueId
        }
        this.init()
    },
    methods: {
        initSelectPicker() {
            this.$nextTick(() => {
                initSelectPicker(this.$el)
            })
        },
        init() {
            API.init({
                paramCode: 1,
                venueId: this.venueId,
            }, (res) => {
                this.venueId = res.venueId
                this.status = res.info.status
                this.memberFilter = res.info.memberFilter || ''
                this.repeatPhoneFilter = res.info.repeatPhoneFilter || ''
                this.blacklistFilter = res.info.blacklistFilter
            })
            initSelectPicker()
        },
        submit() {
            if (!$(this.$el).validate()) {
                return;
            }
            const info = {}
            if (this.status != undefined) {
                info.status = this.status
            }
            if (this.memberFilter) {
                info.memberFilter = this.memberFilter
            }
            if (this.repeatPhoneFilter) {
                info.repeatPhoneFilter = this.repeatPhoneFilter
            }
            if (this.blacklistFilter != undefined) {
                info.blacklistFilter = this.blacklistFilter
            }
            API.submit({
                paramCode: 1,
                venueId: this.venueId,
                paramValue: JSON.stringify(info),
            }, (res) => {
                mAlert.showSuccess('保存成功')
                window.location.reload()
            })
        },
    },
}
</script>
