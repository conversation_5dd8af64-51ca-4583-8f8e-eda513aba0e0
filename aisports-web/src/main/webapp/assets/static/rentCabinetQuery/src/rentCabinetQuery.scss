$scl: 1.5;
@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../common/scss/bigTabs";

.wrapper{
  .main-delay{
    position: static;
  }
}
.delay{ 
  padding: 20px 20px 90px 20px;
  position: relative;

  .title-box{
    overflow: visible;
    position: relative;
    .title-top{
      height: 60px;
      line-height: 60px;
      position: relative;
      border-bottom: 1px solid #ddd;
        .right-icon{
            position: absolute;
            height: 60px;
            width: 14px;
            right: 0px;
            top: 0px;
            cursor: pointer;
            border: 1px solid #dddddd;
            background: url(../images/scroll-right-icon.gif) #fafafa no-repeat center center;
        }
      >.big-tab{
        width: (637/1205)*100%;
        overflow: hidden;
        border-bottom: 0;
        position: relative;
          .tab li:last-child{
              border-right: none;
              padding-right: 41px;
          }
      }
      .title-content{
        display: inline-block;
        text-align: center;
        height: 60px;
        font-size: 18px;
        background-color: #fafafa;
        border-bottom: 1px solid #dddddd;
        cursor: pointer;
        vertical-align: middle;
      }
      .cur{
        color: #1fa2f5;
        border-bottom: 1px solid #fff;
        background-color:#fff;
        vertical-align: middle;
      }

    }
    .main-body{
      .left-body{
        display: inline-block;
        width: (779/1120)*100%;
        padding: 15px;
        padding-bottom: 21px;
        border-right: 1px solid #dddddd;
        min-height: 554px;
        .f-line{
          margin: 0;
        }
      }
      .right-body{
        display: inline-block;
        width: (310/1120)*100%;
        position: absolute;
        margin: 25px 20px 0 10px;
        .selected-cabinet{
          padding: 18px 0;
          border-bottom: 1px dotted #dddddd;
          //max-height: 330px;
          overflow: auto;
          .remark{
            .remark-info{
              font-size: 18px;
              font-weight: 600;
            }
            .upcoming-info{
              float: right;
              margin-top: -9px;
              .day-num{
                color: #fd4d2b;
                font-size: 24px;
              }
            }
            .overdue-info{
              float: right;
              margin-top: -9px;
              .day-num{
                color: #fd4d2b;
                font-size: 24px;
              }
            }
          }
          .no-cabinet-info{
            height: 148px;
            border-top: 1px dotted #dddddd;
            margin-top: 6px;
            text-align: center;
            //background: url("../images/right-background.jpg") no-repeat center;
            i{
              color: #e8e8e8;
              font-size: 50px;
              margin-top: 25px;
              display: inline-block;
            }
            p{
              color: #989898;
            }
          }
        }
        .occupy-cabinet-info{
          border: 0;
        }
        .submit-btn{
          text-align: center;
          margin-top: 2px;
        }
      }
    }
  }
}

.state-tag{
  text-align: right;
  margin-bottom: -25px;
  .state-wrapper{
    .tag-remark{
      display: inline-block;
      width: 28px;
      height: 13px;
      border: 1px solid #dddddd;
      vertical-align: middle;
      margin-left: 10px;
    }
    .available-tag{
      background-color: #56b855;
      border: 1px solid #56b855;
    }
    .occupy-tag{
      background-color: #e8e8e8;
      border: 1px solid #e8e8e8;
    }
    .upcoming-tag{
      background-color: #fb8303;
      border: 1px solid #ffa72b;
    }
    .overdue-tag{
      background-color: #fd4d2b;
      border: 1px solid #fb6e52;
    }
    .process-tag{
      background-color: #d8f0ff;
      border: 1px solid #d8f0ff;
    }
    .content-remark{
      vertical-align: middle;
      margin-left: 5px;
    }
  }
}
.export-btn-wrap{
  margin-top: -60px;
  text-align: right;
  padding-right: 30px;
}
.occupy-content{
  margin-top: 6px;
  .list-info{
    //height: 77px;
    padding: 14px 18px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-bottom: 1px dotted #dddddd;
    position: relative;
    &.no-padding{
      padding: 14px 0;
    }
  }
  .list-info:nth-child(1){
    border-top: 1px dotted #dddddd;
  }
  .rent-info{
    border-left: 3px solid #fc8b79;
  }
  .rent-money-info{
    border-left: 3px solid #f4cf7e;
  }
  .rent-person-info{
    border-left: 3px solid #60cfa5;
  }
}
.remark-wrapper{
  display: inline-block;
  width: 70px;
}
.f-line{
  padding-bottom: 18px;
  .remark-line{
    padding-bottom: 5px;
    span:nth-child(2){
      margin-left: 8px;
    }
  }
  .select-line{
    margin: 0 -5px;
    padding-bottom: 10px;
    .select-wrapper{
      margin:6px 0 0 3px;
      height: 64px;
      width: (1/11.5)*100%;
      text-align: center;
      float: left;
      border-radius: 5px;
      position: relative;
      &:after{
        position: absolute;
        height: 64px;
        background-color: #f3f3f3;
        opacity: 0.5;
        content: '';
        left: 0;
        top: 0;
        bottom: 0;
        right: 0;
      }
      //@include transiTion();
      .cab-number{
        display: block;
        margin-top: 9px;
      }
      .cab-price{
        display: inline-block;
        margin-top: 5px;
      }
    }
    /*.selected{
      @include scaLe($scl);
      @include tranlateOrigen(center,center);
      @include transiTion();
    }*/
    .selected{
      border: 3px solid #1fa2f5;
      border-radius: 4px;
      &:after{
        display: none;
      }
    }
    .available{
      background: url("../images/background-available.jpg") no-repeat center #56b855;
      //background-color: ;
      color: #fff;
      cursor: pointer;
    }
    .occupy{
      background: url("../images/background-occupy.jpg") no-repeat center #e8e8e8;
      //background-color: ;
      cursor: pointer;
    }
    .upcoming{
      background: url("../images/background-upcoming.jpg") no-repeat center #fc8801;
      //background-color: ;
      color: #fff;
      cursor: pointer;
    }
    .overdue{
      background: url("../images/background-overdue.jpg") no-repeat center #fd4d2b;
      //background-color: ;
      color: #fff;
      cursor: pointer;
    }
    .process{
      background: url("../images/background-process.png") no-repeat center #d8f0ff;
      //background-color: ;
      // color: #fff;
      cursor: pointer;
    }
  }
}
.rent-line{
  line-height: 0px;
  .widthsmall{
    width: 185px;
  }
  [data-type="ipt"]{
    display: none;
  }
  .ipt-box{
    display: inline-block;
    position: relative;
    input{
      cursor: pointer;
    }
    .icon-bianji{
      position: absolute;
      right: 10px;
      top: 18px;
      color: #c2c0c0;
      cursor: pointer;
    }
  }
}
.info-box{
  padding: 0 18px;
}
.edit-btn{
  margin-top: 15px;
  @include clearfix();
  padding-left: 18px;
  .btn-confirm{
    width: 100px;
    float: left;
  }
  .btn-cancel{
    float: right;
    background-color: #e8e8e8;
    color: #747474;
  }
}
.edit{
  position: absolute;
  top: 10px;
  right: 2px;
  padding: 3px 13px;
}
.margintop{
  margin-top: 10px;
}

// 导出租赁信息
.export-rent-line{
  padding-bottom: 0;
  margin-top: 0;
  height: 260px;
}
.export-rent-areas{
  margin-top: -20px;
}
.export-rent-area{
  position: relative;
  display: inline-block;
  width: 106px;
  height: 36px;
  line-height: 34px;
  margin-top: 20px;
  background-color: #efefef;
  border: 1px solid #e0e0e0;
  text-align: center;
  margin-right: 15px;
  i.iconfont{
    display: none;
  }
  &.checked{
    background-color: #fff;
    color: #56b854;
    border: 2px solid #56b854;
    line-height: 32px;
    i.iconfont{
      color: #fff;
      display: inline;
      position: absolute;
      right: -2px;
      bottom: -2px;
      line-height: 1;
      font-size: 12px;
      @include scaLe(0.6);
      &:after{
        content: " ";
        position: absolute;
        right: -3px;
        bottom: -12px;
        @include triangle("right", 15px, 15px, #56b854);
        @include rotate(45deg);
      }
      &:before{
        position: relative;
        top: 4px;
        left: 1px;
        z-index: 2;
      }
    }
  }
}
.search-by-phone{
  .normal{
    width: 178px;
  }
  .btn-confirm{
    width: 100px;
    margin-left: 10px;
  }
}
.search-phone-box{
  .content-box{
    height: 340px;
    overflow-y: auto;
  }
  .table-box{
    .table{
      thead{
        th{
          border: none;
        }
      }
      tbody{
        tr{
          border-bottom: 1px solid #f5f5f5;
          &:nth-child(2n){
          background-color: #fcfdff;
        }
          td{
            border: none;
          }
        }
      }
    }
  }
}