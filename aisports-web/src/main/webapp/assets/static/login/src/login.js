/**
 * Created by Nightost on 2015/6/3.
 */
import Vue from 'vue'
import {
    Steps,
    Step,
    Dialog,
    Button,
    Input,
    Icon
} from 'element-ui'
// import JSEncrypt from 'jsencrypt'
import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

define((require) => {
    Vue.use(Steps)
    Vue.use(Step)
    Vue.use(Input)
    Vue.use(Icon)
    Vue.use(Dialog)
    Vue.use(Button)
    const settings = require('aisportsConfig');
    const mAlertIns = require('mAlert');
    let publicKey = ''
    const forgotPassword = new Vue({
        el: '#vue_forgot_password',
        data: {
            dialogVisible: false,
            active: 0,
            mobilePhone: '',
            phoneCode: '',
            codeFlag: true,
            nextDisabled: true,
            nextMessage: '下一步',
            newPassword: '',
            vpassword: '',
            newPasswordType: 'password',
            vpasswordType: 'password',
            time:null,
            timeOut:null,
            time:'获取验证码'
        },
        watch: {
            mobilePhone(val) {
                const mobilPhoneReg = settings.regs.mobilphone;
                if (mobilPhoneReg.test(val)) {
                    this.codeFlag = false
                } else {
                    this.codeFlag = true
                }
            },
            phoneCode(val) {
                if (val.length > 0 && !this.codeFlag) {
                    this.nextDisabled = false
                }
            }
        },
        beforeDestroy(){
            clearInterval(that.timeOut)
        },
        methods: {
            handleClose(done) {
                done();
            },
            setType(val) {
                if (val == 'newPassword') {
                    if (this.newPasswordType == 'password') this.newPasswordType = 'text'
                    else this.newPasswordType = 'password'
                }
                if (val == 'vpassword') {
                    if (this.vpasswordType == 'password') this.vpasswordType = 'text'
                    else this.vpasswordType = 'password'
                }
                this.$forceUpdate()
            },
            next() {
                let that = this
                console.log(that.active, 'that.active')
                if (that.active == 0) {
                    $.ajax({
                        type: 'POST',
                        url: '/login/checkVerifyCode',
                        data: {
                            phone: that.mobilePhone,
                            verifycode:that.phoneCode
                        },
                        success(data) {
                            if (data.error === 0) {
                                that.active = 1
                                return
                            } else {
                                mAlertIns.showWarning(data.message)
                                return
                            }
                        },
                    })
                }
                if (that.active == 1) {
                    if (this.newPassword === this.vpassword) {
                        if (this.newPassword.length > 7) {
                            $.ajax({
                                type: 'POST',
                                url: '/login/changePhonePassword',
                                data: {
                                    phone: that.mobilePhone,
                                    newPassword: that.newPassword,
                                    verifycode: that.phoneCode
                                },
                                success(data) {
                                    if (data.error === 0) {
                                        that.active = 2
                                        that.nextMessage = '完成'
                                    } else {
                                        mAlertIns.showWarning(data.message)
                                    }
                                },
                            })
                        } else {
                            mAlertIns.showWarning('请输入8位以上的密码')
                        }

                    } else {
                        mAlertIns.showWarning('俩次输入密码不一致')
                    }
                }
                if (that.active == 2) {
                    that.dialogVisible = false
                    that.mobilePhone=''
                    that.phoneCode=''
                    that.codeFlag=true
                    that.nextDisabled=true
                    that.newPassword=''
                    that.vpassword=''
                    that.newPasswordType='password'
                    that.vpasswordType='password'
                }
            },
            getCode() {
                let that = this
                $.ajax({
                    type: 'POST',
                    url: '/login/getVerifyCode',
                    data: {
                        mobileNum: that.mobilePhone,
                    },
                    success(data) {
                        if (data.error === 0) {
                            mAlertIns.showSuccess('验证码发送成功')
                            that.time=59
                            that.timeOut=setInterval(() => {
                                if(that.time<1){
                                    that.time='获取验证码'
                                    clearInterval(that.timeOut)
                                    return
                                }
                                that.time--
                                // console.log(that.time,'that.time')
                            }, 1000);
                        } else {
                            mAlertIns.showWarning(data.message)
                        }
                    },
                })
            }
        },
    })
    const $ = require('jquery');
    const cookie = require('cookie');

    let timer = null,
        time = 60,
        defaultVText = '获取验证码';
    require('qselect');
    require('showPrompt');

    let siteList = []; //服务点列表

    let licenseError = false; //license校验是否失效

    // 恢复状态
    function recoveryState() {
        $('button.disabled').removeClass('disabled')
            .prop('disabled', false);
    }

    /**
     * 获取UKeyID
     * //0:没找到 -1：获取失败 1：正常
     */
    function getUKeyID() {
        let result;
        let resultArray;
        try {
            result = MYAvtiveX.getUKeyID('');
        } catch (e) {
            console.log(`无法读取取UKeyID${e}`);
        }
        if (!result) {
            return;
        }
        resultArray = result.split(',');
        if (resultArray[0] == '1') {
            result = resultArray[1];
        } else {
            result = undefined;
            console.log(resultArray[1]);
        }
        return result;
    }
    /**
     * 获取cookie，选择登录方式
     */
    function selectTypeByCookie() {
        let typeIndex;
        typeIndex = cookie.get('loginType');
        if (typeof typeIndex != 'undefined') {
            $('.switch-login-type ul li').eq(parseInt(typeIndex)).trigger('click');
        }
    }
    /**
     * init slide
     */
    function initSlideType() {
        $('.switch-login-type ul li').on('click', function () {
            let $this,
                dataTarget,
                index,
                tempIndexStr,
                flag;
            $this = $(this);
            flag = $this.hasClass('active');
            if (flag) return;
            index = $this.index() + 1;
            tempIndexStr = `type-${index}`;
            dataTarget = $this.attr('data-target');
            $('.form-signin.active').removeClass('active');
            $(`[data-role= ${dataTarget} ]`).addClass('active');
            $('.switch-login-type').attr('data-index', tempIndexStr);
            $(this).siblings('li').removeClass('active');
            $(this).toggleClass('active');
        });
    }

    /**
     * check phone num
     */
    function checkPhoneNum(num) {
        const pn = num || $('#phonenumber').val();
        const mobilPhoneReg = settings.regs.mobilphone;
        if (mobilPhoneReg.test(pn)) {
            return true;
        }

        return false;

    }
    function createUserName(userName) {
        const domain = $('#domain').html();
        const $site = $('.form-signin.active .site-list');
        const s = $site.val();
        let selSite = siteList.filter((item => item.id == s))
        let delayFlag = '0'; //是否延迟跳转
        if (selSite && selSite.length) {
            $.ajax({
                type: 'GET',
                async: false,
                url: '/license/licenseInfo',
                data: {
                    venueId: selSite[0].venueId
                },
                success(data) {
                    if (data.error === 0) {
                        if (data.message != 'ok') {
                            mAlertIns.showWarning(data.message)
                            delayFlag = '1'
                        }
                        licenseError = false
                    } else {
                        mAlertIns.showWrong(data.message)
                        licenseError = true
                    }
                }
            })
        }
        const loginData = {
            u: filterUnVisChar(userName),
            d: domain,
            df: delayFlag
        };
        if (s) {
            loginData.s = s;
        }
        const key = getUKeyID();
        if (key) {
            loginData.k = key;
        }
        return JSON.stringify(loginData)
    }

    /**
     * 过滤非可见字符
     * 原理 去除 ASCII中 0-31的字符
     */
    function filterUnVisChar(str) {
        return str.replace(/^\s*|[\x00-\x1f]|\s*$/g, '');// eslint-disable-line
    }
    /**
     * 用户名的submit,加上后缀,区别与xports
     */
    function submitHandleForUsername() {
        $('.submit-username').on('click', () => {
            const userName = $('#username').val();
            $('#fakeusername').val(createUserName(userName));
            //密码进行加密处理再传给后端
            const password = $("#password").val()
            if (publicKey) {
                const encrypt = new JSEncrypt();
                encrypt.setPublicKey(publicKey);
                const encryptedData = encrypt.encrypt(password);
                $('#fakePassword').val(encodeURI(encryptedData));
            } else {
                $('#fakePassword').val(password);
            }
        })
    }
    function forgotPasswordClick() {
        $('#forgot_password').on('click', (event) => {
            forgotPassword.dialogVisible = true
            forgotPassword.active = 0
            forgotPassword.nextMessage = '下一步'
        })
    }
    function handlePhoneInput() {
        $('#phonenumber').on('input', function () {
            if ($(this).val() === '') {
                $('#get-v-code').addClass('disabled').removeClass('enabled')
            } else {
                $('#get-v-code').removeClass('disabled').addClass('enabled')
            }
        });
        $('#phonenumber').trigger('input');
    }

    /**
     * 手机号的submit
     */
    function submitHandleForPhone() {
        $('.submit-phone').on('click', () => {
            let phone,
                phoneEl,
                vCode,
                vCodeEl;
            const $phoneForm = $('#phone-form');
            phoneEl = $('#phonenumber');
            vCodeEl = $('#vcode');
            phone = phoneEl.val();
            vCode = $.trim(vCodeEl.val());
            if (!checkPhoneNum(phone)) {
                phoneEl.showPrompt('请填入正确的手机号码！', true);
                return false;
            }
            if (vCode.length === 0) {
                vCodeEl.showPrompt('请填入正确的验证码！', true);
                return false;
            }
            // 设置隐藏值
            $phoneForm.find("[name='username']")
                .val(createUserName(`mobile_${phoneEl.val()}`));
            $phoneForm.find("[name='password']")
                .val(filterUnVisChar(vCodeEl.val()));
        });
    }

    /**
     * 初始化验证码点击
     */
    function handleVcodeClick() {
        $(document).on('click', '.get-v-code.enabled', function () {
            let $this = $(this),
                phoneEl,
                phone;
            phoneEl = $('#phonenumber');
            phone = phoneEl.val();
            if (!checkPhoneNum(phone)) {
                phoneEl.showPrompt('请填入正确的手机号码！', true);
                return;
            }
            // 获取验证码
            getVCode(phone, () => {
                if (timer != null) {

                } else {
                    $this.addClass('disabled');
                    $('.get-v-code').text(`${time--} 秒`);
                    timer = setInterval(() => {
                        if (time === 0) {
                            $('.get-v-code').removeClass('disabled').text(defaultVText);
                            clearInterval(timer);
                            timer = null;
                            time = 60;
                            return;
                        }
                        $('.get-v-code').text(`${time--} 秒`);
                    }, 1000);
                }
            });

        })
    }

    /**
     * 创建cookie
     */
    function createLoginTypeByCookie() {
        $("[data-role = 'username']").on('submit', (e) => {
            $('.submit-username').prop('disabled', true).addClass('disabled');
            cookie.set('loginType', '0', {
                expires: 14,
            });
        });
        $("[data-role = 'mobile']").on('submit', () => {
            $('.submit-username').prop('disabled', true).addClass('disabled');
            cookie.set('loginType', '1', {
                expires: 14,
            });
        });
        $('[type="submit"]').on('click', (e) => {
            let tabBoxEl = $('[data-index]'),
                phoneEl = $('#phonenumber'),
                index = tabBoxEl.attr('data-index');
            if ((index === 'type-1') && !checkUserNP()) {
                e.preventDefault();
                return false;
            } else if ((index === 'type-2') && !checkPhoneNum()) {
                e.preventDefault();
                return false;
            }
        });
    }

    /**
     * check user name password
     * check 用户名密码是否为空
     */
    function checkUserNP() {
        let name = $.trim($('#username').val()),
            password = $.trim($('#password').val());
        if (name.length === 0) {
            $('#username').showPrompt('用户名不能为空！', true);
            return false;
        }
        if (password.length === 0) {
            $('#password').showPrompt('密码不能为空！', true);
            return false;
        }
        return true;
    }

    /**
     * 获取验证码逻辑
     */
    function getVCode(mobileNum, callback) {
        $.ajax({
            type: 'POST',
            url: '/login/verifyCode',
            data: {
                mobileNum,
            },
            success(data) {
                if (data.error === 0) {
                    callback();
                } else {
                    $('#phonenumber').showPrompt(data.message, true);
                }
            },
        })
    }

    /**
     * 处理错误
     */
    function handleError() {
        const errorEl = $('#error');
        if (errorEl.html() != '') {
            // 显示错误信息
            const error = errorEl.html();
            let message = '';
            if ($('.login-mobile').hasClass('active')) { // 手机验证码登录
                message = mobielLoginError(error);
                mAlertIns.showWrong(message);
            } else if (error === 'passwordNotMatch') {
                const url = window.location.href
                if (url.indexOf('username') > -1) {
                    const len = url.slice(url.indexOf('username'))
                    const username = len.slice(len.indexOf('username') + 9)
                    $('#username').val(username)
                }
                $('.password-not-match').show();

            } else {
                message = loginError(error);
                mAlertIns.showWrong(message);
            }

        }
    }

    /**
     * 获得登录错误
     * @param error
     */
    function loginError(error) {
        let message = '';
        switch (error) {
            case 'badCredentials':
                message = '密码错误';
                break;
            case 'credentialsExpired':
                message = '密码已过期';
                break;
            case 'accountLocked':
                message = '用户已被锁定';
                break;
            case 'accountDisabled':
                message = '用户已被禁用';
                break;
            case 'usernameNotFound':
                message = '用户名或密码错误';
                break;
            case 'usbKeyNotMatch':
                message = '无效的usbkey,请检查usbkey设备';
                break;
            case 'multiLogin':
                message = '很抱歉，由于您的账号已经在其他设备登录，您被迫下线';
                break;
            default:
                message = error;
        }
        return message;
    }

    /**
     * 获得手机登录错误
     * @param error
     */
    function mobielLoginError(error) {
        let message = '';
        switch (error) {
            case 'badCredentials':
                message = '无效的验证码';
                break;
            case 'credentialsExpired':
                message = '验证码已过期';
                break;
            case 'accountLocked':
                message = '用户已被锁定';
                break;
            case 'accountDisabled':
                message = '用户已被禁用';
                break;
            case 'usernameNotFound':
                message = '手机号码没有注册用户';
                break;
            case 'usbKeyNotMatch':
                message = '无效的usbkey,请检查usbkey设备';
                break;
            default:
                message = error;
        }
        return message;
    }

    /**
     * 获取服务网点
     */
    function checkSite() {
        const $username = $('#username');
        const username = $username.val();
        if (username != '') {
            getSites({
                username,
            });
        }
        $('#username,#phonenumber').on('change', (e) => {
            handleGetSites(e);
        })
            .on('keydown', (e) => {
                if (e.keyCode === 13) {
                    handleGetSites(e, true);
                    return false;
                }
            });
    }

    function handleGetSites(e, autoSubmit) {
        const id = e.currentTarget.getAttribute('id');
        const value = e.currentTarget.value;
        $('.submit-username').prop('disabled', true).addClass('disabled');
        let data;
        if (id === 'username') {
            data = {
                username: value,
            }
        } else if (id === 'phonenumber') {
            data = {
                mobielNum: value,
            }
        }
        getSites(data, autoSubmit);
    }

    function showSites() {
        const $container = $('.form-signin.active .site');
        const $select = $container.find('select');
        $container.show();
        if ($select.data('selectpicker')) {
            $select.selectpicker('refresh');
        } else {
            $select.selectpicker();
        }
        refreshFormMt();
    }

    function hideSites() {
        $('.form-signin.active .site').hide();
        refreshFormMt();
    }

    function getSites(data, autoSubmit) {
        $.ajax({
            url: '/site/getSiteList',
            type: 'POST',
            data,
            success(data) {
                if (data.error) {
                    // $('.form-signin.active .user input[type = text]').showPrompt('未查询到员工信息', true);
                    return;
                }
                $('.submit-username').prop('disabled', false).removeClass('disabled');
                handleSites(autoSubmit, data);
            },
        });
    }


    function handleSites(autoSubmit, data) {
        let list = [];
        if (data.error !== 0) {
            hideSites();
        }
        if (data.siteList && data.siteList.length > 0) {
            list = data.siteList;
            siteList = list;
        }
        renderSite(list);
        if (data.siteList && data.siteList.length > 1) {
            showSites();
        } else {
            hideSites();
        }
        if (autoSubmit) {
            $('.form-signin.active [type = submit]').trigger('click');
        }
    }

    function renderSite(list) {
        $('.form-signin.active select.site-list').html($.map(list, item => `<option value=${item.id}>${item.name}</option>`));
    }

    function refreshFormMt() {
        const $formBox = $('#form-box');
        // $formBox.css({
        //     marginTop: $formBox.height() / 2 * -1,
        // });
    }
    function checkLicenseInfo() {
        $(".form-signin.active").submit(() => {
            if (licenseError) {
                //license失效
                $('.submit-username').prop('disabled', false).removeClass('disabled');
                return false
            } else {
                return true;
            }
        });
    }
    function queryLoginRSAKey(){
        $.ajax({
            url: '/getLoginRSAKey',
            type: 'get',
            success(res) {
                if (res.error) {
                    return;
                }
                publicKey = res.pub
            },
        });
    }
    $(() => {
        queryLoginRSAKey()
        initSlideType();
        submitHandleForUsername();
        submitHandleForPhone();
        handleVcodeClick();
        createLoginTypeByCookie();
        selectTypeByCookie();
        handleError();
        recoveryState();
        handlePhoneInput();
        checkSite();
        checkLicenseInfo();
        forgotPasswordClick();
    });
});
