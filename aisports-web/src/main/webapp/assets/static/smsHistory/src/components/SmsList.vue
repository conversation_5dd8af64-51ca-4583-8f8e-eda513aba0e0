<template>
    <div class="sms-list">
        <div class="query-section">
            <select
                v-model="state"
                class="qselect">
                <option value="">选择状态</option>
                <option
                    v-for="(index, state) in smsStates"
                    :value="index.toString()">{{ mapState(index) }}</option>
            </select>
            <span class="bootstrap-timepicker timepicker" >
                <input
                    v-el:date
                    v-model="date"
                    type="text"
                    class="form-control start-time normal input-query"
                    required
                    placeholder="选择日期">
            </span>
            <input
                v-model="phoneNum"
                class="normal input-query"
                type="text"
                placeholder="手机号码">
            <button
                class="btn btn-md btn-primary"
                @click="handleQueryClick">查询</button>
        </div>
        <result-area>
            <table class="simple-splitter">
                <tbody>
                    <tr>
                        <th>提交时间</th>
                        <th>号码</th>
                        <th>短信内容</th>
                        <th>发送状态</th>
                        <th>发送进度</th>
                        <th>单条计费</th>
                        <th>操作</th>
                    </tr>
                    <tr v-for="log in smsLogs">
                        <td>{{ log.operTime }}</td>
                        <td class="numbers">{{ log.mobileNums }}</td>
                        <td class="sms-content">{{ log.smsContent }}</td>
                        <td><span :class="'g-state-' + mapStateToColor(log.state)">{{ mapState(log.state) }}</span></td>
                        <td>{{ log.sentCnt }}/{{ log.smsCnt }}</td>
                        <td>{{ log.chargeNum }}</td>
                        <td>
                            <operation-text
                                @click="handleViewClick(log.id)">查看</operation-text>
                            <operation-text
                                v-if="log.state === '0'"
                                @click="handleDelClick(log.id)">删除</operation-text>
                        </td>
                    </tr>
                </tbody>
            </table>
            <paginator
                :on-change="handlePageChange"
                :total-pages="pageInfo.totalPages"
                :current-page="pageInfo.currentPage"></paginator>
        </result-area>
    </div>
</template>

<script>
import 'bootstrapDatepicker'
import * as apis from '../apis'
import 'qselect'
import handleTipCover from 'handleTipCover'
import mAlert from 'mAlert'
import Paginator from 'vue-components/vue1/Paginator.vue'

const confirmDeleteDialog = handleTipCover.createTipCover({
    tipTitle: '确认提示',
    tipContent: '是否确认删除？',
    buttonClickHandler(data) {
        data.$container.hide()
        if ($(data.el).data('role') === 'confirm') {
            console.log('confirm')
        } else {
            console.log('cancel')
        }
    },
    cancelHandler(data) {
        data.$container.hide()
    },
})

export default {
    ready() {
        const datepickerConfig = {
            format: 'yyyy-mm-dd',
        }

        $(this.$els.date).datepicker(datepickerConfig)

        // data fetching
        apis.getStateList({}, (data) => {
            this.smsStates = data.stateList

            this.$nextTick(() => {
                $('.qselect').selectpicker({
                    size: 6,
                })
            })
            this.loadLogList()
        })
    },

    components: {
        paginator: Paginator,
    },

    data() {
        return {
            smsLogs: [],
            smsStates: [],
            phoneNum: '',
            date: '',
            // query 过滤用的审核状态
            state: '',
            pageInfo: {
                currentPage: 1,
                totalPages: 1,
            },
        }
    },

    methods: {
        handlePageChange(newPage) {
            this.loadLogList(newPage)
        },

        loadLogList(page = 1) {
            apis.getSmsLogList({
                phoneNum: this.phoneNum,
                date: this.date,
                state: this.state,
                page,
            }, (data) => {
                window.document.body.scrollTop = 0
                this.smsLogs = data.resultList
                this.pageInfo.totalPages = data.pageInfo.pages
                this.pageInfo.currentPage = data.pageInfo.pageNum
            })
        },

        handleViewClick(logId) {
            this.$dispatch('view-detail', logId)
        },

        handleDelClick(logId) {
            confirmDeleteDialog.update({
                tipTitle: '确认提示',
                tipContent: '是否确认删除？',
                buttonClickHandler: function (data) {
                    data.$container.hide()
                    if ($(data.el).data('role') === 'confirm') {
                        apis.deleteLog(logId, (data) => {
                            mAlert.showSuccess('删除成功!')
                            this.loadLogList()
                        })
                    }
                }.bind(this),
            })
            confirmDeleteDialog.showTipCover()
        },

        mapState(stateStr) {
            if (this.smsStates && this.smsStates.length !== 0) {
                return this.smsStates[parseInt(stateStr)][parseInt(stateStr)]
            }
            return '加载中...'
        },

        mapStateToColor(stateStr) {
            if (this.smsStates && this.smsStates.length !== 0) {
                switch (stateStr) {
                case '0':
                    return 'pending'
                case '1':
                    return 'passed'
                case '2':
                    return 'not-passed'
                case '3':
                    return 'exed'
                case '4':
                    return 'deleted'
                case '5':
                    return 'not-passed'
                default:
                    return 'blue'
                }
            }
        },

        handleQueryClick() {
            this.loadLogList()
        },
    },
}
</script>
