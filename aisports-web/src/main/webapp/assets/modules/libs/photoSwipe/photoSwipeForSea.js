/**
 * Created by nightost on 2017/5/12.
 */
define(function (require , exports , module ) {
    var PhotoSwipe = require ('./photoswipe');
    var PhotoSwipeUI_Default = require('./ui/photoswipe-ui-default');
    var  _ = require('underscore');
    var swiper;
    var $ = require('jquery');
    swiper = {
        index : 0,
        list : [],
        gallery : null,
        initSwiper :function(items , swiperIndex){
            var _this = this;
            this.list = items;
            this.index = swiperIndex;
            this.getItemsSize()
            this.gallery = new PhotoSwipe($('[ref=pswp]')[0], PhotoSwipeUI_Default, this.list, {
                index: this.index || 0
            });
            this.gallery.listen('gettingData', function(index, item) {
                if (item.w < 1 || item.h < 1) { // unknown size
                    var img = new Image();
                    img.onload = function() { // will get size after load
                        item.w = this.width; // set image width
                        item.h = this.height; // set image height
                        _this.gallery.invalidateCurrItems(); // reinit Items
                        _this.gallery.updateSize(true); // reinit Items
                    }
                    img.src = item.src; // let's download image
                }
            });
            this.gallery.init();
        },
        getItemsSize :function(){
            this.createImgForSize()
            this.list = _.map(this.list , function(item , index){
                var $hiddenImgs = $('.pswp-hidden-img')
                var $targetEl = $hiddenImgs.eq(index)
                return {
                    src : item.src,
                    w : $targetEl.width(),
                    h : $targetEl.height()
                }
            })
        },
        createImgForSize :function(){
            if(!this.list.length){
                return
            }
            var $conatiner = $('#pswp-hidden-img-box')
            var $html
            if(!$conatiner.length){
                $conatiner = $('<div id="pswp-hidden-img-box" style="width: 1px;height: 1px;overflow: scroll;visibility: hidden;"></div>');
                $('body').append($conatiner);
            }
            $html = $.map(this.list , function(item){
                return '<img class="pswp-hidden-img" src="' + item.src + '">'
            })
            $conatiner.html($html);
        }
    };
    module.exports = swiper;
})