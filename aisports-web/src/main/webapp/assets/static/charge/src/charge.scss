@import "../../frame/src/frame";
@import "../../common/scss/form";
@import "../../common/scss/groupTable";
@import "../../common/scss/bigTabs";
@import "../../../sea_modules/bootstrap/assets/stylesheets/bootstrap/_datepicker3";
@import "../../common/scss/daterangepickerBs3";
@import "../../../sea_modules/bootstrap-select/bootstrap-select";
@import "../../common/element-theme-xports/element-variables";
@import "../../common/scss/photoSlide/photoSlide";

.img-outer{
  margin-top: 20px;
  .title{
      color: #333333;
      font-weight: bold;
      padding-bottom: 15px;
      border-bottom: 1px solid #e4e4e4;
      margin-bottom: 20px;
  }
}
.wrapper .charge-main{
	padding: 20px;
	position: static;
	padding-bottom: 80px;
	.title-box{
		overflow: visible;
	}
	.sub-box{
      padding: 0;
      padding-bottom: 40px;
	}
}
.submit{
  @include bSubmit();
  font-size: 0;
  .btn-c{
    background-color: #5bc0de;
  }
}
.quantity-form{
	display: inline-block;
	.decrement, .increment, .quantity-text{
		background-color: #fff;
		float: left;
		width: 30px;
		text-align: center;
		border: 1px solid #c8c8c8;
		height: 30px;
		line-height: 28px;
		.iconfont{
			color: #c8c8c8;
		}
	}
	.quantity-text{
		margin: 0 -1px;
	}
}
input.other-amount{
	@extend .short; 
	width: 80px;
	text-align: center;
	padding: 0;
}
.delete-wrap{
	.icon-shanchu{
		color: #d4d4d4;
		cursor: pointer;
	}
}
[data-tabs="charge"]{
  padding: 0 20px;
}
.charge{
  padding: 10px 0;
  border-top: 1px solid #ddd;
	tbody{
		.dropdown{
			display: inline-block;
		}
	}
  textarea{
    width: 100%;
    height: 68px;
    padding:10px 0 0 13px;
    border: 1px solid #e0e0e0;
    resize: none;
  }
}
.dateSelect{
  position: relative;
  display: inline-block;
  width: (585/1100)*100%;
  margin-left: (11/1100)*100%;
  >.line-center{
    display: inline-block;
    width: (10/585)*100%;
    margin-left: (5/585)*100%;
    margin-right: (5/585)*100%;
    border-top:1px solid #ccc;
  }
  .date-plugin{
    display: inline-block;
    border: 1px solid #cccccc;
    width: 38.2%;
    height: 34px;
    vertical-align: middle;
    font-size: 14px;
    input{
      text-align: left;
      height: 28px;
      width: (101/130)*100%;
      border:0;
      padding-left: 20px;
    }
    .iconfont{
      display: inline-block;
      width: 10%;
      line-height: 32px;
      height: 32px;
      text-align: center;
      font-size: 20px;
      color: #ccc;
      background-color: #fff;
      border: 0;

    }
  }
  >.bootstrap-timepicker{
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    width: (90/585)*100%;
    margin-left: 5px;
    >input{
      width: 100%;
    }
  }
  .endDate{
    width: 373.5px;
    margin-left: 409.5px;
  }
}

// 历史工单
.fields{
  padding: 20px 20px 20px 20px;
  font-size: 0;
  .s-btn{
    margin-left: 20px;
  }
  .advance-search{
    font-size: 14px;
    vertical-align: middle;
    height: 34px;
  }
  .input-group{
    vertical-align: middle;
  }
  .add-btn-gray{
    float: right;
  }
  .el-date-editor {
    margin: 0 10px;
    vertical-align: middle;
  }
}

.date-range-select{
  display: inline-block;
  width: 220px;
  margin-left: 6px;
  .date-plugin{
    border: 1px solid #ccc;
    position: relative;
    .iconfont{
      position: absolute;
      right: 0;
      width: 34px;
      line-height: 32px;
      height: 32px;
      text-align: center;
      font-size: 18px;
      color: #ccc;
    }
    .date-rage{
      width: 100%;
      padding-right:38px;
      height: 32px;
      border: none;
      text-indent: 5px;
    }
  }
}
// 历史工单表格
.history-query-res{
  padding: 0 20px 10px;
}
.th-history-refund{
  min-width: 362px;
}
.f-line:first-child{
  margin-top: 0;
}
.refund-coverbox{
  .inner-box .content-box{
    padding-left: 0;
    .remark{
      padding: 5px 0 0 15px;
      line-height: 20px;
      height: 76px;
    }
  }
}
.table{
  margin-bottom: 0;
  thead{
    tr{
      th{
        background-color: #fafafa;
        height: 45px;
        text-align: center;
        border: none;
        padding: 0;
        vertical-align: middle;
      }
    }
  }
  tbody{
    tr{
      td{
        text-align: center;
        border: none;
        padding:8px;
      }
    }
    tr + tr{
      border-top: 1px solid #ddd;
    }
  }
}
[data-click="add-line"]{
  height: 30px;
  line-height: 30px;
  padding: 0 21px;
}
.input-group{
  width: 220px;
  margin-right: 10px;
}
.btn-primary-table{
  background-color: #1fa2f5;
  color: #fff;
  height: 35px;
  line-height: 35px;
  cursor: pointer;
  padding: 0 35px;
}
.nav{
  float: right;
}
.table-group{
  tbody{
    &:before{
      display: none;
    }
    .tr-height{
      td{
        height: 10px;
        padding: 0;
      }
    }
    .tr-content{
      border-right: 1px solid #ddd;
      border-bottom: 1px solid #ddd;
      td{
        border-left: 1px solid #ddd;
      }
      .desc-layout{
        table-layout: fixed;
        width: 200px;
      }
      .feetype{
        table-layout: fixed;
        width: 100px;
      }
    }
    tr + tr{
      border: none;
    }
  }
}
.title-desc{
  height: 46px;
  line-height: 55px;
  @include clearfix();
  .name{
    float: left;
    color: #333333;
    font-weight: bold;
  }
  .more{
    float: right;
    color: #56b854;
    cursor: pointer;
    margin-top: 12px;
    .icon-jia1{
      font-weight: bold;
    }
  }
}