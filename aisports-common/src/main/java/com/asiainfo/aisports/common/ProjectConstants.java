package com.asiainfo.aisports.common;

/**
 * <AUTHOR>
 * @date 2025/6/9 11:10
 */
public interface ProjectConstants {

    interface Attr{
        String REAL_FLAG = "real_flag";

        String POP_FLAG = "pop_flag";


        String POP_CONTENT = "pop_content";
        String NEW_FLAG = "new_flag";
    }

    interface TicketKind {
        String ADULT = "1"; // 成人票
        String GROUP = "2"; // 套票
        String AWAY= "3"; // 客场
        String OUT_ADULT= "4"; // 外地成人票
        String OUT_GROUP= "5"; // 外地套票
        String MINOR= "6"; // 儿童票
        String OUT_MINOR= "7"; // 外地儿童票
        String TEAM= "8"; // 团体票
    }

    interface RightsValid{
        String FOREVER = "1"; // 永久有效
        String DATE = "2"; // 指定日期
    }

    interface RightsState{
        String INVALID = "0";

        String VALID = "1";

        String CHECKED = "2";

        String CANCEL = "3";
    }

    interface PersonType {
        String ADULT = "1";
        String MINOR = "2";
    }

    public interface StockState {
        String FOR_SALE = "1"; // 可售
        String NOT_FOR_SALE = "2"; // 不可售
        String SALE_OUT = "3"; // 售罄
    }

    interface SeatDisType {
        String CONTINUE = "1"; // 连续
        String ODD = "2"; // 奇数
        String EVEN = "3"; // 偶数
    }

    public interface SeatState {
        String VALID = "1"; // 可用
        String INVALID = "0"; // 不可用
        String OCCUPY = "2"; // 已分配
    }

    interface TaskSeatState{
        String FAILED = "0";

        String PROCESS = "1";

        String SUCCESS = "2";
    }
}
