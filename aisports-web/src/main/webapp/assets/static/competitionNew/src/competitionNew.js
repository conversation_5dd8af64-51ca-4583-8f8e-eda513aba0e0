import Vue from 'vue'
import $ from 'jquery'
import App from './ticketConfig/App.vue'
import Router from 'vue-router'
import routes from './ticketConfig/routes'
import 'frame'
import OperationText from 'vue-components/OperationText'
import ListPage from 'vue-components/ListPage'

import '../../common/element-theme/index.css'
import {
    DatePicker, TimePicker, Switch, Select,
    Option,
    Button,
    Radio,
    Card,
    Table,
    TableColumn,
    Tabs,
    TabPane,
    Icon,
    Row,
    Col,
    Tag,
    Pagination,
    Drawer,
    Form,
    FormItem,
    InputNumber,
    Dialog
} from 'element-ui-2.15.5'

Vue.use(DatePicker)
Vue.use(TimePicker)
Vue.use(Switch)
Vue.use(Select)
Vue.use(Option)
Vue.use(Button)
Vue.use(Card)
Vue.use(Table)
Vue.use(TableColumn)
Vue.use(Tabs)
Vue.use(TabPane)
Vue.use(Icon)
Vue.use(Row)
Vue.use(Col)
Vue.use(Pagination)
Vue.use(Tag)
Vue.use(Radio)
Vue.use(Drawer)
Vue.use(Form)
Vue.use(FormItem)
Vue.use(InputNumber)
Vue.use(Dialog)
Vue.use(Router)
Vue.use(OperationText)
Vue.use(ListPage)

const router = new Router({ routes })

window.vueRouter = router
new Vue({
    el: '#comp-ticket-config',
    router,
})
