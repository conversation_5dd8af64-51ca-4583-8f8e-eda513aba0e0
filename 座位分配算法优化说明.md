# 座位分配算法优化说明

## 概述

本次优化针对 `ProjectSeatService.takeSeat()` 方法，实现了智能的座位分配算法，确保团体票、套票和单人票能够最大程度地坐在一起，特别是套票的连坐需求。

## 核心数据结构

### 票种分类
- **团体票** (`TEAM = "8"`): 按 `tradeIdB` 分组的大型团体
- **套票** (`GROUP = "2"`): 按 `ticketId` 分组的小型团体，需要尽可能坐在一起
- **单人票** (`ADULT = "1"`): 个人票

### 座位结构
- **`subRow`**: 连坐区域标识，相同 `subRow` 表示同一排的连坐座位
- **`row`**: 排号
- **`seat`**: 座位号

## 优化后的分配策略

### 1. 座位预处理
```java
// 按subRow分组座位，每个subRow代表一个连坐区域
Map<Integer, List<StockSeat>> subRowSeatMap = validSeatList
        .stream()
        .collect(Collectors.groupingBy(StockSeat::getSubRow));

// 将连坐区域按座位数量降序排列，优先分配大的连坐区域
List<List<StockSeat>> sortedSubRowSeats = subRowSeatMap.values()
        .stream()
        .sorted(Comparator.comparing(List::size, Comparator.reverseOrder()))
        .collect(Collectors.toList());
```

### 2. 团体票分配策略（简化版）
**原则**: 从最小排号和座位号开始连续分配

```java
// 获取所有可用座位并按排号、座位号排序
List<StockSeat> allAvailableSeats = getAllAvailableSeats();
allAvailableSeats.sort(Comparator.comparing(StockSeat::getRow)
        .thenComparing(StockSeat::getSeat));

// 从最小排号和座位号开始连续分配
for (List<TicketPersonForSeat> team : teamList) {
    List<StockSeat> teamSeats = allAvailableSeats.subList(0, teamSize);
    // 分配座位并从可用列表中移除
}
```

### 3. 套票分配策略（智能优化）
**原则**: 最大化连坐效果，优先在单个连坐区域内分配

#### 策略1: 单连坐区域内分配
- 在单个 `subRow` 内寻找连续座位
- 优先选择能完全容纳套票的连坐区域

#### 策略2: 同排跨区域分配
- 如果单个连坐区域无法满足，在同一排的不同连坐区域内分配
- 保持在同一排内，减少分散程度

#### 策略3: 跨排分配
- 最后选择，尽量选择相近的排号和座位号

```java
private List<StockSeat> findBestSeatsForGroup(int groupSize, List<List<StockSeat>> sortedSubRowSeats) {
    // 策略1: 优先在单个连坐区域内分配
    for (List<StockSeat> subRowSeats : sortedSubRowSeats) {
        List<StockSeat> consecutiveSeats = findConsecutiveSeats(availableSeats, groupSize);
        if (consecutiveSeats.size() >= groupSize) {
            return consecutiveSeats.subList(0, groupSize);
        }
    }
    
    // 策略2: 跨区域分配（优先相同排）
    // 策略3: 跨排分配
}
```

## 分配优先级

1. **团体票优先**: 从最小排号和座位号开始连续分配
2. **套票按人数降序**: 大套票优先分配，更容易获得连坐
3. **单人票**: 填补剩余座位

## 连续座位识别算法

```java
private List<StockSeat> findConsecutiveSeats(List<StockSeat> seats, int requiredCount) {
    for (int i = 0; i <= seats.size() - requiredCount; i++) {
        List<StockSeat> consecutive = new ArrayList<>();
        consecutive.add(seats.get(i));
        
        for (int j = i + 1; j < seats.size() && consecutive.size() < requiredCount; j++) {
            StockSeat currentSeat = seats.get(j);
            StockSeat lastSeat = consecutive.get(consecutive.size() - 1);
            
            // 检查是否连续（同排且座位号连续）
            if (currentSeat.getRow().equals(lastSeat.getRow()) && 
                currentSeat.getSeat().equals(lastSeat.getSeat() + 1)) {
                consecutive.add(currentSeat);
            } else {
                break;
            }
        }
        
        if (consecutive.size() >= requiredCount) {
            return consecutive;
        }
    }
    return new ArrayList<>();
}
```

## 优化效果

### 团体票
- ✅ 简化分配逻辑，从最小排号和座位号开始连续分配
- ✅ 保证团体成员坐在一起
- ✅ 分配效率高

### 套票
- ✅ 最大化连坐概率，优先在单个连坐区域内分配
- ✅ 三层分配策略，确保最佳座位安排
- ✅ 按人数降序分配，大套票优先获得连坐

### 系统性能
- ✅ 减少座位碎片化
- ✅ 提高座位利用率
- ✅ 详细的日志记录，便于调试和监控

## 使用示例

```java
// 调用座位分配
projectSeatService.takeSeat(performId, stockIds, staff);

// 日志输出示例
// Team ticket assignment successful, team size: 8, assigned seats: Row1Seat1, Row1Seat2, Row1Seat3, Row1Seat4, Row1Seat5, Row1Seat6, Row1Seat7, Row1Seat8
// Group ticket assignment successful, group size: 4, assigned seats: Row2Seat1, Row2Seat2, Row2Seat3, Row2Seat4
// Seat assignment completed, stock ID: 1001, assigned seats count: 25
```

## 注意事项

1. **数据一致性**: 确保 `subRow` 字段正确标识连坐区域
2. **并发控制**: 使用 Redis 锁防止并发分配冲突
3. **异常处理**: 座位不足时抛出明确的异常信息
4. **事务管理**: 整个分配过程在事务中执行，确保数据一致性
