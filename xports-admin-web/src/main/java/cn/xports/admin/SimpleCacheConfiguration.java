package cn.xports.admin;

import com.asiainfo.aisports.cache.MemCacheManager;
import com.asiainfo.aisports.cache.MemcachedConfig;
import com.asiainfo.aisports.cache.MyKeyGenerator;
import net.rubyeye.xmemcached.MemcachedClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * Created by liuchangyuan on 2018/3/8.
 */
@Configuration
public class SimpleCacheConfiguration extends CachingConfigurerSupport {
    private static final Logger LOGGER = LoggerFactory.getLogger(SimpleCacheConfiguration.class);
    @Autowired
    MemcachedConfig memcachedConfig;

    @Override
    @Bean
    public CacheManager cacheManager() {
        return new MemCacheManager(memcachedClient());
    }

    @Bean
    public MemcachedClient memcachedClient() {
        try {
            return memcachedConfig.getClientBuilder().build();
        } catch (IOException e) {
            LOGGER.error("", e);
        }
        return null;
    }

    @Override
    public KeyGenerator keyGenerator() {
        return new MyKeyGenerator();
    }
}
