package cn.xports.admin.controller;

import cn.xports.admin.service.ParamDefService;
import cn.xports.admin.service.ParamService;
import cn.xports.admin.service.PayModeService;
import cn.xports.admin.service.StaffService;
import com.asiainfo.aisports.annotation.TargetDataSource;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.StaticParamInfo;
import com.asiainfo.aisports.param.StaticParamConfig;
import com.asiainfo.aisports.service.StaticParamService;
import com.asiainfo.aisports.web.WebResult;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by liuchangyuan on 2018/4/12.
 */
@Api(tags = "系统参数")
@RestController
@RequestMapping("/param")
public class ParamController {
    @Autowired
    private StaticParamConfig staticParamConfig;
    @Autowired
    private ParamDefService paramDefService;
    @Autowired
    private ParamService paramService;
    @Autowired
    private StaffService staffService;
    @Autowired
    private PayModeService payModeService;
    @Autowired
    private StaticParamService staticParamService;

    /**
     * 获取参数集合
     *
     * @return
     */
    @ApiOperation(value = "获取静态参数列表")
    @TargetDataSource(name = "center")
    @RequestMapping(value = "/static/list/{code}", method = RequestMethod.GET)
    public WebResult staticParamList(
            @ApiParam(name = "code", value = "静态参数编码", required = true) @PathVariable String code) {
        return new WebResult().set("result", staticParamConfig.getParamList(code).stream()
                .map(p-> ImmutableMap.of("key", p.get("paramKey"), "value", p.get("paramValue")))
                .collect(Collectors.toList()));
    }

    /**
     * 获取参数集合
     *
     * @return
     */
    @ApiOperation(value = "获取静态参数列表")
    @TargetDataSource(name = "center")
    @RequestMapping(value = "/findStaticParams", method = RequestMethod.GET)
    public WebResult findStaticParams(
            @ApiParam(name = "paramCode", value = "静态参数编码", required = true) @RequestParam String paramCode) {
        return new WebResult().set("result", staticParamConfig.getParamList(paramCode).stream()
                .map(p-> ImmutableMap.of("key", p.get("paramKey"), "value", p.get("paramValue")))
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "获取静态参数Map")
    @TargetDataSource(name = "center")
    @RequestMapping(value = "/static/map/{code}", method = RequestMethod.GET)
    public WebResult staticParamMap(
            @ApiParam(name = "code", value = "静态参数编码", required = true) @PathVariable String code) {
        return new WebResult().set("result", staticParamConfig.getParamMap(code));
    }

    @ApiOperation(value = "获取参数定义")
    @RequestMapping(value = "/def/list", method = RequestMethod.GET)
    public WebResult findParamDef(@RequestParam String paramType,
                                  @RequestParam String paramLevel,
                                  @RequestParam(required = false) String editable) {
        return new WebResult().set("result", paramDefService.find(paramType, paramLevel, editable));
    }

    @ApiOperation(value = "获取中心列表")
    @RequestMapping(value = "/centers", method = RequestMethod.GET)
    public WebResult findCenters(Long tenantId) {
        return new WebResult().set("result", paramService.findCenters(tenantId));
    }

    @ApiOperation(value = "获取场馆列表")
    @RequestMapping(value = "/venues", method = RequestMethod.GET)
    public WebResult findVenues(Long centerId, String venueType) {
        return new WebResult().set("result", paramService.findVenues(centerId, venueType));
    }

    @ApiOperation(value = "获取员工列表")
    @RequestMapping(value = "/findStaffList", method = RequestMethod.GET)
    public WebResult findStaffList(Long centerId) {
        return new WebResult().set("result", staffService.findStaffList(centerId));
    }

    @ApiOperation(value = "获取自定义支付方式列表")
    @RequestMapping(value = "/findCustomerPayModeList", method = RequestMethod.GET)
    public WebResult findCustomerPayModeList() {
        return new WebResult().set("result", payModeService.findCustomerPayModeList());
    }

    @RequestMapping(value = "/findGroupedStaticParam", method = RequestMethod.GET)
    public WebResult findGroupedStaticParam(
            @ApiParam(name = "paramCode", value = "静态参数编码", required = true) @RequestParam String paramCode) {

        List<StaticParamInfo> list = staticParamService.findSortedParams(paramCode);
        List<DataMap> result = Lists.newArrayList();
        for (StaticParamInfo param : list) {
            result.add(new DataMap().set("label", param.getValueName())
                    .set("value", param.getAttrValue())
                    .set("children", param.getSubStaticParamList().stream()
                            .map(p -> new DataMap().set("label", p.getValueName()).set("value", p.getAttrValue()))
                            .collect(Collectors.toList())));
        }
        return new WebResult().set("result", result);
    }
}
