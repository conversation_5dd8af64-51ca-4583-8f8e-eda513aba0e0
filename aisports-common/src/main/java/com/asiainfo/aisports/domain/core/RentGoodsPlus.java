package com.asiainfo.aisports.domain.core;

/**
 * Created by f<PERSON><PERSON><PERSON> on 2015/3/18.
 */
public class RentGoodsPlus extends RentGoods{
    private String goodsTypeName;

    private String brandName;

    public String getGoodsTypeName() {
        return goodsTypeName;
    }

    public void setGoodsTypeName(String goodsTypeName) {
        this.goodsTypeName = goodsTypeName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", goodsTypeName=").append(goodsTypeName);
        sb.append(", brandName=").append(brandName);
        sb.append("]");
        return sb.toString();
    }
}
