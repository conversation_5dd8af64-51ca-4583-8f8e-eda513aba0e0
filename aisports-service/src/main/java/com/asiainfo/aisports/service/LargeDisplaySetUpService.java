package com.asiainfo.aisports.service;

import cn.hutool.http.HttpUtil;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.SubVenue;
import com.asiainfo.aisports.domain.core.SubVenueAttr;
import com.asiainfo.aisports.domain.core.SubVenueAttrKey;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.param.StaticParamConfig;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.MediaContentMapper;
import com.asiainfo.aisports.persistence.core.SubVenueAttrMapper;
import com.asiainfo.aisports.persistence.core.SubVenueMapper;
import com.asiainfo.aisports.persistence.core.VenueMapper;
import com.asiainfo.aisports.tools.JSONObjectUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by wangdd on 16/4/11.
 */
@Service
public class LargeDisplaySetUpService {
    @Autowired
    SubVenueMapper subVenueMapper;
    @Autowired
    VenueMapper venueMapper;
    @Autowired
    MediaContentMapper mediaContentMapper;
    @Autowired
    SubVenueAttrMapper subVenueAttrMapper;
    @Autowired
    StaticParamConfig staticParamConfig;
    @Autowired
    VenueParamConfig venueParamConfig;
    @Autowired
    SequenceWrapper sequenceWrapper;

    /**
     * 根据venueId获取tab签的具体内容项
     *
     * @param venueId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> getVenueSubInfo(Long venueId) {
        List<Map<String, Object>> resultList = Lists.newArrayList();
        List<Map<String, Object>> subVenueList = subVenueMapper.selectByVenueId(venueId);
        if (!subVenueList.isEmpty()) {
            resultList.addAll(subVenueList);
        } else {
            Map<String, Object> map = venueMapper.selectForLargeDisplay(venueId);
            resultList.add(map);
        }
        return resultList;
    }

    /**
     * 获取该场馆具体的媒体内容
     *
     * @param venueId
     * @param subVenueId
     * @return
     */
    @Transactional(readOnly = true)
    public List getMediaContent(Long venueId, Long subVenueId) {
        //获取数据
        List<Map<String, Object>> mediaContentList = mediaContentMapper.getMediaContent(venueId, subVenueId);

        JSONObject waterInfo = getWaterInfo(subVenueId);
        // 修改数据
        if (waterInfo != null) {
            mediaContentList.forEach(map -> {
                String contCode = MapUtils.getString(map, "contCode");
                switch (contCode) {
                    case "temperature":
                        map.put("value", waterInfo.getString("WaterTemp"));
                        break;
                    case "ph":
                        map.put("value", waterInfo.getString("PH"));
                        break;
                    case "residue_chlorine":
                        map.put("value", waterInfo.getString("ResidualChlorine"));
                        break;
                    default:
                        break;
                }
            });
        }

        //定义及初始化
        List resultList = Lists.newArrayList(); //结果集合
        List list = Lists.newArrayList(); //分类中间集合
        Map theMap; //分类中间map
        String contCat = "初始化";

        //遍历集合进行分类
        if (!mediaContentList.isEmpty()) {
            for (Map map : mediaContentList) {
                String mapContCat = (String) map.get("contCat");

                //如果是馆下面的泳池 替换掉 cont_cat=2 的配置
                if ("2".equals(mapContCat)) {
                    String contCode = MapUtils.getString(map, "contCode");
                    map.clear();
                    map.putAll(mediaContentMapper.getSubVenueMediaContent(venueId,subVenueId,contCode));
                }

                if (!contCat.equals(mapContCat)) {
                    if (!list.isEmpty()) {
                        //处理相同数据集合
                        theMap = Maps.newHashMap();
                        theMap.put("name", staticParamConfig.getName(Constants.StaticParam.CONT_CAT, contCat));
                        theMap.put("data", list);
                        resultList.add(theMap);
                        list = Lists.newArrayList();
                    }
                    //新的初始化
                    contCat = mapContCat;
                }
                list.add(map);
            }

            //循环结束再进行一次数据装载
            if (!list.isEmpty()) {
                //处理相同数据集合
                theMap = Maps.newHashMap();
                theMap.put("name", staticParamConfig.getName(Constants.StaticParam.CONT_CAT, contCat));
                theMap.put("data", list);
                resultList.add(theMap);
            }

        }
        return resultList;
    }

    /**
     * 大屏配置数据的修改或插入
     *
     * @param subVenueId
     * @param contCode
     * @param value
     * @return
     */
    @Transactional
    public int updateValue(Long subVenueId, String contCode, String value, Long staffId) {

        int changeNum;
        SubVenueAttrKey key = new SubVenueAttrKey();
        key.setAttrCode(contCode);
        key.setSubVenueId(subVenueId);
        SubVenueAttr subVenueAttr = subVenueAttrMapper.selectByPrimaryKey(key);
        if (subVenueAttr != null) {
            subVenueAttr.setAttrValue(value);
            subVenueAttr.setUpdateStaffId(staffId);
            subVenueAttr.setUpdateTime(new Date());
            changeNum = subVenueAttrMapper.updateByPrimaryKeySelective(subVenueAttr);
        } else {
            subVenueAttr = new SubVenueAttr();
            subVenueAttr.setSubVenueId(subVenueId);
            subVenueAttr.setAttrCode(contCode);
            subVenueAttr.setAttrValue(value);
            subVenueAttr.setUpdateStaffId(staffId);
            subVenueAttr.setCreateTime(new Date());
            changeNum = subVenueAttrMapper.insert(subVenueAttr);
        }

        return changeNum;
    }

    /**
     * 通过接口获取
     * 水温
     * ph值
     * 余氯
     *
     * @return
     */
    private JSONObject getWaterInfo(Long venueId) {
        String url = venueParamConfig.getParam(venueId, Constants.VenueParam.GET_WATER_INFO_BY_URL);
        String body = venueParamConfig.getParam(venueId, Constants.VenueParam.GET_WATER_INFO_BODY);

        if (Strings.isNullOrEmpty(url) || Strings.isNullOrEmpty(body)) {
            return null;
        }

        String result = HttpUtil.createPost(url)
                .contentType("application/json")
                .body(body)
                .execute().body();

        JSONObject data = null;

        try {
            data = JSONObjectUtils.parseObject(result).getJSONObject("Data");
        } catch (Exception e) {
            data = null;
        }
        return data;
    }

    public void saveOrUpdateSubVenue(Integer type, Long subVenueId, String subVenueName, String sort, String remark, LoginStaff staff) {
        if (type != null) {
            SubVenue subVenue = subVenueMapper.selectByPrimaryKey(subVenueId);
            if (subVenue == null) return;

            if (StringUtils.isNotEmpty(subVenueName)) subVenue.setSubVenueName(subVenueName);
            if (StringUtils.isNotEmpty(sort)) subVenue.setSort(sort);
            if (StringUtils.isNotEmpty(remark)) subVenue.setRemark(remark);
            subVenueMapper.updateByPrimaryKeySelective(subVenue);
        } else {
            Long subVenueID = sequenceWrapper.subVenueId();
            SubVenue subVenue = new SubVenue();
            subVenue.setParentSubVenueId(subVenueId);
            subVenue.setSort(sort);
            subVenue.setVenueId(staff.getVenueId());
            subVenue.setSubVenueName(subVenueName);
            subVenue.setRemark(remark);
            subVenue.setSubVenueId(subVenueID);
            subVenueMapper.insert(subVenue);

            //插入venue_media_cont
            List<String> venueMediaContList = subVenueMapper.selectVenueMediaCont(staff.getVenueId(), subVenueId);
            if (CollectionUtils.isNotEmpty(venueMediaContList)){
                subVenueMapper.bathInsertVenueMediaCont(staff.getVenueId(), subVenueID, venueMediaContList);
            }

        }
    }

    public List<Map> getChildVenueSubInfo(Long subVenueId) {
        return subVenueMapper.selectByParentSubVenueId(subVenueId);
    }

    public void delete(Long subVenueId) {
        subVenueMapper.deleteByPrimaryKey(subVenueId);
    }

    public List<Long> getSubVenueIdByVenueId(Long venueId) {
        return subVenueMapper.getSubVenueIdByVenueId(venueId);
    }
}
