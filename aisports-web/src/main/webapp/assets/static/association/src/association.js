import Vue from 'vue'
import $ from 'jquery'
import 'frame'
import VueRouter from 'vue-router'
import routes from './router'
// import '../../common/element-theme/index.css'
// import store from './vuex/store'
import {Select, Option, Input, Button, Table,Tabs, TabPane, TableColumn, Pagination, Form, FormItem, Dialog, Message, Upload, DatePicker, Autocomplete, Radio, RadioGroup, Checkbox, CheckboxButton, CheckboxGroup,} from 'element-ui'
// import Viewer from 'v-viewer'
// import 'viewerjs/dist/viewer.css'

Vue.prototype.$message = Message;
// Vue.prototype.$dispatch = (...value) => store.dispatch(...value);
// Vue.prototype.$commit = (...value) => store.commit(...value);
Vue.use(VueRouter)
Vue.use(Select);
Vue.use(Option);
Vue.use(Input);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Button);
Vue.use(Table);
Vue.use(Tabs);
Vue.use(TabPane);
Vue.use(TableColumn);
Vue.use(Pagination);
Vue.use(Dialog);
Vue.use(DatePicker);
Vue.use(Upload);
Vue.use(Autocomplete);
Vue.use(Radio);
Vue.use(RadioGroup);
Vue.use(Checkbox);
Vue.use(CheckboxButton);
Vue.use(CheckboxGroup);

// Viewer.setDefaults({
//   Options: { "inline": true, "button": true, "navbar": true, "title": true, "toolbar": true, "tooltip": true, "movable": true, "zoomable": true, "rotatable": true, "scalable": true, "transition": true, "fullscreen": true, "keyboard": true, "url": "data-source" }
// });

const router = new VueRouter({
  routes,
})

window.vueRouter = router

new Vue({
  el: '#association',
  router,
})
