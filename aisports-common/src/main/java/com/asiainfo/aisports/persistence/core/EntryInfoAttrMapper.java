package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.EntryInfoAttr;
import com.asiainfo.aisports.domain.core.EntryInfoAttrKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface EntryInfoAttrMapper {
    @Delete({
        "delete from entry_info_attr",
        "where ticket_id = #{ticketId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    int deleteByPrimaryKey(EntryInfoAttrKey key);

    @Insert({
        "insert into entry_info_attr (ticket_id, attr_code, ",
        "attr_value, update_time)",
        "values (#{ticketId,jdbcType=BIGINT}, #{attrCode,jdbcType=VARCHAR}, ",
        "#{attrValue,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})"
    })
    int insert(EntryInfoAttr record);

    @Select({
        "select",
        "ticket_id, attr_code, attr_value, update_time",
        "from entry_info_attr",
        "where ticket_id = #{ticketId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    EntryInfoAttr selectByPrimaryKey(EntryInfoAttrKey key);

    @Select({
            "select",
            "a.key_no",
            "from key_info a left join entry_info_attr b on a.key_id = b.attr_value",
            "where b.ticket_id = #{ticketId,jdbcType=BIGINT}",
            "and b.attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column="key_no", property="keyNo", jdbcType=JdbcType.VARCHAR)
    })
    String selectKeyNo(EntryInfoAttrKey key);

    @Update({
        "update entry_info_attr",
        "set attr_value = #{attrValue,jdbcType=VARCHAR},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
        "where ticket_id = #{ticketId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}"
    })
    int updateByPrimaryKey(EntryInfoAttr record);

    @Select({
        "<script>",
        "select",
        "ticket_id, attr_code, attr_value, update_time",
        "from entry_info_attr",
        "<where>",
        "<if test=\"ticketId != null\">and ticket_id = #{ticketId,jdbcType=BIGINT}</if>",
        "<if test=\"attrCode != null\">and attr_code = #{attrCode,jdbcType=VARCHAR}</if>",
        "<if test=\"attrValue != null\">and attr_value = #{attrValue,jdbcType=VARCHAR}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
        @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<EntryInfoAttr> selectByFields(EntryInfoAttr param);

    @Update({
        "<script>",
        "update entry_info_attr",
        "<set>",
        "<if test=\"attrValue != null\">attr_value = #{attrValue,jdbcType=VARCHAR},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "</set>",
        "where ticket_id = #{ticketId,jdbcType=BIGINT}",
          "and attr_code = #{attrCode,jdbcType=VARCHAR}",
        "</script>"
    })
    int updateByPrimaryKeySelective(EntryInfoAttr record);

    @Insert({
            "<script>",
            "insert into entry_info_attr (ticket_id, attr_code,attr_value, update_time) ",
            "values",
            "<foreach collection=\"entryInfoAttrList\" item=\"item\" index=\"index\" separator=\",\">",
            "(#{item.ticketId,jdbcType=BIGINT}, #{item.attrCode,jdbcType=VARCHAR}, ",
            "#{item.attrValue,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP})",
            "</foreach>",
            "</script>"
    })
    int batchInsert(@Param("entryInfoAttrList") List<EntryInfoAttr> entryInfoAttrList);

    @Insert({
            "replace into entry_info_attr (ticket_id, attr_code, ",
            "attr_value, update_time)",
            "values (#{ticketId,jdbcType=BIGINT}, #{attrCode,jdbcType=VARCHAR}, ",
            "#{attrValue,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})"
    })
    int replace(EntryInfoAttr record);

    @Select({
            "<script>",
            "select",
            "ticket_id, attr_code, attr_value, update_time",
            "from entry_info_attr",
            "<where>",
            "attr_code = #{attrCode, jdbcType=VARCHAR} and attr_value = #{attrValue, jdbcType=VARCHAR}",
            "</where>",
            "order by update_time desc ",
            "limit 1",
            "</script>"
    })
    @Results({
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="attr_code", property="attrCode", jdbcType=JdbcType.VARCHAR, id=true),
            @Result(column="attr_value", property="attrValue", jdbcType=JdbcType.VARCHAR),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    EntryInfoAttr selectHealthAgreementEntryInfo(EntryInfoAttr param);

    @Insert({
            "<script>",
            "replace into entry_info_attr (ticket_id, attr_code,attr_value, update_time) ",
            "values",
            "<foreach collection=\"entryInfoAttrList\" item=\"item\" index=\"index\" separator=\",\">",
            "(#{item.ticketId,jdbcType=BIGINT}, #{item.attrCode,jdbcType=VARCHAR}, ",
            "#{item.attrValue,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP})",
            "</foreach>",
            "</script>"
    })
    int batchReplace(@Param("entryInfoAttrList") List<EntryInfoAttr> entryInfoAttrList);
}