<template>
    <div class="match-result-manage-page">
        <div class="title-box">
            <h1 style="display:block">
                <span style="font: 20px bold;">赛事任务成绩详情</span>
                <span class="btn-return" @click="backToList">
                    <i class="iconfont icon-zuojiantou"></i>
                </span>
            </h1>
        </div>
        <div class="body">
            <div class="steps">
                <div
                    class="public-step"
                    :class="{'current': currentRound == step.roundNum, 'finished': step.status !== '未开始' && step.status !== '未发布' }"
                    v-for="(step, index) in stepList"
                    :key="index"
                    @click="chooseCurrentStep(step)">
                    <div class="step-number">{{ step.round }}</div>
                    <div class="step-status">{{ step.status }}</div>
                </div>
            </div>
            <div v-if="!hasRankList" class="operation-box">
                <div class="left-info">
                    <i class="iconfont icon-renwu" style="color: #1FA2F5;margin-right: 10px"></i>
                    <div>对阵双方已匹配完成，点击发布即可推送至选手</div>
                </div>
                <div class="right-btns">
                    <!-- <div v-if="currentStatus === '进行中'" class="normal-btns">
                        <button v-show="!isEditResult" class="btn btn-default" @click="handleEditResult">
                            <i class="iconfont icon-bianji"></i>
                            编辑成绩
                        </button>
                        <button v-show="isEditResult" class="btn btn-default" @click="handleSaveEditResult">
                            保存
                        </button>
                        <button v-show="isEditResult" class="btn btn-default" @click="handleCancelEditResult">
                            取消
                        </button>
                    </div> -->
                    <div v-if="currentStatus === '待发布'" class="edit-btns">
                        <button v-show="!isEditRound" class="btn btn-default" @click="handleEditRound">
                            编辑对阵
                        </button>
                        <button v-show="!isEditRound" class="btn btn-default" @click="handleReleaseRound">
                            发布对阵
                        </button>
                        <button v-show="isEditRound" class="btn btn-default" @click="handleSaveEditRound">
                            保存
                        </button>
                        <button v-show="isEditRound" class="btn btn-default" @click="handleCancelEditRound">
                            取消
                        </button>
                    </div>
                </div>
            </div>
            <div v-if="!hasRankList" class="result-table">
                <el-table
                    :data="roundList"
                    border>
                    <el-table-column
                        fixed
                        type="index"
                        label="序号"
                        width="70">
                    </el-table-column>
                    <el-table-column
                        fixed
                        prop="date"
                        label="对阵双方"
                        width="360">
                        <el-table-column
                            fixed
                            label="主场"
                            width="185">
                            <template slot-scope="scope" style="position: relative">
                                <div v-if="!scope.row.editHostPlayer">
                                    <div>{{ scope.row.hostName }}</div>
                                    <div v-if="scope.row.hostName" style="font-size: 12px;color: #999;">{{ scope.row.hostWinNum ? scope.row.hostWinNum : '无' }}胜场</div>
                                    <div v-if="isEditRound" class="delete-btn" @click="editPlayer(scope.row, 'host')">
                                        <i class="iconfont icon-cha-thin-small"></i>
                                    </div>
                                </div>
                                <div v-else>
                                    <el-select
                                        v-model="scope.row.hostName"
                                        style="width: auto"
                                        size="medium"
                                        clearable
                                        placeholder="请选择上架状态"
                                        @change="changeHostTeam($event, scope.row)">
                                        <el-option
                                            v-for="item in teamList"
                                            :key="item.id"
                                            :label="item.team"
                                            :value="item.team">
                                        </el-option>
                                    </el-select>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column
                            fixed
                            label="客场"
                            width="185">
                            <template slot-scope="scope" style="position: relative">
                                <div v-if="!scope.row.editVisiterPlayer">
                                    <div>{{ scope.row.visitorName }}</div>
                                    <div style="font-size: 12px;color: #999;">{{ scope.row.visitorWinNum ? scope.row.visitorWinNum : '无' }}胜场</div>
                                    <div v-if="isEditRound" class="delete-btn" @click="editPlayer(scope.row, 'visitor')">
                                        <i class="iconfont icon-cha-thin-small"></i>
                                    </div>
                                </div>
                                <div v-else>
                                    <el-select
                                        v-model="scope.row.visitorName"
                                        style="width: auto"
                                        size="medium"
                                        clearable
                                        placeholder="请选择上架状态"
                                        @change="changeVisitorTeam($event, scope.row)">
                                        <el-option
                                            v-for="item in teamList"
                                            :key="item.id"
                                            :label="item.team"
                                            :value="item.team">
                                        </el-option>
                                    </el-select>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column
                        prop="date"
                        label="对阵结果">
                        <el-table-column
                            label="主场  VS  客场"
                            width="240">
                            <template slot-scope="scope">
                                <div v-if="(scope.row.hostScore || scope.row.hostScore == 0) && !scope.row.isEditRow">
                                    {{ scope.row.hostScore }} : {{ scope.row.visitorScore }}
                                </div>
                                <div v-if="scope.row.isEditRow" style="display: flex;justify-content: center;align-items: center">
                                    <el-input
                                        v-model="scope.row.matchHostResult" 
                                        max="3"
                                        placeholder="" 
                                        style="width: 60px"></el-input>
                                    <div style="margin: 0 10px;">:</div>
                                    <el-input v-model="scope.row.matchVisitorResult" placeholder="" style="width: 60px"></el-input>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column
                        label="对战详情">
                        <el-table-column
                            v-for="(item, index) in roundNumList"
                            :key="index"
                            :label="item"
                            min-width="200">
                            <template slot-scope="scope">
                                <div v-if="scope.row.handleRoundScoreList[index] && !scope.row.isEditRow">
                                    {{ scope.row.handleRoundScoreList[index].hostScore }} : {{ scope.row.handleRoundScoreList[index].visitorScore }}
                                </div>
                                <div v-if="scope.row.isEditRow" style="display: flex;justify-content: center;align-items: center">
                                    <el-input
                                        v-model="scope.row.matchHostDetail[index]" 
                                        max="3"
                                        placeholder="" 
                                        style="width: 60px"></el-input>
                                    <div style="margin: 0 10px;">:</div>
                                    <el-input v-model="scope.row.matchVisitorDetail[index]" placeholder="" style="width: 60px"></el-input>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column
                        v-if="currentStatus === '已结束'"
                        show-overflow-tooltip
                        width="200"
                        label="场地">
                        <template v-if="currentStatus === '已结束'" slot-scope="scope">
                            <div>
                                {{ scope.row.playingAddress }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column
                        v-if="currentStatus === '已结束'"
                        label="时间"
                        width="200">
                        <template v-if="currentStatus === '已结束'" slot-scope="scope">
                            <div>
                                {{ scope.row.playingDate }}
                            </div>
                        </template>
                    </el-table-column>
                    <div v-if="currentStatus === '进行中'">
                        <el-table-column
                            fixed="right"
                            label="操作"
                            width="120">
                            <template slot-scope="scope">
                                <div v-if="!scope.row.isEditRow" class="edit-row-btn">
                                    <span class="blue-underline-text" @click="handleChangeRowState(scope.row)">编辑</span>
                                </div>
                                <div v-else class="save-row-btn">
                                    <span class="blue-underline-text" @click="handleChangeRowState(scope.row)">取消</span>
                                    <span class="blue-underline-text" style="margin-left: 10px" @click="handleEditMatchResult(scope.row)">保存</span>
                                </div>
                            </template>
                        </el-table-column>
                    </div>
                </el-table>
            </div>
            <div v-if="hasRankList" class="rank-list">
                <table class="simple-splitter">
                    <thead>
                    <tr>
                        <th>名次</th>
                        <th>用户</th>
                        <th>总积分</th>
                        <th>净胜分</th>
                    </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(item, index) in rankList" :key="index">
                            <td>
                                <div v-if="index == 0">
                                    <img src='/assets/release/static/images/matchManage/golden.png'>
                                </div>
                                <div v-else-if="index == 1">
                                    <img src='/assets/release/static/images/matchManage/silver.png'>
                                </div>
                                <div v-else-if="index == 2">
                                    <img src='/assets/release/static/images/matchManage/brozen.png'>
                                </div>
                                <div v-else>{{ index + 1 }}</div>
                            </td>
                            <td>{{ item.name }}</td>
                            <td>{{ item.integral }}</td>
                            <td>{{ item.points }}</td>
                        </tr>
                    </tbody>
                </table>
                <div style="margin-top: 20px">
                    <pagination
                        :page-size="pageSize"
                        :total="total"
                        :current-page="pageNo"
                        @changepagesize="handleChangePageSize"
                        @changepage="handleChangePage"
                    >
                    </pagination>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import dayjs from 'dayjs';
import * as API from './api';
import mAlert from 'mAlert';
import "utils/confirm";
import { Input } from 'element-ui';
import Pagination from "vue-components/Pagination.vue";

export default {
    components: {
        Pagination,
        "el-table": () => import("element-ui").then(({ Table }) => Table),
        "el-table-column": () => import("element-ui").then(({ TableColumn }) => TableColumn),
        "el-select": () => import("element-ui").then(({ Select }) => Select),
        "el-option": () => import("element-ui").then(({ Option }) => Option),
        'el-input': Input,
    },
    data() {
        return {
            campId: '',
            teamList: [], // 队伍列表
            stepList: [], // 步骤条
            rankList: [], // 成绩排名
            totalRounds: 0, // 总轮次
            currentRound: 0, // 当前轮次
            currentStatus: '', // 当前轮次状态
            roundNumObj: {
                '1': '第一轮',
                '2': '第二轮',
                '3': '第三轮',
                '4': '第四轮',
                '5': '第五轮',
                '6': '第六轮',
                '7': '第七轮',
                '8': '第八轮',
                '9': '第九轮',
            },
            romanNum: {
                '1': '第Ⅰ轮',
                '2': '第Ⅱ轮',
                '3': '第Ⅲ轮',
                '4': '第Ⅳ轮',
                '5': '第Ⅴ轮',
                '6': '第Ⅵ轮',
                '7': '第Ⅶ轮',
                '8': '第Ⅷ轮',
                '9': '第Ⅸ轮',
            },
            gameStateObj: {
                '1': '进行中',
                '2': '已结束',
                '3': '待发布'
            },
            bestOfGames: 0, // 对阵详情的总轮次 
            groupedMatchRoundList: [], // 接口获取的对阵列表
            roundList: [], // 处理过的，用于展示的对阵列表
            roundNumList: [], // 对阵详情轮次
            roundDetailList: [], // 对阵详情列表
            showEditBtn: false, // 行内编辑成绩是否展示
            isEditRound: false, // 是否正在编辑对阵信息
            isEditResult: false, // 是否正在编辑成绩
            total: 0,
            pageNo: 1,
            pageSize: 10,
        }
    },
    computed: {
        hasRankList() {
            if (this.stepList[this.currentRound - 1]) {
                return this.stepList[this.currentRound - 1].round === '最终排名'
            }
        }
    },
    watch: {
        currentRound() {
            this.currentStatus = this.stepList[this.currentRound - 1] ? this.stepList[this.currentRound - 1].status : '';
        },
        // groupedMatchRoundList() {
        //     if (this.groupedMatchRoundList.length > 0) {
        //         this.handleRoundList();
        //     }
        // }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            this.campId = this.$route.query.campId;
            this.getMatchRoundList()
        },
        reset() {
            console.log('reset')
            this.showEditBtn = false;
            this.isEditRound = false;
            this.isEditResult = false;
            this.roundList = JSON.parse(JSON.stringify(this.roundList));
        },
        // 获取对阵轮次
        getMatchRoundList() {
            API.getMatchRoundListByCampId({campId: this.campId}, (res) => {
                this.totalRounds = res.campRounds;
                this.stepList = res.groupedMatchRoundList.map(i => ({
                    roundNum: i.round,
                    round: this.roundNumObj[i.round],
                    status: i.roundList[0].gameState ? this.gameStateObj[i.roundList[0].gameState] : '未开始',
                }));
                this.currentRound = res.groupedMatchRoundList.length;
                this.groupedMatchRoundList = res.groupedMatchRoundList;
                this.bestOfGames = res.bestOfGames;
                if (this.groupedMatchRoundList.length > 0) {
                    this.handleRoundList();
                }
                this.getFinalRank()
            })
        },
        // 处理显示的对阵信息 写得脑子一团浆糊,代码冗余请见谅
        handleRoundList() {
            // 当前轮次大于总轮次说明是选中最终排名
            let idx = this.currentRound - 1;
            // let idx = this.currentStatus === '已结束' ? this.currentRound - 1 : this.currentRound - 2;
            // if (this.groupedMatchRoundList.length = 1) {
            //     idx = 0
            // }
            this.roundList = this.groupedMatchRoundList[idx].roundList
            this.roundList.forEach(i => {
                i.isEditRow = false;
                i.matchHostResult = '';
                i.matchVisitorResult = '';
                i.matchHostDetail = [];
                i.matchVisitorDetail = [];
                i.handleRoundScoreList = [];
                i.roundScoreList.forEach(j => {
                    for (let k = 0;k < i.roundScoreList.length / 2;k ++) {
                        let scorePlayRound;
                        let hostScore;
                        let visitorScore;
                        if (j.playRound == k + 1 && j.teamTag === 'H') {
                            scorePlayRound = j.playRound,
                            hostScore = j.score
                            if (i.handleRoundScoreList[k]) {
                                i.handleRoundScoreList[k].playRound = scorePlayRound;
                                i.handleRoundScoreList[k].hostScore = hostScore;
                            } else {
                                i.handleRoundScoreList.push({
                                    playRound: scorePlayRound,
                                    hostScore: hostScore,
                                })
                            }
                        } else if (j.playRound == k + 1 && j.teamTag === 'V') {
                            scorePlayRound = j.playRound,
                            visitorScore = j.score
                            if (i.handleRoundScoreList[k]) {
                                i.handleRoundScoreList[k].playRound = scorePlayRound;
                                i.handleRoundScoreList[k].visitorScore = visitorScore;
                            } else {
                                i.handleRoundScoreList.push({
                                    playRound: scorePlayRound,
                                    visitorScore: visitorScore
                                })
                            }
                        }
                    }
                })
            });
            this.roundNumList = [];
            for(let k = 0;k < this.bestOfGames;k ++) {
                this.roundNumList.push(this.romanNum[k + 1])
                // this.roundDetailList = this.roundList[k].roundScoreList.map(i => ({
                //     round: i.playRound,
                //     score: i.score,
                //     teamTag: i.teamTag
                // }))
            };
        },
        // 获取队伍列表
        getTeamList() {
            API.getTeamsByCampId({
                campId: this.campId,
                round: this.currentRound
            }, (res) => {
                this.teamList = Object.keys(res.teams).map(i => ({id: i, team: res.teams[i]}))
                console.log('this.teamList', this.teamList)
                setTimeout(() => {
                    this.roundList = JSON.parse(JSON.stringify(this.roundList));
                }, 500)
            }) 
        },
        // 获取最终排名
        getFinalRank() {
            API.getRankList({campId: this.campId}, (res) => {
                this.rankList = res.rankList;
                if (this.stepList.filter(i => i.round === '最终排名').length > 0) {
                    return
                }
                if (!res.rankList) {
                    this.stepList.push({
                        round: '最终排名',
                        status: '未发布',
                        roundNum: this.stepList.length + 1
                    })
                    if (this.stepList.length > 1 && this.stepList.slice(-2,-1)[0].status === '已结束')
                    this.currentRound--
                } else {
                    this.stepList.push({
                        round: '最终排名',
                        status: '已发布',
                        roundNum: this.stepList.length + 1
                    });
                };
            })
        },
        chooseCurrentStep(step) {
            if (step.status === '未开始' || step.status === '未发布') {
                return
            }
            this.currentRound = step.roundNum;
            this.stepList = JSON.parse(JSON.stringify(this.stepList));
            this.roundList = JSON.parse(JSON.stringify(this.roundList));
            this.handleRoundList();
        },
        // 编辑成绩
        handleEditResult() {
            this.showEditBtn = true;
            this.isEditResult = true;
        },
        // // 保存编辑成绩
        // handleSaveEditResult() {
        //     this.$confirm(`确定修改成绩吗?` , "提示").then(() => {
                
        //     })
        // },
        // 取消编辑成绩
        handleCancelEditResult() {
            this.reset()
        },
        // 保存编辑对阵
        handleSaveEditRound() {
            let formatRoundList = this.roundList.map(i => ({
                hostTeamId: i.hostTeamId,
                hostName: i.hostName,
                visitorName: i.visitorName,
                visitorTeamId: i.visitorTeamId,
            }))
            this.$confirm(`确定修改对阵吗?` , "提示").then(() => {
                API.setMatchInfo({
                    campId: this.campId,
                    round: this.currentRound,
                    groupedMatchRoundList: JSON.stringify(formatRoundList)
                }, (res) => {
                    if (res.error === 0) {
                        this.init()
                    }
                })
            })
        },
        changeHostTeam(e, row) {
            console.log('e', e)
            row.hostTeamId = parseInt(this.teamList.filter(i => i.team === e)[0].id)
        },
        changeVisitorTeam(e, row) {
            console.log('e', e)
            row.visitorTeamId = parseInt(this.teamList.filter(i => i.team === e)[0].id)
        },
        // 取消编辑对阵信息
        handleCancelEditRound() {
            this.reset();
            this.roundList.forEach(i => {
                i.editHostPlayer = false;
                i.editVisiterPlayer = false
            })
        },
        // 编辑对阵信息
        handleEditRound() {
            this.isEditRound = true;
            this.roundList = JSON.parse(JSON.stringify(this.roundList));
        },
        // 发布对阵信息
        handleReleaseRound() {
            API.matchInfoRelease({
                campId: this.campId,
                round: this.currentRound
            }, (res) => {
                if (res.error == 0) {
                    mAlert.showSuccess("发布成功");
                    this.getMatchRoundList();
                } else mAlert.showWrong("发布失败");
                this.getMatchRoundList()
            })
        },
        // 点叉号修改对阵
        editPlayer(row, type) {
            if (type === 'host') {
                row.editHostPlayer = true
            } else {
                row.editVisiterPlayer = true
            }
            this.roundList = JSON.parse(JSON.stringify(this.roundList));
            this.getTeamList()
        },
        // 编辑行
        handleChangeRowState(row) {
            row.isEditRow = !row.isEditRow
            this.roundList = JSON.parse(JSON.stringify(this.roundList));
        },
        handleEditMatchResult(row) {
            // 编辑对阵结果
            if (row.matchHostResult && row.matchVisitorResult) {
                API.setBattleResult({
                    gameId: row.gameId,
                    hostScore: parseInt(row.matchHostResult),
                    visitorScore: parseInt(row.matchVisitorResult),
                }, (res) => {
                    if (res.error == 0) {
                        this.init()
                    }
                })
            }
            // 编辑对阵结果详情
            for(let i = 0;i < row.matchHostDetail.length;i ++ ) {
                if ((row.matchHostDetail[i] || row.matchHostDetail[i] == 0) && (row.matchVisitorDetail[i] || row.matchVisitorDetail[i] == 0)) {
                    API.setBattleDetail({
                        gameId: row.gameId,
                        hostScore: parseInt(row.matchHostDetail[i]),
                        visitorScore: parseInt(row.matchVisitorDetail[i]),
                        playRound: i + 1
                    }, (res) => {
                        if (res.error == 0) {
                            this.init()
                        }
                    })
                }
            }
        },
        handleSave() {
            
        },
        backToList() {
            window.history.back()
        },
        handleChangePage(pageNo) {
            document.body.scrollTop = 0
            this.pageNo = pageNo
            this.getFinalRank()
        },
        handleChangePageSize(pageSize) {
            this.pageSize = pageSize
            this.getFinalRank()
        }
    },
}
</script>
