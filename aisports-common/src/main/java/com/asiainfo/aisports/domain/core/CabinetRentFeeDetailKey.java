package com.asiainfo.aisports.domain.core;

public class CabinetRentFeeDetailKey {
    private Long feeId;

    private Integer rentAmount;

    public Long getFeeId() {
        return feeId;
    }

    public void setFeeId(Long feeId) {
        this.feeId = feeId;
    }

    public Integer getRentAmount() {
        return rentAmount;
    }

    public void setRentAmount(Integer rentAmount) {
        this.rentAmount = rentAmount;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", feeId=").append(feeId);
        sb.append(", rentAmount=").append(rentAmount);
        sb.append("]");
        return sb.toString();
    }
}