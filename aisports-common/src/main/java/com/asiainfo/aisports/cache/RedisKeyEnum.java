package com.asiainfo.aisports.cache;

/**
 * <AUTHOR>
 * @date 2025/6/10 14:54
 */
public enum RedisKeyEnum {

    PROJECT_LIST("project:{0}:list"),
    PROJECT_LIST_SLOT("'{'{0}'}'project:{1}:list:{2}"),
    PROJECT_LIST_LOCK("project:{0}:list:lock"),
    PROJECT_DETAIL("project:{0}:detail"),
    PROJECT_DETAIL_SLOT("'{'{0}'}'project:{1}:detail:{2}"),
    PROJECT_DETAIL_LOCK("project:{0}:detail:lock"),
    PROJECT_PERFORM_LIST("project:{0}:perform:list"),
    PROJECT_PERFORM_LIST_SLOT("'{'{0}'}'project:{1}:perform:list:{2}"),
    PROJECT_PERFORM_LIST_LOCK("project:{0}:perform:list:lock"),
    PROJECT_PERFORM_STOCK("project:perform:stock:{0}"),
    PROJECT_PERFORM_STOCK_LOCK("project:perform:stock:{0}:lock"),
    PROJECT_USER_ORDER("project:perform:{0}:user:{1}:order"),
    PROJECT_USER_GROUP_ORDER("project:perform:{0}:user:{1}:gp:order"),
    PROJECT_PSPT_ORDER("project:perform:{0}:pspt:{1}:order"),
    PROJECT_SLOT_LIST("slot:project:{0}:list"),
    PROJECT_DETAIL_SLOT_LIST("slot:project:{0}:detail"),
    PROJECT_PERFORM_SLOT_LIST("slot:project:{0}:perform:list"),
    CACHE_TRADE("trade:{0}:cache"),
    PROJECT_PERFORM_RIGHTS("project:ticket:{0}:right"),
    TICKET_QRCODE("project:ticket:qrcode:{0}"),
    PROJECT_PERFORM_DETAIL("project:perform:{0}:detail"),
    PROJECT_PERFORM_TICKET_DETAIL("project:perform:ticket:{0}:detail"),
    PROJECT_PERFORM_STOCK_LIST("project:perform:stock:{0}:list"),
    PROJECT_TICKET_VALID_PERIOD("project:ticket:period:{0}"),
    PROJECT_TRADE_DEAL_LOCK("project:trade:deal:lock:{0}"),
    PROJECT_PERFORM_TAKE_SEAT_LOCK("project:perform:{0}:takeseat:lock"),

    ;

    private final String key;

    RedisKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }


}
