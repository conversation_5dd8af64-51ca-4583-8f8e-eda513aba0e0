/**
 * nightost
 * 2016年02月24日15:18:16
 */
define(function(require){
    var $ = require('jquery'),
        mAlert = require('mAlert'),
        settings = require('aisportsConfig'),
        _ = require('underscore');
    require('frame');
    require('checkBoxAll');
    require('./checkBoxAllSub');
    require("form-control");
    require('copyFixedTableHead');
    const showConfirm = require('helpers/showConfirm');

    var uiIns = null,
        businessIns = null;
    uiIns = {
        init : function(){
            this.initSelect();
            this.initCheckboxAll();
        },
        initSelect : function(){
            $('.bs-select').selectpicker({
                size : 5
            });
        },
        initCheckboxAll : function() {
            $('.handle-field-occupy').checkBoxAll();
            $('.handle-field-occupy').checkBoxAllSub();
            $('.resolve-conflicts').checkBoxAll();
            $("#check-all").on('change', function () {
                let val = $(this).prop("checked")
                $(".list-con").find("input[type='checkbox']:not(:disabled)").prop("checked", val)
            })
        }
    };
    /**
     * business instance
     */
    businessIns = {
        container : '.handle-field-occupy',
        $container : null,
        tradeId : '',
        handleBatch: false, //是否是批量处理
        coverIns : {
            container : '[data-pop-box="resolve-conflicts"]',
            templateSelector : '#handle-template',
            templateBatchSelector : '#batch-handle-template',
            template : null,
            templateBatch: null,
            $cover : null,
            $listCon : null,
            conflictTradeId : '',
            renderData : null,
            parent : null,
            init : function(parent){
                this.parent = parent;
                this.$cover  = $(this.container);
                this.$listCon = this.$cover.find('.conflicts-box');
                this.template = _.template($(this.templateSelector).html());
                this.templateBatch= _.template($(this.templateBatchSelector).html());
                this.bindEvents();
            },
            bindEvents : function(){
                this.$cover.on('click','.confirm', $.proxy(this.handleConfirm,this))
            },
            showErrorTipInfo : function(tipText){
                this.$cover.find('.error-tip').text(tipText);
            },
            /**
             * 处理确认处理
             */
            handleConfirm : function(){
                this.$cover.find('.error-tip').text('');
                if(businessIns.handleBatch){
                    this.batchConfirm()
                }else{
                    var _this = this
                    var sendData = this.getData()
                    if(sendData === null){
                        return;
                    }
                    $.ajax({
                        url : '/occupyConflict/handleSubmit',
                        data : {
                            conflictTradeId : _this.conflictTradeId,
                            fixedTradeId: $("#fixedTradeId").attr("fixedTradeId"),
                            handleArray : JSON.stringify(sendData)
                        },
                        success : function(data){
                            if(data.error == '0'){
                                mAlert.showSuccess('冲突处理成功');
                                _this.parent.changeHandleState(_this.conflictTradeId);
                                _this.hideCover();
                            }
                            else{
                                showAjaxErrorInfo('处理冲突失败,请联系管理员.');
                            }
                        },
                        error : function(){
                            showAjaxErrorInfo('处理冲突失败,请联系管理员.');
                        }
                    });
                }

            },
            batchConfirm:function(){
                let _this = this;
                showConfirm({
                    title: '提示',
                    content: '是否确认处理冲突',
                    onConfirm: function (){
                        let handleList = _this.getBatchData();
                        if(!handleList) return;
                        $.ajax({
                            url : '/occupyConflict/handleSubmitBatch',
                            data : {
                                fixedTradeId: $("#fixedTradeId").attr("fixedTradeId"),
                                handleList:JSON.stringify(handleList)
                            },
                            success : function(data){
                                if(data.error == '0'){
                                    mAlert.showSuccess('冲突处理成功');
                                    _this.renderData = null;
                                    setTimeout(() => {
                                        window.location.reload();
                                    }, 2000);
                                } else {
                                    showAjaxErrorInfo(data.message);
                                }
                            },
                            error : function(){
                                showAjaxErrorInfo('处理冲突失败,请联系管理员.');
                            }
                        });
                    }
                })
            },
            getConflictById:function (id){
                let $handleItems = this.$cover.find('.data-item');
                if($handleItems.length === 0){
                    return null;
                }
                let result;
                $handleItems.each(function(){
                     var $this = $(this)
                      if($this.attr('data-id') === id){
                          result = $this;
                          return false;
                      }
                });
                return result;
            },
            /**
             * 获取批量处理数据
             */
            getBatchData : function() {
                let handleList = [],
                    itemCheck = true,
                    _this = this,
                    data = this.renderData;

                data.totalList.forEach((item) => {
                    let handleArray = [], totalReturn = 0;
                    item.resultList.forEach((obj) => {
                        let dom = _this.getConflictById(obj.id);
                        let opData = {
                            returnMoney: obj.canBackFee,
                            startSegment: obj.startSegment,
                            endSegment: obj.endSegment,
                            date: obj.effectDate,
                            workday: obj.workday,
                            fieldId: obj.fieldId
                        }
                        if (dom) {
                            opData.handleType = dom.find('input[type=checkbox]:checked').val();
                            opData.returnMoney = parseFloat(dom.find('[name = return-money]').val()) * 100;
                        }
                        if (!opData.handleType) {
                            //没选中退款时候
                            opData.handleType = obj.conflictTag != 1 ? "3" : ""
                        }
                        if (obj.ticketId) {
                            opData.ticketId = obj.ticketId;
                        }
                        if (!_this.checkHandleData(obj, opData)) {
                            itemCheck = false;
                            return false;
                        }
                        totalReturn = Number(totalReturn) + Number(opData.returnMoney);
                        handleArray.push(opData)
                    })
                    if (totalReturn > item.tradePayTfee) {
                        _this.showErrorTipInfo('退款总金额大于业务总费用');
                        itemCheck = false;
                        return false;
                    }
                    handleList.push({
                        conflictTradeId: item.conflictTradeId,
                        handleArray: handleArray
                    })
                })
                if (!itemCheck) {
                    return null;
                }
                return handleList;
            },
            /**
             * 获取协议占场数据
             */
            getData : function(){
                var handleArray = [],
                    itemCheck = true,
                    totalReturn = 0,//退款总金额
                    _this = this,
                    tradeTypeCode,//占场类型 票 / 协议占场
                    $handleItem;
                tradeTypeCode = this.renderData.tradeTypeCode,
                $handleItem = this.$cover.find('.data-item');
                if($handleItem.length === 0){
                    return null;
                }
                $handleItem.each(function(){
                    var $this = $(this),
                        curItemData,
                        opData = {},
                        id;
                    id = $this.attr('data-id');
                    opData.handleType = $this.find('input[type=checkbox]:checked').val();

                    opData.returnMoney = parseFloat($this.find('[name = return-money]').val())*100;
                    //默认以ticketId处理
                    curItemData = _.find(_this.renderData.resultList,{
                        ticketId : parseInt(id.split('-')[0])
                    });
                    if(curItemData){
                        opData.ticketId = curItemData.ticketId;
                    }
                    else{
                        curItemData = _.find(_this.renderData.resultList,{
                            tempStampId : id
                        });
                    }
                    if(!opData.handleType) {
                        //没选中退款时候
                        opData.handleType = curItemData.conflictTag != 1 ? "3" : ""
                    }
                    opData.startSegment = curItemData.startSegment;
                    opData.endSegment = curItemData.endSegment;
                    opData.workday = curItemData.workday;
                    opData.date = curItemData.effectDate;
                    opData.fieldId = curItemData.fieldId;

                    if(!_this.checkHandleData(curItemData,opData)){
                        itemCheck = false;
                        return itemCheck;
                    }
                    totalReturn = totalReturn + opData.returnMoney;
                    handleArray.push(opData);
                });
                if(!itemCheck){
                    return null;
                }
                if(totalReturn > this.renderData.tradePayTfee){
                    this.showErrorTipInfo('退款总金额大于业务总费用');
                    return null;
                }
                return handleArray;
            },
            /**
             * check 获取占场handle 数据
             * @return boolean
             */
            checkHandleData : function(origin,opData){
                if( origin.conflictTag == 1 && !opData.handleType){
                    this.showErrorTipInfo('请选择处理的操作类型');
                    return false;
                }
                if(isNaN(opData.returnMoney) || opData.returnMoney < 0){
                    this.showErrorTipInfo('退款金额需要大于等于0');
                    return false;
                }
                if(origin.canBackFee && (opData.returnMoney > parseFloat(origin.canBackFee))){
                    this.showErrorTipInfo('退款金额需要小于当前最大可退金额: ' + parseFloat(origin.canBackFee / 100));
                    return false;
                }
                return true;
            },
            /**
             * 隐藏coverbox
             */
            hideCover : function(){
                this.$cover.hide();
                this.clearCoverBox();
            },
            /**
             * 清除弹层的数据
             */
            clearCoverBox : function(){
                this.renderData = null;
                this.$cover.find('.error-tip').text('');
                this.$listCon.html('');
                this.conflictTradeId = '';
            },
            setRenderData : function(id,data){
                this.conflictTradeId = id;
                this.$cover.show();
                this.renderData = data;
                this.render();
            },
            render : function(){
                this.$listCon.html(this.template(this.renderData));
                this.$listCon.find('.table-con').copyFixedTableHead();
                this.checkboxInitial()
            },
            /**
             * 批量退票弹框渲染
             */
            setBatchRenderData: function(data){
                this.$cover.show();
                this.renderData = data;
                this.$listCon.html(this.templateBatch(this.renderData));
                this.$listCon.find('.table-con').copyFixedTableHead();
                this.checkboxInitial()
            },
            checkboxInitial:function (){
                let $handleItems = this.$cover.find('.data-item');
                let allChecked = true;
                $handleItems.each(function(){
                    let val = $(this).find('input[type=checkbox]:checked').val();
                    if(!val){
                        allChecked = false;
                        return false
                    }
                })
                if(!allChecked){
                    $(".select-all").find(".checkbox-all").prop('checked', false)
                }else {
                    $(".select-all").find(".checkbox-all").prop('checked', true)
                }
            }
        },
        init : function(){
            this.$container = $(this.container);
            this.getArgs();
            this.bindEvents();
            this.coverIns.init(this);
        },
        /**
         * 获取参数
         */
        getArgs : function(){
            this.tradeId = $('#tradeId').val();
        },
        /**
         * 绑定事件
         */
        bindEvents : function(){
            this.$container.on('click','#quick-set', $.proxy(this.quickSet,this));
            this.$container.on('click','#batchRefund', $.proxy(this.batchRefund,this));
            this.$container.on('click','.notice-btn', $.proxy(this.setSingleState,this));
            this.$container.on('click','.handle-btn', $.proxy(this.handleConflict,this));
            this.$container.on('click','.btn-return', $.proxy(this.handleBackPage,this));
            // $('.resolve-conflicts').on('click','.checkbox-all input', $.proxy(this.selectAll,this));

        },
        /**
         * 统一设置通知状态
         */
        quickSet : function(){
            var cardNoArr = this.getSelectedEcardNo();
            if(cardNoArr == 0){
                mAlert.showWarning('没有选中需要通知的项目.');
                return;
            }
            this.sendNoticeSet({
                fixedTradeId : this.tradeId,
                noticeTag : 1,
                ecardNoString : cardNoArr.join(',')
            });
        },
        /**
         * 批量退款
         */
        batchRefund:function (){
            let _this = this;
            let conflictTradeIdList = this.getSelectedTrade();
            if(!conflictTradeIdList.length){
                mAlert.showWarning('请选择要处理的订单.');
                return;
            }
            businessIns.handleBatch = true;
            $.ajax({
                url : '/occupyConflict/getHandleInfoBatch',
                data : {
                    conflictTradeIdList: conflictTradeIdList.join(','),
                    fixedTradeId:$("#fixedTradeId").attr("fixedTradeId"),
                },
                success : function(data){
                    if(data.error == '0') {
                        data.totalList.forEach(function(obj){
                            obj.resultList.forEach(function(item){
                                //添加唯一id以便后面查找
                                item.id = _this.randomRangeId(8);
                            })
                        })
                        _this.coverIns.setBatchRenderData(data);
                    }
                },
                error : function(){
                    showAjaxErrorInfo('处理冲突失败,请联系管理员.');
                }
            });
        },
        /**
         * 生成随机ID
         */
        randomRangeId:function(num){
            let returnStr = "",
            charStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            for(let i = 0; i < num; i++){
                let index = Math.round(Math.random() * (charStr.length-1));
                returnStr += charStr.substring(index,index+1);
            }
            return returnStr;
        },
        /**
         * 单个设置状态
         */
        setSingleState : function(e){
            var $this = $(e.currentTarget),
                nextState = 1,
                id = $this.attr('data-ecardid');
            if($this.hasClass('disabled')){
                mAlert.showWarning('此条冲突无法进行通知.');
                return;
            }
            if($this.hasClass('active')){
                nextState = 0;
            }
            this.sendNoticeSet({
                fixedTradeId : this.tradeId,
                noticeTag : nextState,
                ecardNoString : id
            });
        },
        /**
         * 处理 冲突
         */
        handleConflict : function(e){
            var $this = $(e.currentTarget),
                conflictId = $this.attr('conflictTradeId');
            if($this.hasClass('disabled')){
                return;
            }
            businessIns.handleBatch = false;
            this.getConflictData({
                fixedTradeId : this.tradeId,
                conflictTradeId : conflictId
            });
        },
        /**
         * 返回列表页
         */
        handleBackPage : function(){
            window.location.href = settings.urlPrefix + '/fieldFixedOccupy/init ';
        },
        //改变处理状态
        changeHandleState : function(conflictId){
            var $curItem;
            $curItem = $('[item-id = ' + conflictId + ']');
            $curItem.find('.status-btn-md').removeClass('status-error').addClass('status-normal').text('已处理');
            $curItem.find('.handle-btn').addClass('disabled');
            $curItem.find('checkbox-single-sub').attr('disabled','disabled');
        },
        getConflictData : function(sendData){
            var msg = '获取处理信息失败,请联系管理员.',
                _this = this;
            $.ajax({
                url : '/occupyConflict/getHandleInfo',
                data : sendData,
                success : function(data){
                    if(data.error === 0){
                        _this.coverIns.setRenderData(sendData.conflictTradeId,data);
                        return;
                    }
                    msg = data.message || msg;
                    showAjaxErrorInfo(msg);
                    if(data.error === 1){
                        setTimeout(function(){
                            window.location.reload();
                        },2000)
                    }
                },
                error : function(){
                    showAjaxErrorInfo('获取处理信息失败,请联系管理员.');
                }
            });
        },
        /**
         * 发送切换 通知状态请求
         * @param sendData
         */
        sendNoticeSet : function(sendData){
            var msg = '设置',
                _this = this;
            $.ajax({
                url : '/occupyConflict/changeNoticeTag',
                data : sendData,
                success : function(data){
                    if(data.error == '0'){
                        _this.setNoticeState(sendData.ecardNoString,sendData.noticeTag ? true : false);
                    }
                    else{
                        msg = data.message || msg;
                        showAjaxErrorInfo(msg);
                    }
                },
                error : function(){
                    showAjaxErrorInfo('通知失败,请联系管理员.');
                }
            })
        },
        /**
         * 设置页面的通知状态
         * @param ids '100,101' 一卡通卡号集合
         * @param state
         */
        setNoticeState : function(ids,state){
            var idArr = ids.split(',');
           $.each(idArr,function(index,item){
               var $el =  $('[data-ecardid = ' + item + ']');
               if(state){
                   $el.addClass('active');
               }
               else{
                   $el.removeClass('active');
               }
           });
        },
        /**
         * 获取选中的ecardNo
         */
        getSelectedEcardNo : function(){
            var $noticeCheck = $('[name="noticeCheck"]:checked'),
            ecardNoArr = [];
            $noticeCheck.each(function(){
                var $this = $(this);
                ecardNoArr.push($this.attr('data-id'));
            });
            return ecardNoArr;
        },
        /**
         * 获取选中的conflictTradeId
         */
        getSelectedTrade : function(){
            var $tradeCheck = $('[name="tradeObj"]:checked'),
                tradeArr = [];
            $tradeCheck.each(function(){
                var $this = $(this);
                tradeArr.push($this.attr('data-id'));
            });
            return tradeArr;
        }
    };
    /**
     * 使用spinner 显示错误信息
     */
    function showAjaxErrorInfo(str){
        settings.spinner.showErrorResult(str);
    }
    $(function(){
        uiIns.init();
        businessIns.init();
    });
});
