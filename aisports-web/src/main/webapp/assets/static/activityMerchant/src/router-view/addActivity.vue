<template>
  <div class="title-box associationIndex">
    <div class="pub-head">
      <div class="left-title">{{ type == "edit" ? "详情" : titleName }}</div>
      <div @click="goBack" class="right-area">
        <div class="btn-return">
          <div class="iconfont icon-zuojiantou" style="font-size: 24px"></div>
        </div>
      </div>
    </div>
    <div class="addMember">
      <el-form
        :model="formData"
        ref="formData"
        :rules="formRule"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="商户名称" prop="name">
          <el-input
            type="text"
            v-model="formData.name"
            autocomplete="off"
            placeholder="请输入商户名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input
            type="text"
            v-model="formData.contactPerson"
            autocomplete="off"
            placeholder="请输入联系人"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系人电话" prop="contactPhone">
          <el-input
            type="text"
            v-model="formData.contactPhone"
            autocomplete="off"
            placeholder="请输入联系人电话"
          ></el-input>
        </el-form-item>
        <el-form-item label="企业地址" prop="provinceCode">
          <el-cascader
            ref="area_code"
            v-model="areaCode"
            :props="areaProps"
            :key="cascaderKey"
            class="select_frame"
            clearable
            placeholder="请选择地区"
            @change="handleAreaChange"
          >
          </el-cascader>
        </el-form-item>
        <el-form-item label="详细地址" prop="contactAddr">
          <div class="map_mark">
            <el-input
              v-model="formData.contactAddr"
              class="input_frame"
              type="textarea"
              :rows="2"
              maxlength="300"
              placeholder="请输入详细地址"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item class="bottom-fr">
          <el-button @click="reset('formData')">取消</el-button>
          <el-button type="primary" @click="submitForm('formData')"
            >确认</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { API } from "../utils/api";

export default {
  inject: ["reload"],
  data() {
    return {
      cascaderKey: 0, // 地区选择器的key
      // 地区层级选择器懒加载配置
      areaProps: {
        lazy: true,
        async lazyLoad(node, resolve) {
          const { level } = node;
          const params = {
            areaLevel: level + 1,
          };
          if (level > 0) {
            params.parentCode = node.value || "";
          }
          const { result = [] } = await API.findAreaInfo(params);
          resolve(
            result.map((item) => ({
              value: item.areaCode,
              label: item.areaName,
              lat: item.lat,
              lng: item.lng,
              leaf: level == 2, // 是否有子节点
            }))
          );
        },
      },
      areaCode: [], // 地区编码
      ossUrl: $("#ossUrl").val(),
      titleName: "新增",
      formRule: {
        name: [{ required: true, message: "请输入", trigger: "change" }],
        contactPerson: [{ required: true, message: "请输入", trigger: "blur" }],
        contactPhone: [
          { required: true, message: "请输入", trigger: "change" },
        ],
        provinceCode: [
          { required: true, message: "请选择", trigger: "change" },
        ],
        contactAddr: [{ required: true, message: "请输入", trigger: "change" }],
      },
      formData: {
        name: "",
        contactPerson: "",
        contactPhone: "",
        provinceCode: "",
        cityCode: "",
        districtCode: "",
        contactAddr: "",
      },
      type: "",
    };
  },
  mounted() {
    this.type = this.$route.query.type;
    if(this.type == "edit"){
      this.formData.id = this.$route.query.id;
      this.getDetails();
    }
  },
  methods: {
    reset(){
      this.$router.go(-1);
    },
    async getDetails(){
      const res = await API.getDetail({id:this.formData.id});
      this.formData = res.activityMerchant
      this.areaCode = [res.activityMerchant.provinceCode, res.activityMerchant.cityCode, res.activityMerchant.districtCode];
    },
    // 处理地区选择
    handleAreaChange(value) {
      this.areaCode = value;
      console.log(value);
      if(value.length == 3){
        this.formData.provinceCode = value[0];
        this.formData.cityCode = value[1];
        this.formData.districtCode = value[2];
      }else{
        this.formData.provinceCode = "";
        this.formData.cityCode = "";
        this.formData.districtCode = "";
      }
      
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        console.log("32");
        console.log(this.formData);
        
        if (valid) {
          API.saveOrEdit(this.formData).then((res) => {
            if (res.error == 0) {
              this.$message({
                type: "success",
                message: "保存成功",
              });
              this.$router.go(-1);
            } else {
              this.$message({
                type: "error",
                message: res.message,
              });
            }
          });
        }
      });
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
