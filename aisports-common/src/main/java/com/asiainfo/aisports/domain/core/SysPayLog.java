package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class SysPayLog {
    private Long id;

    private Long tradeId;

    private String payModeCode;

    private Integer shouldPay;

    private Integer realPay;

    private String remark;

    private Date tradeDate;

    private Long tradeStaffId;

    private Long tradeVenueId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    public String getPayModeCode() {
        return payModeCode;
    }

    public void setPayModeCode(String payModeCode) {
        this.payModeCode = payModeCode == null ? null : payModeCode.trim();
    }

    public Integer getShouldPay() {
        return shouldPay;
    }

    public void setShouldPay(Integer shouldPay) {
        this.shouldPay = shouldPay;
    }

    public Integer getRealPay() {
        return realPay;
    }

    public void setRealPay(Integer realPay) {
        this.realPay = realPay;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(Date tradeDate) {
        this.tradeDate = tradeDate;
    }

    public Long getTradeStaffId() {
        return tradeStaffId;
    }

    public void setTradeStaffId(Long tradeStaffId) {
        this.tradeStaffId = tradeStaffId;
    }

    public Long getTradeVenueId() {
        return tradeVenueId;
    }

    public void setTradeVenueId(Long tradeVenueId) {
        this.tradeVenueId = tradeVenueId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", tradeId=").append(tradeId);
        sb.append(", payModeCode=").append(payModeCode);
        sb.append(", shouldPay=").append(shouldPay);
        sb.append(", realPay=").append(realPay);
        sb.append(", remark=").append(remark);
        sb.append(", tradeDate=").append(tradeDate);
        sb.append(", tradeStaffId=").append(tradeStaffId);
        sb.append(", tradeVenueId=").append(tradeVenueId);
        sb.append("]");
        return sb.toString();
    }
}