package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class FieldTimingTicket {
    private Long id;

    private Long fieldTimingId;

    private Long ticketId;

    private Date createTime;

    private Long createStaffId;

    private Date updateTime;

    private Long updateStaffId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFieldTimingId() {
        return fieldTimingId;
    }

    public void setFieldTimingId(Long fieldTimingId) {
        this.fieldTimingId = fieldTimingId;
    }

    public Long getTicketId() {
        return ticketId;
    }

    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateStaffId() {
        return createStaffId;
    }

    public void setCreateStaffId(Long createStaffId) {
        this.createStaffId = createStaffId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateStaffId() {
        return updateStaffId;
    }

    public void setUpdateStaffId(Long updateStaffId) {
        this.updateStaffId = updateStaffId;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", fieldTimingId=").append(fieldTimingId);
        sb.append(", ticketId=").append(ticketId);
        sb.append(", createTime=").append(createTime);
        sb.append(", createStaffId=").append(createStaffId);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", updateStaffId=").append(updateStaffId);
        sb.append("]");
        return sb.toString();
    }
}