function intercept(res) {
    if (res.error !== 0) {
        throw new Error(res.message)
    }
    return res
}


export function queryInitInfo() {
    return $.get('/gameManage/queryInitInfo').then(intercept)
}

export function queryGamesByParams(data) {
    return $.get('/gameMatch/queryGamesByParams', data).then(intercept)
}

export function matchGame(data) {
    return $.get('/gameMatch/matchGame', data).then(intercept)
}
