import router from '@/router';
<template>
  <div class="coach-resv-history coach-resv-wrap">
    <h1>
      <span class="title">取消预约历史</span>
      <span class="btn-return" @click="goback">
        <i class="iconfont icon-zu<PERSON><PERSON><PERSON><PERSON>"></i>
      </span>
    </h1>

    <el-table :data="list" style="width: 100%" tooltip-effect="dark">
      <el-table-column prop="ecardNo" label="学员卡号" align='center' width="200">
      </el-table-column>
      <el-table-column prop="stuName" label="学员姓名" align='center'>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="courseName" label="购买课程信息" align='center' width="180">
      </el-table-column>
      <el-table-column prop="coachName" label="任课教练" align='center' width="200">
      </el-table-column>
      <el-table-column prop="createTime" label="预约时间" align='center' width="200">
      </el-table-column>
      <el-table-column prop="updateTime" label="取消时间" align='center' width="200">
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="remark" label="取消原因" align='center' width="180">
      </el-table-column>
      <el-table-column prop="updateStaffName" label="操作员工"  align='center'>
      </el-table-column>
    </el-table>

    <div class="pagination-wrap" v-if="list.length > 0">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="currentPage" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
  import {
    getCancelResvList
  } from '../apis.js'
  export default {
    data() {
      return {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        list: [],
        lessonId: '',
        tableData: [{
          date: '2016-05-02',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1518 弄'
        }, {
          date: '2016-05-04',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1517 弄'
        }, {
          date: '2016-05-01',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1519 弄'
        }, {
          date: '2016-05-03',
          name: '王小虎',
          address: '上海市普陀区金沙江路 1516 弄'
        }]
      }
    },
    mounted() {
       this.lessonId = this.$route.query.lessonId
      this.getCancelResvList()
    },
    methods: {
      async getCancelResvList() {
        const {
          pageInfo = {
            list: [],
            pageNum: 1,
            pageSize: 10,
            total: 0,
          }
        } = await getCancelResvList({
          pageSize: this.pageSize,
          pageNum: this.currentPage,
          lessonId: this.lessonId
        })

        this.currentPage = pageInfo.pageNum
        this.pageSize = pageInfo.pageSize
        this.total = pageInfo.total
        this.list = pageInfo.list

      },
      handleSizeChange(val) {
        this.pageSize = val
        this.getCancelResvList()
        console.log(`每页 ${val} 条`);
      },
      handleCurrentChange(val) {
        this.currentPage = val
        this.getCancelResvList()
        console.log(`当前页: ${val}`);
      },
      goback() {
        this.$router.back()
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
