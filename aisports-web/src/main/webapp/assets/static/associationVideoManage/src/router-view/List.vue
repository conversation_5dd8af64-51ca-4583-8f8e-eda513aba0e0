<template>
  <div class="title-box clubManageIndex">
    <div class="search-clubManage">
      <el-select
        v-model="tableQuery.collectionId"
        filterable
        clearable
        placeholder="请选择视频合集"
      >
        <el-option
          v-for="item in collectionList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        ></el-option>
      </el-select>
      <el-date-picker
        v-model="rangeDate"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd HH:mm:ss"
        :default-time="['00:00:00', '23:59:59']"
        @change="changeDate"
      >
      </el-date-picker>
      <el-select
        v-model="tableQuery.serviceId"
        filterable
        clearable
        placeholder="请选择运动项目"
      >
        <el-option
          v-for="item in serviceList"
          :key="item.serviceId"
          :label="item.serviceName"
          :value="item.serviceId"
        ></el-option>
      </el-select>
      <el-input v-model="tableQuery.name" placeholder="请输入标题"></el-input>
      <el-button @click="searchList" type="primary">查询</el-button>
    </div>
    <div class="video-list-audit">
      <div class="clubManage-audit fr">
        <el-button icon="el-icon-setting" @click="toCollection"
          >管理合集</el-button
        >
        <el-button icon="el-icon-plus" @click="toAddVideo">新增视频</el-button>
      </div>
    </div>
    <div class="categoryList">
      <div>
        <el-table
          ref="multipleTable"
          :data="tableData"
          style="width: 100%"
          :header-cell-style="{ background: '#FAFAFA', color: '#333' }"
        >
          <!-- <el-table-column type="selection" width="55"></el-table-column> -->
          <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            align="center"
          ></el-table-column>
          <el-table-column align="center" prop="clubLogo" label="封面">
            <template slot-scope="scope">
              <img class="table-img" :src="scope.row.coverImg" alt />
            </template>
          </el-table-column>
          <el-table-column align="center" prop="resourceName" label="标题">
            <template slot-scope="scope">
              <el-popover
                placement="bottom"
                title=""
                width="200"
                trigger="hover"
                :content="scope.row.resourceName"
              >
                <span class="table-tooltip" slot="reference">{{ scope.row.resourceName }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="collectionName" label="所属合集">
            <template slot-scope="scope">
              <el-popover
                placement="bottom"
                title=""
                width="200"
                trigger="hover"
                :content="scope.row.collectionName"
              >
                <span class="table-tooltip" slot="reference">{{ scope.row.collectionName }}</span>
              </el-popover>
              
            </template>
          </el-table-column>
          <el-table-column
            prop="serviceName"
            label="运动项目"
          ></el-table-column>
          <el-table-column align="center" label="有效期">
            <template slot-scope="scope">
              <span v-if="scope.row.startDate">{{
                scope.row.startDate + "到" + scope.row.endDate
              }}</span>
              <span v-else>--</span>
            </template></el-table-column
          >
          <el-table-column align="center" label="状态">
            <template slot-scope="scope">
              <div v-if="scope.row.state == 1" class="status-blue">已发布</div>
              <div v-else-if="scope.row.state == 0" class="status-grey">
                未发布
              </div>
              <div v-else-if="scope.row.state == 3" class="status-red">
                已过期
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button @click="jumpEdit(scope.row)" type="text"
                >编辑</el-button
              >
              <el-button
                @click="unReleaseVideo(scope.row)"
                type="text"
                v-if="scope.row.state == 1"
                >下架</el-button
              >
              <el-button
                @click="releaseVideo(scope.row)"
                type="text"
                v-if="scope.row.state == 0"
                >发布</el-button
              >
              <el-button
                @click="delClick(scope.row)"
                type="text"
                v-if="scope.row.state == 3"
                >--</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="page-bottom">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tableQuery.pageNo"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import * as api from "../utils/api";
import filter from "lodash/filter";
import "utils/confirm";

export default {
  data() {
    return {
      ossUrl: $("#ossUrl").val(),
      tableQuery: {
        pageNo: 1,
        pageSize: 10,
        origin: 1,
        collectionId: "",
        serviceId: "",
        startDate: "",
        endDate: "",
      },
      serviceList: [],
      rangeDate: "",
      total: 0,
      tableData: [],
      collectionList: [],
    };
  },
  mounted() {
    this.getVideoList();
    this.queryTenantServices();
    this.getCollectionList();
  },
  methods: {
    indexMethod(index) {
      return (
        index + 1 + (this.tableQuery.pageNo - 1) * this.tableQuery.pageSize
      );
    },
    handleSizeChange(value) {
      this.tableQuery.pageNo = 1;
      this.tableQuery.pageSize = value;
      this.getVideoList();
    },
    handleCurrentChange(value) {
      this.tableQuery.pageNo = value;
      this.getVideoList();
    },
    changeDate(value) {
      if (!value) {
        this.tableQuery.startDate = "";
        this.tableQuery.endDate = "";
        return;
      }
      this.tableQuery.startDate = value[0];
      this.tableQuery.endDate = value[1];
    },
    getCollectionList() {
      api
        .getCollectionList({ origin: 1, pageNo: 1, pageSize: 0 })
        .then((res) => {
          this.collectionList = res.videoCollection.list;
        });
    },
    getVideoList() {
      api.getVideoList(this.tableQuery).then((res) => {
        this.tableData = res.result.list;
        this.tableData.forEach((element) => {
          let arr = [];
          element.collectionList.forEach((c) => {
            arr.push(c.title);
          });
          element.collectionName = arr.join(",");
          if (arr.length == 0) {
            element.collectionName = "无";
          }
        });
        this.total = res.result.total;
      });
    },
    searchList() {
      this.tableQuery.pageNo = 1;
      this.getVideoList();
    },
    toAddVideo() {
      this.$router.push({
        name: "addVideo",
      });
    },
    toCollection() {
      this.$router.push({
        name: "collectionList",
      });
    },
    jumpEdit(row) {
      this.$confirm("确定修改这个视频？").then(() => {
        this.$router.push({
          name: "editVideo",
          params: {
            resourceId: row.resourceId,
          },
        });
      });
    },
    releaseVideo(row) {
      api.releaseVideo({ resourceIds: row.resourceId }).then((res) => {
        this.$message.success("上架成功");
        this.getVideoList();
      });
    },
    unReleaseVideo(row) {
      this.$confirm("确定下架这个视频？").then(() => {
        api.unReleaseVideo({ resourceIds: row.resourceId }).then((res) => {
          this.$message.success("下架成功");
          this.getVideoList();
        });
      });
    },
    queryTenantServices() {
      api.queryTenantServices().then((res) => {
        this.serviceList = res.result;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.video-list-audit {
  padding: 10px;
  height: 50px;
}
.status-blue {
  width: 58px;
  height: 24px;
  background: rgba(31, 162, 245, 0.1);
  border: 1px solid #1fa2f5;
  border-radius: 12px;
  text-align: center;
  line-height: 24px;
  color: #1fa2f5;
  margin: 0 auto;
}
.status-grey {
  width: 58px;
  height: 24px;
  background: rgba(135, 141, 153, 0.1);
  border: 1px solid #878d99;
  border-radius: 12px;
  text-align: center;
  line-height: 24px;
  color: #878d99;
  margin: 0 auto;
}
.status-red {
  width: 58px;
  height: 24px;
  background: rgba(250, 85, 85, 0.1);
  border: 1px solid #fa5555;
  border-radius: 12px;
  text-align: center;
  line-height: 24px;
  color: #fa5555;
  margin: 0 auto;
}
/deep/ .el-range-editor {
  padding: 0;
}
.table-tooltip {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>