package com.asiainfo.aisports.api.controller.api.diving;

import com.asiainfo.aisports.api.web.WebResult;
import com.asiainfo.aisports.service.api.diving.DivingCoachService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/diving/coach")
public class DivingCoachController {

    @Autowired
    private DivingCoachService divingCoachService;

    /**
     * 教练查询
     *
     * @param keyWord
     * @param pageNo
     * @return
     */
    @RequestMapping(value = "/queryDivingCoachList", method = RequestMethod.GET)
    public WebResult queryDivingCoachList(@RequestParam(value = "keyWord", required = false) String keyWord,
                                          @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(value = "pageSize", defaultValue = "10", required = false) Integer pageSize) {
        return new WebResult().set("coachList", new PageInfo<>(divingCoachService.queryDivingCoachPageList(keyWord, pageNo, pageSize)));
    }

    /**
     * 教练查询--小程序
     *
     * @param keyWord
     * @return
     */
    @RequestMapping(value = "/queryDivingCoachListForMiniApp", method = RequestMethod.GET)
    public WebResult queryDivingCoachListForMiniApp(@RequestParam(value = "keyWord", required = false) String keyWord) {
        return new WebResult().set("coachList", divingCoachService.queryDivingCoachListForMiniApp(keyWord));
    }

}