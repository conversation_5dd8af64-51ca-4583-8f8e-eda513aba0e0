/**
 * Created by Administrator on 2015/4/22.
 */
define(function (require) {
    var $ = require("jquery");
    require("frame");
    var settings = require("aisportsConfig");
    //弹出加载插件
    var spinner = settings.spinner;
    var mAlertInstance = require("mAlert");
    var passwordCheck;
    var result;
    var passwordValidLimit = 4;
    var phoneNumberBox=false
    function toggleInputActive() {
        $('[need-second]').on('click', function () {
            $('[need-second]').removeClass('activePSW');
            $(this).addClass('activePSW');
        });
        $('.password-field .iconfont').on('click', function () {
            console.dir(this)
            if (/icon-biyan-shurukuangnei/.test(this.className)) {
                this.className = 'iconfont icon-yanjing-zheng'
                $(this).siblings('input').prop('type', 'text')
            } else {
                this.className = 'iconfont icon-biyan-shurukuangnei'
                $(this).siblings('input').prop('type', 'password')
            }
        })
    }
    function phoneSet() {
        $('.f_forgot .phoneName').on('click', function () {
            $("#oldPass_box").hide()
            $("#phoneNumber_box").show()
            $("#vCodes").show()
            phoneNumberBox=true
        })
        $(".getCode").on('click', function () {
            let phone = $("#phoneNumber").val()
            const mobilPhoneReg = settings.regs.mobilphone;
            if (mobilPhoneReg.test(phone)) {
                console.log($(".getCode").text())
                if ($(".getCode").text() == '获取验证码') {
                    $.ajax({
                        type: 'POST',
                        url: '/changepassword/generateVerifyCode',
                        data: {
                            phone: phone,
                        },
                        success(data) {
                            if (data.error === 0) {
                                mAlertInstance.showSuccess('验证码发送成功')
                                $('.getCode').attr("disabled", true);
                                let time = 60
                                let timeOut = setInterval(() => {
                                    if (time < 1) {
                                        time = '获取验证码'
                                        clearInterval(timeOut)
                                        $(".getCode").text(time)
                                        $('.getCode').removeAttr("disabled");
                                        return
                                    }
                                    time--
                                    $(".getCode").text(time)
                                    // console.log(that.time,'that.time')
                                }, 1000);
                            } else {
                                mAlertInstance.showWarning(data.message)
                            }
                        },
                    })
                }

            } else {
                mAlertInstance.showWarning("请输入正确的手机号！");
            }

        })
    }
    function checkSubmit() {
        if ($("#newPass").val() == null || $("#newPass").val() == "") {
            mAlertInstance.showWarning("校验未通过,请输入新密码！密码需为8位以上字符，且同时包含大、小写字母、数字和特殊字符");
            return false;
        } else if ($("#oldPass").val() == null || $("#oldPass").val() == "") {
            if (phoneNumberBox) {
                if ($("#newPass").val() != $("#confirmPass").val()) {
                    mAlertInstance.showWarning("校验未通过,新密码和确认密码不相同！");
                    return false;
                }else {
                    return true
                }
            } else {
                mAlertInstance.showWarning("校验未通过,请输入旧密码！密码需为8位以上字符，且同时包含大、小写字母、数字和特殊字符");
                return false;
            }
        } else if ($("#confirmPass").val() == null || $("#confirmPass").val() == "") {
            mAlertInstance.showWarning("校验未通过,请输入确认密码！密码需为8位以上字符，且同时包含大、小写字母、数字和特殊字符");
            return false;
        } else if ($("#newPass").val() != $("#confirmPass").val()) {
            mAlertInstance.showWarning("校验未通过,新密码和确认密码不相同！");
            return false;
        } else {
            return true;
        }
    }

    function findVenueParam() {
        let venueId = $('#venueId').val()
        $.ajax({
            type: "GET",
            url: "/commonParam/findVenueParam",
            data: {
                paramCode: "sys_password_complexity_attr",
                venueId: venueId
            },
            success: function (res) {
                passwordValidLimit = res.paramValue ? Number(res.paramValue) : 4
            }

        })
    }
    passwordCheck = {
        selectorArr: [],
        setCheckInput: function (selector) {
            this.selectorArr.push(selector);
            this.bindInput();
        },
        checkPassStrength: function (str) {
            /*
             *密码安全程度
             *return 1 : 长度8
             *return 2 : 长度12
             *return 3 : 长度16
             */
            if (str.length > 0 && str.length <= 6) return 0;
            var n1 = (str.length >= 8) ? 1 : 0,
                n2 = (str.length >= 12) ? 1 : 0,
                n3 = (str.length >= 16) ? 1 : 0;
            return n1 + n2 + n3;
        },
        bindInput: function () {
            $(document).on('input', this.selectorArr.join(' '), $.proxy(this.checkPsw, this));
        },
        validatePassword: function (pwd) {
            let n = 0
            if (/(?=^)(?=.*\d+).{8,}/.test(pwd)) {
                n += 1
            }
            if (/(?=^)(?=.*[a-z]+).{8,}/.test(pwd)) {
                n += 1
            }
            if (/(?=^)(?=.*[A-Z]+).{8,}/.test(pwd)) {
                n += 1
            }
            if (/(?=^)(?=.*[!@#$%&*]+)(?!.*[^!@#$%&*\w]+).{8,}/.test(pwd)) {
                n += 1
            }
            return n
        },
        checkPsw: function (e) {
            var $target = $(e.currentTarget);
            var $strength = $target.siblings('.password-strength');
            var checkFlag;
            var val;
            var tipSelector;
            var $tipSelector;
            if ($strength.length === 0) {
                return;
            }
            tipSelector = $target.attr('tip-selector');
            $tipSelector = $('[data-role=' + tipSelector + ']');
            val = $target.val();
            checkFlag = this.checkPassStrength(val);
            // if (/(?=^)(?=.*\d+)(?=.*[a-z]+)(?=.*[A-Z]+)(?=.*[!@#$%&*]+)(?!.*[^!@#$%&*\w]+).{8,}/.test(val)) {
            if (this.validatePassword(val) >= passwordValidLimit) {
                if (checkFlag === 0) {
                    $target.addClass('error');
                    $tipSelector.show();
                    $strength.hide();
                }
                else {
                    $tipSelector.hide();
                    $target.removeClass('error');
                    $strength.show()
                        .find('.active')
                        .removeClass('active');
                    $strength.find('[val=' + checkFlag + ']')
                        .addClass('active');
                }
            } else {
                $target.addClass('error');
                $tipSelector.show();
                $strength.hide();
            }
        }
    }


    //业务提交
    $("#submitBtn").bind("click", function () {
        if (!checkSubmit()) {
            return;
        }
        let parms = {}
        if (phoneNumberBox) {
            console.log($("#vCode").val())
            if ($("#vCode").val() == '') {
                mAlertInstance.showWarning("校验未通过,请输入验证码！");
                return
            }
            parms.verifyCode = $("#vCode").val()
            parms.newPass = $("#confirmPass").val()
        } else {
            parms.oldPass = $("#oldPass").val()
            parms.newPass = $("#confirmPass").val()
        }
        $.ajax({
            type: "POST",
            url: "/changepassword/submitPassword",
            data: parms,
            success: function (data) {
                if (Object.prototype.hasOwnProperty.call(data, 'error')) {
                    if (data.error == 0) {
                        settings.spinner.showSuccessResult(data.message);
                        window.setTimeout(function () {
                            window.location.href = settings.urlPrefix + "/logout";
                        }, 1500);
                    } else if (data.error == 1) {
                        spinner.showErrorResult(data.message || '');
                    }
                } else {
                    spinner.showErrorResult(data);
                }
            }
        });
    });

    $(function () {
        passwordCheck.setCheckInput('#newPass');
        toggleInputActive();
        findVenueParam()
        phoneSet();

    });
});
