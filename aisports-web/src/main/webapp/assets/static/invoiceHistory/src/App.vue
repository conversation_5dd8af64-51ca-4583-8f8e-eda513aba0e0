<template>
    <div>
        <history-list
            v-show="pageIndex === 0"
            @change-page-index="handlePageChange"></history-list>
        <invoice-detail
            v-if="pageIndex === 1"
            :id="sysInvoiceLogId"
            @change-page-index="handlePageChange"></invoice-detail>
    </div>
</template>

<script>
import HistoryList from './components/HistoryList.vue'
import InvoiceDetail from './components/InvoiceDetail.vue'

export default {

    components: {
        'history-list': HistoryList,
        'invoice-detail': InvoiceDetail,
    },
    data() {
        return {
            pageIndex: 0,
            sysInvoiceLogId: '',
        }
    },

    methods: {
        handlePageChange(newIndex, sysInvoiceLogId) {
            this.pageIndex = newIndex
            this.sysInvoiceLogId = sysInvoiceLogId
        },
    },
}
</script>
