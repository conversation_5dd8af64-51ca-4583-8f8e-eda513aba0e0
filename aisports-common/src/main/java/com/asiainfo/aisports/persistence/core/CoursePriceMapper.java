package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.CoursePrice;
import com.asiainfo.aisports.model.DataMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;
import java.util.Map;

public interface CoursePriceMapper {
    @Delete({
        "delete from course_price",
        "where price_id = #{priceId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long priceId);

    @Insert({
        "insert into course_price (price_id, course_id, ",
        "type, lesson_subject_id, ",
        "lesson_num, price, ",
        "start_date, end_date, ",
        "update_time, update_staff_id, valid_period)",
        "values (#{priceId,jdbcType=BIGINT}, #{courseId,jdbcType=BIGINT}, ",
        "#{type,jdbcType=CHAR}, #{lessonSubjectId,jdbcType=BIGINT}, ",
        "#{lessonNum,jdbcType=BIGINT}, #{price,jdbcType=INTEGER}, ",
        "#{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, ",
        "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT},",
        "#{validPeriod,jdbcType=VARCHAR} )",
    })
    int insert(CoursePrice record);

    @Select({
        "select",
        "price_id, course_id, type, lesson_subject_id, lesson_num, price, start_date, ",
        "end_date, update_time, update_staff_id, valid_period",
        "from course_price",
        "where price_id = #{priceId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="lesson_subject_id", property="lessonSubjectId", jdbcType=JdbcType.BIGINT),
        @Result(column="lesson_num", property="lessonNum", jdbcType=JdbcType.BIGINT),
        @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column = "valid_period", property = "validPeriod", jdbcType = JdbcType.VARCHAR)
    })
    CoursePrice selectByPrimaryKey(Long priceId);

    @Update({
        "update course_price",
        "set course_id = #{courseId,jdbcType=BIGINT},",
        "type = #{type,jdbcType=CHAR},",
        "lesson_subject_id = #{lessonSubjectId,jdbcType=BIGINT},",
        "lesson_num = #{lessonNum,jdbcType=BIGINT},",
        "price = #{price,jdbcType=INTEGER},",
        "start_date = #{startDate,jdbcType=TIMESTAMP},",
        "end_date = #{endDate,jdbcType=TIMESTAMP},",
        "update_time = #{updateTime,jdbcType=TIMESTAMP},",
        "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where price_id = #{priceId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(CoursePrice record);

    @Select({
        "<script>",
        "select",
        "price_id, course_id, type, lesson_subject_id, lesson_num, price, start_date, end_date, update_time, update_staff_id",
        "from course_price",
        "<where>",
        "<if test=\"priceId != null\">and price_id = #{priceId,jdbcType=BIGINT}</if>",
        "<if test=\"courseId != null\">and course_id = #{courseId,jdbcType=BIGINT}</if>",
        "<if test=\"type != null\">and type = #{type,jdbcType=CHAR}</if>",
        "<if test=\"lessonSubjectId != null\">and lesson_subject_id = #{lessonSubjectId,jdbcType=BIGINT}</if>",
        "<if test=\"lessonNum != null\">and lesson_num = #{lessonNum,jdbcType=BIGINT}</if>",
        "<if test=\"price != null\">and price = #{price,jdbcType=INTEGER}</if>",
        "<if test=\"startDate != null\">and start_date = #{startDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"endDate != null\">and end_date = #{endDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="lesson_subject_id", property="lessonSubjectId", jdbcType=JdbcType.BIGINT),
        @Result(column="lesson_num", property="lessonNum", jdbcType=JdbcType.BIGINT),
        @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<CoursePrice> selectByFields(CoursePrice param);

    @Update({
        "<script>",
        "update course_price",
        "<set>",
        "<if test=\"courseId != null\">course_id = #{courseId,jdbcType=BIGINT},</if>",
        "<if test=\"type != null\">type = #{type,jdbcType=CHAR},</if>",
        "<if test=\"lessonSubjectId != null\">lesson_subject_id = #{lessonSubjectId,jdbcType=BIGINT},</if>",
        "<if test=\"lessonNum != null\">lesson_num = #{lessonNum,jdbcType=BIGINT},</if>",
        "<if test=\"price != null\">price = #{price,jdbcType=INTEGER},</if>",
        "<if test=\"startDate != null\">start_date = #{startDate,jdbcType=TIMESTAMP},</if>",
        "<if test=\"endDate != null\">end_date = #{endDate,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "<if test=\"validPeriod != null\">valid_period = #{validPeriod,jdbcType=VARCHAR},</if>",
        "</set>",
        "where price_id = #{priceId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(CoursePrice record);

    /**
     * 查询出当前时间内有效的基础价格和课程价格
     * @param param
     * @return
     */
    @Select({
        "<script>",
        "select",
        "price_id, course_id, type, lesson_subject_id, lesson_num, price, start_date, end_date, update_time, update_staff_id, valid_period",
        "from course_price",
        "<where>",
        "now() between start_date and end_date",
        "<if test=\"priceId != null\">and price_id = #{priceId,jdbcType=BIGINT}</if>",
        "<if test=\"courseId != null\">and course_id = #{courseId,jdbcType=BIGINT}</if>",
        "<if test=\"type != null\">and type = #{type,jdbcType=CHAR}</if>",
        "<if test=\"price != null\">and price = #{price,jdbcType=INTEGER}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="lesson_subject_id", property="lessonSubjectId", jdbcType=JdbcType.BIGINT),
        @Result(column="lesson_num", property="lessonNum", jdbcType=JdbcType.BIGINT),
        @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column = "valid_period", property = "validPeriod", jdbcType = JdbcType.VARCHAR)
    })
    List<CoursePrice> selectCurPriceInfo(CoursePrice param);


    /**
     * 查询出当前时间内有效的基础价格和课程价格以及未生效的
     * @param param
     * @return
     */
    @Select({
            "<script>",
            "select",
            "price_id, course_id, type, lesson_subject_id, lesson_num, price, start_date, end_date, update_time, update_staff_id, valid_period",
            "from course_price",
            "<where>",
            "end_date &gt; now()",
            "<if test=\"priceId != null\">and price_id = #{priceId,jdbcType=BIGINT}</if>",
            "<if test=\"courseId != null\">and course_id = #{courseId,jdbcType=BIGINT}</if>",
            "<if test=\"type != null\">and type = #{type,jdbcType=CHAR}</if>",
            "<if test=\"price != null\">and price = #{price,jdbcType=INTEGER}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
            @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
            @Result(column="lesson_subject_id", property="lessonSubjectId", jdbcType=JdbcType.BIGINT),
            @Result(column="lesson_num", property="lessonNum", jdbcType=JdbcType.BIGINT),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column = "valid_period", property = "validPeriod", jdbcType = JdbcType.VARCHAR)
    })
    List<CoursePrice> selectValidPriceInfo(CoursePrice param);

    @Select({
        "<script>",
        "select price_id, course_id, type, price, start_date, end_date, update_time, update_staff_id",
        "from course_price",
        "<where>",
        "and course_id = #{courseId,jdbcType=BIGINT}",
        "and type in",
            "<foreach item=\"type\" collection=\"typeList\" separator=\",\" open=\"(\" close=\")\" index=\"\">",
                "#{type, jdbcType=CHAR}",
            "</foreach>",
        "</where>",
        "order by end_date desc",
        "</script>"
    })
    @Results({
        @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<CoursePrice> selectByCourseId(@Param("courseId") Long courseId, @Param("typeList") List<String> typeList);

    @Delete({
        "delete from course_price",
        "where course_id = #{courseId,jdbcType=BIGINT}"
    })
    int deleteByCourseId(Long courseId);

    //获取当前时间内有效的续课价格或者续课折扣信息
    @Select({
        "<script>",
        "select a.course_id, a.price_id, a.type, b.amount, b.value_type, b.value",
        "from course_price a, course_price_item b",
        "where a.course_id = #{courseId,jdbcType=BIGINT} and a.type = '"+ Constants.CoursePriceType.RENEWAL + "' and  a.end_date &gt; now()",
        "and a.price_id = b.price_id and b.amount = '1'",
        "</script>"
    })
    @Results({
        @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="amount", property="amount", jdbcType=JdbcType.INTEGER),
        @Result(column="value_type", property="valueType", jdbcType=JdbcType.VARCHAR),
        @Result(column="value", property="value", jdbcType=JdbcType.INTEGER)
    })
    Map getCourseContinue(@Param("courseId") Long courseId);

    //将某课程下的所有价格信息都至为失效状态
    @Update({
        "update course_price",
        "set end_date = #{endDate,jdbcType=TIMESTAMP},",
        "update_time = #{updateTime,jdbcType=TIMESTAMP},",
        "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where course_id = #{courseId,jdbcType=BIGINT} and now() between start_date and end_date"
    })
    int invalidByCourseId(CoursePrice record);

    //续课价、多期优惠获取
    @Select({
        "<script>",
        "select",
        "a.course_id, b.amount, a.type, b.value_type, b.value, a.start_date, a.end_date, a.update_time",
        "from course_price a, course_price_item b",
        "<where>",
        "and a.price_id = b.price_id",
        "and a.type = #{type,jdbcType=CHAR}",
        "<if test=\"courseId != null\">and a.course_id = #{courseId,jdbcType=BIGINT}</if>",
        "<if test=\"date != null\">and #{date,jdbcType=TIMESTAMP} BETWEEN a.start_date and a.end_date</if>",
        "<if test=\"amount != null\">and b.amount = #{amount,jdbcType=INTEGER}</if>",
        "</where>",
        "order by b.amount asc",
        "</script>"
    })
    @Results({
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="amount", property="amount", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="value_type", property="valueType", jdbcType=JdbcType.CHAR),
        @Result(column="value", property="value", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    List<Map<String, Object>> selectPreferentials(Map<String, Object> param);

    //课程基本价格获取
    @Select({
        "<script>",
        "select",
        "a.course_id, a.type, a.price, a.start_date, a.end_date, a.update_time",
        "from course_price a",
        "<where>",
        "and a.type = #{type,jdbcType=CHAR}",
        "<if test=\"courseId != null\">and a.course_id = #{courseId,jdbcType=BIGINT}</if>",
        "<if test=\"date != null\">and #{date,jdbcType=TIMESTAMP} BETWEEN a.start_date and a.end_date</if>",
        "</where>",
        "limit 1",
        "</script>"
    })
    @Results({
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="amount", property="amount", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="value_type", property="valueType", jdbcType=JdbcType.CHAR),
        @Result(column="value", property="value", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP)
    })
    CoursePrice selectBasePrice(Map<String, Object> param);

    /**
     * 根据课程类型查询课程价格
     *
     * @param courseId
     * @param priceType
     * @return
     */
    @Select({
        "select a.course_id, a.price_id, a.type, b.amount, b.grade_id, b.value_type, b.value",
        "from course_price a, course_price_item b",
        "where a.course_id = #{courseId,jdbcType=BIGINT} and a.type = #{priceType,jdbcType=VARCHAR} and now() between a.start_date and a.end_date",
        "and a.price_id = b.price_id and b.amount = '1'"
    })
    @Results({
        @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="amount", property="amount", jdbcType=JdbcType.INTEGER),
        @Result(column="grade_id", property="gradeId", jdbcType=JdbcType.VARCHAR),
        @Result(column="value_type", property="valueType", jdbcType=JdbcType.VARCHAR),
        @Result(column="value", property="value", jdbcType=JdbcType.INTEGER)
    })
    List<Map<String, Object>> selectCoursePriceItemByType(@Param("courseId") Long courseId, @Param("priceType") String priceType);

    @Select({
        "<script>",
        "select",
        "a.price_id, a.course_id, a.type, a.lesson_subject_id, a.lesson_num, a.price, ifnull(b.subject_name, '') subject_name, a.valid_period",
        "from course_price a left join tc_subject b on a.lesson_subject_id = b.subject_id",
        "<where>",
        "and now() between a.start_date and a.end_date",
        "and a.course_id = #{courseId,jdbcType=BIGINT} and a.type = '7'",
        "</where>",
        "order by a.lesson_subject_id asc, a.lesson_num asc",
        "</script>"
    })
    @Results({
        @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
        @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
        @Result(column="lesson_subject_id", property="lessonSubjectId", jdbcType=JdbcType.BIGINT),
        @Result(column="lesson_num", property="lessonNum", jdbcType=JdbcType.BIGINT),
        @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
        @Result(column="subject_name", property="subjectName", jdbcType=JdbcType.VARCHAR),
        @Result(column = "valid_period", property = "validPeriod", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectSubjectPriceList(@Param("courseId") Long courseId);


    /**
     * 根据场馆id和服务Id查询团课信息
     * @param serviceId
     * @param venueId
     * @return
     */
    @Select({
            "select",
            "a.course_id, a.start_date, a.end_date, a.inst_id, a.course_type, b.price_id, b.lesson_num, b.price, FLOOR(b.price/b.lesson_num) unit_price",
            "from training_course a, course_price b",
            "where",
            "a.course_id=b.course_id and a.service_id=#{serviceId,jdbcType=BIGINT} ",
            "and a.status='1' ",
            "and a.inst_id = #{venueId, jdbcType=BIGINT} ",
            "and a.training_type='2' and a.class_tag='0'",
            "and b.lesson_num is not null",
            "and b.lesson_subject_id = 0 ",
            "and now() between a.start_date and a.end_date and now() between b.start_date and b.end_date order by b.lesson_num"
    })
    @Results({
            @Result(column = "course_id", property = "courseId", jdbcType = JdbcType.BIGINT),
            @Result(column = "start_date", property = "startDate", jdbcType = JdbcType.DATE),
            @Result(column = "end_date", property = "endDate", jdbcType = JdbcType.DATE),
            @Result(column = "inst_id", property = "instId", jdbcType = JdbcType.BIGINT),
            @Result(column = "course_type", property = "courseType", jdbcType = JdbcType.BIGINT),
            @Result(column = "price_id", property = "priceId", jdbcType = JdbcType.BIGINT),
            @Result(column = "course_id", property = "courseId", jdbcType = JdbcType.BIGINT),
            @Result(column = "lesson_num", property = "lessonNum", jdbcType = JdbcType.BIGINT),
            @Result(column = "price", property = "price", jdbcType = JdbcType.BIGINT),
            @Result(column = "unit_price", property = "unitPrice", jdbcType = JdbcType.INTEGER)
    })
    List<DataMap> selectPriceListByServiceId(@Param("serviceId") Long serviceId, @Param("venueId") Long venueId);

    /**
     * 批量获取课程价格
     * @param courseIds
     * @return
     */
    @Select({
            "select",
            "price_id, course_id, type, lesson_subject_id, lesson_num, price, start_date, ",
            "end_date, update_time, update_staff_id",
            "from course_price",
            "where find_in_set(course_id,#{courseIds,jdbcType=VARCHAR})"
    })
    @Results({
            @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
            @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
            @Result(column="lesson_subject_id", property="lessonSubjectId", jdbcType=JdbcType.BIGINT),
            @Result(column="lesson_num", property="lessonNum", jdbcType=JdbcType.BIGINT),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<CoursePrice> selectByCourseIds(String courseIds);

    @Select({
            "<script>",
            "select",
            "a.price_id, a.course_id, a.type, a.lesson_subject_id, a.lesson_num, a.price, ifnull(tsl.subject_name,b.subject_name) subject_name, a.valid_period",
            "from course_price a left join tc_subject b on a.lesson_subject_id = b.subject_id",
            "left join tc_subject_lang tsl on b.subject_id = tsl.subject_id and tsl.language=#{lang,jdbcType=VARCHAR}",
            "<where>",
            "and now() between a.start_date and a.end_date",
            "and a.course_id = #{courseId,jdbcType=BIGINT} and a.type = '7'",
            "</where>",
            "order by a.lesson_subject_id asc, a.lesson_num asc",
            "</script>"
    })
    @Results({
            @Result(column="price_id", property="priceId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="course_id", property="courseId", jdbcType=JdbcType.BIGINT),
            @Result(column="type", property="type", jdbcType=JdbcType.CHAR),
            @Result(column="lesson_subject_id", property="lessonSubjectId", jdbcType=JdbcType.BIGINT),
            @Result(column="lesson_num", property="lessonNum", jdbcType=JdbcType.BIGINT),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="subject_name", property="subjectName", jdbcType=JdbcType.VARCHAR),
            @Result(column = "valid_period", property = "validPeriod", jdbcType = JdbcType.VARCHAR)
    })
    List<DataMap> selectSubjectPriceListLang(@Param("courseId") Long courseId, @Param("lang") String lang);

}