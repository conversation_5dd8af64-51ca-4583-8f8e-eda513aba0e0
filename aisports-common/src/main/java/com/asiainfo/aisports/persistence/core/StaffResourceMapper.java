package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.domain.core.StaffResource;
import com.asiainfo.aisports.domain.core.StaffResourceKey;
import com.asiainfo.aisports.model.CenterResource;
import com.asiainfo.aisports.model.VenueResource;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface StaffResourceMapper {
    @Delete({
        "delete from staff_resource",
        "where staff_id = #{staffId,jdbcType=BIGINT}",
          "and resource_type = #{resourceType,jdbcType=CHAR}",
          "and resource_id = #{resourceId,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(StaffResourceKey key);

    @Insert({
        "insert into staff_resource (staff_id, resource_type, ",
        "resource_id, resource_range, ",
        "start_date, end_date, ",
        "state, update_time, ",
        "update_staff_id)",
        "values (#{staffId,jdbcType=BIGINT}, #{resourceType,jdbcType=CHAR}, ",
        "#{resourceId,jdbcType=BIGINT}, #{resourceRange,jdbcType=CHAR}, ",
        "#{startDate,jdbcType=TIMESTAMP}, #{endDate,jdbcType=TIMESTAMP}, ",
        "#{state,jdbcType=CHAR}, #{updateTime,jdbcType=TIMESTAMP}, ",
        "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(StaffResource record);

    @Select({
        "select",
        "staff_id, resource_type, resource_id, resource_range, start_date, end_date, ",
        "state, update_time, update_staff_id",
        "from staff_resource",
        "where staff_id = #{staffId,jdbcType=BIGINT}",
          "and resource_type = #{resourceType,jdbcType=CHAR}",
          "and resource_id = #{resourceId,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="resource_type", property="resourceType", jdbcType=JdbcType.CHAR, id=true),
        @Result(column="resource_id", property="resourceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="resource_range", property="resourceRange", jdbcType=JdbcType.CHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    StaffResource selectByPrimaryKey(StaffResourceKey key);

    @Update({
        "update staff_resource",
        "set resource_range = #{resourceRange,jdbcType=CHAR},",
          "start_date = #{startDate,jdbcType=TIMESTAMP},",
          "end_date = #{endDate,jdbcType=TIMESTAMP},",
          "state = #{state,jdbcType=CHAR},",
          "update_time = #{updateTime,jdbcType=TIMESTAMP},",
          "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
        "where staff_id = #{staffId,jdbcType=BIGINT}",
          "and resource_type = #{resourceType,jdbcType=CHAR}",
          "and resource_id = #{resourceId,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(StaffResource record);

    @Select({
        "<script>",
        "select",
        "staff_id, resource_type, resource_id, resource_range, start_date, end_date, state, update_time, update_staff_id",
        "from staff_resource",
        "<where>",
        "<if test=\"staffId != null\">and staff_id = #{staffId,jdbcType=BIGINT}</if>",
        "<if test=\"resourceType != null\">and resource_type = #{resourceType,jdbcType=CHAR}</if>",
        "<if test=\"resourceId != null\">and resource_id = #{resourceId,jdbcType=BIGINT}</if>",
        "<if test=\"resourceRange != null\">and resource_range = #{resourceRange,jdbcType=CHAR}</if>",
        "<if test=\"startDate != null\">and start_date = #{startDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"endDate != null\">and end_date = #{endDate,jdbcType=TIMESTAMP}</if>",
        "<if test=\"state != null\">and state = #{state,jdbcType=CHAR}</if>",
        "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
        "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
        "</where>",
        "</script>"
    })
    @Results({
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="resource_type", property="resourceType", jdbcType=JdbcType.CHAR, id=true),
        @Result(column="resource_id", property="resourceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="resource_range", property="resourceRange", jdbcType=JdbcType.CHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<StaffResource> selectByFields(StaffResource param);

    @Update({
        "<script>",
        "update staff_resource",
        "<set>",
        "<if test=\"resourceRange != null\">resource_range = #{resourceRange,jdbcType=CHAR},</if>",
        "<if test=\"startDate != null\">start_date = #{startDate,jdbcType=TIMESTAMP},</if>",
        "<if test=\"endDate != null\">end_date = #{endDate,jdbcType=TIMESTAMP},</if>",
        "<if test=\"state != null\">state = #{state,jdbcType=CHAR},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where staff_id = #{staffId,jdbcType=BIGINT}",
          "and resource_type = #{resourceType,jdbcType=CHAR}",
          "and resource_id = #{resourceId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateByPrimaryKeySelective(StaffResource record);

    /**
     * 根据staffId查询对应的中心
     * @param staffId
     * @return
     */
    @Select({
        "select",
        "b.center_id, b.center_name, b.phone, b.start_date, b.end_date, b.ecard_cust_id, ",
        "b.center_type, b.state, b.create_time, b.create_staff_id, b.update_time, b.update_staff_id, ",
        "a.resource_range",
        "from staff_resource a, center b",
        "where",
        "a.resource_id = b.center_id and a.resource_type = '1'",
        "and a.staff_id = #{staffId,jdbcType=BIGINT}",
        "and now() >= a.start_date and (now() < a.end_date or a.end_date is null)",
        "and b.state = '1'",
        "order by b.center_id"
    })
    @Results({
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_name", property="centerName", jdbcType=JdbcType.VARCHAR),
        @Result(column="phone", property="phone", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="ecard_cust_id", property="ecardCustId", jdbcType=JdbcType.BIGINT),
        @Result(column="center_type", property="centerType", jdbcType=JdbcType.CHAR),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="resource_range", property="resourceRange", jdbcType=JdbcType.VARCHAR)
    })
    List<CenterResource> selectCentersByStaffId(@Param("staffId") Long staffId);

    /**
     * 根据staffId查询对应的场馆
     * @param staffId
     * @return
     */
    @Select({
        "select",
        "b.venue_id, b.center_id, b.sort, b.venue_name, b.phone, b.start_date, b.end_date, b.business_hours, ",
        "b.ecard_cust_id, b.pinyin, b.city_code, b.icon_name, b.location_id, b.venue_type, b.create_time, b.create_staff_id, ",
        "b.update_time, b.update_staff_id, a.resource_range",
        "from staff_resource a, venue b",
        "where",
        "a.resource_id = b.venue_id and a.resource_type = '2'",
        "and a.staff_id = #{staffId,jdbcType=BIGINT}",
        "and now() >= a.start_date and (now() < a.end_date or a.end_date is null)",
        "and now() between b.start_date and b.end_date",
        "order by b.center_id, b.sort"
    })
    @Results({
        @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_name", property="venueName", jdbcType=JdbcType.VARCHAR),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="phone", property="phone", jdbcType=JdbcType.VARCHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="business_hours", property="businessHours", jdbcType=JdbcType.VARCHAR),
        @Result(column="ecard_cust_id", property="ecardCustId", jdbcType=JdbcType.BIGINT),
        @Result(column="pinyin", property="pinyin", jdbcType=JdbcType.VARCHAR),
        @Result(column="city_code", property="cityCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="icon_name", property="iconName", jdbcType=JdbcType.VARCHAR),
        @Result(column="location_id", property="locationId", jdbcType=JdbcType.BIGINT),
        @Result(column="venue_type", property="venueType", jdbcType=JdbcType.CHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
        @Result(column="resource_range", property="resourceRange", jdbcType=JdbcType.VARCHAR)
    })
    List<VenueResource> selectVenuesByStaffId(@Param("staffId") Long staffId);

    @Delete({
        "delete from staff_resource",
        "where staff_id = #{staffId,jdbcType=BIGINT} and resource_type = #{resourceType,jdbcType=CHAR}",
    })
    int deleteByStaffIdAndType(@Param("staffId") Long staffId, @Param("resourceType") String resourceType);

    @Insert({
        "<script>",
        "insert into staff_resource (staff_id, resource_type, ",
        "resource_id, resource_range, ",
        "start_date, end_date, ",
        "state, update_time, ",
        "update_staff_id) values",
        "<foreach collection=\"list\" item=\"item\" index=\"index\" separator=\",\">",
        "(#{item.staffId,jdbcType=BIGINT}, #{item.resourceType,jdbcType=CHAR}, ",
        "#{item.resourceId,jdbcType=BIGINT}, #{item.resourceRange,jdbcType=CHAR}, ",
        "#{item.startDate,jdbcType=TIMESTAMP}, #{item.endDate,jdbcType=TIMESTAMP}, ",
        "#{item.state,jdbcType=CHAR}, #{item.updateTime,jdbcType=TIMESTAMP}, ",
        "#{item.updateStaffId,jdbcType=BIGINT})",
        "</foreach>",
        "</script>"
    })
    int batchInsert(List<StaffResource> staffResourceList);

    @Select({
        "select",
        "staff_id, resource_type, resource_id, ifnull(resource_range, 0) resource_range, start_date, end_date, state, update_time, update_staff_id",
        "from staff_resource a",
        "where",
        "a.staff_id = #{staffId,jdbcType=BIGINT} and a.state = '1'",
        "and now() >= a.start_date and (now() < a.end_date or a.end_date is null)",
    })
    @Results({
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="resource_type", property="resourceType", jdbcType=JdbcType.CHAR, id=true),
        @Result(column="resource_id", property="resourceId", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="resource_range", property="resourceRange", jdbcType=JdbcType.CHAR),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="state", property="state", jdbcType=JdbcType.CHAR),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<StaffResource> selectByStaffId(Long staffId);

    @Update({
        "<script>",
        "update staff_resource",
        "<set>",
        "<if test=\"resourceRange != null\">resource_range = #{resourceRange,jdbcType=CHAR},</if>",
        "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
        "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
        "</set>",
        "where staff_id = #{staffId,jdbcType=BIGINT}",
        "and resource_type = #{resourceType,jdbcType=CHAR}",
        "and resource_id = #{resourceId,jdbcType=BIGINT}",
        "</script>"
    })
    int updateResourceRange(StaffResource staffResource);

}