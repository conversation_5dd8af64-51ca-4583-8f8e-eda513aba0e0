package com.asiainfo.aisports.model.interactive.form;

import com.asiainfo.aisports.model.interactive.ActivityFormOptionDto;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/19 11:49
 */
public abstract class BaseForm {

    private String formName;

    private String valueType;

    private String required;

    public BaseForm() {
    }

    public BaseForm(String formName, String required) {
        this.formName = formName;
        this.required = required;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public String getValueType() {
        return valueType;
    }

    public void setValueType(String valueType) {
        this.valueType = valueType;
    }

    public String getRequired() {
        return required;
    }

    public void setRequired(String required) {
        this.required = required;
    }

    public List<ActivityFormOptionDto> getOptions() {
        return new ArrayList<>();
    }

    public void checkValue(String content) {
        if ("1".equals(this.required) ) {
            if (StringUtils.isBlank(content)) {
                throw new IllegalStateException("请填写[" + this.formName + "]内容");
            }

        }

    }
}
