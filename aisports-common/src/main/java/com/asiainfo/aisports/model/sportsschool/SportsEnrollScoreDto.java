package com.asiainfo.aisports.model.sportsschool;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/11 10:10
 */
public class SportsEnrollScoreDto {

    private Long id;

    private Long serviceId;

    private String courseName;

    private String serviceName;

    private Double score;

    private Date publishTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public String getCourseName() {
        return courseName;
    }

    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    @Override
    public String toString() {
        return "SportsEnrollScoreDto{" +
                "id=" + id +
                ", serviceId=" + serviceId +
                ", courseName='" + courseName + '\'' +
                ", serviceName='" + serviceName + '\'' +
                ", score=" + score +
                ", publishTime=" + publishTime +
                '}';
    }
}
