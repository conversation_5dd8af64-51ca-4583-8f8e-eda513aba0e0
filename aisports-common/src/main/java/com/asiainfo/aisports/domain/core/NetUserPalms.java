package com.asiainfo.aisports.domain.core;

import java.util.Date;

public class NetUserPalms {
    private Long id;

    private Long netUserId;

    private String palmsId;

    private String openId;

    private String organizationId;

    private String state;

    private Date authorizeTime;

    private Long centerId;

    private Date createTime;

    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNetUserId() {
        return netUserId;
    }

    public void setNetUserId(Long netUserId) {
        this.netUserId = netUserId;
    }

    public String getPalmsId() {
        return palmsId;
    }

    public void setPalmsId(String palmsId) {
        this.palmsId = palmsId == null ? null : palmsId.trim();
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId == null ? null : openId.trim();
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId == null ? null : organizationId.trim();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public Date getAuthorizeTime() {
        return authorizeTime;
    }

    public void setAuthorizeTime(Date authorizeTime) {
        this.authorizeTime = authorizeTime;
    }

    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", netUserId=").append(netUserId);
        sb.append(", palmsId=").append(palmsId);
        sb.append(", openId=").append(openId);
        sb.append(", organizationId=").append(organizationId);
        sb.append(", state=").append(state);
        sb.append(", authorizeTime=").append(authorizeTime);
        sb.append(", centerId=").append(centerId);
        sb.append(", createTime=").append(createTime);
        sb.append(", updateTime=").append(updateTime);
        sb.append("]");
        return sb.toString();
    }
}