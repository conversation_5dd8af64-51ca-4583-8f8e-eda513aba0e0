package com.asiainfo.aisports.persistence.core.diving;

import com.asiainfo.aisports.domain.core.diving.DivingCoachLevel;
import com.asiainfo.aisports.domain.core.diving.DivingCoachLevelKey;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface DivingCoachLevelMapper {
    @Delete({
            "delete from diving_coach_level",
            "where diving_coach_id = #{divingCoachId,jdbcType=BIGINT}",
            "and coach_number = #{coachNumber,jdbcType=VARCHAR}",
            "and diving_course_type = #{divingCourseType,jdbcType=CHAR}",
            "and level = #{level,jdbcType=INTEGER}"
    })
    int deleteByPrimaryKey(DivingCoachLevelKey key);

    @Insert({
            "insert into diving_coach_level (diving_coach_id, coach_number, ",
            "diving_course_type, level, ",
            "status, create_time, ",
            "create_staff_id, update_time, ",
            "update_staff_id)",
            "values (#{divingCoachId,jdbcType=BIGINT}, #{coachNumber,jdbcType=VARCHAR}, ",
            "#{divingCourseType,jdbcType=CHAR}, #{level,jdbcType=INTEGER}, ",
            "#{status,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
            "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
            "#{updateStaffId,jdbcType=BIGINT})"
    })
    int insert(DivingCoachLevel record);

    @Select({
            "select",
            "diving_coach_id, coach_number, diving_course_type, level, status, create_time, ",
            "create_staff_id, update_time, update_staff_id",
            "from diving_coach_level",
            "where diving_coach_id = #{divingCoachId,jdbcType=BIGINT}",
            "and coach_number = #{coachNumber,jdbcType=VARCHAR}",
            "and diving_course_type = #{divingCourseType,jdbcType=CHAR}",
            "and level = #{level,jdbcType=INTEGER}"
    })
    @Results({
            @Result(column="diving_coach_id", property="divingCoachId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="coach_number", property="coachNumber", jdbcType=JdbcType.VARCHAR, id=true),
            @Result(column="diving_course_type", property="divingCourseType", jdbcType=JdbcType.CHAR, id=true),
            @Result(column="level", property="level", jdbcType=JdbcType.INTEGER, id=true),
            @Result(column="status", property="status", jdbcType=JdbcType.CHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    DivingCoachLevel selectByPrimaryKey(DivingCoachLevelKey key);

    @Update({
            "update diving_coach_level",
            "set status = #{status,jdbcType=CHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where diving_coach_id = #{divingCoachId,jdbcType=BIGINT}",
            "and coach_number = #{coachNumber,jdbcType=VARCHAR}",
            "and diving_course_type = #{divingCourseType,jdbcType=CHAR}",
            "and level = #{level,jdbcType=INTEGER}"
    })
    int updateByPrimaryKey(DivingCoachLevel record);

    @Select({
            "<script>",
            "select",
            "diving_coach_id, coach_number, diving_course_type, level, status, create_time, create_staff_id, update_time, update_staff_id",
            "from diving_coach_level",
            "<where>",
            "<if test=\"divingCoachId != null\">and diving_coach_id = #{divingCoachId,jdbcType=BIGINT}</if>",
            "<if test=\"coachNumber != null\">and coach_number = #{coachNumber,jdbcType=VARCHAR}</if>",
            "<if test=\"divingCourseType != null\">and diving_course_type = #{divingCourseType,jdbcType=CHAR}</if>",
            "<if test=\"level != null\">and level = #{level,jdbcType=INTEGER}</if>",
            "<if test=\"status != null\">and status = #{status,jdbcType=CHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="diving_coach_id", property="divingCoachId", jdbcType=JdbcType.BIGINT, id=true),
            @Result(column="coach_number", property="coachNumber", jdbcType=JdbcType.VARCHAR, id=true),
            @Result(column="diving_course_type", property="divingCourseType", jdbcType=JdbcType.CHAR, id=true),
            @Result(column="level", property="level", jdbcType=JdbcType.INTEGER, id=true),
            @Result(column="status", property="status", jdbcType=JdbcType.CHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<DivingCoachLevel> selectByFields(DivingCoachLevel param);

    @Update({
            "<script>",
            "update diving_coach_level",
            "<set>",
            "<if test=\"status != null\">status = #{status,jdbcType=CHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where diving_coach_id = #{divingCoachId,jdbcType=BIGINT}",
            "and coach_number = #{coachNumber,jdbcType=VARCHAR}",
            "and diving_course_type = #{divingCourseType,jdbcType=CHAR}",
            "and level = #{level,jdbcType=INTEGER}",
            "</script>"
    })
    int updateByPrimaryKeySelective(DivingCoachLevel record);

    @Insert({
            "replace into diving_coach_level (diving_coach_id, coach_number, ",
            "diving_course_type, level, ",
            "status, create_time, ",
            "create_staff_id, update_time, ",
            "update_staff_id)",
            "values (#{divingCoachId,jdbcType=BIGINT}, #{coachNumber,jdbcType=VARCHAR}, ",
            "#{divingCourseType,jdbcType=CHAR}, #{level,jdbcType=INTEGER}, ",
            "#{status,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
            "#{createStaffId,jdbcType=BIGINT}, #{updateTime,jdbcType=TIMESTAMP}, ",
            "#{updateStaffId,jdbcType=BIGINT})"
    })
    int replaceInsert(DivingCoachLevel record);

    @Delete({
            "delete from diving_coach_level",
            "where diving_coach_id = #{divingCoachId,jdbcType=BIGINT}"
    })
    int deleteByCoachId(@Param("divingCoachId") Long divingCoachId);

    @Update({
            "update diving_coach_level",
            "set level = #{level,jdbcType=INTEGER}",
            "where diving_coach_id = #{divingCoachId,jdbcType=BIGINT}",
            "and diving_course_type = #{divingCourseType,jdbcType=CHAR}",
            "and status = #{status,jdbcType=CHAR}",
    })
    int updateCoachLevel(DivingCoachLevel record);
}