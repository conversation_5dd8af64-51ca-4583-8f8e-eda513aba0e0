package com.asiainfo.aisports.persistence.core;

import com.asiainfo.aisports.common.ProjectConstants;
import com.asiainfo.aisports.domain.core.PerformNewTicket;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.ParamMap;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.JdbcType;

public interface PerformNewTicketMapper {
    @Delete({
            "delete from perform_new_ticket",
            "where id = #{id,jdbcType=VARCHAR}"
    })
    int deleteByPrimaryKey(String id);

    @Insert({
            "insert into perform_new_ticket (id, name, ",
            "state, perform_id, ",
            "ticket_type, stock_id, ",
            "site_id, type, adult_num, ",
            "minor_num, rule, ",
            "create_time, create_staff_id, ",
            "update_time, update_staff_id, team_name)",
            "values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, ",
            "#{state,jdbcType=VARCHAR}, #{performId,jdbcType=VARCHAR}, ",
            "#{ticketType,jdbcType=BIGINT}, #{stockId,jdbcType=BIGINT}, ",
            "#{siteId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{adultNum,jdbcType=INTEGER}, ",
            "#{minorNum,jdbcType=INTEGER}, #{rule,jdbcType=VARCHAR}, ",
            "#{createTime,jdbcType=TIMESTAMP}, #{createStaffId,jdbcType=BIGINT}, ",
            "#{updateTime,jdbcType=TIMESTAMP}, #{updateStaffId,jdbcType=BIGINT}, #{teamName,jdbcType=VARCHAR})"
    })
    int insert(PerformNewTicket record);

    @Select({
            "select",
            "id, name, state, perform_id, ticket_type, stock_id, site_id, type, adult_num, ",
            "minor_num, rule, create_time, create_staff_id, update_time, update_staff_id, team_name",
            "from perform_new_ticket",
            "where id = #{id,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.VARCHAR, id=true),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
            @Result(column="ticket_type", property="ticketType", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
            @Result(column="site_id", property="siteId", jdbcType=JdbcType.BIGINT),
            @Result(column="type", property="type", jdbcType=JdbcType.VARCHAR),
            @Result(column="adult_num", property="adultNum", jdbcType=JdbcType.INTEGER),
            @Result(column="minor_num", property="minorNum", jdbcType=JdbcType.INTEGER),
            @Result(column="rule", property="rule", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="team_name", property="teamName", jdbcType=JdbcType.VARCHAR)
    })
    PerformNewTicket selectByPrimaryKey(String id);

    @Update({
            "update perform_new_ticket",
            "set name = #{name,jdbcType=VARCHAR},",
            "team_name = #{teamName,jdbcType=VARCHAR},",
            "state = #{state,jdbcType=VARCHAR},",
            "perform_id = #{performId,jdbcType=VARCHAR},",
            "ticket_type = #{ticketType,jdbcType=BIGINT},",
            "stock_id = #{stockId,jdbcType=BIGINT},",
            "site_id = #{siteId,jdbcType=BIGINT},",
            "type = #{type,jdbcType=VARCHAR},",
            "adult_num = #{adultNum,jdbcType=INTEGER},",
            "minor_num = #{minorNum,jdbcType=INTEGER},",
            "rule = #{rule,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=TIMESTAMP},",
            "create_staff_id = #{createStaffId,jdbcType=BIGINT},",
            "update_time = #{updateTime,jdbcType=TIMESTAMP},",
            "update_staff_id = #{updateStaffId,jdbcType=BIGINT}",
            "where id = #{id,jdbcType=VARCHAR}"
    })
    int updateByPrimaryKey(PerformNewTicket record);

    @Select({
            "<script>",
            "select",
            "id, name, team_name, state, perform_id, ticket_type, stock_id, site_id, type, adult_num, minor_num, rule, create_time, create_staff_id, update_time, update_staff_id",
            "from perform_new_ticket",
            "<where>",
            "<if test=\"id != null\">and id = #{id,jdbcType=VARCHAR}</if>",
            "<if test=\"name != null\">and name = #{name,jdbcType=VARCHAR}</if>",
            "<if test=\"teamName != null\">and team_name = #{teamName,jdbcType=VARCHAR}</if>",
            "<if test=\"state != null\">and state = #{state,jdbcType=VARCHAR}</if>",
            "<if test=\"performId != null\">and perform_id = #{performId,jdbcType=VARCHAR}</if>",
            "<if test=\"ticketType != null\">and ticket_type = #{ticketType,jdbcType=BIGINT}</if>",
            "<if test=\"stockId != null\">and stock_id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"siteId != null\">and site_id = #{siteId,jdbcType=BIGINT}</if>",
            "<if test=\"type != null\">and type = #{type,jdbcType=VARCHAR}</if>",
            "<if test=\"adultNum != null\">and adult_num = #{adultNum,jdbcType=INTEGER}</if>",
            "<if test=\"minorNum != null\">and minor_num = #{minorNum,jdbcType=INTEGER}</if>",
            "<if test=\"rule != null\">and rule = #{rule,jdbcType=VARCHAR}</if>",
            "<if test=\"createTime != null\">and create_time = #{createTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"createStaffId != null\">and create_staff_id = #{createStaffId,jdbcType=BIGINT}</if>",
            "<if test=\"updateTime != null\">and update_time = #{updateTime,jdbcType=TIMESTAMP}</if>",
            "<if test=\"updateStaffId != null\">and update_staff_id = #{updateStaffId,jdbcType=BIGINT}</if>",
            "</where>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.VARCHAR, id=true),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="team_name", property="teamName", jdbcType=JdbcType.VARCHAR),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
            @Result(column="ticket_type", property="ticketType", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
            @Result(column="site_id", property="siteId", jdbcType=JdbcType.BIGINT),
            @Result(column="type", property="type", jdbcType=JdbcType.VARCHAR),
            @Result(column="adult_num", property="adultNum", jdbcType=JdbcType.INTEGER),
            @Result(column="minor_num", property="minorNum", jdbcType=JdbcType.INTEGER),
            @Result(column="rule", property="rule", jdbcType=JdbcType.VARCHAR),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="create_staff_id", property="createStaffId", jdbcType=JdbcType.BIGINT),
            @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="update_staff_id", property="updateStaffId", jdbcType=JdbcType.BIGINT)
    })
    List<PerformNewTicket> selectByFields(PerformNewTicket param);

    @Update({
            "<script>",
            "update perform_new_ticket",
            "<set>",
            "<if test=\"name != null\">name = #{name,jdbcType=VARCHAR},</if>",
            "<if test=\"teamName != null\">team_name = #{teamName,jdbcType=VARCHAR},</if>",
            "<if test=\"state != null\">state = #{state,jdbcType=VARCHAR},</if>",
            "<if test=\"performId != null\">perform_id = #{performId,jdbcType=VARCHAR},</if>",
            "<if test=\"ticketType != null\">ticket_type = #{ticketType,jdbcType=BIGINT},</if>",
            "<if test=\"stockId != null\">stock_id = #{stockId,jdbcType=BIGINT},</if>",
            "<if test=\"siteId != null\">site_id = #{siteId,jdbcType=BIGINT},</if>",
            "<if test=\"type != null\">type = #{type,jdbcType=VARCHAR},</if>",
            "<if test=\"adultNum != null\">adult_num = #{adultNum,jdbcType=INTEGER},</if>",
            "<if test=\"minorNum != null\">minor_num = #{minorNum,jdbcType=INTEGER},</if>",
            "<if test=\"rule != null\">rule = #{rule,jdbcType=VARCHAR},</if>",
            "<if test=\"createTime != null\">create_time = #{createTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"createStaffId != null\">create_staff_id = #{createStaffId,jdbcType=BIGINT},</if>",
            "<if test=\"updateTime != null\">update_time = #{updateTime,jdbcType=TIMESTAMP},</if>",
            "<if test=\"updateStaffId != null\">update_staff_id = #{updateStaffId,jdbcType=BIGINT},</if>",
            "</set>",
            "where id = #{id,jdbcType=VARCHAR}",
            "</script>"
    })
    int updateByPrimaryKeySelective(PerformNewTicket record);

    /**
     * 获取有效场次票的列表信息
     * @param performId
     * @return
     */
    @Select({
            "<script>",
            "select",
            "a.id perform_ticket_id, a.name perform_ticket_name, a.perform_id perform_id, a.ticket_type ticket_type,",
            "a.stock_id,a.site_id,b.venue_id,",
            "ps.name as stock_name ,s.name site_name,ps.total_amount,ps.remain_amount,",
            "d.price price,a.type as ticket_kind,a.adult_num ,a.minor_num ,a.rule,",
            "(select group_concat(ptr.rights_id) from perform_ticket_rights ptr where ptr.perform_ticket_id = a.id and now() between ptr.start_date and ptr.end_date) as rights_ids",
            "from perform_new_ticket a, ticket_type b, price_item c, price_strategy d,perform_stock ps,site s",
            "<where>",
            "a.perform_id = #{performId,jdbcType=VARCHAR}",
            "and a.state = '1'",
            "and a.ticket_type = b.ticket_type_id",
            "and b.ticket_type_id = c.type_id",
            "and c.price_item = d.price_item",
            "and ps.id = a.stock_id",
            "and s.id = a.site_id",
            "and a.type in ('1','2','3','4','5','6','7')",
            "and #{date,jdbcType=TIMESTAMP} between c.create_time and c.destroy_time",
            "</where>",
            "order by a.id desc",
            "</script>"
    })
    @Results({
            @Result(column="perform_ticket_id", property="performTicketId", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_ticket_name", property="performTicketName", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
            @Result(column="ticket_type", property="ticketType", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
            @Result(column="site_id", property="siteId", jdbcType=JdbcType.BIGINT),
            @Result(column="venue_id", property="venueId", jdbcType=JdbcType.BIGINT),
            @Result(column="ticket_type", property="ticketType", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="site_name", property="siteName", jdbcType=JdbcType.VARCHAR),
            @Result(column="total_amount", property="totalAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="remain_amount", property="remainAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="ticket_kind", property="ticketKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="adult_num", property="adultNum", jdbcType=JdbcType.INTEGER),
            @Result(column="minor_num", property="minorNum", jdbcType=JdbcType.INTEGER),
            @Result(column="rule", property="rule", jdbcType=JdbcType.VARCHAR),
            @Result(column="rights_ids", property="rightsIds", jdbcType=JdbcType.VARCHAR),
    })
    List<Map<String,Object>> selectValidPerformTicketByPerformId(@Param("performId")String performId, @Param("date")Date date);

    @Select({
            "select tt.ticket_id,tt.state,j.name,pt.name,p.perform_date,p.start_time,p.end_time,p.address,t.center_id," ,
            "       (select c.center_name from center c where c.center_id = t.center_id)  as center_name," ,
            "pt.name as perform_ticket_name" ,
            "from net_trade nt," ,
            "     trade t," ,
            "     trade_ticket tt" ,
            "         left join trade_ticket_attr tta on tta.ticket_id = tt.ticket_id and tta.attr_code = 'perform_ticket_id'," ,
            "     perform_new_ticket pt," ,
            "     perform p ," ,
            "     project j" ,
            "where nt.net_user_id = #{netUserId}" ,
            "and nt.trade_id = t.trade_id" ,
            "and tt.trade_id = t.trade_id" ,
            "and t.subscribe_state = '1'" ,
            "and tt.state in ('0','2','3')",
            "and tta.attr_value = pt.id" ,
            "and pt.perform_id = p.id" ,
            "and p.project_id = j.id",
            "order by tt.create_time desc ",
    })
    @Results({
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_date", property="performDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="start_time", property="startTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="end_time", property="endTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="address", property="address", jdbcType=JdbcType.VARCHAR),
            @Result(column="center_id", property="centerId", jdbcType=JdbcType.BIGINT),
            @Result(column="center_name", property="centerName", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_ticket_name", property="performTicketName", jdbcType=JdbcType.VARCHAR),
    })
    List<DataMap> selectBoughtList(@Param("netUserId") Long netUserId);

    @Select({
            "select tt.ticket_id,tt.state,tt.check_ticket_time,j.name as project_name,j.image,p.name as perform_name,p.perform_date,p.start_time,p.end_time," ,
            "       p.address,p.description,tt.pay_money as price,tt.create_time,pt.name as perform_ticket_name,pt.team_name," ,
            "       (select c.center_name from center c where c.center_id = j.center_id)  as center_name," ,
            "       ps.name as stock_name,ps.location as stock_location," ,
            "       (select si.name from site si where si.id = pt.site_id) as site_name," ,
            "       (select attr.attr_value from project_attr attr where attr.project_id = j.id and attr.attr_code = 'project_buy_manual') as project_buy_manual" ,
            "from" ,
            "     trade_ticket tt" ,
            "         left join trade_ticket_attr tta on tta.ticket_id = tt.ticket_id and tta.attr_code = 'perform_ticket_id'," ,
            "     perform_new_ticket pt," ,
            "     perform_stock ps," ,
            "     perform p ," ,
            "     project j" ,
            "where tt.ticket_id = #{ticketId}" ,
            "and tta.attr_value = pt.id" ,
            "and pt.perform_id = p.id" ,
            "and p.project_id = j.id" ,
            "and ps.id = pt.stock_id"
    })
    @Results({
            @Result(column="ticket_id", property="ticketId", jdbcType=JdbcType.BIGINT),
            @Result(column="state", property="state", jdbcType=JdbcType.VARCHAR),
            @Result(column="project_name", property="projectName", jdbcType=JdbcType.VARCHAR),
            @Result(column="image", property="image", jdbcType=JdbcType.VARCHAR),
            @Result(column="check_ticket_time", property="checkTicketTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="perform_name", property="performName", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_date", property="performDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="start_time", property="startTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="end_time", property="endTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="address", property="address", jdbcType=JdbcType.VARCHAR),
            @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="perform_ticket_name", property="performTicketName", jdbcType=JdbcType.VARCHAR),
            @Result(column="team_name", property="teamName", jdbcType=JdbcType.VARCHAR),
            @Result(column="center_name", property="centerName", jdbcType=JdbcType.VARCHAR),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="stock_location", property="stockLocation", jdbcType=JdbcType.VARCHAR),
            @Result(column="site_name", property="siteName", jdbcType=JdbcType.VARCHAR),
            @Result(column="project_buy_manual", property="shouldKnow", jdbcType=JdbcType.VARCHAR),
    })
    DataMap getTicketInfo(@Param("ticketId") Long ticketId);


    @Select({
            "select pj.id as project_id,pj.name as project_name,pj.image," ,
            "       pf.id as perform_id, pf.name as perform_name,pf.address," ,
            "       pf.perform_date,pf.start_time,pf.end_time," ,
            "       pnt.id as perform_ticket_id,pnt.name as perform_ticket_name,ps.name as  stock_name,s.name as site_name" ,
            "from project pj,perform pf,perform_new_ticket pnt ,perform_stock ps,site s" ,
            "where pj.id  = pf.project_id" ,
            "and pf.id = pnt.perform_id" ,
            "and pnt.stock_id = ps.id" ,
            "and pnt.site_id = s.id" ,
            "and pnt.id = #{id}"
    })
    @Results({
            @Result(column="project_id", property="projectId", jdbcType=JdbcType.VARCHAR),
            @Result(column="project_name", property="projectName", jdbcType=JdbcType.VARCHAR),
            @Result(column="image", property="image", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_name", property="performName", jdbcType=JdbcType.VARCHAR),
            @Result(column="address", property="address", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_date", property="performDate", jdbcType=JdbcType.TIMESTAMP),
            @Result(column="start_time", property="startTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="end_time", property="endTime", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_ticket_id", property="performTicketId", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_ticket_name", property="performTicketName", jdbcType=JdbcType.VARCHAR),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="site_name", property="siteName", jdbcType=JdbcType.VARCHAR),
    })
    DataMap selectOrderInfoById(@Param("id") String performTicketId);


    @Select({
            "<script>",
            "select a.id ",
            "from perform_new_ticket a",
            "left join perform b on a.perform_id = b.id",
            "where a.state = 1",
            "<if test=\"projectId != null and projectId != ''\">and b.project_id = #{projectId,jdbcType=BIGINT}</if>",
            "<if test=\"stockId != null and stockId != ''\">and a.stock_id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"performId != null and performId != ''\">and a.perform_id = #{performId,jdbcType=VARCHAR}</if>",
            "</script>"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.BIGINT)
    })
    List<Long> selectPerformTicketId(@Param("projectId") String projectId,
                                     @Param("performId") String performId,
                                     @Param("stockId") Long stockId);


    @Select({
            "<script>",
            "select",
            "a.id perform_ticket_id, a.name perform_ticket_name, a.team_name, a.perform_id perform_id, a.ticket_type ticket_type,",
            "a.stock_id,a.site_id,",
            "ps.name as stock_name ,s.name site_name,ps.total_amount,ps.remain_amount,",
            "d.price price,a.type as ticket_kind,a.adult_num ,a.minor_num ,a.rule,",
            "(select group_concat(ptr.rights_id) from perform_ticket_rights ptr where ptr.perform_ticket_id = a.id and now() between ptr.start_date and ptr.end_date) as rights_ids",
            "from perform_new_ticket a, ticket_type b, price_item c, price_strategy d,perform_stock ps,site s",
            "<where>",
            "a.perform_id = #{performId,jdbcType=VARCHAR}",
            "and a.state = '1'",
            "and a.ticket_type = b.ticket_type_id",
            "and b.ticket_type_id = c.type_id",
            "and c.price_item = d.price_item",
            "and ps.id = a.stock_id",
            "and s.id = a.site_id",
            "<if test=\"stockId != null\">and a.stock_id = #{stockId,jdbcType=BIGINT}</if>",
            "<if test=\"ticketKind != null and ticketKind != ''\">and a.type = #{ticketKind}</if>",
            "<if test=\"ticketKind == null || ticketKind == ''\">and a.type != '" + ProjectConstants.TicketKind.TEAM + "'</if>",
            "<if test=\"ticketName != null\">and a.name like concat('%', #{ticketName,jdbcType=VARCHAR}, '%')</if>",
            "and #{date,jdbcType=TIMESTAMP} between c.create_time and c.destroy_time",
            "</where>",
            "order by a.update_time desc",
            "</script>"
    })
    @Results({
            @Result(column="perform_ticket_id", property="performTicketId", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_ticket_name", property="performTicketName", jdbcType=JdbcType.VARCHAR),
            @Result(column="team_name", property="teamName", jdbcType=JdbcType.VARCHAR),
            @Result(column="perform_id", property="performId", jdbcType=JdbcType.VARCHAR),
            @Result(column="ticket_type", property="ticketType", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_id", property="stockId", jdbcType=JdbcType.BIGINT),
            @Result(column="site_id", property="siteId", jdbcType=JdbcType.BIGINT),
            @Result(column="ticket_type", property="ticketType", jdbcType=JdbcType.BIGINT),
            @Result(column="stock_name", property="stockName", jdbcType=JdbcType.VARCHAR),
            @Result(column="site_name", property="siteName", jdbcType=JdbcType.VARCHAR),
            @Result(column="total_amount", property="totalAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="remain_amount", property="remainAmount", jdbcType=JdbcType.INTEGER),
            @Result(column="price", property="price", jdbcType=JdbcType.INTEGER),
            @Result(column="ticket_kind", property="ticketKind", jdbcType=JdbcType.VARCHAR),
            @Result(column="adult_num", property="adultNum", jdbcType=JdbcType.INTEGER),
            @Result(column="minor_num", property="minorNum", jdbcType=JdbcType.INTEGER),
            @Result(column="rule", property="rule", jdbcType=JdbcType.VARCHAR),
            @Result(column="rights_ids", property="rightsIds", jdbcType=JdbcType.VARCHAR),
    })
    List<Map<String, Object>> selectValidPerformTicketPageList(ParamMap param, RowBounds rowBounds);

}
