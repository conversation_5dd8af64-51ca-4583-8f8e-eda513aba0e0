// <script src="//code.jquery.com/jquery-1.11.0.min.js"></script>
function addJQScript(){
	var body=document.getElementsByTagName("body")[0];
	var script=document.createElement("script");
	script.src="//code.jquery.com/jquery-1.11.0.min.js";
	script.type="text/javascript";
	body.appendChild(script);
}
addJQScript();

$(".font-lists .icon").each(function(){
     var _this=$(this);
     setTimeout(function(){
     	_this[0].click();
     }, 200);
});

function addShopingCart(){
	var _icons=
}

$(".operate").each(function(){
     var _this=$(this);
     console.log(_this.find("a:first"));
});


function addIconToCart(){
	var icons = document.querySelectorAll('.pannel:not(#J_pannel1) div.icon');
	var auto_click = function(i){ if(i<icons.length){ setTimeout(function(){ icons.item(i).click(); auto_click(i+1); }, 500); } }; auto_click(0);
	document.querySelector('.user-car').style.height = '300px';
	document.querySelector('.user-car').style.overflowY = 'auto';
	//如果漏了，补足
	// document.querySelectorAll('.font-lists .icon:not(.selected)')[0].click();
}