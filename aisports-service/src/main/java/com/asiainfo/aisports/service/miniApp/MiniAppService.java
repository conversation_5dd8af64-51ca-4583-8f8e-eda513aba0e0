package com.asiainfo.aisports.service.miniApp;

import com.asiainfo.aisports.model.NetUserInfo;
import com.asiainfo.aisports.service.ServiceResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * Created by xzeng on 18/6/7.
 */
public interface MiniAppService {

    /**
     * 查询小程序信息
     *
     * @param appId
     * @return
     */
    ServiceResult findMiniAppInfo(String appId);

    /**
     * 根据授权码获取用户openid
     *
     * @param appId
     * @param code
     * @return
     */
    ServiceResult queryOpenIdByCode(Long appId, String code);

    /**
     * 根据用户授权查询网络用户
     *
     * @param openId
     * @return
     */
    ServiceResult queryUserByOpenId(String openId);

    /**
     * 绑定
     *
     * @param centerId
     * @param openId
     * @param miniAppId
     * @param netUserId
     * @param nickName
     * @return
     */
    ServiceResult bind(Long centerId, String openId, Long miniAppId, Long netUserId, String nickName, String avatar);

    /**
     * 查询是否绑定
     *
     * @param netUserId
     * @param openId
     * @param miniAppId
     * @return
     */
    ServiceResult checkBind(Long netUserId, String openId, Long miniAppId);

    /**
     * 解绑
     *
     * @param openId
     * @param miniAppId
     * @param netUserId
     * @return
     */
    ServiceResult unbind(String openId, Long miniAppId, Long netUserId);

    /**
     * 更新用户头像和昵称
     *
     * @param openId
     * @param miniAppId
     * @param nickName
     * @param avatar
     * @return
     */
    int updateUserInfo(String openId, Long miniAppId, String nickName, String avatar);

    /**
     * 根据netUserId获取用户信息
     *
     * @param netUserId
     * @param miniAppId
     * @return
     */
    Map findMemberByNetUserId(Long netUserId, Long miniAppId);

    /**
     * 根据unionId查询用户信息
     *
     * @param centerId 中心id
     * @param unionId  unionId
     * @return
     */
    ServiceResult queryUserByUnionId(Long centerId, String unionId);

    ServiceResult checkOpenIdBindLimit(String openId, Long miniAppId, String phoneNum, Long centerId);

    ServiceResult queryUserByPhone(Long centerId, String phoneNum);

    void setMiniAppAvatar(NetUserInfo netUserInfo, Long miniAppId);
}
