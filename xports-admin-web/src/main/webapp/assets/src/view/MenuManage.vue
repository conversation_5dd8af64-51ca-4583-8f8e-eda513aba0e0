<template>
  <div>
    <el-breadcrumb separator="/">
      <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/menuManage' }">
        菜单管理
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div class="query-area">
      <el-row :gutter="20">
        <xc-query-col>
          <el-input v-model="query.menuName" placeholder="请输入菜单名称" clearable></el-input>
        </xc-query-col>
        <!--<xc-query-col>-->
          <!--<el-select-->
            <!--placeholder="请选择菜单类型"-->
            <!--v-model="query.menuType"-->
            <!--filterable-->
            <!--@change="changeMenuType">-->
            <!--<el-option-->
              <!--v-for="platform in menuTypeList"-->
              <!--:key="platform.key"-->
              <!--:label="platform.value"-->
              <!--:value="platform.key">-->
            <!--</el-option>-->
          <!--</el-select>-->
        <!--</xc-query-col>-->
        <xc-query-col>
          <el-select
            placeholder="请选择上级菜单"
            v-model="query.parentMenuId"
            filterable
            clearable
            @change="getList">
            <el-option
              v-for="folder in folders"
              :key="folder.menuId"
              :label="folder.menuName"
              :value="folder.menuId">
            </el-option>
          </el-select>
        </xc-query-col>
        <xc-query-col>
          <el-button type="primary" @click="getList">查询</el-button>
        </xc-query-col>
        <xc-query-right-col>
          <el-button  @click="view">新增</el-button>
        </xc-query-right-col>
      </el-row>
    </div>
    <div class="result-area">
      <el-table
        :data="list"
        style="width: 100%">
        <el-table-column
          prop="menuName"
          label="菜单名称">
          <template slot-scope="scope">
            <span class="folder" @click="more(scope.row.menuId)" v-if="!scope.row.parentMenuId && !scope.row.menuUrl">{{scope.row.menuName}}</span>
            <template v-else>{{scope.row.menuName}}</template>
          </template>
        </el-table-column>
        <el-table-column
          prop="menuUrl"
          label="菜单Url">
        </el-table-column>
        <el-table-column
          prop="parentMenuName"
          label="上级菜单">
          <template slot-scope="scope">
            <span class="folder" @click="more(scope.row.parentMenuId)">{{scope.row.parentMenuName}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="showOrder"
          label="显示顺序">
          <template slot-scope="scope">
            <el-popover
              placement="bottom"
              width="160"
              v-model="scope.row.editVisiable">
              <p>显示顺序：</p>
              <div style="text-align: center; padding-bottom: 10px">
                <el-input type="number" min="0" v-model="scope.row.showOrder" size="small" style="width:130px"></el-input>
              </div>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="scope.row.editVisiable = false">取消</el-button>
                <el-button type="primary" size="mini" @click="updateShowOrder(scope.row)">确定</el-button>
              </div>
              <el-button size="small" type="text" slot="reference" @click="scope.row.editVisiable = true">{{scope.row.showOrder}}</el-button>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          prop="menuCode"
          label="菜单编码">
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="更新时间">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="150">
          <template slot-scope="scope">
            <el-button type="button" size="small" @click="view(scope.row)">编辑</el-button>
            <el-popover
              placement="top"
              width="160"
              v-model="scope.row.confirmVisible">
              <p>确认删除？</p>
              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="scope.row.confirmVisible = false">取消</el-button>
                <el-button type="primary" size="mini" @click="remove(scope.row)">确定</el-button>
              </div>
              <el-button slot="reference" type="button" size="small" @click="scope.row.confirmVisible = true">删除</el-button>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-area clearfix">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="defaultPageSizes"
        :page-size="pageSize"
        :layout="defaultPageLayout"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<style>
  .folder {
    color:#56b854;
    font-weight: bold;
  }
</style>

<script>
  import queryUtils from '../layout/queryUtils'
  import {menu} from '../api'
  import {find, map} from 'lodash'

  export default {
    data() {
      return {
        folders: [],
        query: {
          menuName: '',
          menuType: '1',
          parentMenuId: ''
        },
        menuTypeList: [{key: '1', value: 'web前台'}, {key: '2', value: '后台管理'}]
      }
    },
    methods: {
      more(parentMenuId) {
        this.query.parentMenuId = `${parentMenuId}`
        this.getList()
      },
      changeMenuType() {
        this.getArgs().then(this.getList)
      },
      getList() {
        menu.list(this.query).then(this.handleList);
      },
      getArgs() {
        return menu.findMenuFolders({
          menuType: this.query.menuType
        }).then(({data = {}}) => {
            this.folders = map(data.folders || [], item => ({...item, menuId: `${item.menuId}`}))
        })
      },
      view(item) {
        this.$router.push({
          name: 'menuDetail',
          query: {
            menuId: item.menuId
          }
        })
      },
      remove(item) {
        item.confirmVisible = false
        return menu.remove({menuId: item.menuId}).then(({data = {}}) => {
          this.$message({type: "success", message: "删除成功！"})
          this.getList()
        })
      },
      updateShowOrder(item) {
        item.editVisiable = false
        return menu.updateShowOrder({
          menuId: item.menuId,
          showOrder: item.showOrder
        }).then(({data = {}}) => {
          this.$message({type: "success", message: "保存成功！"})
          this.getList()
        })
      }
    },
    mixins: [queryUtils],
    mounted() {
      this.getArgs().then(this.getList)
    }
  };
</script>
