package com.asiainfo.aisports.api.controller;

import com.asiainfo.aisports.api.web.WebResult;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.domain.core.BmMember;
import com.asiainfo.aisports.domain.core.NetUser;
import com.asiainfo.aisports.domain.core.NetUserCards;
import com.asiainfo.aisports.model.DataMap;
import com.asiainfo.aisports.model.LoginStaff;
import com.asiainfo.aisports.model.ParamMap;
import com.asiainfo.aisports.service.BmMemberService;
import com.asiainfo.aisports.service.BmReportService;
import com.asiainfo.aisports.service.api.NetUserCardsService;
import com.asiainfo.aisports.service.api.NetUserService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/wechatAPI/bmReportManage")
public class BmReportManageController {

    @Autowired
    private BmReportService bmReportService;
    @Autowired
    private BmMemberService bmMemberService;
    @Autowired
    private NetUserCardsService netUserCardsService;
    @Autowired
    private NetUserService netUserService;

    /**
     * 查询体测报告列表 -分页
     *
     * @param param
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping("/find")
    public WebResult find(LoginStaff staff,
                          @RequestParam Map<String, String> param,
                          @RequestParam(defaultValue = "1") int pageNum,
                          @RequestParam(defaultValue = Constants.DEFAULT_PAGE_SIZE) int pageSize) {
        WebResult result = new WebResult();
        Long netUserId = MapUtils.getLong(param, "netUserId");
        if (netUserId != null) {
            NetUserCards lastUseCard = netUserCardsService.getLastUseCard(netUserId);
            if (lastUseCard != null && lastUseCard.getVenueCustId() != null) {
                BmMember bmMember = bmMemberService.getBmMemberByCustId(lastUseCard.getVenueCustId());
                if (bmMember != null && bmMember.getId() != null) {
                    param.put("memberId", String.valueOf(bmMember.getId()));
                    result.put("member", bmMember);
                }
            }
        }
        List<DataMap> list = bmReportService.find(new ParamMap(param), staff, pageNum, pageSize);
        return result.set("pageInfo", new PageInfo<>(list));
    }

    /**
     * 获取用户信息
     *
     * @param netUserId
     * @return
     */
    @RequestMapping("/getUserInfo")
    public WebResult getUserInfo(LoginStaff staff, @RequestParam Long netUserId) {
        if(netUserId == null){
            return new WebResult(1, "netUserId不能为空");
        }
        NetUser netUser = netUserService.getNetUserById(netUserId);
        if(netUser == null){
            return new WebResult(1, "未查询到用户信息");
        }
        return new WebResult().set("userInfo", netUser);
    }

    /**
     * 查询体测报告的详细信息
     *
     * @param reportId
     * @return
     */
    @RequestMapping("/showReport")
    public WebResult showReport(LoginStaff staff, @RequestParam("reportId") String reportId) {
        WebResult webResult = new WebResult();
        webResult.putAll(bmReportService.showReport(reportId));
        return webResult;
    }
}