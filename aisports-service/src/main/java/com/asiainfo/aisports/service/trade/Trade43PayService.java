package com.asiainfo.aisports.service.trade;

import com.asiainfo.aisports.domain.core.Trade;
import com.asiainfo.aisports.service.TradeDelayCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by yuanxy on 15/3/16.
 */
@Service
public class Trade43PayService extends BaseTradePayService {
    private static final String URL = "/cards/cardDelay";

    @Autowired
    TradeDelayCardService tradeDelayCardService;

    @Override
    @Transactional
    public TradePayResult finishTrade(Trade trade){
        TradePayResult tradePayResult = tradeDelayCardService.finishOrder1(trade);
        if (tradePayResult.getError() == 0) {
            super.countPoints(trade);
        }
        return tradePayResult;
    }

    @Override
    public String getUrl(Trade trade) {
        return URL;
    }
}
