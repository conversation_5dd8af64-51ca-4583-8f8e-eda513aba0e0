<#include "../public/macros.ftl">
<!DOCTYPE html>
<html>
<head>
<#assign assetState="compressed" />
<@head_declares />
    <meta charset="UTF-8">
    <title>角色权限管理</title>
    <@head_includes path="roleManagement" filename="roleManagement"/>
</head>
<body>
<div class="wrapper">
<input type="hidden" id="roleId" value="${roleId!''}" />
<#include "../public/header.ftl">
<#include "../public/aside.ftl">
    <div class="main" ${bodyStyle!}>
        <div class="roleManage" id="roleManage" v-cloak>
            <div class="title-box">
                <div class="headtitle">
                    <span>{{curTitle}}</span>
                    <a href="#" class="btn-return" title="后退" @click="backtopre">
                        <i class="iconfont icon-zuojiantou"></i>
                    </a>
                </div>
                <div class="sub-box">
                    <div class="left-menu">
                        <ul>
                            <li>
                                <a href="#" :class="{active: curTab === 'basicInfo'}" @click.prevent="changeTab('basicInfo')">基本信息</a>
                            </li>
                            <li>
                                <a href="#" :class="{active: curTab === 'assignRole',disabled: curBasicinfo.roleId === undefined}" @click.prevent="changeTab('assignRole', curBasicinfo.roleId === undefined)">分配权限</a>
                            </li>
                        </ul>
                    </div>
                    <div class="op-con">
                        <div class="op-con-inner" ref = 'validation' :class="{active: curTab === 'basicInfo' }" v-show="curTab === 'basicInfo'">
                            <div class="form-row">
                                <label>
                                    <span>角色名称</span>：
                                </label>
                                <input type="text" required rtext="请输入角色名称" class="normal" v-model="curBasicinfo.roleName">
                            </div>
                            <div class="form-row">
                                <label>
                                    <span>角色所属</span>：
                                </label>
                                <multi-layers-selector
                                        required
                                        :disabled = 'roleId === "" ? false : true'
                                        placeholder = '请选择所属单位'
                                        :data = 'companyList'
                                        ref = 'companyEl'
                                        :on-select = 'selectCompany'
                                ></multi-layers-selector>
                            </div>

                            <div class="form-row">
                                <label><span class="text">权限类型</span>：</label>
                                <static-param-select <#if roleId??>disabled</#if>
                                        v-model="roleType"
                                        name="roleType"
                                        param-key="role_type"
                                ></static-param-select>
                            </div>

                            <div class="form-row">
                                <label><span class="text">角色描述</span>：</label>
                                <div class="description">
                                    <textarea class="remark" v-model="curBasicinfo.description">{{curBasicinfo.description}}</textarea>
                                </div>
                            </div>
                            <div class="form-row" id="paymethod" style="">
                                <label>
                                    <span>启用状态</span>：
                                </label>
                                <div class="paymethod" id="rentTerm" data-type="fakeradio">
                                    <label class="small-label">
                                        <input value="1" type="radio" name="paymethod" v-model="rolePicked">
                                        <span>是</span>
                                    </label>
                                    <label class="small-label">
                                        <input value="0" type="radio" name="paymethod" v-model="rolePicked">
                                        <span>否</span>
                                    </label>
                                </div>
                            </div>
                            <div class="bottom-btns">
                                <button type="button" @click="submitBasicinfo" class="btn btn-lg btn-1fa2f5 save save-nomargin">保存</button>
                            </div>
                        </div>
                        <div class="op-con-inner" :class="{active: curTab === 'assignRole' }">
                            <div class="c-line">
                                <input type="text" placeholder="请输入关键词" class="long" v-model="menuNameFilter">
                                <label>
                                    <input type="checkbox" v-model="showChecked">
                                    <span>显示已选菜单</span>
                                </label>
                            </div>
                            <div class="table-box">
                                <table class="talbe-noborder-striped">
                                    <thead>
                                    <tr>
                                        <th class="check-th">
                                            <input type="checkbox" @change="totalCheckAll" v-model="totalChecked">
                                        </th>
                                        <th>模块名称</th>
                                        <th>权限</th>
                                    </tr>
                                    </thead>
                                    <tbody v-for="role in checkCurmenu">
                                        <tr class="sub-title" v-show="(role.menuName || '').search(menuNameFilter) >= 0 && (!showChecked || role.checked)">
                                            <td class="check-td">
                                                <input type="checkbox" @change="checkSubItem(role)" v-model="role.checked">
                                            </td>
                                            <td :class="{name : true , 'auto-width' : false}">{{role.menuName}}</td>
                                            <td class="func">
                                                <label v-for="func in role.functions">
                                                    <input type="checkbox" v-model="func.checked" @change="delFunc(func,role)">
                                                    <span>{{func.name}}</span>
                                                </label>
                                            </td>
                                        </tr>
                                        <tr v-if="role.subMenus" v-for="roleinner in role.subMenus" v-show="((roleinner.menuName || '').search(menuNameFilter) >= 0) && (!showChecked || roleinner.checked)">
                                            <td class="check-td">
                                                <input type="checkbox" v-model="roleinner.checked">
                                            </td>
                                            <td :class="{name : true , 'auto-width' : false}">{{roleinner.menuName}}</td>
                                            <td class="func">
                                                <label v-for="func in roleinner.functions">
                                                    <input type="checkbox" v-model="func.checked" @change="delFunc(func,roleinner)">
                                                    <span>{{func.name}}</span>
                                                </label>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="bottom-btns bottom-btns-left">
                                <button type="button" @click="submit" class="btn btn-lg btn-1fa2f5 save save-nomargin">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<@foot_includes path="roleManagement" stylename="roleManagement" filename="roleManagementDetail" />
</body>
</html>