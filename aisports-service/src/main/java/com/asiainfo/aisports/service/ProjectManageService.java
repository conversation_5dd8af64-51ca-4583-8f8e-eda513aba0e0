package com.asiainfo.aisports.service;

import cn.hutool.core.collection.CollUtil;
import com.asiainfo.aisports.cache.RedisKeyEnum;
import com.asiainfo.aisports.common.Constants;
import com.asiainfo.aisports.common.ProjectConstants;
import com.asiainfo.aisports.domain.core.*;
import com.asiainfo.aisports.exception.ServiceException;
import com.asiainfo.aisports.model.*;
import com.asiainfo.aisports.model.project.*;
import com.asiainfo.aisports.param.VenueParamConfig;
import com.asiainfo.aisports.persistence.core.*;
import com.asiainfo.aisports.tools.DateCalUtil;
import com.asiainfo.aisports.tools.JSONObjectUtils;
import com.asiainfo.aisports.tools.TicketUtils;
import com.asiainfo.aisports.tools.TradeConstants;
import com.asiainfo.aisports.utils.ExcelPoiUtils;
import com.asiainfo.aisports.utils.RedisKeyGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageConfig;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Created by wangdd on 2017/6/21.
 * 赛事配置(关于赛事票的)
 */
@Service
public class ProjectManageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProjectManageService.class);
    // 长日期格式
    private static final ThreadLocal<DateFormat> dateFormat = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    // 短日期格式
    private static final ThreadLocal<DateFormat> shortDateFormat = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    @Autowired
    ProjectMapper projectMapper;
    @Value("${oss.url}")
    String ossUrl;
    @Autowired
    SequenceWrapper sequenceWrapper;
    @Autowired
    ProjectAttrMapper projectAttrMapper;
    @Autowired
    PerformMapper performMapper;
    @Autowired
    VenueLocationMapper venueLocationMapper;
    @Autowired
    PerformTicketMapper performTicketMapper;
    @Autowired
    TicketTypeMapper ticketTypeMapper;
    @Autowired
    PriceItemMapper priceItemMapper;
    @Autowired
    PriceStrategyMapper priceStrategyMapper;
    @Autowired
    AreaMapper areaMapper;
    @Autowired
    private PerformStockMapper performStockMapper;
    @Autowired
    private PerformStockChangeLogMapper performStockChangeLogMapper;
    @Autowired
    private PerformNewTicketMapper performNewTicketMapper;
    @Autowired
    private PerformTicketRightsMapper ticketRightsMapper;
    @Autowired
    private PerformRightsMapper performRightsMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private RedisKeyGenerator redisKeyGenerator;
    @Autowired
    private ProjectAttrService projectAttrService;
    @Autowired
    private ProjectNewService projectNewService;
    @Autowired
    private TradeTicketMapper tradeTicketMapper;
    @Autowired
    private VenueParamConfig venueParamConfig;
    @Autowired
    private PerformService performService;
    @Autowired
    private ActivityMerchantService activityMerchantService;
    @Autowired
    private TradeTicketRightsMapper tradeTicketRightsMapper;
    @Autowired
    private TradeTicketPersonMapper tradeTicketPersonMapper;
    @Autowired
    private PerformNewTicketService performNewTicketService;
    @Autowired
    private PerformTeamTicketRecordMapper performTeamTicketRecordMapper;
    @Autowired
    private TradeTicketAttrMapper tradeTicketAttrMapper;
    @Autowired
    private TradeService tradeService;
    @Autowired
    private TradeExtraMapper tradeExtraMapper;

    /**
     * 获取有效的project记录
     *
     * @param centerId
     * @param projectName
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true)
    public List<Project> getValidProjectList(Long centerId, String projectName, Integer pageNum, Integer pageSize) {
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = Constants.EIGHT_PAGE_SIZE;
        }
        projectName = Strings.emptyToNull(projectName);
        return projectMapper.getValidProjectForCenter(centerId, projectName, new RowBounds(pageNum, pageSize));
    }

    @Transactional(readOnly = true)
    public List<Project> getValidNewProjectList(Long centerId, String projectName, Integer pageNum, Integer pageSize) {
        if (pageNum == null) {
            pageNum = 1;
        }
        if (pageSize == null) {
            pageSize = Constants.EIGHT_PAGE_SIZE;
        }
        projectName = Strings.emptyToNull(projectName);
        return projectMapper.getValidNewProjectForCenter(centerId, projectName, new RowBounds(pageNum, pageSize));
    }



    /**
     * 更新赛事的发布状态
     *
     * @param staffId
     * @param projectId
     * @param status
     * @return
     */
    @Transactional
    public ServiceResult updateProjectReleaseStatus(Long staffId, String projectId, String status) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        Project param = new Project();
        param.setId(projectId);
        param.setStatus(status);
        param.setUpdateStaffId(staffId);
        param.setUpdateTime(now);
        int changNum = projectMapper.updateByPrimaryKeySelective(param);
        if (changNum == 0) {
            serviceResult.setError(1);
            serviceResult.setMessage("赛事发布状态修改失败!");
        }
        return serviceResult;
    }

    /**
     * 删除赛事
     *
     * @param staff
     * @param projectId
     * @return
     */
    @Transactional
    public ServiceResult deleteProject(Staff staff, String projectId) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        Project param = new Project();
        param.setId(projectId);
        param.setUpdateStaffId(staff.getStaffId());
        param.setUpdateTime(now);
        param.setStatus(Constants.ProjectStatus.DELETE);
        int changNum = projectMapper.updateByPrimaryKeySelective(param);
        if (changNum == 0) {
            serviceResult.setError(1);
            serviceResult.setMessage("赛事删除失败!");
        }
//        redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_LIST, staff.getCenterId()));
//        redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_DETAIL, projectId));
        return serviceResult;
    }


    /**
     * 根据projectId来获取赛事信息
     *
     * @param projectId
     * @return
     */
    @Transactional
    public Map<String, Object> getProjectById(String projectId) {
        Map<String, Object> resultMap = projectMapper.selectByPrimaryKeyReturnMap(projectId);
        String image = (String) resultMap.get("image");
        if (!Strings.isNullOrEmpty(image) && !image.startsWith("http")) {
            image = ossUrl + image;
            resultMap.put("image", image);
            Project param = new Project();
            param.setId(projectId);
            param.setImage(image);
            projectMapper.updateByPrimaryKeySelective(param);
        }

        Map<String,String> resultAttr = projectAttrService.getAttrValue(projectId);
        resultMap.put("projectBuyManual", resultAttr.get("project_buy_manual"));
        resultMap.put("realFlag", resultAttr.get(ProjectConstants.Attr.REAL_FLAG));
        resultMap.put("popFlag", resultAttr.get(ProjectConstants.Attr.POP_FLAG));
        resultMap.put("popContent", resultAttr.get(ProjectConstants.Attr.POP_CONTENT));

//        projectNewService.reloadProjectInfo(projectId, redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_DETAIL, projectId));
//        projectNewService.reloadPerformList(projectId);

        return resultMap;
    }


    /**
     * 新增或者更新赛事项目信息
     *
     * @param staffId
     * @param centerId
     * @param projectId        项目ID
     * @param projectName      项目名称
     * @param projectTypeId    项目类型ID
     * @param image            图片
     * @param performStartDate 项目开始时间
     * @param performEndDate   项目结束时间
     * @param saleStartDate    销售开始时间
     * @param saleEndDate      销售结束时间
     * @param description      描述
     * @param projectBuyManual 购票须知
     * @param sort
     * @param realFlag
     * @param popFlag
     * @param popContent
     * @param newFlag
     * @return
     */
    @Transactional
    public ServiceResult insertOrUpdateProject(Long staffId, Long centerId, String projectId, String projectName,
                                               Long projectTypeId, String image, String performStartDate, String performEndDate,
                                               String saleStartDate, String saleEndDate, String description, String projectBuyManual, Integer sort,
                                               String realFlag, String popFlag, String popContent, String newFlag) throws ParseException {
        ServiceResult serviceResult = new ServiceResult();
        if (!Strings.isNullOrEmpty(image) && !image.startsWith("http")) {
            image = ossUrl + image;
        }
        Date now = new Date();
        Project project = new Project();
        project.setId(projectId);
        project.setName(projectName);
        project.setDescription(description);
        project.setImage(image);
        if (!Strings.isNullOrEmpty(performStartDate)) {
            project.setPerformStartDate(dateFormat.get().parse(performStartDate));
        }
        if (!Strings.isNullOrEmpty(performEndDate)) {
            project.setPerformEndDate(dateFormat.get().parse(performEndDate));
        }
        if (!Strings.isNullOrEmpty(saleStartDate)) {
            project.setSaleStartDate(dateFormat.get().parse(saleStartDate));
        }
        if (!Strings.isNullOrEmpty(saleEndDate)) {
            project.setSaleEndDate(dateFormat.get().parse(saleEndDate));
        }
        project.setUpdateTime(now);
        project.setUpdateStaffId(staffId);
        project.setProjectTypeId(projectTypeId);
        project.setSort(sort);
        if (Strings.isNullOrEmpty(projectId)) { //不存在projectId时为新增
            project.setId(sequenceWrapper.projectSequence());
            project.setStatus(Constants.ProjectStatus.NOT_VALID);
            project.setStartDate(now);
            project.setEndDate(dateFormat.get().parse(Constants.DEFAULT_END_TIME));
            project.setCenterId(centerId);
            project.setCreateTime(now);
            projectMapper.insert(project);

            ProjectAttr projectAttr = new ProjectAttr();
            projectAttr.setProjectId(project.getId());
            projectAttr.setAttrCode("project_buy_manual");
            projectAttr.setAttrValue(projectBuyManual);
            projectAttr.setUpdateTime(now);
            projectAttrMapper.insert(projectAttr);
        } else { //更新
            projectMapper.updateByPrimaryKeySelective(project);
            ProjectAttr projectAttr = new ProjectAttr();
            projectAttr.setProjectId(project.getId());
            projectAttr.setAttrCode("project_buy_manual");
            projectAttr.setAttrValue(projectBuyManual);
            projectAttr.setUpdateTime(now);
            projectAttrMapper.updateByPrimaryKey(projectAttr);
        }
        saveOrUpdateProjectAttr(project.getId(), ProjectConstants.Attr.REAL_FLAG, realFlag);
        saveOrUpdateProjectAttr(project.getId(), ProjectConstants.Attr.POP_FLAG, popFlag);
        saveOrUpdateProjectAttr(project.getId(), ProjectConstants.Attr.POP_CONTENT, popContent);
        saveOrUpdateProjectAttr(project.getId(), ProjectConstants.Attr.NEW_FLAG, newFlag);

        serviceResult.set("projectId", project.getId());
        /*redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_LIST, centerId));
        if (!Strings.isNullOrEmpty(projectId)) {
            redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_DETAIL, projectId));

        }*/
        return serviceResult;

    }


    public void saveOrUpdateProjectAttr(String projectId, String attrCode, String attrValue) {
        ProjectAttr projectAttr = new ProjectAttr();
        projectAttr.setProjectId(projectId);
        projectAttr.setAttrCode(attrCode);
        projectAttr.setAttrValue(attrValue);
        projectAttr.setUpdateTime(new Date());
        projectAttrMapper.replace(projectAttr);

    }


    /**
     * 获取有效的演出场次记录
     *
     * @param projectId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Perform> getValidPerformList(String projectId) {
        Perform param = new Perform();
        param.setProjectId(projectId);
        param.setState(Constants.Tag.YES);
        return performMapper.selectByFields(param);
    }

    /**
     * 删除演出场次
     *
     * @param staffId
     * @param performId
     * @return
     */
    @Transactional
    public ServiceResult deletePerform(Long staffId, String performId) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        Perform perform = new Perform();
        perform.setId(performId);
        perform.setState(Constants.Tag.NO);
        perform.setUpdateTime(now);
        perform.setUpdateStaffId(staffId);
        int changNum = performMapper.updateByPrimaryKeySelective(perform);
        if (changNum == 0) {
            serviceResult.setError(1);
            serviceResult.setMessage("删除场次信息失败!");
        }
        return serviceResult;
    }

    /**
     * 获取场次的基本信息
     *
     * @param performId
     * @return
     */
    @Transactional
    public Perform getPerformBaseInfo(String performId) {
        Perform performBaseInfo = performMapper.selectByPrimaryKey(performId);
        String remark = performBaseInfo.getRemark();
        if (!Strings.isNullOrEmpty(remark) && !remark.startsWith("http")) {
            remark = ossUrl + remark;
            performBaseInfo.setRemark(remark);
            performMapper.updateByPrimaryKey(performBaseInfo);
        }
        return performBaseInfo;
    }

    /**
     * 获取可选择的演出场次地址列表
     *
     * @param centerId
     * @return
     */
    @Transactional(readOnly = true)
    public List<VenueLocation> getPerformLocationList(Long centerId) {
        VenueLocation param = new VenueLocation();
        param.setCenterId(centerId);
        param.setLocationType(Constants.LocationType.PERFORM_LOCATION);
        return venueLocationMapper.selectByFields(param);
    }

    /**
     * 新增演出地址
     *
     * @param staffId
     * @param centerId
     * @param name
     * @param latitude
     * @param longitude
     * @param address
     * @param cityCode
     * @param districtCode
     * @return
     */
    @Transactional
    public ServiceResult addPerformLocation(Long staffId, Long centerId, String name, String latitude,
                                            String longitude, String address, String cityCode, String districtCode) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        VenueLocation param = new VenueLocation();
        param.setId(sequenceWrapper.venueLocationSequence());
        param.setName(name);
        param.setLocationType(Constants.LocationType.PERFORM_LOCATION);
        param.setLatitude(latitude);
        param.setLongitude(longitude);
        param.setAddress(address);
        param.setCityCode(cityCode);
        param.setDistrictCode(districtCode);
        param.setCenterId(centerId);
        param.setCreateStaffId(staffId);
        param.setCreateTime(now);
        param.setUpdateStaffId(staffId);
        param.setUpdateTime(now);
        int changNum = venueLocationMapper.insert(param);
        if (changNum == 0) {
            serviceResult.setError(1);
            serviceResult.setMessage("演出地址新增失败!");
        }
        return serviceResult;
    }

    /**
     * 获取有效场次票的列表信息
     *
     * @param performId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectValidPerformTicketByPerformId(String performId) {
        return performTicketMapper.selectValidPerformTicketByPerformId(performId, new Date());
    }


    /**
     * 插入或更新场次的基本信息
     *
     * @param staff
     * @param performId
     * @param performName
     * @param description
     * @param projectId
     * @param performDate
     * @param address
     * @param locationId
     * @param startTime
     * @param endTime
     * @param buyLimit
     * @param remark
     * @param saleStartDate
     * @param saleEndDate
     * @return
     */
    @Transactional
    public ServiceResult insertOrUpdatePerformBaseInfo(Staff staff, String performId, String performName, String description,
                                                       String projectId, String performDate, String address, Long locationId,
                                                       String startTime, String endTime, Integer buyLimit, String remark,
                                                       String saleStartDate, String saleEndDate) throws ParseException {
        ServiceResult serviceResult = new ServiceResult();
        int changNum;
        Date now = new Date();
        if (!Strings.isNullOrEmpty(remark) && !remark.startsWith("http")) {
            remark = ossUrl + remark;
        }
        Perform perform = new Perform();
        perform.setId(performId);
        perform.setName(performName);
        perform.setDescription(description);
        perform.setProjectId(projectId);
        perform.setPerformDate(shortDateFormat.get().parse(performDate));
        perform.setAddress(address);
        perform.setLocationId(locationId);
        perform.setStartTime(startTime);
        perform.setEndTime(endTime);
        perform.setBuyLimit(buyLimit);
        perform.setRemark(remark);
        perform.setUpdateTime(now);
        perform.setUpdateStaffId(staff.getStaffId());
        perform.setSaleStartDate(dateFormat.get().parse(saleStartDate));
        perform.setSaleEndDate(dateFormat.get().parse(saleEndDate));
        if (Strings.isNullOrEmpty(performId)) { //为新增
            perform.setId(sequenceWrapper.projectSequence());
            perform.setState(Constants.Tag.YES);
            perform.setCreateTime(now);
            changNum = performMapper.insert(perform);
        } else {
            changNum = performMapper.updatePerform(perform);
        }
        if (changNum == 0) {
            serviceResult.setError(1);
            serviceResult.setMessage("保存场次信息失败!");
            return serviceResult;
        }
        serviceResult.set("performId", perform.getId());
//        redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_LIST, staff.getCenterId()));
//        redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_DETAIL, projectId));

        return serviceResult;
    }

    /**
     * 删除场次票信息
     *
     * @param staff
     * @param performTicketId
     * @return
     */
    @Transactional
    public ServiceResult deletePerformTicket(LoginStaff staff, String performTicketId) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        PerformTicket performTicket = performTicketMapper.selectByPrimaryKey(performTicketId);
        if (performTicket == null) {
            serviceResult.setError(1);
            serviceResult.setMessage("未找到场次票信息");
            return serviceResult;
        }
        if (!performTicket.getTotalAmount().equals(performTicket.getRemainAmount())) {
            serviceResult.setError(1);
            serviceResult.setMessage("场次票已经售卖,不可删除!");
            return serviceResult;
        }
        //更新场次状态为失效
        performTicket.setState(Constants.Tag.NO);
        performTicket.setUpdateStaffId(staff.getStaffId());
        performTicket.setUpdateTime(now);
        performTicketMapper.updateByPrimaryKey(performTicket);
        //更新ticket_type状态为失效
        TicketType ticketType = new TicketType();
        ticketType.setTicketTypeId(performTicket.getTicketType());
        ticketType.setStatus(Constants.Tag.NO);
        ticketType.setUpdateStaffId(staff.getStaffId());
        ticketType.setUpdateTime(now);
        ticketTypeMapper.updateByPrimaryKeySelective(ticketType);


        return serviceResult;
    }

    /**
     * 新增或者更新场次票
     *
     * @param staffId
     * @param centerId
     * @param performId
     * @param performTicketId
     * @param performTicketName
     * @param price
     * @param totalAmount
     * @return
     */
    @Transactional
    public ServiceResult insertOrUpdatePerformTicket(Long staffId, Long centerId, String performId, String performTicketId,
                                                     String performTicketName, Integer price, Integer totalAmount) throws ParseException {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        Date defaultEndDate = dateFormat.get().parse(Constants.DEFAULT_END_TIME);
        if (Strings.isNullOrEmpty(performTicketId)) {   //新增
            TicketType ticketType = new TicketType();
            //插入ticket_type
            Long ticketTypeId = sequenceWrapper.priceSequence();
            ticketType.setTicketTypeId(ticketTypeId);
            ticketType.setTicketTypeName(performTicketName);
            ticketType.setStartDate(now);
            ticketType.setEndDate(defaultEndDate);
            ticketType.setTicketKind(Constants.TicketKind.SIMPLE_TICKET);
            ticketType.setCenterId(centerId);
            ticketType.setGroupTag(Constants.Tag.NO);
            ticketType.setValidDays(1);
            ticketType.setStatus(Constants.Tag.YES);
            ticketType.setUpdateStaffId(staffId);
            ticketType.setUpdateTime(now);
            ticketTypeMapper.insert(ticketType);
            //插入price_item
            saveTicketPrice(centerId, price, ticketTypeId, performTicketName);
            //插入perform_ticket
            PerformTicket performTicket = new PerformTicket();
            performTicket.setId(sequenceWrapper.projectSequence());
            performTicket.setName(performTicketName);
            performTicket.setState(Constants.Tag.YES);
            performTicket.setPerformId(performId);
            performTicket.setTicketType(ticketTypeId);
            performTicket.setTotalAmount(totalAmount);
            performTicket.setRemainAmount(totalAmount);
            performTicket.setVersion(0);
            performTicket.setCreateTime(now);
            performTicket.setUpdateTime(now);
            performTicket.setUpdateStaffId(staffId);
            performTicketMapper.insert(performTicket);
        } else {    //更新
            //更新perform_ticket
            PerformTicket performTicket = performTicketMapper.selectByPrimaryKey(performTicketId);
            int oldTotalAmount = performTicket.getTotalAmount();
            int oldRemainAmount = performTicket.getRemainAmount();
            int newRemainAmount = (totalAmount - oldTotalAmount) + oldRemainAmount;
            performTicket.setTotalAmount(totalAmount);
            performTicket.setRemainAmount(newRemainAmount);
            performTicket.setName(performTicketName);
            performTicket.setUpdateStaffId(staffId);
            performTicket.setUpdateTime(now);
            performTicketMapper.updateByPrimaryKey(performTicket);
            //更新ticket_type
            TicketType ticketType = ticketTypeMapper.selectByPrimaryKey(performTicket.getTicketType());
            ticketType.setTicketTypeName(performTicketName);
            ticketType.setUpdateStaffId(staffId);
            ticketType.setUpdateTime(now);
            ticketTypeMapper.updateByPrimaryKey(ticketType);
            //把旧记录更新为失效
            PriceItem oldPriceItem = priceItemMapper.selectByTypeId(ticketType.getTicketTypeId());
            if (oldPriceItem != null) {     //如果存在记录,更新为失效
                oldPriceItem.setDestroyTime(now);
                priceItemMapper.updateByPrimaryKey(oldPriceItem);
            }
            saveTicketPrice(centerId, price, ticketType.getTicketTypeId(), performTicketName);
        }
        return serviceResult;
    }

    private void saveTicketPrice(Long centerId, Integer price, Long ticketTypeId, String ticketName) throws ParseException {
        Date defaultEndDate = dateFormat.get().parse(Constants.DEFAULT_END_TIME);
        //插入price_item
        PriceItem priceItem = new PriceItem();
        Long priceItemId = sequenceWrapper.priceSequence();
        priceItem.setPriceItem(priceItemId);
        priceItem.setPriceItemName(ticketName + "价格");
        priceItem.setPriceItemType(Constants.PriceItemType.SIMPLE);
        priceItem.setPriceType(Constants.PriceStrategyPriceType.BY_NUMBER);
        priceItem.setTicketTag(Constants.Tag.YES);
        priceItem.setTypeId(ticketTypeId);
        priceItem.setCreateTime(new Date());
        priceItem.setDestroyTime(defaultEndDate);
        priceItem.setDiscountTag("0");
        priceItemMapper.insert(priceItem);

        //插入price_strategy
        PriceStrategy priceStrategy = new PriceStrategy();
        priceStrategy.setStrategyId(sequenceWrapper.priceSequence());
        priceStrategy.setCenterId(centerId);
        priceStrategy.setVenueId(0L);
        priceStrategy.setServiceId(0L);
        priceStrategy.setPriceItem(priceItemId);
        priceStrategy.setStartDate(new Date());
        priceStrategy.setEndDate(defaultEndDate);
        priceStrategy.setStartWorkday("1");
        priceStrategy.setEndWorkday("8");
        priceStrategy.setStartSegment((short) 0);
        priceStrategy.setEndSegment((short) 48);
        priceStrategy.setPrice(price);
        priceStrategy.setPriceUnit(1);
        priceStrategy.setPriceType(Constants.PriceStrategyPriceType.BY_NUMBER);
        priceStrategy.setStrategyType(Constants.StrategyType.COMMON_PRICE);
        priceStrategy.setTicketTimeId(0L);
        priceStrategyMapper.insert(priceStrategy);
    }

    /**
     * 获取省份的集合
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<Area> getProvinceList() {
        Area param = new Area();
        param.setAreaLevel(Constants.AreaLevel.PROVINCE);
        return areaMapper.selectByFields(param);
    }

    /**
     * 根据父节点ID以及等级来获取城市集合
     *
     * @param parentCode
     * @param level
     * @return
     */
    @Transactional(readOnly = true)
    public List<Area> getAreaListByParentCodeAndLevel(String parentCode, String level) {
        Area param = new Area();
        param.setAreaLevel(level);
        param.setParentCode(parentCode);
        return areaMapper.selectByFields(param);
    }


    @Transactional
    public ServiceResult saveOrUpdateStock(Long stockId, String name, String performId, Integer num, String location, LoginStaff staff) {

        if (StringUtils.isBlank(name)) {
            throw new ServiceException("请设置票区名称");
        }
        PerformStock param = new PerformStock();
        param.setName(name);
        param.setPerformId(performId);
        param.setState(Constants.Status.VALID);
        List<PerformStock> performStocks = performStockMapper.selectByFields(param);

        if (stockId == null) {
            if (!performStocks.isEmpty()) {
                throw new ServiceException("票区名称重复");
            }
            PerformStock ps = new PerformStock();
            ps.setId(sequenceWrapper.performStockId());
            ps.setTotalAmount(num);
            ps.setRemainAmount(num);
            ps.setName(name);
            ps.setPerformId(performId);
            ps.setState(Constants.Status.VALID);
            ps.setLocation(location);
            ps.setCreateTime(new Date());
            ps.setCreateStaffId(staff.getStaffId());
            ps.setVersion(0);
            performStockMapper.insert(ps);

            PerformStockChangeLog log = new PerformStockChangeLog();
            log.setId(sequenceWrapper.performStockId());
            log.setStockId(ps.getId());
            log.setChangeNum(num);
            log.setCreateTime(new Date());
            log.setCreateStaffId(staff.getStaffId());
            performStockChangeLogMapper.insert(log);
        } else {
            if(performStocks.stream().anyMatch(p -> !p.getId().equals(stockId))) {
                throw new ServiceException("票区名称重复");
            }

            PerformStock performStock = performStockMapper.selectByPrimaryKey(stockId);
            PerformStock updateObj = new PerformStock();
            updateObj.setId(stockId);
            updateObj.setName(name);
            updateObj.setLocation(location);
            updateObj.setUpdateTime(new Date());
            updateObj.setUpdateStaffId(staff.getStaffId());
            updateObj.setVersion(performStock.getVersion());

            if (!num.equals(performStock.getTotalAmount())) {
               /* if (performStock.getRemainAmount() != 0) {
                    throw new ServiceException("剩余票数不为0,不能修改总票数");
                }*/
                /*if (num < performStock.getRemainAmount()) {
                    throw new ServiceException("剩余票数不能大于总票数");
                }*/

                if (num < (performStock.getTotalAmount() - performStock.getRemainAmount())) {
                    throw new ServiceException("不可小于已售票数量");

                }
                //如果总票数大于原来的总票数,则需要记录变更日志
                PerformStockChangeLog log = new PerformStockChangeLog();
                log.setId(sequenceWrapper.performStockId());
                log.setStockId(performStock.getId());
                log.setChangeNum(num - performStock.getTotalAmount());
                log.setCreateTime(new Date());
                log.setCreateStaffId(staff.getStaffId());
                performStockChangeLogMapper.insert(log);

                updateObj.setRemainAmount(performStock.getRemainAmount() + (num - performStock.getTotalAmount()));
                updateObj.setTotalAmount(num);


            }




            int updated = performStockMapper.updateByCheck(updateObj);
            if (updated == 0) {
                throw new ServiceException("更新失败,请刷新后重试");
            }

        }

        return new ServiceResult();



    }

    @Transactional
    public ServiceResult deleteStock(Long stockId, LoginStaff staff) {
        PerformStock performStock = performStockMapper.selectByPrimaryKey(stockId);
        if (performStock == null) {
            throw new ServiceException("未找到对应的票区");
        }
        //是不是有绑定的票
        PerformNewTicket param = new PerformNewTicket();
        param.setStockId(stockId);
        param.setState(Constants.Status.VALID);
        List<PerformNewTicket> pts = performNewTicketMapper.selectByFields(param);
        if(!pts.isEmpty()) {
            throw new ServiceException("该票区有绑定的票,请先解绑后再删除");
        }
        performStock.setState(Constants.Status.INVALID);
        performStock.setUpdateTime(new Date());
        performStock.setUpdateStaffId(staff.getStaffId());
        performStockMapper.updateByPrimaryKeySelective(performStock);


        return new ServiceResult();
    }



    public List<PerformStock> getStockList(String performId) {
        PerformStock param = new PerformStock();
        param.setPerformId(performId);
        param.setState(Constants.Status.VALID);
        return performStockMapper.selectByFields(param);
    }


    @Transactional
    public ServiceResult insertOrUpdatePerformTicketNew(Long staffId, Long centerId, String performId, String performTicketId,
                                                        String performTicketName, Integer price, Long stockId, String ticketKind,
                                                        Integer adultNum, Integer minorNum, String rule, Long siteId, String rightIds, String teamName) throws ParseException {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        Date defaultEndDate = dateFormat.get().parse(Constants.DEFAULT_END_TIME);
        if (Strings.isNullOrEmpty(performTicketId)) {   //新增
            TicketType ticketType = new TicketType();
            //插入ticket_type
            Long ticketTypeId = sequenceWrapper.priceSequence();
            ticketType.setTicketTypeId(ticketTypeId);
            ticketType.setTicketTypeName(performTicketName);
            ticketType.setStartDate(now);
            ticketType.setEndDate(defaultEndDate);
            ticketType.setTicketKind(Constants.TicketKind.PERFORM_NEW_TICKET);
            ticketType.setCenterId(centerId);
            Long defaultVenueId = venueParamConfig.getLong(centerId, Constants.VenueParam.DEFAULT_PROJECT_VENUE_ID);
            if (defaultVenueId == null) {
                throw new ServiceException("未配置默认场馆");
            }
            ticketType.setVenueId(defaultVenueId);
            Long defaultServiceId = venueParamConfig.getLong(centerId, Constants.VenueParam.DEFAULT_PROJECT_SERVICE_ID);
            if (defaultServiceId == null) {
                throw new ServiceException("未配置默认服务");
            }
            ticketType.setServiceId(defaultServiceId);
            ticketType.setGroupTag(Constants.Tag.NO);
            ticketType.setValidDays(1);
            ticketType.setStatus(Constants.Tag.YES);
            ticketType.setUpdateStaffId(staffId);
            ticketType.setUpdateTime(now);
            ticketTypeMapper.insert(ticketType);
            //插入price_item
            saveTicketPrice(centerId, price, ticketTypeId, performTicketName);
            //插入perform_ticket
            PerformNewTicket performTicket = new PerformNewTicket();
            performTicket.setId(sequenceWrapper.projectSequence());
            performTicket.setName(performTicketName);
            performTicket.setTeamName(teamName);
            performTicket.setState(Constants.Tag.YES);
            performTicket.setPerformId(performId);
            performTicket.setTicketType(ticketTypeId);
            performTicket.setStockId(stockId);
            performTicket.setType(ticketKind);
            if(ProjectConstants.TicketKind.TEAM.equals(ticketKind)) {
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
            } else if(ProjectConstants.TicketKind.ADULT.equals(ticketKind)) {
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
            } else if (ProjectConstants.TicketKind.GROUP.equals(ticketKind)) {
                if(adultNum == null || adultNum < 0) {
                    throw new ServiceException("成人票数不能小于0");
                }
                performTicket.setAdultNum(adultNum);
                performTicket.setMinorNum(minorNum);
            }else if( ProjectConstants.TicketKind.AWAY.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置客队票规则");
                }
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
                performTicket.setRule(rule);
            } else if (ProjectConstants.TicketKind.OUT_ADULT.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置本地人身份证信息");
                }
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
                performTicket.setRule(rule);


            } else if (ProjectConstants.TicketKind.OUT_GROUP.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置本地人身份证信息");
                }
                if(adultNum == null || adultNum < 0) {
                    throw new ServiceException("成人票数不能小于0");
                }
                performTicket.setAdultNum(adultNum);
                performTicket.setMinorNum(minorNum);
                performTicket.setRule(rule);

            } else if (ProjectConstants.TicketKind.MINOR.equals(ticketKind)) {
                performTicket.setAdultNum(0);
                performTicket.setMinorNum(1);

            }else if (ProjectConstants.TicketKind.OUT_MINOR.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置本地人身份证信息");
                }
                performTicket.setAdultNum(0);
                performTicket.setMinorNum(1);
                performTicket.setRule(rule);


            } else {

                throw new ServiceException("未知的票种类型");
            }
            performTicket.setSiteId(siteId);
            performTicket.setCreateTime(now);
            performTicket.setCreateStaffId(staffId);
            performTicket.setUpdateTime(now);
            performTicket.setUpdateStaffId(staffId);
            performNewTicketMapper.insert(performTicket);

            saveTicketRights(performTicket.getId(), rightIds, staffId);
        } else {    //更新
            //更新perform_ticket
            PerformNewTicket performTicket = performNewTicketMapper.selectByPrimaryKey(performTicketId);
            performTicket.setName(performTicketName);
            performTicket.setStockId(stockId);
            performTicket.setType(ticketKind);
            if(ProjectConstants.TicketKind.TEAM.equals(ticketKind)) {
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
            } else if(ProjectConstants.TicketKind.ADULT.equals(ticketKind)) {
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
            } else if (ProjectConstants.TicketKind.GROUP.equals(ticketKind)) {
                if(adultNum == null || adultNum < 0) {
                    throw new ServiceException("成人票数不能小于0");
                }
                performTicket.setAdultNum(adultNum);
                performTicket.setMinorNum(minorNum);
            }else if( ProjectConstants.TicketKind.AWAY.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置客队票规则");
                }
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
                performTicket.setRule(rule);
            }else if (ProjectConstants.TicketKind.OUT_ADULT.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置本地人身份证信息");
                }
                performTicket.setAdultNum(1);
                performTicket.setMinorNum(0);
                performTicket.setRule(rule);


            } else if (ProjectConstants.TicketKind.OUT_GROUP.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置本地人身份证信息");
                }
                if(adultNum == null || adultNum < 0) {
                    throw new ServiceException("成人票数不能小于0");
                }
                performTicket.setAdultNum(adultNum);
                performTicket.setMinorNum(minorNum);
                performTicket.setRule(rule);

            } else if (ProjectConstants.TicketKind.MINOR.equals(ticketKind)) {
                performTicket.setAdultNum(0);
                performTicket.setMinorNum(1);

            }else if (ProjectConstants.TicketKind.OUT_MINOR.equals(ticketKind)) {
                if (StringUtils.isBlank(rule) || !StringUtils.isNumeric(rule)) {
                    throw new ServiceException("请设置本地人身份证信息");
                }
                performTicket.setAdultNum(0);
                performTicket.setMinorNum(1);
                performTicket.setRule(rule);


            } else {
                throw new ServiceException("未知的票种类型");
            }
            performTicket.setSiteId(siteId);
            performTicket.setUpdateStaffId(staffId);
            performTicket.setUpdateTime(now);
            performNewTicketMapper.updateByPrimaryKey(performTicket);
            saveTicketRights(performTicket.getId(), rightIds, staffId);

            //更新ticket_type
            TicketType ticketType = ticketTypeMapper.selectByPrimaryKey(performTicket.getTicketType());
            ticketType.setTicketTypeName(performTicketName);
            ticketType.setUpdateStaffId(staffId);
            ticketType.setUpdateTime(now);
            ticketTypeMapper.updateByPrimaryKey(ticketType);
            //把旧记录更新为失效
            PriceItem oldPriceItem = priceItemMapper.selectByTypeId(ticketType.getTicketTypeId());
            if (oldPriceItem != null) {     //如果存在记录,更新为失效
                oldPriceItem.setDestroyTime(now);
                priceItemMapper.updateByPrimaryKey(oldPriceItem);
            }
            saveTicketPrice(centerId, price, ticketType.getTicketTypeId(), performTicketName);
        }

//        redisTemplate.delete(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_LIST, centerId));

        return serviceResult;
    }


    @Transactional
    public void saveTicketRights(String performTicketId, String rightIds, Long staffId) {

        Date date = DateCalUtil.trimMillisecond(new Date());

        List<PerformTicketRights> ticketRights = ticketRightsMapper.selectValid(performTicketId, date);
        if (!ticketRights.isEmpty()) {
            for (PerformTicketRights ticketRight : ticketRights) {
                ticketRight.setEndDate(DateCalUtil.getDateWithOffset(date, "-1s"));
                ticketRight.setUpdateStaffId(staffId);
                ticketRight.setUpdateTime(date);
                ticketRightsMapper.updateByPrimaryKeySelective(ticketRight);
            }
        }

        if (Strings.isNullOrEmpty(rightIds)) {
            return;
        }

        String[] ids = rightIds.split(",");
        for (String id : ids) {
            if (Strings.isNullOrEmpty(id)) {
                continue;
            }
            PerformTicketRights rights = new PerformTicketRights();
            rights.setId(sequenceWrapper.performTicketRightsId());
            rights.setPerformTicketId(performTicketId);
            rights.setRightsId(Long.valueOf(id));
            rights.setStartDate(date);
            rights.setEndDate(DateCalUtil.defaultEndDate());
            rights.setCreateTime(date);
            rights.setCreateStaffId(staffId);
            rights.setUpdateTime(date);
            rights.setUpdateStaffId(staffId);
            ticketRightsMapper.insert(rights);
        }







    }

    public List<ActivityMerchant> getPmList(Long centerId) {

        return activityMerchantService.getValidList(centerId);
    }


    @Transactional
    public ServiceResult saveRights(LoginStaff staff, Long rightId, String performId, Long pmId, String name, String content,
                                    String validType, Date startDate, Date endDate) {

        if (ProjectConstants.RightsValid.DATE.equals(validType)) {
            if (startDate == null || endDate == null) {
                throw new ServiceException("请设置有效期");
            }
            if (startDate.after(endDate)) {
                throw new ServiceException("开始时间不能大于结束时间");
            }
        }

        PerformRights param = new PerformRights();
        param.setPerformId(performId);
        param.setName(name);
        param.setState(Constants.Status.VALID);
        List<PerformRights> performRights = performRightsMapper.selectByFields(param);

        if (rightId == null) {
            if (!performRights.isEmpty()) {
                throw new ServiceException("权益名称重复");
            }
            PerformRights pst = new PerformRights();
            pst.setId(sequenceWrapper.performTicketRightsId());
            pst.setName(name);
            pst.setPmId(pmId);
            pst.setPerformId(performId);
            pst.setContent(content);
            pst.setValidType(validType);
            pst.setStartDate(startDate);
            pst.setEndDate(endDate);
            pst.setState(Constants.Status.VALID);
            pst.setCreateTime(new Date());
            pst.setCreateStaffId(staff.getStaffId());
            pst.setUpdateTime(new Date());
            pst.setUpdateStaffId(staff.getStaffId());
            performRightsMapper.insert(pst);

            rightId = pst.getId();


        } else {

            Long finalRightId = rightId;
            if (performRights.stream().anyMatch(p -> !p.getId().equals(finalRightId))) {
                throw new ServiceException("权益名称重复");
            }
            PerformRights pst = performRightsMapper.selectByPrimaryKey(rightId);
            if (pst == null) {
                throw new ServiceException("未找到对应的权益");
            }
            pst.setName(name);
            pst.setPmId(pmId);
            pst.setContent(content);
            pst.setValidType(validType);
            pst.setStartDate(startDate);
            pst.setEndDate(endDate);
            pst.setUpdateTime(new Date());
            pst.setUpdateStaffId(staff.getStaffId());
            performRightsMapper.updateByPrimaryKey(pst);
        }


        return new ServiceResult().set("rightId", rightId);
    }


    @Transactional
    public ServiceResult delRights(LoginStaff staff, Long rightId) {
        PerformRights pst = performRightsMapper.selectByPrimaryKey(rightId);
        if (pst == null) {
            throw new ServiceException("未找到对应的权益");
        }

        PerformTicketRights ticketRightsParam = new PerformTicketRights();
        ticketRightsParam.setRightsId(rightId);
        List<PerformTicketRights> ticketRights = ticketRightsMapper.selectValidByFields(ticketRightsParam);
        if (!ticketRights.isEmpty()) {
            throw new ServiceException("该权益已绑定到票上,请先解绑后再删除");
        }

        pst.setState(Constants.Status.INVALID);
        pst.setUpdateTime(new Date());
        pst.setUpdateStaffId(staff.getStaffId());

        performRightsMapper.updateByPrimaryKeySelective(pst);

        return new ServiceResult();
    }

    public List<Map<String, Object>> getRightsList(String performId) {

        PerformRights param = new PerformRights();
        param.setPerformId(performId);
        param.setState(Constants.Status.VALID);
        List<PerformRights> performRights = performRightsMapper.selectByFields(param);

        List<Map<String, Object>> rightsList = new ArrayList<>();
        for (PerformRights right : performRights) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", right.getId());
            map.put("name", right.getName());
            map.put("content", right.getContent());
            map.put("validType", right.getValidType());
            map.put("startDate", right.getStartDate());
            map.put("endDate", right.getEndDate());
            rightsList.add(map);
        }
        return rightsList;

    }


    public List<DataMap> getRightsPageList(String performId, int pageNum, int pageSize) {
        PerformRights param = new PerformRights();
        param.setPerformId(performId);
        param.setState(Constants.Status.VALID);
        List<DataMap> performRights = performRightsMapper.selectPageListByFields(param, new RowBounds(pageNum, pageSize));
        return performRights;

    }

    public Map<String, Object> getRightsById(Long rightId) {

        PerformRights right = performRightsMapper.selectByPrimaryKey(rightId);
        if (right == null || Constants.Status.INVALID.equals(right.getState())) {
            throw new ServiceException("未找到对应的权益");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("id", right.getId());
        map.put("name", right.getName());
        map.put("pmId", right.getPmId());
        map.put("content", right.getContent());
        map.put("validType", right.getValidType());
        map.put("startDate", right.getStartDate());
        map.put("endDate", right.getEndDate());
        return map;
    }

    public List<Map<String, Object>> selectValidPerformTicketByPerformIdNew(String performId) {
        return performNewTicketMapper.selectValidPerformTicketByPerformId(performId, new Date());

    }

    public PerformNewTicket getPerformTicketInfo(String performTicketId) {

        return performNewTicketMapper.selectByPrimaryKey(performTicketId);



    }

    public ServiceResult deletePerformNewTicket(LoginStaff staff, String performTicketId) {
        ServiceResult serviceResult = new ServiceResult();
        Date now = new Date();
        PerformNewTicket performTicket = performNewTicketMapper.selectByPrimaryKey(performTicketId);
        if (performTicket == null) {
            serviceResult.setError(1);
            serviceResult.setMessage("未找到场次票信息");
            return serviceResult;
        }

        TradeTicket tradeTicket = tradeTicketMapper.selectByPerformTicket(performTicketId);
        if (tradeTicket != null) {
            throw new ServiceException("已经售票了");
        }


        //更新场次状态为失效
        performTicket.setState(Constants.Tag.NO);
        performTicket.setUpdateStaffId(staff.getStaffId());
        performTicket.setUpdateTime(now);
        performNewTicketMapper.updateByPrimaryKey(performTicket);
        //更新ticket_type状态为失效
        TicketType ticketType = new TicketType();
        ticketType.setTicketTypeId(performTicket.getTicketType());
        ticketType.setStatus(Constants.Tag.NO);
        ticketType.setUpdateStaffId(staff.getStaffId());
        ticketType.setUpdateTime(now);
        ticketTypeMapper.updateByPrimaryKeySelective(ticketType);


        return serviceResult;
    }

    public void loadProjectCache(Long centerId) {

        String slotStr = venueParamConfig.getParam(centerId, Constants.VenueParam.PROJECT_REDIS_SLOT);
        if( StringUtils.isBlank(slotStr)) {
            throw new ServiceException("未配置副本信息");
        }
        String[] slotArr = StringUtils.split(slotStr, ",");

        String projectListSlot = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_SLOT_LIST, centerId);
        redisTemplate.delete(projectListSlot);

        List<ProjectListVo> voList = new ArrayList<>();
        ProjectListVo vo;
        List<ProjectInfo> projectInfos = projectMapper.selectNewProjectList(centerId);
        for (ProjectInfo projectInfo : projectInfos) {
            vo = new ProjectListVo();
            String id = projectInfo.getId();
            vo.setId(id);
            vo.setName(projectInfo.getName());
            vo.setImage(projectInfo.getImage());
            vo.setProjectTypeId(projectInfo.getProjectTypeId().toString());
            vo.setProjectTypeName(projectInfo.getProjectTypeName());
            vo.setCenterId(projectInfo.getCenterId());
            vo.setCenterName(projectInfo.getCenterName());
            vo.setPerformEndDate(projectInfo.getPerformEndDate());
            vo.setPerformStartDate(projectInfo.getPerformStartDate());
            vo.setSaleEndDate(projectInfo.getSaleEndDate());
            vo.setSaleStartDate(projectInfo.getSaleStartDate());
            vo.setLowestPrice(projectInfo.getMinPrice() == null ? null : Long.valueOf(projectInfo.getMinPrice()));
            voList.add(vo);

            ProjectVo projectVo = reloadProjectInfo(id);
            List<PerformVo> performVos = reloadPerformList(id);
            String detailSlot = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_DETAIL_SLOT_LIST, id);
            String performSlot = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_SLOT_LIST, id);
            redisTemplate.delete(detailSlot);
            redisTemplate.delete(performSlot);

            for (int i = 0; i < slotArr.length; i++) {
                String slot = slotArr[i];
                String detailKey = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_DETAIL_SLOT, slot, id, i);
                redisTemplate.opsForValue().set(detailKey, projectVo);
                redisTemplate.opsForList().rightPush(detailSlot, detailKey);

                String performListKey = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_LIST_SLOT, slot, id, i);
                redisTemplate.opsForValue().set(performListKey, performVos);
                redisTemplate.opsForList().rightPush(performSlot, performListKey);

            }

        }

        for (int i = 0; i < slotArr.length; i++) {
            String slot = slotArr[i];
            String key = redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_LIST_SLOT, slot, centerId, i);
            redisTemplate.opsForValue().set(key, voList);
            redisTemplate.opsForList().rightPush(projectListSlot, key);

        }











    }



    public ProjectVo reloadProjectInfo(String projectId) {

        ProjectInfo projectInfo = projectMapper.selectNewProjectInfoById(projectId);
        if (projectInfo == null) {
            throw new ServiceException("赛事不存在");
        }

        Map<String, String> attrMap = projectAttrService.getAttrValue(projectId);

        ProjectVo projectVo = new ProjectVo();
        projectVo.setId(projectInfo.getId());
        projectVo.setName(projectInfo.getName());
        projectVo.setProjectTypeId(projectInfo.getProjectTypeId().toString());
        projectVo.setProjectTypeName(projectInfo.getProjectTypeName());
        projectVo.setPerformStartDate(projectInfo.getPerformStartDate());
        projectVo.setPerformEndDate(projectInfo.getPerformEndDate());
        projectVo.setSaleStartDate(projectInfo.getSaleStartDate());
        projectVo.setSaleEndDate(projectInfo.getSaleEndDate());
        projectVo.setAddress(projectInfo.getAddress());
        projectVo.setImage(projectInfo.getImage());
        if (projectInfo.getMinPrice() != null) {
            projectVo.setLowestPrice(Long.valueOf(projectInfo.getMinPrice()));
            projectVo.setHighestPrice(Long.valueOf(projectInfo.getMaxPrice()));
        }
        projectVo.setDescription(projectInfo.getDescription());
        projectVo.setShouldKnow(attrMap.get("project_buy_manual"));
        projectVo.setRealFlag(attrMap.get(ProjectConstants.Attr.REAL_FLAG));
        projectVo.setPopFlag(attrMap.get(ProjectConstants.Attr.POP_FLAG));
        projectVo.setPopContent(attrMap.get(ProjectConstants.Attr.POP_CONTENT));
        return projectVo;
    }


    public List<PerformVo> reloadPerformList(String projectId) {

        List<PerformInfo> performList = performService.getPerformList(projectId);

        List<PerformVo> performVoList = new ArrayList<>();
        for (PerformInfo pi : performList) {
            PerformVo pv = new PerformVo();
            pv.setId(pi.getId());
            pv.setName(pi.getName());
            pv.setImage(pi.getRemark());
//            pv.setDescription(pi.getDescription());
            pv.setAddress(pi.getAddress());
            pv.setLatitude(pi.getLatitude());
            pv.setLongitude(pi.getLongitude());
            pv.setPerformDate(pi.getPerformDate());
            pv.setStartTime(pi.getStartTime());
            pv.setEndTime(pi.getEndTime());
            pv.setBuyLimit(pi.getBuyLimit());
            pv.setSaleStartDate(pi.getSaleStartDate());
            pv.setSaleEndDate(pi.getSaleEndDate());
            List<PerformTicketVo> performTickList = getPerformTickList(pi.getId());
            pv.setStockList(loadTicketCache(performTickList));
//            pv.setTicketList(performTickList);
            PerformDescVo performDescVo = new PerformDescVo();
            performDescVo.setId(pi.getId());
            performDescVo.setImage(pi.getRemark());
            performDescVo.setDescription(pi.getDescription());
            redisTemplate.opsForValue().set(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_DETAIL, pi.getId()), performDescVo);
            performVoList.add(pv);

        }
        return performVoList;
    }

    private List<StockVo> loadTicketCache(List<PerformTicketVo> performTickList) {
        if (performTickList.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, List<PerformTicketVo>> smap = performTickList.stream().collect(Collectors.groupingBy(PerformTicketVo::getStockId));
        return smap.entrySet().stream().map(
                entry -> {
                    StockVo stockVo = new StockVo();
                    List<PerformTicketVo> tl = entry.getValue();
                    PerformTicketVo firstTicket = tl.get(0);
                    stockVo.setId(entry.getKey());
                    stockVo.setName(firstTicket.getStockName());
                    stockVo.setTag(ProjectConstants.StockState.FOR_SALE);
                    if (Constants.Tag.YES.equals(firstTicket.getNotForSale())) {
                        stockVo.setTag(ProjectConstants.StockState.NOT_FOR_SALE);
                    } else {
                        if (tl.stream().allMatch(t -> Constants.Tag.YES.equals(t.getSoldOutFlag()))) {
                            stockVo.setTag(ProjectConstants.StockState.SALE_OUT);
                        }
                    }
                    redisTemplate.opsForValue().set(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_STOCK_LIST, entry.getKey()), tl);
                    return stockVo;
                }
        ).collect(Collectors.toList());
    }

    private List<PerformTicketVo> getPerformTickList(String performId) {

        List<Map<String, Object>> mapList = performNewTicketMapper.selectValidPerformTicketByPerformId(performId, new Date());
        List<PerformTicketVo> ticketVoList = new ArrayList<>();
        if (mapList != null && !mapList.isEmpty()) {
            for (Map<String, Object> map : mapList) {
                PerformTicketVo ticketVo = new PerformTicketVo();
                String performTicketId = MapUtils.getString(map, "performTicketId");
                ticketVo.setId(performTicketId);
                ticketVo.setName(MapUtils.getString(map, "performTicketName"));
                ticketVo.setPrice(MapUtils.getLong(map, "price"));
                ticketVo.setAdultNum(MapUtils.getInteger(map, "adultNum"));
                ticketVo.setMinorNum(MapUtils.getInteger(map, "minorNum"));
                ticketVo.setRule(MapUtils.getString(map, "rule"));
                ticketVo.setTicketKind(MapUtils.getString(map, "ticketKind"));
                ticketVo.setStockName(MapUtils.getString(map, "stockName"));
                ticketVo.setStockId(MapUtils.getLong(map, "stockId"));
                ticketVo.setSiteName(MapUtils.getString(map, "siteName"));
                ticketVo.setTicketTypeId(MapUtils.getLong(map, "ticketType"));
                ticketVo.setNotForSale(adjustNotSale(map));
                ticketVo.setVenueId(MapUtils.getLong(map, "venueId"));
//                ticketVo.setRightsList(buildTicketRights(MapUtils.getString(map, "performTicketId")));
                List<RightsVo> rightsVos = buildTicketRights(performTicketId);
                if (rightsVos.isEmpty()) {
                    ticketVo.setNoRightFlag(Constants.Tag.YES);
                }
                redisTemplate.opsForValue().set(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_RIGHTS, performTicketId), rightsVos);

                if (MapUtils.getInteger(map, "remainAmount") < (MapUtils.getInteger(map, "minorNum") + MapUtils.getInteger(map, "adultNum"))) {
                    ticketVo.setSoldOutFlag(Constants.Tag.YES);
                }

                redisTemplate.opsForValue().set(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_PERFORM_TICKET_DETAIL, performTicketId), ticketVo);

                ticketVoList.add(ticketVo);
            }
        }

        return ticketVoList;
    }

    private String adjustNotSale(Map<String, Object> map) {
        Integer totalAmount = MapUtils.getInteger(map, "totalAmount");
        return totalAmount == null || totalAmount == 0 ? Constants.Tag.YES : Constants.Tag.NO;
    }

    private List<RightsVo> buildTicketRights(String performTicketId) {
        List<PerformRightsPlus> rights = performRightsMapper.selectByPerformTicketId(performTicketId, new Date());
        List<RightsVo> rightsVoList = new ArrayList<>();
        if (rights != null && !rights.isEmpty()) {
            for (PerformRightsPlus right : rights) {
                RightsVo rightsVo = new RightsVo();
                rightsVo.setId(right.getId());
                rightsVo.setName(right.getName());
                rightsVo.setContent(right.getContent());
                rightsVo.setValidTag(right.getValidType());
                rightsVo.setStartDate(right.getStartDate());
                rightsVo.setEndDate(right.getEndDate());
                rightsVo.setPmName(right.getPmName());
                rightsVo.setContactAddr(right.getContactAddr());
                rightsVoList.add(rightsVo);
            }
        }


        return rightsVoList;
    }

    public List<ActivityMerchant> getPmSelector(List<Long> pmIdList) {
        return activityMerchantService.getPmSelector(pmIdList);
    }

    public List<DataMap> getPerformTickets(LoginStaff staff, String projectId, String performId, Long stockId, int pageNum, int pageSize) {
        List<DataMap> list = tradeTicketMapper.selectPerformTickets(projectId, performId, stockId, new RowBounds(pageNum, pageSize));
        if(CollUtil.isNotEmpty(list)){
            list.forEach(dataMap -> {
                TradeTicketPerson param = new TradeTicketPerson();
                param.setTicketId(MapUtils.getLong(dataMap, "ticketId"));
                dataMap.put("person", tradeTicketPersonMapper.getPersons(param));
            });
        }
        return list;
    }

    public DataMap rightTicketStatistics(LoginStaff staff, String projectId, String performId, String rightId) {
        return tradeTicketRightsMapper.statistics(projectId, performId, rightId);
    }

    public List<DataMap> getPerformTicketRights(LoginStaff staff, String projectId, String performId, String rightId, int pageNum, int pageSize) {
        List<DataMap> list = tradeTicketRightsMapper.selectRights(projectId, performId, rightId, new RowBounds(pageNum, pageSize));
        if(CollUtil.isNotEmpty(list)){
            list.forEach(dataMap -> {
                TradeTicketPerson param = new TradeTicketPerson();
                param.setTicketId(MapUtils.getLong(dataMap, "ticketId"));
                dataMap.put("person", tradeTicketPersonMapper.getPersons(param));
            });
        }
        return list;
    }

    public DataMap ticketStatistics(LoginStaff staff, String projectId, String performId, Long stockId) {
        DataMap dataMap = new DataMap();
        dataMap.set("totalNum", performStockMapper.selectTotalNum(projectId, performId, stockId));
        List<Long> performTicketIdList = performNewTicketMapper.selectPerformTicketId(projectId, performId, stockId);
        if(CollUtil.isNotEmpty(performTicketIdList)){
            dataMap.putAll(tradeTicketMapper.selectStatistics(performTicketIdList));
        } else {
            dataMap.put("saleNum", 0);
            dataMap.put("verifiedOffNum", 0);
        }
        return dataMap;
    }

    public void reloadmessage(Long id, Long centerId) {
        performNewTicketService.reloadmessage(id);

    }

    public List<Map<String, Object>> selectValidPerformTicketPageList(String performId, Long stockId, String ticketKind, String ticketName, int pageNum, int pageSize) {
        ParamMap param = new ParamMap();
        param.set("performId", performId);
        param.set("stockId", stockId);
        param.set("ticketKind", Strings.emptyToNull(ticketKind));
        param.set("ticketName", Strings.emptyToNull(ticketName));
        param.set("date", DateCalUtil.trimMillisecond(new Date()));
        return performNewTicketMapper.selectValidPerformTicketPageList(param, new RowBounds(pageNum, pageSize));
    }

    public void setValidPeriod(String period, Long centerId) {

        redisTemplate.opsForValue().set(redisKeyGenerator.generateKey(RedisKeyEnum.PROJECT_TICKET_VALID_PERIOD, centerId), period);

    }
    @Transactional(rollbackFor = Exception.class)
    public ServiceResult sendTeamTicket(String performTicketId, Integer sendNum, Long stockId, Long price, LoginStaff staff) {
        ServiceResult serviceResult = new ServiceResult();
        if (sendNum == null || sendNum <= 0) {
            throw new ServiceException("发放数量异常");
        }
        PerformStock stock = performStockMapper.selectByPrimaryKey(stockId);
        if (Objects.isNull(stock)){
            throw new ServiceException("票区不存在");
        }
        if (stock.getRemainAmount() == null || stock.getRemainAmount() < sendNum){
            throw new ServiceException("库存不足");
        }
        PerformNewTicket performNewTicket = performNewTicketMapper.selectByPrimaryKey(performTicketId);
        if (Objects.isNull(performNewTicket)){
            throw new ServiceException("票种不存在");
        }
        Date now = new Date();
        PerformTeamTicketRecord record = new PerformTeamTicketRecord();
        record.setId(sequenceWrapper.projectSequence());
        record.setPerformTicketId(performTicketId);
        record.setSendNum(sendNum);
        record.setState(Constants.Status.VALID);
        record.setCreateStaffId(staff.getStaffId());
        record.setCreateTime(now);
        record.setUpdateTime(now);
        record.setUpdateStaffId(staff.getStaffId());
        performTeamTicketRecordMapper.insert(record);
        performStockMapper.updateRemain(stockId, -sendNum);
        //生成订单
        List<Trade> tradeList = Lists.newArrayList();
        Trade trade = new Trade();
        trade.setTradeId(sequenceWrapper.tradeSequence());
        trade.setTradeTypeCode(TradeConstants.TradeTypeCode.SEND_TEAM_PERFORM_TICKET.getLongValue());
        trade.setTradeDesc("赛事活动团体票发放");
        trade.setRemark("赛事团体票:" + stock.getName() + "-" +performNewTicket.getName() + "(" + performTicketId + ")" + "发放" + sendNum + "张");
        trade.setPayTfee(price * sendNum);
        trade.setServiceId(0L);
        trade.setCenterId(staff.getCenterId());
        trade.setVenueId(staff.getVenueId());
        trade.setBusinessType(Constants.BusinessType.SIMPLE_TICKET_SALE);
        trade.setSubscribeState(Constants.SubscribeState.COMPLETED);
        trade.setPayState(Constants.PayState.PAID);
        trade.setAcceptDate(now);
        tradeList.add(trade);
        TradeExtra tradeExtra = new TradeExtra();
        tradeExtra.setTradeId(trade.getTradeId());
        tradeExtra.setExtraKey(Constants.Extra.PERFORM_TEAM_TICKET_SEND_ID);
        tradeExtra.setValue(record.getId());
        tradeExtraMapper.insert(tradeExtra);
        //批量生成sendNum张票
        List<TradeTicket> tradeTickets = Lists.newArrayList();
        List<TradeTicketAttr> tradeTicketAttrList = Lists.newArrayList();
        List<TradeTicketRights> rightsList = new ArrayList<>();
        Long[] ticketIds = sequenceWrapper.ticketSequence(sendNum);
        Perform perform = performMapper.selectByPrimaryKey(performNewTicket.getPerformId());
        for (int i = 0; i < sendNum; i++) {
            Trade mTrade = new Trade();
            Long tradeId = sequenceWrapper.tradeSequence();
            mTrade.setTradeId(tradeId);
            mTrade.setTradeIdB(trade.getTradeId());
            mTrade.setTradeTypeCode(TradeConstants.TradeTypeCode.BUY_NEW_PERFORM_TICKET.getLongValue());
            mTrade.setTradeDesc("赛事活动团体票发放");
            mTrade.setRemark("赛事团体票:" + stock.getName() + "-" +performNewTicket.getName() + "(" + performTicketId + ")" + "共发放第" + i + "张");
            mTrade.setPayTfee(price);
            mTrade.setServiceId(0L);
            mTrade.setCenterId(staff.getCenterId());
            mTrade.setVenueId(staff.getVenueId());
            mTrade.setBusinessType(Constants.BusinessType.SIMPLE_TICKET_SALE);
            mTrade.setSubscribeState(Constants.SubscribeState.COMPLETED);
            mTrade.setPayState(Constants.PayState.PAID);
            mTrade.setAcceptDate(now);
            tradeList.add(mTrade);
            //赛事活动票
            TicketType ticketType = ticketTypeMapper.selectByPrimaryKey(performNewTicket.getTicketType());
            TradeTicket tradeTicket = new TradeTicket();
            tradeTicket.setTradeId(tradeId);
            tradeTicket.setTicketId(ticketIds[i]);
            tradeTicket.setTicketNo(TicketUtils.geneTicketNo(staff.getTenantId(), tradeTicket.getTicketId()));
            tradeTicket.setServiceId(ticketType.getServiceId());
            tradeTicket.setVenueId(ticketType.getVenueId());
            tradeTicket.setDiscount(0L);
            tradeTicket.setCouponAmount(0);
            tradeTicket.setState(Constants.TicketState.UNBIND);
            tradeTicket.setTicketSourceType(Constants.TicketSourceType.NORMAL);
            tradeTicket.setTicketType(ticketType.getTicketTypeId());
            tradeTicket.setGroupTag(Constants.GroupTicket.NOT);
            tradeTicket.setCreateTime(now);
            tradeTicket.setFetchTicketTime(now);
            tradeTicket.setPayMoney(price);
            tradeTicket.setEffectDate(perform.getPerformDate());
            tradeTicket.setExpireDate(perform.getPerformDate());
            tradeTicket.setStartTime(perform.getStartTime());
            tradeTicket.setEndTime(perform.getEndTime());
            tradeTicket.setStartSegment(DateCalUtil.getSegmentFromOffset(perform.getStartTime()));
            tradeTicket.setEndSegment(DateCalUtil.getSegmentFromOffset(perform.getEndTime()));
            // 设置tenantId
            tradeTicket.setTenantId(staff.getTenantId());
            tradeTickets.add(tradeTicket);
            //权益
            List<PerformRightsPlus> ticketRights = performRightsMapper.selectByPerformTicketId(performTicketId, now);
            for (PerformRightsPlus ticketRight : ticketRights) {
                TradeTicketRights ttr = new TradeTicketRights();
                ttr.setId(sequenceWrapper.performTicketRightsId());
                ttr.setTicketId(tradeTicket.getTicketId());
                ttr.setRightsId(ticketRight.getId());
                ttr.setValidType(ticketRight.getValidType());
                ttr.setStartDate(ticketRight.getStartDate());
                ttr.setEndDate(ticketRight.getEndDate());
                ttr.setCreateTime(new Date());
                ttr.setState(Constants.Status.INVALID);
                ttr.setRightsSnap(ticketRight.getContent());
                rightsList.add(ttr);
            }

            //赛事活动票Id保存
            TradeTicketAttr tradeTicketAttr = new TradeTicketAttr();
            tradeTicketAttr.setTicketId(tradeTicket.getTicketId());
            tradeTicketAttr.setAttrCode(Constants.TradeTicketAttr.PERFORM_TICKET_ID);
            tradeTicketAttr.setAttrValue(performTicketId);
            tradeTicketAttr.setUpdateStaffId(staff.getStaffId());
            tradeTicketAttr.setUpdateTime(now);
            tradeTicketAttrList.add(tradeTicketAttr);
        }
        tradeService.insertBatch(tradeList);
        tradeService.saveTradeTicket(tradeTickets);
        tradeTicketAttrMapper.batchInsert(tradeTicketAttrList);
        if (!rightsList.isEmpty()) {
            tradeTicketRightsMapper.batchInsert(rightsList);
        }
        return serviceResult;
    }

    public List<DataMap> getTeamTicketSendRecordList(LoginStaff staff, String performId, Long stockId, String keyword, Date startDate, Date endDate, int pageNum, int pageSize) {
        return performTeamTicketRecordMapper.selectPageList(performId, stockId, keyword, startDate, endDate, new RowBounds(pageNum, pageSize));
    }

    public DataMap getTeamTicketSendRecord(String recordId) {
        DataMap dataMap = performTeamTicketRecordMapper.selectById(recordId);
        if(dataMap == null){
            throw new ServiceException("记录不存在");
        }
        if (StringUtils.isNotBlank(dataMap.getString("rightsIds"))) {
            List<Long> rightsIdList = Arrays.stream(dataMap.getString("rightsIds").split(","))
                    .map(String::trim)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            dataMap.put("rightsList", performRightsMapper.selectByIds(rightsIdList));
        } else {
            dataMap.put("rightsList", Collections.emptyList());
        }
        return dataMap;
    }

    public List<DataMap> getTeamTicketList(LoginStaff staff, String recordId, String keyword, String state, int pageNum, int pageSize) {
        List<DataMap> list = tradeTicketMapper.selectPerformTeamTickets(recordId, keyword, state, new RowBounds(pageNum, pageSize));
        if(CollUtil.isNotEmpty(list)){
            list.forEach(dataMap -> {
                TradeTicketPerson param = new TradeTicketPerson();
                param.setTicketId(MapUtils.getLong(dataMap, "ticketId"));
                dataMap.put("person", tradeTicketPersonMapper.getPersons(param));
                try {
                    BufferedImage qrcode = getQrcode(dataMap.getString("ticketId"));
                    String base64Image = qrCodeImageToBase64(qrcode);
                    dataMap.put("qrcode", base64Image);
                } catch (Exception e) {
                    LOGGER.error("二维码生成有误", e);
                }
            });
        }
        return list;
    }

    private BufferedImage getQrcode(String content) throws WriterException {
        String teamTicketQrcodeUrl = venueParamConfig.getParam(0L, Constants.VenueParam.TEAM_TICKET_QRCODE_PREFIX);
        MatrixToImageConfig DEFAULT_CONFIG = new MatrixToImageConfig();
        int width = 300; // 图像宽度
        int height = 300; // 图像高度
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        BitMatrix bitMatrix = new MultiFormatWriter().encode(teamTicketQrcodeUrl + "&ticketId=" + content, BarcodeFormat.QR_CODE, width, height, hints);// 生成矩阵
        return MatrixToImageWriter.toBufferedImage(bitMatrix, DEFAULT_CONFIG);
    }

    /**
     * image转成base64字符串
     *
     * @param image
     * @return
     * @throws IOException
     */
    private String qrCodeImageToBase64(BufferedImage image) throws IOException {
        String result = "";
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "jpg", baos);
            baos.flush();
            //使用toByteArray()方法转换成字节数组
            byte[] imageInByte = baos.toByteArray();
            JSONObject jsonObject = JSONObjectUtils.parseObject(new String(imageInByte));
            if (jsonObject != null) { // 返回的是json，说明有错误
                result = "";
            } else {
                result = "data:image/jpg;base64," + new String(Base64.encodeBase64(imageInByte));
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        } finally {
            if (baos != null) {
                baos.close();
            }
        }
        return result;
    }

    public void downloadQrcodes(HttpServletResponse response,
                                HttpServletRequest request,
                                String recordId,
                                String keyword,
                                String state,
                                String ticketIds) {
        //响应头的设置
        response.reset();
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setContentType("application/zip;charset=utf-8");
        //解决不同浏览器压缩包名字含有中文乱码的问题
        String zipFileName = "团体票二维码压缩包";
        try {
            zipFileName = URLEncoder.encode(zipFileName, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            //返回客户端浏览器的版本号、类型
            String agent = request.getHeader("USER-AGENT");
            //针对IE或者以IE为内核的浏览器处理
            if (agent.contains("MSIE")||agent.contains("Trident")){

                zipFileName = URLEncoder.encode(zipFileName, StandardCharsets.UTF_8.name());
            }else {
                zipFileName = new String(zipFileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            }
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + zipFileName + ".zip");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream())) {
            List<String> split = new ArrayList<>();
            if (StringUtils.isBlank(ticketIds)) {
                List<Map<String, Object>> list = tradeTicketMapper.queryQrcodes(recordId, keyword, state, null);
                if(CollUtil.isNotEmpty(list)){
                    split = list.stream().map(dataMap -> MapUtils.getString(dataMap, "ticketId")).collect(Collectors.toList());
                }
            } else {
                split = Arrays.stream(ticketIds.split(",")).collect(Collectors.toList());
            }
            if (CollUtil.isEmpty(split)) {
                return;
            }
            for (int i = 0; i < split.size(); i++) {
                try {
                    BufferedImage qrcode = getQrcode(split.get(i));
                    String fileName = String.format("No.%04d.png", i + 1);
                    saveImageToZip(zipOutputStream, qrcode, fileName);
                } catch (Exception e) {
                    LOGGER.error("二维码生成有误", e);
                }
            }

            zipOutputStream.finish();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void saveImageToZip(ZipOutputStream zipOutputStream, BufferedImage image, String fileName) throws IOException {
        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            ImageIO.write(image, "png", byteArrayOutputStream);
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOutputStream.putNextEntry(zipEntry);
            zipOutputStream.write(byteArrayOutputStream.toByteArray());
            zipOutputStream.closeEntry();
        }
    }

    public List<Map<String, Object>> queryQrcodes(@RequestParam(value = "recordId") String recordId, String keyword, String state, String ticketIds) throws Exception {
        List<Long> ids = Lists.newArrayList();
        if (StringUtils.isNotBlank(ticketIds)){
            ids = Arrays.stream(ticketIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        List<Map<String, Object>> list = tradeTicketMapper.queryQrcodes(recordId, keyword, state, ids);
        if (CollUtil.isNotEmpty(list)){
            list.forEach(e -> {
                try {
                    BufferedImage qrcode = getQrcode(e.get("ticketId").toString());
                    e.put("qrcode", qrcode);
                } catch (Exception e1) {
                    LOGGER.error("二维码生成有误", e1);
                }
            });
        }
        return list;
    }
}
